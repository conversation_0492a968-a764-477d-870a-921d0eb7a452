
#include "BlockMelonStem.h"
#include "BlockMaterialMgr.h"
#include "section.h"
#include "SectionMesh.h"
#include "BlockGeom.h"
#include "Collision.h"
#include "world.h"
#include "OgreUtils.h"
#include "special_blockid.h"
#include "Ecosystem.h"

//#include "OgreMaterial.h"
#include "Graphics/Texture.h"
#include "TaskData.h"
#include "SandboxCoreDriver.h"
#include "worldData/coreMisc.h"

using namespace MINIW;

IMPLEMENT_BLOCKMATERIAL(MelonStemMaterial)
//IMPLEMENT_BLOCKINSTANCE(MelonStemMaterial)

MelonStemMaterial::MelonStemMaterial()
{
	m_ConnMtl = m_DisConnMtl = NULL;
}

MelonStemMaterial::~MelonStemMaterial()
{
	ENG_RELEASE(m_ConnMtl);
	ENG_RELEASE(m_DisConnMtl);
}

//const char *MelonStemMaterial::getGeomName()
//{
//	return "hurbs";
//}

void MelonStemMaterial::init(int resid)
{
	Super::init(resid);
	SetToggle(BlockToggle_RandomTick, true);

	

	if(m_LoadOnlyLogic) return;

	char texname[256];
	sprintf(texname, "%s_connected", GetBlockDef()->Texture1.c_str());
	m_ConnMtl = g_BlockMtlMgr.createRenderMaterial(texname, GetBlockDef(), GETTEX_WITHDEFAULT, BLOCKDRAW_GRASS);
	sprintf(texname, "%s_disconnected", GetBlockDef()->Texture1.c_str());
	m_DisConnMtl = g_BlockMtlMgr.createRenderMaterial(texname, GetBlockDef(), GETTEX_WITHDEFAULT, BLOCKDRAW_GRASS);
}

void MelonStemMaterial::initGeomName()
{
	m_geomName = "hurbs";
}
BlockTexElement *MelonStemMaterial::getDestroyTexture(Block pblock, BlockTexDesc &desc)
{
	desc.gray = true;
	desc.blendmode = BLEND_ALPHATEST;

	return m_DisConnMtl->getTexElement();
}

void MelonStemMaterial::createBlockMesh(const BuildSectionMeshData& data, const WCoord &blockpos, SectionMesh *poutmesh)
{
	BlockGeomTemplate* geom = getGeom(data.m_LODLevel);
	if (!geom)
		return;
	auto psection = data.m_SharedSectionData;
	Block pblock = psection->getBlock(blockpos);
	Rainbow::ColorRGBAf verts_light[1] = { Rainbow::ColorRGBAf::black };
	psection->getBlockVertexLight(blockpos, verts_light);

	BlockGeomMeshInfo meshinfo;
	RenderBlockMaterial *pmtl;

	float v;
	if(pblock.getData() < 8)
	{
		float h = getBlockCollideHeight(pblock.getData());
		geom->getFaceVerts(meshinfo, 0, h, 2);
		pmtl = m_DisConnMtl;
	}
	else
	{
		v = 1.0f;
		geom->getFaceVerts(meshinfo, 0);
		pmtl = m_ConnMtl;
	}

	SectionSubMesh *psubmesh = poutmesh->getSubMesh(pmtl);

	//Ecosystem *biome = psection->getBiomeGen(data.m_World, blockpos);
	Ecosystem* biome = psection->getBiomeGenOnJob(data.m_World, data.m_SharedChunkData, blockpos);
	BlockColor vertcolor(255, 255, 255, 255);
	if (biome)
		vertcolor = biome->getGrassColor();
	vertcolor.a = 0;
	psubmesh->addGeomBlockLight(meshinfo, &blockpos, verts_light, &vertcolor, pmtl->getUVTile());
}

int MelonStemMaterial::getPlaceBlockData(World *pworld, const WCoord &blockpos, DirectionType face, float hitptx, float hitpty, float hitptz, int def_blockdata)
{
	return face;
}

void MelonStemMaterial::blockTick(World *pworld, const WCoord &blockpos)
{
	HerbMaterial::blockTick(pworld, blockpos);

	const BlockDef* def = GetBlockDef();
	bool cangrow = false;
	if (def&&def->CropsSign > 0 && def->GrowthTimeNum > 0)
	{
		cangrow = true;
	}
	else
	{
		cangrow = pworld->getBlockLightValue(blockpos + WCoord(0, 1, 0)) >= 9;
	}


	if(cangrow)
	{
		if (def->CropsSign == 0 || def->GrowthTimeNum == 0)
		{
			float rate = getGrowRate(pworld, blockpos);

			if (GenRandomInt(0, int(25.0f / rate)) == 0)
			{
				//红土 不能生成
				if (pworld->getBlockID(DownCoord(blockpos)) == BLOCK_FARMLAND_RED)
				{
					return;
				}
				int blockdata = pworld->getBlockData(blockpos);

				if (blockdata < 7)
				{
					++blockdata;
					pworld->setBlockData(blockpos, blockdata, 2);
				}
				else
				{
					/*
					if (TaskSubSystem::GetTaskSubSystem())
					{
						TaskSubSystem::GetTaskSubSystem()->CheckCommonSyncTask(TASKSYS_GROW, blockpos * BLOCK_SIZE, m_BlockResID);
					}*/
					WCoord pos = blockpos * BLOCK_SIZE;
					if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
						MNSandbox::SandboxContext sContext = MNSandbox::SandboxContext(nullptr).
							SetData_Number("type", TASKSYS_GROW).
							SetData_Userdata("WCoord", "trackPos", &pos).
							SetData_Number("target1", m_BlockResID).
							SetData_Number("target2", 0).
							SetData_Number("goalnum", 1);
						MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("TaskSubSystem_CheckCommonSyncTask", sContext);
					}
					int fruitid = getBlockResID() - 1;
					//西瓜，南瓜不长在旁边，而是覆盖掉原来的藤  code-by:yanfengying
					pworld->setBlockAll(blockpos, fruitid, 8);  //blockdata 8 表示是种植的来的，西瓜,南瓜被采集后要还原.
				}
			}
		}
		else
		{
			//红土 不能生成
			if (pworld->getBlockID(DownCoord(blockpos)) == BLOCK_FARMLAND_RED)
			{
				return;
			}
			int blockdata = pworld->getBlockData(blockpos);

			if (blockdata < 7)
			{
				dealNewGrow(pworld, blockpos,2);
			}
			else
			{
				/*
				if (TaskSubSystem::GetTaskSubSystem())
				{
					TaskSubSystem::GetTaskSubSystem()->CheckCommonSyncTask(TASKSYS_GROW, blockpos * BLOCK_SIZE, m_BlockResID);
				}*/
				WCoord pos = blockpos * BLOCK_SIZE;
				if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
					MNSandbox::SandboxContext sContext = MNSandbox::SandboxContext(nullptr).
						SetData_Number("type", TASKSYS_GROW).
						SetData_Userdata("WCoord", "trackPos", &pos).
						SetData_Number("target1", m_BlockResID).
						SetData_Number("target2", 0).
						SetData_Number("goalnum", 1);
					MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("TaskSubSystem_CheckCommonSyncTask", sContext);
				}
				int fruitid = getBlockResID() - 1;
				//西瓜，南瓜不长在旁边，而是覆盖掉原来的藤  code-by:yanfengying
				pworld->setBlockAll(blockpos, fruitid, 8);  //blockdata 8 表示是种植的来的，西瓜,南瓜被采集后要还原.
			}
		}
	}
}
void  MelonStemMaterial::forceResh(World* pworld, const WCoord& blockpos)
{
	if (!pworld || !pworld->onServer())
	{
		return;
	}
	const BlockDef* def = GetBlockDef();
	if (!def || def->CropsSign == 0 || def->GrowthTimeNum == 0)
	{
		return;
	}
	if (pworld->getBlockID(DownCoord(blockpos)) == BLOCK_FARMLAND_RED)
	{
		return;
	}
	int blockdata = pworld->getBlockData(blockpos);
	if (blockdata < 7)
	{
		dealNewGrow(pworld, blockpos, 2);
	}
	blockdata = pworld->getBlockData(blockpos);
	if(blockdata>=7)
	{
		/*if (TaskSubSystem::GetTaskSubSystem())
		{
			TaskSubSystem::GetTaskSubSystem()->CheckCommonSyncTask(TASKSYS_GROW, blockpos * BLOCK_SIZE, m_BlockResID);
		}*/
		WCoord pos = blockpos * BLOCK_SIZE;
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
			MNSandbox::SandboxContext sContext = MNSandbox::SandboxContext(nullptr).
				SetData_Number("type", TASKSYS_GROW).
				SetData_Userdata("WCoord", "trackPos", &pos).
				SetData_Number("target1", m_BlockResID).
				SetData_Number("target2", 0).
				SetData_Number("goalnum", 1);
			MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("TaskSubSystem_CheckCommonSyncTask", sContext);
		}
		int fruitid = getBlockResID() - 1;
		//西瓜，南瓜不长在旁边，而是覆盖掉原来的藤  code-by:yanfengying
		pworld->setBlockAll(blockpos, fruitid, 8);  //blockdata 8 表示是种植的来的，西瓜,南瓜被采集后要还原.
	}
}

bool MelonStemMaterial::canThisPlantGrowOnThisBlockID(int blockid)
{
	return blockid==BLOCK_FARMLAND || blockid == BLOCK_FARMLAND_RED||  blockid == BLOCK_GRASS_WOOD_GRAY_FARMLAND;
}

float MelonStemMaterial::getBlockCollideHeight(int blockData)
{
	float h = 1.0f;
	if (blockData >= 0 && blockData <= 7)
	{
		h = 0.2 + blockData / 7.0 * 0.8;
		if (h > 1.0)
		{
			h = 1.0;
		}
	}
	return h;
}

void MelonStemMaterial::createCollideData(CollisionDetect* coldetect, World* pworld, const WCoord& blockpos)
{
	WCoord pos = blockpos * BLOCK_SIZE;
	int blockData = pworld->getBlockData(blockpos);
	coldetect->addObstacle(pos, pos + WCoord((float)BLOCK_SIZE, BLOCK_SIZE * getBlockCollideHeight(blockData), (float)BLOCK_SIZE));
}

bool MelonStemMaterial::onFertilized(World *pworld, const WCoord &blockpos, int fertiliser)
{
	if(pworld->getCurMapID() >= MAPID_MENGYANSTAR)
	{
		if(pworld->getBlockID(TopCoord(blockpos)) != BLOCK_PLANTSPACE_OXYGEN) return false;
	}
	//红土 不能生成
	if (pworld->getBlockID(DownCoord(blockpos)) == BLOCK_FARMLAND_RED)
	{
		return false;
	}

	const BlockDef* def = GetBlockDef();
	if (def && def->CropsSign == 0 || def->GrowthTimeNum == 0)
	{
		FertilizedPlayEffect(pworld, blockpos);
		int blockdata = pworld->getBlockData(blockpos) + GenRandomInt(2, 5);
		if (blockdata > 7)
		{
			blockdata = 7;
		}
		pworld->setBlockData(blockpos, blockdata, 2);

		if (blockdata == 7)
		{
			blockTick(pworld, blockpos);
		}

	}
	else
	{
		return dealFertilized(pworld, blockpos, fertiliser);
	}

	
	
	return true;
}

void MelonStemMaterial::dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata, BLOCK_MINE_TYPE droptype, float chance, int uin/* = -1 */)
{
	if(GenRandomFloat() > chance) return;

	int num = (blockdata >= 7) ? 2 : 1;

	for(int i=0; i<num; i++)
	{
		doDropItem(pworld, blockpos, GetBlockDef()->ToolMineDrops[0].item);
	}
}

bool MelonStemMaterial::hasDestroyScore(int blockdata)
{
	return blockdata>=7;
}
