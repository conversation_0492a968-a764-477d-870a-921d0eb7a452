
#ifndef __BLOCK_SOC_ELECTRIC_DOOR_H__
#define __BLOCK_SOC_ELECTRIC_DOOR_H__

#include "BlockDoor.h"
#include "BlockSocLogicInterface.h"

class BlockSocElectricDoor : public BlockSocDoor, public BlockSocElectricLogicInterface //tolua_exports
{ //tolua_exports
	DECLARE_BLOCKMATERIAL(BlockSocElectricDoor)
public:
	BlockSocElectricDoor();
	virtual ~BlockSocElectricDoor();
	virtual void init(int resid) override;
	virtual void initSpceialLogic();
	virtual WorldContainer* createContainer(World* pworld, const WCoord& blockpos) override;
	virtual void ChangeBlockCrackModel(World* pworld, const WCoord& blockpos);
	void closeDoor(World* pworld, const WCoord& blockpos);
	void closeCoreDoor(World* pworld, const WCoord& corepos);
	void openDoor(World* pworld, const WCoord& blockpos);
	void openCoreDoor(World* pworld, const WCoord& corepos);
	virtual bool onTrigger(World* pworld, const WCoord& blockpos, DirectionType face, IClientPlayer* player, const Rainbow::Vector3f& colpoint = Rainbow::Vector3f(0, 0, 0));
	//virtual bool logicActive(World* pworld, const WCoord& pos);
	virtual bool logicUnactive(World* pworld, const WCoord& pos);
	virtual bool isActive(World* pworld, const WCoord& pos);

	virtual RenderBlockMaterial* getGeomMtl(const SectionDataHandler* sectionData, const WCoord& blockpos, World* world);
	virtual void createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh) override;
	RenderBlockMaterial* getMtl(unsigned int index)const;
protected:
	RenderBlockMaterial* m_pActiveMtl = NULL;
	unsigned int m_nActiveMtlIndex = 0;
	RenderBlockMaterial* m_pOpenMtl = NULL;
	unsigned int m_nOpenMtlIndex = 0;
}; //tolua_exports


#endif