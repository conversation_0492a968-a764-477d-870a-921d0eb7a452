#include "TerritoryOctree.h"
#include "container_territory.h"
#include <algorithm>
#include <memory>

// TerritoryOctreeNode 实现

TerritoryOctreeNode::TerritoryOctreeNode(const TerritoryBoundingBox& nodeBounds)
    : bounds(nodeBounds)
{
    children.reserve(8);
}

void TerritoryOctreeNode::Insert(TerritoryContainer* territory, int depth)
{
    if (!territory) return;
    
    // 检查领地是否与当前节点相交
    if (!IntersectsTerritory(territory)) return;
    
    // 如果是叶子节点或达到最大深度，直接添加到当前节点
    if (children.empty() && (depth >= MAX_DEPTH || territories.size() < MAX_TERRITORIES_PER_NODE))
    {
        territories.push_back(territory);
        return;
    }
    
    // 如果需要细分且还未细分
    if (children.empty() && ShouldSubdivide(depth))
    {
        Subdivide();
        
        // 将现有领地重新分配到子节点
        std::vector<TerritoryContainer*> currentTerritories = std::move(territories);
        territories.clear();
        
        for (auto* existingTerritory : currentTerritories)
        {
            Insert(existingTerritory, depth + 1);
        }
    }
    
    // 如果有子节点，尝试插入到合适的子节点
    if (!children.empty())
    {
        bool inserted = false;
        TerritoryBoundingBox territoryBounds = territory->GetTerritoryBounds();
        Rainbow::Vector3f center = territoryBounds.GetCenter();
        
        // 尝试找到完全包含该领地的子节点
        for (auto& child : children)
        {
            if (child && child->bounds.IsPointInside(center))
            {
                child->Insert(territory, depth + 1);
                inserted = true;
                break;
            }
        }
        
        // 如果没有子节点能完全包含，则放在当前节点
        if (!inserted)
        {
            territories.push_back(territory);
        }
    }
}

bool TerritoryOctreeNode::Remove(TerritoryContainer* territory)
{
    if (!territory) return false;
    
    // 从当前节点移除
    auto it = std::find(territories.begin(), territories.end(), territory);
    if (it != territories.end())
    {
        territories.erase(it);
        return true;
    }
    
    // 从子节点中递归移除
    for (auto& child : children)
    {
        if (child && child->Remove(territory))
        {
            return true;
        }
    }
    
    return false;
}

std::vector<TerritoryContainer*> TerritoryOctreeNode::QueryPoint(const Rainbow::Vector3f& point) const
{
    std::vector<TerritoryContainer*> result;
    
    // 检查点是否在当前节点范围内
    if (!bounds.IsPointInside(point)) return result;
    
    // 检查当前节点的领地
    for (auto* territory : territories)
    {
        if (territory && territory->IsPointInTerritoryBounds(point))
        {
            result.push_back(territory);
        }
    }
    
    // 递归检查子节点
    for (const auto& child : children)
    {
        if (child)
        {
            auto childResult = child->QueryPoint(point);
            result.insert(result.end(), childResult.begin(), childResult.end());
        }
    }
    
    return result;
}

std::vector<TerritoryContainer*> TerritoryOctreeNode::QueryRegion(const TerritoryBoundingBox& region) const
{
    std::vector<TerritoryContainer*> result;
    
    // 检查区域是否与当前节点相交
    if (!bounds.Intersects(region)) return result;
    
    // 检查当前节点的领地
    for (auto* territory : territories)
    {
        if (territory)
        {
            TerritoryBoundingBox territoryBounds = territory->GetTerritoryBounds();
            if (territoryBounds.Intersects(region))
            {
                result.push_back(territory);
            }
        }
    }
    
    // 递归检查子节点
    for (const auto& child : children)
    {
        if (child)
        {
            auto childResult = child->QueryRegion(region);
            result.insert(result.end(), childResult.begin(), childResult.end());
        }
    }
    
    return result;
}

std::vector<TerritoryContainer*> TerritoryOctreeNode::GetTerritoriesContaining(const Rainbow::Vector3f& point) const
{
    return QueryPoint(point);
}

bool TerritoryOctreeNode::ShouldSubdivide(int depth) const
{
    return depth < MAX_DEPTH && territories.size() >= MAX_TERRITORIES_PER_NODE;
}

void TerritoryOctreeNode::Subdivide()
{
    if (!children.empty()) return; // 已经细分过了
    
    Rainbow::Vector3f center = bounds.GetCenter();
    Rainbow::Vector3f halfExtents = bounds.GetHalfExtents() * 0.5f;
    
    children.resize(8);
    
    // 创建8个子节点
    for (int i = 0; i < 8; ++i)
    {
        float offsetX = (i & 1) ? halfExtents.x : -halfExtents.x;
        float offsetY = (i & 2) ? halfExtents.y : -halfExtents.y;
        float offsetZ = (i & 4) ? halfExtents.z : -halfExtents.z;
        
        Rainbow::Vector3f childCenter = center + Rainbow::Vector3f(offsetX, offsetY, offsetZ);
        TerritoryBoundingBox childBounds(childCenter, halfExtents);
        
        children[i] = std::unique_ptr<TerritoryOctreeNode>(new TerritoryOctreeNode(childBounds));
    }
}

int TerritoryOctreeNode::GetChildIndex(const Rainbow::Vector3f& point) const
{
    Rainbow::Vector3f center = bounds.GetCenter();
    int index = 0;
    
    if (point.x >= center.x) index |= 1;
    if (point.y >= center.y) index |= 2;
    if (point.z >= center.z) index |= 4;
    
    return index;
}

bool TerritoryOctreeNode::IntersectsTerritory(TerritoryContainer* territory) const
{
    if (!territory) return false;
    
    TerritoryBoundingBox territoryBounds = territory->GetTerritoryBounds();
    return bounds.Intersects(territoryBounds);
}

// TerritoryOctree 实现

TerritoryOctree::TerritoryOctree(const TerritoryBoundingBox& worldBounds)
    : m_worldBounds(worldBounds)
{
    m_root = std::unique_ptr<TerritoryOctreeNode>(new TerritoryOctreeNode(worldBounds));
}

void TerritoryOctree::AddTerritory(TerritoryContainer* territory)
{
    if (m_root && territory)
    {
        m_root->Insert(territory);
    }
}

void TerritoryOctree::RemoveTerritory(TerritoryContainer* territory)
{
    if (m_root && territory)
    {
        m_root->Remove(territory);
    }
}

void TerritoryOctree::UpdateTerritory(TerritoryContainer* territory)
{
    if (territory)
    {
        RemoveTerritory(territory);
        AddTerritory(territory);
    }
}

bool TerritoryOctree::IsPointInAnyTerritory(const Rainbow::Vector3f& point) const
{
    if (!m_root) return false;
    
    auto territories = m_root->QueryPoint(point);
    return !territories.empty();
}

std::vector<TerritoryContainer*> TerritoryOctree::GetTerritoriesContaining(const Rainbow::Vector3f& point) const
{
    if (!m_root) return {};
    
    return m_root->QueryPoint(point);
}

bool TerritoryOctree::IsPlayerInAuthorizedTerritory(const Rainbow::Vector3f& point, unsigned int playerUin) const
{
    if (!m_root) return false;
    
    auto territories = m_root->QueryPoint(point);
    
    // 检查玩家是否在任何被授权的领地内
    for (auto* territory : territories)
    {
        if (territory && territory->IsAuthorized(playerUin))
        {
            return true;
        }
    }
    
    return false;
}

std::vector<TerritoryContainer*> TerritoryOctree::QueryRegion(const TerritoryBoundingBox& region) const
{
    if (!m_root) return {};
    
    return m_root->QueryRegion(region);
}

void TerritoryOctree::Clear()
{
    if (m_root)
    {
        m_root = std::unique_ptr<TerritoryOctreeNode>(new TerritoryOctreeNode(m_worldBounds));
    }
}

TerritoryOctree::Stats TerritoryOctree::GetStats() const
{
    Stats stats;
    if (m_root)
    {
        CollectStats(m_root.get(), stats, 0);
        
        // 计算平均每个叶子节点的领地数量
        int leafNodes = 0;
        int totalLeafTerritories = 0;
        // 这里需要递归计算叶子节点，简化处理
        if (stats.totalNodes > 0)
        {
            stats.avgTerritoriesPerLeaf = static_cast<float>(stats.totalTerritories) / stats.totalNodes;
        }
    }
    return stats;
}

void TerritoryOctree::CollectStats(const TerritoryOctreeNode* node, Stats& stats, int depth) const
{
    if (!node) return;
    
    stats.totalNodes++;
    stats.totalTerritories += static_cast<int>(node->territories.size());
    stats.maxDepth = std::max(stats.maxDepth, depth);
    
    for (const auto& child : node->children)
    {
        if (child)
        {
            CollectStats(child.get(), stats, depth + 1);
        }
    }
}

