#pragma once

#include "Math/Vector3f.h"
#include "Common/OgreWCoord.h"

class TerritoryContainer;
class IClientPlayer;

/**
 * 领地查询接口 - 提供便捷的领地查询功能
 * 这是一个静态接口类，用于简化游戏逻辑中的领地查询
 */
class TerritoryQuery
{
public:
    /**
     * 检查玩家当前是否在自己的领地或被授权的领地内
     * @param player 玩家对象
     * @return 如果在授权领地内返回true，否则返回false
     */
    static bool IsPlayerInOwnTerritory(IClientPlayer* player);
    
    /**
     * 检查指定位置是否在玩家的授权领地内
     * @param position 世界坐标位置
     * @param playerUin 玩家UIN
     * @return 如果在授权领地内返回true，否则返回false
     */
    static bool IsPlayerInOwnTerritory(const Rainbow::Vector3f& position, unsigned int playerUin);
    
    /**
     * 检查指定方块坐标是否在玩家的授权领地内
     * @param blockPos 方块坐标
     * @param playerUin 玩家UIN
     * @return 如果在授权领地内返回true，否则返回false
     */
    static bool IsPlayerInOwnTerritory(const WCoord& blockPos, unsigned int playerUin);
    
    /**
     * 检查位置是否在任何领地内
     * @param position 世界坐标位置
     * @return 如果在任何领地内返回true，否则返回false
     */
    static bool IsInAnyTerritory(const Rainbow::Vector3f& position);
    
    /**
     * 检查方块位置是否在任何领地内
     * @param blockPos 方块坐标
     * @return 如果在任何领地内返回true，否则返回false
     */
    static bool IsInAnyTerritory(const WCoord& blockPos);
    
    /**
     * 获取包含指定位置的领地
     * @param position 世界坐标位置
     * @return 包含该位置的领地容器，如果不在任何领地内返回nullptr
     */
    static TerritoryContainer* GetTerritoryAt(const Rainbow::Vector3f& position);
    
    /**
     * 获取包含指定方块的领地
     * @param blockPos 方块坐标
     * @return 包含该方块的领地容器，如果不在任何领地内返回nullptr
     */
    static TerritoryContainer* GetTerritoryAt(const WCoord& blockPos);
    
    /**
     * 检查玩家是否可以在指定位置建造
     * @param position 世界坐标位置
     * @param playerUin 玩家UIN
     * @return 如果可以建造返回true，否则返回false
     */
    static bool CanPlayerBuildAt(const Rainbow::Vector3f& position, unsigned int playerUin);
    
    /**
     * 检查玩家是否可以在指定方块建造
     * @param blockPos 方块坐标
     * @param playerUin 玩家UIN
     * @return 如果可以建造返回true，否则返回false
     */
    static bool CanPlayerBuildAt(const WCoord& blockPos, unsigned int playerUin);
    
    /**
     * 获取玩家当前所在的领地信息
     * @param player 玩家对象
     * @return 玩家当前所在的领地信息结构
     */
    struct PlayerTerritoryInfo
    {
        bool isInTerritory = false;           // 是否在领地内
        bool isAuthorized = false;            // 是否有权限
        bool isOwner = false;                 // 是否是领地主人
        TerritoryContainer* territory = nullptr;  // 领地容器
        std::string ownerName;                // 领地主人名称（如果需要）
    };
    static PlayerTerritoryInfo GetPlayerTerritoryInfo(IClientPlayer* player);
    
    /**
     * 获取指定位置的领地信息
     * @param position 世界坐标位置
     * @param playerUin 玩家UIN（用于权限检查）
     * @return 位置的领地信息结构
     */
    static PlayerTerritoryInfo GetTerritoryInfo(const Rainbow::Vector3f& position, unsigned int playerUin);
    
    /**
     * 获取八叉树优化状态和统计信息
     */
    struct TerritorySystemStats
    {
        bool octreeEnabled = false;
        int totalTerritories = 0;
        int octreeNodes = 0;
        int maxDepth = 0;
        float avgTerritoriesPerLeaf = 0.0f;
        float lastQueryTime = 0.0f;  // 最后一次查询耗时（毫秒）
    };
    static TerritorySystemStats GetSystemStats();
    
    /**
     * 启用/禁用八叉树优化
     * @param enabled 是否启用
     */
    static void SetOptimizationEnabled(bool enabled);

private:
    // 辅助方法：将方块坐标转换为世界坐标
    static Rainbow::Vector3f BlockToWorldPos(const WCoord& blockPos);
};



