
#include "BlockLog.h"
#include "BlockMaterialMgr.h"
#include "special_blockid.h"
#include "world.h"
#include "ClientMob.h"
#include "ClientActorManager.h"
#include "EffectManager.h"
#include "CoreCommonDef.h"
#include "chunk.h"
#include "WorldManager.h"
#include "DangerNightManager.h"

IMPLEMENT_BLOCKMATERIAL(LogBlockMaterial)
//IMPLEMENT_BLOCKINSTANCE(LogBlockMaterial)
IMPLEMENT_BLOCKMATERIAL(LogCropperBlockMaterial)
IMPLEMENT_BLOCKMATERIAL(BlockScalyFruit)
IMPLEMENT_BLOCKMATERIAL(BlockBlueFruit)

using namespace MINIW;


IMPLEMENT_BLOCKMATERIAL_INSTANCE_BEGIN(LogBlockMaterial)
	IMPLEMENT_BLOCKMATERIAL_INSTANCE_R_PARAM(LogBlockMaterial, R_Dir, int)(0, "Dir", "Block", &LogBlockMaterialInstance::GetBlockDir, &LogBlockMaterialInstance::SetBlockDir);
IMPLEMENT_BLOCKMATERIAL_INSTANCE_END()

void LogBlockMaterialInstance::SetBlockDir(const int& dir)
{
	LogBlockMaterial* mtl = dynamic_cast<LogBlockMaterial*>(m_blockMtrl);
	int data = dir;
//todo 
	if (dir >=0 && dir<=4)
	{
		m_pworld->setBlockAll(m_pos.x, m_pos.y, m_pos.z, m_block.getResID(), data);
	}
}
int LogBlockMaterialInstance::GetBlockDir() const
{
	return m_block.getData() & 7;
}

/////////////////////////////////////////////////////////////////////////////////////////

void LogBlockMaterial::init(int resid)
{
	CubeBlockMaterial::init(resid);

	if(m_LoadOnlyLogic) return;

	char texname[256];
	sprintf(texname, "%s_top", GetBlockDef()->Texture1.c_str());
	RenderBlockMaterial *topmtl = g_BlockMtlMgr.createRenderMaterial(texname, GetBlockDef(), GETTEX_WITHDEFAULT);
	setFaceMtl(DIR_POS_Y, topmtl);

	RenderBlockMaterial *sidemtl = NULL;
	if (strcmp("pumpkin", m_Def->Type.c_str()) == 0)
	{
		sprintf(texname, "%s_side", !m_Def->Texture2.empty() ? m_Def->Texture2.c_str() : m_Def->Texture1.c_str());
		sidemtl = g_BlockMtlMgr.createRenderMaterial(texname, m_Def);
	}
	else
	{
		sidemtl = g_BlockMtlMgr.createRenderMaterial(!GetBlockDef()->Texture2.empty() ? GetBlockDef()->Texture2.c_str() : GetBlockDef()->Texture1.c_str(), GetBlockDef());
	}
	if(sidemtl == NULL)
	{
		sprintf(texname, "%s_side", GetBlockDef()->Texture1.c_str());
		sidemtl = g_BlockMtlMgr.createRenderMaterial(texname, GetBlockDef());
	}

	setFaceMtl(DIR_NEG_X, sidemtl);
	setFaceMtl(DIR_POS_X, sidemtl);
	setFaceMtl(DIR_NEG_Z, sidemtl);
	setFaceMtl(DIR_POS_Z, sidemtl);

	sprintf(texname, "%s_bottom", GetBlockDef()->Texture1.c_str());
	RenderBlockMaterial *bottommtl = g_BlockMtlMgr.createRenderMaterial(texname, GetBlockDef());
	setFaceMtl(DIR_NEG_Y, bottommtl==NULL ? topmtl : bottommtl);

	if(sidemtl) sidemtl->Release();
	if(topmtl) topmtl->Release();
	if(bottommtl) bottommtl->Release();
}

RenderBlockMaterial *LogBlockMaterial::getFaceMtl(const BiomeDef *biome, DirectionType dir, int blockdata, BlockColor &facecolor)
{
	const int top = DIR_POS_Y;
	const int bottom = DIR_NEG_Y;
	const int side = DIR_NEG_X;

	int index;

	blockdata = getDirBlockData(blockdata);

	if(blockdata == 0) index = dir;
	else if(blockdata == 1) //top to negx
	{
		if(dir == DIR_NEG_X) index = bottom;
		else if(dir == DIR_POS_X) index = top;
		else index = side;
	}
	else if(blockdata == 2) //top to posx
	{
		if(dir == DIR_POS_X) index = bottom ;
		else if(dir == DIR_NEG_X) index = top;
		else index = side;
	}
	else if(blockdata == 3) //top to negz
	{
		if(dir == DIR_NEG_Z) index = bottom ;
		else if(dir == DIR_POS_Z) index = top;
		else index = side;
	}
	else if(blockdata == 4) //top to posz
	{
		if(dir == DIR_POS_Z) index = bottom;
		else if(dir == DIR_NEG_Z) index = top;
		else index = side;
	}
	else
	{
		//assert(0);
		index = dir;
	}

	return getRenderMtlMgr().getMtl(m_mtlsIndex[index]);//m_Mtls[index];
}

int LogBlockMaterial::getPlaceBlockData(World *pworld, const WCoord &blockpos, DirectionType face, float hitptx, float hity, float hitpty, int def_blockdata)
{
	

	if(getBlockResID()==BLOCK_REDSOIL || getBlockResID()==BLOCK_BOOK_SHELF || getBlockResID() == BLOCK_WIND_EROSIONS_STONE)
	{
		return 0;
	}
	else
	{
		if(face == DIR_NEG_X) return 1;
		else if(face == DIR_POS_X) return 2;
		else if(face == DIR_NEG_Z) return 3;
		else if(face == DIR_POS_Z) return 4;
		else return 0;
	}

	
}

bool LogBlockMaterial::renderTexRotByDir()
{
	return true;
}

int LogBlockMaterial::convertDataByRotate(int blockdata, int rotatetype)
{
	int oriBlockData = blockdata;
	blockdata = getDirBlockData(blockdata);
	int curDir = blockdata-1;
	if (curDir < 0)
		return blockdata;

	curDir = this->commonConvertDataByRotate(curDir, rotatetype);

	return MergeBlockData(oriBlockData, curDir+1);
}

int LogBlockMaterial::getDirBlockData(int pOriData)
{
	return pOriData&7;  //低3位表示方向
}

int LogBlockMaterial::MergeBlockData(int pOriData, int pNewDirData)
{
	return (pOriData & 8) | pNewDirData;
}

void LogBlockMaterial::onBlockRemoved(World *pworld, const WCoord &blockpos, int blockid, int blockdata)
{
	/*Chunk *pchunk = pworld->getChunk(blockpos);
	WCoord offset = blockpos - pchunk->m_Origin;
	if(pchunk) pchunk->removeSearchBlock(offset.x, offset.y, offset.z, getBlockResID()); */

	if(!IsWoodBlockID(getBlockResID())) return;

	int range = 4;
	int range2 = range + 1;

	if (pworld->checkChunksExist(blockpos-range2, blockpos+range2))
	{
		for (int x = -range; x <= range; ++x)
		{
			for (int y = -range; y <= range; ++y)
			{
				for (int z = -range; z <= range; ++z)
				{
					WCoord curpos = blockpos + WCoord(x,y,z);
					int blockid = pworld->getBlockID(curpos);

					if (IsLeavesBlockID(blockid))
					{
						int blockdata = pworld->getBlockData(curpos);
						blockdata = getDirBlockData(blockdata);

						if ((blockdata & 8) == 0)
						{
							pworld->setBlockData(curpos, blockdata | 8, 4);
						}
					}
				}
			}
		}
	}
}

void LogBlockMaterial::onBlockPlacedBy(World *pworld, const WCoord &blockpos, IClientPlayer *player)
{
	//摆南瓜灯的时候检测能否生成稻草人
	if (GetBlockDef()->ID == 230)//code-by:hanyunqiang 816（方南瓜灯）替换为230（方南瓜）
	{
		WCoord downPos = DownCoord(blockpos);
		WCoord downDownPos = DownCoord(downPos);
		if (pworld->getBlockID(downPos) == 822 && pworld->getBlockID(downDownPos) == BLOCK_WOODPILE)
		{
			ActorManager* actorMgr = dynamic_cast<ActorManager*>(pworld->getActorMgr());
			if (!actorMgr) return;
			//检测是否有手
			if (pworld->getBlockID(NeighborCoord(downPos, DIR_NEG_X)) == BLOCK_WOODPILE && pworld->getBlockID(NeighborCoord(downPos, DIR_POS_X)) == BLOCK_WOODPILE)
			{
				LOG_INFO("U are right boy.");
				pworld->setBlockAll(downPos, 0, 0, 2);
				pworld->setBlockAll(blockpos, 0, 0, 2);
				pworld->setBlockAll(downDownPos, 0, 0, 2);
				pworld->setBlockAll(NeighborCoord(downPos, DIR_NEG_X), 0, 0, 2);
				pworld->setBlockAll(NeighborCoord(downPos, DIR_POS_X), 0, 0, 2);

				ClientMob *mob = actorMgr->spawnMob(BlockCenterCoord(downPos), 3121, false, false);
				if (mob)
				{
					mob->playSaySound();
					//pworld->getEffectMgr()->playParticleEffect("particles/item_810_1.ent", BlockCenterCoord(downPos), 40);
					pworld->getEffectMgr()->playParticleEffectAsync("particles/acchorse.ent", BlockCenterCoord(downPos), 40);
				}
				return;
			}

			if (pworld->getBlockID(NeighborCoord(downPos, DIR_NEG_Z)) == BLOCK_WOODPILE && pworld->getBlockID(NeighborCoord(downPos, DIR_POS_Z)) == BLOCK_WOODPILE)
			{
				pworld->setBlockAll(downPos, 0, 0, 2);
				pworld->setBlockAll(blockpos, 0, 0, 2);
				pworld->setBlockAll(downDownPos, 0, 0, 2);
				pworld->setBlockAll(NeighborCoord(downPos, DIR_NEG_Z), 0, 0, 2);
				pworld->setBlockAll(NeighborCoord(downPos, DIR_POS_Z), 0, 0, 2);
				ClientMob *mob = actorMgr->spawnMob(BlockCenterCoord(downPos), 3121, false, false);
				if (mob)
				{
					mob->playSaySound();
					//pworld->getEffectMgr()->playParticleEffect("particles/item_810_1.ent", BlockCenterCoord(downPos), 40);
					pworld->getEffectMgr()->playParticleEffectAsync("particles/acchorse.ent", BlockCenterCoord(downPos), 40);
				}
				return;
			}
		}
	}
}

void LogBlockMaterial::onBlockAdded(World *pworld, const WCoord &blockpos)
{
	/*Chunk *pchunk = pworld->getChunk(blockpos);
	WCoord offset = blockpos - pchunk->m_Origin;
	if(pchunk) pchunk->addSearchBlock(offset.x, offset.y, offset.z, getBlockResID()); */
}

void BlockScalyFruit::init(int resid)
{
	LogBlockMaterial::init(resid);

	SetToggle(BlockToggle_VoidNightReplace, true);
}

void BlockScalyFruit::dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata/* =0 */, BLOCK_MINE_TYPE droptype/* =BLOCK_MINE_NOTOOL */, float chance/* =1.0f */, int uin/* = -1 */)
{
	if(GenRandomFloat() > chance) return;
	doDropItem(pworld, blockpos, GetBlockDef()->ToolMineDrops[0].item); //改为直接掉落一个西瓜，code-by:yanfengying
}

bool BlockScalyFruit::beginVoidNightMutant(World* pworld, const WCoord& blockpos)
{
	if (!pworld)
	{
		return false;
	}

	if (g_WorldMgr)
	{
		DangerNightManager* pDNMgr = dynamic_cast<DangerNightManager*>(g_WorldMgr->getDangerNightManager());
		if (pDNMgr)
		{
			pDNMgr->playMutateEffect(pworld, blockpos);
		}
	}

	int blockdata = pworld->getBlockData(blockpos);
	int newBlockData = 0; // 第2位表示是巨布鲁果还是巨鳞羽果， 第4位是标记破坏之后是否重新种植回去
	if (blockdata | 8)
		newBlockData += 8;

	pworld->setBlockAll(blockpos, BLOCK_VOID_MELON_UNKNOWN, newBlockData | 2, kBlockUpdateFlagNeedUpdate); 
	return true;
}

void BlockBlueFruit::init(int resid)
{
	LogBlockMaterial::init(resid);

	SetToggle(BlockToggle_VoidNightReplace, true);
}

void BlockBlueFruit::onBlockAdded(World* pworld, const WCoord& blockpos)
{
	BlockMaterial::onBlockAdded(pworld, blockpos);
}

void BlockBlueFruit::dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata/* =0 */, BLOCK_MINE_TYPE droptype/* =BLOCK_MINE_NOTOOL */, float chance/* =1.0f */, int uin/* = -1 */)
{
	if (GenRandomFloat() > chance) return;
	doDropItem(pworld, blockpos, GetBlockDef()->ToolMineDrops[0].item); //改为直接掉落一个南瓜，code-by:yanfengying
}

bool BlockBlueFruit::beginVoidNightMutant(World* pworld, const WCoord& blockpos)
{
	if (!pworld)
	{
		return false;
	}

	if (g_WorldMgr)
	{
		DangerNightManager* pDNMgr = dynamic_cast<DangerNightManager*>(g_WorldMgr->getDangerNightManager());
		if (pDNMgr)
		{
			pDNMgr->playMutateEffect(pworld, blockpos);
		}
	}

	int blockdata = pworld->getBlockData(blockpos);
	int newBlockData = 0; // // 第2位表示是巨布鲁果还是巨鳞羽果
	if (blockdata | 8)
		newBlockData += 8;
	pworld->setBlockAll(blockpos, BLOCK_VOID_MELON_UNKNOWN, newBlockData, kBlockUpdateFlagNeedUpdate);
	return true;
}

void LogCropperBlockMaterial::onBlockRemoved(World *pworld, const WCoord &blockpos, int blockid, int blockdata)
{
	Chunk *pchunk = pworld->getChunk(blockpos);
	if(pchunk)
	{
		WCoord offset = blockpos - pchunk->m_Origin;
		pchunk->removeSearchBlock(offset.x, offset.y, offset.z, getBlockResID());
	}
}

void LogCropperBlockMaterial::onBlockAdded(World *pworld, const WCoord &blockpos)
{
	Chunk *pchunk = pworld->getChunk(blockpos);
	if(pchunk)
	{
		WCoord offset = blockpos - pchunk->m_Origin;
		pchunk->addSearchBlock(offset.x, offset.y, offset.z, getBlockResID());
	}
}
