
#ifndef __BLOCKJAR_H__
#define __BLOCKJAR_H__

#include "BlockMaterial.h"

class BlockJar : public ModelBlockMaterial //tolua_exports
{ //tolua_exports
	DECLARE_BLOCKMATERIAL(BlockJar)
public:
	BlockJar();

	virtual ~BlockJar();
	void init(int resid);
	//tolua_begin
	virtual int getPlaceBlockData(World *pworld, const WCoord &blockpos, DirectionType face, float hitptx, float hitpty, float hitptz, int def_blockdata);
	virtual void dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata=0, BLOCK_MINE_TYPE droptype=BLOCK_MINE_NOTOOL, float chance=1.0f, int uin = -1);
	virtual int getInitialDataOnPlaced(WORLD_SEED worldSeed, const WCoord &blockpos);
	virtual void blockTick(World* pworld, const WCoord& blockpos);
	virtual void createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh);
	RenderBlockMaterial* m_JarSnowMtl;
	//tolua_end
private:
	virtual int getSelectMethod();
	virtual int getBlockGeomID(int *idbuf, int *dirbuf, const SectionDataHandler* sectionData, const WCoord &blockpos, World* world);
	virtual int getProtoBlockGeomID(int *idbuf, int *dirbuf);
}; //tolua_exports

class BlockJarEx : public BlockJar //tolua_exports
{ //tolua_exports
	DECLARE_BLOCKMATERIAL(BlockJarEx)
public:
	//tolua_begin
	void onPlayRandEffect(World *pworld, const WCoord &blockpos);
	virtual void dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata=0, BLOCK_MINE_TYPE droptype=BLOCK_MINE_NOTOOL, float chance=1.0f, int uin = -1);
	virtual void onBlockRemoved(World *pworld, const WCoord &blockpos, int blockid, int blockdata) override;
	virtual void onBlockDestroyedBy(World *pworld, const WCoord &blockpos, int blockdata, BLOCK_DESTROY_REASON_T destroytype, IClientActor *player);
	//tolua_end
}; //tolua_exports

class BlockChrismasBox : public BlockJar //tolua_exports
{ //tolua_exports
	DECLARE_BLOCKMATERIAL(BlockChrismasBox)
public:
	//tolua_begin
	BlockChrismasBox();
	virtual ~BlockChrismasBox();

	virtual void init(int resid);
	virtual void createBlockMesh(const BuildSectionMeshData& data, const WCoord &blockpos, SectionMesh *poutmesh) override;
	virtual BlockTexElement *getDestroyTexture(Block pblock, BlockTexDesc &desc) override;

	RenderBlockMaterial *m_OtherMtls[3];
	//tolua_end
}; //tolua_exports

/*
	玩家死亡掉落罐子
*/
class BlockDeathJar : public ModelBlockMaterial
{
	DECLARE_BLOCKMATERIAL(BlockDeathJar)
public:
	virtual void init(int resid);
	virtual WorldContainer* createContainer(World* pworld, const WCoord& blockpos) override;
	virtual std::string getGeomName() override
	{
		return "cube";
	}
	virtual void createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh);
	virtual void createCollideData(CollisionDetect* coldetect, World* pworld, const WCoord& blockpos);
private:
	int m_destroyJarOwnerUin;
};

#endif