
#ifndef __BLOCKGRAYHERBS_H__
#define __BLOCKGRAYHERBS_H__

#include "BlockMaterial.h"
#include "SandboxGame.h"
extern float getPlantGrowRate(World *pworld, const WCoord &blockpos, int resid);

class EXPORT_SANDBOXGAME HerbMaterial;
class HerbMaterial : public BlockMaterial //tolua_exports
{ //tolua_exports
	DECLARE_BLOCKMATERIAL(HerbMaterial)
public:
	virtual void init(int resid) override;
	//tolua_begin
	HerbMaterial() : m_RenderInPot(0), m_RenderPotOffsetY(0.0f){}
	//virtual bool isSolid();

	virtual void onNotify(World *pworld, const WCoord &blockpos, int blockid);
	virtual void blockTick(World *pworld, const WCoord &blockpos);
	virtual bool canPutOntoPos(WorldProxy *pworld, const WCoord &blockpos);
	virtual bool canStayOnPos(WorldProxy *pworld, const WCoord &blockpos);
	virtual void dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata=0, BLOCK_MINE_TYPE droptype=BLOCK_MINE_NOTOOL, float chance=1.0f, int uin = -1);
	virtual bool canAttachedToMecha(int curblockdata, DirectionType dir, int neighbor_id, int neighbor_data) override
	{
		return dir==DIR_NEG_Y;
	}

	void setRenderInPot(int n, float offsety=0) //0: 正常渲染, 1: 第一格, 2: 第二格
	{
		m_RenderInPot = n;
		m_RenderPotOffsetY = offsety;
	}

	BlockMaterial::BlockType BlockTypeId() {return BlockMaterial::BlockType::BlockType_LeafPlane;}
    virtual void onBlockDestroyedBy(World *pworld, const WCoord &blockpos, int blockdata, BLOCK_DESTROY_REASON_T destroytype, IClientActor *bywho);
	//tolua_end
	float getGrowRate(World* pworld, const WCoord& blockpos);
protected:
	virtual bool canThisPlantGrowOnThisBlockID(int blockid);
	void checkHerbChange(World *pworld, const WCoord &blockpos);
	bool dealNewGrow(World* pworld, const WCoord& blockpos, int dataindex, int maxdata = 7);
	void FertilizedPlayEffect(World* pworld, const WCoord& blockpos);
	bool dealFertilized(World* pworld, const WCoord& blockpos, int itemid);
	virtual int getMaxBlockdata();
	int getTemperatureFactor(World* pworld, const WCoord& blockpos);
	int getBlockGrowSpeedUpTime(World* pworld, const WCoord& blockpos,int time);
	int getActorGrowSpeedUpTime(World* pworld, const WCoord& blockpos, int time);
protected:
	int m_RenderInPot;
	float m_RenderPotOffsetY;

}; //tolua_exports

class GrayHerbMaterial : public HerbMaterial //tolua_exports
{ //tolua_exports
	DECLARE_BLOCKMATERIAL(GrayHerbMaterial)
public:
	//tolua_begin
	GrayHerbMaterial();

	virtual ~GrayHerbMaterial();
	//virtual const char *getGeomName();
	virtual void init(int resid);
	virtual void blockTick(World* pworld, const WCoord& blockpos);
	virtual void update(unsigned int dtick) override;
	virtual void createBlockMesh(const BuildSectionMeshData& data, const WCoord &blockpos, SectionMesh *poutmesh) override;
	virtual BlockTexElement *getDestroyTexture(Block pblock, BlockTexDesc &desc) override;
	virtual void createCollideData(CollisionDetect *coldetect, World *pworld, const WCoord &blockpos);
	virtual SectionMesh* createBlockProtoMesh(int protodata=0);
	virtual int getBlockGeomID(int* idbuf, int* dirbuf, const SectionDataHandler* sectionData, const WCoord& blockpos, World* world);
	virtual int getProtoBlockGeomID(int* idbuf, int* dirbuf);
	virtual void setPlantSnowCoverThickness(Rainbow::Vector4f value) override;
	//tolua_end
	virtual void BlendBlockVertexColor(const Rainbow::Vector4f& blockWorldPos, BlockColor& vertexColor) override;
private:
	virtual void initGeomName() override;
private:
	RenderBlockMaterial *m_BottomMtl;
	RenderBlockMaterial *m_TopMtl;
	RenderBlockMaterial* m_SnowMtl;
	RenderBlockMaterial* m_WhiteMtl;
	Rainbow::Vector4f m_PlantSnowCoverThickness;
	RenderBlockMaterial* m_BottomSnowCoverMtl;
	RenderBlockMaterial* m_TopSnowCoverMtl;
}; //tolua_exports

#endif