#ifndef __BLOCKFEEDTROUGH_H__
#define __BLOCKFEEDTROUGH_H__

#include "BlockMaterial.h"
#include "container_feedtrough.h"
//tolua_begin
enum FeedTroughDirectionType
{
	FRONT_RIGHT = 0, //前右
	FRONT_LEFT, //前左
	BACK_RIGHT, //后右
	BACK_LEFT, //后左
};
//tolua_end

class BlockFeedTrough: public ModelBlockMaterial //tolua_exports
{ //tolua_exports
	DECLARE_BLOCKMATERIAL(BlockFeedTrough)
public:
	//tolua_begin
	BlockFeedTrough();
	virtual ~BlockFeedTrough();

	virtual void init(int resid);
	//virtual const char *getGeomName()
	//{
	//	return "trough";
	//}
	//virtual bool hasContainer() override
	//{
	//	return true;
	//}
	//tolua_end
public:
	//tolua_begin
	virtual void createBlockMesh(const BuildSectionMeshData& data, const WCoord &blockpos, SectionMesh *poutmesh) override;
	virtual bool onTrigger(World *pworld, const WCoord &blockpos, DirectionType face, IClientPlayer *player, const Rainbow::Vector3f &colpoint=Rainbow::Vector3f(0, 0, 0));
	
	virtual void onBlockPlacedBy(World *pworld, const WCoord &blockpos, IClientPlayer *player);
	virtual void onBlockAdded(World *pworld, const WCoord &blockpos);
	virtual void onBlockRemoved(World *pworld, const WCoord &blockpos, int blockid, int blockdata);
	virtual WorldContainer *createContainer(World *pworld, const WCoord &blockpos) override;	
	//virtual void dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata, BLOCK_MINE_TYPE droptype, float chance, int uin = -1);
	virtual int getBlockGeomID(int *idbuf, int *dirbuf, const SectionDataHandler* sectionData, const WCoord &blockpos, World* world) override;
	virtual void onNotify(World *pworld, const WCoord &blockpos, int blockid) override;
	static int GetMeshIDFromBlocks(const SectionDataHandler* sectionData, World* world, const WCoord &blockpos, int &geomdir, bool &is_upside_down); //计算特殊模型饲养槽
	static void calTwoSpecialAngleCenterData(const WCoord& tPos, World* pworld, FeedTroughContainer* neiContainer1, FeedTroughContainer* neiContainer2, FeedTroughDirectionType dir, int &index, int &geomdir);//计算和两个拐角相连的饲养槽数据
	static void calOneSpecialAngleCenterData(const WCoord& tPos, World *pworld, FeedTroughContainer* container, FeedTroughContainer* neiContainer1, FeedTroughContainer* neiContainer2, FeedTroughDirectionType dir, int& index, int& geomdir, int rightDir);//计算和一个拐角相连的饲养槽数据
	static bool calDirectionBlock(const SectionDataHandler* sectionData, World* pworld, const WCoord& blockpos, int& index, int& geomdir, FeedTroughDirectionType dir, const WCoord& posOne, const WCoord& posTwo); //计算和有拐角相连的饲养槽模型数据
	static int calNeighborBlock(World* pworld, const SectionDataHandler* sectionData, const WCoord& blockpos, int dir); //计算两侧是否有饲养槽，根据两侧饲养槽改变模型
	static void calIndexCanChange(FeedTroughContainer* container, int &index, int &dir, const WCoord& oldLeftPos, const WCoord& oldRightPos);
	void handleNearPlacedBlock(World* pworld, const WCoord& blockpos, int blockid);
	void notifyNearSameBlock(World* pworld, const WCoord& blockpos, int blockid);
	//tolua_end
private:
	virtual void initGeomName() override;
private:	
	RenderBlockMaterial *m_StageMtls[3];

public:
	DECLARE_BLOCKMATERIAL_INSTANCE_BEGIN(BlockFeedTrough)
		DECLARE_BLOCKMATERIAL_INSTANCE_R_PARAM(R_Dir, int)

		void SetBlockDir(const int& dir);
		int GetBlockDir() const;
	DECLARE_BLOCKMATERIAL_INSTANCE_END(BlockFeedTrough)

}; //tolua_exports
#endif

