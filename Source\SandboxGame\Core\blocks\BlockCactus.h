
#ifndef __BLOCKCACTUS_H__
#define __BLOCKCACTUS_H__

#include "BlockMaterial.h"

//仙人掌基类
class CactusBase :
	public ModelBlockMaterial
{
	DECLARE_BLOCKMATERIAL(CactusBase)
	//typedef ModelBlockMaterial Super;
public:
	enum { DOWNDATA = 9, CENTERDATA = 10, TOPDATA = 11 };
	std::map<int, int> objIndex{ {DOWNDATA,7},{CENTERDATA,0},{TOPDATA,2} };//按顺序为仙人掌圆球，仙人掌主干，仙人掌头部的模型
	CactusBase();
	virtual ~CactusBase();
	//virtual const char* getGeomName() override;
	virtual void init(int resid)override;
	
	virtual void forceResh(World* pworld, const WCoord& blockpos) override;
	//virtual void onBlockDestroyedBy(World* pworld, const WCoord& blockpos, int blockdata, BLOCK_DESTROY_REASON_T destroytype, IClientActor* bywho)override;
	virtual void onNotify(World* pworld, const WCoord& blockpos, int blockid)override;
	virtual bool onFertilized(World* pworld, const WCoord& blockpos, int fertiliser) override;
	bool checkAroundBlock(World* pworld, const WCoord& blockpos);//检测3x3格子，高度1范围内是否存在其他方块
	bool checkTopBlock(World* pworld, const WCoord& blockpos, int topNum);//检测高度topNum是否存在其他方块，包括topNum本身
	virtual bool markOrGrowMarked(World* pworld, const WCoord& blockpos);
	virtual bool growTree(World* pworld, const WCoord& blockpos);
	virtual bool onActorCollidedWithBlock(World* pworld, const WCoord& blockpos, IClientActor* actor) override;
	virtual void onActorWalking(World* pworld, const WCoord& blockpos, IClientActor* actor) override;
	void playPunchEffect(World* pworld, const WCoord& blockpos, IClientActor* actor);//播放击退特效
	void addBloodBuff(IClientActor* actor);//添加流血BUFF

	virtual void initGeomName();
};

//仙人掌幼苗 blockid=458

class CactusSmallSeed :
	public CactusBase
{
	//typedef CactusBase Super;
	DECLARE_BLOCKMATERIAL(CactusSmallSeed)
public:
	CactusSmallSeed();
	virtual ~CactusSmallSeed();

private:
	virtual bool canPutOntoPos(WorldProxy* pworld, const WCoord& blockpos)override;
	bool canTreeGrow(World* pworld, const WCoord& blockpos);
	virtual void blockTick(World* pworld, const WCoord& blockpos) override;
	virtual int getBlockGeomID(int* idbuf, int* dirbuf, const SectionDataHandler* sectionData, const WCoord& blockpos, World* world)override;
	virtual bool growTree(World* pworld, const WCoord& blockpos) override;
	virtual bool onActorCollidedWithBlock(World* pworld, const WCoord& blockpos, IClientActor* actor) override;
	virtual bool onFertilized(World* pworld, const WCoord& blockpos, int fertiliser) override;
};
//仙人掌苗 blockid=459
class CactusSeed :
	public CactusBase
{
	//typedef CactusBase Super;
	DECLARE_BLOCKMATERIAL(CactusSeed)
public:
	CactusSeed();
	virtual ~CactusSeed();
	virtual bool canPutOntoPos(WorldProxy* pworld, const WCoord& blockpos)override;
	bool canTreeGrow(World* pworld, const WCoord& blockpos);
	virtual bool growTree(World* pworld, const WCoord& blockpos) override;
	virtual bool onFertilized(World* pworld, const WCoord& blockpos, int fertiliser) override;
private:
	virtual int getBlockGeomID(int* idbuf, int* dirbuf, const SectionDataHandler* sectionData, const WCoord& blockpos, World* world)override;
	virtual void blockTick(World* pworld, const WCoord& blockpos) override;
};

//仙人掌茎 blockid=242
class CactusMaterial : public CactusBase //tolua_exports
{ //tolua_exports
	//typedef CactusBase Super;

	DECLARE_BLOCKMATERIAL(CactusMaterial)
public:

	CactusMaterial();

	virtual ~CactusMaterial();
	//virtual void init(int resid) override;
	//tolua_end
	//tolua_begin

	virtual void onBlockRemoved(World* pworld, const WCoord& blockpos, int blockid, int blockdata);
	virtual void createPickData(CollisionDetect* coldetect, World* pworld, const WCoord& blockpos) override;
	virtual void createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh) override;
	//virtual void onBlockDestroyedBy(World* pworld, const WCoord& blockpos, int blockdata, BLOCK_DESTROY_REASON_T destroytype, IClientActor* bywho)override;
	virtual bool onFertilized(World* pworld, const WCoord& blockpos, int fertiliser) override;
	virtual int getPlaceBlockData(World* pworld, const WCoord& blockpos, DirectionType face, float hitptx, float hitpty, float hitptz, int def_blockdata);
	virtual bool hasActorCollidedWithBlockLogic() { return true; }
	//virtual void blockTick(World *pworld, const WCoord &blockpos);
	virtual bool canPutOntoPos(WorldProxy* pworld, const WCoord& blockpos);
	virtual void onNotify(World* pworld, const WCoord& blockpos, int blockid);
	//virtual bool hasActorCollidedWithBlockLogic() { return true; }
	virtual void createCollideData(CollisionDetect* coldetect, World* pworld, const WCoord& blockpos) override;
	virtual bool onActorCollidedWithBlock(World* pworld, const WCoord& blockpos, IClientActor* actor) override;
	//virtual void onActorWalking(World *pworld, const WCoord &blockpos, IClientActor *actor) override;
	virtual bool canPutOntoFace(WorldProxy* pworld, const WCoord& blockpos, int face) override;
	bool canStayOnPos(WorldProxy* pworld, const WCoord& blockpos);
	//virtual void dropBlockAsItem(World* pworld, const WCoord& blockpos, int blockdata = 0, BLOCK_MINE_TYPE droptype = BLOCK_MINE_NOTOOL, float chance = 1.0f, int uin = -1) override;
private:
	virtual int getBlockGeomID(int* idbuf, int* dirbuf, const SectionDataHandler* sectionData, const WCoord& blockpos, World* world)override;
	void destroyAroundBranch(World* pworld, const WCoord& blockpos);
	bool checkHasFlower(WorldProxy* pworld, const WCoord& blockpos);
	//tolua_end
	
}; //tolua_exports


//仙人掌左右分支
class CactusBranch : public CactusBase //tolua_exports
{ //tolua_exports
	//typedef CactusBase Super;
	DECLARE_BLOCKMATERIAL(CactusBranch)
public:
	CactusBranch();
	virtual ~CactusBranch();

	//tolua_begin
	virtual bool canPutOntoPos(WorldProxy* pworld, const WCoord& blockpos);
	virtual bool onFertilized(World* pworld, const WCoord& blockpos, int fertiliser) override;
	virtual bool hasActorCollidedWithBlockLogic() { return true; }
	virtual int getPlaceBlockData(World* pworld, const WCoord& blockpos, DirectionType face, float hitptx, float hitpty, float hitptz, int def_blockdata)override;
	virtual bool onActorCollidedWithBlock(World* pworld, const WCoord& blockpos, IClientActor* actor) override;
	virtual void onNotify(World* pworld, const WCoord& blockpos, int blockid) override;
private:
	virtual int getBlockGeomID(int* idbuf, int* dirbuf, const SectionDataHandler* sectionData, const WCoord& blockpos, World* world)override;
	//tolua_end
}; //tolua_exports


//仙人掌果实
class CactusFruit : public CactusBase //tolua_exports
{ //tolua_exports
	//typedef CactusBase Super;
	DECLARE_BLOCKMATERIAL(CactusFruit)
public:
	CactusFruit();
	virtual ~CactusFruit();
	//tolua_begin
	virtual bool onActorCollidedWithBlock(World* pworld, const WCoord& blockpos, IClientActor* actor) override;
	virtual int getPlaceBlockData(World* pworld, const WCoord& blockpos, DirectionType face, float hitptx, float hitpty, float hitptz, int def_blockdata)override;
private:
	virtual int getBlockGeomID(int* idbuf, int* dirbuf, const SectionDataHandler* sectionData, const WCoord& blockpos, World* world)override;
	//tolua_end
	//virtual void initGeomName() override;
}; //tolua_exports

//仙人掌花
class CactusFlower : public CactusBase //tolua_exports
{ //tolua_exports
	//typedef CactusBase Super;
	DECLARE_BLOCKMATERIAL(CactusFlower)
public:
	CactusFlower();
	virtual ~CactusFlower();

	//tolua_begin

	virtual bool onActorCollidedWithBlock(World* pworld, const WCoord& blockpos, IClientActor* actor) override;
	virtual bool canPutOntoFace(WorldProxy* pworld, const WCoord& blockpos, int face) override;
private:
	virtual int getBlockGeomID(int* idbuf, int* dirbuf, const SectionDataHandler* sectionData, const WCoord& blockpos, World* world)override;


	//tolua_end
}; //tolua_exports

#endif