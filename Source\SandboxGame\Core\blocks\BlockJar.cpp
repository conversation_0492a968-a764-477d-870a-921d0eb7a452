
#include "BlockJar.h"
#include "container_world.h"
#include "container_deathJar.h"
#include "OgreUtils.h"
#include "world.h"
#include "BlockGeom.h"
//#include "GameEvent.h"
#include "WorldManager.h"
#include "WeatherManager.h"
#include "IPlayerControl.h"
#include "IClientActor.h"
#include "ClientItem.h"
#include "SandboxResult.h"
#include "ShareRenderMaterial.h"
#include "BlockMaterialMgr.h"
#include "basesection.h"
#include "SectionMesh.h"
#include "IClientGameManagerInterface.h"
#include "CommonUtil.h"
#include "CityConfig.h"
#include "PlayerControl.h"
using namespace MNSandbox;

IMPLEMENT_BLOCKMATERIAL(BlockJar)
//IMPLEMENT_BLOCKINSTANCE(BlockJar)
IMPLEMENT_BLOCKMATERIAL(BlockJarEx)
IMPLEMENT_BLOCKMATERIAL(BlockChrismasBox)
IMPLEMENT_BLOCKMATERIAL(BlockDeathJar)

BlockJar::BlockJar() : m_JarSnowMtl(NULL)
{

}
BlockJar::~BlockJar()
{
	ENG_RELEASE(m_JarSnowMtl);
}
void BlockJar::init(int resid)
{
	ModelBlockMaterial::init(resid);
	m_nSpecialLogicType[0] |= BlockMaterial::BlockSpceialLogicTeam0::BlockFaceToPlane;
	m_nSpecialLogicType[0] |= BlockMaterial::BlockSpceialLogicTeam0::BranchNotLink;
	m_nSpecialLogicType[0] |= BlockMaterial::BlockSpceialLogicTeam0::IsRoundBlock;
	m_JarSnowMtl = g_BlockMtlMgr.createRenderMaterial("snow", m_Def, GETTEX_WITHDEFAULT, getDrawType(), getMipmapMethod());
	SetToggle(BlockToggle_RandomTick, true);
}

int BlockJar::getInitialDataOnPlaced(WORLD_SEED worldSeed, const WCoord &blockpos)
{
	ChunkRandGen randgen;
	WORLD_ID s = worldSeed;
	s = (s ^ blockpos.x) * (s ^ blockpos.y) * (s ^ blockpos.z);
	randgen.setSeed64(s);

	return randgen.get(3);
}


int BlockJar::getPlaceBlockData(World *pworld, const WCoord &blockpos, DirectionType face, float hitptx, float hitpty, float hitptz, int def_blockdata)
{
	return getInitialDataOnPlaced(pworld->getRandomSeed(), blockpos);
}

int BlockJar::getSelectMethod()
{
	if(getBlockResID() == 747 || getBlockResID()==847) return 1;
	else return 0;
}

void BlockJar::dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata, BLOCK_MINE_TYPE droptype, float chance, int uin/* = -1 */)
{
	if(droptype == BLOCK_MINE_NONE) return;
	if(GenRandomFloat() > chance) return;


	//if (CityConfig::getSingletonPtr() && CityConfig::getSingletonPtr()->getOtherConfig().m_useModReward == true)
	if (false)
	{
		//todo 抛出掉落mod奖励事件
		ObserverEvent obevent;
		jsonxx::Object customObj;
		customObj << "x" << blockpos.x;
		customObj << "y" << blockpos.y;
		customObj << "z" << blockpos.z;
		obevent.SetData_CustomStr(customObj.json_nospace());
		GetObserverEventManager().OnTriggerEvent("Block.DropModReward", &obevent);
	}
	else
	{
		std::vector<GenerateItemDesc>items;
		int selmethod = getSelectMethod();
		WorldContainerMgr::generateChestItems(items, getBlockResID(), NULL, selmethod);
		for (size_t i = 0; i < items.size(); i++)
		{
			doDropItem(pworld, blockpos, items[i].itemid, items[i].itemnum);
		}
	}
}

void BlockJar::blockTick(World* pworld, const WCoord& blockpos)
{
	int blockId = pworld->getBlockID(blockpos);
	if (blockId >= 737 && blockId <= 739)
	{
		int blockdata = pworld->getBlockData(blockpos);
		if (pworld && pworld->getWeatherMgr())
		{
			int weather = pworld->getWeatherMgr()->getWeather(blockpos);
			if (((blockdata & 4) == 0) && (weather == GROUP_BLIZZARD_WEATHER || weather == GROUP_SNOW_WEATHER))
			{
				pworld->setBlockAll(blockpos, blockId, blockdata | 4, 2);//覆雪
			}
		}
	}
}

void BlockJar::createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh)
{
	BlockGeomTemplate* geom = getGeom(data.m_LODLevel);
	if (!geom)
		return;

	auto psection = data.m_SharedSectionData;
	Block pblock = psection->getBlock(blockpos);
	int blockdata = pblock.getData();
	bool isSnowing = false;
	if ((blockdata & 4) == 4)
	{
		isSnowing = true;
	}
	Rainbow::ColorRGBAf verts_light[1] = { Rainbow::ColorRGBAf::white };
	psection->getBlockVertexLight(blockpos, verts_light);

	int idbuf[32];
	int dirbuf[32];
	int ngeom = getBlockGeomID(idbuf, dirbuf, psection, blockpos, data.m_World);
	BlockGeomMeshInfo meshinfo;
	BlockGeomMeshInfo meshinfo_snow;
	RenderBlockMaterial* pmtl = getDefaultMtl(); //getGeomMtl(psection, blockpos);
	RenderBlockMaterial* pmtl_snow = m_JarSnowMtl; //getGeomMtl(psection, blockpos);
	SectionSubMesh* psubmesh = poutmesh->getSubMesh(pmtl);
	SectionSubMesh* psubmesh_snow = poutmesh->getSubMesh(pmtl_snow);
	int dir = dirbuf[0] & 0xffff;
	int mirrortype = (dirbuf[0] >> 16) & 3;
	geom->getFaceVerts(meshinfo, idbuf[0], 1.0f, 0, dir, mirrortype);
	psubmesh->addGeomBlockLight(meshinfo, &blockpos, verts_light, NULL, pmtl->getUVTile());
	
	if (isSnowing == true)
	{
		idbuf[1] = 3;
		int dir = dirbuf[1] & 0xffff;
		int mirrortype = (dirbuf[1] >> 16) & 3;
		//getGeom()->getFaceVerts(meshinfo, idbuf[1], 1.0f, 0, dir, mirrortype);
		geom->getFaceVerts(meshinfo_snow, idbuf[1], 1.0f, 0, dir, mirrortype);
		psubmesh_snow->addGeomBlockLight(meshinfo_snow, &blockpos, verts_light, NULL, pmtl_snow->getUVTile());
	}

}

int BlockJar::getBlockGeomID(int *idbuf, int *dirbuf, const SectionDataHandler* sectionData, const WCoord &blockpos, World* world)
{
	int data = sectionData->getBlock(blockpos).getData();
	bool isSnowing = false;
	if ((data & 4) == 4)//第三位为覆雪标记
	{
		isSnowing = true;
		int mask = ~(1 << 2);
		data = data & mask;
	}
	if (data == 3)
	{
		data = GenRandomInt(0, 2);//data等于3时，没有对应子模型
	}
	idbuf[0] = data;
	
	int returnNegom = 1;
	if (isSnowing == true)
	{
		idbuf[1] = 3;
		returnNegom = 2;
		dirbuf[1] = DIR_NEG_X;
	}
	int meshsize = getGeom(0)->getMeshCount();
	if(data >= meshsize){
		idbuf[0] = 0;
	}
	dirbuf[0] = DIR_NEG_Z;
	return returnNegom;
}

int BlockJar::getProtoBlockGeomID(int *idbuf, int *dirbuf)
{
	idbuf[0] = 0;
	dirbuf[0] = DIR_NEG_Z;
	return 1;
}

#include "EffectManager.h"
void BlockJarEx::onPlayRandEffect(World *pworld, const WCoord &blockpos)
{
	int blockID = pworld->getBlockID(blockpos.x, blockpos.y, blockpos.z);
	if (blockID == 1043 || blockID == 1044)
	{
		pworld->getEffectMgr()->playParticleEffectAsync("particles/item_1043.ent", blockpos*BLOCK_SIZE+WCoord(BLOCK_SIZE/2, 0, BLOCK_SIZE/2), 40, GenRandomFloat()*360.0f, 0, false);
	}
}

void BlockJarEx::dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata, BLOCK_MINE_TYPE droptype, float chance, int uin/* = -1 */)
{
	int blockID = pworld->getBlockID(blockpos.x, blockpos.y, blockpos.z);
	if (blockID == 1043 || blockID == 1044)
	{
		pworld->getEffectMgr()->playParticleEffectAsync("particles/item_1043_break.ent", blockpos*BLOCK_SIZE+WCoord(BLOCK_SIZE/2, 0, BLOCK_SIZE/2), 20, GenRandomFloat()*360.0f, 0, false);
	}

	BlockJar::dropBlockAsItem(pworld, blockpos, blockdata, droptype, chance, uin);
}

void BlockJarEx::onBlockRemoved(World *pworld, const WCoord &blockpos, int blockid, int blockdata)
{
	ModelBlockMaterial::onBlockRemoved(pworld, blockpos, blockid, blockdata);

	//获得星球宝藏成就上报
	//if(pworld->isSurviveMode())
	//	GetGameEventQue().postArchievement_GetTotemCore(blockpos.x, blockpos.y, blockpos.z);
}

void BlockJarEx::onBlockDestroyedBy(World *pworld, const WCoord &blockpos, int blockdata, BLOCK_DESTROY_REASON_T destroytype, IClientActor *player)
{
	if(GetWorldManagerPtr()->isSurviveMode())
	{
		WORLD_ID uin = player->getObjId();
		//ge GetGameEventQue().postArchievement_GetTotemCore(blockpos.x, blockpos.y, blockpos.z, (long)uin);
		MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
			SetData_Number("nType", 1002).
			SetData_Number("nData", (blockpos.x * 1000 + blockpos.y * 100 + blockpos.z * 10) % 10000).
			SetData_Number("uin", (long)uin);
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_PLAYERCENTER_ARCHIEVEMENT_REPORT", sandboxContext);
		}
	}
}

//-------------------------------------------------------------------------------------------------------------------
//#include "OgreMaterial.h"
#include "section.h"
#include "BlockMaterialMgr.h"
#include "SectionMesh.h"
#include "BlockGeom.h"

BlockChrismasBox::BlockChrismasBox()
{
	memset(m_OtherMtls, 0, sizeof(m_OtherMtls));
}

BlockChrismasBox::~BlockChrismasBox()
{
	for(int i=0; i<3; i++)
	{
		ENG_RELEASE(m_OtherMtls[i]);
	}
}

void BlockChrismasBox::init(int resid)
{
	BlockJar::init(resid);

	if(m_LoadOnlyLogic) return;

	char texname[256];
	for(int i=0; i<3; i++)
	{
		sprintf(texname, "%s%d", m_Def->Texture1.c_str(), i);
		m_OtherMtls[i] = g_BlockMtlMgr.createRenderMaterial(texname, m_Def, GETTEX_WITHDEFAULT);
	}
}

void BlockChrismasBox::createBlockMesh(const BuildSectionMeshData& data, const WCoord &blockpos, SectionMesh *poutmesh)
{
	BlockGeomTemplate* geom = getGeom(data.m_LODLevel);
	if (!geom)
		return;
	auto psection = data.m_SharedSectionData;
	Rainbow::ColorRGBAf verts_light[1] = { Rainbow::ColorRGBAf::black };
	psection->getBlockVertexLight(blockpos, verts_light);

	int blockdata = psection->getBlock(blockpos).getData();
	if(blockdata > 2) blockdata = 2;

	BlockGeomMeshInfo meshinfo;
	SectionSubMesh *psubmesh = poutmesh->getSubMesh(m_OtherMtls[blockdata]);

	geom->getFaceVerts(meshinfo, blockdata, 1.0f, 0, DIR_NEG_Z);
	psubmesh->addGeomBlockLight(meshinfo, &blockpos, verts_light, NULL, m_OtherMtls[blockdata]->getUVTile());
}

BlockTexElement *BlockChrismasBox::getDestroyTexture(Block pblock, BlockTexDesc &desc)
{
	int blockdata = pblock.getData();
	if(blockdata > 2) blockdata = 2;

	desc.blendmode = MINIW::BLEND_ALPHATEST;
	desc.gray = false;
	return m_OtherMtls[blockdata]->getTexElement();
}

/*------------------------------------------------玩家死亡遗落的罐子begin----------------------------------------------------*/
void BlockDeathJar::init(int resid)
{
	ModelBlockMaterial::init(resid);
	//创建container
	SetToggle(BlockToggle_HasContainer, true);
}

WorldContainer* BlockDeathJar::createContainer(World* pworld, const WCoord& blockpos)
{
	ContainerDeathJar* c = SANDBOX_NEW(ContainerDeathJar, blockpos, DIR_NEG_Z);
	return c;
}

void BlockDeathJar::createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh) {}

void BlockDeathJar::createCollideData(CollisionDetect* coldetect, World* pworld, const WCoord& blockpos)
{
	coldetect->addObstacle(blockpos * BLOCK_SIZE, blockpos * BLOCK_SIZE + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
}

/*------------------------------------------------玩家死亡遗落的罐子end----------------------------------------------------*/