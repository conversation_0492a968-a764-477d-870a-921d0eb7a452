
#include "BlockHorizontalTriangleSlab.h"
#include "BlockMaterialMgr.h"
#include "section.h"
#include "SectionMesh.h"
#include "BlockGeom.h"
#include "world.h"
#include "DefManagerProxy.h"
#include "PlayerControl.h"
#include "BlockMeshVert.h"
#include "WorldManager.h"
#include "WeatherManager.h"
#include "Common/GameStatic.h"
#include "special_blockid.h"
#include "ClientPlayer.h"
IMPLEMENT_SCENEOBJECTCLASS(HorizontalTriangleHalfSlabMaterial)
//IMPLEMENT_BLOCKINSTANCE(WholeTriangleMaterial)
using namespace Rainbow;

static MINIW::GameStatic<dynamic_array<dynamic_array<BlockGeomVert>>> s_mHalfWholeFace;
dynamic_array<dynamic_array<BlockGeomVert>>& HorizontalTriangleHalfSlabMaterial::m_mHalfWholeFace()
{
	return *s_mHalfWholeFace.EnsureInitialized();
}

static MINIW::GameStatic<dynamic_array<dynamic_array<BlockGeomVert>>> s_mHalfFace;
dynamic_array<dynamic_array<BlockGeomVert>>& HorizontalTriangleHalfSlabMaterial::m_mHalfFace()
{
	return *s_mHalfFace.EnsureInitialized();
}
// dynamic_array<dynamic_array<BlockGeomVert>> HorizontalTriangleHalfSlabMaterial::m_mHalfWholeFace;
// dynamic_array<dynamic_array<BlockGeomVert>> HorizontalTriangleHalfSlabMaterial::m_mHalfFace;

float HorizontalTriangleHalfSlabMaterial::getBlockHeight(int blockdata)
{
	if (blockdata & 8)
	{
		if (blockdata & 4)
		{
			return -0.5f;
		}
		else
		{
			return 0.5f;
		}
	}
	else if (blockdata & 4)
	{
		return -0.25f;
	}
	return 0.25f;
}

void HorizontalTriangleHalfSlabMaterial::createCollideData(CollisionDetect* coldetect, World* pworld, const WCoord& blockpos)
{
	auto block = pworld->getBlock(blockpos);
	float blockheight = getBlockHeight(block.getData());
	WCoord pos = blockpos * BLOCK_SIZE;

	if (blockheight == 0.5f)
	{
		coldetect->addObstacle(pos, pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE, BLOCK_SIZE));
	}
	else if (blockheight == -0.5f)
	{
		coldetect->addObstacle(pos + WCoord(0, BLOCK_HALFSIZE, 0), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
	}
	else
	{
		short step = 10;
		float movesize = 1.f / float(step);
		float heightOffset = 1.f / (float(step));
		int heightsize = BLOCK_SIZE * 0.45f;
		int xzsize = BLOCK_SIZE * 0.9f;
		int dir = pworld->getBlockData(blockpos) & 3;

		auto pblock = pworld->getBlock(blockpos);
		int warp = -1;
		int turnDir = -1;

		auto frontBlock = pworld->getBlock(blockpos + g_DirectionCoord[dir]);
		if (pblock.getResID() == frontBlock.getResID() && frontBlock.getData() < 8 && ((pblock.getData() & 4) == (frontBlock.getData() & 4)))
		{
			int frontDir = frontBlock.getData() & 3;
			if (frontDir != dir && frontDir != ReverseDirection(dir))
			{
				warp = 0;
				turnDir = frontDir;
			}
		}
		if (warp == -1)
		{
			auto backBlock = pworld->getBlock(blockpos + g_DirectionCoord[ReverseDirection(dir)]);
			if (pblock.getResID() == backBlock.getResID() && backBlock.getData() < 8 && ((pblock.getData() & 4) == (backBlock.getData() & 4)))
			{
				int backDir = backBlock.getData() & 3;
				if (backDir != dir && backDir != ReverseDirection(dir))
				{
					warp = 1;
					turnDir = backDir;
				}
			}
		}

		if (warp == -1)
		{
			if (blockheight > 0)
			{
				if (dir == 0)
				{
					for (int i = 1; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(int(movesize * i * BLOCK_SIZE), 0, 0), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * heightsize), BLOCK_SIZE));
						drawBox(pos + WCoord(int(movesize * i * BLOCK_SIZE), 0, 0), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * heightsize), BLOCK_SIZE));
					}
				}
				else if (dir == 1)
				{
					for (int i = 1; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(0, 0, 0), pos + WCoord(BLOCK_SIZE - int(movesize * i * BLOCK_SIZE), int(heightOffset * i * heightsize), BLOCK_SIZE));
						drawBox(pos + WCoord(0, 0, 0), pos + WCoord(BLOCK_SIZE - int(movesize * i * BLOCK_SIZE), int(heightOffset * i * heightsize), BLOCK_SIZE));
					}
				}
				else if (dir == 2)
				{
					for (int i = 1; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(0, 0, int(movesize * i * BLOCK_SIZE)), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * heightsize), BLOCK_SIZE));
						drawBox(pos + WCoord(0, 0, int(movesize * i * BLOCK_SIZE)), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * heightsize), BLOCK_SIZE));
					}
				}
				else if (dir == 3)
				{
					for (int i = 1; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(0, 0, 0), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * heightsize), BLOCK_SIZE - int(movesize * i * BLOCK_SIZE)));
						drawBox(pos + WCoord(0, 0, 0), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * heightsize), BLOCK_SIZE - int(movesize * i * BLOCK_SIZE)));
					}
				}
			}
			else
			{
				if (dir == 0)
				{
					for (int i = 1; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(BLOCK_SIZE - int(movesize * i * xzsize), BLOCK_HALFSIZE + int(heightOffset * i * BLOCK_HALFSIZE), 0), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
						drawBox(pos + WCoord(BLOCK_SIZE - int(movesize * i * xzsize), BLOCK_HALFSIZE + int(heightOffset * i * BLOCK_HALFSIZE), 0), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
					}
				}
				else if (dir == 1)
				{
					for (int i = 1; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(0, BLOCK_HALFSIZE + int(heightOffset * i * BLOCK_HALFSIZE), 0), pos + WCoord(int(movesize * i * xzsize), BLOCK_SIZE, BLOCK_SIZE));
						drawBox(pos + WCoord(0, BLOCK_HALFSIZE + int(heightOffset * i * BLOCK_HALFSIZE), 0), pos + WCoord(int(movesize * i * xzsize), BLOCK_SIZE, BLOCK_SIZE));
					}
				}
				else if (dir == 2)
				{
					for (int i = 1; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(0, BLOCK_HALFSIZE + int(heightOffset * i * BLOCK_HALFSIZE), BLOCK_SIZE - int(movesize * i * xzsize)), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
						drawBox(pos + WCoord(0, BLOCK_HALFSIZE + int(heightOffset * i * BLOCK_HALFSIZE), BLOCK_SIZE - int(movesize * i * xzsize)), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
					}
				}
				else if (dir == 3)
				{
					for (int i = 1; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(0, BLOCK_HALFSIZE + int(heightOffset * i * BLOCK_HALFSIZE), 0), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, int(movesize * i * xzsize)));
						drawBox(pos + WCoord(0, BLOCK_HALFSIZE + int(heightOffset * i * BLOCK_HALFSIZE), 0), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, int(movesize * i * xzsize)));
					}
				}
			}
		}
		else if (1 == warp)
		{
			if (blockheight > 0)
			{
				if (dir == 0 && turnDir == 2 || dir == 2 && turnDir == 0)
				{
					for (int i = 1; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(int(movesize * i * BLOCK_SIZE), 0, int(movesize * i * BLOCK_SIZE)), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * heightsize), BLOCK_SIZE));
						drawBox(pos + WCoord(int(movesize * i * BLOCK_SIZE), 0, int(movesize * i * BLOCK_SIZE)), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * heightsize), BLOCK_SIZE));
					}
				}
				else if (dir == 0 && turnDir == 3 || dir == 3 && turnDir == 0)
				{
					for (int i = 1; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(int(movesize * i * BLOCK_SIZE), 0, 0), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * heightsize), BLOCK_SIZE - int(movesize * i * BLOCK_SIZE)));
						drawBox(pos + WCoord(int(movesize * i * BLOCK_SIZE), 0, 0), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * heightsize), BLOCK_SIZE - int(movesize * i * BLOCK_SIZE)));
					}
				}
				else if (dir == 1 && turnDir == 2 || dir == 2 && turnDir == 1)
				{
					for (int i = 1; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(0, 0, int(movesize * i * BLOCK_SIZE)), pos + WCoord(BLOCK_SIZE - int(movesize * i * BLOCK_SIZE), int(heightOffset * i * heightsize), BLOCK_SIZE));
						drawBox(pos + WCoord(0, 0, int(movesize * i * BLOCK_SIZE)), pos + WCoord(BLOCK_SIZE - int(movesize * i * BLOCK_SIZE), int(heightOffset * i * heightsize), BLOCK_SIZE));
					}
				}
				else if (dir == 1 && turnDir == 3 || dir == 3 && turnDir == 1)
				{
					for (int i = 1; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(0, 0, 0), pos + WCoord(BLOCK_SIZE - int(movesize * i * BLOCK_SIZE), int(heightOffset * i * heightsize), BLOCK_SIZE - int(movesize * i * BLOCK_SIZE)));
						drawBox(pos + WCoord(0, 0, 0), pos + WCoord(BLOCK_SIZE - int(movesize * i * BLOCK_SIZE), int(heightOffset * i * heightsize), BLOCK_SIZE - int(movesize * i * BLOCK_SIZE)));
					}
				}
			}
			else
			{
				if (dir == 0 && turnDir == 2 || dir == 2 && turnDir == 0)
				{
					for (int i = 1; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(BLOCK_SIZE - int(movesize * i * xzsize), BLOCK_HALFSIZE + int(heightOffset * i * BLOCK_HALFSIZE), BLOCK_SIZE - int(movesize * i * xzsize)), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
						drawBox(pos + WCoord(BLOCK_SIZE - int(movesize * i * xzsize), BLOCK_HALFSIZE + int(heightOffset * i * BLOCK_HALFSIZE), BLOCK_SIZE - int(movesize * i * xzsize)), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
					}
				}
				else if (dir == 0 && turnDir == 3 || dir == 3 && turnDir == 0)
				{
					for (int i = 1; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(BLOCK_SIZE - int(movesize * i * xzsize), BLOCK_HALFSIZE + int(heightOffset * i * BLOCK_HALFSIZE), 0), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, int(movesize * i * xzsize)));
						drawBox(pos + WCoord(BLOCK_SIZE - int(movesize * i * xzsize), BLOCK_HALFSIZE + int(heightOffset * i * BLOCK_HALFSIZE), 0), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, int(movesize * i * xzsize)));
					}
				}
				else if (dir == 1 && turnDir == 2 || dir == 2 && turnDir == 1)
				{
					for (int i = 1; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(0, BLOCK_HALFSIZE + int(heightOffset * i * BLOCK_HALFSIZE), BLOCK_SIZE - int(movesize * i * xzsize)), pos + WCoord(int(movesize * i * xzsize), BLOCK_SIZE, BLOCK_SIZE));
						drawBox(pos + WCoord(0, BLOCK_HALFSIZE + int(heightOffset * i * BLOCK_HALFSIZE), BLOCK_SIZE - int(movesize * i * xzsize)), pos + WCoord(int(movesize * i * xzsize), BLOCK_SIZE, BLOCK_SIZE));
					}
				}
				else if (dir == 1 && turnDir == 3 || dir == 3 && turnDir == 1)
				{
					for (int i = 1; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(0, BLOCK_HALFSIZE + int(heightOffset * i * BLOCK_HALFSIZE), 0), pos + WCoord(int(movesize * i * xzsize), BLOCK_SIZE, int(movesize * i * xzsize)));
						drawBox(pos + WCoord(0, BLOCK_HALFSIZE + int(heightOffset * i * BLOCK_HALFSIZE), 0), pos + WCoord(int(movesize * (i)*xzsize), BLOCK_SIZE, int(movesize * i * xzsize)));
					}
				}
			}
		}
		else
		{
			if (blockheight > 0)
			{
				if (dir == 0 && turnDir == 2 || dir == 2 || turnDir == 0)
				{
					for (int i = 1; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(int(movesize * i * BLOCK_SIZE), 0, 0), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * heightsize), BLOCK_SIZE));
						drawBox(pos + WCoord(int(movesize * i * BLOCK_SIZE), 0, 0), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * heightsize), BLOCK_SIZE));
						coldetect->addObstacle(pos + WCoord(0, 0, int(movesize * i * BLOCK_SIZE)), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * heightsize), BLOCK_SIZE));
						drawBox(pos + WCoord(0, 0, int(movesize * i * BLOCK_SIZE)), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * heightsize), BLOCK_SIZE));
					}
				}
				else if (dir == 0 && turnDir == 3 || dir == 3 && turnDir == 0)
				{
					for (int i = 1; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(int(movesize * i * BLOCK_SIZE), 0, 0), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * heightsize), BLOCK_SIZE));
						drawBox(pos + WCoord(int(movesize * i * BLOCK_SIZE), 0, 0), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * heightsize), BLOCK_SIZE));
						coldetect->addObstacle(pos + WCoord(0, 0, 0), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * heightsize), BLOCK_SIZE - int(movesize * i * BLOCK_SIZE)));
						drawBox(pos + WCoord(0, 0, 0), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * heightsize), BLOCK_SIZE - int(movesize * i * BLOCK_SIZE)));
					}
				}
				else if (dir == 1 && turnDir == 2 || dir == 2 || turnDir == 1)
				{
					for (int i = 1; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(0, 0, 0), pos + WCoord(BLOCK_SIZE - int(movesize * i * BLOCK_SIZE), int(heightOffset * i * heightsize), BLOCK_SIZE));
						drawBox(pos + WCoord(0, 0, 0), pos + WCoord(BLOCK_SIZE - int(movesize * i * BLOCK_SIZE), int(heightOffset * i * heightsize), BLOCK_SIZE));
						coldetect->addObstacle(pos + WCoord(0, 0, int(movesize * i * BLOCK_SIZE)), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * heightsize), BLOCK_SIZE));
						drawBox(pos + WCoord(0, 0, int(movesize * i * BLOCK_SIZE)), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * heightsize), BLOCK_SIZE));
					}
				}
				else if (dir == 1 && turnDir == 3 || dir == 3 && turnDir == 1)
				{
					for (int i = 1; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(0, 0, 0), pos + WCoord(BLOCK_SIZE - int(movesize * i * BLOCK_SIZE), int(heightOffset * i * heightsize), BLOCK_SIZE));
						drawBox(pos + WCoord(0, 0, 0), pos + WCoord(BLOCK_SIZE - int(movesize * i * BLOCK_SIZE), int(heightOffset * i * heightsize), BLOCK_SIZE));
						coldetect->addObstacle(pos + WCoord(0, 0, 0), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * heightsize), BLOCK_SIZE - int(movesize * i * BLOCK_SIZE)));
						drawBox(pos + WCoord(0, 0, 0), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * heightsize), BLOCK_SIZE - int(movesize * i * BLOCK_SIZE)));
					}
				}

			}
			else
			{
				if (dir == 0 && turnDir == 2 || dir == 2 || turnDir == 0)
				{
					for (int i = 1; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(BLOCK_SIZE - int(movesize * i * xzsize), BLOCK_HALFSIZE + int(heightOffset * i * BLOCK_HALFSIZE), 0), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
						drawBox(pos + WCoord(BLOCK_SIZE - int(movesize * i * xzsize), BLOCK_HALFSIZE + int(heightOffset * i * BLOCK_HALFSIZE), 0), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
						coldetect->addObstacle(pos + WCoord(0, BLOCK_HALFSIZE + int(heightOffset * i * BLOCK_HALFSIZE), BLOCK_SIZE - int(movesize * i * xzsize)), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
						drawBox(pos + WCoord(0, BLOCK_HALFSIZE + int(heightOffset * i * BLOCK_HALFSIZE), BLOCK_SIZE - int(movesize * i * xzsize)), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
					}
				}
				else if (dir == 0 && turnDir == 3 || dir == 3 && turnDir == 0)
				{
					for (int i = 1; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(BLOCK_SIZE - int(movesize * i * xzsize), BLOCK_HALFSIZE + int(heightOffset * i * BLOCK_HALFSIZE), 0), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
						drawBox(pos + WCoord(BLOCK_SIZE - int(movesize * i * xzsize), BLOCK_HALFSIZE + int(heightOffset * i * BLOCK_HALFSIZE), 0), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
						coldetect->addObstacle(pos + WCoord(0, BLOCK_HALFSIZE + int(heightOffset * i * BLOCK_HALFSIZE), 0), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, int(movesize * i * xzsize)));
						drawBox(pos + WCoord(0, BLOCK_HALFSIZE + int(heightOffset * i * BLOCK_HALFSIZE), 0), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, int(movesize * i * xzsize)));
					}
				}
				else if (dir == 1 && turnDir == 2 || dir == 2 || turnDir == 1)
				{
					for (int i = 1; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(0, BLOCK_HALFSIZE + int(heightOffset * i * BLOCK_HALFSIZE), 0), pos + WCoord(int(movesize * i * xzsize), BLOCK_SIZE, BLOCK_SIZE));
						drawBox(pos + WCoord(0, BLOCK_HALFSIZE + int(heightOffset * i * BLOCK_HALFSIZE), 0), pos + WCoord(int(movesize * i * xzsize), BLOCK_SIZE, BLOCK_SIZE));
						coldetect->addObstacle(pos + WCoord(0, BLOCK_HALFSIZE + int(heightOffset * i * BLOCK_HALFSIZE), BLOCK_SIZE - int(movesize * i * xzsize)), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
						drawBox(pos + WCoord(0, BLOCK_HALFSIZE + int(heightOffset * i * BLOCK_HALFSIZE), BLOCK_SIZE - int(movesize * i * xzsize)), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
					}
				}
				else if (dir == 1 && turnDir == 3 || dir == 3 && turnDir == 1)
				{
					for (int i = 1; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(0, BLOCK_HALFSIZE + int(heightOffset * i * BLOCK_HALFSIZE), 0), pos + WCoord(int(movesize * i * xzsize), BLOCK_SIZE, BLOCK_SIZE));
						drawBox(pos + WCoord(0, BLOCK_HALFSIZE + int(heightOffset * i * BLOCK_HALFSIZE), 0), pos + WCoord(int(movesize * i * xzsize), BLOCK_SIZE, BLOCK_SIZE));
						coldetect->addObstacle(pos + WCoord(0, BLOCK_HALFSIZE + int(heightOffset * i * BLOCK_HALFSIZE), 0), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, int(movesize * i * xzsize)));
						drawBox(pos + WCoord(0, BLOCK_HALFSIZE + int(heightOffset * i * BLOCK_HALFSIZE), 0), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, int(movesize * i * xzsize)));
					}
				}
			}
		}
	}
}

bool HorizontalTriangleHalfSlabMaterial::coverNeighbor(int curblockdata, SolidBlockMaterial* neighbor, int neighbor_data, DirectionType dir)
{
	int updown = (curblockdata & 4) >> 2;
	if ((updown + 4) == dir)
	{
		if (dir == DIR_NEG_Y)
		{
			if (neighbor && neighbor->GetAttrRenderType() == BLOCKRENDER_FLUID && neighbor_data) return false;
		}
		return true;
	}
	return false;
}

void HorizontalTriangleHalfSlabMaterial::dropBlockAsItem(World* pworld, const WCoord& blockpos, int blockdata, BLOCK_MINE_TYPE droptype, float chance, int uin/* = -1 */)
{
	CubeBlockMaterial::dropBlockAsItem(pworld, blockpos, blockdata, droptype, chance, uin);
	if (blockdata & 8)
	{
		CubeBlockMaterial::dropBlockAsItem(pworld, blockpos, blockdata, droptype, chance, uin);
	}
}

int HorizontalTriangleHalfSlabMaterial::getPhisicMeshVerts(BaseSection* psection, const WCoord& blockpos, dynamic_array<Rainbow::Vector3f>& verts, dynamic_array<UInt16>& idxs)
{
#ifdef IWORLD_SERVER_BUILD
	if (!m_mPhyModel.size())
	{
		initVertData();
	}
#endif	
	int blockdata = psection->getBlock(blockpos).getData();
	if (blockdata < 8)
	{
		auto pblock = psection->getBlock(blockpos);
		int warp = -1;
		int turnDir = -1;
		DirectionType curDir = DirectionType(blockdata & 3);
		auto frontBlock = psection->getNeighborBlock(blockpos, g_DirectionCoord[curDir]);
		if (pblock.getResID() == frontBlock.getResID() && frontBlock.getData() < 8 && ((pblock.getData() & 4) == (frontBlock.getData() & 4)))
		{
			int frontDir = frontBlock.getData() & 3;
			if (frontDir != curDir && frontDir != ReverseDirection(curDir))
			{
				warp = 0;
				turnDir = frontDir;
			}
		}
		if (warp == -1)
		{
			auto backBlock = psection->getNeighborBlock(blockpos, g_DirectionCoord[ReverseDirection(curDir)]);
			if (pblock.getResID() == backBlock.getResID() && backBlock.getData() < 8 && ((pblock.getData() & 4) == (backBlock.getData() & 4)))
			{
				int backDir = backBlock.getData() & 3;
				if (backDir != curDir && backDir != ReverseDirection(curDir))
				{
					warp = 1;
					turnDir = backDir;
				}
			}
		}
		if (warp == -1)
		{
			if (m_mPhyModel.find(blockdata) != m_mPhyModel.end())
			{
				TrianglePhyModel* pTag = &m_mPhyModel[blockdata];
				verts = pTag->verts;
				idxs = pTag->idxs;
				return  pTag->triangleCount;
			}
		}
		else
		{
			int downUp = (blockdata & 4) ? 1 : 0;
			if (m_mPhyModel.find(10000 + downUp * 1000 + warp * 100 + curDir * 10 + turnDir) != m_mPhyModel.end())
			{
				TrianglePhyModel* pTag = &m_mPhyModel[10000 + downUp * 1000 + warp * 100 + curDir * 10 + turnDir];
				verts = pTag->verts;
				idxs = pTag->idxs;
				return  pTag->triangleCount;
			}
		}
	}
	else
	{
		TrianglePhyModel* pTag = &m_mPhyModel[20000 + (blockdata & 4)];
		verts = pTag->verts;
		idxs = pTag->idxs;
		return  pTag->triangleCount;
	}
	return 0;
}

BLOCK_RENDERTYPE_T HorizontalTriangleHalfSlabMaterial::GetAttrRenderType() const
{
	return BLOCKRENDER_MODEL;
}

void HorizontalTriangleHalfSlabMaterial::onBlockPlacedBy(World* pworld, const WCoord& blockpos, IClientPlayer* player)
{
	auto playerTmp = dynamic_cast<ClientPlayer*>(player);
	if (!playerTmp) return;
	int dir = playerTmp->getCurPlaceDir();
	float x, y, z;
	playerTmp->getFaceDir(x, y, z);
	int data = dir;
	if (y > 0)
	{
		data += 4;
	}
	pworld->setBlockData(blockpos, data);
}

void HorizontalTriangleHalfSlabMaterial::createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh)
{
	auto psection = data.m_SharedSectionData;
#ifndef IWORLD_SERVER_BUILD
	FaceVertexLight faceVertexLight;
	Block pblock = psection->getBlock(blockpos);

	int curblockdata = pblock.getData();
	int curDir = curblockdata & 3;

	float blockheight = getBlockHeight(curblockdata);
	DirectionType specialdir = DIR_NOT_INIT;

	if (blockheight > 0 && blockheight < 1.0f) specialdir = DIR_POS_Y;
	else if (blockheight<0 && blockheight>-1.0f) specialdir = DIR_NEG_Y;

	const BiomeDef* biome = GetDefManagerProxy()->getBiomeDef(1);
	int weather = 0;
	if (data.m_World && data.m_World->getWeatherMgr())
	{
		weather = data.m_World->getWeatherMgr()->getWeather(blockpos);
	}
	std::vector<int> wholeFace;
	std::vector<int> halfWholeFace;
	std::vector<int> halfFace;
	std::vector<int> triangleFace;
	std::vector<int> slantFace;
	dynamic_array<int> turnslantFace;
	if (curblockdata & 8)
	{
		if (1 == BlockMaterialMgr::m_BlockShape || poutmesh->isSquareSectionMesh())
		{
			if (specialdir == DIR_POS_Y)
			{
				wholeFace.push_back(4);
				halfWholeFace.push_back(1);//1+4
				for (int ii = 0; ii < 4; ii++)
				{
					halfFace.push_back(ii);
				}
			}
			else
			{
				wholeFace.push_back(5);
				halfWholeFace.push_back(0);//0+4
				for (int ii = 4; ii < 8; ii++)
				{
					halfFace.push_back(ii);
				}
			}
		}
		else
		{
			createBlockMeshAngleSnow(data, blockpos, poutmesh);
			return;
		}
	}
	else
	{
		int warp = -1;
		int turnDir = -1;
		auto frontBlock = psection->getNeighborBlock(blockpos, g_DirectionCoord[curDir]);
		if (pblock.getResID() == frontBlock.getResID() && frontBlock.getData() < 8 && ((pblock.getData() & 4) == (frontBlock.getData() & 4)))
		{
			int frontDir = frontBlock.getData() & 3;
			if (frontDir != curDir && frontDir != ReverseDirection(curDir))
			{
				warp = 0;
				turnDir = frontDir;
			}
		}

		if (warp == -1)
		{
			auto backBlock = psection->getNeighborBlock(blockpos, g_DirectionCoord[ReverseDirection(curDir)]);
			if (pblock.getResID() == backBlock.getResID() && backBlock.getData() < 8 && ((pblock.getData() & 4) == (backBlock.getData() & 4)))
			{
				int backDir = backBlock.getData() & 3;
				if (backDir != curDir && backDir != ReverseDirection(curDir))
				{
					warp = 1;
					turnDir = backDir;
				}
			}
		}

		if (specialdir == DIR_POS_Y)
		{
			wholeFace.push_back(4);
			if (warp == -1)
			{
				halfFace.push_back(ReverseDirection(curDir));
			}
		}
		else
		{
			wholeFace.push_back(5);
			if (warp == -1)
			{
				halfFace.push_back(ReverseDirection(curDir) + 4);
			}
		}

		if (curDir == DIR_NEG_X)
		{
			if (specialdir == DIR_POS_Y)
			{
				if (warp == -1)
				{
					triangleFace.push_back(6);
					triangleFace.push_back(7);
					slantFace.push_back(0);
				}
				else
				{
					if (1 == warp)
					{
						triangleFace.push_back(ReverseDirection(turnDir) + 4);//4+3
						triangleFace.push_back(ReverseDirection(curDir) + (1 - (turnDir % 2)) * 4);
					}
					else
					{
						triangleFace.push_back(turnDir + 4);//4+3       6  7
						triangleFace.push_back(((turnDir - 1) % 2) * 4);    //4  0
						halfFace.push_back(ReverseDirection(curDir));
						halfFace.push_back(ReverseDirection(turnDir));
					}
					turnslantFace.push_back(warp * 100 + curDir * 10 + turnDir);
					turnslantFace.push_back(warp * 100 + turnDir * 10 + curDir);
				}
			}
			else
			{
				if (warp == -1)
				{
					triangleFace.push_back(14);//3*4+2
					triangleFace.push_back(15);//3*4+3
					slantFace.push_back(4);
				}
				else
				{
					if (1 == warp)
					{
						triangleFace.push_back(ReverseDirection(turnDir) + 12);//3*4+3
						triangleFace.push_back(ReverseDirection(curDir) + (1 - (turnDir % 2)) * 4 + 8);
					}
					else
					{
						triangleFace.push_back(turnDir + 12);//3*4+3 14  15
						triangleFace.push_back(curDir + (1 - (turnDir % 2)) * 4 + 8);  // 12   8
						halfFace.push_back(ReverseDirection(curDir) + 4);
						halfFace.push_back(ReverseDirection(turnDir) + 4);
					}
					turnslantFace.push_back(1000 + warp * 100 + curDir * 10 + turnDir);
					turnslantFace.push_back(1000 + warp * 100 + turnDir * 10 + curDir);
				}
			}
		}
		else if (curDir == DIR_POS_X)
		{
			if (specialdir == DIR_POS_Y)
			{
				if (warp == -1)
				{
					triangleFace.push_back(2);
					triangleFace.push_back(3);
					slantFace.push_back(1);
				}
				else
				{
					if (1 == warp)
					{
						triangleFace.push_back(ReverseDirection(turnDir));
						triangleFace.push_back(ReverseDirection(curDir) + (1 - (turnDir % 2)) * 4);
					}
					else
					{
						triangleFace.push_back(turnDir); //2 3
						triangleFace.push_back(curDir + ((turnDir + 1) % 2) * 4); //5 1
						halfFace.push_back(ReverseDirection(curDir));
						halfFace.push_back(ReverseDirection(turnDir));
					}
					turnslantFace.push_back(warp * 100 + curDir * 10 + turnDir);
					turnslantFace.push_back(warp * 100 + turnDir * 10 + curDir);
				}
			}
			else
			{
				if (warp == -1)
				{
					triangleFace.push_back(10);//2*4+2
					triangleFace.push_back(11);//2*4+3
					slantFace.push_back(5);
				}
				else
				{
					if (1 == warp)
					{
						triangleFace.push_back(ReverseDirection(turnDir) + 8);//2*4+3
						triangleFace.push_back(ReverseDirection(curDir) + (1 - (turnDir % 2)) * 4 + 8);
					}
					else
					{
						triangleFace.push_back(turnDir + 8);//2*4+3  10 11
						triangleFace.push_back(curDir + (1 - (turnDir % 2)) * 4 + 8); //13  9
						halfFace.push_back(ReverseDirection(curDir) + 4);
						halfFace.push_back(ReverseDirection(turnDir) + 4);
					}
					turnslantFace.push_back(1000 + warp * 100 + curDir * 10 + turnDir);
					turnslantFace.push_back(1000 + warp * 100 + turnDir * 10 + curDir);
				}
			}
		}
		else if (curDir == DIR_NEG_Z)
		{
			if (specialdir == DIR_POS_Y)
			{
				if (warp == -1)
				{
					triangleFace.push_back(4);
					triangleFace.push_back(5);
					slantFace.push_back(2);
				}
				else
				{
					if (1 == warp)
					{
						triangleFace.push_back(ReverseDirection(turnDir) + 4);
						triangleFace.push_back(ReverseDirection(curDir) + (1 - (turnDir % 2)) * 4);
					}
					else
					{
						triangleFace.push_back(turnDir + 4);  //4 5
						triangleFace.push_back(curDir + ((turnDir + 1) % 2) * 4);  //6 2
						halfFace.push_back(ReverseDirection(curDir));
						halfFace.push_back(ReverseDirection(turnDir));
					}
					turnslantFace.push_back(warp * 100 + curDir * 10 + turnDir);
					turnslantFace.push_back(warp * 100 + turnDir * 10 + curDir);
				}
			}
			else
			{
				if (warp == -1)
				{
					triangleFace.push_back(12);//3*4+0
					triangleFace.push_back(13);//3*4+1
					slantFace.push_back(6);
				}
				else
				{
					if (1 == warp)
					{
						triangleFace.push_back(ReverseDirection(turnDir) + 12);//3*4+1
						triangleFace.push_back(ReverseDirection(curDir) + (1 - (turnDir % 2)) * 4 + 8);
					}
					else
					{
						triangleFace.push_back(turnDir + 12);//3*4+1
						halfFace.push_back(ReverseDirection(curDir) + 4);
						halfFace.push_back(ReverseDirection(turnDir) + 4);
					}
					turnslantFace.push_back(1000 + warp * 100 + curDir * 10 + turnDir);
					turnslantFace.push_back(1000 + warp * 100 + turnDir * 10 + curDir);
				}
			}
		}
		else if (curDir == DIR_POS_Z)
		{
			if (specialdir == DIR_POS_Y)
			{
				if (warp == -1)
				{
					triangleFace.push_back(0);
					triangleFace.push_back(1);
					slantFace.push_back(3);
				}
				else
				{
					if (1 == warp)
					{
						triangleFace.push_back(ReverseDirection(turnDir));
						triangleFace.push_back(ReverseDirection(curDir) + (1 - (turnDir % 2)) * 4);
					}
					else
					{
						triangleFace.push_back(turnDir); // 1 0
						triangleFace.push_back(curDir + ((turnDir + 1) % 2) * 4);  // 3 7
						halfFace.push_back(ReverseDirection(curDir));
						halfFace.push_back(ReverseDirection(turnDir));
					}
					turnslantFace.push_back(warp * 100 + curDir * 10 + turnDir);
					turnslantFace.push_back(warp * 100 + turnDir * 10 + curDir);
				}
			}
			else
			{
				if (warp == -1)
				{
					triangleFace.push_back(8);//2*4+0
					triangleFace.push_back(9);//2*4+1
					slantFace.push_back(7);
				}
				else
				{
					if (1 == warp)
					{
						triangleFace.push_back(ReverseDirection(turnDir) + 8);//2*4+1
						triangleFace.push_back(ReverseDirection(curDir) + (1 - (turnDir % 2)) * 4 + 8);
					}
					else
					{
						triangleFace.push_back(turnDir + 8);//2*4+1
						halfFace.push_back(ReverseDirection(curDir) + 4);
						halfFace.push_back(ReverseDirection(turnDir) + 4);
					}
					turnslantFace.push_back(1000 + warp * 100 + curDir * 10 + turnDir);
					turnslantFace.push_back(1000 + warp * 100 + turnDir * 10 + curDir);
				}
			}
		}
	}
	BlockColor facecolor(255, 255, 255, 0);
	bool isFaceUp = false;
	bool isSnowing = false;//��ѩlock
	if (weather == GROUP_BLIZZARD_WEATHER || weather == GROUP_SNOW_WEATHER)
	{
		isSnowing = true;
		WCoord blockpos_tmp = blockpos + psection->getOrigin();
		int y = data.m_World->getTopHeight(blockpos_tmp.x, blockpos_tmp.z);
		if ((y - 1) != blockpos_tmp.y)
		{
			isSnowing = false;
		}
	}
	for (auto& d : wholeFace)
	{
		DirectionType dir = (DirectionType)d;
		// 		if (m_DisableCoverFaceOpt || dir == specialdir || !psection->getNeighborCover(blockpos, this, curblockdata, dir))
		{
			bool flipQuad = psection->getCubeFaceVertexLight(blockpos, dir, faceVertexLight);

			dynamic_array<UInt16>* indices = /*flipQuad ? &m_dNegIndices : */m_dPosIndices;

			RenderBlockMaterial* pmtl = NULL;
			if (isSnowing == true)
			{
				pmtl = snowSideMtl;
				if (dir == DIR_POS_Y)
				{
					pmtl = snowTopMtl;
					isFaceUp = true;
				}
				else if (dir == DIR_NEG_Y)
				{
					pmtl = getFaceMtl(biome, dir, pblock.getData(), facecolor);
				}
			}
			if (pmtl == NULL) pmtl = getFaceMtl(biome, dir, pblock.getData(), facecolor);
			if (pmtl == NULL)
				continue;
			SectionSubMesh* psubmesh = poutmesh->getSubMesh(pmtl);
			const float* uvtile = nullptr;
			if (psubmesh && !psubmesh->IsIgnoreTileUV())
				uvtile = pmtl->getUVTile();
			BlockGeomMeshInfo mesh;

			mesh.vertices = m_mWholeFace()[d];
			mesh.indices = *indices;


			////unsigned short dir_color = TriangleNormal2LightColor(g_DirectionCoord[dir].toVector3());
			//BlockColor vertcolor;// r g b a
			////vertcolor.v = 0xffffffff;
			//vertcolor.a = dir_color;
			unsigned int avelt[4];
			getAvelt(data, blockpos, dir, avelt);
			for (int n = 0; n < m_mWholeFace()[d].size(); n++)
			{
				auto& vert = mesh.vertices[n];
				//int lt1 = (((255 >> 4) & 0xf) * vertcolor.a) >> 5;
				//int lt2 = (((255 >> 20) & 0xf) * vertcolor.a) >> 5;
				//vert.pos.w = (lt1 << 8) | lt2;
				//vert.color = vertcolor;
				//vert.color = vertcolor;
				InitBlockVertLight(vert, avelt[n], uvtile);
			}
			if (psubmesh) //psubmesh->addGeomFace(mesh, &blockpos);
				psubmesh->addGeomFaceLight(mesh, &blockpos, faceVertexLight, &facecolor, pmtl->getUVTile());
		}
	}
	for (auto& d : halfWholeFace)
	{
		DirectionType dir = (DirectionType)(d + 4);
		// 		if (m_DisableCoverFaceOpt || dir == specialdir || !psection->getNeighborCover(blockpos, this, curblockdata, dir))
		{
			bool flipQuad = psection->getCubeFaceVertexLight(blockpos, dir, faceVertexLight);

			dynamic_array<UInt16>* indices = m_dPosIndices;

			RenderBlockMaterial* pmtl = NULL;
			if (isSnowing == true)
			{
				pmtl = snowSideMtl;
				if (dir == DIR_POS_Y)
				{
					pmtl = snowTopMtl;
					isFaceUp = true;
				}
				else if (dir == DIR_NEG_Y)
				{
					pmtl = getFaceMtl(biome, dir, pblock.getData(), facecolor);
				}
			}
			if (pmtl == NULL) pmtl = getFaceMtl(biome, dir, pblock.getData(), facecolor);
			if (pmtl == NULL)
				continue;
			SectionSubMesh* psubmesh = poutmesh->getSubMesh(pmtl);
			const float* uvtile = nullptr;
			if (psubmesh && !psubmesh->IsIgnoreTileUV())
				uvtile = pmtl->getUVTile();
			BlockGeomMeshInfo mesh;

			mesh.vertices = m_mHalfWholeFace()[d];
			mesh.indices = *indices;
			//////unsigned short dir_color = TriangleNormal2LightColor(g_DirectionCoord[dir].toVector3());
			//BlockColor vertcolor;
			////vertcolor.v = 0xffffffff;
			//vertcolor.a = dir_color;
			unsigned int avelt[4];
			getAvelt(data, blockpos, dir, avelt);
			for (int n = 0; n < m_mHalfWholeFace()[d].size(); n++)
			{
				auto& vert = mesh.vertices[n];
				//int lt1 = (((255 >> 4) & 0xf) * vertcolor.a) >> 5;
				//int lt2 = (((255 >> 20) & 0xf) * vertcolor.a) >> 5;
				//vert.pos.w = (lt1 << 8) | lt2;
				//vert.color = vertcolor;
				//vert.color = vertcolor;
				InitBlockVertLight(vert, avelt[n], uvtile);
			}
			if (psubmesh) //psubmesh->addGeomFace(mesh, &blockpos);// 
				psubmesh->addGeomFaceLight(mesh, &blockpos, faceVertexLight, &facecolor, pmtl->getUVTile());
		}
	}
	for (auto& d : halfFace)
	{
		DirectionType dir = (DirectionType)(d % 4);
		// 		if (m_DisableCoverFaceOpt || dir == specialdir || !psection->getNeighborCover(blockpos, this, curblockdata, dir))
		{
			bool flipQuad = psection->getCubeFaceVertexLight(blockpos, dir, faceVertexLight);
			dynamic_array<UInt16>* indices = m_dPosIndices;

			RenderBlockMaterial* pmtl = NULL;
			if (isSnowing == true)
			{
				pmtl = snowSideMtl;
				if (dir == DIR_POS_Y)
				{
					pmtl = snowTopMtl;
					isFaceUp = true;
				}
				else if (dir == DIR_NEG_Y)
				{
					pmtl = getFaceMtl(biome, dir, pblock.getData(), facecolor);
				}
			}
			if (pmtl == NULL) pmtl = getFaceMtl(biome, dir, pblock.getData(), facecolor);
			if (pmtl == NULL)
				continue;
			SectionSubMesh* psubmesh = poutmesh->getSubMesh(pmtl);
			const float* uvtile = nullptr;
			if (psubmesh && !psubmesh->IsIgnoreTileUV())
				uvtile = pmtl->getUVTile();
			BlockGeomMeshInfo mesh;

			mesh.vertices = m_mHalfFace()[d];
			if (isSnowing == true)
			{
				if (d < 4)
				{
					if (d == 1 || d == 2)
					{
						mesh.vertices[0].uv.x = 0; mesh.vertices[0].uv.y = 2048;
						mesh.vertices[1].uv.x = 0; mesh.vertices[1].uv.y = 0;
						mesh.vertices[2].uv.x = 4096; mesh.vertices[2].uv.y = 0;
						mesh.vertices[3].uv.x = 4096; mesh.vertices[3].uv.y = 2048;
					}
					else
					{
						mesh.vertices[0].uv.x = 4096; mesh.vertices[0].uv.y = 2048;
						mesh.vertices[1].uv.x = 0; mesh.vertices[1].uv.y = 2048;
						mesh.vertices[2].uv.x = 0; mesh.vertices[2].uv.y = 0;
						mesh.vertices[3].uv.x = 4096; mesh.vertices[3].uv.y = 0;
					}
				}
			}
			mesh.indices = *indices;
			//////unsigned short dir_color = TriangleNormal2LightColor(g_DirectionCoord[dir].toVector3());
			//BlockColor vertcolor;
			////vertcolor.v = 0xffffffff;
			//vertcolor.a = dir_color;
			
			unsigned int avelt[4];
			getAvelt(data, blockpos, dir, avelt);
			for (int n = 0; n < m_mHalfFace()[d].size(); n++)
			{
				auto& vert = mesh.vertices[n];
				//int lt1 = (((255 >> 4) & 0xf) * vertcolor.a) >> 5;
				//int lt2 = (((255 >> 20) & 0xf) * vertcolor.a) >> 5;
				//vert.pos.w = (lt1 << 8) | lt2;
				//vert.color = vertcolor;
				//vert.color = vertcolor;
				InitBlockVertLight(vert, avelt[n], uvtile);
			}
			if (psubmesh) //psubmesh->addGeomFace(mesh, &blockpos); 
				psubmesh->addGeomFaceLight(mesh, &blockpos, faceVertexLight, &facecolor, pmtl->getUVTile());
		}
	}
	for (auto& d : triangleFace)
	{
		DirectionType dir = (DirectionType)(d % 4);
		bool flipQuad = psection->getCubeFaceVertexLight(blockpos, dir, faceVertexLight);
		dynamic_array<UInt16>* indices = m_PosTrIndices;

		RenderBlockMaterial* pmtl = NULL;
		if (isSnowing == true)
		{
			pmtl = snowHorizontalHalfTriangleMtl;
			if (isFaceUp == true)
			{
				pmtl = snowSideMtl;
			}
		}
		if (pmtl == NULL) pmtl = getFaceMtl(biome, dir, pblock.getData(), facecolor);
		if (pmtl == NULL)
			continue;
		SectionSubMesh* psubmesh = poutmesh->getSubMesh(pmtl);
		const float* uvtile = nullptr;
		if (psubmesh && !psubmesh->IsIgnoreTileUV())
			uvtile = pmtl->getUVTile();
		BlockGeomMeshInfo mesh;
		if (isSnowing == true)
		{
			mesh.vertices = m_mTriangleFace_uv[d];
		}
		else
		{
			mesh.vertices = m_mTriangleFace[d];
		}
		mesh.indices = *indices;
		//////unsigned short dir_color = TriangleNormal2LightColor(g_DirectionCoord[dir].toVector3());
		//BlockColor vertcolor;
		////vertcolor.v = 0xffffffff;
		//vertcolor.a = dir_color;
		for (int n = 0; n < m_mTriangleFace[d].size(); n++)
		{
			auto& vert = mesh.vertices[n];
			int aveltMe = 0;
			int aveltNeight[3] = { 0 };
			int avelt = 0;
			if (vert.pos.y > 0 && specialdir == DIR_POS_Y || vert.pos.y == 0 && specialdir == DIR_NEG_Y)
			{
				auto selfPos = blockpos + g_DirectionCoord[specialdir] + g_DirectionCoord[dir];
				bool isXdir = dir < 2;
				int xdir = isXdir ? dir : vert.pos.x == 0 ? 0 : 1;
				int zdir = !isXdir ? dir : vert.pos.z == 0 ? 2 : 3;
				aveltMe = psection->getLight2(selfPos, true);
				aveltNeight[0] = psection->getLight2(selfPos + g_DirectionCoord[xdir], true);
				aveltNeight[1] = psection->getLight2(selfPos + g_DirectionCoord[zdir], true);
				aveltNeight[2] = psection->getLight2(selfPos + g_DirectionCoord[xdir] + g_DirectionCoord[zdir], true);
				avelt = ((aveltMe + aveltNeight[0] + aveltNeight[1] + aveltNeight[2]) >> 2) & 0xff00ff;
			}
			else
			{
				auto selfPos = blockpos + g_DirectionCoord[dir];
				int sideDir = specialdir == DIR_NEG_Y ? DIR_POS_Y : DIR_NEG_Y;
				aveltMe = psection->getLight2(selfPos, true);
				aveltNeight[0] = psection->getLight2(selfPos + g_DirectionCoord[sideDir], true);
				avelt = ((aveltMe + aveltNeight[0]) >> 1) & 0xff00ff;
			}
			InitBlockVertLight(vert, avelt, uvtile);
		}
		if (psubmesh) psubmesh->addGeomFace(mesh, &blockpos);// 
			//psubmesh->addGeomFaceLight(mesh, &blockpos, faceVertexLight, &facecolor, pmtl->getUVTile());
	}
	for (auto& d : turnslantFace)
	{
		DirectionType dir = (DirectionType)((d / 10) % 10);
		bool flipQuad = psection->getFaceVertexLight(blockpos, dir, faceVertexLight);
		FaceVertexLight faceUpDownVertexLight;
		psection->getFaceVertexLight(blockpos, specialdir, faceUpDownVertexLight);
		for (int i = 0; i < 4; i++)
		{
			faceVertexLight.m_Light[i] = (faceVertexLight.m_Light[i] + 2 * faceUpDownVertexLight.m_Light[i]) / 3;
			faceVertexLight.m_AmbientOcclusion[i] = (faceVertexLight.m_AmbientOcclusion[i] + 2 * faceUpDownVertexLight.m_AmbientOcclusion[i]) / 3;
		}
		dynamic_array<UInt16>* indices = m_PosTrIndices;
		RenderBlockMaterial* pmtl = NULL;
		if (isSnowing == true)
		{
			pmtl = snowTopMtl;
			if (isFaceUp == true)
			{
				pmtl = snowDownMoreMtl;
			}
		}
		if (pmtl == NULL) pmtl = getFaceMtl(biome, dir, pblock.getData(), facecolor);
		if (pmtl == NULL)
			continue;
		SectionSubMesh* psubmesh = poutmesh->getSubMesh(pmtl);
		const float* uvtile = nullptr;
		if (psubmesh && !psubmesh->IsIgnoreTileUV())
			uvtile = pmtl->getUVTile();
		BlockGeomMeshInfo mesh;

		mesh.vertices = m_mTurnSlantFace[d];
		mesh.indices = *indices;
		// unsigned short dir_color1 = TriangleNormal2LightColor(g_DirectionCoord[dir].toVector3());
		// unsigned short dir_color2 = TriangleNormal2LightColor(g_DirectionCoord[specialdir].toVector3());
		BlockColor vertcolor = Rainbow::ColorRGBA32::white;// r g b a
		//vertcolor.v = 0xffffffff;
		vertcolor.a = 0;// (dir_color1 + 2 * dir_color2) / 3;
		// if (specialdir == DIR_NEG_Y)
		// {
		// 	unsigned short dir_color3 = TriangleNormal2LightColor(g_DirectionCoord[DIR_POS_Y].toVector3());
		// 	vertcolor.a = (3 * dir_color1 + dir_color2 + 2 * dir_color3) / 6;
		// }
		for (int n = 0; n < m_mTurnSlantFace[d].size(); n++)
		{
			auto& vert = mesh.vertices[n];
			int aveltMe = 0;
			int aveltNeight[3] = { 0 };
			int avelt = 0;
			if (vert.pos.y > 0 && specialdir == DIR_POS_Y || vert.pos.y == 0 && specialdir == DIR_NEG_Y)
			{
				auto selfPos = blockpos + g_DirectionCoord[specialdir];
				bool isXdir = dir < 2;
				int xdir = isXdir ? dir : vert.pos.x == 0 ? 0 : 1;
				int zdir = !isXdir ? dir : vert.pos.z == 0 ? 2 : 3;
				aveltMe = psection->getLight2(selfPos, true);
				aveltNeight[0] = psection->getLight2(selfPos + g_DirectionCoord[xdir], true);
				aveltNeight[1] = psection->getLight2(selfPos + g_DirectionCoord[zdir], true);
				aveltNeight[2] = psection->getLight2(selfPos + g_DirectionCoord[xdir] + g_DirectionCoord[zdir], true);
				avelt = ((aveltMe + aveltNeight[0] + aveltNeight[1] + aveltNeight[2]) >> 2) & 0xff00ff;
			}
			else
			{
				auto selfPos = blockpos + g_DirectionCoord[dir];
				int xdir = vert.pos.x == 0 ? 0 : 1;
				int zdir = vert.pos.z == 0 ? 2 : 3;
				int sideDir = dir > 1 ? xdir : zdir;
				aveltMe = psection->getLight2(selfPos, true);
				aveltNeight[0] = psection->getLight2(selfPos + g_DirectionCoord[sideDir], true);
				avelt = ((aveltMe + aveltNeight[0]) >> 1) & 0xff00ff;
			}
			//int lt1 = (((avelt >> 4) & 0xf) * vertcolor.a) >> 5;
			//int lt2 = (((avelt >> 20) & 0xf) * vertcolor.a) >> 5;
			//vert.pos.w = (lt1 << 8) | lt2;
			//vert.color = vertcolor;
			vert.color = vertcolor;
			InitBlockVertLight(vert, avelt, uvtile);
		}
		if (psubmesh) psubmesh->addGeomFace(mesh, &blockpos);// addGeomFaceLight(mesh, &blockpos, faceVertexLight, &facecolor, pmtl->getUVTile());
	}
	for (auto& d : slantFace)
	{
		DirectionType dir = (DirectionType)curDir;
		bool flipQuad = psection->getFaceVertexLight(blockpos, dir, faceVertexLight);
		FaceVertexLight faceUpDownVertexLight;
		psection->getFaceVertexLight(blockpos, specialdir, faceUpDownVertexLight);
		for (int i = 0; i < 4; i++)
		{
			faceVertexLight.m_Light[i] = (faceVertexLight.m_Light[i] + 2 * faceUpDownVertexLight.m_Light[i]) / 3;
			faceVertexLight.m_AmbientOcclusion[i] = (faceVertexLight.m_AmbientOcclusion[i] + 2 * faceUpDownVertexLight.m_AmbientOcclusion[i]) / 3;
		}

		dynamic_array<UInt16>* indices = m_dPosIndices;

		RenderBlockMaterial* pmtl = getFaceMtl(biome, dir, pblock.getData(), facecolor);
		if (isSnowing == true)
		{
			pmtl = snowTopMtl;
			if (isFaceUp == true)
			{
				pmtl = snowDownMoreMtl;
			}
		}
		if (pmtl == NULL) pmtl = getFaceMtl(biome, dir, pblock.getData(), facecolor);
		if (pmtl == NULL)
			continue;
		SectionSubMesh* psubmesh = poutmesh->getSubMesh(pmtl);
		const float* uvtile = nullptr;
		if (psubmesh && !psubmesh->IsIgnoreTileUV())
			uvtile = pmtl->getUVTile();
		BlockGeomMeshInfo mesh;

		mesh.vertices = m_mSlantFace[d];
		mesh.indices = *indices;
		// unsigned short dir_color1 = TriangleNormal2LightColor(g_DirectionCoord[dir].toVector3());
		// unsigned short dir_color2 = TriangleNormal2LightColor(g_DirectionCoord[specialdir].toVector3());
		BlockColor vertcolor = Rainbow::ColorRGBA32::white;
		//vertcolor.v = 0xffffffff;
		vertcolor.a = 0;//(dir_color1 + 2 * dir_color2) / 3;
		// if (specialdir == DIR_NEG_Y)
		// {
		// 	vertcolor.a = (3 * dir_color1 + dir_color2) / 4;
		// }
		for (int n = 0; n < m_mSlantFace[d].size(); n++)
		{
			auto& vert = mesh.vertices[n];
			int aveltMe = 0;
			int aveltNeight[3] = { 0 };
			int avelt = 0;
			if (vert.pos.y > 0 && specialdir == DIR_POS_Y || vert.pos.y == 0 && specialdir == DIR_NEG_Y)
			{
				auto selfPos = blockpos + g_DirectionCoord[specialdir];
				aveltMe = psection->getLight2(selfPos, true);
				bool isXdir = dir < 2;
				int xdir = isXdir ? dir : vert.pos.x == 0 ? 0 : 1;
				int zdir = !isXdir ? dir : vert.pos.z == 0 ? 2 : 3;
				aveltNeight[0] = psection->getLight2(selfPos + g_DirectionCoord[xdir], true);
				aveltNeight[1] = psection->getLight2(selfPos + g_DirectionCoord[zdir], true);
				aveltNeight[2] = psection->getLight2(selfPos + g_DirectionCoord[xdir] + g_DirectionCoord[zdir], true);
				avelt = ((aveltMe + aveltNeight[0] + aveltNeight[1] + aveltNeight[2]) >> 2) & 0xff00ff;
				avelt = ((aveltMe + aveltNeight[0]) >> 1) & 0xff00ff;
			}
			else
			{
				auto selfPos = blockpos + g_DirectionCoord[dir];
				aveltMe = psection->getLight2(selfPos, true);
				int xdir = vert.pos.x == 0 ? 0 : 1;
				int zdir = vert.pos.z == 0 ? 2 : 3;
				int sideDir = dir > 1 ? xdir : zdir;
				aveltNeight[0] = psection->getLight2(selfPos + g_DirectionCoord[sideDir], true);
				avelt = ((aveltMe + aveltNeight[0]) >> 1) & 0xff00ff;
			}
			//int lt1 = (((avelt >> 4) & 0xf) * vertcolor.a) >> 5;
			//int lt2 = (((avelt >> 20) & 0xf) * vertcolor.a) >> 5;
			//vert.pos.w = (lt1 << 8) | lt2;
			//vert.color = vertcolor;
			vert.color = vertcolor;
			InitBlockVertLight(vert, avelt, uvtile);
		}
		if (psubmesh) psubmesh->addGeomFace(mesh, &blockpos);// addGeomFaceLight(mesh, &blockpos, faceVertexLight, &facecolor, pmtl->getUVTile());
	}
#endif
}

void HorizontalTriangleHalfSlabMaterial::initHalfFaceVertData()
{
	if (m_mHalfFace().size())
	{
		return;
	}
	for (int i = 0; i < 2; i++)
	{
		for (int j = 0; j < 4; j++)
		{
			int d = i * 4 + j;
			dynamic_array<UInt16>* indices = m_dPosIndices;
			DirectionType dir = (DirectionType)(d % 4);
			dynamic_array<BlockGeomVert> vertices;
			Rainbow::Vector3f normalVec = g_DirectionCoord[j].toVector3();
			////unsigned short dir_color = TriangleNormal2LightColor(g_DirectionCoord[dir].toVector3());
			////unsigned short dir_color = Normal2LightColor(normalVec);
			BlockVector vertcolor;
			vertcolor.v = 0xffffffff;
			vertcolor.w = 0;
			Normalize(normalVec);
			BlockVector normal_dir = PackVertNormal(normalVec);
			BlockGeomVert vert[4];
			if (0 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0x7f7f);
				vert[1].pos = Rainbow::Vector4f(0, 0, 100, 0x7f7f);
				vert[2].pos = Rainbow::Vector4f(0, 50, 100, 0x7f7f);
				vert[3].pos = Rainbow::Vector4f(0, 50, 0, 0x7f7f);
				//�����������²���
				vert[0].uv = { 1, 1 };
				vert[1].uv = { 0, 1 };
				vert[2].uv = { 0, 0 };
				vert[3].uv = { 1, 0 };
			}
			else if (1 == d)
			{
				vert[0].pos = Rainbow::Vector4f(100, 0, 0, 0x7f7f);
				vert[1].pos = Rainbow::Vector4f(100, 50, 0, 0x7f7f);
				vert[2].pos = Rainbow::Vector4f(100, 50, 100, 0x7f7f);
				vert[3].pos = Rainbow::Vector4f(100, 0, 100, 0x7f7f);

				vert[0].uv = { 0, 1 };
				vert[1].uv = { 0, 0 };
				vert[2].uv = { 1, 0 };
				vert[3].uv = { 1, 1 };
			}
			else if (2 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0x7f7f);
				vert[1].pos = Rainbow::Vector4f(0, 50, 0, 0x7f7f);
				vert[2].pos = Rainbow::Vector4f(100, 50, 0, 0x7f7f);
				vert[3].pos = Rainbow::Vector4f(100, 0, 0, 0x7f7f);

				vert[0].uv = { 0, 1 };
				vert[1].uv = { 0, 0 };
				vert[2].uv = { 1, 0 };
				vert[3].uv = { 1, 1 };
			}
			else if (3 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 0, 100, 0x7f7f);
				vert[1].pos = Rainbow::Vector4f(100, 0, 100, 0x7f7f);
				vert[2].pos = Rainbow::Vector4f(100, 50, 100, 0x7f7f);
				vert[3].pos = Rainbow::Vector4f(0, 50, 100, 0x7f7f);

				vert[0].uv = { 1, 1 };
				vert[1].uv = { 0, 1 };
				vert[2].uv = { 0, 0 };
				vert[3].uv = { 1, 0 };
			}
			else if (4 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 50, 0, 0x7f7f);
				vert[1].pos = Rainbow::Vector4f(0, 50, 100, 0x7f7f);
				vert[2].pos = Rainbow::Vector4f(0, 100, 100, 0x7f7f);
				vert[3].pos = Rainbow::Vector4f(0, 100, 0, 0x7f7f);
				//�����������²���
				vert[0].uv = { 1, 1 };
				vert[1].uv = { 0, 1 };
				vert[2].uv = { 0, 0 };
				vert[3].uv = { 1, 0 };
			}
			else if (5 == d)
			{
				vert[0].pos = Rainbow::Vector4f(100, 50, 0, 0x7f7f);
				vert[1].pos = Rainbow::Vector4f(100, 100, 0, 0x7f7f);
				vert[2].pos = Rainbow::Vector4f(100, 100, 100, 0x7f7f);
				vert[3].pos = Rainbow::Vector4f(100, 50, 100, 0x7f7f);

				vert[0].uv = { 0, 1 };
				vert[1].uv = { 0, 0 };
				vert[2].uv = { 1, 0 };
				vert[3].uv = { 1, 1 };
			}
			else if (6 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 50, 0, 0x7f7f);
				vert[1].pos = Rainbow::Vector4f(0, 100, 0, 0x7f7f);
				vert[2].pos = Rainbow::Vector4f(100, 100, 0, 0x7f7f);
				vert[3].pos = Rainbow::Vector4f(100, 50, 0, 0x7f7f);

				vert[0].uv = { 0, 1 };
				vert[1].uv = { 0, 0 };
				vert[2].uv = { 1, 0 };
				vert[3].uv = { 1, 1 };
			}
			else if (7 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 50, 100, 0x7f7f);
				vert[1].pos = Rainbow::Vector4f(100, 50, 100, 0x7f7f);
				vert[2].pos = Rainbow::Vector4f(100, 100, 100, 0x7f7f);
				vert[3].pos = Rainbow::Vector4f(0, 100, 100, 0x7f7f);

				vert[0].uv = { 1, 1 };
				vert[1].uv = { 0, 1 };
				vert[2].uv = { 0, 0 };
				vert[3].uv = { 1, 0 };
			}

			for (int oo = 0; oo < 4; oo++)
			{
				vert[oo].uv = { short(vert[oo].uv.x * BLOCKUV_SCALE), short(i == 1 ? short(vert[oo].uv.y * 0.5f * BLOCKUV_SCALE) : short((1.f - (1.f - vert[oo].uv.y) * 0.5f) * BLOCKUV_SCALE)) };
				vert[oo].normal = normal_dir;
				vert[oo].color.SetUInt32(vertcolor.v);
				vert[oo].pos.w = 0;//(lt1 << 8) | lt2;
				vertices.push_back(vert[oo]);
			}
			m_mHalfFace().push_back(vertices);
		}
	}
}

void HorizontalTriangleHalfSlabMaterial::initVertData()
{
	initHalfWholeFaceVertData();
	initHalfFaceVertData();
	WholeTriangleMaterial::initVertData();
// 	initWholeFaceVertData();
// 	initTriangleFaceVertData();
// 	initSlantFaceVertData();
// 	initPhyModelData();
}

void HorizontalTriangleHalfSlabMaterial::initHalfWholeFaceVertData()
{
	if (m_mHalfWholeFace().size() != 0)
	{
		return;
	}
	for (int d = 4; d < 6; d++)
	{
		DirectionType dir = (DirectionType)d;
		dynamic_array<UInt16>* indices = m_dPosIndices;

		dynamic_array<BlockGeomVert> vertices;
		Rainbow::Vector3f normalVec = g_DirectionCoord[d].toVector3();
		////unsigned short dir_color = TriangleNormal2LightColor(g_DirectionCoord[dir].toVector3());
		////unsigned short dir_color = Normal2LightColor(normalVec);
		BlockVector vertcolor;
		vertcolor.v = 0xffffffff;
		vertcolor.w = 0;
		Normalize(normalVec);
		BlockVector normal_dir = PackVertNormal(normalVec);
		BlockGeomVert vert[4];
		/*if (0 == d)
		{
			vert[0].pos = Rainbow::Vector4f(50, 0, 0, 0x7f7f);
			vert[1].pos = Rainbow::Vector4f(50, 0, 100, 0x7f7f);
			vert[2].pos = Rainbow::Vector4f(50, 100, 100, 0x7f7f);
			vert[3].pos = Rainbow::Vector4f(50, 100, 0, 0x7f7f);

			vert[0].uv = { 1, 1 };
			vert[1].uv = { 0, 1 };
			vert[2].uv = { 0, 0 };
			vert[3].uv = { 1, 0 };
		}
		else if (1 == d)
		{
			vert[0].pos = Rainbow::Vector4f(50, 0, 0, 0x7f7f);
			vert[1].pos = Rainbow::Vector4f(50, 100, 0, 0x7f7f);
			vert[2].pos = Rainbow::Vector4f(50, 100, 100, 0x7f7f);
			vert[3].pos = Rainbow::Vector4f(50, 0, 100, 0x7f7f);

			vert[0].uv = { 0, 1 };
			vert[1].uv = { 0, 0 };
			vert[2].uv = { 1, 0 };
			vert[3].uv = { 1, 1 };
		}
		else if (2 == d)
		{
			vert[0].pos = Rainbow::Vector4f(0, 0, 50, 0x7f7f);
			vert[1].pos = Rainbow::Vector4f(0, 100, 50, 0x7f7f);
			vert[2].pos = Rainbow::Vector4f(100, 100, 50, 0x7f7f);
			vert[3].pos = Rainbow::Vector4f(100, 0, 50, 0x7f7f);

			vert[0].uv = { 0, 1 };
			vert[1].uv = { 0, 0 };
			vert[2].uv = { 1, 0 };
			vert[3].uv = { 1, 1 };
		}
		else if (3 == d)
		{
			vert[0].pos = Rainbow::Vector4f(0, 0, 50, 0x7f7f);
			vert[1].pos = Rainbow::Vector4f(100, 0, 50, 0x7f7f);
			vert[2].pos = Rainbow::Vector4f(100, 100, 50, 0x7f7f);
			vert[3].pos = Rainbow::Vector4f(0, 100, 50, 0x7f7f);

			vert[0].uv = { 1, 1 };
			vert[1].uv = { 0, 1 };
			vert[2].uv = { 0, 0 };
			vert[3].uv = { 1, 0 };
		}
		else */if (4 == d)
		{
			vert[0].pos = Rainbow::Vector4f(0, 50, 0, 0x7f7f);
			vert[1].pos = Rainbow::Vector4f(100, 50, 0, 0x7f7f);
			vert[2].pos = Rainbow::Vector4f(100, 50, 100, 0x7f7f);
			vert[3].pos = Rainbow::Vector4f(0, 50, 100, 0x7f7f);

			vert[0].uv = { 1, 0 };
			vert[1].uv = { 0, 0 };
			vert[2].uv = { 0, 1 };
			vert[3].uv = { 1, 1 };
		}
		else if (5 == d)
		{
			vert[0].pos = Rainbow::Vector4f(0, 50, 0, 0x7f7f);
			vert[1].pos = Rainbow::Vector4f(0, 50, 100, 0x7f7f);
			vert[2].pos = Rainbow::Vector4f(100, 50, 100, 0x7f7f);
			vert[3].pos = Rainbow::Vector4f(100, 50, 0, 0x7f7f);

			vert[0].uv = { 0, 0 };
			vert[1].uv = { 0, 1 };
			vert[2].uv = { 1, 1 };
			vert[3].uv = { 1, 0 };
		}

		for (int oo = 0; oo < 4; oo++)
		{
			vert[oo].uv = {short( vert[oo].uv.x * BLOCKUV_SCALE),short( vert[oo].uv.y * BLOCKUV_SCALE) };
			vert[oo].normal = normal_dir;
			vert[oo].color.SetUInt32(vertcolor.v);
 			vert[oo].pos.w = 0;//(lt1 << 8) | lt2;
			vertices.push_back(vert[oo]);
		}
		m_mHalfWholeFace().push_back(vertices);
	}
}

void HorizontalTriangleHalfSlabMaterial::initTriangleFaceVertData()
{
	for (int i = 0; i < 4; i++)
	{
		for (int j = 0; j < 4; j++)
		{
			dynamic_array<UInt16>* indices = m_PosTrIndices;
			int d = i * 4 + j;
			DirectionType dir = (DirectionType)(d % 4);
			////unsigned short dir_color = TriangleNormal2LightColor(g_DirectionCoord[dir].toVector3());
			dynamic_array<BlockGeomVert> vertices;

			Rainbow::Vector3f normalVec = g_DirectionCoord[j].toVector3();
			normalVec.y /= 2.f;
			////unsigned short dir_color = Normal2LightColor(normalVec);
			BlockVector vertcolor;
			vertcolor.v = 0xffffffff;
			vertcolor.w = 0;
			Normalize(normalVec);
			BlockVector normal_dir = PackVertNormal(normalVec);

			BlockGeomVert vert[3];
			if (0 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
				vert[1].pos = Rainbow::Vector4f(0, 0, 100, 0);
				vert[2].pos = Rainbow::Vector4f(0, 50, 0, 0);

				vert[0].uv = { 1, 1 };
				vert[1].uv = { 0, 1 };
				vert[2].uv = { 1, 0 };
			}
			else if (1 == d)
			{
				vert[0].pos = Rainbow::Vector4f(100, 0, 0, 0);
				vert[1].pos = Rainbow::Vector4f(100, 50, 0, 0);
				vert[2].pos = Rainbow::Vector4f(100, 0, 100, 0);

				vert[0].uv = { 0, 1 };
				vert[1].uv = { 0, 0 };
				vert[2].uv = { 1, 1 };
			}
			else if (2 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
				vert[1].pos = Rainbow::Vector4f(0, 50, 0, 0);
				vert[2].pos = Rainbow::Vector4f(100, 0, 0, 0);

				vert[0].uv = { 0, 1 };
				vert[1].uv = { 0, 0 };
				vert[2].uv = { 1, 1 };
			}
			else if (3 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 0, 100, 0);
				vert[1].pos = Rainbow::Vector4f(100, 0, 100, 0);
				vert[2].pos = Rainbow::Vector4f(0, 50, 100, 0);

				vert[0].uv = { 1, 1 };
				vert[1].uv = { 0, 1 };
				vert[2].uv = { 1, 0 };
			}
			else if (4 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
				vert[1].pos = Rainbow::Vector4f(0, 0, 100, 0);
				vert[2].pos = Rainbow::Vector4f(0, 50, 100, 0);

				vert[0].uv = { 1, 1 };
				vert[1].uv = { 0, 1 };
				vert[2].uv = { 0, 0 };
			}
			else if (5 == d)
			{
				vert[0].pos = Rainbow::Vector4f(100, 0, 0, 0);
				vert[1].pos = Rainbow::Vector4f(100, 50, 100, 0);
				vert[2].pos = Rainbow::Vector4f(100, 0, 100, 0);

				vert[0].uv = { 0, 1 };
				vert[1].uv = { 1, 0 };
				vert[2].uv = { 1, 1 };
			}
			else if (6 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
				vert[1].pos = Rainbow::Vector4f(100, 50, 0, 0);
				vert[2].pos = Rainbow::Vector4f(100, 0, 0, 0);

				vert[0].uv = { 0, 1 };
				vert[1].uv = { 1, 0 };
				vert[2].uv = { 1, 1 };
			}
			else if (7 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 0, 100, 0);
				vert[1].pos = Rainbow::Vector4f(100, 0, 100, 0);
				vert[2].pos = Rainbow::Vector4f(100, 50, 100, 0);

				vert[0].uv = { 1, 1 };
				vert[1].uv = { 0, 1 };
				vert[2].uv = { 0, 0 };
			}
			else if (8 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 50, 0, 0);
				vert[1].pos = Rainbow::Vector4f(0, 100, 100, 0);
				vert[2].pos = Rainbow::Vector4f(0, 100, 0, 0);

				vert[0].uv = { 1, 1 };
				vert[1].uv = { 0, 0 };
				vert[2].uv = { 1, 0 };
			}
			else if (9 == d)
			{
				vert[0].pos = Rainbow::Vector4f(100, 50, 0, 0);
				vert[1].pos = Rainbow::Vector4f(100, 100, 0, 0);
				vert[2].pos = Rainbow::Vector4f(100, 100, 100, 0);

				vert[0].uv = { 0, 1 };
				vert[1].uv = { 0, 0 };
				vert[2].uv = { 1, 0 };
			}
			else if (10 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 50, 0, 0);
				vert[1].pos = Rainbow::Vector4f(0, 100, 0, 0);
				vert[2].pos = Rainbow::Vector4f(100, 100, 0, 0);

				vert[0].uv = { 0, 1 };
				vert[1].uv = { 0, 0 };
				vert[2].uv = { 1, 0 };
			}
			else if (11 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 50, 100, 0);
				vert[1].pos = Rainbow::Vector4f(100, 100, 100, 0);
				vert[2].pos = Rainbow::Vector4f(0, 100, 100, 0);

				vert[0].uv = { 1, 1 };
				vert[1].uv = { 0, 0 };
				vert[2].uv = { 1, 0 };
			}
			else if (12 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 50, 100, 0);
				vert[1].pos = Rainbow::Vector4f(0, 100, 100, 0);
				vert[2].pos = Rainbow::Vector4f(0, 100, 0, 0);

				vert[0].uv = { 0, 1 };
				vert[1].uv = { 0, 0 };
				vert[2].uv = { 1, 0 };
			}
			else if (13 == d)
			{
				vert[0].pos = Rainbow::Vector4f(100, 100, 0, 0);
				vert[1].pos = Rainbow::Vector4f(100, 100, 100, 0);
				vert[2].pos = Rainbow::Vector4f(100, 50, 100, 0);

				vert[0].uv = { 0, 0 };
				vert[1].uv = { 1, 0 };
				vert[2].uv = { 1, 1 };
			}
			else if (14 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 100, 0, 0);
				vert[1].pos = Rainbow::Vector4f(100, 100, 0, 0);
				vert[2].pos = Rainbow::Vector4f(100, 50, 0, 0);

				vert[0].uv = { 0, 0 };
				vert[1].uv = { 1, 0 };
				vert[2].uv = { 1, 1 };
			}
			else if (15 == d)
			{
				vert[0].pos = Rainbow::Vector4f(100, 50, 100, 0);
				vert[1].pos = Rainbow::Vector4f(100, 100, 100, 0);
				vert[2].pos = Rainbow::Vector4f(0, 100, 100, 0);

				vert[0].uv = { 0, 1 };
				vert[1].uv = { 0, 0 };
				vert[2].uv = { 1, 0 };
			}
			for (int oo = 0; oo < 3; oo++)
			{
				vert[oo].uv = { short(vert[oo].uv.x * BLOCKUV_SCALE), short(i >= 2 ? short(vert[oo].uv.y * 0.5f * BLOCKUV_SCALE) : short((1.f - (1.f - vert[oo].uv.y) * 0.5f) * BLOCKUV_SCALE)) };
				vert[oo].normal = normal_dir;
				vert[oo].color.SetUInt32(vertcolor.v);
				vert[oo].pos.w = 0;//(lt1 << 8) | lt2;
				vertices.push_back(vert[oo]);
			}
			m_mTriangleFace.push_back(vertices);
		}
	}
	for (int i = 0; i < 4; i++)
	{
		for (int j = 0; j < 4; j++)
		{
			dynamic_array<UInt16>* indices = m_PosTrIndices;
			int d = i * 4 + j;
			DirectionType dir = (DirectionType)(d % 4);
			//////unsigned short dir_color = TriangleNormal2LightColor(g_DirectionCoord[dir].toVector3());
			dynamic_array<BlockGeomVert> vertices;

			Rainbow::Vector3f normalVec = g_DirectionCoord[j].toVector3();
			normalVec.y /= 2.f;
			////unsigned short dir_color = Normal2LightColor(normalVec);
			BlockVector vertcolor;
			vertcolor.v = 0xffffffff;
			vertcolor.w = 0;
			Normalize(normalVec);
			BlockVector normal_dir = PackVertNormal(normalVec);

			BlockGeomVert vert[3];
			if (0 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
				vert[1].pos = Rainbow::Vector4f(0, 0, 100, 0);
				vert[2].pos = Rainbow::Vector4f(0, 50, 0, 0);

				vert[0].uv = { 0, 1 };
				vert[1].uv = { 1, 1 };
				vert[2].uv = { 0, 0 };
			}
			else if (1 == d)
			{
				vert[0].pos = Rainbow::Vector4f(100, 0, 0, 0);
				vert[1].pos = Rainbow::Vector4f(100, 50, 0, 0);
				vert[2].pos = Rainbow::Vector4f(100, 0, 100, 0);

				vert[0].uv = { 0, 1 };
				vert[1].uv = { 0, 0 };
				vert[2].uv = { 1, 1 };
			}
			else if (2 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
				vert[1].pos = Rainbow::Vector4f(0, 50, 0, 0);
				vert[2].pos = Rainbow::Vector4f(100, 0, 0, 0);

				vert[0].uv = { 0, 1 };
				vert[1].uv = { 0, 0 };
				vert[2].uv = { 1, 1 };
			}
			else if (3 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 0, 100, 0);
				vert[1].pos = Rainbow::Vector4f(100, 0, 100, 0);
				vert[2].pos = Rainbow::Vector4f(0, 50, 100, 0);

				vert[0].uv = { 0, 1 };
				vert[1].uv = { 1, 1 };
				vert[2].uv = { 0, 0 };
			}
			else if (4 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
				vert[1].pos = Rainbow::Vector4f(0, 0, 100, 0);
				vert[2].pos = Rainbow::Vector4f(0, 50, 100, 0);

				vert[0].uv = { 1, 1 };
				vert[1].uv = { 0, 1 };
				vert[2].uv = { 0, 0 };
			}
			else if (5 == d)
			{
				vert[0].pos = Rainbow::Vector4f(100, 0, 0, 0);
				vert[1].pos = Rainbow::Vector4f(100, 50, 100, 0);
				vert[2].pos = Rainbow::Vector4f(100, 0, 100, 0);

				vert[0].uv = { 1, 1 };
				vert[1].uv = { 0, 0 };
				vert[2].uv = { 0, 1 };
			}
			else if (6 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
				vert[1].pos = Rainbow::Vector4f(100, 50, 0, 0);
				vert[2].pos = Rainbow::Vector4f(100, 0, 0, 0);

				vert[0].uv = { 1, 1 };
				vert[1].uv = { 0, 0 };
				vert[2].uv = { 0, 1 };
			}
			else if (7 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 0, 100, 0);
				vert[1].pos = Rainbow::Vector4f(100, 0, 100, 0);
				vert[2].pos = Rainbow::Vector4f(100, 50, 100, 0);

				vert[0].uv = { 1, 1 };
				vert[1].uv = { 0, 1 };
				vert[2].uv = { 0, 0 };
			}
			else if (8 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 50, 0, 0);
				vert[1].pos = Rainbow::Vector4f(0, 100, 100, 0);
				vert[2].pos = Rainbow::Vector4f(0, 100, 0, 0);

				vert[0].uv = { 1, 1 };
				vert[1].uv = { 0, 0 };
				vert[2].uv = { 1, 0 };
			}
			else if (9 == d)
			{
				vert[0].pos = Rainbow::Vector4f(100, 50, 0, 0);
				vert[1].pos = Rainbow::Vector4f(100, 100, 0, 0);
				vert[2].pos = Rainbow::Vector4f(100, 100, 100, 0);

				vert[0].uv = { 0, 1 };
				vert[1].uv = { 0, 0 };
				vert[2].uv = { 1, 0 };
			}
			else if (10 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 50, 0, 0);
				vert[1].pos = Rainbow::Vector4f(0, 100, 0, 0);
				vert[2].pos = Rainbow::Vector4f(100, 100, 0, 0);

				vert[0].uv = { 0, 1 };
				vert[1].uv = { 0, 0 };
				vert[2].uv = { 1, 0 };
			}
			else if (11 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 50, 100, 0);
				vert[1].pos = Rainbow::Vector4f(100, 100, 100, 0);
				vert[2].pos = Rainbow::Vector4f(0, 100, 100, 0);

				vert[0].uv = { 1, 1 };
				vert[1].uv = { 0, 0 };
				vert[2].uv = { 1, 0 };
			}
			else if (12 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 50, 100, 0);
				vert[1].pos = Rainbow::Vector4f(0, 100, 100, 0);
				vert[2].pos = Rainbow::Vector4f(0, 100, 0, 0);

				vert[0].uv = { 0, 1 };
				vert[1].uv = { 0, 0 };
				vert[2].uv = { 1, 0 };
			}
			else if (13 == d)
			{
				vert[0].pos = Rainbow::Vector4f(100, 100, 0, 0);
				vert[1].pos = Rainbow::Vector4f(100, 100, 100, 0);
				vert[2].pos = Rainbow::Vector4f(100, 50, 100, 0);

				vert[0].uv = { 0, 0 };
				vert[1].uv = { 1, 0 };
				vert[2].uv = { 1, 1 };
			}
			else if (14 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 100, 0, 0);
				vert[1].pos = Rainbow::Vector4f(100, 100, 0, 0);
				vert[2].pos = Rainbow::Vector4f(100, 50, 0, 0);

				vert[0].uv = { 0, 0 };
				vert[1].uv = { 1, 0 };
				vert[2].uv = { 1, 1 };
			}
			else if (15 == d)
			{
				vert[0].pos = Rainbow::Vector4f(100, 50, 100, 0);
				vert[1].pos = Rainbow::Vector4f(100, 100, 100, 0);
				vert[2].pos = Rainbow::Vector4f(0, 100, 100, 0);

				vert[0].uv = { 0, 1 };
				vert[1].uv = { 0, 0 };
				vert[2].uv = { 1, 0 };
			}
			for (int oo = 0; oo < 3; oo++)
			{
				vert[oo].uv = { short(vert[oo].uv.x * BLOCKUV_SCALE), short(i >= 2 ? short(vert[oo].uv.y * 0.5f * BLOCKUV_SCALE) : short((1.f - (1.f - vert[oo].uv.y) * 0.5f) * BLOCKUV_SCALE)) };
				vert[oo].normal = normal_dir;
				vert[oo].color.SetUInt32(vertcolor.v);
				vert[oo].pos.w = 0;//(lt1 << 8) | lt2;
				vertices.push_back(vert[oo]);
			}
			m_mTriangleFace_uv.push_back(vertices);
		}
	}
}

void HorizontalTriangleHalfSlabMaterial::initSlantFaceVertData()
{
	for (int i = 0; i < 2; i++)
	{
		for (int j = 0; j < 4; j++)
		{
			dynamic_array<UInt16>* indices = m_dPosIndices;
			int d = i * 4 + j;
			dynamic_array<BlockGeomVert> vertices;
			Rainbow::Vector3f normalVec = (g_DirectionCoord[j] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
			//unsigned short dir_color = Normal2LightColor(normalVec);
			BlockVector vertcolor;
			vertcolor.v = 0xffffffff;
			vertcolor.w = 0;
			Normalize(normalVec);
			BlockVector normal_dir = PackVertNormal(normalVec);
			BlockGeomVert vert[4];
			if (0 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
				vert[1].pos = Rainbow::Vector4f(0, 0, 100, 0);
				vert[2].pos = Rainbow::Vector4f(100, 50, 100, 0);
				vert[3].pos = Rainbow::Vector4f(100, 50, 0, 0);

				vert[0].uv = { 1, 1 };
				vert[1].uv = { 0, 1 };
				vert[2].uv = { 0, 0 };
				vert[3].uv = { 1, 0 };
			}
			else if (1 == d)
			{
				vert[0].pos = Rainbow::Vector4f(100, 0, 0, 0);
				vert[1].pos = Rainbow::Vector4f(0, 50, 0, 0);
				vert[2].pos = Rainbow::Vector4f(0, 50, 100, 0);
				vert[3].pos = Rainbow::Vector4f(100, 0, 100, 0);

				vert[0].uv = { 0, 1 };
				vert[1].uv = { 0, 0 };
				vert[2].uv = { 1, 0 };
				vert[3].uv = { 1, 1 };
			}
			else if (2 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
				vert[1].pos = Rainbow::Vector4f(0, 50, 100, 0);
				vert[2].pos = Rainbow::Vector4f(100, 50, 100, 0);
				vert[3].pos = Rainbow::Vector4f(100, 0, 0, 0);

				vert[0].uv = { 0, 1 };
				vert[1].uv = { 0, 0 };
				vert[2].uv = { 1, 0 };
				vert[3].uv = { 1, 1 };
			}
			else if (3 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 0, 100, 0);
				vert[1].pos = Rainbow::Vector4f(100, 0, 100, 0);
				vert[2].pos = Rainbow::Vector4f(100, 50, 0, 0);
				vert[3].pos = Rainbow::Vector4f(0, 50, 0, 0);

				vert[0].uv = { 1, 1 };
				vert[1].uv = { 0, 1 };
				vert[2].uv = { 0, 0 };
				vert[3].uv = { 1, 0 };
			}
			else if (4 == d)
			{
				vert[0].pos = Rainbow::Vector4f(100, 50, 0, 0);
				vert[1].pos = Rainbow::Vector4f(100, 50, 100, 0);
				vert[2].pos = Rainbow::Vector4f(0, 100, 100, 0);
				vert[3].pos = Rainbow::Vector4f(0, 100, 0, 0);

				vert[0].uv = { 1, 1 };
				vert[1].uv = { 0, 1 };
				vert[2].uv = { 0, 0 };
				vert[3].uv = { 1, 0 };
			}
			else if (5 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 50, 0, 0);
				vert[1].pos = Rainbow::Vector4f(100, 100, 0, 0);
				vert[2].pos = Rainbow::Vector4f(100, 100, 100, 0);
				vert[3].pos = Rainbow::Vector4f(0, 50, 100, 0);

				vert[0].uv = { 0, 1 };
				vert[1].uv = { 0, 0 };
				vert[2].uv = { 1, 0 };
				vert[3].uv = { 1, 1 };
			}
			else if (6 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 50, 100, 0);
				vert[1].pos = Rainbow::Vector4f(0, 100, 0, 0);
				vert[2].pos = Rainbow::Vector4f(100, 100, 0, 0);
				vert[3].pos = Rainbow::Vector4f(100, 50, 100, 0);

				vert[0].uv = { 0, 1 };
				vert[1].uv = { 0, 0 };
				vert[2].uv = { 1, 0 };
				vert[3].uv = { 1, 1 };
			}
			else if (7 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 50, 0, 0);
				vert[1].pos = Rainbow::Vector4f(100, 50, 0, 0);
				vert[2].pos = Rainbow::Vector4f(100, 100, 100, 0);
				vert[3].pos = Rainbow::Vector4f(0, 100, 100, 0);

				vert[0].uv = { 1, 1 };
				vert[1].uv = { 0, 1 };
				vert[2].uv = { 0, 0 };
				vert[3].uv = { 1, 0 };
			}
			for (int oo = 0; oo < 4; oo++)
			{
				vert[oo].uv = {short( vert[oo].uv.x * BLOCKUV_SCALE), short(vert[oo].uv.y * BLOCKUV_SCALE) };
				vert[oo].normal = normal_dir;
				vert[oo].color.SetUInt32(vertcolor.v);
				vertices.push_back(vert[oo]);
			}
			m_mSlantFace.push_back(vertices);
		}
	}
}

void HorizontalTriangleHalfSlabMaterial::initTurnSlantFaceVertData()
{
	for (int i = 0; i < 2; i++)//updown
	{
		for (int j = 0; j < 2; j++) //0�ǰ�1��͹
		{
			for (int k = 0; k < 4; k++)//4��������
			{
				DirectionType dir = (DirectionType)j;

				if (0 == k)
				{
					if (0 == i)
					{
						if (1 == j)
						{
							for (int o = 2; o <= 3; o++)
							{
								dynamic_array<BlockGeomVert> vertices;
								BlockGeomVert vert[3];
								if (o == 2)
								{
									vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
									vert[1].pos = Rainbow::Vector4f(0, 0, 100, 0);
									vert[2].pos = Rainbow::Vector4f(100, 50, 100, 0);
									vert[0].uv = { 1, 1 };
									vert[1].uv = { 0, 1 };
									vert[2].uv = { 0, 0 };
								}
								else
								{
									vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
									vert[1].pos = Rainbow::Vector4f(0, 0, 100, 0);
									vert[2].pos = Rainbow::Vector4f(100, 50, 0, 0);
									vert[0].uv = { 1, 1 };
									vert[1].uv = { 0, 1 };
									vert[2].uv = { 1, 0 };
								}

								Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
								//unsigned short dir_color = Normal2LightColor(normalVec);
								BlockVector vertcolor;
								vertcolor.v = 0xffffffff;
								vertcolor.w = 0;
								Normalize(normalVec);
								BlockVector normal_dir = PackVertNormal(normalVec);
								for (int oo = 0; oo < 3; oo++)
								{
									vert[oo].uv = { short(vert[oo].uv.x * BLOCKUV_SCALE), short(vert[oo].uv.y * BLOCKUV_SCALE) };
// 									vert[oo].uv = { vert[oo].uv.x * BLOCKUV_SCALE, i >= 2 ? short(vert[oo].uv.y * 0.5f * BLOCKUV_SCALE) : short((1.f - (1.f - vert[oo].uv.y) * 0.5f) * BLOCKUV_SCALE) };
									vert[oo].normal = normal_dir;
									vert[oo].color.SetUInt32(vertcolor.v);
									vertices.push_back(vert[oo]);
								}

								m_mTurnSlantFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							}
						}
						else
						{
							for (int o = 2; o <= 3; o++)
							{
								dynamic_array<BlockGeomVert> vertices;
								BlockGeomVert vert[3];
								if (o == 2)
								{
									vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
									vert[1].pos = Rainbow::Vector4f(100, 50, 100, 0);
									vert[2].pos = Rainbow::Vector4f(100, 50, 0, 0);

									vert[0].uv = { 1, 1 };
									vert[1].uv = { 0, 0 };
									vert[2].uv = { 1, 0 };
								}
								else
								{
									vert[0].pos = Rainbow::Vector4f(0, 0, 100, 0);
									vert[1].pos = Rainbow::Vector4f(100, 50, 100, 0);
									vert[2].pos = Rainbow::Vector4f(100, 50, 0, 0);

									vert[0].uv = { 0, 1 };
									vert[1].uv = { 0, 0 };
									vert[2].uv = { 1, 0 };
								}

								Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
								//unsigned short dir_color = Normal2LightColor(normalVec);
								BlockVector vertcolor;
								vertcolor.v = 0xffffffff;
								vertcolor.w = 0;
								Normalize(normalVec);
								BlockVector normal_dir = PackVertNormal(normalVec);
								for (int oo = 0; oo < 3; oo++)
								{
									vert[oo].uv = {short( vert[oo].uv.x * BLOCKUV_SCALE), short(vert[oo].uv.y * BLOCKUV_SCALE) };
// 									vert[oo].uv = { vert[oo].uv.x * BLOCKUV_SCALE, i >= 2 ? short(vert[oo].uv.y * 0.5f * BLOCKUV_SCALE) : short((1.f - (1.f - vert[oo].uv.y) * 0.5f) * BLOCKUV_SCALE) };
									vert[oo].normal = normal_dir;
									vert[oo].color.SetUInt32(vertcolor.v);
									vertices.push_back(vert[oo]);
								}
								m_mTurnSlantFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							}
						}
					}
					else
					{
						if (1 == j)
						{
							for (int o = 2; o <= 3; o++)
							{
								dynamic_array<BlockGeomVert> vertices;
								BlockGeomVert vert[3];
								if (o == 2)
								{
									vert[0].pos = Rainbow::Vector4f(100, 50, 100, 0);
									vert[1].pos = Rainbow::Vector4f(0, 100, 100, 0);
									vert[2].pos = Rainbow::Vector4f(0, 100, 0, 0);
									vert[0].uv = { 0, 1 };
									vert[1].uv = { 0, 0 };
									vert[2].uv = { 1, 0 };
								}
								else
								{
									vert[0].pos = Rainbow::Vector4f(100, 50, 0, 0);
									vert[1].pos = Rainbow::Vector4f(0, 100, 100, 0);
									vert[2].pos = Rainbow::Vector4f(0, 100, 0, 0);
									vert[0].uv = { 1, 1 };
									vert[1].uv = { 0, 0 };
									vert[2].uv = { 1, 0 };
								}

								Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
								//unsigned short dir_color = Normal2LightColor(normalVec);
								BlockVector vertcolor;
								vertcolor.v = 0xffffffff;
								vertcolor.w = 0;
								Normalize(normalVec);
								BlockVector normal_dir = PackVertNormal(normalVec);
								for (int oo = 0; oo < 3; oo++)
								{
									vert[oo].uv = { short(vert[oo].uv.x * BLOCKUV_SCALE), short(vert[oo].uv.y * BLOCKUV_SCALE) };
// 									vert[oo].uv = { vert[oo].uv.x * BLOCKUV_SCALE, i >= 2 ? short(vert[oo].uv.y * 0.5f * BLOCKUV_SCALE) : short((1.f - (1.f - vert[oo].uv.y) * 0.5f) * BLOCKUV_SCALE) };
									vert[oo].normal = normal_dir;
									vert[oo].color.SetUInt32(vertcolor.v);
									vertices.push_back(vert[oo]);
								}
								m_mTurnSlantFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							}
						}
						else
						{
							for (int o = 2; o <= 3; o++)
							{
								dynamic_array<BlockGeomVert> vertices;
								BlockGeomVert vert[3];
								if (o == 2)
								{
									vert[0].pos = Rainbow::Vector4f(100, 50, 0, 0);
									vert[1].pos = Rainbow::Vector4f(100, 50, 100, 0);
									vert[2].pos = Rainbow::Vector4f(0, 100, 0, 0);
									vert[0].uv = { 1, 1 };
									vert[1].uv = { 0, 1 };
									vert[2].uv = { 1, 0 };
								}
								else
								{
									vert[0].pos = Rainbow::Vector4f(100, 50, 0, 0);
									vert[1].pos = Rainbow::Vector4f(100, 50, 100, 0);
									vert[2].pos = Rainbow::Vector4f(0, 100, 100, 0);
									vert[0].uv = { 1, 1 };
									vert[1].uv = { 0, 1 };
									vert[2].uv = { 0, 0 };
								}

								Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
								//unsigned short dir_color = Normal2LightColor(normalVec);
								BlockVector vertcolor;
								vertcolor.v = 0xffffffff;
								vertcolor.w = 0;
								Normalize(normalVec);
								BlockVector normal_dir = PackVertNormal(normalVec);
								for (int oo = 0; oo < 3; oo++)
								{
									vert[oo].uv = { short(vert[oo].uv.x * BLOCKUV_SCALE),short( vert[oo].uv.y * BLOCKUV_SCALE) };
// 									vert[oo].uv = { vert[oo].uv.x * BLOCKUV_SCALE, i >= 2 ? short(vert[oo].uv.y * 0.5f * BLOCKUV_SCALE) : short((1.f - (1.f - vert[oo].uv.y) * 0.5f) * BLOCKUV_SCALE) };
									vert[oo].normal = normal_dir;
									vert[oo].color.SetUInt32(vertcolor.v);
									vertices.push_back(vert[oo]);
								}
								m_mTurnSlantFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							}
						}
					}
				}
				else if (1 == k)
				{
					if (0 == i)
					{
						if (1 == j)
						{
							for (int o = 2; o <= 3; o++)
							{
								dynamic_array<BlockGeomVert> vertices;
								BlockGeomVert vert[3];
								if (o == 2)
								{
									vert[0].pos = Rainbow::Vector4f(100, 0, 0, 0);
									vert[1].pos = Rainbow::Vector4f(0, 50, 100, 0);
									vert[2].pos = Rainbow::Vector4f(100, 0, 100, 0);
									vert[0].uv = { 0, 1 };
									vert[1].uv = { 1, 0 };
									vert[2].uv = { 1, 1 }; 
								}
								else
								{
									vert[0].pos = Rainbow::Vector4f(100, 0, 0, 0);
									vert[1].pos = Rainbow::Vector4f(0, 50, 0, 0);
									vert[2].pos = Rainbow::Vector4f(100, 0, 100, 0);
									vert[0].uv = { 0, 1 };
									vert[1].uv = { 0, 0 };
									vert[2].uv = { 1, 1 };
								}

								Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
								//unsigned short dir_color = Normal2LightColor(normalVec);
								BlockVector vertcolor;
								vertcolor.v = 0xffffffff;
								vertcolor.w = 0;
								Normalize(normalVec);
								BlockVector normal_dir = PackVertNormal(normalVec);
								for (int oo = 0; oo < 3; oo++)
								{
									vert[oo].uv = { short(vert[oo].uv.x * BLOCKUV_SCALE), short(vert[oo].uv.y * BLOCKUV_SCALE) };
// 									vert[oo].uv = { vert[oo].uv.x * BLOCKUV_SCALE, i >= 2 ? short(vert[oo].uv.y * 0.5f * BLOCKUV_SCALE) : short((1.f - (1.f - vert[oo].uv.y) * 0.5f) * BLOCKUV_SCALE) };
									vert[oo].normal = normal_dir;
									vert[oo].color.SetUInt32(vertcolor.v);
									vertices.push_back(vert[oo]);
								}
								m_mTurnSlantFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							}
						}
						else
						{
							for (int o = 2; o <= 3; o++)
							{
								dynamic_array<BlockGeomVert> vertices;
								BlockGeomVert vert[3];
								if (o == 2)
								{
									vert[0].pos = Rainbow::Vector4f(100, 0, 0, 0);
									vert[1].pos = Rainbow::Vector4f(0, 50, 0, 0);
									vert[2].pos = Rainbow::Vector4f(0, 50, 100, 0);
									vert[0].uv = { 0, 1 };
									vert[1].uv = { 0, 0 };
									vert[2].uv = { 1, 0 };
								}
								else
								{
									vert[0].pos = Rainbow::Vector4f(0, 50, 0, 0);
									vert[1].pos = Rainbow::Vector4f(0, 50, 100, 0);
									vert[2].pos = Rainbow::Vector4f(100, 0, 100, 0);
									vert[0].uv = { 0, 0 };
									vert[1].uv = { 1, 0 };
									vert[2].uv = { 1, 1 };
								}
								Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
								//unsigned short dir_color = Normal2LightColor(normalVec);
								BlockVector vertcolor;
								vertcolor.v = 0xffffffff;
								vertcolor.w = 0;
								Normalize(normalVec);
								BlockVector normal_dir = PackVertNormal(normalVec);
								for (int oo = 0; oo < 3; oo++)
								{
									vert[oo].uv = { short(vert[oo].uv.x * BLOCKUV_SCALE), short(vert[oo].uv.y * BLOCKUV_SCALE) };
// 									vert[oo].uv = { vert[oo].uv.x * BLOCKUV_SCALE, i >= 2 ? short(vert[oo].uv.y * 0.5f * BLOCKUV_SCALE) : short((1.f - (1.f - vert[oo].uv.y) * 0.5f) * BLOCKUV_SCALE) };
									vert[oo].normal = normal_dir;
									vert[oo].color.SetUInt32(vertcolor.v);
									vertices.push_back(vert[oo]);
								}
								m_mTurnSlantFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							}
						}
					}
					else
					{
						if (1 == j)
						{
							for (int o = 2; o <= 3; o++)
							{
								dynamic_array<BlockGeomVert> vertices;
								BlockGeomVert vert[3];
								if (o == 2)
								{
									vert[0].pos = Rainbow::Vector4f(100, 100, 0, 0);
									vert[1].pos = Rainbow::Vector4f(100, 100, 100, 0);
									vert[2].pos = Rainbow::Vector4f(0, 50, 100, 0);
									vert[0].uv = { 0, 0 };
									vert[1].uv = { 1, 0 };
									vert[2].uv = { 1, 1 };
								}
								else
								{
									vert[0].pos = Rainbow::Vector4f(0, 50, 0, 0);
									vert[1].pos = Rainbow::Vector4f(100, 100, 0, 0);
									vert[2].pos = Rainbow::Vector4f(100, 100, 100, 0);
									vert[0].uv = { 0, 1 };
									vert[1].uv = { 0, 0 };
									vert[2].uv = { 1, 0 };
								}

								Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
								//unsigned short dir_color = Normal2LightColor(normalVec);
								BlockVector vertcolor;
								vertcolor.v = 0xffffffff;
								vertcolor.w = 0;
								Normalize(normalVec);
								BlockVector normal_dir = PackVertNormal(normalVec);
								for (int oo = 0; oo < 3; oo++)
								{
									vert[oo].uv = { short(vert[oo].uv.x * BLOCKUV_SCALE), short(vert[oo].uv.y * BLOCKUV_SCALE) };
// 									vert[oo].uv = { vert[oo].uv.x * BLOCKUV_SCALE, i >= 2 ? short(vert[oo].uv.y * 0.5f * BLOCKUV_SCALE) : short((1.f - (1.f - vert[oo].uv.y) * 0.5f) * BLOCKUV_SCALE) };
									vert[oo].normal = normal_dir;
									vert[oo].color.SetUInt32(vertcolor.v);
									vertices.push_back(vert[oo]);
								}
								m_mTurnSlantFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							}
						}
						else
						{
							for (int o = 2; o <= 3; o++)
							{
								dynamic_array<BlockGeomVert> vertices;
								BlockGeomVert vert[3];
								if (o == 2)
								{
									vert[0].pos = Rainbow::Vector4f(0, 50, 0, 0);
									vert[1].pos = Rainbow::Vector4f(100, 100, 0, 0);
									vert[2].pos = Rainbow::Vector4f(0, 50, 100, 0);
									vert[0].uv = { 0, 1 };
									vert[1].uv = { 0, 0 };
									vert[2].uv = { 1, 1 };
								}
								else
								{
									vert[0].pos = Rainbow::Vector4f(0, 50, 0, 0);
									vert[1].pos = Rainbow::Vector4f(100, 100, 100, 0);
									vert[2].pos = Rainbow::Vector4f(0, 50, 100, 0);
									vert[0].uv = { 0, 1 };
									vert[1].uv = { 1, 0 };
									vert[2].uv = { 1, 1 };
								}

								Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
								//unsigned short dir_color = Normal2LightColor(normalVec);
								BlockVector vertcolor;
								vertcolor.v = 0xffffffff;
								vertcolor.w = 0;
								Normalize(normalVec);
								BlockVector normal_dir = PackVertNormal(normalVec);
								for (int oo = 0; oo < 3; oo++)
								{
									vert[oo].uv = { short(vert[oo].uv.x * BLOCKUV_SCALE), short(vert[oo].uv.y * BLOCKUV_SCALE) };
// 									vert[oo].uv = { vert[oo].uv.x * BLOCKUV_SCALE, i >= 2 ? short(vert[oo].uv.y * 0.5f * BLOCKUV_SCALE) : short((1.f - (1.f - vert[oo].uv.y) * 0.5f) * BLOCKUV_SCALE) };
									vert[oo].normal = normal_dir;
									vert[oo].color.SetUInt32(vertcolor.v);
									vertices.push_back(vert[oo]);
								}
								m_mTurnSlantFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							}
						}
					}
				}
				else if (2 == k)
				{
					if (0 == i)
					{
						if (1 == j)
						{
							for (int o = 0; o <= 1; o++)
							{
								dynamic_array<BlockGeomVert> vertices;
								BlockGeomVert vert[3];
								if (o == 0)
								{
									vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
									vert[1].pos = Rainbow::Vector4f(100, 50, 100, 0);
									vert[2].pos = Rainbow::Vector4f(100, 0, 0, 0);
									vert[0].uv = { 0, 1 };
									vert[1].uv = { 1, 0 };
									vert[2].uv = { 1, 1 };
								}
								else
								{
									vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
									vert[1].pos = Rainbow::Vector4f(0, 50, 100, 0);
									vert[2].pos = Rainbow::Vector4f(100, 0, 0, 0);
									vert[0].uv = { 0, 1 };
									vert[1].uv = { 0, 0 };
									vert[2].uv = { 1, 1 };
								}

								Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
								//unsigned short dir_color = Normal2LightColor(normalVec);
								BlockVector vertcolor;
								vertcolor.v = 0xffffffff;
								vertcolor.w = 0;
								Normalize(normalVec);
								BlockVector normal_dir = PackVertNormal(normalVec);
								for (int oo = 0; oo < 3; oo++)
								{
									vert[oo].uv = { short(vert[oo].uv.x * BLOCKUV_SCALE), short(vert[oo].uv.y * BLOCKUV_SCALE) };
// 									vert[oo].uv = { vert[oo].uv.x * BLOCKUV_SCALE, i >= 2 ? short(vert[oo].uv.y * 0.5f * BLOCKUV_SCALE) : short((1.f - (1.f - vert[oo].uv.y) * 0.5f) * BLOCKUV_SCALE) };
									vert[oo].normal = normal_dir;
									vert[oo].color.SetUInt32(vertcolor.v);
									vertices.push_back(vert[oo]);
								}
								m_mTurnSlantFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							}
						}
						else
						{
							for (int o = 0; o <= 1; o++)
							{
								dynamic_array<BlockGeomVert> vertices;
								BlockGeomVert vert[3];
								if (o == 0)
								{
									vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
									vert[1].pos = Rainbow::Vector4f(0, 50, 100, 0);
									vert[2].pos = Rainbow::Vector4f(100, 50, 100, 0);
									vert[0].uv = { 0, 1 };
									vert[1].uv = { 0, 0 };
									vert[2].uv = { 1, 0 };
								}
								else
								{
									vert[0].pos = Rainbow::Vector4f(0, 50, 100, 0);
									vert[1].pos = Rainbow::Vector4f(100, 50, 100, 0);
									vert[2].pos = Rainbow::Vector4f(100, 0, 0, 0);
									vert[0].uv = { 0, 0 };
									vert[1].uv = { 1, 0 };
									vert[2].uv = { 1, 1 };
								}

								Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
								//unsigned short dir_color = Normal2LightColor(normalVec);
								BlockVector vertcolor;
								vertcolor.v = 0xffffffff;
								vertcolor.w = 0;
								Normalize(normalVec);
								BlockVector normal_dir = PackVertNormal(normalVec);
								for (int oo = 0; oo < 3; oo++)
								{
									vert[oo].uv = { short(vert[oo].uv.x * BLOCKUV_SCALE), short(vert[oo].uv.y * BLOCKUV_SCALE) };
// 									vert[oo].uv = { vert[oo].uv.x * BLOCKUV_SCALE, i >= 2 ? short(vert[oo].uv.y * 0.5f * BLOCKUV_SCALE) : short((1.f - (1.f - vert[oo].uv.y) * 0.5f) * BLOCKUV_SCALE) };
									vert[oo].normal = normal_dir;
									vert[oo].color.SetUInt32(vertcolor.v);
									vertices.push_back(vert[oo]);
								}
								m_mTurnSlantFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							}
						}
					}
					else
					{
						if (1 == j)
						{
							for (int o = 0; o <= 1; o++)
							{
								dynamic_array<BlockGeomVert> vertices;
								BlockGeomVert vert[3];
								if (o == 0)
								{
									vert[0].pos = Rainbow::Vector4f(0, 100, 0, 0);
									vert[1].pos = Rainbow::Vector4f(100, 100, 0, 0);
									vert[2].pos = Rainbow::Vector4f(100, 50, 100, 0);
									vert[0].uv = { 0, 0 };
									vert[1].uv = { 1, 0 };
									vert[2].uv = { 1, 1 };
								}
								else
								{
									vert[0].pos = Rainbow::Vector4f(0, 50, 100, 0);
									vert[1].pos = Rainbow::Vector4f(0, 100, 0, 0);
									vert[2].pos = Rainbow::Vector4f(100, 100, 0, 0);
									vert[0].uv = { 0, 1 };
									vert[1].uv = { 0, 0 };
									vert[2].uv = { 1, 0 };
								}

								Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
								//unsigned short dir_color = Normal2LightColor(normalVec);
								BlockVector vertcolor;
								vertcolor.v = 0xffffffff;
								vertcolor.w = 0;
								Normalize(normalVec);
								BlockVector normal_dir = PackVertNormal(normalVec);
								for (int oo = 0; oo < 3; oo++)
								{
									vert[oo].uv = { short(vert[oo].uv.x * BLOCKUV_SCALE), short(vert[oo].uv.y * BLOCKUV_SCALE) };
// 									vert[oo].uv = { vert[oo].uv.x * BLOCKUV_SCALE, i >= 2 ? short(vert[oo].uv.y * 0.5f * BLOCKUV_SCALE) : short((1.f - (1.f - vert[oo].uv.y) * 0.5f) * BLOCKUV_SCALE) };
									vert[oo].normal = normal_dir;
									vert[oo].color.SetUInt32(vertcolor.v);
									vertices.push_back(vert[oo]);
								}
								m_mTurnSlantFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							}
						}
						else
						{
							for (int o = 0; o <= 1; o++)
							{
								dynamic_array<BlockGeomVert> vertices;
								BlockGeomVert vert[3];
								if (o == 0)
								{
									vert[0].pos = Rainbow::Vector4f(0, 50, 100, 0);
									vert[1].pos = Rainbow::Vector4f(0, 100, 0, 0);
									vert[2].pos = Rainbow::Vector4f(100, 50, 100, 0);
									vert[0].uv = { 0, 1 };
									vert[1].uv = { 1, 0 };
									vert[2].uv = { 1, 1 };
								}
								else
								{
									vert[0].pos = Rainbow::Vector4f(0, 50, 100, 0);
									vert[1].pos = Rainbow::Vector4f(100, 100, 0, 0);
									vert[2].pos = Rainbow::Vector4f(100, 50, 100, 0);
									vert[0].uv = { 0, 1 };
									vert[1].uv = { 0, 0 };
									vert[2].uv = { 1, 1 };
								}

								Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
								//unsigned short dir_color = Normal2LightColor(normalVec);
								BlockVector vertcolor;
								vertcolor.v = 0xffffffff;
								vertcolor.w = 0;
								Normalize(normalVec);
								BlockVector normal_dir = PackVertNormal(normalVec);
								for (int oo = 0; oo < 3; oo++)
								{
									vert[oo].uv = { short(vert[oo].uv.x * BLOCKUV_SCALE), short(vert[oo].uv.y * BLOCKUV_SCALE) };
// 									vert[oo].uv = { vert[oo].uv.x * BLOCKUV_SCALE, i >= 2 ? short(vert[oo].uv.y * 0.5f * BLOCKUV_SCALE) : short((1.f - (1.f - vert[oo].uv.y) * 0.5f) * BLOCKUV_SCALE) };
									vert[oo].normal = normal_dir;
									vert[oo].color.SetUInt32(vertcolor.v);
									vertices.push_back(vert[oo]);
								}
								m_mTurnSlantFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							}
						}
					}
				}
				else if (3 == k)
				{
					if (0 == i)
					{
						if (1 == j)
						{
							for (int o = 0; o <= 1; o++)
							{
								dynamic_array<BlockGeomVert> vertices;
								BlockGeomVert vert[3];
								if (o == 0)
								{
									vert[0].pos = Rainbow::Vector4f(0, 0, 100, 0);
									vert[1].pos = Rainbow::Vector4f(100, 0, 100, 0);
									vert[2].pos = Rainbow::Vector4f(100, 50, 0, 0);
									vert[0].uv = { 1, 1 };
									vert[1].uv = { 0, 1 };
									vert[2].uv = { 0, 0 };
								}
								else
								{
									vert[0].pos = Rainbow::Vector4f(0, 0, 100, 0);
									vert[1].pos = Rainbow::Vector4f(100, 0, 100, 0);
									vert[2].pos = Rainbow::Vector4f(0, 50, 0, 0);
									vert[0].uv = { 1, 1 };
									vert[1].uv = { 0, 1 };
									vert[2].uv = { 1, 0 };
								}

								Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
								//unsigned short dir_color = Normal2LightColor(normalVec);
								BlockVector vertcolor;
								vertcolor.v = 0xffffffff;
								vertcolor.w = 0;
								Normalize(normalVec);
								BlockVector normal_dir = PackVertNormal(normalVec);
								for (int oo = 0; oo < 3; oo++)
								{
									vert[oo].uv = { short(vert[oo].uv.x * BLOCKUV_SCALE), short(vert[oo].uv.y * BLOCKUV_SCALE) };
// 									vert[oo].uv = { vert[oo].uv.x * BLOCKUV_SCALE, i >= 2 ? short(vert[oo].uv.y * 0.5f * BLOCKUV_SCALE) : short((1.f - (1.f - vert[oo].uv.y) * 0.5f) * BLOCKUV_SCALE) };
									vert[oo].normal = normal_dir;
									vert[oo].color.SetUInt32(vertcolor.v);
									vertices.push_back(vert[oo]);
								}
								m_mTurnSlantFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							}
						}
						else
						{
							for (int o = 0; o <= 1; o++)
							{
								dynamic_array<BlockGeomVert> vertices;
								BlockGeomVert vert[3];
								if (o == 0)
								{
									vert[0].pos = Rainbow::Vector4f(0, 0, 100, 0);
									vert[1].pos = Rainbow::Vector4f(100, 50, 0, 0);
									vert[2].pos = Rainbow::Vector4f(0, 50, 0, 0);
									vert[0].uv = { 0, 1 };
									vert[1].uv = { 0, 0 };
									vert[2].uv = { 1, 0 };
								}
								else
								{
									vert[0].pos = Rainbow::Vector4f(100, 0, 100, 0);
									vert[1].pos = Rainbow::Vector4f(100, 50, 0, 0);
									vert[2].pos = Rainbow::Vector4f(0, 50, 0, 0);
									vert[0].uv = { 0, 1 };
									vert[1].uv = { 0, 0 };
									vert[2].uv = { 1, 0 };
								}

								Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
								//unsigned short dir_color = Normal2LightColor(normalVec);
								BlockVector vertcolor;
								vertcolor.v = 0xffffffff;
								vertcolor.w = 0;
								Normalize(normalVec);
								BlockVector normal_dir = PackVertNormal(normalVec);
								for (int oo = 0; oo < 3; oo++)
								{
									vert[oo].uv = { short(vert[oo].uv.x * BLOCKUV_SCALE), short(vert[oo].uv.y * BLOCKUV_SCALE) };
// 									vert[oo].uv = { vert[oo].uv.x * BLOCKUV_SCALE, i >= 2 ? short(vert[oo].uv.y * 0.5f * BLOCKUV_SCALE) : short((1.f - (1.f - vert[oo].uv.y) * 0.5f) * BLOCKUV_SCALE) };
									vert[oo].normal = normal_dir;
									vert[oo].color.SetUInt32(vertcolor.v);
									vertices.push_back(vert[oo]);
								}
								m_mTurnSlantFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							}
						}
					}
					else
					{
						if (1 == j)
						{
							for (int o = 0; o <= 1; o++)
							{
								dynamic_array<BlockGeomVert> vertices;
								BlockGeomVert vert[3];
								if (o == 0)
								{
									vert[0].pos = Rainbow::Vector4f(100, 50, 0, 0);
									vert[1].pos = Rainbow::Vector4f(100, 100, 100, 0);
									vert[2].pos = Rainbow::Vector4f(0, 100, 100, 0);
									vert[0].uv = { 0, 1 };
									vert[1].uv = { 0, 0 };
									vert[2].uv = { 1, 0 };
								}
								else
								{
									vert[0].pos = Rainbow::Vector4f(0, 50, 0, 0);
									vert[1].pos = Rainbow::Vector4f(100, 100, 100, 0);
									vert[2].pos = Rainbow::Vector4f(0, 100, 100, 0);
									vert[0].uv = { 1, 1 };
									vert[1].uv = { 0, 0 };
									vert[2].uv = { 1, 0 };
								}

								Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
								//unsigned short dir_color = Normal2LightColor(normalVec);
								BlockVector vertcolor;
								vertcolor.v = 0xffffffff;
								vertcolor.w = 0;
								Normalize(normalVec);
								BlockVector normal_dir = PackVertNormal(normalVec);
								for (int oo = 0; oo < 3; oo++)
								{
									vert[oo].uv = { short(vert[oo].uv.x * BLOCKUV_SCALE), short(vert[oo].uv.y * BLOCKUV_SCALE) };
// 									vert[oo].uv = { vert[oo].uv.x * BLOCKUV_SCALE, i >= 2 ? short(vert[oo].uv.y * 0.5f * BLOCKUV_SCALE) : short((1.f - (1.f - vert[oo].uv.y) * 0.5f) * BLOCKUV_SCALE) };
									vert[oo].normal = normal_dir;
									vert[oo].color.SetUInt32(vertcolor.v);
									vertices.push_back(vert[oo]);
								}
								m_mTurnSlantFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							}
						}
						else
						{
							for (int o = 0; o <= 1; o++)
							{
								dynamic_array<BlockGeomVert> vertices;
								BlockGeomVert vert[3];
								if (o == 0)
								{
									vert[0].pos = Rainbow::Vector4f(0, 50, 0, 0);
									vert[1].pos = Rainbow::Vector4f(100, 50, 0, 0);
									vert[2].pos = Rainbow::Vector4f(0, 100, 100, 0);
									vert[0].uv = { 1, 1 };
									vert[1].uv = { 0, 1 };
									vert[2].uv = { 1, 0 };
								}
								else
								{
									vert[0].pos = Rainbow::Vector4f(0, 50, 0, 0);
									vert[1].pos = Rainbow::Vector4f(100, 50, 0, 0);
									vert[2].pos = Rainbow::Vector4f(100, 100, 100, 0);

									vert[0].uv = { 1, 1 };
									vert[1].uv = { 0, 1 };
									vert[2].uv = { 0, 0 };
								}

								Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
								//unsigned short dir_color = Normal2LightColor(normalVec);
								BlockVector vertcolor;
								vertcolor.v = 0xffffffff;
								vertcolor.w = 0;
								Normalize(normalVec);
								BlockVector normal_dir = PackVertNormal(normalVec);
								for (int oo = 0; oo < 3; oo++)
								{
									vert[oo].uv = { short(vert[oo].uv.x * BLOCKUV_SCALE), short(vert[oo].uv.y * BLOCKUV_SCALE) };
// 									vert[oo].uv = { vert[oo].uv.x * BLOCKUV_SCALE, i >= 2 ? short(vert[oo].uv.y * 0.5f * BLOCKUV_SCALE) : short((1.f - (1.f - vert[oo].uv.y) * 0.5f) * BLOCKUV_SCALE) };
									vert[oo].normal = normal_dir;
									vert[oo].color.SetUInt32(vertcolor.v);
									vertices.push_back(vert[oo]);
								}
								m_mTurnSlantFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							}
						}
					}
				}
			}
		}
	}
}

void HorizontalTriangleHalfSlabMaterial::initPhyModelData()
{
	if (m_mWholeFace().size() == 0 || m_mTriangleFace.size() == 0 || m_mSlantFace.size() == 0
		|| m_mHalfWholeFace().size() == 0 || m_mHalfFace().size() == 0)
	{
		return;
	}
	int triangleFaceId[4] = { 6,2,4,0 };
	for (int i = 0; i < 2; i++)
	{
		for (int j = 0; j < 4; j++)
		{
			TrianglePhyModel model;
			dynamic_array<dynamic_array<BlockGeomVert>*> faceList;
			//4 vert
			faceList.push_back(0 == i ? &m_mWholeFace()[4] : &m_mWholeFace()[5]);
			faceList.push_back(&m_mHalfFace()[ReverseDirection(j) + (i * 4)]);
			faceList.push_back(&m_mTriangleFace[8 * i + triangleFaceId[j]]);
			faceList.push_back(&m_mSlantFace[4 * i + j]);
			faceList.push_back(&m_mTriangleFace[8 * i + triangleFaceId[j] + 1]);

			int triangleCount = 0;
			for (auto face : faceList)
			{
				dynamic_array<short> indexlist;
				for (auto& vertdata : (*face))
				{
					Vector3f pos(vertdata.pos.x, vertdata.pos.y, vertdata.pos.z);
					short index = -1;
					for (int o = 0; o < model.verts.size(); o++)
					{
						if (model.verts[o] == pos)
						{
							index = o;
							break;
						}
					}
					if (index == -1)
					{
						index = static_cast<short>(model.verts.size());
						model.verts.push_back(pos);
					}
					indexlist.push_back(index);
				}
				dynamic_array<UInt16>* posIndeices = m_dPosIndices;
				if (indexlist.size() == 3)
				{
					posIndeices = m_PosTrIndices;
					triangleCount++;
				}
				else if (indexlist.size() > 3)
				{
					triangleCount += indexlist.size() - 2;
				}
				for (auto& idxex : (*posIndeices))
				{
					model.idxs.push_back(indexlist[idxex]);
				}
			}
			model.triangleCount = triangleCount;
			m_mPhyModel.insert(make_pair(i * 4 + j, model));
		}
	}

	int speicalFaceId[4] = { 4,0,4,0 };
	for (int i = 0; i < 2; i++)//0 down 1 up
	{
		for (int j = 0; j < 2; j++)//0 �� 1 ͹
		{
			for (int k = 0; k < 4; k++)
			{
				int dirlist[2] = { RotateDirPos90(k), RotateDir90(k) };
				for (int q = 0; q < 2; q++)
				{
					auto iter = m_mPhyModel.find(10000 + i * 1000 + j * 100 + k * 10 + dirlist[q]);
					if (iter == m_mPhyModel.end())
					{
						TrianglePhyModel model;
						dynamic_array<dynamic_array<BlockGeomVert>*> faceList;
						faceList.push_back(0 == i ? &m_mWholeFace()[4] : &m_mWholeFace()[5]);
						if (1 == j)
						{
							faceList.push_back(&m_mTriangleFace[8 * i + speicalFaceId[k] + ReverseDirection(dirlist[q])]);
						}
						else
						{
							faceList.push_back(&m_mTriangleFace[8 * i + speicalFaceId[k] + dirlist[q]]);
							faceList.push_back(&m_mHalfFace()[ReverseDirection(k) + i * 4]);
							faceList.push_back(&m_mHalfFace()[ReverseDirection(dirlist[q]) + i * 4]);
						}
						faceList.push_back(&m_mTurnSlantFace[i * 1000 + j * 100 + k * 10 + dirlist[q]]);
						faceList.push_back(&m_mTurnSlantFace[i * 1000 + j * 100 + dirlist[q] * 10 + k]);

						int triangleCount = 0;
						for (auto face : faceList)
						{
							dynamic_array<short> indexlist;
							for (auto& vertdata : (*face))
							{
								Vector3f pos(vertdata.pos.x, vertdata.pos.y, vertdata.pos.z);
								short index = -1;
								for (int o = 0; o < model.verts.size(); o++)
								{
									if (model.verts[o] == pos)
									{
										index = o;
										break;
									}
								}
								if (index == -1)
								{
									index = static_cast<short>(model.verts.size());
									model.verts.push_back(pos);
								}
								indexlist.push_back(index);
							}
							dynamic_array<UInt16>* posIndeices = m_dPosIndices;
							if (indexlist.size() == 3)
							{
								posIndeices = m_PosTrIndices;
								triangleCount++;
							}
							else if (indexlist.size() > 3)
							{
								triangleCount += indexlist.size() - 2;
							}
							for (auto& idxex : (*posIndeices))
							{
								model.idxs.push_back(indexlist[idxex]);
							}
						}
						model.triangleCount = triangleCount;
						m_mPhyModel.insert(make_pair(10000 + i * 1000 + j * 100 + k * 10 + dirlist[q], model));
					}
				}
			}
		}
	}

	for (int i = 0; i < 2; i++)
	{
		auto iter = m_mPhyModel.find(20000 + i);
		if (iter == m_mPhyModel.end())
		{
			TrianglePhyModel model;
			dynamic_array<dynamic_array<BlockGeomVert>*> faceList;
			faceList.push_back(0 == i ? &m_mWholeFace()[4] : &m_mWholeFace()[5]);
			faceList.push_back(0 == i ? &m_mHalfWholeFace()[1] : &m_mHalfWholeFace()[0]); 
			for (int ii = 0 + i * 4; ii < i * 4 + 4; ii++)
			{
				faceList.push_back(&m_mHalfFace()[ii]);
			}

			int triangleCount = 0;
			for (auto face : faceList)
			{
				dynamic_array<short> indexlist;
				for (auto& vertdata : (*face))
				{
					Vector3f pos(vertdata.pos.x, vertdata.pos.y, vertdata.pos.z);
					short index = -1;
					for (int o = 0; o < model.verts.size(); o++)
					{
						if (model.verts[o] == pos)
						{
							index = o;
							break;
						}
					}
					if (index == -1)
					{
						index = static_cast<short>(model.verts.size());
						model.verts.push_back(pos);
					}
					indexlist.push_back(index);
				}
				dynamic_array<UInt16>* posIndeices = m_dPosIndices;
				if (indexlist.size() == 3)
				{
					posIndeices = m_PosTrIndices;
					triangleCount++;
				}
				else if (indexlist.size() > 3)
				{
					triangleCount += indexlist.size() - 2;
				}
				for (auto& idxex : (*posIndeices))
				{
					model.idxs.push_back(indexlist[idxex]);
				}
			}
			model.triangleCount = triangleCount;
			m_mPhyModel.insert(make_pair(20000 + i, model));
		}
	}
}

 SectionMesh* HorizontalTriangleHalfSlabMaterial::createBlockProtoMesh(int protodata)
 {
	 SectionMesh* pmesh = ENG_NEW(SectionMesh)();
	 int blockdata = protodata;

	 const BiomeDef* biome = GetDefManagerProxy()->getBiomeDef(0);

	 char list[5] = { 3,4,4,5,2 };
	 BlockColor facecolor(255, 255, 255, 0);
	 for (int i = 0; i < 5; i++)
	 {
		 DirectionType dir = (DirectionType)(list[i] % 4);
		 RenderBlockMaterial* pmtl = getFaceMtl(biome, dir, blockdata, facecolor);
		 SectionSubMesh* psubmesh = pmesh->getSubMesh(pmtl, true);
		 dynamic_array<BlockGeomVert> vertices;

		 BlockGeomMeshInfo meshinfo;
		 if (i == 0)
		 {
			 meshinfo.vertices = m_mHalfFace()[list[i]];
			 meshinfo.indices = *m_dPosIndices;
		 }
		 else if (i == 1)
		 {
			 meshinfo.vertices = m_mWholeFace()[list[i]];
			 meshinfo.indices = *m_dPosIndices;
		 }
		 else if (i < 4)
		 {
			 meshinfo.vertices = m_mTriangleFace[list[i]];
			 meshinfo.indices = *m_PosTrIndices;
		 }
		 else
		 {
			 meshinfo.vertices = m_mSlantFace[list[i]];
			 meshinfo.indices = *m_dPosIndices;
		 }
		 if (psubmesh)
			 psubmesh->addGeomFaceLight(meshinfo, NULL, s_DefaultFaceVertexLights, &facecolor);
	 }
	 return pmesh;
 }

 typedef HorizontalTriangleHalfSlabMaterial::BlockInstance HorizontalTriangleHalfSlabMaterialInstance;
 IMPLEMENT_SCENEOBJECTCLASS(HorizontalTriangleHalfSlabMaterialInstance)
	 MNSandbox::ReflexClassParam<HorizontalTriangleHalfSlabMaterial::BlockInstance, int> HorizontalTriangleHalfSlabMaterialInstance::R_Dir(0, "Dir", "Block", &HorizontalTriangleHalfSlabMaterial::BlockInstance::GetBlockData, &HorizontalTriangleHalfSlabMaterial::BlockInstance::SetBlockData);




 IMPLEMENT_SCENEOBJECTCLASS(HorizontalTriangleSlabMaterial)
 float HorizontalTriangleSlabMaterial::getBlockHeight(int blockdata)
 {
	 if (blockdata & 8)
	 {
		 return 1;
	 }
	 else if (blockdata & 4)
	 {
		 return -0.5f;
	 }
	 return 0.5f;
 }

 void HorizontalTriangleSlabMaterial::initPhyModelData()
 {
	 if (m_mWholeFace().size() == 0 || m_mTriangleFace.size() == 0 || m_mSlantFace.size() == 0
		 || m_mHalfWholeFace().size() == 0 || m_mHalfFace().size() == 0)
	 {
		 return;
	 }
	 int triangleFaceId[4] = { 6,2,4,0 };
	 for (int i = 0; i < 2; i++)
	 {
		 for (int j = 0; j < 4; j++)
		 {
			 TrianglePhyModel model;
			 dynamic_array<dynamic_array<BlockGeomVert>*> faceList;
			 //4 vert
			 faceList.push_back(0 == i ? &m_mWholeFace()[4] : &m_mWholeFace()[5]);
			 faceList.push_back(&m_mWholeFace()[ReverseDirection(j)]);
			 faceList.push_back(&m_mTriangleFace[8 * i + triangleFaceId[j]]);
			 faceList.push_back(&m_mSlantFace[4 * i + j]);
			 faceList.push_back(&m_mTriangleFace[8 * i + triangleFaceId[j] + 1]);
			 faceList.push_back(&m_mHalfFace()[j + i * 4]);

			 int triangleCount = 0;
			 for (auto face : faceList)
			 {
				 std::vector<short> indexlist;
				 for (auto& vertdata : (*face))
				 {
					 Vector3f pos(vertdata.pos.x, vertdata.pos.y, vertdata.pos.z);
					 short index = -1;
					 for (int o = 0; o < model.verts.size(); o++)
					 {
						 if (model.verts[o] == pos)
						 {
							 index = o;
							 break;
						 }
					 }
					 if (index == -1)
					 {
						 index = static_cast<short>(model.verts.size());
						 model.verts.push_back(pos);
					 }
					 indexlist.push_back(index);
				 }
				 dynamic_array<UInt16>* posIndeices = m_dPosIndices;
				 if (indexlist.size() == 3)
				 {
					 posIndeices = m_PosTrIndices;
					 triangleCount++;
				 }
				 else if (indexlist.size() > 3)
				 {
					 triangleCount += indexlist.size() - 2;
				 }
				 for (auto& idxex : (*posIndeices))
				 {
					 model.idxs.push_back(indexlist[idxex]);
				 }
			 }
			 model.triangleCount = triangleCount;
			 m_mPhyModel.insert(make_pair(i * 4 + j, model));
		 }
	 }

	 int speicalFaceId[4] = { 4,0,4,0 };
	 for (int i = 0; i < 2; i++)//0 down 1 up
	 {
		 for (int j = 0; j < 2; j++)//0 �� 1 ͹
		 {
			 for (int k = 0; k < 4; k++)
			 {
				 int dirlist[2] = { RotateDirPos90(k), RotateDir90(k) };
				 for (int q = 0; q < 2; q++)
				 {
					 auto iter = m_mPhyModel.find(10000 + i * 1000 + j * 100 + k * 10 + dirlist[q]);
					 if (iter == m_mPhyModel.end())
					 {
						 TrianglePhyModel model;
						 dynamic_array<dynamic_array<BlockGeomVert>*> faceList;
						 faceList.push_back(0 == i ? &m_mWholeFace()[4] : &m_mWholeFace()[5]);
						 if (1 == j)
						 {
							 faceList.push_back(&m_mTriangleFace[8 * i + speicalFaceId[k] + ReverseDirection(dirlist[q])]);
						 }
						 else
						 {
							 faceList.push_back(&m_mTriangleFace[8 * i + speicalFaceId[k] + dirlist[q]]);
						 }
						 faceList.push_back(&m_mTurnSlantFace[i * 1000 + j * 100 + k * 10 + dirlist[q]]);
						 faceList.push_back(&m_mTurnSlantFace[i * 1000 + j * 100 + dirlist[q] * 10 + k]);
						 if (j == 0)
						 {
							 faceList.push_back(&m_mWholeFace()[ReverseDirection(k)]);
							 faceList.push_back(&m_mWholeFace()[ReverseDirection(dirlist[q])]);
						 }
						 else if (j == 1)
						 {
							 faceList.push_back(&m_mHalfFace()[dirlist[q] + i * 4]);
							 faceList.push_back(&m_mHalfFace()[k + i * 4]);
						 }
						 int triangleCount = 0;
						 for (auto face : faceList)
						 {
							 std::vector<short> indexlist;
							 for (auto& vertdata : (*face))
							 {
								 Vector3f pos(vertdata.pos.x, vertdata.pos.y, vertdata.pos.z);
								 short index = -1;
								 for (int o = 0; o < model.verts.size(); o++)
								 {
									 if (model.verts[o] == pos)
									 {
										 index = o;
										 break;
									 }
								 }
								 if (index == -1)
								 {
									 index = static_cast<short>(model.verts.size());
									 model.verts.push_back(pos);
								 }
								 indexlist.push_back(index);
							 }
							 dynamic_array<UInt16>* posIndeices = m_dPosIndices;
							 if (indexlist.size() == 3)
							 {
								 posIndeices = m_PosTrIndices;
								 triangleCount++;
							 }
							 else if (indexlist.size() > 3)
							 {
								 triangleCount += indexlist.size() - 2;
							 }
							 for (auto& idxex : (*posIndeices))
							 {
								 model.idxs.push_back(indexlist[idxex]);
							 }
						 }
						 model.triangleCount = triangleCount;
						 m_mPhyModel.insert(make_pair(10000 + i * 1000 + j * 100 + k * 10 + dirlist[q], model));
					 }
				 }
			 }
		 }
	 }
 }

 void HorizontalTriangleSlabMaterial::initSlantFaceVertData()
 {
	 for (int i = 0; i < 2; i++)
	 {
		 for (int j = 0; j < 4; j++)
		 {
			 int d = i * 4 + j;
			 dynamic_array<BlockGeomVert> vertices;
			 Rainbow::Vector3f normalVec = (g_DirectionCoord[j] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
			 normalVec.y /= 2.f;
			 //unsigned short dir_color = Normal2LightColor(normalVec);
			 BlockVector vertcolor;
			 vertcolor.v = 0xffffffff;
			 vertcolor.w = 0;
			 Normalize(normalVec);
			 BlockVector normal_dir = PackVertNormal(normalVec);
			 BlockGeomVert vert[4];
			 if (0 == d)
			 {
				 vert[0].pos = Rainbow::Vector4f(0, 50, 0, 0);
				 vert[1].pos = Rainbow::Vector4f(0, 50, 100, 0);
				 vert[2].pos = Rainbow::Vector4f(100, 100, 100, 0);
				 vert[3].pos = Rainbow::Vector4f(100, 100, 0, 0);

				 vert[0].uv = { 1, 1 };
				 vert[1].uv = { 0, 1 };
				 vert[2].uv = { 0, 0 };
				 vert[3].uv = { 1, 0 };
			 }
			 else if (1 == d)
			 {
				 vert[0].pos = Rainbow::Vector4f(100, 50, 0, 0);
				 vert[1].pos = Rainbow::Vector4f(0, 100, 0, 0);
				 vert[2].pos = Rainbow::Vector4f(0, 100, 100, 0);
				 vert[3].pos = Rainbow::Vector4f(100, 50, 100, 0);

				 vert[0].uv = { 0, 1 };
				 vert[1].uv = { 0, 0 };
				 vert[2].uv = { 1, 0 };
				 vert[3].uv = { 1, 1 };
			 }
			 else if (2 == d)
			 {
				 vert[0].pos = Rainbow::Vector4f(0, 50, 0, 0);
				 vert[1].pos = Rainbow::Vector4f(0, 100, 100, 0);
				 vert[2].pos = Rainbow::Vector4f(100, 100, 100, 0);
				 vert[3].pos = Rainbow::Vector4f(100, 50, 0, 0);

				 vert[0].uv = { 0, 1 };
				 vert[1].uv = { 0, 0 };
				 vert[2].uv = { 1, 0 };
				 vert[3].uv = { 1, 1 };
			 }
			 else if (3 == d)
			 {
				 vert[0].pos = Rainbow::Vector4f(0, 50, 100, 0);
				 vert[1].pos = Rainbow::Vector4f(100, 50, 100, 0);
				 vert[2].pos = Rainbow::Vector4f(100, 100, 0, 0);
				 vert[3].pos = Rainbow::Vector4f(0, 100, 0, 0);

				 vert[0].uv = { 1, 1 };
				 vert[1].uv = { 0, 1 };
				 vert[2].uv = { 0, 0 };
				 vert[3].uv = { 1, 0 };
			 }
			 else if (4 == d)
			 {
				 vert[0].pos = Rainbow::Vector4f(100, 0, 0, 0);
				 vert[1].pos = Rainbow::Vector4f(100, 0, 100, 0);
				 vert[2].pos = Rainbow::Vector4f(0, 50, 100, 0);
				 vert[3].pos = Rainbow::Vector4f(0, 50, 0, 0);

				 vert[0].uv = { 1, 1 };
				 vert[1].uv = { 0, 1 };
				 vert[2].uv = { 0, 0 };
				 vert[3].uv = { 1, 0 };
			 }
			 else if (5 == d)
			 {
				 vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
				 vert[1].pos = Rainbow::Vector4f(100, 50, 0, 0);
				 vert[2].pos = Rainbow::Vector4f(100, 50, 100, 0);
				 vert[3].pos = Rainbow::Vector4f(0, 0, 100, 0);

				 vert[0].uv = { 0, 1 };
				 vert[1].uv = { 0, 0 };
				 vert[2].uv = { 1, 0 };
				 vert[3].uv = { 1, 1 };
			 }
			 else if (6 == d)
			 {
				 vert[0].pos = Rainbow::Vector4f(0, 0, 100, 0);
				 vert[1].pos = Rainbow::Vector4f(0, 50, 0, 0);
				 vert[2].pos = Rainbow::Vector4f(100, 50, 0, 0);
				 vert[3].pos = Rainbow::Vector4f(100, 0, 100, 0);

				 vert[0].uv = { 0, 1 };
				 vert[1].uv = { 0, 0 };
				 vert[2].uv = { 1, 0 };
				 vert[3].uv = { 1, 1 };
			 }
			 else if (7 == d)
			 {
				 vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
				 vert[1].pos = Rainbow::Vector4f(100, 0, 0, 0);
				 vert[2].pos = Rainbow::Vector4f(100, 50, 100, 0);
				 vert[3].pos = Rainbow::Vector4f(0, 50, 100, 0);

				 vert[0].uv = { 1, 1 };
				 vert[1].uv = { 0, 1 };
				 vert[2].uv = { 0, 0 };
				 vert[3].uv = { 1, 0 };
			 }
			 for (int oo = 0; oo < 4; oo++)
			 {
				 vert[oo].uv = { short(vert[oo].uv.x * BLOCKUV_SCALE), short(vert[oo].uv.y * BLOCKUV_SCALE) };
				 vert[oo].normal = normal_dir;
				 vert[oo].color.SetUInt32(vertcolor.v);
				 vertices.push_back(vert[oo]);
			 }
			 m_mSlantFace.push_back(vertices);
		 }
	 }
 }

 void HorizontalTriangleSlabMaterial::initTriangleFaceVertData()
 {
	 for (int i = 0; i < 4; i++)
	 {
		 for (int j = 0; j < 4; j++)
		 {
			 int d = i * 4 + j;
			 dynamic_array<BlockGeomVert> vertices;

			 Rainbow::Vector3f normalVec = g_DirectionCoord[j].toVector3();
			 unsigned short dir_color = Normal2LightColor(normalVec);
			 BlockVector vertcolor;
			 vertcolor.v = 0xffffffff;
			 vertcolor.w = dir_color;
			 Normalize(normalVec);
			 BlockVector normal_dir = PackVertNormal(normalVec);

			 BlockGeomVert vert[4];
			 Vector2f uv[4];
			 if (0 == d)
			 {
				 vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
				 vert[1].pos = Rainbow::Vector4f(0, 0, 100, 0);
				 vert[2].pos = Rainbow::Vector4f(0, 50, 100, 0);
				 vert[3].pos = Rainbow::Vector4f(0, 100, 0, 0);

				 uv[0] = { 1.f, 1.f };
				 uv[1] = { 0.f, 1.f };
				 uv[2] = { 0.f, 0.5f };
				 uv[3] = { 1.f, 0.f };
			 }
			 else if (1 == d)
			 {
				 vert[0].pos = Rainbow::Vector4f(100, 0, 0, 0);
				 vert[1].pos = Rainbow::Vector4f(100, 100, 0, 0);
				 vert[2].pos = Rainbow::Vector4f(100, 50, 100, 0);
				 vert[3].pos = Rainbow::Vector4f(100, 0, 100, 0x7f7f);

				 uv[0] = { 0.f, 1.f };
				 uv[1] = { 0.f, 0.f };
				 uv[2] = { 1.f, 0.5f };
				 uv[3] = { 1.f, 1.f };
			 }
			 else if (2 == d)
			 {
				 vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
				 vert[1].pos = Rainbow::Vector4f(0, 100, 0, 0);
				 vert[2].pos = Rainbow::Vector4f(100, 50, 0, 0);
				 vert[3].pos = Rainbow::Vector4f(100, 0, 0, 0x7f7f);

				 uv[0] = { 0.f, 1.f };
				 uv[1] = { 0.f, 0.f };
				 uv[2] = { 1.f, 0.5f };
				 uv[3] = { 1.f, 1.f };
			 }
			 else if (3 == d)
			 {
				 vert[0].pos = Rainbow::Vector4f(0, 0, 100, 0);
				 vert[1].pos = Rainbow::Vector4f(100, 0, 100, 0);
				 vert[2].pos = Rainbow::Vector4f(100, 50, 100, 0);
				 vert[3].pos = Rainbow::Vector4f(0, 100, 100, 0);

				 uv[0] = { 1.f, 1.f };
				 uv[1] = { 0.f, 1.f };
				 uv[2] = { 0.f, 0.5f };
				 uv[3] = { 1.f, 0.f };
			 }
			 else if (4 == d)
			 {
				 vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
				 vert[1].pos = Rainbow::Vector4f(0, 0, 100, 0);
				 vert[2].pos = Rainbow::Vector4f(0, 100, 100, 0);
				 vert[3].pos = Rainbow::Vector4f(0, 50, 0, 0);

				 uv[0] = { 1.f, 1.f };
				 uv[1] = { 0.f, 1.f };
				 uv[2] = { 0.f, 0.f };
				 uv[3] = { 1.f, 0.5f };
			 }
			 else if (5 == d)
			 {
				 vert[0].pos = Rainbow::Vector4f(100, 0, 0, 0);
				 vert[1].pos = Rainbow::Vector4f(100, 50, 0, 0);
				 vert[2].pos = Rainbow::Vector4f(100, 100, 100, 0);
				 vert[3].pos = Rainbow::Vector4f(100, 0, 100, 0);

				 uv[0] = { 0.f, 1.f };
				 uv[1] = { 0.f, 0.5f };
				 uv[2] = { 1.f, 0.f };
				 uv[3] = { 1.f, 1.f };
			 }
			 else if (6 == d)
			 {
				 vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
				 vert[1].pos = Rainbow::Vector4f(0, 50, 0, 0);
				 vert[2].pos = Rainbow::Vector4f(100, 100, 0, 0);
				 vert[3].pos = Rainbow::Vector4f(100, 0, 0, 0);

				 uv[0] = { 0.f, 1.f };
				 uv[1] = { 0.f, 0.5f };
				 uv[2] = { 1.f, 0.f };
				 uv[3] = { 1.f, 1.f };
			 }
			 else if (7 == d)
			 {
				 vert[0].pos = Rainbow::Vector4f(0, 0, 100, 0);
				 vert[1].pos = Rainbow::Vector4f(100, 0, 100, 0);
				 vert[2].pos = Rainbow::Vector4f(100, 100, 100, 0);
				 vert[3].pos = Rainbow::Vector4f(0, 50, 100, 0);

				 uv[0] = { 1.f, 1.f };
				 uv[1] = { 0.f, 1.f };
				 uv[2] = { 0.f, 0.f };
				 uv[3] = { 1.f, 0.5f };
			 }
			 else if (8 == d)
			 {
				 vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
				 vert[1].pos = Rainbow::Vector4f(0, 50, 100, 0);
				 vert[2].pos = Rainbow::Vector4f(0, 100, 100, 0);
				 vert[3].pos = Rainbow::Vector4f(0, 100, 0, 0);

				 uv[0] = { 1.f, 1.f };
				 uv[1] = { 0.f, 0.5f };
				 uv[2] = { 0.f, 0.f };
				 uv[3] = { 1.f, 0.f };
			 }
			 else if (9 == d)
			 {
				 vert[0].pos = Rainbow::Vector4f(100, 0, 0, 0);
				 vert[1].pos = Rainbow::Vector4f(100, 100, 0, 0);
				 vert[2].pos = Rainbow::Vector4f(100, 100, 100, 0);
				 vert[3].pos = Rainbow::Vector4f(100, 50, 100, 0);

				 uv[0] = { 0.f, 1.f };
				 uv[1] = { 0.f, 0.f };
				 uv[2] = { 1.f, 0.f };
				 uv[3] = { 1.f, 0.5f };
			 }
			 else if (10 == d)
			 {
				 vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
				 vert[1].pos = Rainbow::Vector4f(0, 100, 0, 0);
				 vert[2].pos = Rainbow::Vector4f(100, 100, 0, 0);
				 vert[3].pos = Rainbow::Vector4f(100, 50, 0, 0);

				 uv[0] = { 0.f, 1.f };
				 uv[1] = { 0.f, 0.f };
				 uv[2] = { 1.f, 0.f };
				 uv[3] = { 1.f, 0.5f };
			 }
			 else if (11 == d)
			 {
				 vert[0].pos = Rainbow::Vector4f(0, 0, 100, 0);
				 vert[1].pos = Rainbow::Vector4f(100, 50, 100, 0);
				 vert[2].pos = Rainbow::Vector4f(100, 100, 100, 0);
				 vert[3].pos = Rainbow::Vector4f(0, 100, 100, 0);

				 uv[0] = { 1.f, 1.f };
				 uv[1] = { 0.f, 0.5f };
				 uv[2] = { 0.f, 0.f };
				 uv[3] = { 1.f, 0.f };
			 }
			 else if (12 == d)
			 {
				 vert[0].pos = Rainbow::Vector4f(0, 50, 0, 0);
				 vert[1].pos = Rainbow::Vector4f(0, 0, 100, 0);
				 vert[2].pos = Rainbow::Vector4f(0, 100, 100, 0);
				 vert[3].pos = Rainbow::Vector4f(0, 100, 0, 0x7f7f);

				 uv[0] = { 1.f, 0.5f };
				 uv[1] = { 0.f, 1.f };
				 uv[2] = { 0.f, 0.f };
				 uv[3] = { 1.f, 0.f };
			 }
			 else if (13 == d)
			 {
				 vert[0].pos = Rainbow::Vector4f(100, 50, 0, 0);
				 vert[1].pos = Rainbow::Vector4f(100, 100, 0, 0);
				 vert[2].pos = Rainbow::Vector4f(100, 100, 100, 0);
				 vert[3].pos = Rainbow::Vector4f(100, 0, 100, 0);

				 uv[0] = { 0.f, 0.5f };
				 uv[1] = { 0.f, 0.f };
				 uv[2] = { 1.f, 0.f };
				 uv[3] = { 1.f, 1.f };
			 }
			 else if (14 == d)
			 {
				 vert[0].pos = Rainbow::Vector4f(0, 50, 0, 0);
				 vert[1].pos = Rainbow::Vector4f(0, 100, 0, 0);
				 vert[2].pos = Rainbow::Vector4f(100, 100, 0, 0);
				 vert[3].pos = Rainbow::Vector4f(100, 0, 0, 0);

				 uv[0] = { 0.f, 0.5f };
				 uv[1] = { 0.f, 0.f };
				 uv[2] = { 1.f, 0.f };
				 uv[3] = { 1.f, 1.f };
			 }
			 else if (15 == d)
			 {
				 vert[0].pos = Rainbow::Vector4f(0, 50, 100, 0);
				 vert[1].pos = Rainbow::Vector4f(100, 0, 100, 0);
				 vert[2].pos = Rainbow::Vector4f(100, 100, 100, 0);
				 vert[3].pos = Rainbow::Vector4f(0, 100, 100, 0);

				 uv[0] = { 1.f, 0.5f };
				 uv[1] = { 0.f, 1.f };
				 uv[2] = { 0.f, 0.f };
				 uv[3] = { 1.f, 0.f };
			 }
			 for (int oo = 0; oo < 4; oo++)
			 {
				 vert[oo].uv = { short(uv[oo].x * BLOCKUV_SCALE), short(uv[oo].y * BLOCKUV_SCALE) };
				 vert[oo].normal = normal_dir;
				 vert[oo].color.SetUInt32(vertcolor.v);
				 vertices.push_back(vert[oo]);
			 }
			 m_mTriangleFace.push_back(vertices);
		 }
	 }
	 for (int i = 0; i < 4; i++)
	 {
		 for (int j = 0; j < 4; j++)
		 {
			 int d = i * 4 + j;
			 dynamic_array<BlockGeomVert> vertices;

			 Rainbow::Vector3f normalVec = g_DirectionCoord[j].toVector3();
			 //unsigned short dir_color = Normal2LightColor(normalVec);
			 BlockVector vertcolor;
			 vertcolor.v = 0xffffffff;
			 vertcolor.w = 0;
			 Normalize(normalVec);
			 BlockVector normal_dir = PackVertNormal(normalVec);

			 BlockGeomVert vert[4];
			 Vector2f uv[4];
			 if (0 == d)
			 {
				 vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
				 vert[1].pos = Rainbow::Vector4f(0, 0, 100, 0);
				 vert[2].pos = Rainbow::Vector4f(0, 50, 100, 0);
				 vert[3].pos = Rainbow::Vector4f(0, 100, 0, 0);

				 uv[0] = { 0.f, 1.f };
				 uv[1] = { 1.f, 1.f };
				 uv[2] = { 1.f, 0.5f };
				 uv[3] = { 0.f, 0.f };
			 }
			 else if (1 == d)
			 {
				 vert[0].pos = Rainbow::Vector4f(100, 0, 0, 0);
				 vert[1].pos = Rainbow::Vector4f(100, 100, 0, 0);
				 vert[2].pos = Rainbow::Vector4f(100, 50, 100, 0);
				 vert[3].pos = Rainbow::Vector4f(100, 0, 100, 0x7f7f);

				 uv[0] = { 0.f, 1.f };
				 uv[1] = { 0.f, 0.f };
				 uv[2] = { 1.f, 0.5f };
				 uv[3] = { 1.f, 1.f };
			 }
			 else if (2 == d)
			 {
				 vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
				 vert[1].pos = Rainbow::Vector4f(0, 100, 0, 0);
				 vert[2].pos = Rainbow::Vector4f(100, 50, 0, 0);
				 vert[3].pos = Rainbow::Vector4f(100, 0, 0, 0x7f7f);

				 uv[0] = { 0.f, 1.f };
				 uv[1] = { 0.f, 0.f };
				 uv[2] = { 1.f, 0.5f };
				 uv[3] = { 1.f, 1.f };
			 }
			 else if (3 == d)
			 {
				 vert[0].pos = Rainbow::Vector4f(0, 0, 100, 0);
				 vert[1].pos = Rainbow::Vector4f(100, 0, 100, 0);
				 vert[2].pos = Rainbow::Vector4f(100, 50, 100, 0);
				 vert[3].pos = Rainbow::Vector4f(0, 100, 100, 0);

				 uv[0] = { 0.f, 1.f };
				 uv[1] = { 1.f, 1.f };
				 uv[2] = { 1.f, 0.5f };
				 uv[3] = { 0.f, 0.f };
			 }
			 else if (4 == d)
			 {
				 vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
				 vert[1].pos = Rainbow::Vector4f(0, 0, 100, 0);
				 vert[2].pos = Rainbow::Vector4f(0, 100, 100, 0);
				 vert[3].pos = Rainbow::Vector4f(0, 50, 0, 0);

				 uv[0] = { 1.f, 1.f };
				 uv[1] = { 0.f, 1.f };
				 uv[2] = { 0.f, 0.f };
				 uv[3] = { 1.f, 0.5f };
			 }
			 else if (5 == d)
			 {
				 vert[0].pos = Rainbow::Vector4f(100, 0, 0, 0);
				 vert[1].pos = Rainbow::Vector4f(100, 50, 0, 0);
				 vert[2].pos = Rainbow::Vector4f(100, 100, 100, 0);
				 vert[3].pos = Rainbow::Vector4f(100, 0, 100, 0);

				 uv[0] = { 1.f, 1.f };
				 uv[1] = { 1.f, 0.5f };
				 uv[2] = { 0.f, 0.f };
				 uv[3] = { 0.f, 1.f };
			 }
			 else if (6 == d)
			 {
				 vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
				 vert[1].pos = Rainbow::Vector4f(0, 50, 0, 0);
				 vert[2].pos = Rainbow::Vector4f(100, 100, 0, 0);
				 vert[3].pos = Rainbow::Vector4f(100, 0, 0, 0);

				 uv[0] = { 1.f, 1.f };
				 uv[1] = { 1.f, 0.5f };
				 uv[2] = { 0.f, 0.f };
				 uv[3] = { 0.f, 1.f };
			 }
			 else if (7 == d)
			 {
				 vert[0].pos = Rainbow::Vector4f(0, 0, 100, 0);
				 vert[1].pos = Rainbow::Vector4f(100, 0, 100, 0);
				 vert[2].pos = Rainbow::Vector4f(100, 100, 100, 0);
				 vert[3].pos = Rainbow::Vector4f(0, 50, 100, 0);

				 uv[0] = { 1.f, 1.f };
				 uv[1] = { 0.f, 1.f };
				 uv[2] = { 0.f, 0.f };
				 uv[3] = { 1.f, 0.5f };
			 }
			 else if (8 == d)
			 {
				 vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
				 vert[1].pos = Rainbow::Vector4f(0, 50, 100, 0);
				 vert[2].pos = Rainbow::Vector4f(0, 100, 100, 0);
				 vert[3].pos = Rainbow::Vector4f(0, 100, 0, 0);

				 uv[0] = { 1.f, 1.f };
				 uv[1] = { 0.f, 0.5f };
				 uv[2] = { 0.f, 0.f };
				 uv[3] = { 1.f, 0.f };
			 }
			 else if (9 == d)
			 {
				 vert[0].pos = Rainbow::Vector4f(100, 0, 0, 0);
				 vert[1].pos = Rainbow::Vector4f(100, 100, 0, 0);
				 vert[2].pos = Rainbow::Vector4f(100, 100, 100, 0);
				 vert[3].pos = Rainbow::Vector4f(100, 50, 100, 0);

				 uv[0] = { 0.f, 1.f };
				 uv[1] = { 0.f, 0.f };
				 uv[2] = { 1.f, 0.f };
				 uv[3] = { 1.f, 0.5f };
			 }
			 else if (10 == d)
			 {
				 vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
				 vert[1].pos = Rainbow::Vector4f(0, 100, 0, 0);
				 vert[2].pos = Rainbow::Vector4f(100, 100, 0, 0);
				 vert[3].pos = Rainbow::Vector4f(100, 50, 0, 0);

				 uv[0] = { 0.f, 1.f };
				 uv[1] = { 0.f, 0.f };
				 uv[2] = { 1.f, 0.f };
				 uv[3] = { 1.f, 0.5f };
			 }
			 else if (11 == d)
			 {
				 vert[0].pos = Rainbow::Vector4f(0, 0, 100, 0);
				 vert[1].pos = Rainbow::Vector4f(100, 50, 100, 0);
				 vert[2].pos = Rainbow::Vector4f(100, 100, 100, 0);
				 vert[3].pos = Rainbow::Vector4f(0, 100, 100, 0);

				 uv[0] = { 1.f, 1.f };
				 uv[1] = { 0.f, 0.5f };
				 uv[2] = { 0.f, 0.f };
				 uv[3] = { 1.f, 0.f };
			 }
			 else if (12 == d)
			 {
				 vert[0].pos = Rainbow::Vector4f(0, 50, 0, 0);
				 vert[1].pos = Rainbow::Vector4f(0, 0, 100, 0);
				 vert[2].pos = Rainbow::Vector4f(0, 100, 100, 0);
				 vert[3].pos = Rainbow::Vector4f(0, 100, 0, 0x7f7f);

				 uv[0] = { 1.f, 0.5f };
				 uv[1] = { 0.f, 1.f };
				 uv[2] = { 0.f, 0.f };
				 uv[3] = { 1.f, 0.f };
			 }
			 else if (13 == d)
			 {
				 vert[0].pos = Rainbow::Vector4f(100, 50, 0, 0);
				 vert[1].pos = Rainbow::Vector4f(100, 100, 0, 0);
				 vert[2].pos = Rainbow::Vector4f(100, 100, 100, 0);
				 vert[3].pos = Rainbow::Vector4f(100, 0, 100, 0);

				 uv[0] = { 0.f, 0.5f };
				 uv[1] = { 0.f, 0.f };
				 uv[2] = { 1.f, 0.f };
				 uv[3] = { 1.f, 1.f };
			 }
			 else if (14 == d)
			 {
				 vert[0].pos = Rainbow::Vector4f(0, 50, 0, 0);
				 vert[1].pos = Rainbow::Vector4f(0, 100, 0, 0);
				 vert[2].pos = Rainbow::Vector4f(100, 100, 0, 0);
				 vert[3].pos = Rainbow::Vector4f(100, 0, 0, 0);

				 uv[0] = { 0.f, 0.5f };
				 uv[1] = { 0.f, 0.f };
				 uv[2] = { 1.f, 0.f };
				 uv[3] = { 1.f, 1.f };
			 }
			 else if (15 == d)
			 {
				 vert[0].pos = Rainbow::Vector4f(0, 50, 100, 0);
				 vert[1].pos = Rainbow::Vector4f(100, 0, 100, 0);
				 vert[2].pos = Rainbow::Vector4f(100, 100, 100, 0);
				 vert[3].pos = Rainbow::Vector4f(0, 100, 100, 0);

				 uv[0] = { 1.f, 0.5f };
				 uv[1] = { 0.f, 1.f };
				 uv[2] = { 0.f, 0.f };
				 uv[3] = { 1.f, 0.f };
			 }
			 for (int oo = 0; oo < 4; oo++)
			 {
				 vert[oo].uv = { short(uv[oo].x * BLOCKUV_SCALE), short(uv[oo].y * BLOCKUV_SCALE) };
				 vert[oo].normal = normal_dir;
				 vert[oo].color.SetUInt32(vertcolor.v);
				 vertices.push_back(vert[oo]);
			 }
			 m_mTriangleFace_uv.push_back(vertices);
		 }
	 }
 }

 void HorizontalTriangleSlabMaterial::initTurnSlantFaceVertData()
 {
	 for (int i = 0; i < 2; i++)//updown 0down
	 {
		 for (int j = 0; j < 2; j++) //0�ǰ�1��͹
		 {
			 for (int k = 0; k < 4; k++)//4��������
			 {
				 DirectionType dir = (DirectionType)j;

				 if (0 == k)
				 {
					 if (0 == i)
					 {
						 if (1 == j)
						 {
							 for (int o = 2; o <= 3; o++)
							 {
								 dynamic_array<BlockGeomVert> vertices;
								 BlockGeomVert vert[3];
								 if (o == 2)
								 {
									 vert[0].pos = Rainbow::Vector4f(0, 50, 0, 0);
									 vert[1].pos = Rainbow::Vector4f(0, 50, 100, 0);
									 vert[2].pos = Rainbow::Vector4f(100, 100, 100, 0);
									 vert[0].uv = { 1, 1 };
									 vert[1].uv = { 0, 1 };
									 vert[2].uv = { 0, 0 };
								 }
								 else
								 {
									 vert[0].pos = Rainbow::Vector4f(0, 50, 0, 0);
									 vert[1].pos = Rainbow::Vector4f(0, 50, 100, 0);
									 vert[2].pos = Rainbow::Vector4f(100, 100, 0, 0);
									 vert[0].uv = { 1, 1 };
									 vert[1].uv = { 0, 1 };
									 vert[2].uv = { 1, 0 };
								 }

								 Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
								 //unsigned short dir_color = Normal2LightColor(normalVec);
								 BlockVector vertcolor;
								 vertcolor.v = 0xffffffff;
								 vertcolor.w = 0;
								 Normalize(normalVec);
								 BlockVector normal_dir = PackVertNormal(normalVec);
								 for (int oo = 0; oo < 3; oo++)
								 {
									 vert[oo].uv = { short(vert[oo].uv.x * BLOCKUV_SCALE), short(vert[oo].uv.y * BLOCKUV_SCALE) };
									 //vert[oo].uv = { short(vert[oo].uv.x * BLOCKUV_SCALE), i >= 2 ? short(vert[oo].uv.y * 0.5f * BLOCKUV_SCALE) : short((1.f - (1.f - vert[oo].uv.y) * 0.5f) * BLOCKUV_SCALE) };
									 vert[oo].normal = normal_dir;
									 vert[oo].color.SetUInt32(vertcolor.v);
									 vertices.push_back(vert[oo]);
								 }

								 m_mTurnSlantFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							 }
						 }
						 else
						 {
							 for (int o = 2; o <= 3; o++)
							 {
								 dynamic_array<BlockGeomVert> vertices;
								 BlockGeomVert vert[3];
								 if (o == 2)
								 {
									 vert[0].pos = Rainbow::Vector4f(0, 50, 0, 0);
									 vert[1].pos = Rainbow::Vector4f(100, 100, 100, 0);
									 vert[2].pos = Rainbow::Vector4f(100, 100, 0, 0);

									 vert[0].uv = { 1, 1 };
									 vert[1].uv = { 0, 0 };
									 vert[2].uv = { 1, 0 };
								 }
								 else
								 {
									 vert[0].pos = Rainbow::Vector4f(0, 50, 100, 0);
									 vert[1].pos = Rainbow::Vector4f(100, 100, 100, 0);
									 vert[2].pos = Rainbow::Vector4f(100, 100, 0, 0);

									 vert[0].uv = { 0, 1 };
									 vert[1].uv = { 0, 0 };
									 vert[2].uv = { 1, 0 };
								 }

								 Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
								 //unsigned short dir_color = Normal2LightColor(normalVec);
								 BlockVector vertcolor;
								 vertcolor.v = 0xffffffff;
								 vertcolor.w = 0;
								 Normalize(normalVec);
								 BlockVector normal_dir = PackVertNormal(normalVec);
								 for (int oo = 0; oo < 3; oo++)
								 {
									 vert[oo].uv = { short(vert[oo].uv.x * BLOCKUV_SCALE), short(vert[oo].uv.y * BLOCKUV_SCALE) };
									 //vert[oo].uv = { short(vert[oo].uv.x * BLOCKUV_SCALE), i >= 2 ? short(vert[oo].uv.y * 0.5f * BLOCKUV_SCALE) : short((1.f - (1.f - vert[oo].uv.y) * 0.5f) * BLOCKUV_SCALE) };
									 vert[oo].normal = normal_dir;
									 vert[oo].color.SetUInt32(vertcolor.v);
									 vertices.push_back(vert[oo]);
								 }
								 m_mTurnSlantFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							 }
						 }
					 }
					 else
					 {
						 if (1 == j)
						 {
							 for (int o = 2; o <= 3; o++)
							 {
								 dynamic_array<BlockGeomVert> vertices;
								 BlockGeomVert vert[3];
								 if (o == 2)
								 {
									 vert[0].pos = Rainbow::Vector4f(100, 0, 100, 0);
									 vert[1].pos = Rainbow::Vector4f(0, 50, 100, 0);
									 vert[2].pos = Rainbow::Vector4f(0, 50, 0, 0);
									 vert[0].uv = { 0, 1 };
									 vert[1].uv = { 0, 0 };
									 vert[2].uv = { 1, 0 };
								 }
								 else
								 {
									 vert[0].pos = Rainbow::Vector4f(100, 0, 0, 0);
									 vert[1].pos = Rainbow::Vector4f(0, 50, 100, 0);
									 vert[2].pos = Rainbow::Vector4f(0, 50, 0, 0);
									 vert[0].uv = { 1, 1 };
									 vert[1].uv = { 0, 0 };
									 vert[2].uv = { 1, 0 };
								 }

								 Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
								 //unsigned short dir_color = Normal2LightColor(normalVec);
								 BlockVector vertcolor;
								 vertcolor.v = 0xffffffff;
								 vertcolor.w = 0;
								 Normalize(normalVec);
								 BlockVector normal_dir = PackVertNormal(normalVec);
								 for (int oo = 0; oo < 3; oo++)
								 {
									 vert[oo].uv = { short(vert[oo].uv.x * BLOCKUV_SCALE), short(vert[oo].uv.y * BLOCKUV_SCALE) };
									 //vert[oo].uv = { short(vert[oo].uv.x * BLOCKUV_SCALE), i >= 2 ? short(vert[oo].uv.y * 0.5f * BLOCKUV_SCALE) : short((1.f - (1.f - vert[oo].uv.y) * 0.5f) * BLOCKUV_SCALE) };
									 vert[oo].normal = normal_dir;
									 vert[oo].color.SetUInt32(vertcolor.v);
									 vertices.push_back(vert[oo]);
								 }
								 m_mTurnSlantFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							 }
						 }
						 else
						 {
							 for (int o = 2; o <= 3; o++)
							 {
								 dynamic_array<BlockGeomVert> vertices;
								 BlockGeomVert vert[3];
								 if (o == 2)
								 {
									 vert[0].pos = Rainbow::Vector4f(100, 0, 0, 0);
									 vert[1].pos = Rainbow::Vector4f(100, 0, 100, 0);
									 vert[2].pos = Rainbow::Vector4f(0, 50, 0, 0);
									 vert[0].uv = { 1, 1 };
									 vert[1].uv = { 0, 1 };
									 vert[2].uv = { 1, 0 };
								 }
								 else
								 {
									 vert[0].pos = Rainbow::Vector4f(100, 0, 0, 0);
									 vert[1].pos = Rainbow::Vector4f(100, 0, 100, 0);
									 vert[2].pos = Rainbow::Vector4f(0, 50, 100, 0);
									 vert[0].uv = { 1, 1 };
									 vert[1].uv = { 0, 1 };
									 vert[2].uv = { 0, 0 };
								 }

								 Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
								 //unsigned short dir_color = Normal2LightColor(normalVec);
								 BlockVector vertcolor;
								 vertcolor.v = 0xffffffff;
								 vertcolor.w = 0;
								 Normalize(normalVec);
								 BlockVector normal_dir = PackVertNormal(normalVec);
								 for (int oo = 0; oo < 3; oo++)
								 {
									 vert[oo].uv = { short(vert[oo].uv.x * BLOCKUV_SCALE),short(vert[oo].uv.y * BLOCKUV_SCALE) };
									 //vert[oo].uv = { short(vert[oo].uv.x * BLOCKUV_SCALE), i >= 2 ? short(vert[oo].uv.y * 0.5f * BLOCKUV_SCALE) : short((1.f - (1.f - vert[oo].uv.y) * 0.5f) * BLOCKUV_SCALE) };
									 vert[oo].normal = normal_dir;
									 vert[oo].color.SetUInt32(vertcolor.v);
									 vertices.push_back(vert[oo]);
								 }
								 m_mTurnSlantFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							 }
						 }
					 }
				 }
				 else if (1 == k)
				 {
					 if (0 == i)
					 {
						 if (1 == j)
						 {
							 for (int o = 2; o <= 3; o++)
							 {
								 dynamic_array<BlockGeomVert> vertices;
								 BlockGeomVert vert[3];
								 if (o == 2)
								 {
									 vert[0].pos = Rainbow::Vector4f(100, 50, 0, 0);
									 vert[1].pos = Rainbow::Vector4f(0, 100, 100, 0);
									 vert[2].pos = Rainbow::Vector4f(100, 50, 100, 0);
									 vert[0].uv = { 0, 1 };
									 vert[1].uv = { 1, 0 };
									 vert[2].uv = { 1, 1 };
								 }
								 else
								 {
									 vert[0].pos = Rainbow::Vector4f(100, 50, 0, 0);
									 vert[1].pos = Rainbow::Vector4f(0, 100, 0, 0);
									 vert[2].pos = Rainbow::Vector4f(100, 50, 100, 0);
									 vert[0].uv = { 0, 1 };
									 vert[1].uv = { 0, 0 };
									 vert[2].uv = { 1, 1 };
								 }

								 Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
								 //unsigned short dir_color = Normal2LightColor(normalVec);
								 BlockVector vertcolor;
								 vertcolor.v = 0xffffffff;
								 vertcolor.w = 0;
								 Normalize(normalVec);
								 BlockVector normal_dir = PackVertNormal(normalVec);
								 for (int oo = 0; oo < 3; oo++)
								 {
									 vert[oo].uv = { short(vert[oo].uv.x * BLOCKUV_SCALE), short(vert[oo].uv.y * BLOCKUV_SCALE) };
									 //vert[oo].uv = { short(vert[oo].uv.x * BLOCKUV_SCALE), i >= 2 ? short(vert[oo].uv.y * 0.5f * BLOCKUV_SCALE) : short((1.f - (1.f - vert[oo].uv.y) * 0.5f) * BLOCKUV_SCALE) };
									 vert[oo].normal = normal_dir;
									 vert[oo].color.SetUInt32(vertcolor.v);
									 vertices.push_back(vert[oo]);
								 }
								 m_mTurnSlantFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							 }
						 }
						 else
						 {
							 for (int o = 2; o <= 3; o++)
							 {
								 dynamic_array<BlockGeomVert> vertices;
								 BlockGeomVert vert[3];
								 if (o == 2)
								 {
									 vert[0].pos = Rainbow::Vector4f(100, 50, 0, 0);
									 vert[1].pos = Rainbow::Vector4f(0, 100, 0, 0);
									 vert[2].pos = Rainbow::Vector4f(0, 100, 100, 0);
									 vert[0].uv = { 0, 1 };
									 vert[1].uv = { 0, 0 };
									 vert[2].uv = { 1, 0 };
								 }
								 else
								 {
									 vert[0].pos = Rainbow::Vector4f(0, 100, 0, 0);
									 vert[1].pos = Rainbow::Vector4f(0, 100, 100, 0);
									 vert[2].pos = Rainbow::Vector4f(100, 50, 100, 0);
									 vert[0].uv = { 0, 0 };
									 vert[1].uv = { 1, 0 };
									 vert[2].uv = { 1, 1 };
								 }
								 Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
								 //unsigned short dir_color = Normal2LightColor(normalVec);
								 BlockVector vertcolor;
								 vertcolor.v = 0xffffffff;
								 vertcolor.w = 0;
								 Normalize(normalVec);
								 BlockVector normal_dir = PackVertNormal(normalVec);
								 for (int oo = 0; oo < 3; oo++)
								 {
									 vert[oo].uv = { short(vert[oo].uv.x * BLOCKUV_SCALE), short(vert[oo].uv.y * BLOCKUV_SCALE) };
									 //vert[oo].uv = { short(vert[oo].uv.x * BLOCKUV_SCALE), i >= 2 ? short(vert[oo].uv.y * 0.5f * BLOCKUV_SCALE) : short((1.f - (1.f - vert[oo].uv.y) * 0.5f) * BLOCKUV_SCALE) };
									 vert[oo].normal = normal_dir;
									 vert[oo].color.SetUInt32(vertcolor.v);
									 vertices.push_back(vert[oo]);
								 }
								 m_mTurnSlantFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							 }
						 }
					 }
					 else
					 {
						 if (1 == j)
						 {
							 for (int o = 2; o <= 3; o++)
							 {
								 dynamic_array<BlockGeomVert> vertices;
								 BlockGeomVert vert[3];
								 if (o == 2)
								 {
									 vert[0].pos = Rainbow::Vector4f(100, 50, 0, 0);
									 vert[1].pos = Rainbow::Vector4f(100, 50, 100, 0);
									 vert[2].pos = Rainbow::Vector4f(0, 0, 100, 0);
									 vert[0].uv = { 0, 0 };
									 vert[1].uv = { 1, 0 };
									 vert[2].uv = { 1, 1 };
								 }
								 else
								 {
									 vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
									 vert[1].pos = Rainbow::Vector4f(100, 50, 0, 0);
									 vert[2].pos = Rainbow::Vector4f(100, 50, 100, 0);
									 vert[0].uv = { 0, 1 };
									 vert[1].uv = { 0, 0 };
									 vert[2].uv = { 1, 0 };
								 }

								 Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
								 //unsigned short dir_color = Normal2LightColor(normalVec);
								 BlockVector vertcolor;
								 vertcolor.v = 0xffffffff;
								 vertcolor.w = 0;
								 Normalize(normalVec);
								 BlockVector normal_dir = PackVertNormal(normalVec);
								 for (int oo = 0; oo < 3; oo++)
								 {
									 vert[oo].uv = { short(vert[oo].uv.x * BLOCKUV_SCALE), short(vert[oo].uv.y * BLOCKUV_SCALE) };
									 //vert[oo].uv = { short(vert[oo].uv.x * BLOCKUV_SCALE), i >= 2 ? short(vert[oo].uv.y * 0.5f * BLOCKUV_SCALE) : short((1.f - (1.f - vert[oo].uv.y) * 0.5f) * BLOCKUV_SCALE) };
									 vert[oo].normal = normal_dir;
									 vert[oo].color.SetUInt32(vertcolor.v);
									 vertices.push_back(vert[oo]);
								 }
								 m_mTurnSlantFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							 }
						 }
						 else
						 {
							 for (int o = 2; o <= 3; o++)
							 {
								 dynamic_array<BlockGeomVert> vertices;
								 BlockGeomVert vert[3];
								 if (o == 2)
								 {
									 vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
									 vert[1].pos = Rainbow::Vector4f(100, 50, 0, 0);
									 vert[2].pos = Rainbow::Vector4f(0, 0, 100, 0);
									 vert[0].uv = { 0, 1 };
									 vert[1].uv = { 0, 0 };
									 vert[2].uv = { 1, 1 };
								 }
								 else
								 {
									 vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
									 vert[1].pos = Rainbow::Vector4f(100, 50, 100, 0);
									 vert[2].pos = Rainbow::Vector4f(0, 0, 100, 0);
									 vert[0].uv = { 0, 1 };
									 vert[1].uv = { 1, 0 };
									 vert[2].uv = { 1, 1 };
								 }

								 Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
								 //unsigned short dir_color = Normal2LightColor(normalVec);
								 BlockVector vertcolor;
								 vertcolor.v = 0xffffffff;
								 vertcolor.w = 0;
								 Normalize(normalVec);
								 BlockVector normal_dir = PackVertNormal(normalVec);
								 for (int oo = 0; oo < 3; oo++)
								 {
									 vert[oo].uv = { short(vert[oo].uv.x * BLOCKUV_SCALE), short(vert[oo].uv.y * BLOCKUV_SCALE) };
									 //vert[oo].uv = { short(vert[oo].uv.x * BLOCKUV_SCALE), i >= 2 ? short(vert[oo].uv.y * 0.5f * BLOCKUV_SCALE) : short((1.f - (1.f - vert[oo].uv.y) * 0.5f) * BLOCKUV_SCALE) };
									 vert[oo].normal = normal_dir;
									 vert[oo].color.SetUInt32(vertcolor.v);
									 vertices.push_back(vert[oo]);
								 }
								 m_mTurnSlantFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							 }
						 }
					 }
				 }
				 else if (2 == k)
				 {
					 if (0 == i)
					 {
						 if (1 == j)
						 {
							 for (int o = 0; o <= 1; o++)
							 {
								 dynamic_array<BlockGeomVert> vertices;
								 BlockGeomVert vert[3];
								 if (o == 0)
								 {
									 vert[0].pos = Rainbow::Vector4f(0, 50, 0, 0);
									 vert[1].pos = Rainbow::Vector4f(100, 100, 100, 0);
									 vert[2].pos = Rainbow::Vector4f(100, 50, 0, 0);
									 vert[0].uv = { 0, 1 };
									 vert[1].uv = { 1, 0 };
									 vert[2].uv = { 1, 1 };
								 }
								 else
								 {
									 vert[0].pos = Rainbow::Vector4f(0, 50, 0, 0);
									 vert[1].pos = Rainbow::Vector4f(0, 100, 100, 0);
									 vert[2].pos = Rainbow::Vector4f(100, 50, 0, 0);
									 vert[0].uv = { 0, 1 };
									 vert[1].uv = { 0, 0 };
									 vert[2].uv = { 1, 1 };
								 }

								 Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
								 //unsigned short dir_color = Normal2LightColor(normalVec);
								 BlockVector vertcolor;
								 vertcolor.v = 0xffffffff;
								 vertcolor.w = 0;
								 Normalize(normalVec);
								 BlockVector normal_dir = PackVertNormal(normalVec);
								 for (int oo = 0; oo < 3; oo++)
								 {
									 vert[oo].uv = { short(vert[oo].uv.x * BLOCKUV_SCALE), short(vert[oo].uv.y * BLOCKUV_SCALE) };
									 //vert[oo].uv = { short(vert[oo].uv.x * BLOCKUV_SCALE), i >= 2 ? short(vert[oo].uv.y * 0.5f * BLOCKUV_SCALE) : short((1.f - (1.f - vert[oo].uv.y) * 0.5f) * BLOCKUV_SCALE) };
									 vert[oo].normal = normal_dir;
									 vert[oo].color.SetUInt32(vertcolor.v);
									 vertices.push_back(vert[oo]);
								 }
								 m_mTurnSlantFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							 }
						 }
						 else
						 {
							 for (int o = 0; o <= 1; o++)
							 {
								 dynamic_array<BlockGeomVert> vertices;
								 BlockGeomVert vert[3];
								 if (o == 0)
								 {
									 vert[0].pos = Rainbow::Vector4f(0, 50, 0, 0);
									 vert[1].pos = Rainbow::Vector4f(0, 100, 100, 0);
									 vert[2].pos = Rainbow::Vector4f(100, 100, 100, 0);
									 vert[0].uv = { 0, 1 };
									 vert[1].uv = { 0, 0 };
									 vert[2].uv = { 1, 0 };
								 }
								 else
								 {
									 vert[0].pos = Rainbow::Vector4f(0, 100, 100, 0);
									 vert[1].pos = Rainbow::Vector4f(100, 100, 100, 0);
									 vert[2].pos = Rainbow::Vector4f(100, 50, 0, 0);
									 vert[0].uv = { 0, 0 };
									 vert[1].uv = { 1, 0 };
									 vert[2].uv = { 1, 1 };
								 }

								 Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
								 //unsigned short dir_color = Normal2LightColor(normalVec);
								 BlockVector vertcolor;
								 vertcolor.v = 0xffffffff;
								 vertcolor.w = 0;
								 Normalize(normalVec);
								 BlockVector normal_dir = PackVertNormal(normalVec);
								 for (int oo = 0; oo < 3; oo++)
								 {
									 vert[oo].uv = { short(vert[oo].uv.x * BLOCKUV_SCALE), short(vert[oo].uv.y * BLOCKUV_SCALE) };
									 //vert[oo].uv = { short(vert[oo].uv.x * BLOCKUV_SCALE), i >= 2 ? short(vert[oo].uv.y * 0.5f * BLOCKUV_SCALE) : short((1.f - (1.f - vert[oo].uv.y) * 0.5f) * BLOCKUV_SCALE) };
									 vert[oo].normal = normal_dir;
									 vert[oo].color.SetUInt32(vertcolor.v);
									 vertices.push_back(vert[oo]);
								 }
								 m_mTurnSlantFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							 }
						 }
					 }
					 else
					 {
						 if (1 == j)
						 {
							 for (int o = 0; o <= 1; o++)
							 {
								 dynamic_array<BlockGeomVert> vertices;
								 BlockGeomVert vert[3];
								 if (o == 0)
								 {
									 vert[0].pos = Rainbow::Vector4f(0, 50, 0, 0);
									 vert[1].pos = Rainbow::Vector4f(100, 50, 0, 0);
									 vert[2].pos = Rainbow::Vector4f(100, 0, 100, 0);
									 vert[0].uv = { 0, 0 };
									 vert[1].uv = { 1, 0 };
									 vert[2].uv = { 1, 1 };
								 }
								 else
								 {
									 vert[0].pos = Rainbow::Vector4f(0, 0, 100, 0);
									 vert[1].pos = Rainbow::Vector4f(0, 50, 0, 0);
									 vert[2].pos = Rainbow::Vector4f(100, 50, 0, 0);
									 vert[0].uv = { 0, 1 };
									 vert[1].uv = { 0, 0 };
									 vert[2].uv = { 1, 0 };
								 }

								 Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
								 //unsigned short dir_color = Normal2LightColor(normalVec);
								 BlockVector vertcolor;
								 vertcolor.v = 0xffffffff;
								 vertcolor.w = 0;
								 Normalize(normalVec);
								 BlockVector normal_dir = PackVertNormal(normalVec);
								 for (int oo = 0; oo < 3; oo++)
								 {
									 vert[oo].uv = { short(vert[oo].uv.x * BLOCKUV_SCALE), short(vert[oo].uv.y * BLOCKUV_SCALE) };
									 //vert[oo].uv = { short(vert[oo].uv.x * BLOCKUV_SCALE), i >= 2 ? short(vert[oo].uv.y * 0.5f * BLOCKUV_SCALE) : short((1.f - (1.f - vert[oo].uv.y) * 0.5f) * BLOCKUV_SCALE) };
									 vert[oo].normal = normal_dir;
									 vert[oo].color.SetUInt32(vertcolor.v);
									 vertices.push_back(vert[oo]);
								 }
								 m_mTurnSlantFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							 }
						 }
						 else
						 {
							 for (int o = 0; o <= 1; o++)
							 {
								 dynamic_array<BlockGeomVert> vertices;
								 BlockGeomVert vert[3];
								 if (o == 0)
								 {
									 vert[0].pos = Rainbow::Vector4f(0, 0, 100, 0);
									 vert[1].pos = Rainbow::Vector4f(0, 50, 0, 0);
									 vert[2].pos = Rainbow::Vector4f(100, 0, 100, 0);
									 vert[0].uv = { 0, 1 };
									 vert[1].uv = { 1, 0 };
									 vert[2].uv = { 1, 1 };
								 }
								 else
								 {
									 vert[0].pos = Rainbow::Vector4f(0, 0, 100, 0);
									 vert[1].pos = Rainbow::Vector4f(100, 50, 0, 0);
									 vert[2].pos = Rainbow::Vector4f(100, 0, 100, 0);
									 vert[0].uv = { 0, 1 };
									 vert[1].uv = { 0, 0 };
									 vert[2].uv = { 1, 1 };
								 }

								 Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
								 //unsigned short dir_color = Normal2LightColor(normalVec);
								 BlockVector vertcolor;
								 vertcolor.v = 0xffffffff;
								 vertcolor.w = 0;
								 Normalize(normalVec);
								 BlockVector normal_dir = PackVertNormal(normalVec);
								 for (int oo = 0; oo < 3; oo++)
								 {
									 vert[oo].uv = { short(vert[oo].uv.x * BLOCKUV_SCALE), short(vert[oo].uv.y * BLOCKUV_SCALE) };
									 //vert[oo].uv = { short(vert[oo].uv.x * BLOCKUV_SCALE), i >= 2 ? short(vert[oo].uv.y * 0.5f * BLOCKUV_SCALE) : short((1.f - (1.f - vert[oo].uv.y) * 0.5f) * BLOCKUV_SCALE) };
									 vert[oo].normal = normal_dir;
									 vert[oo].color.SetUInt32(vertcolor.v);
									 vertices.push_back(vert[oo]);
								 }
								 m_mTurnSlantFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							 }
						 }
					 }
				 }
				 else if (3 == k)
				 {
					 if (0 == i)
					 {
						 if (1 == j)
						 {
							 for (int o = 0; o <= 1; o++)
							 {
								 dynamic_array<BlockGeomVert> vertices;
								 BlockGeomVert vert[3];
								 if (o == 0)
								 {
									 vert[0].pos = Rainbow::Vector4f(0, 50, 100, 0);
									 vert[1].pos = Rainbow::Vector4f(100, 50, 100, 0);
									 vert[2].pos = Rainbow::Vector4f(100, 100, 0, 0);
									 vert[0].uv = { 1, 1 };
									 vert[1].uv = { 0, 1 };
									 vert[2].uv = { 0, 0 };
								 }
								 else
								 {
									 vert[0].pos = Rainbow::Vector4f(0, 50, 100, 0);
									 vert[1].pos = Rainbow::Vector4f(100, 50, 100, 0);
									 vert[2].pos = Rainbow::Vector4f(0, 100, 0, 0);
									 vert[0].uv = { 1, 1 };
									 vert[1].uv = { 0, 1 };
									 vert[2].uv = { 1, 0 };
								 }

								 Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
								 //unsigned short dir_color = Normal2LightColor(normalVec);
								 BlockVector vertcolor;
								 vertcolor.v = 0xffffffff;
								 vertcolor.w = 0;
								 Normalize(normalVec);
								 BlockVector normal_dir = PackVertNormal(normalVec);
								 for (int oo = 0; oo < 3; oo++)
								 {
									 vert[oo].uv = { short(vert[oo].uv.x * BLOCKUV_SCALE), short(vert[oo].uv.y * BLOCKUV_SCALE) };
									 //vert[oo].uv = { short(vert[oo].uv.x * BLOCKUV_SCALE), i >= 2 ? short(vert[oo].uv.y * 0.5f * BLOCKUV_SCALE) : short((1.f - (1.f - vert[oo].uv.y) * 0.5f) * BLOCKUV_SCALE) };
									 vert[oo].normal = normal_dir;
									 vert[oo].color.SetUInt32(vertcolor.v);
									 vertices.push_back(vert[oo]);
								 }
								 m_mTurnSlantFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							 }
						 }
						 else
						 {
							 for (int o = 0; o <= 1; o++)
							 {
								 dynamic_array<BlockGeomVert> vertices;
								 BlockGeomVert vert[3];
								 if (o == 0)
								 {
									 vert[0].pos = Rainbow::Vector4f(0, 50, 100, 0);
									 vert[1].pos = Rainbow::Vector4f(100, 100, 0, 0);
									 vert[2].pos = Rainbow::Vector4f(0, 100, 0, 0);
									 vert[0].uv = { 0, 1 };
									 vert[1].uv = { 0, 0 };
									 vert[2].uv = { 1, 0 };
								 }
								 else
								 {
									 vert[0].pos = Rainbow::Vector4f(100, 50, 100, 0);
									 vert[1].pos = Rainbow::Vector4f(100, 100, 0, 0);
									 vert[2].pos = Rainbow::Vector4f(0, 100, 0, 0);
									 vert[0].uv = { 0, 1 };
									 vert[1].uv = { 0, 0 };
									 vert[2].uv = { 1, 0 };
								 }

								 Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
								 //unsigned short dir_color = Normal2LightColor(normalVec);
								 BlockVector vertcolor;
								 vertcolor.v = 0xffffffff;
								 vertcolor.w = 0;
								 Normalize(normalVec);
								 BlockVector normal_dir = PackVertNormal(normalVec);
								 for (int oo = 0; oo < 3; oo++)
								 {
									 vert[oo].uv = { short(vert[oo].uv.x * BLOCKUV_SCALE), short(vert[oo].uv.y * BLOCKUV_SCALE) };
									 //vert[oo].uv = { short(vert[oo].uv.x * BLOCKUV_SCALE), i >= 2 ? short(vert[oo].uv.y * 0.5f * BLOCKUV_SCALE) : short((1.f - (1.f - vert[oo].uv.y) * 0.5f) * BLOCKUV_SCALE) };
									 vert[oo].normal = normal_dir;
									 vert[oo].color.SetUInt32(vertcolor.v);
									 vertices.push_back(vert[oo]);
								 }
								 m_mTurnSlantFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							 }
						 }
					 }
					 else
					 {
						 if (1 == j)
						 {
							 for (int o = 0; o <= 1; o++)
							 {
								 dynamic_array<BlockGeomVert> vertices;
								 BlockGeomVert vert[3];
								 if (o == 0)
								 {
									 vert[0].pos = Rainbow::Vector4f(100, 0, 0, 0);
									 vert[1].pos = Rainbow::Vector4f(100, 50, 100, 0);
									 vert[2].pos = Rainbow::Vector4f(0, 50, 100, 0);
									 vert[0].uv = { 0, 1 };
									 vert[1].uv = { 0, 0 };
									 vert[2].uv = { 1, 0 };
								 }
								 else
								 {
									 vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
									 vert[1].pos = Rainbow::Vector4f(100, 50, 100, 0);
									 vert[2].pos = Rainbow::Vector4f(0, 50, 100, 0);
									 vert[0].uv = { 1, 1 };
									 vert[1].uv = { 0, 0 };
									 vert[2].uv = { 1, 0 };
								 }

								 Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
								 //unsigned short dir_color = Normal2LightColor(normalVec);
								 BlockVector vertcolor;
								 vertcolor.v = 0xffffffff;
								 vertcolor.w = 0;
								 Normalize(normalVec);
								 BlockVector normal_dir = PackVertNormal(normalVec);
								 for (int oo = 0; oo < 3; oo++)
								 {
									 vert[oo].uv = { short(vert[oo].uv.x * BLOCKUV_SCALE), short(vert[oo].uv.y * BLOCKUV_SCALE) };
									 //vert[oo].uv = { short(vert[oo].uv.x * BLOCKUV_SCALE), i >= 2 ? short(vert[oo].uv.y * 0.5f * BLOCKUV_SCALE) : short((1.f - (1.f - vert[oo].uv.y) * 0.5f) * BLOCKUV_SCALE) };
									 vert[oo].normal = normal_dir;
									 vert[oo].color.SetUInt32(vertcolor.v);
									 vertices.push_back(vert[oo]);
								 }
								 m_mTurnSlantFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							 }
						 }
						 else
						 {
							 for (int o = 0; o <= 1; o++)
							 {
								 dynamic_array<BlockGeomVert> vertices;
								 BlockGeomVert vert[3];
								 if (o == 0)
								 {
									 vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
									 vert[1].pos = Rainbow::Vector4f(100, 0, 0, 0);
									 vert[2].pos = Rainbow::Vector4f(0, 50, 100, 0);
									 vert[0].uv = { 1, 1 };
									 vert[1].uv = { 0, 1 };
									 vert[2].uv = { 1, 0 };
								 }
								 else
								 {
									 vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
									 vert[1].pos = Rainbow::Vector4f(100, 0, 0, 0);
									 vert[2].pos = Rainbow::Vector4f(100, 50, 100, 0);

									 vert[0].uv = { 1, 1 };
									 vert[1].uv = { 0, 1 };
									 vert[2].uv = { 0, 0 };
								 }

								 Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
								 //unsigned short dir_color = Normal2LightColor(normalVec);
								 BlockVector vertcolor;
								 vertcolor.v = 0xffffffff;
								 vertcolor.w = 0;
								 Normalize(normalVec);
								 BlockVector normal_dir = PackVertNormal(normalVec);
								 for (int oo = 0; oo < 3; oo++)
								 {
									 vert[oo].uv = { short(vert[oo].uv.x * BLOCKUV_SCALE), short(vert[oo].uv.y * BLOCKUV_SCALE) };
									 //vert[oo].uv = { short(vert[oo].uv.x * BLOCKUV_SCALE), i >= 2 ? short(vert[oo].uv.y * 0.5f * BLOCKUV_SCALE) : short((1.f - (1.f - vert[oo].uv.y) * 0.5f) * BLOCKUV_SCALE) };
									 vert[oo].normal = normal_dir;
									 vert[oo].color.SetUInt32(vertcolor.v);
									 vertices.push_back(vert[oo]);
								 }
								 m_mTurnSlantFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							 }
						 }
					 }
				 }
			 }
		 }
	 }
 }

 void HorizontalTriangleSlabMaterial::dropBlockAsItem(World* pworld, const WCoord& blockpos, int blockdata, BLOCK_MINE_TYPE droptype, float chance, int uin/* = -1 */)
 {
	 auto block = pworld->getBlock(blockpos);
	 for (int i = 0; i < 3; i++) 
	 {
		 CubeBlockMaterial::dropBlockAsItem(pworld, blockpos, blockdata, droptype, chance, uin);
	 }
	 if (blockdata == 15)
	 {
		 CubeBlockMaterial::dropBlockAsItem(pworld, blockpos, blockdata, droptype, chance, uin);
	 }
 }

 int HorizontalTriangleSlabMaterial::getPhisicMeshVerts(BaseSection* psection, const WCoord& blockpos, dynamic_array<Rainbow::Vector3f>& verts, dynamic_array<UInt16>& idxs)
 {
#ifdef IWORLD_SERVER_BUILD
	 if (!m_mPhyModel.size())
	 {
		 initVertData();
	 }
#endif
	 int blockdata = psection->getBlock(blockpos).getData();
	 if (blockdata < 8)
	 {
		 auto pblock = psection->getBlock(blockpos);
		 int warp = -1;
		 int turnDir = -1;
		 DirectionType curDir = DirectionType(blockdata & 3);
		 auto frontBlock = psection->getNeighborBlock(blockpos, g_DirectionCoord[curDir]);
		 if (pblock.getResID() == frontBlock.getResID() && frontBlock.getData() < 8 && ((pblock.getData() & 4) == (frontBlock.getData() & 4)))
		 {
			 int frontDir = frontBlock.getData() & 3;
			 if (frontDir != curDir && frontDir != ReverseDirection(curDir))
			 {
				 warp = 0;
				 turnDir = frontDir;
			 }
		 }
		 if (warp == -1)
		 {
			 auto backBlock = psection->getNeighborBlock(blockpos, g_DirectionCoord[ReverseDirection(curDir)]);
			 if (pblock.getResID() == backBlock.getResID() && backBlock.getData() < 8 && ((pblock.getData() & 4) == (backBlock.getData() & 4)))
			 {
				 int backDir = backBlock.getData() & 3;
				 if (backDir != curDir && backDir != ReverseDirection(curDir))
				 {
					 warp = 1;
					 turnDir = backDir;
				 }
			 }
		 }
		 if (warp == -1)
		 {
			 if (m_mPhyModel.find(blockdata) != m_mPhyModel.end())
			 {
				 TrianglePhyModel* pTag = &m_mPhyModel[blockdata];
				 verts = pTag->verts;
				 idxs = pTag->idxs;
				 return  pTag->triangleCount;
			 }
		 }
		 else
		 {
			 int downUp = (blockdata & 8) ? 1 : 0;
			 if (m_mPhyModel.find(10000 + downUp * 1000 + warp * 100 + curDir * 10 + turnDir) != m_mPhyModel.end())
			 {
				 TrianglePhyModel* pTag = &m_mPhyModel[10000 + downUp * 1000 + warp * 100 + curDir * 10 + turnDir];
				 verts = pTag->verts;
				 idxs = pTag->idxs;
				 return  pTag->triangleCount;
			 }
		 }
	 }
	 return 0;
 }

 SectionMesh* HorizontalTriangleSlabMaterial::createBlockProtoMesh(int protodata)
 {
	 SectionMesh* pmesh = ENG_NEW(SectionMesh)();
	 int blockdata = protodata;

	 const BiomeDef* biome = GetDefManagerProxy()->getBiomeDef(0);

	 char list[6] = { 2,4,3,4,5,2};
	 BlockColor facecolor(255, 255, 255, 0);
	 for (int i = 0; i < 6; i++)
	 {
		 DirectionType dir = (DirectionType)(list[i] % 4);
		 RenderBlockMaterial* pmtl = getFaceMtl(biome, dir, blockdata, facecolor);
		 SectionSubMesh* psubmesh = pmesh->getSubMesh(pmtl, true);
		 dynamic_array<BlockGeomVert> vertices;

		 BlockGeomMeshInfo meshinfo;
		 if (i == 0)
		 {
			 meshinfo.vertices = m_mHalfFace()[list[i]];
			 meshinfo.indices = *m_dPosIndices;
		 }
		 else if (i <= 2 )
		 {
			 meshinfo.vertices = m_mWholeFace()[list[i]];
			 meshinfo.indices = *m_dPosIndices;
		 }
		 else if (i <= 4)
		 {
			 meshinfo.vertices = m_mTriangleFace[list[i]];
			 meshinfo.indices = *m_dPosIndices;
		 }
		 else
		 {
			 meshinfo.vertices = m_mSlantFace[list[i]];
			 meshinfo.indices = *m_dPosIndices;
		 }
		 if (psubmesh)
			 psubmesh->addGeomFaceLight(meshinfo, NULL, s_DefaultFaceVertexLights, &facecolor);
	 }
	 return pmesh;
 }

 void HorizontalTriangleSlabMaterial::createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh)
 {
	 auto psection = data.m_SharedSectionData;
#ifndef IWORLD_SERVER_BUILD
	 FaceVertexLight faceVertexLight;
	 Block pblock = psection->getBlock(blockpos);

	 int curblockdata = pblock.getData();
	 int curDir = curblockdata & 3;

	 float blockheight = getBlockHeight(curblockdata);
	 DirectionType specialdir = DIR_NOT_INIT;

	 if (blockheight > 0 && blockheight < 1.0f) specialdir = DIR_POS_Y;
	 else if (blockheight<0 && blockheight>-1.0f) specialdir = DIR_NEG_Y;

	 const BiomeDef* biome = GetDefManagerProxy()->getBiomeDef(1);

	 std::vector<int> wholeFace;
	 std::vector<int> halfWholeFace;
	 std::vector<int> halfFace;
	 std::vector<int> triangleFace;
	 std::vector<int> slantFace;
	 dynamic_array<int> turnslantFace;
	 if (curblockdata & 8)
	 {
		 //CubeBlockMaterial::createBlockMesh(data, blockpos, poutmesh);
		 createBlockMeshAngleSnow(data, blockpos, poutmesh);
		 return;
	 }
	 else
	 {
		 int warp = -1;
		 int turnDir = -1;
		 auto frontBlock = psection->getNeighborBlock(blockpos, g_DirectionCoord[curDir]);
		 if (pblock.getResID() == frontBlock.getResID() && frontBlock.getData() < 8 && ((pblock.getData() & 4) == (frontBlock.getData() & 4)))
		 {
			 int frontDir = frontBlock.getData() & 3;
			 if (frontDir != curDir && frontDir != ReverseDirection(curDir))
			 {
				 warp = 0;
				 turnDir = frontDir;
			 }
		 }

		 if (warp == -1)
		 {
			 auto backBlock = psection->getNeighborBlock(blockpos, g_DirectionCoord[ReverseDirection(curDir)]);
			 if (pblock.getResID() == backBlock.getResID() && backBlock.getData() < 8 && ((pblock.getData() & 4) == (backBlock.getData() & 4)))
			 {
				 int backDir = backBlock.getData() & 3;
				 if (backDir != curDir && backDir != ReverseDirection(curDir))
				 {
					 warp = 1;
					 turnDir = backDir;
				 }
			 }
		 }
		 int add = 0;
		 if (specialdir == DIR_POS_Y)
		 {
			 wholeFace.push_back(4);
		 }
		 else
		 {
			 wholeFace.push_back(5);
			 add = 4;
		 }
		 if (warp == -1)
		 {
			 wholeFace.push_back(ReverseDirection(curDir));
		 }

		 if (curDir == DIR_NEG_X)
		 {
			 if (specialdir == DIR_POS_Y)
			 {
				 if (warp == -1)
				 {
					 triangleFace.push_back(6);
					 triangleFace.push_back(7);
					 slantFace.push_back(0);
				 }
				 else
				 {
					 if (1 == warp)
					 {
						 triangleFace.push_back(ReverseDirection(turnDir) + 4);//4+3   7  6
						 triangleFace.push_back((ReverseDirection(turnDir)%2)*4+1);//  5  1
					 }
					 else
					 {
						 triangleFace.push_back(turnDir + 4);//4+3
					 }
					 turnslantFace.push_back(warp * 100 + curDir * 10 + turnDir);
					 turnslantFace.push_back(warp * 100 + turnDir * 10 + curDir);
				 }
			 }
			 else
			 {
				 if (warp == -1)
				 {
					 triangleFace.push_back(14);//3*4+2
					 triangleFace.push_back(15);//3*4+3
					 slantFace.push_back(4);
				 }
				 else
				 {
					 if (1 == warp)
					 {
						 triangleFace.push_back(ReverseDirection(turnDir) + 12);//3*4+3
					 }
					 else
					 {
						 triangleFace.push_back(turnDir + 12);//3*4+3
					 }
					 turnslantFace.push_back(1000 + warp * 100 + curDir * 10 + turnDir);
					 turnslantFace.push_back(1000 + warp * 100 + turnDir * 10 + curDir);
				 }
			 }
		 }
		 else if (curDir == DIR_POS_X)
		 {
			 if (specialdir == DIR_POS_Y)
			 {
				 if (warp == -1)
				 {
					 triangleFace.push_back(2);
					 triangleFace.push_back(3);
					 slantFace.push_back(1);
				 }
				 else
				 {
					 if (1 == warp)
					 {
						 triangleFace.push_back(ReverseDirection(turnDir)); //==3   2
						 triangleFace.push_back((ReverseDirection(turnDir)%2)*4);//==4   0
					 }
					 else
					 {
						 triangleFace.push_back(turnDir);
					 }
					 turnslantFace.push_back(warp * 100 + curDir * 10 + turnDir);
					 turnslantFace.push_back(warp * 100 + turnDir * 10 + curDir);
				 }
			 }
			 else
			 {
				 if (warp == -1)
				 {
					 triangleFace.push_back(10);//2*4+2
					 triangleFace.push_back(11);//2*4+3
					 slantFace.push_back(5);
				 }
				 else
				 {
					 if (1 == warp)
					 {
						 triangleFace.push_back(ReverseDirection(turnDir) + 8);//2*4+3
					 }
					 else
					 {
						 triangleFace.push_back(turnDir + 8);//2*4+3
					 }
					 turnslantFace.push_back(1000 + warp * 100 + curDir * 10 + turnDir);
					 turnslantFace.push_back(1000 + warp * 100 + turnDir * 10 + curDir);
				 }
			 }
		 }
		 else if (curDir == DIR_NEG_Z)
		 {
			 if (specialdir == DIR_POS_Y)
			 {
				 if (warp == -1)
				 {
					 triangleFace.push_back(4);
					 triangleFace.push_back(5);
					 slantFace.push_back(2);
				 }
				 else
				 {
					 if (1 == warp)
					 {
						 triangleFace.push_back(ReverseDirection(turnDir) + 4);//==4
						 triangleFace.push_back(ReverseDirection(turnDir)*4 + 3);//==3
					 }
					 else
					 {
						 triangleFace.push_back(turnDir + 4);
					 }
					 turnslantFace.push_back(warp * 100 + curDir * 10 + turnDir);
					 turnslantFace.push_back(warp * 100 + turnDir * 10 + curDir);
				 }
			 }
			 else
			 {
				 if (warp == -1)
				 {
					 triangleFace.push_back(12);//3*4+0
					 triangleFace.push_back(13);//3*4+1
					 slantFace.push_back(6);
				 }
				 else
				 {
					 if (1 == warp)
					 {
						 triangleFace.push_back(ReverseDirection(turnDir) + 12);//3*4+1
					 }
					 else
					 {
						 triangleFace.push_back(turnDir + 12);//3*4+1
					 }
					 turnslantFace.push_back(1000 + warp * 100 + curDir * 10 + turnDir);
					 turnslantFace.push_back(1000 + warp * 100 + turnDir * 10 + curDir);
				 }
			 }
		 }
		 else if (curDir == DIR_POS_Z)
		 {
			 if (specialdir == DIR_POS_Y)
			 {
				 if (warp == -1)
				 {
					 triangleFace.push_back(0);
					 triangleFace.push_back(1);
					 slantFace.push_back(3);
				 }
				 else
				 {
					 if (1 == warp)
					 {
						 triangleFace.push_back(ReverseDirection(turnDir)); //ReverseDirection(turnDir)=1
						 triangleFace.push_back(ReverseDirection(turnDir)*4+2);
					 }
					 else
					 {
						 triangleFace.push_back(turnDir);
					 }
					 turnslantFace.push_back(warp * 100 + curDir * 10 + turnDir);
					 turnslantFace.push_back(warp * 100 + turnDir * 10 + curDir);
				 }
			 }
			 else
			 {
				 if (warp == -1)
				 {
					 triangleFace.push_back(8);//2*4+0
					 triangleFace.push_back(9);//2*4+1
					 slantFace.push_back(7);
				 }
				 else
				 {
					 if (1 == warp)
					 {
						 triangleFace.push_back(ReverseDirection(turnDir) + 8);//2*4+1
					 }
					 else
					 {
						 triangleFace.push_back(turnDir + 8);//2*4+1
					 }
					 turnslantFace.push_back(1000 + warp * 100 + curDir * 10 + turnDir);
					 turnslantFace.push_back(1000 + warp * 100 + turnDir * 10 + curDir);
				 }
			 }
		 }
		 if (warp == 0)
		 {
			 wholeFace.push_back(ReverseDirection(curDir));
			 wholeFace.push_back(ReverseDirection(turnDir));
// 			 halfFace.push_back(turnDir + add);
		 }
		 else if (warp == 1)
		 {
			 halfFace.push_back(turnDir + add);
			 halfFace.push_back(curDir + add);
		 }
		 else
		 {
			 halfFace.push_back(curDir + add);
// 			 for (int ii = 0; ii < 4; ii++)
// 			 {
// 				 if (ReverseDirection(curDir) != ii)
// 				 {
// 					 halfFace.push_back(ii + add);
// 				 }
// 			 }
		 }
	 }
	 BlockColor facecolor(255, 255, 255, 0);
	 int weather = 0;
	 if (data.m_World && data.m_World->getWeatherMgr())
	 {
		 weather = data.m_World->getWeatherMgr()->getWeather(blockpos);
	 }
	 bool isFaceUp = false;
	 bool isSnowing = false;//��ѩlock
	 if (weather == GROUP_BLIZZARD_WEATHER || weather == GROUP_SNOW_WEATHER)
	 {
		 isSnowing = true;
		 WCoord blockpos_tmp = blockpos + psection->getOrigin();
		 int y = data.m_World->getTopHeight(blockpos_tmp.x, blockpos_tmp.z);
		 if ((y - 1) != blockpos_tmp.y)
		 {
			 isSnowing = false;
		 }
	 }
	 for (auto& d : wholeFace)
	 {
		 DirectionType dir = (DirectionType)d;
		 // 		if (m_DisableCoverFaceOpt || dir == specialdir || !psection->getNeighborCover(blockpos, this, curblockdata, dir))
		 {
			 bool flipQuad = psection->getCubeFaceVertexLight(blockpos, dir, faceVertexLight);

			 dynamic_array<UInt16>* indices = /*flipQuad ? &m_dNegIndices : */m_dPosIndices;

			 RenderBlockMaterial* pmtl = NULL;
			 if (isSnowing == true)
			 {
				 pmtl = snowSideMtl;
				 if (dir == DIR_POS_Y)
				 {
					 pmtl = snowTopMtl;
					 isFaceUp = true;
				 }
				 else if (dir == DIR_NEG_Y)
				 {
					 pmtl = getFaceMtl(biome, dir, pblock.getData(), facecolor);
				 }
			 }
			 if (pmtl == NULL) pmtl = getFaceMtl(biome, dir, pblock.getData(), facecolor);
			 if (pmtl == NULL)
				 continue;
			 SectionSubMesh* psubmesh = poutmesh->getSubMesh(pmtl);
			 const float* uvtile = nullptr;
			 if (psubmesh && !psubmesh->IsIgnoreTileUV())
				 uvtile = pmtl->getUVTile();
			 BlockGeomMeshInfo mesh;

			 mesh.vertices = m_mWholeFace()[d];
			 mesh.indices = *indices;
			 //unsigned short dir_color1 = TriangleNormal2LightColor(g_DirectionCoord[dir].toVector3());
			 //unsigned short dir_color2 = dir_color1;
			 //if (dir != DIR_NEG_Y)
			 //{
				// dir_color2  = TriangleNormal2LightColor(g_DirectionCoord[specialdir].toVector3());
			 //}
			 //BlockColor vertcolor;
			 ////vertcolor.v = 0xffffffff;
			 //vertcolor.a = (dir_color1+ dir_color2*2)/3;
			 unsigned int avelt[4];
			 getAvelt(data, blockpos, dir, avelt);
			 for (int n = 0; n < m_mWholeFace()[d].size(); n++)
			 {
				 auto& vert = mesh.vertices[n];

				 //int lt1 = (((255 >> 4) & 0xf) * vertcolor.a) >> 5;
				 //int lt2 = (((255 >> 20) & 0xf) * vertcolor.a) >> 5;
				 //vert.pos.w = (lt1 << 8) | lt2;
				 //vert.color = vertcolor;
				 InitBlockVertLight(vert, avelt[n], uvtile);
			 }
			 //if (psubmesh) psubmesh->addGeomFace(mesh, &blockpos);//addGeomFaceLight(mesh, &blockpos, faceVertexLight, &facecolor, pmtl->getUVTile());
			 if (psubmesh) psubmesh->addGeomFaceLight(mesh, &blockpos, faceVertexLight, &facecolor, pmtl->getUVTile());
		 }
	 }
	 for (auto& d : halfWholeFace)
	 {
		 DirectionType dir = (DirectionType)d;
		 // 		if (m_DisableCoverFaceOpt || dir == specialdir || !psection->getNeighborCover(blockpos, this, curblockdata, dir))
		 {
			 bool flipQuad = psection->getCubeFaceVertexLight(blockpos, dir, faceVertexLight);

			 dynamic_array<UInt16>* indices = m_dPosIndices;

			 RenderBlockMaterial* pmtl = NULL;
			 if (isSnowing == true)
			 {
				 pmtl = snowSideMtl;
				 if (dir == DIR_POS_Y)
				 {
					 pmtl = snowTopMtl;
					 isFaceUp = true;
				 }
				 else if (dir == DIR_NEG_Y)
				 {
					 pmtl = getFaceMtl(biome, dir, pblock.getData(), facecolor);
				 }
			 }
			 if (pmtl == NULL) pmtl = getFaceMtl(biome, dir, pblock.getData(), facecolor);
			 if (pmtl == NULL)
				 continue;
			 SectionSubMesh* psubmesh = poutmesh->getSubMesh(pmtl);
			 const float* uvtile = nullptr;
			 if (psubmesh && !psubmesh->IsIgnoreTileUV())
				 uvtile = pmtl->getUVTile();
			 BlockGeomMeshInfo mesh;

			 mesh.vertices = m_mHalfWholeFace()[d];
			 mesh.indices = *indices;
			 ////////unsigned short dir_color = TriangleNormal2LightColor(g_DirectionCoord[dir].toVector3());
			 //BlockColor vertcolor;
			 ////vertcolor.v = 0xffffffff;
			 //vertcolor.a = dir_color;
			 unsigned int avelt[4];
			 getAvelt(data, blockpos, dir, avelt);
			 for (int n = 0; n < m_mHalfWholeFace()[d].size(); n++)
			 {
				 auto& vert = mesh.vertices[n];
				 //int lt1 = (((255 >> 4) & 0xf) * vertcolor.a) >> 5;
				 //int lt2 = (((255 >> 20) & 0xf) * vertcolor.a) >> 5;
				 //vert.pos.w = (lt1 << 8) | lt2;
				 //vert.color = vertcolor;
				 InitBlockVertLight(vert, avelt[n], uvtile);
			 }
			 //if (psubmesh) psubmesh->addGeomFace(mesh, &blockpos); //addGeomFaceLight(mesh, &blockpos, faceVertexLight, &facecolor, pmtl->getUVTile());
			 if (psubmesh) psubmesh->addGeomFaceLight(mesh, &blockpos, faceVertexLight, &facecolor, pmtl->getUVTile());
		 }
	 }
	 for (auto& d : halfFace)
	 {
		 DirectionType dir = (DirectionType)(d % 4);
		 // 		if (m_DisableCoverFaceOpt || dir == specialdir || !psection->getNeighborCover(blockpos, this, curblockdata, dir))
		 {
			 bool flipQuad = psection->getCubeFaceVertexLight(blockpos, dir, faceVertexLight);
			 dynamic_array<UInt16>* indices = m_dPosIndices;

			 RenderBlockMaterial* pmtl = NULL;
			 if (isSnowing == true)
			 {
				 pmtl = snowSideMtl;
				 if (dir == DIR_POS_Y)
				 {
					 pmtl = snowTopMtl;
					 isFaceUp = true;
				 }
				 else if (dir == DIR_NEG_Y)
				 {
					 pmtl = getFaceMtl(biome, dir, pblock.getData(), facecolor);
				 }
			 }
			 if (pmtl == NULL) pmtl = getFaceMtl(biome, dir, pblock.getData(), facecolor);
			 if (pmtl == NULL)
				 continue;
			 SectionSubMesh* psubmesh = poutmesh->getSubMesh(pmtl);
			 const float* uvtile = nullptr;
			 if (psubmesh && !psubmesh->IsIgnoreTileUV())
				 uvtile = pmtl->getUVTile();
			 BlockGeomMeshInfo mesh;

			 mesh.vertices = m_mHalfFace()[d];
			 if (isSnowing == true)
			 {
				 if (d < 4)
				 {
					 if (d == 1 || d == 2)
					 {
						 mesh.vertices[0].uv.x = 0; mesh.vertices[0].uv.y = 2048;
						 mesh.vertices[1].uv.x = 0; mesh.vertices[1].uv.y = 0;
						 mesh.vertices[2].uv.x = 4096; mesh.vertices[2].uv.y = 0;
						 mesh.vertices[3].uv.x = 4096; mesh.vertices[3].uv.y = 2048;
					 }
					 else
					 {
						 mesh.vertices[0].uv.x = 4096; mesh.vertices[0].uv.y = 2048;
						 mesh.vertices[1].uv.x = 0; mesh.vertices[1].uv.y = 2048;
						 mesh.vertices[2].uv.x = 0; mesh.vertices[2].uv.y = 0;
						 mesh.vertices[3].uv.x = 4096; mesh.vertices[3].uv.y = 0;
					 }
				 }
			 }
			 mesh.indices = *indices;
			 //////unsigned short dir_color = TriangleNormal2LightColor(g_DirectionCoord[dir].toVector3());
			 //BlockColor vertcolor;
			 ////vertcolor.v = 0xffffffff;
			 //vertcolor.a = dir_color;
			 unsigned int avelt[4];
			 getAvelt(data, blockpos, dir, avelt);
			 for (int n = 0; n < m_mHalfFace()[d].size(); n++)
			 {
				 auto& vert = mesh.vertices[n];
				 //int lt1 = (((255 >> 4) & 0xf) * vertcolor.a) >> 5;
				 //int lt2 = (((255 >> 20) & 0xf) * vertcolor.a) >> 5;
				 //vert.pos.w = (lt1 << 8) | lt2;
				 //vert.color = vertcolor;
				 InitBlockVertLight(vert, avelt[n], uvtile);
			 }
			 //if (psubmesh) psubmesh->addGeomFace(mesh, &blockpos); //addGeomFaceLight(mesh, &blockpos, faceVertexLight, &facecolor, pmtl->getUVTile());
			 if (psubmesh) psubmesh->addGeomFaceLight(mesh, &blockpos, faceVertexLight, &facecolor, pmtl->getUVTile());
		 }
	 }
	 for (auto& d : triangleFace)
	 {
		 DirectionType dir = (DirectionType)(d % 4);
		 bool flipQuad = psection->getCubeFaceVertexLight(blockpos, dir, faceVertexLight);
		 dynamic_array<UInt16>* indices = m_dPosIndices;
		 
		 RenderBlockMaterial* pmtl = NULL;
		 if (isSnowing == true)
		 {
			 pmtl = snowDownTrapezoidMtl;
			 if (isFaceUp == true)
			 {
				 pmtl = snowSideMtl;
			 }
		 }
		 if (pmtl == NULL) pmtl = getFaceMtl(biome, dir, pblock.getData(), facecolor);
		 if (pmtl == NULL)
			 continue;
		 SectionSubMesh* psubmesh = poutmesh->getSubMesh(pmtl);
		 const float* uvtile = nullptr;
		 if (psubmesh && !psubmesh->IsIgnoreTileUV())
			 uvtile = pmtl->getUVTile();
		 BlockGeomMeshInfo mesh;
		 if (isSnowing == true)
		 {
			 mesh.vertices = m_mTriangleFace_uv[d];
		 }
		 else
		 {
			 mesh.vertices = m_mTriangleFace[d];
		 }
		 mesh.indices = *indices;
		 unsigned short dir_color = TriangleNormal2LightColor(g_DirectionCoord[dir].toVector3());
		 BlockColor vertcolor;
		 //vertcolor.v = 0xffffffff;
		 vertcolor.a = dir_color;
		 unsigned int avelt[4];
		 getAvelt(data, blockpos, dir, avelt);
		 for (int n = 0; n < m_mTriangleFace[d].size(); n++)
		 {
			 auto& vert = mesh.vertices[n];
			 /*int lt1 = (((255 >> 4) & 0xf) * vertcolor.a) >> 5;
			 int lt2 = (((255 >> 20) & 0xf) * vertcolor.a) >> 5;
			 vert.pos.w = (lt1 << 8) | lt2;
			 vert.color = vertcolor;*/
			 InitBlockVertLight(vert, avelt[n], uvtile);
		 }
		 //if (psubmesh) psubmesh->addGeomFace(mesh, &blockpos); //addGeomFaceLight(mesh, &blockpos, faceVertexLight, &facecolor, pmtl->getUVTile());
		 if (psubmesh) psubmesh->addGeomFaceLight(mesh, &blockpos, faceVertexLight, &facecolor, pmtl->getUVTile());
	 }
	 for (auto& d : turnslantFace)
	 {
		 DirectionType dir = (DirectionType)((d / 10) % 10);
		 FaceVertexLight faceUpDownVertexLight;
		 bool flipQuad = psection->getFaceVertexLight(blockpos, dir, faceVertexLight);
		 psection->getFaceVertexLight(blockpos, specialdir, faceUpDownVertexLight);

		 //auto dirBlock = psection->getNeighborBlock(blockpos, dir);
		 //if (IsHalfTriangleBlock(dirBlock.getResID()))
		 //{
			// auto mtl = GetDefManagerProxy()->getBlockDef(dirBlock.getResID());
			// auto thismtlLittle = GetDefManagerProxy()->getBlockDef(pblock.getResID() - 1);
			// if (mtl->Type == thismtlLittle->Type)
			// {
			//	 psection->getFaceVertexLight(blockpos + g_DirectionCoord[specialdir], dir, faceVertexLight);
			// }
		 //}
		 /*for (int i = 0; i < 4; i++)
		 {
			 faceVertexLight.m_Light[i] = (faceVertexLight.m_Light[i] + 2 * faceUpDownVertexLight.m_Light[i]) / 3;
			 faceVertexLight.m_AmbientOcclusion[i] = (faceVertexLight.m_AmbientOcclusion[i] + 2 * faceUpDownVertexLight.m_AmbientOcclusion[i]) / 3;
		 }*/
		 dynamic_array<UInt16>* indices = m_PosTrIndices;
		 RenderBlockMaterial* pmtl = NULL;
		 if (isSnowing == true)
		 {
			 pmtl = snowTopMtl;
			 if (isFaceUp == true)
			 {
				 pmtl = snowSideMtl;
			 }
		 }
		 if (pmtl == NULL) pmtl = getFaceMtl(biome, dir, pblock.getData(), facecolor);
		 if (pmtl == NULL)
			 continue;
		 SectionSubMesh* psubmesh = poutmesh->getSubMesh(pmtl);
		 const float* uvtile = nullptr;
		 if (psubmesh && !psubmesh->IsIgnoreTileUV())
			 uvtile = pmtl->getUVTile();
		 BlockGeomMeshInfo mesh;
		 bool isSpical = false;
		 World* pworld = data.m_World;
		 auto dirBlock = pworld->getBlock(NeighborCoord(blockpos + psection->getOrigin(), specialdir));
		 if (IsHalfTriangleBlock(dirBlock.getResID()))
		 {
			 auto dirBlock_mtl = g_BlockMtlMgr.getMaterial(dirBlock.getResID());
			 auto mtl = dirBlock_mtl->GetBlockDef();
			 auto pblock_mtl = g_BlockMtlMgr.getMaterial(pblock.getResID() - 1);
			 if (pblock_mtl && pblock_mtl->GetBlockDef())
			 {
				 auto thismtlLittle = pblock_mtl->GetBlockDef();
				 if (mtl->Type == thismtlLittle->Type)
				 {
					 isSpical = true;
				 }
			 }
		 }
		 mesh.vertices = m_mTurnSlantFace[d];
		 mesh.indices = *indices;
		//  unsigned short dir_color1 = TriangleNormal2LightColor(g_DirectionCoord[dir].toVector3());
		//  unsigned short dir_color2 = TriangleNormal2LightColor(g_DirectionCoord[specialdir].toVector3());
		 BlockColor vertcolor = Rainbow::ColorRGBA32::white;
		 //vertcolor.v = 0xffffffff;
		 vertcolor.a = 0;//(dir_color1 + 2 * dir_color2) / 3;
		//  if (specialdir == DIR_NEG_Y)
		//  {
		// 	 unsigned short dir_color3 = TriangleNormal2LightColor(g_DirectionCoord[DIR_POS_Y].toVector3());
		// 	 vertcolor.a = (3 * dir_color1 + dir_color2 + 2 * dir_color3) / 6;
		//  }
		 for (int n = 0; n < m_mTurnSlantFace[d].size(); n++)
		 {
			 auto& vert = mesh.vertices[n];
			 int aveltMe = 0;
			 int aveltNeight[3] = { 0 };
			 int avelt = 0;
			 if (vert.pos.y > 0 && specialdir == DIR_POS_Y || vert.pos.y == 0 && specialdir == DIR_NEG_Y)
			 {
				 bool isXdir = dir < 2;
				 int xdir = isXdir ? dir : vert.pos.x == 0 ? 0 : 1;
				 int zdir = !isXdir ? dir : vert.pos.z == 0 ? 2 : 3;
				 auto selfPos = blockpos + g_DirectionCoord[specialdir];
				 aveltMe = psection->getLight2(selfPos, true);
				 aveltNeight[0] = psection->getLight2(selfPos + g_DirectionCoord[xdir], true);
				 aveltNeight[1] = psection->getLight2(selfPos + g_DirectionCoord[zdir], true);
				 aveltNeight[2] = psection->getLight2(selfPos + g_DirectionCoord[xdir] + g_DirectionCoord[zdir], true);
				 avelt = ((aveltMe + aveltNeight[0] + aveltNeight[1] + aveltNeight[2]) >> 2) & 0xff00ff;
			 }
			 else
			 {
				 auto selfPos = blockpos + g_DirectionCoord[dir];
				 aveltMe = psection->getLight2(selfPos, true);
				 int xdir = vert.pos.x == 0 ? 0 : 1;
				 int zdir = vert.pos.z == 0 ? 2 : 3;
				 int sideDir = dir > 1 ? xdir : zdir;

				 aveltMe = isSpical ? psection->getLight2(selfPos + g_DirectionCoord[specialdir], true) : psection->getLight2(selfPos, true);
				 aveltNeight[0] = psection->getLight2(selfPos + g_DirectionCoord[sideDir], true);
				 avelt = ((aveltMe + aveltNeight[0]) >> 1) & 0xff00ff;
			 }
			 //int lt1 = (((avelt >> 4) & 0xf) * vertcolor.a) >> 5;
			 //int lt2 = (((avelt >> 20) & 0xf) * vertcolor.a) >> 5;
			 //vert.pos.w = (lt1 << 8) | lt2;
			 //vert.color = vertcolor;
			 vert.color = vertcolor;
			 InitBlockVertLight(vert, avelt, uvtile);
		 }
		 if (psubmesh) psubmesh->addGeomFace(mesh, &blockpos); //addGeomFaceLight(mesh, &blockpos, faceVertexLight, &facecolor, pmtl->getUVTile());
	 }
	 for (auto& d : slantFace)
	 {
		 DirectionType dir = (DirectionType)curDir;
		 /*bool flipQuad = psection->getFaceVertexLight(blockpos, dir, faceVertexLight);
		 FaceVertexLight faceUpDownVertexLight;
		 psection->getFaceVertexLight(blockpos, specialdir, faceUpDownVertexLight);	*/

		 //auto dirBlock = psection->getNeighborBlock(blockpos, dir);
		 //if (IsHalfTriangleBlock(dirBlock.getResID()))
		 //{
			// auto mtl = GetDefManagerProxy()->getBlockDef(dirBlock.getResID());
			// auto thismtlLittle = GetDefManagerProxy()->getBlockDef(pblock.getResID() - 1);
			// if (mtl->Type == thismtlLittle->Type)
			// {
			//	 psection->getFaceVertexLight(blockpos + g_DirectionCoord[specialdir], dir, faceVertexLight);
			// }
		 //}
		 /*for (int i = 0; i < 4; i++)
		 {
			 faceVertexLight.m_Light[i] = (faceVertexLight.m_Light[i] + 2 * faceUpDownVertexLight.m_Light[i]) / 3;
			 faceVertexLight.m_AmbientOcclusion[i] = (faceVertexLight.m_AmbientOcclusion[i] + 2 * faceUpDownVertexLight.m_AmbientOcclusion[i]) / 3;
		 }*/

		 dynamic_array<UInt16>* indices = m_dPosIndices;
		 RenderBlockMaterial* pmtl = NULL;
		 if (isSnowing == true)
		 {
			 if (isFaceUp == false)
			 {
				 pmtl = snowTopMtl;
			 }
		 }
		 if (pmtl == NULL) pmtl = getFaceMtl(biome, dir, pblock.getData(), facecolor);
		 if (pmtl == NULL)
			 continue;
		 SectionSubMesh* psubmesh = poutmesh->getSubMesh(pmtl);
		 const float* uvtile = nullptr;
		 if (psubmesh && !psubmesh->IsIgnoreTileUV())
			 uvtile = pmtl->getUVTile();
		 BlockGeomMeshInfo mesh;
		 bool isSpical = false;
		 World* pworld = data.m_World;
		 auto dirBlock = pworld->getBlock(NeighborCoord(blockpos + psection->getOrigin(), specialdir));
		 if (IsHalfTriangleBlock(dirBlock.getResID()))
		 {
			 auto dirBlock_mtl = g_BlockMtlMgr.getMaterial(dirBlock.getResID());
			 auto mtl = dirBlock_mtl->GetBlockDef();
			 auto pblock_mtl = g_BlockMtlMgr.getMaterial(pblock.getResID() - 1);
			 if (pblock_mtl && pblock_mtl->GetBlockDef())
			 {
				 auto thismtlLittle = pblock_mtl->GetBlockDef();
				 if (mtl->Type == thismtlLittle->Type)
				 {
					 isSpical = true;
				 }
			 }
		 }
		 mesh.vertices = m_mSlantFace[d];
		 mesh.indices = *indices;
		//  unsigned short dir_color1 = TriangleNormal2LightColor(g_DirectionCoord[dir].toVector3());
		//  unsigned short dir_color2 = TriangleNormal2LightColor(g_DirectionCoord[specialdir].toVector3());
		 BlockColor vertcolor = Rainbow::ColorRGBA32::white;
		 //vertcolor.v = 0xffffffff;
		 vertcolor.a = 0;//(dir_color1 + 2 * dir_color2) / 3;
		//  if (specialdir == DIR_NEG_Y)
		//  {
		// 	 vertcolor.a = (3 * dir_color1 + dir_color2) / 4;
		//  }
		 for (int n = 0; n < m_mSlantFace[d].size(); n++)
		 {
			 auto& vert = mesh.vertices[n];
			 int aveltMe = 0;
			 int aveltNeight[3] = { 0 };
			 int avelt = 0;
			 if (vert.pos.y > 0 && specialdir == DIR_POS_Y || vert.pos.y == 0 && specialdir == DIR_NEG_Y)
			 {
				 bool isXdir = dir < 2;
				 int xdir = isXdir ? dir : vert.pos.x == 0 ? 0 : 1;
				 int zdir = !isXdir ? dir : vert.pos.z == 0 ? 2 : 3;
				 auto selfPos = blockpos + g_DirectionCoord[specialdir];
				 aveltMe = psection->getLight2(selfPos, true);
				 aveltNeight[0] = psection->getLight2(selfPos + g_DirectionCoord[xdir], true);
				 aveltNeight[1] = psection->getLight2(selfPos + g_DirectionCoord[zdir], true);
				 aveltNeight[2] = psection->getLight2(selfPos + g_DirectionCoord[xdir] + g_DirectionCoord[zdir], true);
				 avelt = ((aveltMe + aveltNeight[0] + aveltNeight[1] + aveltNeight[2]) >> 2) & 0xff00ff;
			 }
			 else
			 {
				 auto selfPos = blockpos + g_DirectionCoord[dir];
				 aveltMe = psection->getLight2(selfPos, true);
				 int xdir = vert.pos.x == 0 ? 0 : 1;
				 int zdir = vert.pos.z == 0 ? 2 : 3;
				 int sideDir = dir > 1 ? xdir : zdir;

				 aveltMe = isSpical ? psection->getLight2(selfPos + g_DirectionCoord[specialdir], true) : psection->getLight2(selfPos, true);
				 aveltNeight[0] = psection->getLight2(selfPos + g_DirectionCoord[sideDir], true);
				 avelt = ((aveltMe + aveltNeight[0]) >> 1) & 0xff00ff;
			 }
			 //int lt1 = (((avelt >> 4) & 0xf) * vertcolor.a) >> 5;
			 //int lt2 = (((avelt >> 20) & 0xf) * vertcolor.a) >> 5;
			 //vert.pos.w = (lt1 << 8) | lt2;
			 //vert.color = vertcolor;
			 vert.color = vertcolor;
			 InitBlockVertLight(vert, avelt, uvtile);
		 }

		 if (psubmesh) psubmesh->addGeomFace(mesh, &blockpos); //addGeomFaceLight(mesh, &blockpos, faceVertexLight, &facecolor, pmtl->getUVTile());
	 }
#endif
 }

 void HorizontalTriangleSlabMaterial::createCollideData(CollisionDetect* coldetect, World* pworld, const WCoord& blockpos)
 {
	 auto block = pworld->getBlock(blockpos);
	 float blockheight = getBlockHeight(block.getData());
	 WCoord pos = blockpos * BLOCK_SIZE;
	 if (blockheight == 1)
	 {
		 coldetect->addObstacle(pos, pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
	 }
	 else
	 {
		 short step = 10;
		 float movesize = 1.f / float(step);
		 float heightOffset = 1.f / (float(step) - 1.f);
		 int heightsize = BLOCK_SIZE * 0.45f;
		 int xzsize = BLOCK_SIZE * 0.9f;
		 int dir = pworld->getBlockData(blockpos) & 3;

		 auto pblock = pworld->getBlock(blockpos);
		 int warp = -1;
		 int turnDir = -1;

		 auto frontBlock = pworld->getBlock(blockpos + g_DirectionCoord[dir]);
		 if (pblock.getResID() == frontBlock.getResID() && frontBlock.getData() < 8 && ((pblock.getData() & 4) == (frontBlock.getData() & 4)))
		 {
			 int frontDir = frontBlock.getData() & 3;
			 if (frontDir != dir && frontDir != ReverseDirection(dir))
			 {
				 warp = 0;
				 turnDir = frontDir;
			 }
		 }
		 if (warp == -1)
		 {
			 auto backBlock = pworld->getBlock(blockpos + g_DirectionCoord[ReverseDirection(dir)]);
			 if (pblock.getResID() == backBlock.getResID() && backBlock.getData() < 8 && ((pblock.getData() & 4) == (backBlock.getData() & 4)))
			 {
				 int backDir = backBlock.getData() & 3;
				 if (backDir != dir && backDir != ReverseDirection(dir))
				 {
					 warp = 1;
					 turnDir = backDir;
				 }
			 }
		 }

		 if (warp == -1)
		 {
			 if (blockheight > 0)
			 {
				 if (dir == 0)
				 {
					 for (int i = 1; i < step; i++)
					 {
						 coldetect->addObstacle(pos + WCoord(int(movesize * i * BLOCK_SIZE), BLOCK_HALFSIZE, 0), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE + int(heightOffset * i * heightsize), BLOCK_SIZE));
						 drawBox(pos + WCoord(int(movesize * i * BLOCK_SIZE), BLOCK_HALFSIZE, 0), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE + int(heightOffset * i * heightsize), BLOCK_SIZE));
					 }
				 }
				 else if (dir == 1)
				 {
					 for (int i = 1; i < step; i++)
					 {
						 coldetect->addObstacle(pos + WCoord(0, BLOCK_HALFSIZE, 0), pos + WCoord(BLOCK_SIZE - int(movesize * i * BLOCK_SIZE), BLOCK_HALFSIZE + int(heightOffset * i * heightsize), BLOCK_SIZE));
						 drawBox(pos + WCoord(0, BLOCK_HALFSIZE, 0), pos + WCoord(BLOCK_SIZE - int(movesize * i * BLOCK_SIZE), BLOCK_HALFSIZE + int(heightOffset * i * heightsize), BLOCK_SIZE));
					 }
				 }
				 else if (dir == 2)
				 {
					 for (int i = 1; i < step; i++)
					 {
						 coldetect->addObstacle(pos + WCoord(0, BLOCK_HALFSIZE, int(movesize * i * BLOCK_SIZE)), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE + int(heightOffset * i * heightsize), BLOCK_SIZE));
						 drawBox(pos + WCoord(0, BLOCK_HALFSIZE, int(movesize * i * BLOCK_SIZE)), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE + int(heightOffset * i * heightsize), BLOCK_SIZE));
					 }
				 }
				 else if (dir == 3)
				 {
					 for (int i = 1; i < step; i++)
					 {
						 coldetect->addObstacle(pos + WCoord(0, BLOCK_HALFSIZE, 0), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE + int(heightOffset * i * heightsize), BLOCK_SIZE - int(movesize * i * BLOCK_SIZE)));
						 drawBox(pos + WCoord(0, BLOCK_HALFSIZE, 0), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE + int(heightOffset * i * heightsize), BLOCK_SIZE - int(movesize * i * BLOCK_SIZE)));
					 }
				 }
				 coldetect->addObstacle(pos, pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE, BLOCK_SIZE));
				 drawBox(pos, pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE, BLOCK_SIZE));
			 }
			 else
			 {
				 if (dir == 0)
				 {
					 for (int i = 1; i < step; i++)
					 {
						 coldetect->addObstacle(pos + WCoord(BLOCK_SIZE - int(movesize * i * xzsize), int(heightOffset * i * BLOCK_HALFSIZE), 0), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE, BLOCK_SIZE));
						 drawBox(pos + WCoord(BLOCK_SIZE - int(movesize * i * xzsize), int(heightOffset * i * BLOCK_HALFSIZE), 0), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE, BLOCK_SIZE));
					 }
				 }
				 else if (dir == 1)
				 {
					 for (int i = 1; i < step; i++)
					 {
						 coldetect->addObstacle(pos + WCoord(0, int(heightOffset * i * BLOCK_HALFSIZE), 0), pos + WCoord(int(movesize * i * xzsize), BLOCK_HALFSIZE, BLOCK_SIZE));
						 drawBox(pos + WCoord(0, int(heightOffset * i * BLOCK_HALFSIZE), 0), pos + WCoord(int(movesize * i * xzsize), BLOCK_HALFSIZE, BLOCK_SIZE));
					 }
				 }
				 else if (dir == 2)
				 {
					 for (int i = 1; i < step; i++)
					 {
						 coldetect->addObstacle(pos + WCoord(0, int(heightOffset * i * BLOCK_HALFSIZE), BLOCK_SIZE - int(movesize * i * xzsize)), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE, BLOCK_SIZE));
						 drawBox(pos + WCoord(0, int(heightOffset * i * BLOCK_HALFSIZE), BLOCK_SIZE - int(movesize * i * xzsize)), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE, BLOCK_SIZE));
					 }
				 }
				 else if (dir == 3)
				 {
					 for (int i = 1; i < step; i++)
					 {
						 coldetect->addObstacle(pos + WCoord(0, int(heightOffset * i * BLOCK_HALFSIZE), 0), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE, int(movesize * i * xzsize)));
						 drawBox(pos + WCoord(0, int(heightOffset * i * BLOCK_HALFSIZE), 0), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE, int(movesize * i * xzsize)));
					 }
				 }
				 coldetect->addObstacle(pos + WCoord(0, BLOCK_HALFSIZE, 0), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
				 drawBox(pos + WCoord(0, BLOCK_HALFSIZE, 0), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
			 }
		 }
		 else if (1 == warp)
		 {
			 if (blockheight > 0)
			 {
				 if (dir == 0 && turnDir == 2 || dir == 2 && turnDir == 0)
				 {
					 for (int i = 1; i < step; i++)
					 {
						 coldetect->addObstacle(pos + WCoord(int(movesize * i * BLOCK_SIZE), BLOCK_HALFSIZE, int(movesize * i * BLOCK_SIZE)), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE + int(heightOffset * i * heightsize), BLOCK_SIZE));
						 drawBox(pos + WCoord(int(movesize * i * BLOCK_SIZE), BLOCK_HALFSIZE, int(movesize * i * BLOCK_SIZE)), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE + int(heightOffset * i * heightsize), BLOCK_SIZE));
					 }
				 }
				 else if (dir == 0 && turnDir == 3 || dir == 3 && turnDir == 0)
				 {
					 for (int i = 1; i < step; i++)
					 {
						 coldetect->addObstacle(pos + WCoord(int(movesize * i * BLOCK_SIZE), BLOCK_HALFSIZE, 0), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE + int(heightOffset * i * heightsize), BLOCK_SIZE - int(movesize * i * BLOCK_SIZE)));
						 drawBox(pos + WCoord(int(movesize * i * BLOCK_SIZE), BLOCK_HALFSIZE, 0), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE + int(heightOffset * i * heightsize), BLOCK_SIZE - int(movesize * i * BLOCK_SIZE)));
					 }
				 }
				 else if (dir == 1 && turnDir == 2 || dir == 2 && turnDir == 1)
				 {
					 for (int i = 1; i < step; i++)
					 {
						 coldetect->addObstacle(pos + WCoord(0, BLOCK_HALFSIZE, int(movesize * i * BLOCK_SIZE)), pos + WCoord(BLOCK_SIZE - int(movesize * i * BLOCK_SIZE), BLOCK_HALFSIZE + int(heightOffset * i * heightsize), BLOCK_SIZE));
						 drawBox(pos + WCoord(0, BLOCK_HALFSIZE, int(movesize * i * BLOCK_SIZE)), pos + WCoord(BLOCK_SIZE - int(movesize * i * BLOCK_SIZE), BLOCK_HALFSIZE + int(heightOffset * i * heightsize), BLOCK_SIZE));
					 }
				 }
				 else if (dir == 1 && turnDir == 3 || dir == 3 && turnDir == 1)
				 {
					 for (int i = 1; i < step; i++)
					 {
						 coldetect->addObstacle(pos + WCoord(0, BLOCK_HALFSIZE, 0), pos + WCoord(BLOCK_SIZE - int(movesize * i * BLOCK_SIZE), BLOCK_HALFSIZE + int(heightOffset * i * heightsize), BLOCK_SIZE - int(movesize * i * BLOCK_SIZE)));
						 drawBox(pos + WCoord(0, BLOCK_HALFSIZE, 0), pos + WCoord(BLOCK_SIZE - int(movesize * i * BLOCK_SIZE), BLOCK_HALFSIZE + int(heightOffset * i * heightsize), BLOCK_SIZE - int(movesize * i * BLOCK_SIZE)));
					 }
				 }
				 coldetect->addObstacle(pos, pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE, BLOCK_SIZE));
				 drawBox(pos, pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE, BLOCK_SIZE));
			 }
			 else
			 {
				 if (dir == 0 && turnDir == 2 || dir == 2 && turnDir == 0)
				 {
					 for (int i = 1; i < step; i++)
					 {
						 coldetect->addObstacle(pos + WCoord(BLOCK_SIZE - int(movesize * i * xzsize),  int(heightOffset * i * BLOCK_HALFSIZE), BLOCK_SIZE - int(movesize * i * xzsize)), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE, BLOCK_SIZE));
						 drawBox(pos + WCoord(BLOCK_SIZE - int(movesize * i * xzsize), int(heightOffset * i * BLOCK_HALFSIZE), BLOCK_SIZE - int(movesize * i * xzsize)), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE, BLOCK_SIZE));
					 }
				 }
				 else if (dir == 0 && turnDir == 3 || dir == 3 && turnDir == 0)
				 {
					 for (int i = 1; i < step; i++)
					 {
						 coldetect->addObstacle(pos + WCoord(BLOCK_SIZE - int(movesize * i * xzsize), int(heightOffset * i * BLOCK_HALFSIZE), 0), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE, int(movesize * i * xzsize)));
						 drawBox(pos + WCoord(BLOCK_SIZE - int(movesize * i * xzsize), int(heightOffset * i * BLOCK_HALFSIZE), 0), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE, int(movesize * i * xzsize)));
					 }
				 }
				 else if (dir == 1 && turnDir == 2 || dir == 2 && turnDir == 1)
				 {
					 for (int i = 1; i < step; i++)
					 {
						 coldetect->addObstacle(pos + WCoord(0, int(heightOffset * i * BLOCK_HALFSIZE), BLOCK_SIZE - int(movesize * i * xzsize)), pos + WCoord(int(movesize * i * xzsize), BLOCK_HALFSIZE, BLOCK_SIZE));
						 drawBox(pos + WCoord(0, int(heightOffset * i * BLOCK_HALFSIZE), BLOCK_SIZE - int(movesize * i * xzsize)), pos + WCoord(int(movesize * i * xzsize), BLOCK_HALFSIZE, BLOCK_SIZE));
					 }
				 }
				 else if (dir == 1 && turnDir == 3 || dir == 3 && turnDir == 1)
				 {
					 for (int i = 1; i < step; i++)
					 {
						 coldetect->addObstacle(pos + WCoord(0, int(heightOffset * i * BLOCK_HALFSIZE), 0), pos + WCoord(int(movesize * i * xzsize), BLOCK_HALFSIZE, int(movesize * i * xzsize)));
						 drawBox(pos + WCoord(0, int(heightOffset * i * BLOCK_HALFSIZE), 0), pos + WCoord(int(movesize * (i)*xzsize), BLOCK_HALFSIZE, int(movesize * i * xzsize)));
					 }
				 }
				 coldetect->addObstacle(pos + WCoord(0, BLOCK_HALFSIZE, 0), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
				 drawBox(pos + WCoord(0, BLOCK_HALFSIZE, 0), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
			 }
		 }
		 else
		 {
			 if (blockheight > 0)
			 {
				 if (dir == 0 && turnDir == 2 || dir == 2 || turnDir == 0)
				 {
					 for (int i = 1; i < step; i++)
					 {
						 coldetect->addObstacle(pos + WCoord(int(movesize * i * BLOCK_SIZE), BLOCK_HALFSIZE, 0), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE + int(heightOffset * i * heightsize), BLOCK_SIZE));
						 drawBox(pos + WCoord(int(movesize * i * BLOCK_SIZE), BLOCK_HALFSIZE, 0), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE + int(heightOffset * i * heightsize), BLOCK_SIZE));
						 coldetect->addObstacle(pos + WCoord(0, BLOCK_HALFSIZE, int(movesize * i * BLOCK_SIZE)), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE + int(heightOffset * i * heightsize), BLOCK_SIZE));
						 drawBox(pos + WCoord(0, BLOCK_HALFSIZE, int(movesize * i * BLOCK_SIZE)), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE + int(heightOffset * i * heightsize), BLOCK_SIZE));
					 }
				 }
				 else if (dir == 0 && turnDir == 3 || dir == 3 && turnDir == 0)
				 {
					 for (int i = 1; i < step; i++)
					 {
						 coldetect->addObstacle(pos + WCoord(int(movesize * i * BLOCK_SIZE), BLOCK_HALFSIZE, 0), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE + int(heightOffset * i * heightsize), BLOCK_SIZE));
						 drawBox(pos + WCoord(int(movesize * i * BLOCK_SIZE), BLOCK_HALFSIZE, 0), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE + int(heightOffset * i * heightsize), BLOCK_SIZE));
						 coldetect->addObstacle(pos + WCoord(0, BLOCK_HALFSIZE, 0), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE + int(heightOffset * i * heightsize), BLOCK_SIZE - int(movesize * i * BLOCK_SIZE)));
						 drawBox(pos + WCoord(0, BLOCK_HALFSIZE, 0), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE + int(heightOffset * i * heightsize), BLOCK_SIZE - int(movesize * i * BLOCK_SIZE)));
					 }
				 }
				 else if (dir == 1 && turnDir == 2 || dir == 2 || turnDir == 1)
				 {
					 for (int i = 1; i < step; i++)
					 {
						 coldetect->addObstacle(pos + WCoord(0, BLOCK_HALFSIZE, 0), pos + WCoord(BLOCK_SIZE - int(movesize * i * BLOCK_SIZE), BLOCK_HALFSIZE + int(heightOffset * i * heightsize), BLOCK_SIZE));
						 drawBox(pos + WCoord(0, BLOCK_HALFSIZE, 0), pos + WCoord(BLOCK_SIZE - int(movesize * i * BLOCK_SIZE), BLOCK_HALFSIZE + int(heightOffset * i * heightsize), BLOCK_SIZE));
						 coldetect->addObstacle(pos + WCoord(0, BLOCK_HALFSIZE, int(movesize * i * BLOCK_SIZE)), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE + int(heightOffset * i * heightsize), BLOCK_SIZE));
						 drawBox(pos + WCoord(0, BLOCK_HALFSIZE, int(movesize * i * BLOCK_SIZE)), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE + int(heightOffset * i * heightsize), BLOCK_SIZE));
					 }
				 }
				 else if (dir == 1 && turnDir == 3 || dir == 3 && turnDir == 1)
				 {
					 for (int i = 1; i < step; i++)
					 {
						 coldetect->addObstacle(pos + WCoord(0, BLOCK_HALFSIZE, 0), pos + WCoord(BLOCK_SIZE - int(movesize * i * BLOCK_SIZE), BLOCK_HALFSIZE + int(heightOffset * i * heightsize), BLOCK_SIZE));
						 drawBox(pos + WCoord(0, BLOCK_HALFSIZE, 0), pos + WCoord(BLOCK_SIZE - int(movesize * i * BLOCK_SIZE), BLOCK_HALFSIZE + int(heightOffset * i * heightsize), BLOCK_SIZE));
						 coldetect->addObstacle(pos + WCoord(0, BLOCK_HALFSIZE, 0), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE + int(heightOffset * i * heightsize), BLOCK_SIZE - int(movesize * i * BLOCK_SIZE)));
						 drawBox(pos + WCoord(0, BLOCK_HALFSIZE, 0), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE + int(heightOffset * i * heightsize), BLOCK_SIZE - int(movesize * i * BLOCK_SIZE)));
					 }
				 }
				 coldetect->addObstacle(pos, pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE, BLOCK_SIZE));
				 drawBox(pos, pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE, BLOCK_SIZE));
			 }
			 else
			 {
				 if (dir == 0 && turnDir == 2 || dir == 2 || turnDir == 0)
				 {
					 for (int i = 1; i < step; i++)
					 {
						 coldetect->addObstacle(pos + WCoord(BLOCK_SIZE - int(movesize * i * xzsize), int(heightOffset * i * BLOCK_HALFSIZE), 0), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE, BLOCK_SIZE));
						 drawBox(pos + WCoord(BLOCK_SIZE - int(movesize * i * xzsize), int(heightOffset * i * BLOCK_HALFSIZE), 0), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE, BLOCK_SIZE));
						 coldetect->addObstacle(pos + WCoord(0, int(heightOffset * i * BLOCK_HALFSIZE), BLOCK_SIZE - int(movesize * i * xzsize)), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE, BLOCK_SIZE));
						 drawBox(pos + WCoord(0, int(heightOffset * i * BLOCK_HALFSIZE), BLOCK_SIZE - int(movesize * i * xzsize)), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE, BLOCK_SIZE));
					 }
				 }
				 else if (dir == 0 && turnDir == 3 || dir == 3 && turnDir == 0)
				 {
					 for (int i = 1; i < step; i++)
					 {
						 coldetect->addObstacle(pos + WCoord(BLOCK_SIZE - int(movesize * i * xzsize), int(heightOffset * i * BLOCK_HALFSIZE), 0), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE, BLOCK_SIZE));
						 drawBox(pos + WCoord(BLOCK_SIZE - int(movesize * i * xzsize), int(heightOffset * i * BLOCK_HALFSIZE), 0), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE, BLOCK_SIZE));
						 coldetect->addObstacle(pos + WCoord(0, int(heightOffset * i * BLOCK_HALFSIZE), 0), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE, int(movesize * i * xzsize)));
						 drawBox(pos + WCoord(0, int(heightOffset * i * BLOCK_HALFSIZE), 0), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE, int(movesize * i * xzsize)));
					 }
				 }
				 else if (dir == 1 && turnDir == 2 || dir == 2 || turnDir == 1)
				 {
					 for (int i = 1; i < step; i++)
					 {
						 coldetect->addObstacle(pos + WCoord(0, int(heightOffset * i * BLOCK_HALFSIZE), 0), pos + WCoord(int(movesize * i * xzsize), BLOCK_HALFSIZE, BLOCK_SIZE));
						 drawBox(pos + WCoord(0, int(heightOffset * i * BLOCK_HALFSIZE), 0), pos + WCoord(int(movesize * i * xzsize), BLOCK_HALFSIZE, BLOCK_SIZE));
						 coldetect->addObstacle(pos + WCoord(0, int(heightOffset * i * BLOCK_HALFSIZE), BLOCK_SIZE - int(movesize * i * xzsize)), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE, BLOCK_SIZE));
						 drawBox(pos + WCoord(0, int(heightOffset * i * BLOCK_HALFSIZE), BLOCK_SIZE - int(movesize * i * xzsize)), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE, BLOCK_SIZE));
					 }
				 }
				 else if (dir == 1 && turnDir == 3 || dir == 3 && turnDir == 1)
				 {
					 for (int i = 1; i < step; i++)
					 {
						 coldetect->addObstacle(pos + WCoord(0, int(heightOffset * i * BLOCK_HALFSIZE), 0), pos + WCoord(int(movesize * i * xzsize), BLOCK_HALFSIZE, BLOCK_SIZE));
						 drawBox(pos + WCoord(0, int(heightOffset * i * BLOCK_HALFSIZE), 0), pos + WCoord(int(movesize * i * xzsize), BLOCK_HALFSIZE, BLOCK_SIZE));
						 coldetect->addObstacle(pos + WCoord(0, int(heightOffset * i * BLOCK_HALFSIZE), 0), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE, int(movesize * i * xzsize)));
						 drawBox(pos + WCoord(0, int(heightOffset * i * BLOCK_HALFSIZE), 0), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE, int(movesize * i * xzsize)));
					 }
				 }
				 coldetect->addObstacle(pos + WCoord(0, BLOCK_HALFSIZE, 0), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
				 drawBox(pos + WCoord(0, BLOCK_HALFSIZE, 0), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
			 }
		 }
	 }
 }

 bool HorizontalTriangleSlabMaterial::coverNeighbor(int curblockdata, SolidBlockMaterial* neighbor, int neighbor_data, DirectionType dir)
 {
	 if (curblockdata & 8)
	 {
		 if (dir == DIR_NEG_Y)
		 {
			 if (neighbor && neighbor->GetAttrRenderType() == BLOCKRENDER_FLUID && neighbor_data) return false;
		 }
		 return true;
	 }
	 else
	 {
		 if (dir < DIR_NEG_Y)
		 {
			 int curDir = curblockdata & 3;
			 if (curDir == ReverseDirection(dir))
			 {
				 return true;
			 }
		 }
		 else
		 {
			 int updown = (curblockdata & 4) >> 2;
			 if ((updown + 4) == dir)
			 {
				 if (dir == DIR_NEG_Y)
				 {
					 if (neighbor && neighbor->GetAttrRenderType() == BLOCKRENDER_FLUID && neighbor_data) return false;
				 }
				 return true;
			 }
		 }
	 }
	 return false;
 }

 typedef HorizontalTriangleSlabMaterial::BlockInstance HorizontalTriangleSlabMaterialInstance;
 IMPLEMENT_SCENEOBJECTCLASS(HorizontalTriangleSlabMaterialInstance)
	 MNSandbox::ReflexClassParam<HorizontalTriangleSlabMaterial::BlockInstance, int> HorizontalTriangleSlabMaterialInstance::R_Dir(0, "Dir", "Block", &HorizontalTriangleSlabMaterial::BlockInstance::GetBlockData, &HorizontalTriangleSlabMaterial::BlockInstance::SetBlockData);