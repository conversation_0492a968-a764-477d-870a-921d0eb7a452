#include "PlayerControl.h"
#include "GameCamera.h"
#include "CameraModel.h"
#include "CameraManager.h"
#include "backpack.h"
#include "TouchControlLua.h"
#include "DefManagerProxy.h"
#include "ActorShapeShiftHorse.h"
#include "PlayerStateController.h"
#include "PackingFullyCustomModelMgr.h"
#include "PCControlLua.h"
#include "InputInfo.h"
#include "GameSettings.h"
#include "ui_framemgr.h"
#include "GunUseComponent.h"
#include "block_tickmgr.h"
#include "IRecordInterface.h"
#include "ActorDouDuMount.h"
#include "ActorPumpkinHorse.h"
#include "Sound/MusicManager.h"
#include "PlayerAttrib.h"
#include "ClientInfoProxy.h"
#include "PlayerInputHandler.h"
#include "CommonUtil.h"
#include "LuaInterfaceProxy.h"

#include "OgreUtils.h"
#include "GameNetManager.h"
#include "IWorldConfigProxy.h"
#include "RiddenComponent.h"
#include "CarryComponent.h"
#include "TemperatureComponent.h"
#include "ActorVehicleAssemble.h"
#include "Console/Console.h"
#include "SandboxIdDef.h"
#include "navigationpath.h"
#include "special_blockid.h"
#include "CustomGunUseComponent.h"
#include "ActionIdleStateGunAdvance.h"
using namespace MINIW;
using namespace MNSandbox;

static bool s_isAllowInput = true;
static core::hash_map<size_t, bool> s_forbidden_event;
static core::hash_map<int, bool> s_forbidden_keyvalue;

bool PlayerControl::interactActor(ClientActor *target, int interactType /* = 0 */, bool interactplot/* =false */)
{
	return PlayerControl::performInteractActor(target, interactType, interactplot, false);
}

bool PlayerControl::performInteractActor(ClientActor* target, int interactType, bool interactplot, bool onlyCheckAtk)
{
	if (!isCurToolUnlocked()) return false;
	if (target == nullptr) return false;
	bool ret = ClientPlayer::performInteractActor(target, interactType, interactplot, onlyCheckAtk);

	// 20210910：打断隐身  codeby： keguanqiang
	if (!onlyCheckAtk && ret && m_pWorld && !m_pWorld->isRemoteMode())
	{
		if (target)
		{
			ActorLiving* living = dynamic_cast<ActorLiving*>(target);
			if (living)
			{
				living->breakInvisible();
			}
		}
		breakHorseInvisible();
	}

	return ret;
}

bool PlayerControl::interactBlock(const WCoord &targetblock, DirectionType targetface, const Rainbow::Vector3f &colpoint)
{
    OPTICK_EVENT();
	if (!isCurToolUnlocked()) return false;
	if (!GetDefManagerProxy()->checkItemCrc(getCurToolID()) || !GetDefManagerProxy()->checkItemCrc(m_pWorld->getBlockID(targetblock)))
	{
		//ge GetGameEventQue().postInfoTips(165);
		CommonUtil::GetInstance().PostInfoTips(165);
		return false;
	}

	//m_CameraModel->playHandAnim(101105);
	bool ret = ClientPlayer::interactBlock(targetblock, targetface, colpoint);

	// 20210910：打断隐身  codeby： keguanqiang
	if (ret && m_pWorld && !m_pWorld->isRemoteMode())
	{
		breakHorseInvisible();
		// 尝试上报建筑工升职记活动记录
		generalTaskReportPlaceBlock();
	}

	return ret;
}


bool PlayerControl::interactUnderEditorMode()
{
#if defined(BUILD_MINI_EDITOR_APP)
	if (!GetIWorldConfigProxy()->GetIsStartEdit())
	{
		return false;
	}
	return GetIWorldConfigProxy()->ExcuteCmdWithRBClicked();
#else
	return false;
#endif//BUILD_MINI_EDITOR_APP
}

bool PlayerControl::attackRangedFree(int status)
{
	if (status == 0)
	{
		m_CurBowStage = -1;
		if (!ClientPlayer::attackRangedFree(status))
		{
			//GetGameEventQue().postInfoTips(7);
			CommonUtil::GetInstance().PostInfoTips(7);
			return false;
		}
	}
	else if (status == 1)
	{
		setBowStage(-1);
		if (!ClientPlayer::attackRangedFree(status)) return false;
	}
	return true;
}

//bool PlayerControl::eatFood(int itemid, int status)
//{
//	if (ClientPlayer::eatFood(itemid, status))
//	{
//		if (status == 0) m_CameraModel->playHandAnim(101110);
//		else m_CameraModel->playHandAnim(101100);
//
//		return true;
//	}
//	else return false;
//}

bool PlayerControl::useItem(int itemid, int status, bool onshift/* =false */, unsigned int useTick/* = 0*/)
{
	if (!isCurToolUnlocked()) return false;
	if (!GetDefManagerProxy()->checkItemCrc(itemid))
	{
		//ge GetGameEventQue().postInfoTips(165);
		CommonUtil::GetInstance().PostInfoTips(165);
		return false;
	}

	if (!ClientPlayer::useItem(itemid, status, onshift, useTick))
	{
		return false;
	}
	return true;
}

bool PlayerControl::usePackingFCMItem(int itemid, WCoord usepos)
{
	if (PackingFullyCustomModelMgr::GetInstancePtr() && PackingFullyCustomModelMgr::GetInstancePtr()->isLimit())
	{
		//ge GetGameEventQue().postInfoTips(16010);
		CommonUtil::GetInstance().PostInfoTips(16010);
		return false;
	}

	return ClientPlayer::usePackingFCMItem(itemid, usepos);
}


void PlayerControl::doWakeUp()
{
	getAttrib()->setOffLine(false);
}

bool PlayerControl::doSpecialSkill()
{
	//static bool cd_onece	= false;
	auto RidComp = getRiddenComponent();
	ActorHorse *riding = NULL;
	if (RidComp)
	{
		riding = dynamic_cast<ActorHorse *>(RidComp->getRidingActor());
	}
	if (riding && (riding->getRiddenByActorID() == getObjId()))
	{
		if (riding->getObjType() == OBJ_TYPE_SHAPESHIFT_HORSE)
		{
			ActorShapeShiftHorse* horse = dynamic_cast<ActorShapeShiftHorse*>(riding);
			if (horse)
			{
				if (horse->hasHorseSkill(HORSE_SKILL_TRANSFORMERS_MISSILE) || horse->hasHorseSkill(HORSE_SKILL_FUSION_LASER_MISSILE))
				{
					if (horse->getSkillCD(1) > 0)
					{
						//if (!cd_onece)
						{
							//GetGameEventQue().postInfoTips(100262);
							horse->playAttackSound();
							//cd_onece = true;
						}
						return false;
					}
				}
				else if (horse->hasHorseSkill(HORSE_SKILL_TRANSFORMERS_RUSH))
				{
					if (horse->getSkillCD(0) > 0)
					{
						//if (!cd_onece)
						{
							//GetGameEventQue().postInfoTips(100262);
							horse->playAttackSound();
							//cd_onece = true;
						}
						return false;
					}
				}
				else if (horse->hasHorseSkill(HORSE_SKILL_TRANSFORMERS_SPRINT) || horse->hasHorseSkill(HORSE_SKILL_RED_LINGHTNING_SPRINT) || horse->hasHorseSkill(HORSE_SKILL_RABBIT_SPRINT))
				{
					if (horse->getSkillCD(2) > 0)
					{
						horse->playAttackSound();
						return false;
					}
				}

				horse->setZoomByUseSkill();
			}
		}
		if (riding->getObjType() == OBJ_TYPE_DOUDU_MOUNT)
		{
			ActorDouDuMount* horse = dynamic_cast<ActorDouDuMount*>(riding);
			if (horse)
			{
				if (horse->hasHorseSkill(HORSE_SKILL_DOUDURUSH) ||
					horse->hasHorseSkill(HORSE_SKILL_FORTUNEMOO) ||
					horse->hasHorseSkill(HORSE_SKILL_YE_WU))
				{
					if (horse->getSkillCD(0) > 0)
					{
						horse->playAttackSound();
						return false;
					}
				}
				horse->setZoomByUseSkill();
			}
		}

		if (riding->getObjType() == OBJ_TYPE_PUMPKIN_HORSE) { // 播放南瓜车技能CD音效
			ActorPumpkinHorse* horse = dynamic_cast<ActorPumpkinHorse*>(riding);

			if (!horse)
				return false;

			if (horse->hasHorseSkill(HORSE_SKILL_PUMKKIN_SPRINT_LV1)
				|| horse->hasHorseSkill(HORSE_SKILL_PUMKKIN_SPRINT_LV2))
			{
				if (horse->getSkillCD(0) > 0)
				{
					horse->playAttackSound();

					return false;
				}
			}
		}

		if (!riding->canUseSkill())
		{
			riding->logicByCanNotUseSkill();
			return false;
		}
	}
	else
	{
		return false;
	}
	//cd_onece = false;
	return ClientPlayer::doSpecialSkill();
}

bool PlayerControl::useSpecialItem(int grid_index, int itemId, int num/* =1 */)
{
	if (!GetDefManagerProxy()->checkItemCrc(itemId))
	{
		//ge GetGameEventQue().postInfoTips(165);
		CommonUtil::GetInstance().PostInfoTips(165);
		return false;
	}

	return ClientPlayer::useSpecialItem(grid_index, itemId, num);
}

void PlayerControl::tryCarryActor(ClientActor *actor, WCoord pos /* = WCoord(0, -1, 0) */)
{
	carryActor(actor, pos);
}

void PlayerControl::tryWakeup()
{
	wakeUp(true, false, false);
}

void PlayerControl::trySleep()
{
	playerTrySleep();
}

void PlayerControl::tryStandup()
{
	standUpFromChair();
}

float GetPickRange(PlayerControl *player)
{
	float range = GetWorldManagerPtr()->isGodMode() ? 500.0f : 400.0f; //ray.m_Range = 500.0f;
	if (player->getCurToolID() == 11004) range = 500.0f;

	const ItemSkillDef *skilldef = player->getCurItemSkillDef();
	if (skilldef && skilldef->Distance > 0.0001f)
	{
		range = skilldef->Distance;
	}

	if (player->getOPWay() == PLAYEROP_WAY_FOOTBALLER || player->getOPWay() == PLAYEROP_WAY_PUSHSNOWBALL)
	{
		if (GetWorldManagerPtr()->m_SurviveGameConfig)
		{
			range = GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.catch_ball_range;
		}
	}
	if (player->getOPWay() == PLAYEROP_WAY_BASKETBALLER)
	{
		if (GetWorldManagerPtr()->m_SurviveGameConfig)
		{
			range = (float)(GetWorldManagerPtr()->m_SurviveGameConfig->basketballConfig.aim_max_distance* BLOCK_SIZE);
		}
	}
	else if (player->getViewMode() == CAMERA_TPS_BACK || player->getViewMode() == CAMERA_TPS_BACK_2
		|| player->getViewMode() == CAMERA_TPS_BACK_SHOULDER || player->getViewMode() == CAMERA_CUSTOM_VIEW && player->getCameraConfigOption(CAMERA_OPTION_INDEX_CONFIG_SET) == CCG_BACKVIEW)
	{

		range += player->getExtraPickRange();
	}

	int maxlen = ClientPlayer::m_ViewRangeSetting * 32 * 16 * 100;
	if (range > maxlen)
	{
		return 0;//处理返回结果
	}

	return range;
}

float PlayerControl::getExtraPickRange()
{
	//第三人称摄像机视角的距离与第一人称有所差别，拣取范围要随着摄像机与人物的距离变化而变化
	const Rainbow::Vector3f cameraPos = m_pCamera->getEngineCamera()->GetPosition();
	float extraRange = Distance(cameraPos, getPosition().toVector3());
	float axisY = m_pCamera->getEngineCamera()->GetWorldRotation().GetAxisY().y;
	if (extraRange <= 250.0f)
	{
		if (axisY <= 0.3f && axisY > 0.06f)
		{
			//当第三人称变化到的仰角时，距离跟第一视角保持一致
			extraRange = 0.0f;
		}
		else if (axisY <= 0.06f)
		{
			//当第三人称变化到的仰角大于第一视角最大仰角时，距离稍微缩小一些
			extraRange = -50.0f;
		}
	}
	//LOG_INFO("camera ratation y %f",axisY);
	//LOG_INFO("extra range ..%f",extraRange);
	return extraRange;
}


int PlayerControl::doPick(bool pickliquid, bool ignorecarried/* =false */, bool addboxpickrange/* =false */, bool isPlaceOnActor/* = false*/)
{
	//因为桶子需要持续检测是否在汲取液体，这里将各类桶子的pickliguid值都置为true
	int toolid = getCurToolID();
	if (toolid == ITEM_WOODEN_BUCKET || toolid == ITEM_BUCKET || toolid == ITEM_TITANIUM_BUCKET)
	{
		pickliquid = true;
	}
	else if (toolid == ITEM_SMALL_GLASS_BOTTLE)
	{
		//移动端直接成功
		if (Rainbow::GetIClientInfo().isMobile())
		{
			pickliquid = true;
		}
		else if (m_InputInfo->rightClickDown || m_InputInfo->rightClick || m_InputInfo->keyEClickDown)
		{
			pickliquid = true;
		}
	}

	MINIW::WorldRay ray;
	//Rainbow::Ray ray;
	if (getOPWay() == PLAYEROP_WAY_FOOTBALLER)
	{
		//ray.m_Origin = (getPosition() + WCoord(0, BLOCK_SIZE / 10, 0)).toWorldPos();
		ray.m_Origin = (getPosition() + WCoord(0, 5, 0)).toWorldPos();
		ray.m_Dir = getLocoMotion()->getLookDir();
		ray.m_Dir.y = 0;
		ray.m_Dir  = MINIW::Normalize(ray.m_Dir);
	}
	else
	{
		if (m_ViewMode == CAMERA_TPS_OVERLOOK)
		{
			ray.m_Origin = (getPosition() + WCoord(0, BLOCK_SIZE / 2, 0)).toWorldPos();
			ray.m_Dir = m_pCamera->getLookDir();
		}
		else if (m_ViewMode == CAMERA_CUSTOM_VIEW
			&& getCameraConfigOption(CAMERA_OPTION_INDEX_CROSSHAIR) == CCT_BODY_EYES)
		{
			ray.m_Origin = (getPosition() + WCoord(0, BLOCK_SIZE / 2, 0)).toWorldPos();
			ray.m_Dir = getLocoMotion()->getLookDir();
		}
		else
		{
			m_pCamera->getViewRayByScreenPt(&ray, m_CurMouseX, m_CurMouseY);
			//if (ray.m_Origin.x > 1000000) {
			//	auto cp = m_pCamera->getPosition();
			//	WarningStringMsg("viewmode:%d", m_ViewMode);
			//	WarningStringMsg("campos:%d,%d,%d", cp.x, cp.y, cp.z);
			//	WarningStringMsg("ray:[%d,%d,%d][%f]", ray.m_Origin.x, ray.m_Origin.y, ray.m_Origin.z, ray.m_Range);
			//}
		}
	}
	int range = GetPickRange(this);
	if (range == 0)
		return 0;
	ray.m_Range = range;

	m_PickResult.liquids.resize(0);
	m_PickResult.intersect_actor = m_PickResult.intersect_block = m_PickResult.isIntersectLiquid = false;

	ActorExcludes excludes;
	excludes.addActorWithRiding(this);
	if (ignorecarried)
	{
		auto CarryComp = getCarryComponent();
		if (CarryComp)
		{
			auto *carrying = CarryComp->getCarringActor();
			if (carrying)
			{
				excludes.addActor(carrying);
			}
		}

	}

	//ÒòÎªÍ°×ÓÐèÒª³ÖÐø¼ì²âÊÇ·ñÔÚ¼³È¡ÒºÌå£¬ÕâÀï½«¸÷ÀàÍ°×ÓµÄpickliguidÖµ¶¼ÖÃÎªtrue
	if (pickliquid ||getCurToolID() == ITEM_WOODEN_BUCKET || getCurToolID() == ITEM_BUCKET || getCurToolID() == ITEM_TITANIUM_BUCKET)
	{
		m_PickType = (int)m_pWorld->pickAll(ray, &m_PickResult, excludes, PICK_METHOD_CLICKLIQUID, 0, WCoord(), addboxpickrange);
	}
	else
	{
		m_PickType = (int)m_pWorld->pickAll(ray, &m_PickResult, excludes, PICK_METHOD_CLICK_INCLUDE_LIQUID, 0, WCoord(), addboxpickrange);
	}
	return m_PickType;
}

int PlayerControl::doRangeSkillPick(bool pickliquid, int range, IntersectResult& result, bool ignorecarried, bool addboxpickrange)
{
	if (range == 0)
		return 0;
	MINIW::WorldRay ray;
	//Rainbow::Ray ray;
	if (getOPWay() == PLAYEROP_WAY_FOOTBALLER)
	{
		//ray.m_Origin = (getPosition() + WCoord(0, BLOCK_SIZE / 10, 0)).toWorldPos();
		ray.m_Origin = (getPosition() + WCoord(0, 5, 0)).toWorldPos();
		ray.m_Dir = getLocoMotion()->getLookDir();
		ray.m_Dir.y = 0;
		ray.m_Dir = MINIW::Normalize(ray.m_Dir);
	}
	else
	{
		if (m_ViewMode == CAMERA_TPS_OVERLOOK)
		{
			ray.m_Origin = (getPosition() + WCoord(0, BLOCK_SIZE / 2, 0)).toWorldPos();
			ray.m_Dir = m_pCamera->getLookDir();
		}
		else if (m_ViewMode == CAMERA_CUSTOM_VIEW
			&& getCameraConfigOption(CAMERA_OPTION_INDEX_CROSSHAIR) == CCT_BODY_EYES)
		{
			ray.m_Origin = (getPosition() + WCoord(0, BLOCK_SIZE / 2, 0)).toWorldPos();
			ray.m_Dir = getLocoMotion()->getLookDir();
		}
		else
		{
			m_pCamera->getViewRayByScreenPt(&ray, m_CurMouseX, m_CurMouseY);
			//if (ray.m_Origin.x > 1000000) {
			//	auto cp = m_pCamera->getPosition();
			//	WarningStringMsg("viewmode:%d", m_ViewMode);
			//	WarningStringMsg("campos:%d,%d,%d", cp.x, cp.y, cp.z);
			//	WarningStringMsg("ray:[%d,%d,%d][%f]", ray.m_Origin.x, ray.m_Origin.y, ray.m_Origin.z, ray.m_Range);
			//}
		}
	}
	ray.m_Range = range;

	result.liquids.resize(0);
	result.intersect_actor = result.intersect_block = false;

	ActorExcludes excludes;
	excludes.addActorWithRiding(this);
	if (ignorecarried)
	{
		auto CarryComp = getCarryComponent();
		if (CarryComp)
		{
			auto* carrying = CarryComp->getCarringActor();
			if (carrying)
			{
				excludes.addActor(carrying);
			}
		}

	}
	if (pickliquid)
	{
		m_PickType = (int)m_pWorld->pickAll(ray, &result, excludes, PICK_METHOD_CLICKLIQUID, 0, WCoord(), addboxpickrange);
	}
	else
	{
		m_PickType = (int)m_pWorld->pickAll(ray, &result, excludes, PICK_METHOD_CLICK, 0, WCoord(), addboxpickrange);
	}
	return m_PickType;
}

int PlayerControl::doPickByType(int picktype, bool ignorecarried)
{
	MINIW::WorldRay ray;
	if (getOPWay() == PLAYEROP_WAY_FOOTBALLER)
	{
		//ray.m_Origin = (getPosition() + WCoord(0, BLOCK_SIZE / 10, 0)).toWorldPos();
		ray.m_Origin = (getPosition() + WCoord(0, 5, 0)).toWorldPos();
		ray.m_Dir = getLocoMotion()->getLookDir();
		ray.m_Dir.y = 0;
		MINIW::Normalize(ray.m_Dir);
	}
	else
	{
		if (m_ViewMode == CAMERA_TPS_OVERLOOK)
		{
			ray.m_Origin = (getPosition() + WCoord(0, BLOCK_SIZE / 2, 0)).toWorldPos();
			ray.m_Dir = m_pCamera->getLookDir();
		}
		else if (m_ViewMode == CAMERA_CUSTOM_VIEW
			&& getCameraConfigOption(CAMERA_OPTION_INDEX_CROSSHAIR) == CCT_BODY_EYES)
		{
			ray.m_Origin = (getPosition() + WCoord(0, BLOCK_SIZE / 2, 0)).toWorldPos();
			ray.m_Dir = getLocoMotion()->getLookDir();
		}
		else m_pCamera->getViewRayByScreenPt(&ray, m_CurMouseX, m_CurMouseY);
	}
	int range = GetPickRange(this);
	if (range == 0)
		return 0;
	ray.m_Range = range;

	m_PickResult.liquids.resize(0);
	m_PickResult.intersect_actor = m_PickResult.intersect_block = m_PickResult.isIntersectLiquid = false;

	ActorExcludes excludes;
	excludes.addActorWithRiding(this);
	if (ignorecarried)
	{
		auto CarryComp = getCarryComponent();
		if (CarryComp)
		{
			auto* carrying = CarryComp->getCarringActor();
			if (carrying)
			{
				excludes.addActor(carrying);
			}
		}
	}

	m_PickType = (int)m_pWorld->pickAll(ray, &m_PickResult, excludes, (PICK_METHOD)picktype);

	return m_PickType;
}

std::vector<WCoord> PlayerControl::doPickBlockByItemSkill(int skillid, Rainbow::Vector3f &currentEyePos, Rainbow::Vector3f &currentDir)
{
	std::vector<WCoord> wCoordVec;
	const ItemSkillDef *skilldef = GetDefManagerProxy()->getItemSkillDef(skillid);
	if (skilldef)
	{
		if (skilldef->RangeType == 0)//单体
		{
			//射线方向
			Rainbow::Vector3f origin;
			Rainbow::Vector3f dir;
			if (m_ViewMode == CAMERA_TPS_OVERLOOK || m_ViewMode == CAMERA_TPS_BACK || m_ViewMode == CAMERA_TPS_BACK_2 || m_ViewMode == CAMERA_TPS_BACK_SHOULDER)
			{
				origin = WCoord(getPosition() + WCoord(0, BLOCK_SIZE / 2, 0)).toVector3();
				dir = m_pCamera->getLookDir();
			}
			else if (m_ViewMode == CAMERA_CUSTOM_VIEW
				&& getCameraConfigOption(CAMERA_OPTION_INDEX_CROSSHAIR) == CCT_BODY_EYES)
			{
				origin = (getPosition() + WCoord(0, BLOCK_SIZE / 2, 0)).toVector3();
				dir = getLocoMotion()->getLookDir();
			}
			else
			{
				MINIW::WorldRay ray;
				m_pCamera->getViewRayByScreenPt(&ray, m_CurMouseX, m_CurMouseY);
				origin = ray.m_Origin.toVector3();
				dir = ray.m_Dir;
			}
			currentEyePos = origin;
			dir  = MINIW::Normalize(dir);
			currentDir = dir;

			if (skilldef->SkillType == 0)//有目标
			{
				if (doPick(false) == 1
					&& (skilldef->Distance < 0.001 || skilldef->Distance > getDistanceToPos(m_PickResult.block.x*BLOCK_SIZE, m_PickResult.block.y*BLOCK_SIZE, m_PickResult.block.z*BLOCK_SIZE)))
				{
					wCoordVec.push_back(m_PickResult.block);
					WCoord placepos = NeighborCoord(m_PickResult.block, m_PickResult.face);
					wCoordVec.push_back(placepos);
				}
			}
			else
			{
				WCoord wcoord = CoordDivBlock(getPosition());
				//wcoord.y--;
				wCoordVec.push_back(wcoord);
			}
		}
		else if (skilldef->RangeType == 1)//射线	
		{
			//是否需要判断高度一样
			bool sameHight = false;
			//起始位置
			WCoord coord;
			//射线方向
			Rainbow::Vector3f origin;
			Rainbow::Vector3f dir;
			if (skilldef->SkillType == 0 && sameHight)//有目标
			{
				origin = WCoord(CoordDivBlock(getPosition() - WCoord(0, BLOCK_SIZE / 2, 0)) * BLOCK_SIZE).toVector3();
				WCoord hitcenter = coord * BLOCK_SIZE + WCoord(50, 0, 50);
				dir = (hitcenter - origin).toVector3();
			}
			else
			{
				if (m_ViewMode == CAMERA_TPS_OVERLOOK || m_ViewMode == CAMERA_TPS_BACK || m_ViewMode == CAMERA_TPS_BACK_2 || m_ViewMode == CAMERA_TPS_BACK_SHOULDER)
				{
					origin = WCoord(getPosition() + WCoord(0, BLOCK_SIZE / 2, 0)).toVector3();
					dir = m_pCamera->getLookDir();
				}
				else if (m_ViewMode == CAMERA_CUSTOM_VIEW
					&& getCameraConfigOption(CAMERA_OPTION_INDEX_CROSSHAIR) == CCT_BODY_EYES)
				{
					origin = (getPosition() + WCoord(0, BLOCK_SIZE / 2, 0)).toVector3();
					dir = getLocoMotion()->getLookDir();
				}
				else
				{
					MINIW::WorldRay ray;
					m_pCamera->getViewRayByScreenPt(&ray, m_CurMouseX, m_CurMouseY);
					origin = ray.m_Origin.toVector3();
					dir = ray.m_Dir;
				}
			}
			currentEyePos = origin;
			dir  = MINIW::Normalize(dir);
			currentDir = dir;

			if (skilldef->SkillType == 0)//有目标
			{
				if (doPick(false) == 1
					&& (skilldef->Distance < 0.001 || skilldef->Distance > getDistanceToPos(m_PickResult.block.x*BLOCK_SIZE, m_PickResult.block.y*BLOCK_SIZE, m_PickResult.block.z*BLOCK_SIZE)))
				{
					coord = m_PickResult.block;

					if ((CoordDivBlock(getPosition()).y - 1) == coord.y)
					{
						sameHight = true;
					}
				}
				else
				{
					return wCoordVec;
				}
			}
			else
			{
				coord = CoordDivBlock(getPosition());
			}

			dir  = MINIW::Normalize(dir);
			Rainbow::Vector3f dir2 = CrossProduct(Rainbow::Vector3f(0, 1.0f, 0), dir);
			dir2  = MINIW::Normalize(dir2);
			Rainbow::Vector3f dir3 = CrossProduct(dir2, dir);
			dir3  = MINIW::Normalize(dir3);
			map<long long, int> blockmap;

			float length = skilldef->RangeVal1;
			float width = skilldef->RangeVal2 / 2.0f;
			for (float x = 80.0f; x <= length; x += BLOCK_SIZE / 2.0f)
			{
				for (float y = -width; y <= width; y += BLOCK_SIZE / 2.0f)
				{
					for (float z = -width; z <= width; z += BLOCK_SIZE / 2.0f)
					{
						Rainbow::Vector3f dp = dir2 * y + dir3 * z;
						if (dp.LengthSqr() < width*width)
						{
							Rainbow::Vector3f blockPosV3 = origin + dir * x + dp;
							WCoord blockpos(CoordDivBlock(WCoord(blockPosV3.x, blockPosV3.y, blockPosV3.z)));

							int blockid = getWorld()->getBlockID(blockpos);
							const BlockDef* def = GetDefManagerProxy()->getBlockDef(blockid);
							if (blockid == 0 ||
								(def && (skilldef->TargetClass == 0 || skilldef->TargetClass == def->MineTool)))
							{
								if (blockmap.find(BLOCK_HASH_FUN(0, blockpos.y, blockpos.x, blockpos.z)) == blockmap.end())
								{
									if (wCoordVec.size() < 3000)
									{
										blockmap[BLOCK_HASH_FUN(0, blockpos.y, blockpos.x, blockpos.z)] = 1;
										wCoordVec.push_back(blockpos);
									}
									else
									{
										x = length + 1;
										y = width + 1;
										z = width + 1;
										break;
									}
								}
							}
						}
					}

				}
			}
		}
		else  //立体
		{
			float length = skilldef->RangeVal1 / 2.0f;
			float width = skilldef->RangeVal2 / 2.0f;
			float height = skilldef->RangeVal3 / 2.0f;
			//起始位置
			WCoord coord;
			//立方体方向
			Rainbow::Vector3f origin;
			Rainbow::Vector3f dir;
			if (skilldef->SkillType == 0)//有目标
			{
				origin = coord.toVector3();
				if (m_ViewMode == CAMERA_TPS_OVERLOOK || m_ViewMode == CAMERA_TPS_BACK || m_ViewMode == CAMERA_TPS_BACK_2 || m_ViewMode == CAMERA_TPS_BACK_SHOULDER)
				{
					dir = m_pCamera->getLookDir();
				}
				else if (m_ViewMode == CAMERA_CUSTOM_VIEW
					&& getCameraConfigOption(CAMERA_OPTION_INDEX_CROSSHAIR) == CCT_BODY_EYES)
				{
					dir = getLocoMotion()->getLookDir();
				}
				else
				{
					MINIW::WorldRay ray;
					m_pCamera->getViewRayByScreenPt(&ray, m_CurMouseX, m_CurMouseY);
					dir = ray.m_Dir;
				}

			}
			else if (skilldef->SkillType == 1)//无目标
			{
				if (m_ViewMode == CAMERA_TPS_OVERLOOK || m_ViewMode == CAMERA_TPS_BACK || m_ViewMode == CAMERA_TPS_BACK_2 || m_ViewMode == CAMERA_TPS_BACK_SHOULDER)
				{
					origin = WCoord(getPosition() + WCoord(0, BLOCK_SIZE / 2, 0)).toVector3();
					dir = m_pCamera->getLookDir();
				}
				else if (m_ViewMode == CAMERA_CUSTOM_VIEW
					&& getCameraConfigOption(CAMERA_OPTION_INDEX_CROSSHAIR) == CCT_BODY_EYES)
				{
					origin = (getPosition() + WCoord(0, BLOCK_SIZE / 2, 0)).toVector3();
					dir = getLocoMotion()->getLookDir();
				}
				else
				{
					MINIW::WorldRay ray;
					m_pCamera->getViewRayByScreenPt(&ray, m_CurMouseX, m_CurMouseY);
					origin = ray.m_Origin.toVector3();
					dir = ray.m_Dir;
				}
			}
			currentEyePos = origin;
			dir  = MINIW::Normalize(dir);
			currentDir = dir;
			if (skilldef->SkillType == 0)//有目标
			{
				if (doPick(false) == 1
					&& (skilldef->Distance < 0.001 || (skilldef->Distance < 0.001 || skilldef->Distance > getDistanceToPos(m_PickResult.block.x*BLOCK_SIZE, m_PickResult.block.y*BLOCK_SIZE, m_PickResult.block.z*BLOCK_SIZE))))
				{
					coord = m_PickResult.block * BLOCK_SIZE;
				}
				else
				{
					return wCoordVec;
				}
			}
			else
			{
				coord = getPosition();
			}

			//算出正方体x,y,z三轴
			float rotateYaw;
			float rotationPitch;
			Direction2PitchYaw(&rotateYaw, &rotationPitch, dir);
			Rainbow::Quaternionf rotation;
			//rotation.setEulerAngle(rotateYaw, rotationPitch, 0);
			rotation = AngleEulerToQuaternionf(Vector3f(rotationPitch, rotateYaw, 0));

			dir = rotation.GetAxisZ();
			Rainbow::Vector3f dir2 = rotation.GetAxisY();
			Rainbow::Vector3f dir3 = rotation.GetAxisX();
			map<long long, int> blockmap;
			for (float x = -length; x <= length; x += BLOCK_SIZE / 2.0f)
			{
				for (float y = -height; y <= height; y += BLOCK_SIZE / 2.0f)
				{
					for (float z = -width; z <= width; z += BLOCK_SIZE / 2.0f)
					{
						Rainbow::Vector3f blockPosV3 = origin + dir * x + dir2 * y + dir3 * z;
						WCoord blockpos(CoordDivBlock(WCoord(blockPosV3.x, blockPosV3.y, blockPosV3.z)));

						//判断方块类型是否一致
						int blockid = getWorld()->getBlockID(blockpos);
						const BlockDef* def = GetDefManagerProxy()->getBlockDef(blockid);
						if (blockid == 0 ||
							(def && (skilldef->TargetClass == 0 || skilldef->TargetClass == def->MineTool)))
						{
							if (blockmap.find(BLOCK_HASH_FUN(0, blockpos.y, blockpos.x, blockpos.z)) == blockmap.end())
							{
								if (wCoordVec.size() < 3000)
								{
									blockmap[BLOCK_HASH_FUN(0, blockpos.y, blockpos.x, blockpos.z)] = 1;
									wCoordVec.push_back(blockpos);
								}
								else
								{
									x = length + 1;
									y = height + 1;
									z = width + 1;
									break;
								}
							}
						}
					}

				}
			}


		}
	}
	return wCoordVec;
}


std::vector<WORLD_ID> PlayerControl::doPickActorByItemSkill(int skillid, WCoord &centerPos, Rainbow::Vector3f &currentEyePos, Rainbow::Vector3f &currentDir)
{
	std::vector<WORLD_ID> idvec;
	const ItemSkillDef *skilldef = GetDefManagerProxy()->getItemSkillDef(skillid);
	if (skilldef)
	{
		if (skilldef->RangeType == 0)//单体
		{
			if (skilldef->SkillType == 0)//有目标
			{
				if (doPick(false) == 2 && m_PickResult.actor && (skilldef->Distance < 0.001 || skilldef->Distance*skilldef->Distance > getSquareDistToActor(m_PickResult.actor->ToCast<ClientActor>())))
				{
					if (m_PickResult.actor && m_PickResult.actor->ToCast<ClientActor>()->canAttackByItemSkill(skillid, this))
					{
						idvec.push_back(m_PickResult.actor->getObjId());
					}
				}
			}
			else if (skilldef->SkillType == 1)//无目标
			{
				idvec.push_back(getObjId());
			}

			Rainbow::Vector3f origin;
			Rainbow::Vector3f dir;
			if (m_ViewMode == CAMERA_TPS_OVERLOOK || m_ViewMode == CAMERA_TPS_BACK || m_ViewMode == CAMERA_TPS_BACK_2 || m_ViewMode == CAMERA_TPS_BACK_SHOULDER)
			{
				origin = (getPosition() + WCoord(0, BLOCK_SIZE / 2, 0)).toVector3();
				dir = m_pCamera->getLookDir();
			}
			else if (m_ViewMode == CAMERA_CUSTOM_VIEW
				&& getCameraConfigOption(CAMERA_OPTION_INDEX_CROSSHAIR) == CCT_BODY_EYES)
			{
				origin = (getPosition() + WCoord(0, BLOCK_SIZE / 2, 0)).toVector3();
				dir = getLocoMotion()->getLookDir();
			}
			else
			{
				MINIW::WorldRay ray;
				if (m_pCamera != NULL && m_pCamera->getEngineCamera() != NULL) m_pCamera->getViewRayByScreenPt(&ray, m_CurMouseX, m_CurMouseY);
				origin = ray.m_Origin.toVector3();
				dir = ray.m_Dir;
			}
			currentEyePos = origin;
			dir  = MINIW::Normalize(dir);
			currentDir = dir;
		}
		else if (skilldef->RangeType == 1)//射线	
		{
			float length = skilldef->RangeVal1;
			float width = skilldef->RangeVal2 / 2.0f;
			int range = (int)max(length, width);
			CollideAABB box;
			getCollideBox(box);
			box.expand(range, range, range);
			std::vector<IClientActor *>tmpactors;
			m_pWorld->getActorsInBox(tmpactors, box);

			Rainbow::Vector3f origin;
			Rainbow::Vector3f dir;
			if (m_ViewMode == CAMERA_TPS_OVERLOOK || m_ViewMode == CAMERA_TPS_BACK || m_ViewMode == CAMERA_TPS_BACK_2 || m_ViewMode == CAMERA_TPS_BACK_SHOULDER)
			{
				origin = (getPosition() + WCoord(0, BLOCK_SIZE / 2, 0)).toVector3();
				dir = m_pCamera->getLookDir();
			}
			else if (m_ViewMode == CAMERA_CUSTOM_VIEW
				&& getCameraConfigOption(CAMERA_OPTION_INDEX_CROSSHAIR) == CCT_BODY_EYES)
			{
				origin = (getPosition() + WCoord(0, BLOCK_SIZE / 2, 0)).toVector3();
				dir = getLocoMotion()->getLookDir();
			}
			else
			{
				MINIW::WorldRay ray;
				m_pCamera->getViewRayByScreenPt(&ray, m_CurMouseX, m_CurMouseY);
				origin = ray.m_Origin.toVector3();
				dir = ray.m_Dir;
			}
			currentEyePos = origin;
			dir  = MINIW::Normalize(dir);
			currentDir = dir;

			WORLD_ID objOne = 0;
			if (skilldef->SkillType == 0)//有目标
			{
				if (doPick(false) != 2 || !m_PickResult.actor || (skilldef->Distance > 0.001 && skilldef->Distance*skilldef->Distance < getSquareDistToActor(m_PickResult.actor->ToCast<ClientActor>())))
				{
					return idvec;
				}
			}
			else
			{
				if (doPick(false) == 2 && m_PickResult.actor && (skilldef->RangeVal1*skilldef->RangeVal1 > getSquareDistToActor(m_PickResult.actor->ToCast<ClientActor>())))
				{
					if (m_PickResult.actor->ToCast<ClientActor>()->canAttackByItemSkill(skillid, this))
					{
						idvec.push_back(m_PickResult.actor->getObjId());
						objOne = m_PickResult.actor->getObjId();
					}
				}
			}

			for (size_t i = 0; i < tmpactors.size(); i++)
			{
				ClientActor *actor = static_cast<ClientActor*>(tmpactors[i]);
				if (actor == NULL) continue;
				if (!actor->canAttackByItemSkill(skillid, this))
				{
					continue;
				}
				WCoord originpos(origin.x, origin.y, origin.z);
				WCoord hitcenter(actor->getPosition().x, actor->getPosition().y + BLOCK_SIZE / 2, actor->getPosition().z);
				Rainbow::Vector3f dp = (hitcenter - originpos).toVector3();

				float t = DotProduct(dp, dir);
				if (t > 0 && t < length)
				{
					Rainbow::Vector3f tmp = CrossProduct(dp, dir);
					if (tmp.LengthSqr() < width*width && actor->getObjId() != getObjId() && objOne != actor->getObjId()) idvec.push_back(actor->getObjId());
				}
			}
		}
		else //立体
		{
			float length = skilldef->RangeVal1 / 2.0f;
			float width = skilldef->RangeVal2 / 2.0f;
			float height = skilldef->RangeVal3 / 2.0f;
			float range = max(length, width);
			range = max(range, height)*1.5f;

			//射线方向
			Rainbow::Vector3f origin;
			Rainbow::Vector3f dir;
			if (skilldef->SkillType == 0)//有目标
			{
				if (doPick(false) == 2 && m_PickResult.actor)
				{
					CollideAABB box;
					m_PickResult.actor->getCollideBox(box);
					origin = WCoord(box.centerX(), box.centerY(), box.centerZ()).toVector3();
					if (m_ViewMode == CAMERA_TPS_OVERLOOK)
					{
						dir = m_pCamera->getLookDir();
					}
					else if (m_ViewMode == CAMERA_CUSTOM_VIEW
						&& getCameraConfigOption(CAMERA_OPTION_INDEX_CROSSHAIR) == CCT_BODY_EYES)
					{
						dir = getLocoMotion()->getLookDir();
					}
					else
					{
						MINIW::WorldRay ray;
						m_pCamera->getViewRayByScreenPt(&ray, m_CurMouseX, m_CurMouseY);
						dir = ray.m_Dir;
					}
				}
				else
				{
					return idvec;
				}
			}
			else if (skilldef->SkillType == 1)//无目标
			{
				if (m_ViewMode == CAMERA_TPS_OVERLOOK || m_ViewMode == CAMERA_TPS_BACK || m_ViewMode == CAMERA_TPS_BACK_2 || m_ViewMode == CAMERA_TPS_BACK_SHOULDER)
				{
					origin = WCoord(getPosition() + WCoord(0, BLOCK_SIZE / 2, 0)).toVector3();
					dir = m_pCamera->getLookDir();
				}
				else if (m_ViewMode == CAMERA_CUSTOM_VIEW
					&& getCameraConfigOption(CAMERA_OPTION_INDEX_CROSSHAIR) == CCT_BODY_EYES)
				{
					origin = (getPosition() + WCoord(0, BLOCK_SIZE / 2, 0)).toVector3();
					dir = getLocoMotion()->getLookDir();
				}
				else
				{
					MINIW::WorldRay ray;
					m_pCamera->getViewRayByScreenPt(&ray, m_CurMouseX, m_CurMouseY);
					origin = ray.m_Origin.toVector3();
					dir = ray.m_Dir;
				}
			}
			currentEyePos = origin;
			dir  = MINIW::Normalize(dir);
			currentDir = dir;

			CollideAABB box;
			if (skilldef->SkillType == 0)//有目标
			{
				if (doPick(false) != 2 || !m_PickResult.actor || (skilldef->Distance > 0.001 && skilldef->Distance*skilldef->Distance < getSquareDistToActor(m_PickResult.actor->ToCast<ClientActor>())))
				{
					return idvec;
				}
				else
				{
					if (m_PickResult.actor) m_PickResult.actor->getCollideBox(box);
				}
			}
			else
			{
				getCollideBox(box);
			}
			centerPos.x = box.centerX();
			centerPos.y = box.centerY();
			centerPos.z = box.centerZ();

			box.expand((int)range, (int)range, (int)range);
			std::vector<IClientActor *>tmpactors;
			m_pWorld->getActorsInBox(tmpactors, box);

			//算出正方体x,y,z三轴
			float rotateYaw;
			float rotationPitch;
			Direction2PitchYaw(&rotateYaw, &rotationPitch, dir);
			Rainbow::Quaternionf rotation;
			//rotation.setEulerAngle(rotateYaw, rotationPitch, 0);
			rotation = AngleEulerToQuaternionf(Vector3f(rotationPitch, rotateYaw, 0));
			dir = rotation.GetAxisX();
			Rainbow::Vector3f dir2 = rotation.GetAxisY();
			Rainbow::Vector3f dir3 = rotation.GetAxisZ();

			for (size_t i = 0; i < tmpactors.size(); i++)
			{
				ClientActor *actor = static_cast<ClientActor*>(tmpactors[i]);
				if (actor == NULL) continue;
				if (!actor->canAttackByItemSkill(skillid, this))
				{
					continue;
				}
				CollideAABB hitbox;
				actor->getHitCollideBox(hitbox);
				WCoord originpos(box.centerX(), box.centerY(), box.centerZ());
				WCoord hitcenter(hitbox.centerX(), hitbox.centerY(), hitbox.centerZ());
				Rainbow::Vector3f dp = (hitcenter - originpos).toVector3();

				if (actor->getObjId() == getObjId())
				{
					continue;
				}
				float t = DotProduct(dp, dir);
				if (t == 0 || abs(t) > length)
				{
					continue;
				}
				t = DotProduct(dp, dir2);
				if (t == 0 || abs(t) > width)
				{
					continue;
				}
				t = DotProduct(dp, dir3);
				if (t == 0 || abs(t) > height)
				{
					continue;
				}

				idvec.push_back(actor->getObjId());
			}
		}
	}
	return idvec;
}

std::vector<WORLD_ID> PlayerControl::doPickPhysicsActorByItemSkill(int skillid, WCoord &centerPos, Rainbow::Vector3f &currentEyePos, Rainbow::Vector3f &currentDir)
{
	std::vector<WORLD_ID> idvec;
	const ItemSkillDef *skilldef = GetDefManagerProxy()->getItemSkillDef(skillid);
	if (skilldef)
	{
		if (skilldef->RangeType == 0)//单体
		{
			if (skilldef->SkillType == 0)//有目标
			{
				// 物理抓取--添加maxMass条件是否满足 line：physicsActorDef->Mass<=maxMass
				int maxMass = 0;
				for (int i = 0; i < (int)skilldef->SkillFuncions.size(); i++)
				{
					ItemSkillDef::SkillFuncionsDef* functiondef = (ItemSkillDef::SkillFuncionsDef*)&skilldef->SkillFuncions[i];
					if (functiondef->oper_id == 11)//物理抓取
					{
						maxMass = functiondef->func.physicsCatchfun.maxMass;
					}
					else if (functiondef->oper_id == 13) {//删除物理对象
						ActorVehicleAssemble* pVehicle = dynamic_cast<ActorVehicleAssemble*>(m_PickResult.actor);
						if (pVehicle) {
							shortcutItemUsed();
							pVehicle->setNeedClear();
							idvec.clear();
							return idvec;
						}
					}
				}

				if (doPick(false) == 2 &&
					m_PickResult.actor &&
					(skilldef->Distance < 0.001 || skilldef->Distance*skilldef->Distance > getSquareDistToActor(m_PickResult.actor->ToCast<ClientActor>())))
				{
					if (m_PickResult.actor)
					{
						PhysicsLocoMotion *loc = dynamic_cast<PhysicsLocoMotion *>(m_PickResult.actor->ToCast<ClientActor>()->getLocoMotion());
						VehicleAssembleLocoMotion* vehicleLoc = dynamic_cast<VehicleAssembleLocoMotion *>(m_PickResult.actor->ToCast<ClientActor>()->getLocoMotion());
						if (loc && loc->m_hasPhysActor)
						{
							// 这里判断是否为物理机械actor
							if (vehicleLoc)
							{
								if (m_PickResult.actor->getMass() <= maxMass)
								{
									idvec.push_back(m_PickResult.actor->getObjId());
								}
							}
							else
							{
								const PhysicsActorDef* physicsActorDef = GetDefManagerProxy()->getPhysicsActorDef(loc->m_ownerItemID);
								if (physicsActorDef && physicsActorDef->Mass <= maxMass)
								{
									idvec.push_back(m_PickResult.actor->getObjId());
								}
							}
						}
					}
				}
			}
			else if (skilldef->SkillType == 1)//无目标
			{
				idvec.push_back(getObjId());
			}

			Rainbow::Vector3f origin;
			Rainbow::Vector3f dir;
			//if(m_ViewMode == CAMERA_TPS_OVERLOOK || m_ViewMode == CAMERA_TPS_BACK)
			//{
			//	origin = (getPosition() + WCoord(0,BLOCK_SIZE/2,0)).toVector3();
			//	dir = m_pCamera->getLookDir();
			//}
			//else if (m_ViewMode == CAMERA_CUSTOM_VIEW
			//	&& getCameraConfigOption(CAMERA_OPTION_INDEX_CROSSHAIR) == CCT_BODY_EYES)
			//{
			//	origin = (getPosition() + WCoord(0, BLOCK_SIZE / 2, 0)).toVector3();
			//	dir = getLocoMotion()->getLookDir();
			//}
			//else
			//{
			MINIW::WorldRay ray;
			if (m_pCamera != NULL && m_pCamera->getEngineCamera() != NULL) m_pCamera->getViewRayByScreenPt(&ray, m_CurMouseX, m_CurMouseY);
			origin = ray.m_Origin.toVector3();
			dir = ray.m_Dir;
			//}
			currentEyePos = origin;
			dir  = MINIW::Normalize(dir);
			currentDir = dir;
		}
		else if (skilldef->RangeType == 1)//射线	
		{
			float length = skilldef->RangeVal1;
			float width = skilldef->RangeVal2 / 2.0f;
			int range = (int)max(length, width);
			CollideAABB box;
			getCollideBox(box);
			box.expand(range, range, range);
			std::vector<IClientActor *>tmpactors;
			m_pWorld->getActorsInBox(tmpactors, box);

			Rainbow::Vector3f origin;
			Rainbow::Vector3f dir;
			if (m_ViewMode == CAMERA_TPS_OVERLOOK || m_ViewMode == CAMERA_TPS_BACK || m_ViewMode == CAMERA_TPS_BACK_2 || m_ViewMode == CAMERA_TPS_BACK_SHOULDER)
			{
				origin = (getPosition() + WCoord(0, BLOCK_SIZE / 2, 0)).toVector3();
				dir = m_pCamera->getLookDir();
			}
			else if (m_ViewMode == CAMERA_CUSTOM_VIEW
				&& getCameraConfigOption(CAMERA_OPTION_INDEX_CROSSHAIR) == CCT_BODY_EYES)
			{
				origin = (getPosition() + WCoord(0, BLOCK_SIZE / 2, 0)).toVector3();
				dir = getLocoMotion()->getLookDir();
			}
			else
			{
				MINIW::WorldRay ray;
				m_pCamera->getViewRayByScreenPt(&ray, m_CurMouseX, m_CurMouseY);
				origin = ray.m_Origin.toVector3();
				dir = ray.m_Dir;
			}
			currentEyePos = origin;
			dir  = MINIW::Normalize(dir);
			currentDir = dir;

			WORLD_ID objOne = 0;
			if (skilldef->SkillType == 0)//有目标
			{
				if (doPick(false) != 2 || !m_PickResult.actor || (skilldef->Distance > 0.001 && skilldef->Distance*skilldef->Distance < getSquareDistToActor(m_PickResult.actor->ToCast<ClientActor>())))
				{
					return idvec;
				}
			}
			else
			{
				if (doPick(false) == 2 && m_PickResult.actor && (skilldef->RangeVal1*skilldef->RangeVal1 > getSquareDistToActor(m_PickResult.actor->ToCast<ClientActor>())))
				{
					PhysicsLocoMotion *loc = dynamic_cast<PhysicsLocoMotion *>(m_PickResult.actor->ToCast<ClientActor>()->getLocoMotion());
					if (loc && loc->m_hasPhysActor)
					{
						idvec.push_back(m_PickResult.actor->getObjId());
						objOne = m_PickResult.actor->getObjId();
					}
				}
			}

			for (size_t i = 0; i < tmpactors.size(); i++)
			{
				ClientActor *actor = static_cast<ClientActor*>(tmpactors[i]);
				if (actor == NULL) continue;
				PhysicsLocoMotion *loc = dynamic_cast<PhysicsLocoMotion *>(actor->getLocoMotion());
				if (loc == NULL)
				{
					continue;
				}
				if (loc->m_hasPhysActor == false)
				{
					continue;
				}
				WCoord originpos(origin.x, origin.y, origin.z);
				WCoord hitcenter(actor->getPosition().x, actor->getPosition().y + BLOCK_SIZE / 2, actor->getPosition().z);
				Rainbow::Vector3f dp = (hitcenter - originpos).toVector3();

				float t = DotProduct(dp, dir);
				if (t > 0 && t < length)
				{
					Rainbow::Vector3f tmp = CrossProduct(dp, dir);
					if (tmp.LengthSqr() < width*width && actor->getObjId() != getObjId() && objOne != actor->getObjId()) idvec.push_back(actor->getObjId());
				}
			}
		}
		else //立体
		{
			float length = skilldef->RangeVal1 / 2.0f;
			float width = skilldef->RangeVal2 / 2.0f;
			float height = skilldef->RangeVal3 / 2.0f;
			float range = max(length, width);
			range = max(range, height)*1.5f;

			//射线方向
			Rainbow::Vector3f origin;
			Rainbow::Vector3f dir;
			if (skilldef->SkillType == 0)//有目标
			{
				if (doPick(false) == 2 && m_PickResult.actor)
				{
					CollideAABB box;
					m_PickResult.actor->getCollideBox(box);
					origin = WCoord(box.centerX(), box.centerY(), box.centerZ()).toVector3();
					if (m_ViewMode == CAMERA_TPS_OVERLOOK)
					{
						dir = m_pCamera->getLookDir();
					}
					else if (m_ViewMode == CAMERA_CUSTOM_VIEW
						&& getCameraConfigOption(CAMERA_OPTION_INDEX_CROSSHAIR) == CCT_BODY_EYES)
					{
						dir = getLocoMotion()->getLookDir();
					}
					else
					{
						MINIW::WorldRay ray;
						m_pCamera->getViewRayByScreenPt(&ray, m_CurMouseX, m_CurMouseY);
						dir = ray.m_Dir;
					}
				}
				else
				{
					return idvec;
				}
			}
			else if (skilldef->SkillType == 1)//无目标
			{
				if (m_ViewMode == CAMERA_TPS_OVERLOOK || m_ViewMode == CAMERA_TPS_BACK || m_ViewMode == CAMERA_TPS_BACK_2 || m_ViewMode == CAMERA_TPS_BACK_SHOULDER)
				{
					origin = WCoord(getPosition() + WCoord(0, BLOCK_SIZE / 2, 0)).toVector3();
					dir = m_pCamera->getLookDir();
				}
				else if (m_ViewMode == CAMERA_CUSTOM_VIEW
					&& getCameraConfigOption(CAMERA_OPTION_INDEX_CROSSHAIR) == CCT_BODY_EYES)
				{
					origin = (getPosition() + WCoord(0, BLOCK_SIZE / 2, 0)).toVector3();
					dir = getLocoMotion()->getLookDir();
				}
				else
				{
					MINIW::WorldRay ray;
					m_pCamera->getViewRayByScreenPt(&ray, m_CurMouseX, m_CurMouseY);
					origin = ray.m_Origin.toVector3();
					dir = ray.m_Dir;
				}
			}
			currentEyePos = origin;
			dir  = MINIW::Normalize(dir);
			currentDir = dir;

			CollideAABB box;
			if (skilldef->SkillType == 0)//有目标
			{
				if (doPick(false) != 2 || !m_PickResult.actor || (skilldef->Distance > 0.001 && skilldef->Distance*skilldef->Distance < getSquareDistToActor(m_PickResult.actor->ToCast<ClientActor>())))
				{
					return idvec;
				}
				else
				{
					if (m_PickResult.actor) m_PickResult.actor->getCollideBox(box);
				}
			}
			else
			{
				getCollideBox(box);
			}
			centerPos.x = box.centerX();
			centerPos.y = box.centerY();
			centerPos.z = box.centerZ();

			box.expand((int)range, (int)range, (int)range);
			std::vector<IClientActor *>tmpactors;
			m_pWorld->getActorsInBox(tmpactors, box);

			//算出正方体x,y,z三轴
			float rotateYaw;
			float rotationPitch;
			Direction2PitchYaw(&rotateYaw, &rotationPitch, dir);
			Rainbow::Quaternionf rotation;
			//rotation.setEulerAngle(rotateYaw, rotationPitch, 0);
			rotation = AngleEulerToQuaternionf(Vector3f(rotationPitch, rotateYaw, 0));
			dir = rotation.GetAxisX();
			Rainbow::Vector3f dir2 = rotation.GetAxisY();
			Rainbow::Vector3f dir3 = rotation.GetAxisZ();

			for (size_t i = 0; i < tmpactors.size(); i++)
			{
				ClientActor *actor = static_cast<ClientActor*>(tmpactors[i]);
				if (actor == NULL) continue;
				PhysicsLocoMotion *loc = dynamic_cast<PhysicsLocoMotion *>(actor->getLocoMotion());
				if (loc == NULL)
				{
					continue;
				}
				if (loc->m_hasPhysActor == false)
				{
					continue;
				}
				CollideAABB hitbox;
				actor->getHitCollideBox(hitbox);
				WCoord originpos(box.centerX(), box.centerY(), box.centerZ());
				WCoord hitcenter(hitbox.centerX(), hitbox.centerY(), hitbox.centerZ());
				Rainbow::Vector3f dp = (hitcenter - originpos).toVector3();

				if (actor->getObjId() == getObjId())
				{
					continue;
				}
				float t = DotProduct(dp, dir);
				if (t == 0 || abs(t) > length)
				{
					continue;
				}
				t = DotProduct(dp, dir2);
				if (t == 0 || abs(t) > width)
				{
					continue;
				}
				t = DotProduct(dp, dir3);
				if (t == 0 || abs(t) > height)
				{
					continue;
				}

				idvec.push_back(actor->getObjId());
			}
		}
	}
	return idvec;
}

void PlayerControl::onTriggerUseAction()
{
}

void PlayerControl::onUseActionEnd()
{
}

/*
20210823:使用背包道具兑换其他背包道具 codeby：wangyu
*/
void PlayerControl::doExchangeItems(int useitemid, int usenum, int gainitemid, int gainnum, int type)
{
	int result = 0;
	result = ClientPlayer::exchangeItems(useitemid, usenum, gainitemid, gainnum);
	if (MINIW::ScriptVM::game())
	{
		MINIW::ScriptVM::game()->callFunction("doExchangeItemResult", "ii", type, result);
	}
}

void PlayerControl::clearAllInputState()
{
	if (GetClientInfoProxy()->isMobile())
	{
		if (m_TouchCtrl) m_TouchCtrl->ResetAllInput();
	}
	else
	{
		if (m_PCCtrl) m_PCCtrl->ResetAllInput();

		m_InputInfo->flyUp = 0;
		m_InputInfo->moveForward = 0;
		m_InputInfo->moveStrafe = 0;

		m_InputInfo->leftClickDown = false;
		m_InputInfo->rightClickDown = false;
		m_InputInfo->keyEClickDown = false;
		m_InputInfo->leftClick = false;
		m_InputInfo->rightClick = false;
		m_InputInfo->leftClickUp = false;
		m_InputInfo->rightClickUp = false;
		m_InputInfo->checkRightClickUp = false;
		m_InputInfo->sneak = false;
		m_InputInfo->dropItem = false;
		m_InputInfo->jump = false;
		m_InputInfo->triggerJump = false;
		m_InputInfo->jumpRelease = false;
		m_InputInfo->wingState = 0;
	}
	setStill();
}



//int PlayerControl::onInputEvent(const Rainbow::InputEvent &inevent)
//{
//	SandBoxInputEvent inputEvt;
//	ConvertToSandboxInputEvent(inevent, inputEvt);
//	return onInputEvent(inputEvt);
//}

void PlayerControl::SetAllowInput( bool allow, size_t event, int keyvalue)
{	
	s_isAllowInput = allow;

	if (allow)
	{
		s_forbidden_event.clear();
		s_forbidden_keyvalue.clear();
	}
	else
	{
		s_forbidden_event[event] = true;
		s_forbidden_keyvalue[keyvalue] = true;
	}
}

bool PlayerControl::GetAllowInput(size_t event, int keyvalue)
{
	if (Rainbow::InputEvent::kKeyDown == event)
	{
		if (Rainbow::GetConsole()->IsShow())
		{
			return false;
		}
	}

	if(s_forbidden_event.find(event) != s_forbidden_event.end()
		&& s_forbidden_keyvalue.find(keyvalue) != s_forbidden_keyvalue.end())
	{
		return false;
	}
	return true;
	//return this->s_isAllowInput;
}

void PlayerControl::ClearForbiddenEvent()
{
	s_forbidden_event.clear_dealloc();
	s_forbidden_keyvalue.clear_dealloc();
}

int PlayerControl::onInputEvent(const Rainbow::InputEvent &ev)
{
	//WarningStringMsg("======PlayerControl::onInputEvent(%f,%f)======", ev.mousePosition.x, ev.mousePosition.y);
	bool flymode = getFlying();

	if (GetClientInfoProxy()->isMobile())
	{
		if (m_TouchCtrl && m_TouchCtrl->onInputEvent(ev))
		{
		}
		else
		{
			if (m_PCCtrl) m_PCCtrl->onInputEvent(ev);
		}
	}
	else
	{
		if (m_PCCtrl) m_PCCtrl->onInputEvent(ev);
	}

	if (flymode != getFlying())
	{
		setFlying(flymode);
	}
	return 0;
}

void PlayerControl::ConvertToSandboxInputEvent(const Rainbow::InputEvent& inevent, SandBoxInputEvent& sandboxEvt)
{
	//if (GetClientInfoProxy()->isMobile())
	//{
	//	if (m_TouchCtrl)
	//	{
	//		m_TouchCtrl->ConvertToPlayerInputEvent(inevent, sandboxEvt);
	//	}
	//	else if (m_PCCtrl)
	//	{
	//		m_PCCtrl->ConvertToPlayerInputEvent(inevent, sandboxEvt);
	//	}
	//}
	//else
	//{
	//	if (m_PCCtrl) m_PCCtrl->ConvertToPlayerInputEvent(inevent, sandboxEvt);
	//}
}

bool PlayerControl::carryActor(ClientActor *actor, WCoord pos /* = WCoord(0, -1, 0) */)
{
	auto CarryComp = getCarryComponent();
	if (CarryComp && CarryComp->isCarrying() && actor == NULL)  //·ÅÏÂ¿¸ÆðµÄÉúÎï
		getBody()->playAttack();
	return ClientPlayer::carryActor(actor, pos);
}
void PlayerControl::onOperateEnded()
{
	if (m_CurOperate != PLAYEROP_NULL)
	{
		// if (isDoubleWeapon(getCurToolID()))//双持武器播放对应IDLE
		// {
		// 	m_CameraModel->playHandAnim(FPS_DOUBLEWEAPON_IDLE);
		// }
		// else
		// {
		// 	m_CameraModel->playIdleAnim();
		// }
		m_CameraModel->playIdleAnim();
		
		if (m_OperateTicks >= 5 && m_CurOperate != PLAYEROP_EATFOOD && m_CurOperate != PLAYEROP_DRINKWATER) m_SwitchTick = 0;
		else m_SwitchTick = 5;
	}

	ClientPlayer::onOperateEnded();
}

float PlayerControl::getDigProgress()
{
	if (m_CurOperate == PLAYEROP_DIG)
	{
		if (m_OperateTotalTicks == 0) return -1.0f;
		else
		{
			const ToolDef *tooldef = GetDefManagerProxy()->getToolDef(getCurToolID());
			const ItemDef *itemdef = GetDefManagerProxy()->getItemDef(getCurToolID());
		    return Rainbow::Clamp(float(m_OperateTicks) / m_OperateTotalTicks, 0.0f, 1.0f);
		}
	}
	else return -1.0f;
}

float PlayerControl::getReviveProgress()//获取救援进度
{
	return m_ReviveProgress;
	//return -1.0f;
}

void PlayerControl::setCircleProgress(float p)
{
	m_ReviveProgress = p;
}

int PlayerControl::getDigBlockID()
{
	if (m_CurOperate == PLAYEROP_DIG)
	{
		return m_CurDigBlockID;
	}
	else return 0;
}

float PlayerControl::getCollideInFaceX()
{
	return m_PickResult.facepoint.x;
}

float PlayerControl::getCollideInFaceY()
{
	return m_PickResult.facepoint.y;
}

float PlayerControl::getCollideInFaceZ()
{
	return m_PickResult.facepoint.z;
}

bool PlayerControl::pickLiquid(int &x, int &y, int &z)
{
	int range = GetPickRange(this);
	if (range == 0)
		return false;
	MINIW::WorldRay ray;
	m_pCamera->getViewRayByScreenPt(&ray, m_CurMouseX, m_CurMouseY);
	ray.m_Range = range;

	IntersectResult result;
	result.intersect_actor = result.intersect_block = false;

	if (m_pWorld->pickGround(ray, &result, PICK_METHOD_CLICKLIQUID))
	{
		x = result.block.x;
		y = result.block.y;
		z = result.block.z;
		return true;
	}
	else return false;
}

void PlayerControl::setInputActive(bool enble)
{
	m_EnableInput = enble;
	if (m_EnableInput)
	{
		//int w = Root::getSingleton().getClientWidth();
		//int h = Root::getSingleton().getClientHeight();
		int w, h = 0;
		GetClientInfoProxy()->getClientWindowSize(w, h);
		Rainbow::SetCursorPos(w / 2, h / 2);

	}
}

void PlayerControl::setActionInputActive(bool enble)
{
	m_EnableActionInput = enble;
}

void PlayerControl::setMoveInputActive(bool enble)
{
	m_EnableMoveInput = enble;
}

void PlayerControl::triggerHeadshotTip(bool isDead)
{
	if (GetClientInfoProxy()->isPC())
		m_PCCtrl->triggerHeadshotTip(isDead);
	else
		m_TouchCtrl->triggerHeadshotTip(isDead);

	Rainbow::GetMusicManager().PlaySound2D("sounds/misc/headshot.ogg", 1.0);
}

void PlayerControl::triggerNormalshotTip(bool isDead)
{
	if (GetClientInfoProxy()->isPC())
		m_PCCtrl->triggerNormalshotTip(isDead);
	else
		m_TouchCtrl->triggerNormalshotTip(isDead);

	Rainbow::GetMusicManager().PlaySound2D("sounds/misc/bodyshot.ogg", 1.0);
}

void PlayerControl::triggerVehicleshotTip()
{
	if (GetClientInfoProxy()->isPC())
		m_PCCtrl->triggerNormalshotTip();
	else
		m_TouchCtrl->triggerNormalshotTip();
}

void PlayerControl::resetInput()
{
	m_InputInfo->moveForward = 0;
	m_InputInfo->moveStrafe = 0;
}

void PlayerControl::setAllKeyBindState(bool b)
{
	if (b)
		GameSettings::GetInstance().enableAllKeyBind();
	else
		GameSettings::GetInstance().disableAllKeyBind();
}

void PlayerControl::setOneKeyBindState(bool b, int keycode)
{
	GameSettings::GetInstance().setOneKeyBindState(b, keycode);
}

void PlayerControl::setMoveUp(int dir) //-1: 往下, 1: 往上
{
	m_MoveUp = dir;
}

void PlayerControl::cancelMoveUp(int dir)
{
	if (m_MoveUp == dir) m_MoveUp = 0;
}

bool PlayerControl::setJumping(bool b)
{
	//跳跃是否中断自动寻路
	if (getNavigator() && getNavigator()->getPath()) {
		if (canControl() && b) {
			getNavigator()->clearPathEntity();
		}
	}
	
	if (!checkCanJump() && b)
		return false;

	if (b)
	{
		auto temperatureComponent = getTemperatureComponent();
		if (temperatureComponent) temperatureComponent->ShakeImpactDuration();
	}

	LivingAttrib* livingAttrib = getLivingAttrib();
	if (livingAttrib && livingAttrib->getBuffEffectBankInfo(BuffAttrType::BUFFATTRT_FORBID_OPERATE)) {
		return false;
	}
	
	if (!ClientPlayer::setJumping(b))
		return false;


	if (b)
		m_CameraModel->playJumpAnim();

	auto RidComp = getRiddenComponent();
	if (RidComp && RidComp->isRiding())
	{
		ActorHorse* riding = dynamic_cast<ActorHorse*>(RidComp->getRidingActor());
		if (riding)
		{
			if (b && riding->needSetZoomByJump())
			{
				m_pCamera->setZoomInOut(m_pCamera->getFov() - 15, 10, 6);
			}
			else
			{
				m_pCamera->disableZoom();
			}
		}
	}
	return true;
}

void PlayerControl::setMoveForward(float speed)
{
	m_MoveForward = speed;
}

void PlayerControl::setMoveStrafing(float speed)
{
	m_MoveRight = speed;
}

void PlayerControl::setFlying(bool b)
{
	bool isflying = getFlying();
	ClientPlayer::setFlying(b);

	if (isflying != b)
	{
		if (!GAMERECORD_INTERFACE_EXEC(isRecordPlaying(), false))
		{
			//enter fly mode
			if (b)
			{
				m_StateController->performMoveTransition("ToFly");
				if (getLocoMotion()->m_Motion.y <= 10.0f) getLocoMotion()->m_Motion.y = 10.0f;
			}
			//exit fly mode
			else
			{
				m_StateController->performMoveTransition("ToIdle");
			}
			//ge GetGameEventQue().postSimpleEvent("GIE_FLYMODE_CHANGE");
			if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
				MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GIE_FLYMODE_CHANGE", MNSandbox::SandboxContext(nullptr));
		}
		else
		{
			if (b)
			{
				if (getLocoMotion()->m_Motion.y <= 10.0f) getLocoMotion()->m_Motion.y = 10.0f;
			}
		}
	}
}

bool PlayerControl::checkCanJump()
{
	//篮球模式下不可以跳跃
	bool canJump = true;
	if (getOPWay() == PLAYEROP_WAY_BASKETBALLER)
	{
		canJump = false;
	}

	//中了夹子陷阱不可以跳跃
	if (getLivingAttrib()->hasBuff(LOCK_MOB_BUFF))
	{
		canJump = false;
	}
	
	return canJump;
}

void PlayerControl::setSneaking(bool b)
{
	if (b && !canControl())
		b = false;

	ClientPlayer::setSneaking(b);
}

void PlayerControl::EnableUpdateRun(bool b)
{
	m_enableUpdateRun = b;
}

void PlayerControl::updateRun()
{
	if (!m_enableUpdateRun)
		return;

	// 检查枪械射击禁止run状态是否已结束
	if (m_gunFireDisableRun)
	{
		if (Rainbow::Timer::getSystemTick() >= m_gunFireDisableRunEndTime)
		{
			m_gunFireDisableRun = false;
			m_gunFireDisableRunEndTime = 0;
		}
	}

	if (getRun()) //从其他星球切到萌眼星或增益Buff消失后需取消奔跑状态 code-by:lizb
	{
		if (m_pWorld && m_pWorld->getCurMapID() == MAPID_MENGYANSTAR)
		{
			LivingAttrib *pliving = getLivingAttrib();
			if (pliving && pliving->hasBuff(74))
			{
				if (isNewMoveSyncSwitchOn())
					changeMoveFlag(IFC_Run, false);
				else
					setRun(false);
				return;
			}
		}


		//2021/8/16 增加判断.修复奔跑过程,禁止奔跑没有停止奔跑bug  codeby：wanggengyang
		PlayerAttrib* playerAttrib = getPlayerAttrib();
		if (playerAttrib && playerAttrib->hasStatusEffect(STATUS_EFFECT_FORBIDRUN))
		{
			if (isNewMoveSyncSwitchOn())
				changeMoveFlag(IFC_Run, false);
			else
				setRun(false);
		}

	}

	// 关闭疾跑
	if (g_WorldMgr && g_WorldMgr->getPlayerPermit(ENABLE_RUN) == false)
	{
		return;
	}

	if (!m_InputInfo->triggerRun)
	{
		return;
	}
	if (getViewMode() == CAMERA_TPS_OVERLOOK)
	{
		return;
	}
	auto RidComp = getRiddenComponent();
	if (RidComp && RidComp->isRiding())
	{
		return;
	}
	if (getRun())
	{
		return;
	}
	if (!getLocoMotion()->m_OnGround)
	{
		return;
	}
	if (m_StateController->getActionState() == "ChargeAttack")
	{
		return;
	}

	PlayerAttrib* playerAttrib = getPlayerAttrib();
	if (!playerAttrib)
	{
		assert(false);
		return;
	}

	ConstAtLua& luaConst = *GetLuaInterfaceProxy().get_lua_const();
	if (!playerAttrib->isStrengthEnough(luaConst.strength_consumption_of_running_per_second))
	{
		if (isNewMoveSyncSwitchOn())
		{
			if (getRun())
				changeMoveFlag(IFC_Run, false);
		}
		else
			setRun(false);
		GetLuaInterfaceProxy().showGameTips(1571);
		return;
	}
	if (luaConst.be_forbidden_to_run_with_exhaustion && playerAttrib->isExhausted())
	{
		if (isNewMoveSyncSwitchOn())
		{
			if (getRun())
				changeMoveFlag(IFC_Run, false);
		}
		else
			setRun(false);
		//ge GetGameEventQue().postFlashExhaustionWarning();
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GE_FLASH_EXHAUSTION_WARNING", MNSandbox::SandboxContext(nullptr));
		}
		return;
	}

	//add by navy
	if (playerAttrib->hasStatusEffect(STATUS_EFFECT_FORBIDRUN))//禁止奔跑
		return;

	// 防御状态下禁止奔跑
	if (IsInDefanceState()) return;

	if (isNewMoveSyncSwitchOn())
	{
		if (!getRun())
			changeMoveFlag(IFC_Run, true);
	}
	else
		setRun(true);
}

void PlayerControl::setRun(bool enable)
{
	if (getGunLogical() != NULL)
	{
		if (enable)
		{
			if (!getGunLogical()->getZoom())
			{
				m_pCamera->setZoomInOut(m_pCamera->getFov() + 13, 3, 2);
			}
		}
		else
		{
			//因为设置了狙击枪倍镜后，触发跑步时不会再调用GameCamera::setZoomInOut，所以停止跑步时，如果是狙击枪就不设置镜头
			const GunDef* pGunDef = getGunLogical()->getGunDef();
			if (pGunDef != NULL && pGunDef->ID == 15004)
			{
			}
			else
				m_pCamera->disableZoom();
		}
	}
	ClientActor::setRun(enable);
	if (getLocoMotion())
	{
		getLocoMotion()->m_InRun = enable;
	}
}

void PlayerControl::doReload(int bulletid, int num, bool isCustomGun)
{
	if (isCustomGun)
	{
		CustomGunUseComponent* comp = sureCustomGunComponent();
		int max = comp->maxAmmo();
		int now = comp->getMagazine();
		if (num + now > max)
			num = max - now;

		comp->doCostBulletItem(bulletid, num);
		comp->doReload(now + num);
	}
	else
	{
		if (getGunLogical() == NULL || !getGunLogical()->canReload()) return;
		int itemcount = getGunLogical()->getBulletNum();
		int maxMagazine = getGunLogical()->getMaxMagazines();
		int curMagazine = getGunLogical()->getMagazine();
		num = maxMagazine - curMagazine;
		if (num > itemcount)
			num = itemcount;

		getGunLogical()->doCostBulletItem(bulletid, num);
		getGunLogical()->doReload(curMagazine + num);
	}
}

//天赋：回弹专用（主机处理自己）
void PlayerControl::doReloadWithoutCheck(int num)
{
	CustomGunUseComponent* comp = sureCustomGunComponent();
	int max = comp->maxAmmo();
	num += comp->getMagazine();
	if (num > max)
		num = max;
	comp->doReload(num);
}

void PlayerControl::doGunFire(int id)
{
	ClientPlayer::doGunFire(id);
	if (m_CameraModel->isShow())
	{
		m_CameraModel->playItemMotion(getGunComponent()->getGunDef()->ShootEffect);
	}
	
	// 枪械射击时临时取消run状态
	if (ClientPlayer::getRun())
	{
		// 获取枪械射击CD并设置禁止run状态的持续时间
		int gunFireCD = 0;
		
		// 优先检查新枪械系统
		if (m_GunHoleState == GunHoldState::CUSTOMGUN)
		{
			CustomGunUseComponent* customGunComp = sureCustomGunComponent();
			if (customGunComp)
			{
				gunFireCD = (int)(60000.0f / customGunComp->rpm()); // 转换为毫秒
			}
		}
		else
		{
			// 传统枪械系统
			gunFireCD = getGunComponent()->getGunDef()->FireInterval;
		}
		
		// 设置禁止run状态的持续时间为射击CD的2倍
		if (gunFireCD > 0)
		{
			m_gunFireDisableRun = true;
			m_gunFireDisableRunEndTime = Rainbow::Timer::getSystemTick() + (gunFireCD * 2);
		}
	}
}

void PlayerControl::fall(float fallDist)
{
	ClientPlayer::fall(fallDist);
	addAchievement(2, ACHIEVEMENT_FALLDIST, 0, int(fallDist));
}

void PlayerControl::setSightingTelescope()
{
	if (m_GunHoleState == GunHoldState::CUSTOMGUN)
	{
		ActionIdleStateGunAdvance* idleState = GetIdleStateGunAdvance();
		if (idleState) idleState->HandleAim();
		return;
	}
	if (getGunLogical() == NULL) return;
	getGunLogical()->setZoom(!getGunLogical()->getZoom());
}

bool PlayerControl::hasSightingTelescope()
{
	if (m_GunHoleState == GunHoldState::CUSTOMGUN)
	{
		return true;
	}
	return getGunLogical()->getSightAim() > 0 ? true : false;
}

void PlayerControl::setHookObj(WORLD_ID obj, bool include_me)
{
	PB_SetHookCH setHookCH;
	setHookCH.set_objid(getObjId());
	setHookCH.set_hookid(obj);
	GetGameNetManagerPtr()->sendToHost(PB_SET_HOOK_CH, setHookCH);
	ClientPlayer::setHookObj(obj);
}

int PlayerControl::GetExploitCheckBlockId(bool isDigBlockId)
{
	int blockId = 0;
	int toolId = getCurToolID();
	if (toolId == ITEM_WOODEN_BUCKET || toolId == ITEM_BUCKET || toolId == ITEM_TITANIUM_BUCKET || toolId == ITEM_SMALL_GLASS_BOTTLE)
	{
		//if (isDigBlockId)
		//{
		//	WCoord nearBlockCoord = NeighborCoord(m_PickResult.block, m_PickResult.face);
		//	blockId = getWorld()->getBlockID(nearBlockCoord);
		//}
		//else
		//{
		blockId = getWorld()->getBlockID(m_PickResult.block);
		//}
	}
	else if (toolId == ITEM_FLINTSTEEL || toolId == BLOCK_SMALL_TORCH)
	{
		WCoord nearBlockCoord = NeighborCoord(m_PickResult.block, m_PickResult.face);
		blockId = getWorld()->getBlockID(nearBlockCoord);
	}
	else
	{
		if (isDigBlockId)
		{
			blockId = getDigBlockID();
		}
		else
		{
			blockId = getWorld()->getBlockID(m_PickResult.block);
		}
	}
	return blockId;
}
