#include "BlockKeyPedestal.h"
#include "special_blockid.h"
#include "IClientPlayer.h"
#include "section.h"
#include "world.h"
#include "container_keypedestal.h"
#include "ActorVehicleAssemble.h"
#include "BlockMaterialMgr.h"
#include "ClientPlayer.h"
#include "DefManagerProxy.h"
#include "IPlayerControl.h"
//#include "OgreMaterial.h"
#include "BlockGeom.h"
#include "ClientVacantBoss.h"
#include "ClientActorManager.h"
#include "ChunkGen_Normal.h"
#include "EffectParticle.h"
#include "world.h"
#include "EffectManager.h"
#include "PlayerControl.h"
IMPLEMENT_BLOCKMATERIAL(BlockKeyPedestal)
BlockKeyPedestal::BlockKeyPedestal()
{
	//for (int i = 0; i < 2; i++)
	//{
	//	m_StateMtl[i] = NULL;
	//}
	memset(m_stateMtlsIndex, UINT_MAX, sizeof(m_stateMtlsIndex));
}

BlockKeyPedestal::~BlockKeyPedestal()
{
	//ENG_RELEASE(m_StateMtl[1]);
	memset(m_stateMtlsIndex, UINT_MAX, sizeof(m_stateMtlsIndex));
}

void BlockKeyPedestal::init(int resid)
{
	ModelBlockMaterial::init(resid);

	SetToggle(BlockToggle_HasContainer, true);
	RenderBlockMaterial* mtl = NULL;
	string rootStr = "volcanoaltar_damaged";
	if (BLOCK_BROKEN_PEDESTAL == m_BlockResID)
	{
		m_stateMtlsIndex[0] = m_defaultMtlIndex;
	}
	else
	{
		if (BLOCK_MILA_STAR_PEDESTAL == m_BlockResID)
		{
			rootStr = "volcanoaltar_earth";
		}
		else if (BLOCK_FLAME_STAR_PEDESTAL == m_BlockResID)
		{
			rootStr = "volcanoaltar_flame";
		}
		else if (BLOCK_Q_EYE_STAR_PEDESTAL == m_BlockResID)
		{
			rootStr = "volcanoaltar_horas";
		}
		for (int i = 0; i < Mtl_Num; i++)
		{
			string lastStr = 0 == i ? "_off" : "_on";
			//m_StateMtl[i] 
			mtl = g_BlockMtlMgr.createRenderMaterial((rootStr + lastStr).c_str(), GetBlockDef(), GETTEX_WITHDEFAULT);
			m_stateMtlsIndex[i] = getRenderMtlMgr().addMtl(mtl);
			ENG_RELEASE(mtl);
		}
	}
	//m_Mtl = m_StateMtl[0];
}

void BlockKeyPedestal::initDefaultMtl()
{
	Super::initDefaultMtl();

	RenderBlockMaterial* mtl = NULL;
	string rootStr = "volcanoaltar_damaged";
	if (BLOCK_BROKEN_PEDESTAL == m_BlockResID)
	{
		//m_StateMtl[0] =
		mtl = g_BlockMtlMgr.createRenderMaterial(rootStr.c_str(), GetBlockDef(), GETTEX_WITHDEFAULT);
		m_defaultMtlIndex = getRenderMtlMgr().addMtl(mtl);
		ENG_RELEASE(mtl);
	}
}

void BlockKeyPedestal::onBlockPlacedBy(World* pworld, const WCoord& blockpos, IClientPlayer* player)
{
	ClientPlayer* playerTmp = player->GetPlayer();
	if (!playerTmp) return;
	int placeDir = playerTmp->getCurPlaceDir();
	if (placeDir == DIR_NEG_Y || placeDir == DIR_POS_Y)
	{
		placeDir = DIR_NEG_X;
	}
	pworld->setBlockData(blockpos, placeDir);
}

void BlockKeyPedestal::onNotify(World* pworld, const WCoord& blockpos, int blockid)
{
	if (!pworld || !pworld->getContainerMgr())
	{
		return;
	}
// 	KeyPedestalContainer* container = dynamic_cast<KeyPedestalContainer*>(pworld->getContainerMgr()->getContainer(blockpos));
// 	if (!container)
// 	{
// 		return;
// 	}
	int blockData = pworld->getBlockData(blockpos);
	int state = blockData >> 3;
	WCoord up(blockpos.x, blockpos.y + 1, blockpos.z);
	int upResId = pworld->getBlockID(up);
	int selfResId = pworld->getBlockID(blockpos);

	if (1 == state/*container->getPedestalState()*/)
	{
		bool unactive = false;

		// 迷拉星（地球）
		if (BLOCK_MILA_STAR_PEDESTAL == selfResId)
		{
			if (BLOCK_KEY_OF_FRUIT != upResId)
			{
				unactive = true;
			}
		}
		// 烈焰星
		else if (BLOCK_FLAME_STAR_PEDESTAL == selfResId)
		{
			if (BLOCK_KEY_OF_BROKEN_SWORD != upResId)
			{
				unactive = true;
			}
		}
		// 萌眼星
		else if (BLOCK_Q_EYE_STAR_PEDESTAL == selfResId)
		{
			if (BLOCK_KEY_OF_STONE_EYE != upResId)
			{
				unactive = true;
			}
		}
		if (unactive)
		{
// 			container->setPedestalState(0);
			pworld->setBlockData(blockpos, blockData - 8);
			pworld->markBlockForUpdate(blockpos);
		}
	}
	else if (0 == state/*container->getPedestalState()*/)
	{
		bool active = false;
		if (BLOCK_MILA_STAR_PEDESTAL == selfResId)
		{
			if (BLOCK_KEY_OF_FRUIT == upResId)
			{
				active = true;
			}
		}
		else if (BLOCK_FLAME_STAR_PEDESTAL == selfResId)
		{
			if (BLOCK_KEY_OF_BROKEN_SWORD == upResId)
			{
				active = true;
			}
		}
		else if (BLOCK_Q_EYE_STAR_PEDESTAL == selfResId)
		{
			if (BLOCK_KEY_OF_STONE_EYE == upResId)
			{
				active = true;
			}
		}
		if (active)//判断是否出大boss
		{
// 			container->setPedestalState(1);
			pworld->setBlockData(blockpos, blockData + 8);
			pworld->markBlockForUpdate(blockpos);
			WCoord cornerCoords[8] =
			{
				WCoord(2,0,0), WCoord(4,0,0), WCoord(-2,0,0), WCoord(-4,0,0),WCoord(0,0,2),WCoord(0,0,4),WCoord(0,0,-2),WCoord(0,0,-4)
			};
			std::vector<int> vCoordsIndex;
			std::map<int, WCoord> mPedestalState;
			mPedestalState[selfResId] = blockpos;
			for (int i = 0; i < 8; i++)
			{
				WCoord tmppos = blockpos + cornerCoords[i];
				int tmpResId = pworld->getBlockID(tmppos);
				if (BLOCK_MILA_STAR_PEDESTAL == tmpResId || BLOCK_FLAME_STAR_PEDESTAL == tmpResId || BLOCK_Q_EYE_STAR_PEDESTAL == tmpResId)
				{
					auto iter = mPedestalState.find(tmpResId);
					if (iter == mPedestalState.end())
					{
						int tmpState = pworld->getBlockData(tmppos) >> 3;
						if (1 == tmpState/*tmpcontainer->getPedestalState()*/)
						{
							mPedestalState[tmpResId] = tmppos;
						}
						vCoordsIndex.push_back(i);
						if (2 <= vCoordsIndex.size())
						{
							break;
						}
					}
				}
				else
				{
					if (0 == i % 2)
					{
						i++;
					}
				}
			}
			if (3 == mPedestalState.size())
			{
				int dir = blockData & 3;
				WCoord centerPos = blockpos;
				if (vCoordsIndex[0] + 1 == vCoordsIndex[1])
				{
					centerPos += cornerCoords[vCoordsIndex[0]];
				}
				playBossEnterEffect(pworld, mPedestalState, centerPos, dir);

// 				WorldRenderer* world = pworld->getRender();
// 				if (world)
// 				{
// 					SkyPlane* sky = world->getSky();
// 					if (sky)
// 						sky->setPlanet(PLANET_VOLCANO);
// 				}
				
				if (g_pPlayerCtrl)
				{
					g_pPlayerCtrl->addAchievement(1, ACHIEVEMENT_ACTIVE_ALTAR);
				}
			}
		}
	}
}

bool BlockKeyPedestal::onTrigger(World* pworld, const WCoord& blockpos, DirectionType face, IClientPlayer* player, const Rainbow::Vector3f& colpoint)
{
	return false;
}

void BlockKeyPedestal::onBlockRemoved(World* pworld, const WCoord& blockpos, int blockid, int blockdata)
{
	// 方块移除，停止特效
	WCoord effectPos = BlockBottomCenter(blockpos) + WCoord(0, 50, 0);

	std::string effectName = "";
	// 迷拉星（地球）
	if (BLOCK_MILA_STAR_PEDESTAL == blockid)
	{
		effectName = "particles/item_altar_diqiu.ent";

	}
	// 烈焰星
	else if (BLOCK_FLAME_STAR_PEDESTAL == blockid)
	{
		effectName = "particles/item_altar_dj.ent";

	}
	// 萌眼星
	else if (BLOCK_Q_EYE_STAR_PEDESTAL == blockid)
	{
		effectName = "particles/item_altar_shijuren.ent";
	}

	auto pt = pworld->getEffectMgr()->getParticleOnPos(effectPos, effectName.c_str());
	if (pt && !pt->needClear())
	{
		pworld->getEffectMgr()->stopParticleEffect(effectName.c_str(), effectPos);
	}

	ModelBlockMaterial::onBlockRemoved(pworld, blockpos, blockid, blockdata);
}

WorldContainer* BlockKeyPedestal::createContainer(World* pworld, const WCoord& blockpos)
{
	KeyPedestalContainer* ret = SANDBOX_NEW(KeyPedestalContainer, blockpos);
	if (!ret)
	{
		return NULL;
	}
	return ret;
}

void BlockKeyPedestal::dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata, BLOCK_MINE_TYPE droptype/* =BLOCK_MINE_NOTOOL */, float chance/* =1.0f */, int uin)
{
	ModelBlockMaterial::dropBlockAsItem(pworld, blockpos, blockdata, BLOCK_MINE_PRECISE, chance, uin);
}

RenderBlockMaterial *BlockKeyPedestal::getGeomMtl(const SectionDataHandler* sectionData, const WCoord &blockpos, World* world)
{
// 	int blockid = sectionData->getBlock(blockpos).getResID();
	int index = 0;
	BlockGeomMeshInfo meshinfo;
	if (BLOCK_BROKEN_PEDESTAL != sectionData->getBlock(blockpos).getResID())
	{
		index = sectionData->getBlock(blockpos).getData() >> 3;
// 		KeyPedestalContainer* container = dynamic_cast<KeyPedestalContainer*>(psection->getWorld()->getContainerMgr()->getContainer(wpos));
// 		index = container->getPedestalState();
	}
	return getRenderMtlMgr().getMtl(m_stateMtlsIndex[index]);//m_StateMtl[index];
}

int BlockKeyPedestal::getBlockGeomID(int* idbuf, int* dirbuf, const SectionDataHandler* sectionData, const WCoord& blockpos, World* world)
{
	if (!sectionData) return 1;
	int iGeomNum = 1;
// 	int blockdata = sectionData->getBlock(blockpos).getData();
	int dir = sectionData->getBlock(blockpos).getData() & 3;
// 	int blockResId = sectionData->getBlock(blockpos).getResID();
	if (BLOCK_BROKEN_PEDESTAL == sectionData->getBlock(blockpos).getResID())
	{
		idbuf[0] = 0;
		dirbuf[0] = dir;
	}
	else
	{
		idbuf[0] = 1;
		dirbuf[0] = dir;
	}

	return iGeomNum;
}

int BlockKeyPedestal::getProtoBlockGeomID(int* idbuf, int* dirbuf)
{
	if (BLOCK_BROKEN_PEDESTAL == m_BlockResID)
	{
		idbuf[0] = 0;
		dirbuf[0] = 1;
	}
	else
	{
		idbuf[0] = 1;
		dirbuf[0] = 1;
	}
	return 1;
}

void BlockKeyPedestal::playBossEnterEffect(World* pworld, const std::map<int, WCoord>& posmap, WCoord& centerpos, int dir)
{
	if (pworld->IsVacantBossExist()) // 存在虚空BOSS，不再创建
		return;

	//bool isLive = false;
	//if (pworld->getActorMgr() && pworld->getActorMgr()->getNumBoss() > 0) //已经有boss了就不能再创建
	//{
	//	for (size_t i = 0; i < pworld->getActorMgr()->getNumBoss(); i++)
	//	{
	//		if (pworld->getActorMgr()->getBoss(i)->getObjType() == OBJ_TYPE_VACANTBOSS)
	//		{
	//			isLive = true;
	//		}
	//	}
	//}
	//if (isLive) return;

	if (pworld->getCurMapID() != MAPID_GROUND) //不在其他星球生成
		return;

	WCoord sideCoord[9] =
	{
		WCoord(0,0,0), WCoord(-1,0,0), WCoord(1,0,0), WCoord(0,0,-1), WCoord(0,0,1), WCoord(-1,0,-1), WCoord(1,0,1), WCoord(1,0,-1), WCoord(-1,0,1)
	};
	for (auto iter = posmap.begin(); iter != posmap.end(); iter++)
	{
		for (int j = 0; j < 2; j++)
		{
			WCoord pos(iter->second.x, iter->second.y + j, iter->second.z);
// 			pworld->destroyBlock(pos, BLOCK_MINE_NOTOOL);
			pworld->setBlockAir(pos);
		}
	}

	centerpos += g_DirectionCoord[ReverseDirection(dir)] * 15;
	for (int i = 0; i < 9; i++)
	{
		WCoord pos = centerpos + sideCoord[i];
		for (int j = -2; j < 6; j++)
		{
			WCoord tmpPos(pos.x, pos.y - j, pos.z);
			pworld->setBlockAir(tmpPos);
		}
	}

	ClientVacantBoss *actor = SANDBOX_NEW(ClientVacantBoss);
	actor->init(3515);//召唤虚空boss
	actor->setSpawnPoint(centerpos);
	pworld->syncLoadChunk(CoordDivSection(actor->getPosition().x), CoordDivSection(actor->getPosition().z));
	ActorManager* actorMgr = dynamic_cast<ActorManager*>(pworld->getActorMgr());
	if (actorMgr)
	{
		actorMgr->spawnBoss(actor);
	}
	actor->startAppeal();

	ChunkGenNormal* gen = dynamic_cast<ChunkGenNormal*>(pworld->getChunkProvider());
	if (gen)
	{
		WCoord blockpos = CoordDivBlock(actor->getPosition());
		gen->setBossPos(blockpos);
		
	}

// 	GetSandBoxManager().DoEvent(SandBoxMgrEventID::EVENT_BOSS_IS_APPEAR, SandBoxMgrTypeID::SB_BOSS_APPEAR);
}

void BlockKeyPedestal::onPlayRandEffect(World *pworld, const WCoord &blockpos)
{
	int selfResId = pworld->getBlockID(blockpos);
	int blockData = pworld->getBlockData(blockpos);
	int state = blockData >> 3;

	WCoord effectPos = BlockBottomCenter(blockpos) + WCoord(0, 50, 0);
	WCoord upPos = blockpos + WCoord(0, 1, 0);
	std::string effectName = "";

	int upResId = pworld->getBlockID(upPos);
	bool isActive = false;

	// 迷拉星（地球）
	if (BLOCK_MILA_STAR_PEDESTAL == selfResId)
	{
		effectName = "particles/item_altar_diqiu.ent";

		// 激活状态
		if (BLOCK_KEY_OF_FRUIT == upResId && state == 1)
		{
			isActive = true;
		}
		else
			isActive = false;
	}
	// 烈焰星
	else if (BLOCK_FLAME_STAR_PEDESTAL == selfResId)
	{
		effectName = "particles/item_altar_dj.ent";

		// 激活状态
		if (BLOCK_KEY_OF_BROKEN_SWORD == upResId && state == 1)
		{
			isActive = true;
		}
		else
			isActive = false;
	}
	// 萌眼星
	else if (BLOCK_Q_EYE_STAR_PEDESTAL == selfResId)
	{
		effectName = "particles/item_altar_shijuren.ent";

		// 激活状态
		if (BLOCK_KEY_OF_STONE_EYE == upResId && state == 1)
		{
			isActive = true;
		}
		else
			isActive = false;
	}

	// 激活状态播放特效
	if (isActive)
	{
		if (pworld->getEffectMgr()->getParticleOnPos(effectPos, effectName.c_str()) == NULL)
		{
			pworld->getEffectMgr()->playParticleEffectAsync(effectName.c_str(), effectPos, 0);
		}
	}
	// 非激活状态停止特效
	else
	{
		if (pworld->getEffectMgr()->getParticleOnPos(effectPos, effectName.c_str()) != NULL)
		{
			pworld->getEffectMgr()->stopParticleEffect(effectName.c_str(), effectPos);
		}
	}
		
}

/////////////////////////////////////////////////////////////////////////////////////////


IMPLEMENT_BLOCKMATERIAL_INSTANCE_BEGIN(BlockKeyPedestal)
	IMPLEMENT_BLOCKMATERIAL_INSTANCE_R_PARAM(BlockKeyPedestal, R_Dir, int)(0, "Dir", "Block", &BlockKeyPedestalInstance::GetBlockDir, &BlockKeyPedestalInstance::SetBlockDir);
IMPLEMENT_BLOCKMATERIAL_INSTANCE_END()