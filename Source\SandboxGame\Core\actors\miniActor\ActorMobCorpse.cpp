#include "ActorMobCorpse.h"
#include "LuaInterfaceProxy.h"
#include "DefManagerProxy.h"
#include "ClientActorManager.h"
#include "ObserverEventManager.h"
#include "WorldManager.h"
#include "SandBoxManager.h"
#include "ClientActorHelper.h"
#include "SoundComponent.h"
#include "EffectComponent.h"
#include "TemperatureComponent.h"
#include "ActorBody.h"
#include "SandboxIdDef.h"
#include "MpActorManager.h"
#include "DynamicContainer.h"
#include "GameAnalytics.h"
#include "json/jsonxx.h"
#include "ClientPlayer.h"

IMPLEMENT_SCENEOBJECTCLASS(ActorMobCorpse)

ActorMobCorpse::ActorMobCorpse() 
    : m_MobId(0)
    , m_MobName("")
    , m_MobLevel(1)
    , m_MobModelIndex(1)
    , m_ModelPath("")
    , m_BodyScale(1.0f)
    , m_CorpseBackpack(nullptr)
    , m_DecayTicks(3600)   // 默认3600个tick (大约3分钟，假设每秒20个tick)
    , m_TickCount(0)
    , m_FadeStartTick(0)
    , m_IsLooted(false)
    , m_IsFading(false)
    , m_LootListener(nullptr)
    , m_ItemTakenListener(nullptr)
    , m_CurrentLooterID(0)
    , m_DynamicContainer(nullptr)
{
    // 初始化运动向量为0
    m_Motion = Vector3f(0.0f, 0.0f, 0.0f);
    m_OnGroundTime = 0;
    
    init();
    // 初始化事件系统
    createEvent();
}

ActorMobCorpse::~ActorMobCorpse()
{
    ENG_DELETE(m_Body);
    ENG_DELETE(m_DynamicContainer);
}

void ActorMobCorpse::createEvent()
{
    // Mob尸体不需要复杂的事件监听，保持简单
}

ActorMobCorpse* ActorMobCorpse::CreateFromMob(ClientMob* mob)
{
    if (!mob)
        return nullptr;
        
    World* world = mob->getWorld();
    if (!world)
        return nullptr;
        
    // 创建尸体实例
    ActorManager* actorMgr = dynamic_cast<ActorManager*>(world->getActorMgr());
    if (!actorMgr)
        return nullptr;
        
    ActorMobCorpse* corpse = SANDBOX_NEW(ActorMobCorpse);
    if (!corpse)
        return nullptr;
        
    // 设置位置和朝向（略微抬高以防止陷入地面）
    WCoord mobPos = mob->getPosition();
    WCoord corpsePos = mobPos;
    corpsePos.y += 50; // 微调高度
    
    // 使用ActorManager来生成Actor并添加到世界
    actorMgr->spawnActor(corpse, corpsePos.x, corpsePos.y, corpsePos.z, 
                         mob->getLocoMotion()->m_RotateYaw, 0.0f);
    
    // 保存Mob基本信息
    //corpse->m_MobId = mob->getMobId();
    //corpse->m_MobLevel = mob->getLevel();
    //
    //// 从MonsterDef获取Mob名称
    //const MonsterDef* mobDef = GetDefManagerProxy()->getMonsterDef(mob->getMobId());
    //if (mobDef)
    //{
    //    corpse->m_MobName = GetDefManagerProxy()->getStringDef(mobDef->NameId);
    //    corpse->m_ModelPath = mobDef->ModelPath;
    //    corpse->m_BodyScale = mobDef->ModelScale;
    //}

    // 设置尸体存储箱格子数量为20（比玩家尸体少一些）
    if (corpse->m_DynamicContainer)
    {
        int currentSlot = 0; // 跟踪当前正在使用的尸体存储槽位

        //// 从Mob获取背包对象（如果有的话）
        //BackPack* mobBackpack = mob->getBackPack();
        //if (mobBackpack)
        //{
        //    // 只检查主背包
        //    PackContainer* container = mobBackpack->getPack(BACKPACK_START_INDEX);
        //    if (container)
        //    {
        //        // 遍历容器中的所有格子
        //        for (size_t i = 0; i < container->m_Grids.size() && currentSlot < 20; i++)
        //        {
        //            // 如果Mob格子非空且我们的尸体存储还有空间
        //            if (!container->m_Grids[i].isEmpty())
        //            {
        //                // 复制物品到尸体存储箱
        //                corpse->m_DynamicContainer->SetGrid(currentSlot, container->m_Grids[i]);
        //                currentSlot++; // 移动到下一个尸体存储槽位
        //            }
        //        }
        //    }
        //}

        // 添加一些随机掉落物（基于Mob类型）
        //if (mobDef && mobDef->DropItems.size() > 0)
        //{
        //    for (const auto& dropItem : mobDef->DropItems)
        //    {
        //        if (currentSlot >= 20) break; // 确保不超过存储容量
        //        
        //        // 根据掉落概率决定是否掉落
        //        if (rand() % 100 < dropItem.DropRate)
        //        {
        //            BackPackGrid dropGrid;
        //            dropGrid.setItemId(dropItem.ItemId);
        //            dropGrid.setNum(dropItem.Count);
        //            dropGrid.setIndex(currentSlot);
        //            
        //            corpse->m_DynamicContainer->SetGrid(currentSlot, dropGrid);
        //            currentSlot++;
        //        }
        //    }
        //}
    }

    //// Mob死亡埋点
    //const WCoord& pos = mob->getPosition();
    //std::string location = "[";
    //location += std::to_string(pos.x);
    //location += ",";
    //location += std::to_string(pos.y);
    //location += ",";
    //location += std::to_string(pos.z);
    //location += "]";

    //GameAnalytics::TrackEvent("mob_die", {
    //    {"mob_id", corpse->m_MobId},
    //    {"mob_level", corpse->m_MobLevel},
    //    {"location", location},
    //    });
    
    return corpse;
}

void ActorMobCorpse::init()
{
    ENG_DELETE(m_Body);
    if (!m_Body)
    {
        m_Body = newActorBody();
        // 使用默认的死亡模型，后续可以根据Mob类型调整
        m_Body->initPlayer(12, 0, "4");
        m_Body->playAnim(SEQ_DIE);
        Rainbow::FixedString modelPath = "empty";
        m_Body->setModelPath(modelPath);
    }
    initStorageBox();

    // 设置尸体碰撞边界 - 使用更小的碰撞范围
    getLocoMotion()->setBound(40, 70);
    getLocoMotion()->m_yOffset = 35; // 设置高度偏移
}

void ActorMobCorpse::initStorageBox()
{
    if (!m_DynamicContainer)
    {
        WCoord pos = WCoord(getPosition().x / BLOCK_SIZE, getPosition().y / BLOCK_SIZE, getPosition().z / BLOCK_SIZE);
        m_DynamicContainer = SANDBOX_NEW(DynamicContainer, pos, 0, 20); // Mob尸体只有20个格子
        m_DynamicContainer->setBaseIndex(STORAGE_START_INDEX);
        m_DynamicContainer->actor_storage = true;
        if (m_pWorld)
        {
            m_DynamicContainer->enterWorld(m_pWorld);
        }
    }
}

ActorBody* ActorMobCorpse::newActorBody()
{
    ActorBody* actorBody = ENG_NEW(ActorBody)(this);
    if (actorBody)
    {
        // 可以根据具体的Mob类型来初始化不同的模型
        // 这里暂时使用默认实现
    }
    return actorBody;
}

bool ActorMobCorpse::interact(ClientActor* player, bool onshift, bool isMobile)
{
    auto player_op = dynamic_cast<ClientPlayer*>(player);
    if (player_op && m_DynamicContainer)
    {
        player_op->openContainer(m_DynamicContainer);
    }
    return true;
}

void ActorMobCorpse::tick()
{
    ClientActor::tick();
    World* world = getWorld();
    if (!world)
        return;
        
    // 判断是否为主机模式
    bool isHost = !world->isRemoteMode();
    
    // 应用重力和物理模拟
    if (world && isHost)
    {
        // 应用重力 - 使用物品重力系数
        m_Motion.y -= world->getGravity(GRAVITY_ITEM);

        // 执行移动
        getLocoMotion()->doMoveStep(m_Motion);

        // 应用空气阻力
        m_Motion *= 0.98f;

        // 处理地面碰撞
        if (getLocoMotion()->m_OnGround)
        {
            // 应用地面摩擦和反弹
            m_Motion.x *= 0.7f;
            m_Motion.z *= 0.7f;
            m_Motion.y *= -0.3f; // 反弹系数

            // 计算地面停留时间
            m_OnGroundTime++;

            // 当在地面上停留足够长时间且运动很小时，停止水平运动
            if (m_OnGroundTime > 20 &&
                fabs(m_Motion.y) < 1.0f &&
                fabs(m_Motion.x) < 1.0f &&
                fabs(m_Motion.z) < 1.0f)
            {
                m_Motion.x = 0.0f;
                m_Motion.z = 0.0f;
            }
        }
        else
        {
            // 如果不在地面上，重置地面停留时间
            m_OnGroundTime = 0;
        }
    }
    
    // 更新尸体消失计时器
    m_TickCount++;
    
    // 如果到达淡出阶段但还未开始淡出
    if (!m_IsFading && m_TickCount >= (m_DecayTicks * 0.8f))
    {
        m_IsFading = true;
        m_FadeStartTick = m_TickCount;
    }
    
    // 如果正在淡出，更新淡出效果
    if (m_IsFading)
    {
        UpdateFadeEffect();
    }
    
    // 如果达到最大存在时间，自动销毁
    if (m_TickCount >= m_DecayTicks)
    {
        // 销毁尸体
        setNeedClear();
    }
}

void ActorMobCorpse::ApplyCorpseVisualEffects()
{
    // 应用尸体视觉效果
    // 可以根据Mob类型添加不同的特效
}

void ActorMobCorpse::UpdateFadeEffect()
{
    // 计算淡出进度
    float totalFadeTicks = m_DecayTicks - m_FadeStartTick;
    float currentFadeTicks = m_TickCount - m_FadeStartTick;
    float fadeProgress = currentFadeTicks / totalFadeTicks;
    fadeProgress = std::min(fadeProgress, 1.0f);
    
    // 应用渐隐效果
    ActorBody* body = getBody();
    if (body)
    {
        float alpha = 1.0f - fadeProgress;
        // body->setAlpha(alpha);
    }
}

bool ActorMobCorpse::canLootCorpse(ClientPlayer* player)
{
    if (!player)
        return false;
        
    // 检查距离
    float distance = Distance(player->getPosition().toVector3(), getPosition().toVector3());
    if (distance > 300.0f) // 3格距离
        return false;
        
    // 其他限制条件检查
    if (player->isDead() || player->isSleeping())
        return false;
        
    return true;
}

void ActorMobCorpse::openCorpseLoot(ClientPlayer* player)
{
    if (!player || !m_DynamicContainer)
        return;
        
    // 记录当前拾取者
    m_CurrentLooterID = player->getObjId();
    
    // 打开尸体物品界面
    MINIW::ScriptVM::game()->callFunction("OpenMobCorpseLootUI", "dds", 
        (double)player->getObjId(), 
        (double)getObjId(), 
        m_MobName.c_str());
}

bool ActorMobCorpse::TakeItemFromCorpse(ClientPlayer* player, int slotIndex, int count)
{
    // Mob尸体的物品拾取逻辑可以更简单
    return false;
}

void ActorMobCorpse::CreateDroppedItems()
{
    m_IsLooted = true;
}

bool ActorMobCorpse::leftClickInteract(ClientActor* player)
{
    ClientPlayer* clientPlayer = dynamic_cast<ClientPlayer*>(player);
    if (clientPlayer)
    {
        if (canLootCorpse(clientPlayer))
        {
            openCorpseLoot(clientPlayer);
            return true;
        }
    }
    return false;
}

int ActorMobCorpse::getObjType() const
{
    return OBJ_TYPE_MOBCORPSE; // 需要在SandboxIdDef.h中定义
}

bool ActorMobCorpse::load(const void* srcdata, int version)
{
    auto src = reinterpret_cast<const FBSave::ActorMobCorpse*>(srcdata);
    if (!src)
        return false;
        
    if (!loadActorCommon(src->basedata()))
       return false;

    // 加载Mob ID
    m_MobId = src->mobId();

    if (src->containers())
    {
        initStorageBox();
        m_DynamicContainer->load((void*)src->containers());
    }
    return true;
}

flatbuffers::Offset<FBSave::SectionActor> ActorMobCorpse::save(SAVE_BUFFER_BUILDER& builder)
{
    auto baseData = ClientActor::saveActorCommon(builder);
    auto storage = m_DynamicContainer->SaveTo(builder);

    auto data = FBSave::CreateActorMobCorpse(builder, baseData, storage, m_MobId);
    return saveSectionActor(builder, FBSave::SectionActorUnion_ActorMobCorpse, data.Union());
}

void ActorMobCorpse::enterWorld(World* pworld)
{
    ClientActor::enterWorld(pworld);
    
    // 尸体进入世界后的额外处理
    ApplyCorpseVisualEffects();
    
    // 如果尸体已经处于淡出阶段，恢复淡出效果
    if (m_IsFading)
    {
        UpdateFadeEffect();
    }
    
    if (m_DynamicContainer)
    {
        m_DynamicContainer->enterWorld(pworld);
    }
}

void ActorMobCorpse::leaveWorld(bool keep_inchunk)
{
    if (m_DynamicContainer)
    {
        m_DynamicContainer->leaveWorld();
    }
    
    ClientActor::leaveWorld(keep_inchunk);
}
