#include "BlockExplosionFragment.h"
#include "BlockMaterialMgr.h"
#include "BlockMaterialBase.h"
#include "Math/Random/Random.h"
#include "worldMesh/BlockGeom.h"

void BlockExplosionFragment::GenerateFragments(const WCoord& blockPos,
                                             const Block& block, 
                                             dynamic_array<FragmentMeshInfo>& outFragments,
                                             int fragmentCount)
{
    // 获取原始方块的网格数据
    BlockGeomMeshInfo originalMesh;
    BlockMaterial* material = g_BlockMtlMgr.getMaterial(block.getResID());
    if (!material) return;

    // 生成原始方块的完整网格
    material->getGeom(0)->getFaceVerts(originalMesh, 0, 1.0f, 0, 0);

    // 计算方块在世界中的位置
    Rainbow::Vector3f worldCenter(
        blockPos.x * BLOCK_SIZE, 
        blockPos.y * BLOCK_SIZE,
        blockPos.z * BLOCK_SIZE
    );

    // 将原始网格分割成碎片
    SubdivideBlock(originalMesh.vertices.data(),
                   originalMesh.indices.data(), 
                   originalMesh.indices.size(),
                   outFragments,
                   fragmentCount,
                   worldCenter);  // 传入世界坐标

    //// 为每个碎片添加物理组件
    //for (auto& fragment : outFragments) {
    //    // 创建动态物理Actor
    //    Rainbow::RigidDynamicActor* physicsActor = new Rainbow::RigidDynamicActor(Rainbow::RigidBaseActor::RigidActor_Type_Box);
    //    
    //    // 设置物理属性
    //    physicsActor->SetMass(1.0f); // 设置质量
    //    physicsActor->SetLinearDamping(0.1f); // 设置线性阻尼
    //    physicsActor->SetAngularDamping(0.1f); // 设置角度阻尼
    //    
    //    // 设置初始速度和角速度(爆炸效果)
    //    Rainbow::Vector3f explosionDir = (fragment.position - worldCenter).Normalized();
    //    float explosionForce = 10.0f; // 爆炸力度
    //    physicsActor->SetLinearVelocity(explosionDir * explosionForce);
    //    
    //    physicsActor->SetAngularVelocity(fragment.rotation);
    //    
    //    
    //    // 存储物理组件
    //    fragment.physicsActor = physicsActor;
    //}
}

void BlockExplosionFragment::SubdivideBlock(
    const BlockGeomVert* originalVerts,
    const UInt16* originalIndices,
    int originalIndexCount,
    dynamic_array<FragmentMeshInfo>& outFragments,
    int fragmentCount,
    const Rainbow::Vector3f& worldCenter)
{
    // 1. 输入验证
    if (!originalVerts || !originalIndices || originalIndexCount <= 0) {
        LOG_DEBUG("BlockExplosionFragment: Invalid input data");
        return;
    }

    // 2. 获取原始方块的边界信息
    Rainbow::MinMaxAABB blockBounds;
    Rainbow::Vector3f blockSize(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE);
    blockBounds.m_Min = Rainbow::Vector3f::zero;
    blockBounds.m_Max = blockSize;

    Rainbow::Rand& r = Rainbow::GetGlobalRand();

    // 3. 尝试进行分割
    bool subdivideSuccess = false;
    try {
        // 限制碎片数量在合理范围内
        fragmentCount = Rainbow::Clamp(fragmentCount, 2, 8);
        
        for (int i = 0; i < fragmentCount; i++) {
            FragmentMeshInfo fragment;
            
            // 计算分割平面
            Rainbow::Vector3f planeNormal = GetRandomNormal();
            float planeDistance = Rainbow::RangedRandom(r, blockSize.x * 0.2f, blockSize.x * 0.8f);
            
            // 生��分割后的顶点和索引数据
            if (GenerateFragmentGeometry(originalVerts, originalIndices, originalIndexCount, 
                                       planeNormal, planeDistance, fragment)) {
                // 设置碎片的物理属性
                SetupFragmentPhysics(fragment, worldCenter);
                
                // 验证碎片是否有效
                if (IsValidFragment(fragment)) {
                    outFragments.push_back(fragment);
                    subdivideSuccess = true;
                }
            }
        }
    }
    catch (const std::exception& e) {
        LOG_DEBUG("BlockExplosionFragment: Subdivision failed: %s {}", e.what());
        subdivideSuccess = false;
    }

    // 4. 如果分割失败，使用原始方块作为单个碎片
    if (!subdivideSuccess || outFragments.empty()) {
        LOG_WARNING("BlockExplosionFragment: Falling back to original block mesh");
        FragmentMeshInfo originalFragment;
        
        // 复制原始顶点数据
        originalFragment.vertices.reserve(24); // 立方体顶点数
        memcpy(originalFragment.vertices.data(), originalVerts, 24 * sizeof(BlockGeomVert));
        
        // 复制原始索引数据
        originalFragment.indices.reserve(originalIndexCount);
        memcpy(originalFragment.indices.data(), originalIndices, originalIndexCount * sizeof(UInt16));
        
        // 设置包围盒
        originalFragment.aabb = blockBounds;
        
        // 设置基础物理属性
        SetupFragmentPhysics(originalFragment, worldCenter);
        
        outFragments.push_back(originalFragment);
    }
}

// 设置碎片的物理属性
void BlockExplosionFragment::SetupFragmentPhysics(FragmentMeshInfo& fragment, const Rainbow::Vector3f& worldCenter)
{
    const float kPI2 = 3.14159265f;
    // 获取全局随机数生成器
    Rainbow::Rand& rand = Rainbow::GetGlobalRand();
    
    // 随机初始速度
    float baseSpeed = 10.0f;
    fragment.velocity.x = Rainbow::RangedRandom(rand, baseSpeed * -5.0f, baseSpeed * 5.0f);
    fragment.velocity.y = Rainbow::RangedRandom(rand, baseSpeed * 5.0f, baseSpeed * 10.0f);
    fragment.velocity.z = Rainbow::RangedRandom(rand, baseSpeed * -5.0f, baseSpeed * 5.0f);
    
    // 随机旋转速度 
    fragment.rotation.x = Rainbow::RangedRandom(rand, -kPI2, kPI2);
    fragment.rotation.y = Rainbow::RangedRandom(rand, -kPI2, kPI2);
    fragment.rotation.z = Rainbow::RangedRandom(rand, -kPI2, kPI2);
    
    // 初始位置(从方块中心开始)
	Rainbow::Vector3f localCenter = (fragment.aabb.m_Min + fragment.aabb.m_Max) * 0.5f;
	// 转换到世界坐标
	fragment.position = worldCenter + localCenter;
    
    // 设置生命周期
    fragment.lifeTime = Rainbow::RangedRandom(rand, 2.0f, 3.0f);
}

// 验证碎片是否有效
bool BlockExplosionFragment::IsValidFragment(const FragmentMeshInfo& fragment) 
{
    if (fragment.vertices.empty() || fragment.indices.empty()) {
        return false;
    }

    // 检查碎片大小
    float volume = CalculateFragmentVolume(fragment);
    if (volume < BLOCK_SIZE * BLOCK_SIZE * BLOCK_SIZE * 0.05f) { // 碎片太小
        return false;
    }

    // 检查索引数量是否为三角形的倍数
    if (fragment.indices.size() % 3 != 0) {
        return false;
    }

    return true;
}

// 生成随机法线方向
Rainbow::Vector3f BlockExplosionFragment::GetRandomNormal() 
{
    // 使用引擎提供的随机单位向量
    Rainbow::Rand& rand = Rainbow::GetGlobalRand();
    return Rainbow::RandomUnitVector(rand);
}

// 根据分割平面生成碎片几何体
bool BlockExplosionFragment::GenerateFragmentGeometry(
    const BlockGeomVert* originalVerts,
    const UInt16* originalIndices,
    int originalIndexCount,
    const Rainbow::Vector3f& planeNormal,
    float planeDistance,
    FragmentMeshInfo& outFragment) 
{
    // 1. 初始化碎片数据
    outFragment.vertices.clear();
    outFragment.indices.clear();

    // 2. 计算与分割平面的交点并生成新的顶点和索引
    if (!CalculateIntersection(originalVerts, originalIndices, originalIndexCount,
                             planeNormal, planeDistance, 
                             outFragment.vertices, outFragment.indices)) {
        return false;
    }

    // 3. 更新碎片包围盒
    UpdateFragmentBounds(outFragment);

    return true;
}

// 计算碎片体积
float BlockExplosionFragment::CalculateFragmentVolume(const FragmentMeshInfo& fragment) 
{
    if (fragment.vertices.empty() || fragment.indices.size() < 3) {
        return 0.0f;
    }

    // 使用包围盒体积作为近似值
    Rainbow::Vector3f size = fragment.aabb.m_Max - fragment.aabb.m_Min;
    return size.x * size.y * size.z;
}

// 计算与分割平面的交点
bool BlockExplosionFragment::CalculateIntersection(
    const BlockGeomVert* originalVerts,
    const UInt16* originalIndices,
    int originalIndexCount,
    const Rainbow::Vector3f& planeNormal,
    float planeDistance,
    dynamic_array<BlockGeomVert>& outVertices,
    dynamic_array<UInt16>& outIndices) 
{
    if (!originalVerts || !originalIndices || originalIndexCount < 3) {
        return false;
    }

    // 清空输出数组
    outVertices.clear();
    outIndices.clear();

    // 计算每个顶点到分割平面的距离
    dynamic_array<float> distances;
    const int vertexCount = originalIndexCount / 3 * 3; // 确保是3的倍数
    distances.reserve(vertexCount);
    
    // 计算每个顶点到平面的距离
    for (int i = 0; i < vertexCount; i++) {
        const BlockGeomVert& vert = originalVerts[originalIndices[i]];
        Rainbow::Vector3f vertPos(vert.pos.x, vert.pos.y, vert.pos.z);
        distances[i] = Rainbow::Dot(vertPos, planeNormal) - planeDistance;
    }

    // 处理每个三角形
    for (int i = 0; i < vertexCount; i += 3) {
        float d0 = distances[i];
        float d1 = distances[i + 1];
        float d2 = distances[i + 2];

        // 如果三角形完全在平面一侧，保留它
        if (d0 >= 0 && d1 >= 0 && d2 >= 0) {
            UInt16 newIndex = outVertices.size();
            outVertices.push_back(originalVerts[originalIndices[i]]);
            outVertices.push_back(originalVerts[originalIndices[i + 1]]);
            outVertices.push_back(originalVerts[originalIndices[i + 2]]);
            outIndices.push_back(newIndex);
            outIndices.push_back(newIndex + 1);
            outIndices.push_back(newIndex + 2);
        }
    }

    return !outVertices.empty();
}

// 更新碎片包围盒
void BlockExplosionFragment::UpdateFragmentBounds(FragmentMeshInfo& fragment) 
{
    if (fragment.vertices.empty()) {
        fragment.aabb.m_Min = Rainbow::Vector3f::zero;
        fragment.aabb.m_Max = Rainbow::Vector3f::zero;
        return;
    }

    // 初始化为第一个顶点
    fragment.aabb.m_Min = Rainbow::Vector3f(
        fragment.vertices[0].pos.x,
        fragment.vertices[0].pos.y,
        fragment.vertices[0].pos.z
    );
    fragment.aabb.m_Max = fragment.aabb.m_Min;

    // 扩展包围盒以包含所有顶点
    for (const auto& vert : fragment.vertices) {
        Rainbow::Vector3f vertPos(vert.pos.x, vert.pos.y, vert.pos.z);
        
        fragment.aabb.m_Min.x = Rainbow::Min(fragment.aabb.m_Min.x, vertPos.x);
        fragment.aabb.m_Min.y = Rainbow::Min(fragment.aabb.m_Min.y, vertPos.y);
        fragment.aabb.m_Min.z = Rainbow::Min(fragment.aabb.m_Min.z, vertPos.z);

        fragment.aabb.m_Max.x = Rainbow::Max(fragment.aabb.m_Max.x, vertPos.x);
        fragment.aabb.m_Max.y = Rainbow::Max(fragment.aabb.m_Max.y, vertPos.y);
        fragment.aabb.m_Max.z = Rainbow::Max(fragment.aabb.m_Max.z, vertPos.z);
    }
} 
