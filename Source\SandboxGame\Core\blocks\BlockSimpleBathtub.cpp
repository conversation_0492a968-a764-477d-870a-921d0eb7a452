
#include "BlockSimpleBathtub.h"
#include "BlockMaterialMgr.h"
//#include "OgreMaterial.h"
#include "section.h"
#include "SectionMesh.h"
#include "BlockGeom.h"
#include "Collision.h"
#include "world.h"
#include "Environment.h"
#include "ClientActorManager.h"
#include "IClientPlayer.h"
#include "ChunkGenerator.h"
//#include "GameEvent.h"
#include "DefManagerProxy.h"
#include "VehicleWorld.h"
#include "ActorVehicleAssemble.h"
#include "OgreScriptLuaVM.h"

IMPLEMENT_BLOCKMATERIAL(BlockSimpleBathtub)
IMPLEMENT_BLOCKMATERIAL(BlockPicture)
IMPLEMENT_BLOCKMATERIAL(BlockFourSizeSquarePicture)
IMPLEMENT_BLOCKMATERIAL(BlockSimplePicture)
using namespace MINIW;

inline bool IsBedHead(int blockdata)
{
	return (blockdata & 4) != 0;
}

void BlockSimpleBathtub::initGeomName()
{
	m_geomName = m_Def->Texture2.c_str();
}
int BlockSimpleBathtub::getBlockGeomID(int *idbuf, int *dirbuf, const SectionDataHandler* sectionData, const WCoord &blockpos, World* world)
{
	int blockdata = sectionData->getBlock(blockpos).getData();
	if (IsBedHead(blockdata)) idbuf[0] = 1;
	else idbuf[0] = 0;

	dirbuf[0] = blockdata & 3;
	return 1;
}

//const char *BlockSimpleBathtub::getGeomName()
//{
//	return GetBlockDef()->Texture2.c_str();
//}

bool BlockSimpleBathtub::onTrigger(World *pworld, const WCoord &input_blockpos, DirectionType face, IClientPlayer *player, const Rainbow::Vector3f &colpoint)
{
	return true;
}

void BlockSimpleBathtub::onNotify(World *pworld, const WCoord &blockpos, int blockid)
{
	int blockdata = pworld->getBlockData(blockpos);
	int ishead = blockdata & 4;
	int placedir = blockdata & 3;
	placedir = RotateDir90(placedir);
	if (ishead)
	{
		if (pworld->getBlockID(NeighborCoord(blockpos, ReverseDirection(placedir))) != getBlockResID())
		{
			pworld->setBlockAir(blockpos);
		}
	}
	else
	{
		if (pworld->getBlockID(NeighborCoord(blockpos, placedir)) != getBlockResID())
		{
			pworld->setBlockAir(blockpos);
			dropBlockAsItem(pworld, blockpos, blockdata);
		}
	}
}

int BlockSimpleBathtub::getProtoBlockGeomID(int *idbuf, int *dirbuf)
{
	idbuf[0] = 0;
	dirbuf[0] = DIR_NEG_X;
	return 1;
}

void BlockSimpleBathtub::dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata, BLOCK_MINE_TYPE droptype/* =BLOCK_MINE_NOTOOL */, float chance/* =1.0f */, int uin)
{
	int ishead = blockdata & 4;
	if (!ishead) ModelBlockMaterial::dropBlockAsItem(pworld, blockpos, blockdata, droptype, chance, uin);
}

void BlockSimpleBathtub::createCollideData(CollisionDetect *coldetect, World *pworld, const WCoord &blockpos)
{
	coldetect->addObstacle(blockpos*BLOCK_SIZE, blockpos*BLOCK_SIZE+WCoord(BLOCK_SIZE,BLOCK_SIZE/2,BLOCK_SIZE));
}

int BlockSimpleBathtub::convertDataByRotate(int blockdata, int rotatetype)
{
	return this->commonConvertDataByRotateWithBit(blockdata, rotatetype, 3, 12);
}

SectionMesh *BlockSimpleBathtub::createBlockProtoMesh(int protodata)
{
	SectionMesh* pmesh = ENG_NEW(SectionMesh)();
	SectionSubMesh* psubmesh = pmesh->getSubMesh(getDefaultMtl(), true);

	BlockGeomMeshInfo meshinfo;

	WCoord center(-BLOCK_SIZE / 2, 0, 0);

	getGeom()->getFaceVerts(meshinfo, 0);
	psubmesh->addTriangleList(meshinfo.vertices.data(), meshinfo.vertices.size(), meshinfo.indices.data(), meshinfo.indices.size(), &center);

	getGeom()->getFaceVerts(meshinfo, 1);
	WCoord pos(BLOCK_SIZE, 0, 0);
	pos += center;
	psubmesh->addTriangleList(meshinfo.vertices.data(), meshinfo.vertices.size(), meshinfo.indices.data(), meshinfo.indices.size(), &pos);

	return pmesh;
}


void BlockPicture::createCollideData(CollisionDetect *coldetect, World *pworld, const WCoord &blockpos)
{
	int blockdata = pworld->getBlockData(blockpos);
	int dir = blockdata & 3;
	if (dir == DIR_POS_Z )
	{
		coldetect->addObstacle(blockpos*BLOCK_SIZE, blockpos*BLOCK_SIZE + WCoord(BLOCK_SIZE, BLOCK_SIZE, 10));
	}
	if (dir == DIR_NEG_Z)
	{
		coldetect->addObstacle(blockpos*BLOCK_SIZE + WCoord(0, 0, 90), blockpos*BLOCK_SIZE + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
	}
	if (dir == DIR_POS_X )
	{
		coldetect->addObstacle(blockpos*BLOCK_SIZE, blockpos*BLOCK_SIZE + WCoord(10, BLOCK_SIZE, BLOCK_SIZE));
	}
	if (dir == DIR_NEG_X)
	{
		coldetect->addObstacle(blockpos*BLOCK_SIZE + WCoord(90, 0, 0), blockpos*BLOCK_SIZE + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
	}
}

void BlockFourSizeSquarePicture::createBlockMesh(const BuildSectionMeshData& data, const WCoord &blockpos, SectionMesh *poutmesh)
{
	int blockdata = data.m_SharedSectionData->getBlock(blockpos).getData();
	// 除了关键点的方块，其他都不显示
	if ((blockdata & 12)) return;
	ModelBlockMaterial::createBlockMesh(data, blockpos, poutmesh);
}

void BlockFourSizeSquarePicture::onNotify(World *pworld, const WCoord &blockpos, int blockid)
{
	int blockdata = pworld->getBlockData(blockpos);
	int sectionType = (blockdata & 12) >> 2;
	int placedir = blockdata & 3;
	placedir = RotateDir90(placedir);
	// 区域0，跟随区域1消失而消失
	if (sectionType == 0 )
	{
		if (pworld->getBlockID(NeighborCoord(blockpos, placedir)) != getBlockResID())						// 区域1
		{
			pworld->setBlockAir(blockpos);
		}
	}
	// 区域1， 跟随区域3消失而消失
	else if(sectionType == 1)
	{
		if (pworld->getBlockID(blockpos + WCoord(0, 1, 0)) != getBlockResID())								// 区域3
		{
			pworld->setBlockAir(blockpos);
		}
	}
	// 区域2，跟随区域0消失而消失
	else if (sectionType == 2)
	{
		if (pworld->getBlockID(blockpos - WCoord(0, 1, 0)) != getBlockResID())								// 区域0
		{
			pworld->setBlockAir(blockpos);
		}
	}
	// 区域3，跟随区域2消失而消失
	else if(sectionType == 3)
	{
		if (pworld->getBlockID(NeighborCoord(blockpos, ReverseDirection(placedir))) != getBlockResID())	// 区域2
		{	
			pworld->setBlockAir(blockpos);
		}
	}
}

void BlockFourSizeSquarePicture::createCollideData(CollisionDetect *coldetect, World *pworld, const WCoord &blockpos)
{
	int blockdata = pworld->getBlockData(blockpos);
	int dir = blockdata & 3;
	if (dir == DIR_POS_Z)
	{
		
		coldetect->addObstacle(blockpos*BLOCK_SIZE, blockpos*BLOCK_SIZE + WCoord(BLOCK_SIZE, BLOCK_SIZE, 10));
	}
	if (dir == DIR_NEG_Z)
	{
		coldetect->addObstacle(blockpos*BLOCK_SIZE + WCoord(0, 0, 90), blockpos*BLOCK_SIZE + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
	}
	if (dir == DIR_POS_X)
	{
		coldetect->addObstacle(blockpos*BLOCK_SIZE, blockpos*BLOCK_SIZE + WCoord(10, BLOCK_SIZE, BLOCK_SIZE));
	}
	if (dir == DIR_NEG_X)
	{
		
		coldetect->addObstacle(blockpos*BLOCK_SIZE + WCoord(90, 0, 0), blockpos*BLOCK_SIZE + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
	}
}
