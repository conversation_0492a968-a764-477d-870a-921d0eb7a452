
#ifndef __BLOCK_DETECTION_PIPE_H__
#define __BLOCK_DETECTION_PIPE_H__

#include "BlockMaterial.h"
#include "container_detectionpipe.h"
#include "BlockWirlessElectricUnit.h"

class BlockDetectionPipe : public BlockWirlessElectricUnit
{
	DECLARE_BLOCKMATERIAL(BlockDetectionPipe)

public:
	BlockDetectionPipe();
	virtual ~BlockDetectionPipe();
	virtual void init(int resid) override;

	virtual int getPlaceBlockData(World *pworld, const WCoord &blockpos, DirectionType face, float hitptx, float hitpty, float hitptz, int def_blockdata);
	virtual void onBlockAdded(World *pworld, const WCoord &blockpos);
	virtual void onBlockRemoved(World* pworld, const WCoord& blockpos, int blockid, int blockdata);
	virtual void createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh) override;

	virtual void onBlockPlacedBy(World* pworld, const WCoord& blockpos, IClientPlayer* player);

	virtual bool hasContainer() override
	{
		return true;
	}
	virtual void onNotify(World* pworld, const WCoord& blockpos, int blockid) override;
	virtual bool onTrigger(World* pworld, const WCoord& blockpos, DirectionType face, IClientPlayer* player, const Rainbow::Vector3f& colpoint = Rainbow::Vector3f(0, 0, 0));
	virtual ContainerElectricElement* createContainer(World* pworld, const WCoord& blockpos, int dir, int power) override;
	virtual bool beginTransfer(World* pworld, const WCoord& blockpos, long long objID, int dir);
	virtual bool canConnectToDir(World* pworld, const WCoord& blockpos, int dir, const BackPackGrid& grid) override;
	virtual void endTransfer(World* pworld, const WCoord& blockpos);

	void updateState(World* pworld, const WCoord& blockpos, bool isShow);
	virtual int outputWeakEnergy(World* pworld, const WCoord& blockpos, DirectionType dir);
	virtual void dropBlockAsItem(World* pworld, const WCoord& blockpos, int blockdata = 0, BLOCK_MINE_TYPE droptype = BLOCK_MINE_NOTOOL, float chance = 1.0f, int uin = -1);
	virtual unsigned int electricEmitBlockLaserType(World* pworld, const WCoord& blockpos, int dir) override;
private:
	virtual void initGeomName() override;
protected:
	virtual int getIndirectlyPowered(World* pworld, const WCoord& blockpos, int dir) override;

private:
	RenderBlockMaterial* m_OnMtl;
};

#endif