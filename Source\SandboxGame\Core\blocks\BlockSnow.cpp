
#include "BlockSnow.h"
#include "BlockMaterialMgr.h"
#include "world.h"
#include "WorldProxy.h"
#include "special_blockid.h"
#include "DefManagerProxy.h"
#include "ClientPlayer.h"
#include "ClientActorManager.h"
#include "EffectManager.h"
#include "block_tickmgr.h"
#include "SectionMesh.h"
#include "coreMisc.h"
IMPLEMENT_BLOCKMATERIAL(SnowCubeMaterial)
IMPLEMENT_BLOCKMATERIAL(SnowBlockMaterial)
//IMPLEMENT_BLOCKINSTANCE(SnowBlockMaterial)

using namespace MINIW;

const float s_SnowThick = 0.125f;
const char* pStepOnSnowName = "particles/caixue.ent";//踩雪特效

void SnowCubeMaterial::blockTick(World* pworld, const WCoord& blockpos)
{
	WCoord pt = TopCoord(blockpos);
	if (pworld->getBlockTorchIllum(pt.x, pt.y, pt.z) > 11)
	{
		dropBlockAsItem(pworld, blockpos);
		pworld->setBlockAir(blockpos);
	}
}

void SnowBlockMaterial::initDrawType()
{
	m_blockDrawType = BLOCKDRAW_SNOW;
}


void SnowBlockMaterial::init(int resid)
{
	Super::init(resid);

	SetToggle(BlockToggle_IsOpaqueCube, false);
	SetAttrRenderType(BLOCKRENDER_MODEL);
}

void SnowBlockMaterial::initDefaultMtl()
{
	auto mtl = g_BlockMtlMgr.createRenderMaterial(m_Def->Texture1.c_str(), m_Def, GETTEX_WITHDEFAULT, BLOCKDRAW_SNOW);
	//auto mtl = g_BlockMtlMgr.createRenderMaterial(m_Def->Texture1.c_str(), m_Def, GETTEX_WITHDEFAULT);
	m_defaultMtlIndex = getRenderMtlMgr().addMtl(mtl);
	OGRE_RELEASE(mtl);
}


float SnowBlockMaterial::getBlockHeight(int blockdata)
{
	// 	//之前积雪为8个合成一个,现在为6个,但显示效果依旧和之前一样.加个系数 8 / 6 code-by:曹泽港
	// 	return s_SnowThick * (blockdata+1) * 8 / 6;
	if (blockdata >= 3)
	{
		return 1.f;
	}
	else
	{
		return (blockdata + 1.f) / 4.f;
	}
}

//bool SnowBlockMaterial::isOpaqueCube()
//{
//	return false;
//}




bool SnowBlockMaterial::hasSolidTopSurface(int blockdata)
{
	if (blockdata >= 3) return true;
	else return false;
}

bool SnowBlockMaterial::canPutOntoPos(WorldProxy* pworld, const WCoord& blockpos)
{
	int blockid = pworld->getBlockID(DownCoord(blockpos));
	if (blockid == 0) return false;

	if (blockid == getBlockResID() && pworld->getBlockData(DownCoord(blockpos))>=3) return true;

	if (IsLeavesBlockID(blockid)) return true;

	BlockMaterial* pmtl = g_BlockMtlMgr.getMaterial(blockid);
	if (!pmtl->isOpaqueCube()) return false;

	return pmtl->defBlockMove();
}

void SnowBlockMaterial::onNotify(World* pworld, const WCoord& blockpos, int blockid)
{
	if (!canPutOntoPos(pworld->getWorldProxy(), blockpos))
	{
		dropBlockAsItem(pworld, blockpos);
		pworld->setBlockAir(blockpos);
	}
}
// float changebigsize = 12.f;
// float changedownsize = 15.f;
bool SnowBlockMaterial::isKeepRoundBlock(BlockMaterial* mtl)
{
	return !(mtl->getBlockSpecialLogicType(0) & BlockMaterial::BlockSpceialLogicTeam0::BlockFaceToPlane) || (mtl->getBlockSpecialLogicType(0) & BlockMaterial::BlockSpceialLogicTeam0::IsRoundBlock);
}
void SnowBlockMaterial::createBlockMeshAngle(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh)
{
	auto psection = data.m_SharedSectionData;

	FaceVertexLight faceVertexLight;
	auto pblock = psection->getBlock(blockpos);
	const BiomeDef* biome = GetDefManagerProxy()->getBiomeDef(1);
	float blockHeightScale = getBlockHeight(pblock.getData());
	float blockHalfHeight = blockHeightScale * 50;
	bool hasdown = false;
	auto pDownBlock = psection->getNeighborBlock(blockpos, DIR_NEG_Y);
	if (pDownBlock.getResID() != 0)
	{
		hasdown = true;
	}
	bool isTopChange = false;
	int nNeighorData = 0;
	float changebigsize = 12.f * blockHeightScale;
	for (int oo = 0; oo < 8; oo++)
	{
		auto ptmpBlock = psection->getNeighborBlock(blockpos, SnowBlockOutNeighbor[oo]);
		if (ptmpBlock.getResID() == 0)
		{
			nNeighorData |= 1 << oo;
		}
	}
	for (int d = 0; d < 6; d++)
	{
		DirectionType dir = (DirectionType)d;

		bool isNoClip = false;
		if (checkBlockMeshIsBuild(data, blockpos, d, isNoClip))
		{
			bool flipQuad = psection->getFaceVertexLight(blockpos, dir, faceVertexLight);

			BlockColor facecolor(255, 255, 255, 0);
			RenderBlockMaterial* pmtl = getFaceMtl(biome, dir, pblock.getData(), facecolor);
			if (pmtl == NULL) continue;
			SectionSubMesh* psubmesh = poutmesh->getSubMesh(pmtl);
			bool mergeface = true;
			bool ischange = true;

			if (hasdown)
			{
				if (d < 4 && nNeighorData & (1 << d))
				{
					mergeface = false;
				}
			}
			BlockGeomMeshInfo mesh;
			getBlockMeshAngleData(data, blockpos, mesh, d, psubmesh && !psubmesh->IsIgnoreTileUV(), pmtl, isNoClip, mergeface, hasdown, nNeighorData);
			if (psubmesh)
			{
				// 				if (hasdown)
				// 				{
				// 					if (d < 4 && (nNeighorData & (1 << d)))
				// 					{
				// 						int idx = 0;
				// 						for (int oo = 12; oo < mesh.vertices.size(); oo++)
				// 						{
				// 							if (0 == d)
				// 							{
				// 								mesh.vertices[oo].pos.x -= changebigsize;
				// 								mesh.vertices[oo].pos.y += mesh.vertices[oo].pos.y > blockHalfHeight ? -changebigsize : changebigsize;
				// 							}
				// 							else if (1 == d)
				// 							{
				// 								mesh.vertices[oo].pos.x += changebigsize;
				// 								mesh.vertices[oo].pos.y += mesh.vertices[oo].pos.y > blockHalfHeight ? -changebigsize : changebigsize;
				// 							}
				// 							else if (2 == d)
				// 							{
				// 								mesh.vertices[oo].pos.z -= changebigsize;
				// 								mesh.vertices[oo].pos.y += mesh.vertices[oo].pos.y > blockHalfHeight ? -changebigsize : changebigsize;
				// 							}
				// 							else if (3 == d)
				// 							{
				// 								mesh.vertices[oo].pos.z += changebigsize;
				// 								mesh.vertices[oo].pos.y += mesh.vertices[oo].pos.y > blockHalfHeight ? -changebigsize : changebigsize;
				// 							}
				// 						}
				// 						if (0 == d)
				// 						{
				// 							std::vector<int> minl;
				// 							std::vector<int> maxl;
				// 							float minv = 50.f;
				// 							float maxv = 50.f;
				// 							for (int oo = 4; oo < 12; oo++)
				// 							{
				// 								if (mesh.vertices[oo].pos.z <= minv)
				// 								{
				// 									if (mesh.vertices[oo].pos.z < minv)
				// 									{
				// 										minl.clear();
				// 										minv = mesh.vertices[oo].pos.z;
				// 									}
				// 									minl.push_back(oo);
				// 								}
				// 								if (mesh.vertices[oo].pos.z >= maxv)
				// 								{
				// 									if (mesh.vertices[oo].pos.z > maxv)
				// 									{
				// 										maxl.clear();
				// 										maxv = mesh.vertices[oo].pos.z;
				// 									}
				// 									maxl.push_back(oo);
				// 								}
				// 							}
				// 
				// 							if ((nNeighorData & 1 << 2 ) && (nNeighorData & 1 << 4))
				// 							{
				// 								for (auto it : minl)
				// 								{
				// 									mesh.vertices[it].pos.x -= changebigsize;
				// 									mesh.vertices[it].pos.z -= changebigsize;
				// 									mesh.vertices[it].pos.y += mesh.vertices[it].pos.y > blockHalfHeight ? -changebigsize : changebigsize / 2;
				// 								}
				// 							}
				// 							if ((nNeighorData & 1 << 3) && (nNeighorData & 1 << 5))
				// 							{
				// 								for (auto it : maxl)
				// 								{
				// 									mesh.vertices[it].pos.x -= changebigsize;
				// 									mesh.vertices[it].pos.z += changebigsize;
				// 									mesh.vertices[it].pos.y += mesh.vertices[it].pos.y > blockHalfHeight ? -changebigsize : changebigsize / 2;
				// 								}
				// 							}
				// 						}
				// 						else if (1 == d)
				// 						{
				// 							std::vector<int> minl;
				// 							std::vector<int> maxl;
				// 							float minv = 50.f;
				// 							float maxv = 50.f;
				// 							for (int oo = 4; oo < 12; oo++)
				// 							{
				// 								if (mesh.vertices[oo].pos.z <= minv)
				// 								{
				// 									if (mesh.vertices[oo].pos.z < minv)
				// 									{
				// 										minl.clear();
				// 										minv = mesh.vertices[oo].pos.z;
				// 									}
				// 									minl.push_back(oo);
				// 								}
				// 								if (mesh.vertices[oo].pos.z >= maxv)
				// 								{
				// 									if (mesh.vertices[oo].pos.z > maxv)
				// 									{
				// 										maxl.clear();
				// 										maxv = mesh.vertices[oo].pos.z;
				// 									}
				// 									maxl.push_back(oo);
				// 								}
				// 							}
				// 
				// 							if ((nNeighorData & 1 << 2) && (nNeighorData & 1 << 6))
				// 							{
				// 								for (auto it : minl)
				// 								{
				// 									mesh.vertices[it].pos.x += changebigsize;
				// 									mesh.vertices[it].pos.z -= changebigsize;
				// 									mesh.vertices[it].pos.y += mesh.vertices[it].pos.y > blockHalfHeight ? -changebigsize : changebigsize / 2;
				// 								}
				// 							}
				// 							if ((nNeighorData & 1 << 3) && (nNeighorData & 1 << 7))
				// 							{
				// 								for (auto it : maxl)
				// 								{
				// 									mesh.vertices[it].pos.x += changebigsize;
				// 									mesh.vertices[it].pos.z += changebigsize;
				// 									mesh.vertices[it].pos.y += mesh.vertices[it].pos.y > blockHalfHeight ? -changebigsize : changebigsize / 2;
				// 								}
				// 							}
				// 						}
				// 						else if (2 == d)
				// 						{
				// 							std::vector<int> minl;
				// 							std::vector<int> maxl;
				// 							float minv = 50.f;
				// 							float maxv = 50.f;
				// 							for (int oo = 4; oo < 12; oo++)
				// 							{
				// 								if (mesh.vertices[oo].pos.x <= minv)
				// 								{
				// 									if (mesh.vertices[oo].pos.x < minv)
				// 									{
				// 										minl.clear();
				// 										minv = mesh.vertices[oo].pos.x;
				// 									}
				// 									minl.push_back(oo);
				// 								}
				// 								if (mesh.vertices[oo].pos.x >= maxv)
				// 								{
				// 									if (mesh.vertices[oo].pos.x > maxv)
				// 									{
				// 										maxl.clear();
				// 										maxv = mesh.vertices[oo].pos.x;
				// 									}
				// 									maxl.push_back(oo);
				// 								}
				// 							}
				// 
				// 							if ((nNeighorData & 1 << 0) && (nNeighorData & 1 << 4))
				// 							{
				// 								for (auto it : minl)
				// 								{
				// 									mesh.vertices[it].pos.x -= changebigsize;
				// 									mesh.vertices[it].pos.z -= changebigsize;
				// 									mesh.vertices[it].pos.y += mesh.vertices[it].pos.y > blockHalfHeight ? -changebigsize : changebigsize / 2;
				// 								}
				// 							}
				// 							if ((nNeighorData & 1 << 1) && (nNeighorData & 1 << 6))
				// 							{
				// 								for (auto it : maxl)
				// 								{
				// 									mesh.vertices[it].pos.x += changebigsize;
				// 									mesh.vertices[it].pos.z -= changebigsize;
				// 									mesh.vertices[it].pos.y += mesh.vertices[it].pos.y > blockHalfHeight ? -changebigsize : changebigsize / 2;
				// 								}
				// 							}
				// 						}
				// 						else if (3 == d)
				// 						{
				// 							std::vector<int> minl;
				// 							std::vector<int> maxl;
				// 							float minv = 50.f;
				// 							float maxv = 50.f;
				// 							for (int oo = 4; oo < 12; oo++)
				// 							{
				// 								if (mesh.vertices[oo].pos.x <= minv)
				// 								{
				// 									if (mesh.vertices[oo].pos.x < minv)
				// 									{
				// 										minl.clear();
				// 										minv = mesh.vertices[oo].pos.x;
				// 									}
				// 									minl.push_back(oo);
				// 								}
				// 								if (mesh.vertices[oo].pos.x >= maxv)
				// 								{
				// 									if (mesh.vertices[oo].pos.x > maxv)
				// 									{
				// 										maxl.clear();
				// 										maxv = mesh.vertices[oo].pos.x;
				// 									}
				// 									maxl.push_back(oo);
				// 								}
				// 							}
				// 
				// 							if ((nNeighorData & 1 << 0) && (nNeighorData & 1 << 5))
				// 							{
				// 								for (auto it : minl)
				// 								{
				// 									mesh.vertices[it].pos.x -= changebigsize;
				// 									mesh.vertices[it].pos.z += changebigsize;
				// 									mesh.vertices[it].pos.y += mesh.vertices[it].pos.y > blockHalfHeight ? -changebigsize : changebigsize / 2;
				// 								}
				// 							}
				// 							if ((nNeighorData & 1 << 1) && (nNeighorData & 1 << 7))
				// 							{
				// 								for (auto it : maxl)
				// 								{
				// 									mesh.vertices[it].pos.x += changebigsize;
				// 									mesh.vertices[it].pos.z += changebigsize;
				// 									mesh.vertices[it].pos.y += mesh.vertices[it].pos.y > blockHalfHeight ? -changebigsize : changebigsize / 2;
				// 								}
				// 							}
				// 						}
				// 					}
				// 				}
				psubmesh->addGeomFace(mesh, &blockpos);
			}
		}
	}
}

void SnowBlockMaterial::onActorMoving(World* pworld, const WCoord& blockpos, IClientActor* actor)
{
	//if (actor->getObjType() != OBJ_TYPE_ROLE) return;
	int randNum = GenRandomInt(0, 100);//生成随机数0~100
	if (randNum <= 50)
	{
		pworld->getEffectMgr()->playParticleEffectAsync(pStepOnSnowName, blockpos * BLOCK_SIZE + WCoord(BLOCK_SIZE / 2, 0, BLOCK_SIZE / 2), 20, 0, 0, false);
	}
}

void SnowBlockMaterial::dropBlockAsItem(World* pworld, const WCoord& blockpos, int blockdata, BLOCK_MINE_TYPE droptype, float chance, int uin)
{
	CubeBlockMaterial::dropBlockAsItem(pworld, blockpos, blockdata, droptype, chance, uin);
	int count = blockdata % 4;
	for (int i=0;i<count;i++)
	{
		CubeBlockMaterial::dropBlockAsItem(pworld, blockpos, blockdata, droptype, chance, uin);
	}
}


int SnowBlockMaterial::getPlaceBlockData(World* pworld, const WCoord& blockpos, DirectionType face, float hitptx, float hitpty, float hitptz, int def_blockdata)
{
	// 	int blockid = pworld->getBlockID(blockpos);
	// 
	// 	if (blockid == getBlockResID())
	// 		return pworld->getBlockData(blockpos) + 1;
	return 0;
}
