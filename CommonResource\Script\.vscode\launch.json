{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "type": "lua",
            "request": "launch",
            "internalConsoleOptions": "openOnFirstSessionStart",
            "name": "LuaPanda_iworld",
            "program": "${workspaceFolder}/../Bin",
            "cwd": "${workspaceFolder}",
            "pathCaseSensitivity": true,
            "docPathReplace": [],
            "luaFileExtension": "",
            "connectionPort": 8888,
            "stopOnEntry": true,
            "useCHook": true,
            "autoPathMode": true,
            "logLevel": 1
        },
        {
            "type": "lua",
            "request": "launch",
            "internalConsoleOptions": "openOnFirstSessionStart",
            "name": "LuaPanda_scriptsupport",
            "cwd": "${workspaceFolder}",
            "pathCaseSensitivity": true,
            "docPathReplace": [],
            "luaFileExtension": "",
            "connectionPort": 8819,
            "stopOnEntry": true,
            "useCHook": true,
            "autoPathMode": true,
            "logLevel": 1
        },
        {
            "type": "lua",
            "request": "launch",
            "internalConsoleOptions": "openOnFirstSessionStart",
            "name": "LuaPanda_minigames",
            "cwd": "${workspaceFolder}/../",
            "pathCaseSensitivity": true,
            "docPathReplace": [],
            "luaFileExtension": "",
            "connectionPort": 8888,
            "stopOnEntry": true,
            "useCHook": true,
            "autoPathMode": true,
            "logLevel": 1
        },
        {
            "type": "lua",
            "request": "launch",
            "internalConsoleOptions": "openOnFirstSessionStart",
            "name": "winserver",
            "cwd": "${workspaceFolder}",
            "pathCaseSensitivity": true,
            "docPathReplace": [],
            "luaFileExtension": "",
            "connectionPort": 8889,
            "stopOnEntry": true,
            "useCHook": true,
            "autoPathMode": true,
            "logLevel": 1
        }
    ]
}