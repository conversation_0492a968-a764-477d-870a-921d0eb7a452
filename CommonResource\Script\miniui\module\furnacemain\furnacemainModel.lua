--声明
local furnacemainModel = Class("furnacemainModel",ClassList["UIBaseModel"])

--创建
function furnacemainModel:Create(param)
	return ClassList["furnacemainModel"].new(param)
end

--初始化
function furnacemainModel:Init(param)
	self.super:Init(param)

end

function furnacemainModel:SetIncomingParam(param)
	self.data.incomingParam = param
	self.blockid = param.blockid

	MiniLog("furnacemainModel:SetIncomingParam", self.blockid)
end

function furnacemainModel:GetBlockId()
	return self.data.incomingParam.blockid
end

