
#ifndef __BLOCKOXYGENJAR_H__
#define __BLOCKOXYGENJAR_H__

#include "BlockMaterial.h"

class OxygenJarMaterial : public ModelBlockMaterial //tolua_exports
{ //tolua_exports
	DECLARE_BLOCKMATERIAL(OxygenJarMaterial)
public:
	virtual void initDefaultMtl()override;
	//tolua_begin
	OxygenJarMaterial();
	virtual ~OxygenJarMaterial();
	//virtual const char *getGeomName();
	virtual void init(int resid);
	virtual void createBlockMesh(const BuildSectionMeshData& data, const WCoord &blockpos, SectionMesh *poutmesh) override;
	virtual SectionMesh *createBlockProtoMesh(int protodata = 0);
	virtual BlockTexElement *getDestroyTexture(Block pblock, BlockTexDesc &desc) override;
	virtual void onNotify(World *pworld, const WCoord &blockpos, int blockid);
	virtual void dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata=0, BLOCK_MINE_TYPE droptype=BLOCK_MINE_NOTOOL, float chance=1.0f, int uin = -1);
	//virtual int getBlockGeomID(int *idbuf, int *dirbuf, const SharedSectionData* sectionData, const WCoord &blockpos, World* world);
	virtual void onBlockRemoved(World *pworld, const WCoord &blockpos, int blockid, int blockdata);
	//tolua_end
private:
	virtual void initGeomName() override;
private:
	//RenderBlockMaterial *m_StageMtls[4];
	const static int Mtl_Num = 4;
	unsigned int m_stageMtlsIndex[Mtl_Num];
}; //tolua_exports

#endif