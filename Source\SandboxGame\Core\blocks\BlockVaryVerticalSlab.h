
#ifndef __BLOCKVARYVERTICALSLAB_H__
#define __BLOCKVARYVERTICALSLAB_H__

#include "BlockVerticalSlab.h"

class VerticalVarySlabMaterial : public VerticalSlabMaterial //tolua_exports
{ //tolua_exports
// 	typedef VerticalSlabMaterial Super;
	DECLARE_SCENEOBJECTCLASS(VerticalVarySlabMaterial)
	//DECLARE_BLOCKINSTANCE(SlabMaterial)
public:
	VerticalVarySlabMaterial() {}
	virtual ~VerticalVarySlabMaterial() {}
	//tolua_begin

	void initExVertData();
	virtual void initVertData();
	virtual bool hasSolidTopSurface(int blockdata);
	virtual bool canAttachedToMecha(int curblockdata, DirectionType dir, int neighbor_id, int neighbor_data) override;
	virtual void dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata=0, BLOCK_MINE_TYPE droptype=BLOCK_MINE_NOTOOL, float chance=1.0f, int uin = -1) override;
	virtual char* getPhisicMeshBit(BaseSection *psection, const WCoord &blockpos) override;
	virtual void createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh) override;
	virtual SectionMesh* createBlockProtoMesh(int protodata = 0);
	virtual void createCollideData(CollisionDetect* coldetect, World* pworld, const WCoord& blockpos);
	virtual bool coverNeighbor(int curblockdata, SolidBlockMaterial* neighbor, int neighbor_data, DirectionType dir) override;
	virtual BlockMaterial::BlockType BlockTypeId() { return BlockMaterial::BlockType::BlockType_VerticalSlab; }
private:
	virtual float getBlockHeight(int blockdata);

	dynamic_array<dynamic_array<BlockGeomVert>> m_mLittleHalfWholeFace;
	dynamic_array<dynamic_array<BlockGeomVert>> m_mLittleHalfFace;
	dynamic_array<dynamic_array<BlockGeomVert>> m_mBigHalfWholeFace;
	dynamic_array<dynamic_array<BlockGeomVert>> m_mBigHalfFace;
public:
	class BlockInstance : public Super::BlockInstance
	{
		DECLARE_SCENEOBJECTCLASS(BlockInstance)
	public:
		static MNSandbox::ReflexClassParam<BlockInstance, int> R_Dir;
	};
	virtual MNSandbox::AutoRef<BlockMaterial::BlockInstance> GetBlockInstance() override
	{
		return VerticalVarySlabMaterial::BlockInstance::NewInstance();
	}
}; //tolua_exports

#endif