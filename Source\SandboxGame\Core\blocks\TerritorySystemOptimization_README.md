# 领地系统实时查询优化

## 概述

本次优化为领地系统引入了八叉树空间分割数据结构，显著提升了玩家位置查询的性能。玩家现在可以实时查询自己是否在自己的领地或被授权的领地范围内。

## 性能对比

| 查询方式 | 时间复杂度 | 适用场景 |
|---------|-----------|---------|
| 原始线性搜索 | O(n) | 领地数量 < 100 |
| 八叉树优化 | O(log n) | 领地数量 > 100 |

## 核心功能

### 1. 实时领地查询
```cpp
// 检查玩家是否在授权领地内
bool isInOwnTerritory = TerritoryQuery::IsPlayerInOwnTerritory(player);

// 检查指定位置是否在玩家授权领地内
bool canAccess = TerritoryQuery::IsPlayerInOwnTerritory(position, playerUin);
```

### 2. 建造权限检查
```cpp
// 检查是否可以在指定位置建造
bool canBuild = TerritoryQuery::CanPlayerBuildAt(blockPos, playerUin);
```

### 3. 详细领地信息
```cpp
auto info = TerritoryQuery::GetPlayerTerritoryInfo(player);
if (info.isInTerritory) {
    if (info.isOwner) {
        // 玩家是领地主人
    } else if (info.isAuthorized) {
        // 玩家被授权访问
    }
}
```

## 八叉树优化

### 核心参数
- **最大深度**: 6层
- **每节点最大领地数**: 4个
- **世界边界**: (-50000, -1000, -50000) 到 (50000, 1000, 50000)

### 自适应优化
- 当领地数量 > 4 时自动细分节点
- 支持动态添加/移除领地
- 自动重建优化结构

## 新增文件

### 1. TerritoryOctree.h/cpp
八叉树空间分割实现，提供高效的空间查询算法。

### 2. TerritoryQueryInterface.h/cpp
便捷的查询接口，简化游戏逻辑中的领地查询操作。

### 3. TerritoryQueryExample.cpp
详细的使用示例，展示各种查询场景。

## 使用方法

### 基础查询
```cpp
// 检查玩家当前位置
if (TerritoryQuery::IsPlayerInOwnTerritory(player)) {
    player->showMessage("您在自己的领地内");
}

// 检查建造权限
if (TerritoryQuery::CanPlayerBuildAt(buildPos, playerUin)) {
    // 允许建造
} else {
    // 拒绝建造
}
```

### 实时监控
```cpp
class PlayerTerritoryMonitor {
public:
    void Update() {
        bool currentStatus = TerritoryQuery::IsPlayerInOwnTerritory(m_player);
        if (currentStatus != m_lastStatus) {
            // 领地状态发生变化
            HandleTerritoryStatusChange(currentStatus);
        }
    }
};
```

### 性能监控
```cpp
auto stats = TerritoryQuery::GetSystemStats();
printf("八叉树节点数: %d, 最大深度: %d\n", stats.octreeNodes, stats.maxDepth);
```

## 配置选项

### 启用/禁用八叉树优化
```cpp
// 启用八叉树优化（默认启用）
TerritoryQuery::SetOptimizationEnabled(true);

// 禁用八叉树优化（回退到线性搜索）
TerritoryQuery::SetOptimizationEnabled(false);
```

### 性能建议
- **领地数量 < 50**: 可以禁用八叉树优化
- **领地数量 50-500**: 建议启用八叉树优化
- **领地数量 > 500**: 强烈建议启用八叉树优化

## 兼容性

### 向后兼容
- 保留原有的 TerritoryManager 接口
- 自动回退机制：八叉树失败时使用线性搜索
- 现有代码无需修改即可获得性能提升

### 内存使用
- 八叉树节点内存开销：约 64 bytes/节点
- 每个领地平均增加内存开销：约 16 bytes
- 总体内存增加：< 1% (对于1000个领地)

## 测试建议

### 性能测试
```cpp
void PerformanceTest() {
    auto start = std::chrono::high_resolution_clock::now();
    
    // 执行1000次查询
    for (int i = 0; i < 1000; i++) {
        TerritoryQuery::IsPlayerInOwnTerritory(randomPosition, playerUin);
    }
    
    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
    printf("1000次查询耗时: %ld 微秒\n", duration.count());
}
```

### 功能测试
- 测试领地边界检测的准确性
- 测试权限检查的正确性
- 测试八叉树的动态更新

## 常见问题

### Q: 八叉树是否影响内存使用？
A: 影响很小，对于1000个领地大约增加64KB内存。

### Q: 如何调试查询性能？
A: 使用 `TerritoryQuery::GetSystemStats()` 获取详细统计信息。

### Q: 是否支持动态世界边界？
A: 当前使用固定边界，可以通过修改 TerritoryManager 构造函数调整。

### Q: 如何处理大量玩家同时查询？
A: 八叉树支持并发读取，查询性能随玩家数量线性缩放。

## 未来优化

### 可能的改进方向
1. **动态世界边界**: 根据实际领地分布自动调整八叉树边界
2. **缓存系统**: 为频繁查询的位置添加缓存
3. **异步查询**: 支持异步批量查询以进一步提升性能
4. **空间索引**: 结合其他空间索引算法（如 R-tree）
5. **内存池**: 使用内存池减少八叉树节点的分配开销

### 性能监控
建议在生产环境中监控以下指标：
- 平均查询时间
- 八叉树深度分布
- 内存使用情况
- 查询命中率






