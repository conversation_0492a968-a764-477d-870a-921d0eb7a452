/*
	Desc:三棱柱
	Auth:chenwei
	Date:2023-09-12
*/

#ifndef __BLOCK_TRIANGULAR_PRISM_H__
#define __BLOCK_TRIANGULAR_PRISM_H__

#include "BlockMaterial.h"
#include "BlockWholeTriangle.h"
struct TriangularPrismPhyModel
{
	dynamic_array<Rainbow::Vector3f> verts;
	dynamic_array<UInt16> idxs;
	int triangleCount;
	TriangularPrismPhyModel() :
		triangleCount(0)
	{
	}
};

enum MergeType
{
	MergeNone = 0,
	MergeLeft = 1 << 0,
	MergeRight = 1 << 1,
	MergeBoth = 1 << 2,
	MergeFront = 1 << 3,
	MergeBack = 1 << 4,
	MergeTurn = 1 << 7,
};


class TriangularPrismMaterial : public WholeTriangleMaterial
{ 
	DECLARE_SCENEOBJECTCLASS(TriangularPrismMaterial)
public:
	TriangularPrismMaterial();
	virtual ~TriangularPrismMaterial();
	//tolua_begin
	virtual void init(int resid) override;

	void initWholeFaceVertData();
	virtual void initVertData();
	virtual void initTriangleFaceVertData();
	virtual void initSlantFaceVertData();
	void initTriangleSlantFaceVertData();
	void initTriangleSlantFace2VertData();
	void initTrapezoidSlantFaceVertData();
	virtual void initPhyModelData();
	
	virtual void dropBlockAsItem(World* pworld, const WCoord& blockpos, int blockdata = 0, BLOCK_MINE_TYPE droptype = BLOCK_MINE_NOTOOL, float chance = 1.0f, int uin = -1) override;
	virtual int getPhisicMeshVerts(BaseSection* psection, const WCoord& blockpos, dynamic_array<Rainbow::Vector3f>& verts, dynamic_array<UInt16>& idxs);
	
	BLOCK_RENDERTYPE_T GetAttrRenderType() const;/* 渲染类型 */
	virtual void onBlockPlacedBy(World* pworld, const WCoord& blockpos, IClientPlayer* player);
	bool isSameUpsidedown(const Block& pblockA, const Block pblockB);
	void getFaceIdxVecByDir(std::vector<int>& vec, int angle);
	
	MergeType getMergeType(const SectionDataHandler* psection, Block& pblock, const WCoord& blockpos, int curDir);
	virtual void createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh) override;
	virtual SectionMesh* createBlockProtoMesh(int protodata = 0);
	void ParseLuaVertCfg(const char* strFuncName, dynamic_array<dynamic_array<BlockGeomVert>>& vecFaces, BlockVector& normal_dir, BlockVector& vertcolor, int dir, BlockGeomVert vert[], const int vertLen);
	void ParseLuaFaceCfg(const char* strFuncName, std::vector<int>& vec, int dir);
	virtual void createCollideData(CollisionDetect* coldetect, World* pworld, const WCoord& blockpos);
	virtual bool coverNeighbor(int curblockdata, SolidBlockMaterial* neighbor, int neighbor_data, DirectionType dir) override;
	
	unsigned char TriangleNormal2LightColor(const Rainbow::Vector3f& normal);
	virtual int convertDataByRotate(int blockdata, int rotatetype);
	virtual BlockMaterial::BlockType BlockTypeId() { return BlockMaterial::BlockType::BlockType_SlantBlock; }
public:
	static dynamic_array<UInt16>* m_dNegIndices;
	static dynamic_array<UInt16>* m_dPosIndices;
	static dynamic_array<UInt16>* m_NegTrIndices;
	static dynamic_array<UInt16>* m_PosTrIndices;
	static Rainbow::Vector3f* ms_LightVec;

	/*
	vector<int> m_vecFaceDir0;
	vector<int> m_vecFaceDir1;
	vector<int> m_vecFaceDir2;
	vector<int> m_vecFaceDir3;


	vector<int> m_vecTriangleFaceDir0;
	vector<int> m_vecTriangleFaceDir1;
	vector<int> m_vecTriangleFaceDir2;
	vector<int> m_vecTriangleFaceDir3;
	*/
private:
	virtual float getBlockHeight(int blockdata);

protected:

	static dynamic_array<dynamic_array<BlockGeomVert>>& m_mWholeFace();

	dynamic_array<dynamic_array<BlockGeomVert>> m_mTriangleFace;
	dynamic_array<dynamic_array<BlockGeomVert>> m_mTriangleSlantFace;
	dynamic_array<dynamic_array<BlockGeomVert>> m_mSlantFace;
	dynamic_array<dynamic_array<BlockGeomVert>> m_mTurnSlantFace;
	dynamic_array<dynamic_array<BlockGeomVert>> m_mTriangleSlantFace2; //等腰三角形斜面
	dynamic_array<dynamic_array<BlockGeomVert>> m_mTrapezoidSlantFace; //梯形斜面
	

	std::map<unsigned short, TriangularPrismPhyModel> m_mPhyModel;
public:
	class BlockInstance : public Super::BlockInstance
	{
		DECLARE_SCENEOBJECTCLASS(BlockInstance)
	public:
		static MNSandbox::ReflexClassParam<BlockInstance, int> R_Dir;
	};
	virtual MNSandbox::AutoRef<BlockMaterial::BlockInstance> GetBlockInstance() override
	{
		return TriangularPrismMaterial::BlockInstance::NewInstance();
	}
}; 

#endif //__BLOCK_TRIANGULAR_PRISM_H__