
#ifndef __BLOCK_BED_H__
#define __BLOCK_BED_H__
#include "SandboxGame.h"
#include "BlockMaterial.h"

class BedLogicInterface {
public:
	virtual bool IsBedOccupied(World* pworld, const WCoord& blockpos, int blockdata) { return false; };
	virtual void setBedOccupied(World* pworld, const WCoord& blockpos, bool occupied) {};
	virtual bool getNearestEmptyChunkCoordinates(WCoord& ret, World* pworld, const WCoord& blockpos, int loopcount) { return false; };
	virtual void getEyePosInBed(World* pworld, const WCoord& sleeppos, WCoord& eyepos, Rainbow::Vector3f& lookdir) {};
	virtual WORLD_ID getBindActor(World* pworld, const WCoord& blockpos) { return 0; };
	virtual void setBindActor(World* pworld, const WCoord& blockpos, WORLD_ID bindactor) {};
	virtual WCoord getSleepPosition(World* pworld, const WCoord& blockpos) { return WCoord(); };
	virtual WorldContainer* getCoreContainer(World* pworld, const WCoord& blockpos) { return NULL; };
	virtual bool IsSleepNeedHide(World* pworld, const WCoord& blockpos) { return false; };
};

class EXPORT_SANDBOXGAME BedLogicHandle
{
public:
	static bool IsBedOccupied(World* pworld, const WCoord& blockpos, int blockdata);
	static void setBedOccupied(World* pworld, const WCoord& blockpos, bool occupied);
	static bool getNearestEmptyChunkCoordinates(WCoord& ret, World* pworld, const WCoord& blockpos, int loopcount);
	static void getEyePosInBed(World* pworld, const WCoord& sleeppos, WCoord& eyepos, Rainbow::Vector3f& lookdir);
	static WORLD_ID getBindActor(World* pworld, const WCoord& blockpos);
	static void setBindActor(World* pworld, const WCoord& blockpos, WORLD_ID bindactor);
	static WCoord getSleepPosition(World* pworld, const WCoord& blockpos);
	static WorldContainer* getCoreContainer(World* pworld, const WCoord& blockpos);
	static bool IsSleepNeedHide(World* pworld, const WCoord& blockpos);
private:
	static BedLogicInterface* getMaterial(World* pworld, const WCoord& blockpos);
};

class BlockBed : public ModelBlockMaterial, public BedLogicInterface //tolua_exports
{ //tolua_exports
	DECLARE_BLOCKMATERIAL(BlockBed)
	//DECLARE_BLOCKINSTANCE(BlockBed)
public:
	//tolua_begin
	BlockBed();

	virtual void init(int resid) override;
	//virtual const char *getGeomName() override;
	virtual bool onTrigger(World *pworld, const WCoord &blockpos, DirectionType face, IClientPlayer *player, const Rainbow::Vector3f &colpoint=Rainbow::Vector3f(0, 0, 0));
	virtual void onNotify(World *pworld, const WCoord &blockpos, int blockid);
	virtual void dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata=0, BLOCK_MINE_TYPE droptype=BLOCK_MINE_NOTOOL, float chance=1.0f, int uin = -1);
	virtual void createCollideData(CollisionDetect *coldetect, World *pworld, const WCoord &blockpos);
	virtual int convertDataByRotate(int blockdata, int rotatetype) override;
	virtual void onBlockRemoved(World *pworld, const WCoord &blockpos, int blockid, int blockdata);
	virtual void onBlockPlacedBy(World *pworld, const WCoord &blockpos, IClientPlayer *player) override;

	//socbed
	void addSocRevivePoint(World* pworld, IClientPlayer* player, const WCoord& blockpos);
	void removeSocRevivePoint(World* pworld, const WCoord& blockpos);

	//virtual bool hasContainer();
	virtual SectionMesh *createBlockProtoMesh(int protodata = 0);
	virtual void blockTick(World* pworld, const WCoord& blockpos);
	virtual void ignoreCheckUpBlockWhenNotify(bool ignore)
	{
		m_ignoreCheckUpBlock = ignore;
	}
	////////////////////////////logic/////////////////////////////////
	virtual bool IsBedOccupied(World* pworld, const WCoord& blockpos, int blockdata) override;
	virtual void setBedOccupied(World* pworld, const WCoord& blockpos, bool occupied) override;
	virtual bool getNearestEmptyChunkCoordinates(WCoord& ret, World* pworld, const WCoord& blockpos, int loopcount) override;
	virtual void getEyePosInBed(World* pworld, const WCoord& sleeppos, WCoord& eyepos, Rainbow::Vector3f& lookdir) override;
	virtual WORLD_ID getBindActor(World* pworld, const WCoord& blockpos) override;
	virtual void setBindActor(World* pworld, const WCoord& blockpos, WORLD_ID bindactor) override;
	virtual WCoord getSleepPosition(World* pworld, const WCoord& blockpos) override;
	virtual WorldContainer* getCoreContainer(World* pworld, const WCoord& blockpos) override;
	//tolua_end
	virtual bool onBlockDamaged(World* pworld, const WCoord& blockpos, IClientPlayer* player, int attack_type, float damage) override;
	virtual int getBlockHP(World* pworld, const WCoord& blockpos) override;
	virtual bool getBlockRange(World* pworld, const WCoord& blockpos, std::vector<WCoord>& blockList, bool includeSelf = false) override;
	
	// 获取床的核心坐标（床脚位置）
	static WCoord getBedCorePosition(World* pworld, const WCoord& blockpos);

private:
	virtual void initGeomName() override;
protected:
    virtual int doSleep(World *pworld, IClientPlayer *player,const WCoord & blockpos)const;


	virtual int getBlockGeomID(int *idbuf, int *dirbuf, const SectionDataHandler* sectionData, const WCoord &blockpos, World* world);

	bool m_ignoreCheckUpBlock;

public:
	DECLARE_BLOCKMATERIAL_INSTANCE_BEGIN(BlockBed)
		DECLARE_BLOCKMATERIAL_INSTANCE_R_PARAM(R_Dir, int)
	DECLARE_BLOCKMATERIAL_INSTANCE_END(BlockBed)

}; //tolua_exports

class SleepingBag : public BlockBed //tolua_exports
{ //tolua_exports
    DECLARE_BLOCKMATERIAL(SleepingBag)
protected:
  //  virtual int doSleep(World *pworld, IClientPlayer *player,const WCoord & blockpos)const override;
 	virtual int getBlockGeomID(int *idbuf, int *dirbuf, const SectionDataHandler* sectionData, const WCoord &blockpos, World* world) override;
    virtual void createCollideData(CollisionDetect *coldetect, World *pworld, const WCoord &blockpos) override;
	virtual bool IsSleepNeedHide(World* pworld, const WCoord& blockpos) { return true; };
}; //tolua_exports

// ����Ÿ�ĸ��
class BlockSanrioBed : public BlockBed //tolua_exports
{ //tolua_exports
	DECLARE_BLOCKMATERIAL(BlockSanrioBed)
protected:
	virtual int doSleep(World* pworld, IClientPlayer* player, const WCoord& blockpos)const override;
	virtual void createCollideData(CollisionDetect* coldetect, World* pworld, const WCoord& blockpos) override;

	virtual bool canPutOntoPos(WorldProxy* pworld, const WCoord& blockpos) override;

	virtual WCoord getSleepPosition(World* pworld, const WCoord& blockpos) override;
	virtual bool IsSleepNeedHide(World* pworld, const WCoord& blockpos) override { return false; };
}; //tolua_exports

#endif