#ifndef __BLOCK_PILE_H__
#define __BLOCK_PILE_H__

#include "BlockMaterial.h"
#include "SandboxIdDef.h"

//#define BLOCKPILEUSE_SIMPLE

#ifndef  BLOCKPILEUSE_SIMPLE

	//这个类是用来处理一些X x Y x Z堆叠的方块, 显示模型只用一个特殊方块来显示
	//特殊方块里储存着方向, 其余方块里 4个位保存着检测的方向.
	class BlockPile : public ModelBlockMaterial
	{
		DECLARE_BLOCKMATERIAL(BlockPile)
	public:
		BlockPile() {};
		virtual ~BlockPile() {};
		//列举blockid, 因为配置表里不能加很多的id, 也不想频繁的去取material来判断.
		//后面做优化也许会删掉
		static bool pileIsSpecialBlockId(int blockid)
		{
			if (blockid >= 200012 && blockid <= 200100)
			{
				return true;
			}
			return false;
		}

		static bool pileIsMonsterStatueSpecialBlockId(int blockid)
		{
			if (blockid >= 200012 && blockid <= 200100)
			{
				return true;
			}
			return false;
		}

		static bool pileIsNormalBlock(int blockid)
		{
			return blockid >= BLOCK_PILE_TOP && blockid <= BLOCK_MONSTER_STATUE_LAYER;
		}
		/*virtual std::string getGeomName() override
		{
			return "energy_beam";
		}*/
		//virtual void init(int resid);
		//virtual void initDefaultMtl() override;
		/*
		一些堆叠方块有特殊交互onTrigger需求, 按理没有特殊交互的和有特殊交互的可以分成不同的block类, 但是block表不允许加那么多的方块id.
		如果后面block data扩展,那么可以加一些额外数据来做区分和优化. 目前,针对不同方块的特殊交互处理都写在了基类pile中.
		后果是交互一下都会去找special方块, 不需要交互处理的白白损耗了性能. 
		onNotify 同理todo
		*/
		virtual void onNotify(World* pworld, const WCoord& blockpos, int blockid) override;
		virtual bool onTrigger(World* pworld, const WCoord& blockpos, DirectionType face, IClientPlayer* player, const Rainbow::Vector3f& colpoint = Rainbow::Vector3f(0, 0, 0)) override;
		int topId() const { return BLOCK_PILE_TOP; }
		int centerId() const { return BLOCK_PILE_CENTER; }
		int downId() const { return BLOCK_PILE_BOTTOM; }
		int getSurroundId() const { return m_BlockResID; }
		virtual bool isTopId(int blockid, int data) const { return blockid == topId(); }
		virtual bool isCenterId(int blockid, int data) const { return blockid == centerId(); }
		virtual bool isDownId(int blockid, int data) const { return blockid == downId(); }
		virtual bool isSpecialId(int blockid, int data) const { return pileIsSpecialBlockId(blockid); }
		virtual bool isSurroundId(int blockid, int data) const { return blockid == getSurroundId(); }
		//int getSpecialId() const { return -1; }
		bool checkSpecialBlock(int blockid) const;
		virtual bool needCheckTop() const { return false; }
		virtual bool needCheckDown() const { return false; }
		virtual bool needCheckX() const { return true; }
		virtual bool needCheckZ() const { return true; }
		virtual bool needCheckSpecial() const { return true; }
		bool checkTopId(int blockid,  int data) const;
		bool checkDownId(int blockid, int data) const;
		bool checkXPOSId(int blockid, int data) const;
		bool checkXNEGId(int blockid, int data) const;
		bool checkZPOSId(int blockid, int data) const;
		bool checkZNEGId(int blockid, int data) const;
		virtual void createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh) override;
		//virtual SectionMesh* createBlockProtoMesh(int protodata = 0) override;
	//	virtual BlockDrawType getDrawType() override { return (BlockDrawType)m_drawType; }

		virtual bool isSpecialBlock()const { return false; }
		Block getSpecialBlock(World* pworld, const WCoord& blockpos);
		Block getSpecialBlockOnDestory(World* pworld, const WCoord& blockpos, int blockdata);
		virtual void dropBlockAsItem(World* pworld, const WCoord& blockpos, int blockdata, BLOCK_MINE_TYPE droptype, float chance, int uin = -1) override;
		//bool specialNotify(World* pworld, const WCoord& blockpos);
		//bool checkPOSX() const;
		//bool checkNEGX() const;
		//bool checkPOSY() const;
		//bool checkNEGY() const;
		//bool checkPOSZ() const;
		//bool checkNEGZ() const;
	protected:
		bool findXPOS(int data) const { return data & 1; }
		bool findXNEG(int data) const { return data & 2; }
		bool findZPOS(int data) const { return data & 4; }
		bool findZNEG(int data) const { return data & 8; }
	private:
		Block _getSpecialBlock(World* pworld, Block cur, const WCoord& blockpos);
	};

	class BlockPileBottom : public BlockPile
	{
		DECLARE_BLOCKMATERIAL(BlockPileBottom)
	public:
		virtual bool needCheckTop() const override { return true; }
		virtual bool isTopId(int blockid, int data) const override
		{
			return blockid == topId() || blockid == centerId();
		}
	};

	class BlockPileTop : public BlockPile
	{
		DECLARE_BLOCKMATERIAL(BlockPileTop)
	public:
		virtual bool needCheckDown() const override { return true; }
		virtual bool isDownId(int blockid, int data) const;
	};


	class BlockPileCenter : public BlockPileTop
	{
		DECLARE_BLOCKMATERIAL(BlockPileCenter)
	public:
		virtual bool needCheckTop() const override { return true; }
		virtual bool isTopId(int blockid, int data) const;
	};

	//一般只认为特殊方块在左下角
	class BlockPileSpecial : public BlockPileBottom
	{
		DECLARE_BLOCKMATERIAL(BlockPileSpecial)
	public:
		virtual bool isSpecialBlock() const override{ return true; }
		void setRangX(int x) { m_rangeX = x; }
		void setRangY(int y) { m_rangeY = y; }
		void setRangZ(int z) { m_rangeZ = z; }
		void setDrawType(int type) { m_drawType = type; }
		int getRangX() const { return m_rangeX; }
		int getRangY() const { return m_rangeY; }
		int getRangZ() const { return m_rangeZ; }
		virtual bool isSurroundId(int blockid, int data) const { return blockid == BLOCK_PILE_BOTTOM || blockid == BLOCK_PILE_LAYER; }
		virtual BlockType BlockTypeId() override { return BlockType_PileSpecial; }
		virtual bool needCheckX() const 
		{
			return getRangX() > 1;
		}
		virtual bool needCheckZ() const
		{
			return getRangZ() > 1;
		}
		virtual bool needCheckTop() const
		{
			return getRangY() > 1;
		}
	private:
		int m_rangeX = 0;
		int m_rangeZ = 0;
		int m_rangeY = 0;
		int m_drawType = 0;
	};
#else
	class BlockPile : public ModelBlockMaterial
	{
		DECLARE_BLOCKMATERIAL(BlockPile)
	public:
		BlockPile() {};
		virtual ~BlockPile() {};
		void setRangX(int x) { m_rangeX = x; }
		void setRangY(int y) { m_rangeY = y; }
		void setRangZ(int z) { m_rangeZ = z; }
		void setDrawType(int type) { m_drawType = type; }
		int getRangX() const { return m_rangeX; }
		int getRangY() const { return m_rangeY; }
		int getRangZ() const { return m_rangeZ; }
		virtual void onBlockRemoved(World* pworld, const WCoord& blockpos, int blockid, int blockdata) override;
		void removeOtherBlock(World* pworld, const WCoord& blockpos, int blockdata);
		virtual bool isSpecialBlock(int blockdata)const { return blockdata & 8; }
	private:
		//bool specialNotify(World* pworld, const WCoord& blockpos);
		//是否是个合适位置, 以此为左下角,整个范围都是这个方块.
		bool isCorrectPos();
		void _removeOtherBlock();
	private:
		int m_rangeX = 0;
		int m_rangeZ = 0;
		int m_rangeY = 0;
		int m_drawType = 0;
	};
 
#endif //BLOCKPILEUSE_SIMPLE

#endif