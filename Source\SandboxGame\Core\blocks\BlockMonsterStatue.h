#ifndef __BLOCK_MONSTER_STATUR_H__
#define __BLOCK_MONSTER_STATUR_H__

#include "BlockPile.h"

#define MONSTER_STATUE_MAX 10

struct MonsterStatueSpecialMatInfo
{
	std::string matcapTexName;
	std::string mainTexName;
	UInt32 color;
	float paramIntensity;
	float paramProportion;
	int dir;
	float scale;

	MonsterStatueSpecialMatInfo()
	{
		color = 0;
		paramIntensity = 0.0f;
		paramProportion = 0.0f;
		dir = 0;
		scale = 1.0f;
	}
};

//一般只认为特殊方块在左下角
class BlockMonsterStatueSpecial : public BlockPileSpecial
{
	DECLARE_BLOCKMATERIAL(BlockMonsterStatueSpecial)
public:
	struct StatueInfo
	{
		StatueInfo(int mid = -1, int geomnum = -1, int x = 0, int y = 0, int z = 0, float s = 1.0f, 
				   const Rainbow::Vector3f &inOffset = Rainbow::Vector3f::zero)
			: monsterid(mid), geom(geomnum), rangeX(x), rangeY(y), rangeZ(z), scale(s), offset(inOffset) {}
		int monsterid;
		int geom;
		int rangeX;
		int rangeY;
		int rangeZ;
		float scale;
		Rainbow::Vector3f offset;
		bool isValid() const
		{
			return monsterid > 0 && geom > 0;
		}
	};
	BlockMonsterStatueSpecial();
	//virtual ~BlockMonsterStatue();
	virtual bool isSurroundId(int blockid, int data) const { return blockid == BLOCK_MONSTER_STATUE_BOTTOM || blockid == BLOCK_MONSTER_STATUE_LAYER; }
	virtual void createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh) override;
	virtual SectionMesh* createBlockProtoMesh(int protodata = 0) override;
	virtual bool hasContainer() override;
	virtual WorldContainer* createContainer(World* pworld, const WCoord& blockpos) override;
	virtual BlockDrawType getDrawType() override { return BLOCKDRAW_STATUE; }
	virtual void init(int resid) override;
	virtual void initDefaultMtl() override;
	void setMaterialParam(float intensity, float proportion, const char* name) {
		m_paramIntensity = intensity;  m_paramProportion = proportion; m_paramMaterialName = name;
	}
	void setMatrialAppendName(const char* name) { m_append = name; };
	virtual bool needCheckDown() const override { return true; }
	virtual bool isDownId(int blockid, int data) const override
	{
		return blockid == BLOCK_MONSTER_STATUE_FOUNDATION;
	}
	
	const MonsterStatueSpecialMatInfo& getItemMatInfo();

private:
	int m_mtls[MONSTER_STATUE_MAX];
	StatueInfo m_info;
	float m_paramIntensity;
	float m_paramProportion;
	std::string m_paramMaterialName;
	std::string m_append;
	MonsterStatueSpecialMatInfo m_itemMatInfo;
};

//雕像底部特殊,因为有个底部.所以要检测下面的
class BlockMonsterStatueBottom : public BlockPileBottom
{
	DECLARE_BLOCKMATERIAL(BlockMonsterStatueBottom)
public:
	virtual bool needCheckDown() const override { return true; }
	//virtual int getDownId() const override { return BLOCK_MONSTER_STATUE_FOUNDATION; }
	virtual bool isDownId(int blockid, int data) const override
	{
		return blockid == BLOCK_MONSTER_STATUE_FOUNDATION;
	}
};

//一层雕像用的
class BlockMonsterStatueLayer : public BlockMonsterStatueBottom
{
	DECLARE_BLOCKMATERIAL(BlockMonsterStatueLayer)
public:
	virtual bool needCheckDown() const override { return true; }
};

//底座
class BlockMonsterStatueFoundation : public BlockPileBottom
{
	DECLARE_BLOCKMATERIAL(BlockMonsterStatueFoundation)
public:
	virtual bool needCheckDown() const override { return false; }
	virtual bool onTrigger(World* pworld, const WCoord& blockpos, DirectionType face, IClientPlayer* player, const Rainbow::Vector3f& colpoint = Rainbow::Vector3f(0, 0, 0)) override;
	virtual bool isTopId(int blockid, int data) const override
	{
		return blockid == BLOCK_MONSTER_STATUE_BOTTOM || pileIsMonsterStatueSpecialBlockId(blockid);
	}
	virtual void createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh) override;
	virtual void initDefaultMtl() override;
	virtual void dropBlockAsItem(World* pworld, const WCoord& blockpos, int blockdata, BLOCK_MINE_TYPE droptype, float chance, int uin = -1) override;

private:
	int m_mtlsIndex[4];
};

#define BLOCKMONSTERSTATUE_CLASS(CX, CY, CZ, DRAWNAME, PARAM1, PARAM2, PARAM3) \
class BlockMonsterStatueSpecialx##CX##y##CY##z##CZ##DRAWNAME : public BlockMonsterStatueSpecial \
{\
	DECLARE_BLOCKMATERIAL(BlockMonsterStatueSpecialx##CX##y##CY##z##CZ##DRAWNAME)\
public: \
	BlockMonsterStatueSpecialx##CX##y##CY##z##CZ##DRAWNAME()\
	{\
		setRangX(CX);setRangY(CY);setRangZ(CZ); setMaterialParam(PARAM1, PARAM2, #PARAM3); setMatrialAppendName(#DRAWNAME);\
	}\
};


#define BLOCKMONSTERSTATUE_SPECIAL_IMPLEMENT(CX, CY, CZ, DRAWNAME) \
IMPLEMENT_BLOCKMATERIAL(BlockMonsterStatueSpecialx##CX##y##CY##z##CZ##DRAWNAME)

#define BLOCKMONSTERSTATUE_CLASS_WITH_GOLD(CX, CY, CZ) BLOCKMONSTERSTATUE_CLASS(CX, CY, CZ, GOLD, 0.8f, 1.9f, gold.png)
#define BLOCKMONSTERSTATUE_CLASS_WITH_SILIVER(CX, CY, CZ) BLOCKMONSTERSTATUE_CLASS(CX, CY, CZ, SILIVER, 1.f, 2.f, silver.png)
#define BLOCKMONSTERSTATUE_CLASS_WITH_STONE(CX, CY, CZ) BLOCKMONSTERSTATUE_CLASS(CX, CY, CZ, STONE, 1.f, 0.5f, woodStone.jpg)


#define BLOCKMONSTERSTATUE_ALL_CLASS(CX, CY, CZ) \
BLOCKMONSTERSTATUE_CLASS_WITH_GOLD(CX, CY, CZ)\
BLOCKMONSTERSTATUE_CLASS_WITH_SILIVER(CX, CY, CZ)\
BLOCKMONSTERSTATUE_CLASS_WITH_STONE(CX, CY, CZ)

BLOCKMONSTERSTATUE_ALL_CLASS(1, 1, 1)
BLOCKMONSTERSTATUE_ALL_CLASS(1, 2, 1)
BLOCKMONSTERSTATUE_ALL_CLASS(2, 2, 2)
BLOCKMONSTERSTATUE_ALL_CLASS(3, 3, 3)
BLOCKMONSTERSTATUE_CLASS(3, 3, 3, BOSS, 1.5f, 1.1f, boss.png)

#endif