
#include "BlockFlowerGrow.h"
#include "BlockMaterialMgr.h"
#include "BlockGeom.h"
//#include "special_blockid.h"
#include "SectionMesh.h"
#include "ClientMob.h"
#include "TaskData.h"
#include "DefManagerProxy.h"
#include "ClientActorProjectile.h"
#include "ActorLocoMotion.h"
#include "world.h"
#include "SandboxIdDef.h"
using namespace MINIW;
IMPLEMENT_BLOCKMATERIAL(FlowerGrowMaterial)

FlowerGrowMaterial::FlowerGrowMaterial(): m_MaxStages(0)
{
	for (int i = 0; i < MAX_STAGES; i++)
	{
		m_Mtls[i] = NULL;
	}
}

FlowerGrowMaterial::~FlowerGrowMaterial()
{
	for(int i=0; i<MAX_STAGES; i++)
	{
		ENG_RELEASE(m_Mtls[i]);
	}
}

void FlowerGrowMaterial::init(int resid)
{
	Super::init(resid);
	SetToggle(BlockToggle_RandomTick, true);

	m_MaxStages = 2;
	if(m_LoadOnlyLogic) return;

	if (m_Def == NULL)
	{
		return;
	}
	/*
	表格配置提醒
	@ 表格中Texture2对应blockgeom.xml中的一条配置, 名字要对应上
	@ 配置了Texture2代表有自定义的第二阶段模型, 否则不要配置Texture2
	@ 第二阶段模型文件名、贴图文件名 = Texture1 + _middle
	*/
	if (m_Def == NULL)
	{
		return;
	}
	//第一阶段贴图
	m_Mtls[0] = g_BlockMtlMgr.createRenderMaterial((m_Def->Texture1 + "_seed").c_str(), m_Def, GETTEX_NORMAL, BLOCKDRAW_GRASS);
	//第二阶段贴图, Texture1 + _middle
	char texname[256];
	sprintf(texname, "%s_middle", GetBlockDef()->Texture1.c_str());
	m_Mtls[1] = g_BlockMtlMgr.createRenderMaterial(texname, GetBlockDef(), GETTEX_NORMAL, BLOCKDRAW_GRASS);
	
}

//std::string FlowerGrowMaterial::getGeomName()
//{
//	//有自定义的blockgeom配置
//	if (!m_Def->Texture2.empty())
//		return m_Def->Texture2;
//
//	//没有配置Texture2就用默认的blockgeom配置
//	return "flowergrow";
//}

void FlowerGrowMaterial::initGeomName()
{
	if (m_Def && !m_Def->Texture2.empty())
	{
		m_geomName = m_Def->Texture2.c_str();
	}
	else
	{
		m_geomName = "flowergrow";
	}
}
BlockTexElement *FlowerGrowMaterial::getDestroyTexture(Block pblock, BlockTexDesc &desc)
{
	desc.gray = false;
	desc.blendmode = BLEND_ALPHATEST;
	return m_Mtls[m_MaxStages-1]->getTexElement();
}

int FlowerGrowMaterial::getStage(int blockdata)
{
	float s = float(m_MaxStages)*(blockdata+1)/(getMaxGrowStage()+1);
	int stage = int(s+0.5f) - 1;
	
	if(stage >= m_MaxStages) stage = m_MaxStages-1;
	if(blockdata<getMaxGrowStage() && stage==m_MaxStages-1) stage = m_MaxStages-2;

	if(stage < 0) stage = 0;
	return stage;
}

void FlowerGrowMaterial::createBlockMesh(const BuildSectionMeshData& data, const WCoord &blockpos, SectionMesh *poutmesh)
{
	BlockGeomTemplate* geom = getGeom(data.m_LODLevel);
	if (!geom) return;
	auto psection = data.m_SharedSectionData;
	Block pblock = psection->getBlock(blockpos);
	int blockdata = pblock.getData();
	Rainbow::ColorRGBAf verts_light[1] = { Rainbow::ColorRGBAf::black };
	psection->getBlockVertexLight(blockpos, verts_light);

	//const BiomeDef *biome = psection->getBiomeGen(data.m_World, blockpos)->getDef();

	BlockGeomMeshInfo meshinfo;
	int stage = 0;//= getStage(blockdata);
	if(blockdata >= 3)
	  stage = 1;
	SectionSubMesh *psubmesh = poutmesh->getSubMesh(m_Mtls[stage], false);

	SolidBlockMaterial * m_BlockMtl =  dynamic_cast<SolidBlockMaterial *>(g_BlockMtlMgr.getMaterial(psection->getNeighborBlock(blockpos,WCoord(0,-1,0)).getResID()));
	float height = 1.0f;
	if(m_BlockMtl)
	 height = m_BlockMtl->getBlockHeight(0);

	 const BlockDef *defbase = GetDefManagerProxy()->getBlockDef(m_BlockResID);
	//getGeom()->getFaceVerts(meshinfo, 0);
	//if(defbase->ModelIndex != BLOCK_SUN_FLOWER || stage == 0)
	//	getGeom()->getModelFaceVerts(meshinfo, 0, 0, 0, 0, 0, (int)((height - 1.0f)*BLOCK_SIZE));
	//else
	//	getGeom()->getModelFaceVerts(meshinfo, 1, 0, 0, 0, 0, (int)((height - 1.0f)*BLOCK_SIZE));
	// 
	 //有自定义的第二阶段模型则用之, 否则就跟第一阶段一样是十字贴片
	 int meshsize = geom->getMeshCount();
	 int meshIndex = stage < meshsize ? stage : 0;
	 geom->getModelFaceVerts(meshinfo, meshIndex, 0, 0, 0, 0, (int)((height - 1.0f) * BLOCK_SIZE));

	psubmesh->addGeomBlockLight(meshinfo, &blockpos, verts_light, NULL, m_Mtls[stage]->getUVTile());
}

void FlowerGrowMaterial::blockTick(World *pworld, const WCoord &blockpos)
{
	if (!pworld)
	{
		return;
	}
	HerbMaterial::blockTick(pworld, blockpos);

	if (pworld->getCurMapID() >= MAPID_MENGYANSTAR)
	{
		if (pworld->getBlockID(TopCoord(blockpos)) != BLOCK_PLANTSPACE_OXYGEN)
		{
			return;
		}
	}

	bool cangrow = false;
	const BlockDef* def = GetBlockDef();
	int blockdata = pworld->getBlockData(blockpos);

	if (def && def->CropsSign > 0 && def->GrowthTimeNum > 0)
	{
		cangrow = true;
	}
	else
	{
		cangrow = pworld->getBlockLightValue(blockpos) >= 9;
	}
	//红土 土坑不能生成
	if (pworld->getBlockID(DownCoord(blockpos)) == BLOCK_FARMLAND_PIT)
	{
		cangrow = false;
	}
	if (pworld->getBlockID(DownCoord(blockpos)) == BLOCK_DIRT_FREEZE_PIT && canGrowOnFreezePit(pworld->getBlockID(blockpos)) == false)//冻土坑只允许部分植物生长
	{
		cangrow = false;
	}

	if (blockdata >= 6 && isVoidPlant()) //只在最后阶段判断
	{
		cangrow = pworld->getBlockLightValue(blockpos) <= 9;
	}

	if(cangrow)
	{
		if(blockdata < 7)
		{
			float canSpeedUpGrow = false;

			float rangexz = 4;
			CollideAABB box;
			box.pos = blockpos * BLOCK_SIZE;
			box.dim = WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE);
			box.expand((int)(rangexz*BLOCK_SIZE), 2*BLOCK_SIZE, (int)(rangexz*BLOCK_SIZE));

			std::vector<IClientActor *>actors;
			pworld->getActorsOfTypeInBox(actors, box, OBJ_TYPE_MONSTER);
			int speedUpMonsterID = 3121;
			WCoord pos = BlockBottomCenter(blockpos);

			for (size_t i = 0; i < actors.size(); i++)
			{
				ClientMob *mob = static_cast<ClientMob *>(actors[i]);
				if (mob->getDef()->ID == speedUpMonsterID)
				{
					WCoord vec = mob->getLocoMotion()->getPosition() - pos;
					float dist = vec.length();
					if (dist < rangexz * BLOCK_SIZE)
					{
						canSpeedUpGrow = true;
						break;
					}
				}
			}
			const BlockDef* def = GetBlockDef();
			if ( def->CropsSign == 0 || def->GrowthTimeNum == 0)
			{
				float growrate = getGrowRate(pworld, blockpos);
				if (m_BlockResID == BLOCK_WINTER_FLOWER)
					growrate = 3.0f;
				if (canSpeedUpGrow)
				{
					growrate = Rainbow::Min(growrate * 2, 25.0f);
				}

				if (GenRandomInt(0, int(25.0f / growrate)) == 0)
				{
					pworld->setBlockData(blockpos, blockdata + 1, 2);

					if (blockdata >= 6 && canGrowInBlockLight(pworld, blockpos))
					{
						onMakeFlower(pworld, blockpos);
					}
				}
			}
			else
			{
				dealNewGrow(pworld, blockpos,2);
					if (pworld->getBlockData(blockpos) >= 6 && canGrowInBlockLight(pworld, blockpos))
					{
						onMakeFlower(pworld, blockpos);
					}
			
			}		
		}
	}
}

void FlowerGrowMaterial::forceResh(World* pworld, const WCoord& blockpos)
{
	if (!pworld ||!pworld->onServer())
	{
		return;
	}
	const BlockDef* def = GetBlockDef();
	if (!def||def->CropsSign == 0 || def->GrowthTimeNum == 0)
	{
		return;
	}

	int blockdata = pworld->getBlockData(blockpos);
	if (blockdata < 7)
	{
		float canSpeedUpGrow = false;

		float rangexz = 4;
		CollideAABB box;
		box.pos = blockpos * BLOCK_SIZE;
		box.dim = WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE);
		box.expand((int)(rangexz * BLOCK_SIZE), 2 * BLOCK_SIZE, (int)(rangexz * BLOCK_SIZE));

		std::vector<IClientActor*>actors;;
		pworld->getActorsOfTypeInBox(actors, box, OBJ_TYPE_MONSTER);
		int speedUpMonsterID = 3121;
		WCoord pos = BlockBottomCenter(blockpos);

		for (size_t i = 0; i < actors.size(); i++)
		{
			ClientMob* mob = static_cast<ClientMob*>(actors[i]);
			if (mob->getDef()->ID == speedUpMonsterID)
			{
				WCoord vec = mob->getLocoMotion()->getPosition() - pos;
				float dist = vec.length();
				if (dist < rangexz * BLOCK_SIZE)
				{
					canSpeedUpGrow = true;
					break;
				}
			}
		}
	
			dealNewGrow(pworld, blockpos, 2);
			if (pworld->getBlockData(blockpos) >= 6)
			{
				onMakeFlower(pworld, blockpos);
			}
	}
}

bool FlowerGrowMaterial::canThisPlantGrowOnThisBlockID(int blockid)
{
	return blockid == BLOCK_BURYLAND || blockid == BLOCK_FARMLAND_PIT || blockid == BLOCK_DIRT_FREEZE_PIT;
}

void FlowerGrowMaterial::dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata, BLOCK_MINE_TYPE droptype, float chance, int uin/* = -1 */)
{
	if (GenRandomFloat() > chance)
	{
		return;
	}
	auto dropId = getBlockToolMineDropId((blockdata >= 7) ? 1 : 0);
	doDropItem(pworld, blockpos, dropId);
	//if(blockdata >= 7)
	//{
	//	doDropItem(pworld, blockpos, GetBlockDef()->ToolMineDrops[1].item);
	//}
	//else
	//{
	//	doDropItem(pworld, blockpos, GetBlockDef()->ToolMineDrops[0].item);
	//}
}

bool FlowerGrowMaterial::hasDestroyScore(int blockdata)
{
	return blockdata >= 7;
}

bool FlowerGrowMaterial::canGrowInBlockLight(World* pworld, const WCoord& blockpos)
{
	return true;
}
 

bool FlowerGrowMaterial::onFertilized(World *pworld, const WCoord &blockpos, int fertiliser)
{
	if(pworld->getCurMapID() >= MAPID_MENGYANSTAR)
	{
		if(pworld->getBlockID(TopCoord(blockpos)) != BLOCK_PLANTSPACE_OXYGEN)
			return false;
	}
	//红土 土坑不能生成
	if (pworld->getBlockID(DownCoord(blockpos)) == BLOCK_FARMLAND_PIT)
	{
		return false;
	}
	if (pworld->getBlockID(DownCoord(blockpos)) == BLOCK_DIRT_FREEZE_PIT && canGrowOnFreezePit(pworld->getBlockID(blockpos)) == false)//冻土坑只允许部分植物生长
	{
		return false;
	}

	const BlockDef* def = GetBlockDef();
	if (def && def->CropsSign == 0 || def->GrowthTimeNum == 0)
	{
		FertilizedPlayEffect(pworld, blockpos);
		int blockdata = pworld->getBlockData(blockpos) + GenRandomInt(2, 4);
		if (blockdata > 7)
			blockdata = 7;

		pworld->setBlockData(blockpos, blockdata, 2);
		if (blockdata >= 7 && canGrowInBlockLight(pworld, blockpos))
		{
			onMakeFlower(pworld, blockpos);
		}
	}
	else
	{
		return dealFertilized(pworld, blockpos, fertiliser);
	}

	return true;
}

bool FlowerGrowMaterial::canGrowOnFreezePit(int blockId)
{
	if (blockId != 760 && blockId != 11825 && blockId != 11666 && blockId != 442)//只有冬温花、灌木丛、冰晶蕨、白椰花能生长在冻土里
	{
		return false;
	}
	return true;
}

void FlowerGrowMaterial::setGrowBlockID(World* pworld, const WCoord& blockpos, int blockid, int blockdata)
{
	if (!pworld)
		return;

	pworld->setBlockAll(blockpos, blockid, blockdata, kBlockUpdateFlagNeighbor | kBlockUpdateFlagNeedUpdate);
}

bool FlowerGrowMaterial::onMakeFlower(World *pworld, const WCoord &blockpos)
{
	const BlockDef *defbase = GetDefManagerProxy()->getBlockDef(getBlockResID());
	if(defbase->ModelIndex)
	{
		const BlockDef *def = GetDefManagerProxy()->getBlockDef(defbase->ModelIndex);
		int blockdata = pworld->getBlockData(blockpos);
		if(blockdata <7)
			 return false;
		WCoord bottomPos(blockpos.x, blockpos.y - 1, blockpos.z);
		int id = pworld->getBlockID(bottomPos);
		if (id == BLOCK_DIRT_FREEZE_PIT && canGrowOnFreezePit(getBlockResID()) == false)//冻土坑只允许部分植物生长
		{
			return false;
		}
		if (def->Height > 1)
		{
			if (pworld->getCurMapID() >= MAPID_MENGYANSTAR)
			{
				if (pworld->getBlockID(TopCoord(blockpos)) != BLOCK_PLANTSPACE_OXYGEN)
					return false;
			}
			else if (pworld->getBlockID(TopCoord(blockpos)) != BLOCK_AIR)
			{
				return false;
			}
		}

		for(int i=1; i<def->Height; i++)
		{
			setGrowBlockID(pworld, blockpos + WCoord(0, i, 0), defbase->ModelIndex, 8);
		}
		
		setGrowBlockID(pworld, blockpos, defbase->ModelIndex, 0);
		
		if (id == BLOCK_BURYLAND)//把土坑变为土块
		{
			pworld->setBlockAll(bottomPos, BLOCK_GRASS, 0);
		}
		if (id == BLOCK_DIRT_FREEZE_PIT)//冻土坑变为冻土
		{
			pworld->setBlockAll(bottomPos, BLOCK_DIRT_FREEZE, 0);
		}
		/*if (TaskSubSystem::GetTaskSubSystem())
		{
			TaskSubSystem::GetTaskSubSystem()->CheckCommonSyncTask(TASKSYS_GROW, blockpos * BLOCK_SIZE, m_BlockResID);
		}*/
		WCoord pos = blockpos * BLOCK_SIZE;
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
			MNSandbox::SandboxContext sContext = MNSandbox::SandboxContext(nullptr).
				SetData_Number("type", TASKSYS_GROW).
				SetData_Userdata("WCoord", "trackPos", &pos).
				SetData_Number("target1", m_BlockResID).
				SetData_Number("target2", 0).
				SetData_Number("goalnum", 1);
			MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("TaskSubSystem_CheckCommonSyncTask", sContext);
		}
		return true;
	}
	return false;
}

void FlowerGrowMaterial::createCollideData(CollisionDetect *coldetect, World *pworld, const WCoord &blockpos)
{
	WCoord pos = blockpos*BLOCK_SIZE;
	int step = BLOCK_SIZE/8;

	int h = getStage(pworld->getBlockData(blockpos));
	h = (h+1)*BLOCK_SIZE/getMaxGrowStage();

	coldetect->addObstacle(pos+WCoord(step,0,step), pos+WCoord(BLOCK_SIZE-step, h, BLOCK_SIZE-step));
}

bool FlowerGrowMaterial::onActorCollidedWithBlock(World* pworld, const WCoord& blockpos, IClientActor* actor)
{
	// 冰杖攻击，促进生长
	ClientActorProjectile* projectile = dynamic_cast<ClientActorProjectile*>(actor);
	if (projectile)
	{
		int itemid = projectile->GetItemId();
		if (itemid == ITEM_ICE_BALL || itemid == ITEM_EXTREME_COLD)
		{
			if (GenRandomInt(100) < 50)
			{
				int blockdata = pworld->getBlockData(blockpos);
				if (blockdata < 7)
				{
					pworld->setBlockData(blockpos, blockdata + 1, 2);

					if (blockdata >= 6)
					{
						onMakeFlower(pworld, blockpos);
					}
				}
			}
		}
	}

	return false;
}





