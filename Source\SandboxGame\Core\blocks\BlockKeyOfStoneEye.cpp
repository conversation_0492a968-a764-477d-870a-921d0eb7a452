#include "BlockKeyOfStoneEye.h"
#include "special_blockid.h"
#include "IClientPlayer.h"
#include "section.h"
#include "world.h"
#include "Math/Quaternionf.h"
#include "container_keypedestal.h"
#include "ActorVehicleAssemble.h"
#include "ActorVillager.h"
#include "backpack.h"
#include "ClientPlayer.h"
#include "OgreScriptLuaVM.h"
#include "DefManagerProxy.h"
#include "IPlayerControl.h"
#include "container_keyEffect.h"
#include "EffectManager.h"
#include "EffectParticle.h"

IMPLEMENT_BLOCKMATERIAL(BlockKeyOfStoneEye)

BlockKeyOfStoneEye::BlockKeyOfStoneEye()
{

}

BlockKeyOfStoneEye::~BlockKeyOfStoneEye()
{

}

void BlockKeyOfStoneEye::init(int resid)
{
	ModelBlockMaterial::init(resid);

	SetToggle(BlockToggle_HasContainer, true);
}

void BlockKeyOfStoneEye::onBlockPlacedBy(World* pworld, const WCoord& blockpos, IClientPlayer* player)
{
	ClientPlayer* playerTmp = player->GetPlayer();
	if (!playerTmp) return;
	int placeDir = playerTmp->getCurPlaceDir();
	if (placeDir == DIR_NEG_Y || placeDir == DIR_POS_Y)
	{
		placeDir = DIR_NEG_X;
	}
	pworld->setBlockData(blockpos, placeDir);
}

void BlockKeyOfStoneEye::onNotify(World* pworld, const WCoord& blockpos, int blockid)
{
}

bool BlockKeyOfStoneEye::onTrigger(World* pworld, const WCoord& blockpos, DirectionType face, IClientPlayer* player, const Rainbow::Vector3f& colpoint)
{
	if (0 == player->getCurToolID())
	{
		pworld->setBlockAir(blockpos);
		ClientPlayer* playerTmp = player->GetPlayer();
		if (!playerTmp) return false;
		playerTmp->getBackPack()->setItem(BLOCK_KEY_OF_STONE_EYE, playerTmp->getBackPack()->getShortcutStartIndex() + playerTmp->getCurShortcut(), 1);
	}
	return true;
}

void BlockKeyOfStoneEye::onBlockAdded(World* pworld, const WCoord& blockpos)
{
	ModelBlockMaterial::onBlockAdded(pworld, blockpos);
	/*WCoord down = blockpos;
	down.y -= 1;
	int resid = pworld->getBlockID(down);
	if (BLOCK_Q_EYE_STAR_PEDESTAL == resid)
	{
		pworld->notifyBlock(down, resid);
	}*/
}

void BlockKeyOfStoneEye::onBlockRemoved(World* pworld, const WCoord& blockpos, int blockid, int blockdata)
{
	ModelBlockMaterial::onBlockRemoved(pworld, blockpos, blockid, blockdata);
	/*WCoord down = blockpos;
	down.y -= 1;
	int resid = pworld->getBlockID(down);
	if (BLOCK_Q_EYE_STAR_PEDESTAL == resid)
	{
		pworld->notifyBlock(down, resid);
	}*/

	// 方块移除，停止特效
	WCoord effectPos = BlockBottomCenter(blockpos) + WCoord(0, 30, 0);
	auto pt = pworld->getEffectMgr()->getParticleOnPos(effectPos, "particles/item_key_shijuren.ent");
	if (pt && !pt->needClear())
	{
		pworld->getEffectMgr()->stopParticleEffect("particles/item_key_shijuren.ent", effectPos);
	}
}

void BlockKeyOfStoneEye::dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata, BLOCK_MINE_TYPE droptype/* =BLOCK_MINE_NOTOOL */, float chance/* =1.0f */, int uin)
{
	ModelBlockMaterial::dropBlockAsItem(pworld, blockpos, blockdata, droptype, chance, uin);
}

void BlockKeyOfStoneEye::onPlayRandEffect(World *pworld, const WCoord &blockpos)
{
	string effectPath = "particles/item_key_shijuren.ent";
	WCoord effectPos = BlockBottomCenter(blockpos) + WCoord(0, 30, 0);

	if (pworld->getEffectMgr()->getParticleOnPos(effectPos, effectPath.c_str()) == NULL)
	{
		pworld->getEffectMgr()->playParticleEffectAsync(effectPath.c_str(), effectPos, 0);
	}
}

WorldContainer* BlockKeyOfStoneEye::createContainer(World* pworld, const WCoord& blockpos)
{
	KeyEffectContainer* ret = SANDBOX_NEW(KeyEffectContainer, blockpos);
	if (!ret)
	{
		return NULL;
	}
	return ret;
}

int BlockKeyOfStoneEye::getBlockGeomID(int* idbuf, int* dirbuf, const SectionDataHandler* sectionData, const WCoord& blockpos, World* world)
{
	if (!sectionData) return 1;
	int blockdata = sectionData->getBlock(blockpos).getData();
	int dir = blockdata & 3;
	idbuf[0] = 0;
	dirbuf[0] = dir;
	return 1;
}

int BlockKeyOfStoneEye::getProtoBlockGeomID(int* idbuf, int* dirbuf)
{
	idbuf[0] = 0;
	dirbuf[0] = 0;
	return 1;
}
