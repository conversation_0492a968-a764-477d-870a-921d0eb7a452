
#ifndef __BLOCK_PYRAMID_H__
#define __BLOCK_PYRAMID_H__

#include "BlockMaterial.h"

// struct mapless
// {
// 	bool  operator()(const Rainbow::Vector4f& _Left, const Rainbow::Vector4f& _Right) const {
// 		return _Left == _Right;
// 	}
// };
// bool mapcompare(const Rainbow::Vector4f& _Left, const Rainbow::Vector4f& _Right)
// {
// 	return _Left == _Right;
// }
struct PyramidPhyModel
{
	dynamic_array<Rainbow::Vector3f> verts;
	dynamic_array<UInt16> idxs;
	int triangleCount;
	PyramidPhyModel() :
		triangleCount(0)
	{
	}
};
class PyramidMaterial : public CubeBlockMaterial //tolua_exports
{ //tolua_exports
	//typedef CubeBlockMaterial Super;
	DECLARE_SCENEOBJECTCLASS(PyramidMaterial)
	//DECLARE_BLOCKINSTANCE(PyramidMaterial)
public:
	PyramidMaterial();
	virtual ~PyramidMaterial();
	//tolua_begin
	virtual void init(int resid) override;

	void initWholeFaceVertData(); 
	virtual void initVertData();
	virtual void initTriangleFaceVertData();
	virtual void initPhyModelData();
	//virtual bool isOpaqueCube()
	//{
	//	return false;
	//}
	virtual bool hasSolidTopSurface(int blockdata);
	virtual bool canAttachedToMecha(int curblockdata, DirectionType dir, int neighbor_id, int neighbor_data) override;
	virtual void dropBlockAsItem(World* pworld, const WCoord& blockpos, int blockdata = 0, BLOCK_MINE_TYPE droptype = BLOCK_MINE_NOTOOL, float chance = 1.0f, int uin = -1) override;
	virtual int getPhisicMeshVerts(BaseSection* psection, const WCoord& blockpos, dynamic_array<Rainbow::Vector3f>& verts, dynamic_array<UInt16>& idxs);

	BLOCK_RENDERTYPE_T GetAttrRenderType() const;/* 渲染类型 */
	virtual void onBlockPlacedBy(World* pworld, const WCoord& blockpos, IClientPlayer* player);
	virtual void createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh) override;
	virtual SectionMesh* createBlockProtoMesh(int protodata = 0);
	virtual void createCollideData(CollisionDetect* coldetect, World* pworld, const WCoord& blockpos);
	virtual bool coverNeighbor(int curblockdata, SolidBlockMaterial* neighbor, int neighbor_data, DirectionType dir) override;
	void drawBox(WCoord min, WCoord max);
	unsigned char TriangleNormal2LightColor(const Rainbow::Vector3f& normal,bool useDirColor=false ,short Dir=0);
	virtual int convertDataByRotate(int blockdata, int rotatetype);
	virtual BlockMaterial::BlockType BlockTypeId() { return BlockMaterial::BlockType::BlockType_SlantBlock; }
	virtual bool canSprayPaint(World* pworld, const WCoord& blockpos);
public:
	static dynamic_array<UInt16>* m_dPosIndices;
	static dynamic_array<UInt16>* m_PosTrIndices;
	static Rainbow::Vector3f* ms_LightVec;
private:
	virtual float getBlockHeight(int blockdata);

protected:

	static dynamic_array<dynamic_array<BlockGeomVert>>& m_mWholeFace();

	dynamic_array<dynamic_array<BlockGeomVert>> m_mTriangleFace;

	std::map<unsigned short, PyramidPhyModel> m_mPhyModel;
public:
	class BlockInstance : public Super::BlockInstance
	{
		DECLARE_SCENEOBJECTCLASS(BlockInstance)
	public:
		static MNSandbox::ReflexClassParam<BlockInstance, int> R_Dir;
	};
	virtual MNSandbox::AutoRef<BlockMaterial::BlockInstance> GetBlockInstance() override
	{
		return PyramidMaterial::BlockInstance::NewInstance();
	}
}; //tolua_exports

#endif