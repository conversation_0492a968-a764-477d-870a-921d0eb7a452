#include "BlockCoagulation.h"
#include "container_coagulation.h"
#include "DefManagerProxy.h"
#include "block_tickmgr.h"
#include "LuaInterfaceProxy.h"
#include "BlockMaterialMgr.h"
#include "Ecosystem.h"
#include "BlockTNT.h"
#include "ActorAttrib.h"
#include "ObserverEvent.h"
#include "ObserverEventManager.h"
#include "FireBurnComponent.h"
#include "AttackedComponent.h"
#include "ThermalSpringManager.h"
#include "special_blockid.h"
#include "EffectManager.h"
#include "world.h"
#include "IClientActor.h"

IMPLEMENT_BLOCKMATERIAL(BlockCoagulation)

static bool IsTouchRain(World* pworld, const WCoord& blockpos)
{
	if (!pworld->isRaining(blockpos)) return false;
	if (pworld->getBlockRaining(blockpos)) return true;
	for (int i = 0; i < 4; i++)
	{
		if (pworld->getBlockRaining(NeighborCoord(blockpos, i))) return true;
	}
	return false;
}

BlockCoagulation::BlockCoagulation()
{
	//for (int i = 0; i < 6; i++)
	//{
	//	m_Mtls[i] = NULL;
	//}
	m_Stable = NULL;
	m_StoreEnergy_1 = NULL;
	m_StoreEnergy_2 = NULL;
	m_BlowUp = NULL;
}

BlockCoagulation::~BlockCoagulation()
{
	//for (int i = 0; i < 6; i++)
	//{
		//ENG_RELEASE(m_Mtls[i]);
	//}
	//ENG_RELEASE(m_Mtl);
	OGRE_RELEASE(m_Stable);
	OGRE_RELEASE(m_StoreEnergy_1);
	OGRE_RELEASE(m_StoreEnergy_2);
	OGRE_RELEASE(m_BlowUp);
}

void BlockCoagulation::init(int resid)
{
	CubeBlockMaterial::init(resid);

	SetToggle(BlockToggle_HasContainer, true);
	SetToggle(BlockToggle_RandomTick, true);
	if (BlockMaterial::m_LoadOnlyLogic) return;
	// --------------黑凝浆块贴图
	m_Stable = g_BlockMtlMgr.createRenderMaterial("all_condensation", m_Def, getTextureType(), getDrawType());
	m_StoreEnergy_1 = g_BlockMtlMgr.createRenderMaterial("all_condensation_1", m_Def, getTextureType(), getDrawType());
	m_StoreEnergy_2 = g_BlockMtlMgr.createRenderMaterial("all_condensation_2", m_Def, getTextureType(), getDrawType());
	m_BlowUp = g_BlockMtlMgr.createRenderMaterial("all_condensation_3", m_Def, getTextureType(), getDrawType());
	// --------------黑凝浆块贴图
	if (resid == BLOCK_BLACK_COAGULATION)
	{
		for (int i = 0; i < 6; i++)
		{
			setFaceMtl((DirectionType)i, m_Stable);
		}
	}
	else
		for (int i = 0; i < 6; i++)
		{
			setFaceMtl((DirectionType)i, getDefaultMtl());
		}

	//#ifdef IWORLD_EXPOBJ_TOOLS
	m_BaseTex = RenderBlockMaterial::loadBlockTexture(GetBlockDef()->Texture1.c_str(), GetBlockDef()->gamemod, 1);
}

void BlockCoagulation::initGeomName()
{
	m_geomName= "coagulation";
}

void BlockCoagulation::initDefaultMtl()
{
	auto mtl = g_BlockMtlMgr.createRenderMaterial(GetBlockDef()->Texture1.c_str(), GetBlockDef(), getTextureType(), getDrawType());
	m_defaultMtlIndex = getRenderMtlMgr().addMtl(mtl);
	OGRE_RELEASE(mtl);
}


bool BlockCoagulation::onTrigger(World* pworld, const WCoord& blockpos, DirectionType face, IClientPlayer* player, const Rainbow::Vector3f& colpoint)
{
	return false;
}


RenderBlockMaterial* BlockCoagulation::getFaceMtl(const BiomeDef* biome, DirectionType dir, int blockdata, BlockColor& facecolor)
{
	RenderBlockMaterial* pmtl;
	pmtl = getDefaultMtl();
	if (m_BlockResID == BLOCK_BLACK_COAGULATION) {
		if (blockdata == 1) { //黑凝浆块稳定状态
			pmtl = m_Stable;
		}
		else if (blockdata == 2) { //黑凝浆块蓄能1状态
			pmtl = m_StoreEnergy_1;
		}
		else if (blockdata == 3) { //黑凝浆块蓄能2状态
			pmtl = m_StoreEnergy_2;
		}
		else if (blockdata == 4) { //黑凝浆块喷发状态
			pmtl = m_BlowUp;
		}
		return pmtl;
	}
	else {
		return pmtl;
	}
}

void BlockCoagulation::onBlockPlacedBy(World* pworld, const WCoord& blockpos, IClientPlayer* player)
{
	//if (player && pworld)
	//{
	//	int placedir = player->getCurPlaceDir();
	//	int blockdata = pworld->getBlockData(blockpos) & 3;

	//	pworld->setBlockData(blockpos, blockdata | placedir, 3);
	//}
	if (pworld->getBlockID(blockpos) == BLOCK_COAGULATION && isTouchWater(pworld, blockpos))//改为接触到水就变黑凝浆块
	{
		pworld->setBlockAll(blockpos, BLOCK_BLACK_COAGULATION, pworld->getBlockData(blockpos) & 3, 3);

	}
	if (pworld->getBlockID(blockpos) == BLOCK_BLACK_COAGULATION && !pworld->isRemoteMode())//判断热泉结构
	{
		GetThermalSpringMgr().tryConsistThermalSpring(pworld, blockpos);
	}
}

void BlockCoagulation::onBlockAdded(World* pworld, const WCoord& blockpos)
{
	/*if (pworld->getBlockID(blockpos) == BLOCK_COAGULATION)
	{
		pworld->getBlockTickMgr()->scheduleBlockUpdate(blockpos, m_BlockResID, GenRandomInt(4));
	}*/
	if (pworld->getBlockID(blockpos) == BLOCK_BLACK_COAGULATION)//地形编辑器复制方块时初始化黑凝浆块
	{
		CoagualationContainer* container_blow = dynamic_cast<CoagualationContainer*>(pworld->getContainerMgr()->getContainer(blockpos));
		if (container_blow)
		{
			container_blow->m_CoreBlockPos = WCoord(0, 0, 0);
			container_blow->m_iCoreBlock = false;
			container_blow->m_iBlowTick = 0;
		}
		pworld->setBlockData(blockpos, 0);
	}
}

void BlockCoagulation::onBlockRemoved(World *pworld, const WCoord &blockpos, int blockid, int blockdata)
{
	const BlockDef *def = GetDefManagerProxy()->getBlockDef(getBlockResID());
	WCoord centerpos = BlockCenterCoord(blockpos);

	const char *digsound = def->DigSound.c_str();
	if (digsound[0] == 0) 
		digsound = "blockd.magma";
	pworld->getEffectMgr()->playSound(centerpos, digsound, GSOUND_DESTROY);
	if (m_BlockResID == BLOCK_BLACK_COAGULATION)
	{
		CoagualationContainer* container_blow = dynamic_cast<CoagualationContainer*>(pworld->getContainerMgr()->getContainer(blockpos));
		if (container_blow && container_blow->m_CoreBlockPos != WCoord(0, 0, 0) && GetThermalSpringMgr().isHotSpring(pworld, container_blow->m_CoreBlockPos) == false)
		{
			GetThermalSpringMgr().removeThermalSpringData(container_blow->m_CoreBlockPos);
		}

	} 
}

WorldContainer* BlockCoagulation::createContainer(World* pworld, const WCoord& blockpos)
{
	return nullptr; //延迟生成
	/*CoagualationContainer* container = SANDBOX_NEW(CoagualationContainer, blockpos);
	return container;*/
}

void BlockCoagulation::dropBlockAsItemWithToolId(World *pworld, const WCoord &blockpos, int blockdata, BLOCK_MINE_TYPE droptype, float chance, int useToolId, int uin)
{
	if (getBlockResID() == BLOCK_COAGULATION)
	{
		//凝浆块 铲子回收  正常掉落
		if (IsShovelID(useToolId))
			doDropItem(pworld, blockpos, getBlockResID());
	}
	else
	{
		dropBlockAsItem(pworld, blockpos, blockdata, droptype, chance, uin);
	}
}


int BlockCoagulation::getBlockGeomID(int* idbuf, int* dirbuf, const SectionDataHandler* sectionData, const WCoord& blockpos, World* world)
{
	Block pblock = sectionData->getBlock(blockpos);

	idbuf[0] = 0;
	dirbuf[0] = pblock.getData() % 4;

	return 1;
}

void BlockCoagulation::onNotify(World* pworld, const WCoord& blockpos, int blockid)
{
	if (pworld->getBlockID(blockpos) == BLOCK_COAGULATION && isTouchWater(pworld, blockpos))
	{
		pworld->setBlockAll(blockpos, BLOCK_BLACK_COAGULATION, pworld->getBlockData(blockpos) & 3, 3);
	}
	if (pworld->getBlockID(blockpos) == BLOCK_BLACK_COAGULATION && !pworld->isRemoteMode()) //判断结构是否是热泉，并生成
	{
		CoagualationContainer* container_blow = dynamic_cast<CoagualationContainer*>(pworld->getContainerMgr()->getContainer(blockpos));
		if (container_blow)
		{
			if (container_blow->m_CoreBlockPos == WCoord(0, 0, 0) && container_blow->m_iCoreBlock == false && !GetThermalSpringMgr().checkIsInOtherSpringRange(blockpos))
			{
				GetThermalSpringMgr().tryConsistThermalSpring(pworld, blockpos);
			}
		}
		else
		{
			if (!GetThermalSpringMgr().checkIsInOtherSpringRange(blockpos))
			{
				GetThermalSpringMgr().tryConsistThermalSpring(pworld, blockpos);
			}
		}
	}
}

void BlockCoagulation::blockTick(World* pworld, const WCoord& blockpos)
{
	if (pworld->getBlockID(blockpos) != BLOCK_COAGULATION)
	{
		return;
	}
	int difficultySetting = 0;

	CoagualationContainer* container = CoagualationContainer::sureContainer(pworld, blockpos);
	if (container)
	{
		isTouchLava(pworld, blockpos);
		if (!container->isContactLava)
		{
			container->isContactLava = true;
			pworld->setBlockAll(blockpos, BLOCK_BLACK_COAGULATION, pworld->getBlockData(blockpos) & 3, 3);
			if (pworld->getBlockID(blockpos) == BLOCK_BLACK_COAGULATION && !GetThermalSpringMgr().checkIsInOtherSpringRange(blockpos) && !pworld->isRemoteMode())
			{
				GetThermalSpringMgr().tryConsistThermalSpring(pworld, blockpos);//判断热泉结构
			}
			return;
		}
	}

	if (isTouchWater(pworld, blockpos))
	{
		//pworld->setBlockAir(blockpos);
		pworld->setBlockAll(blockpos, BLOCK_BLACK_COAGULATION, pworld->getBlockData(blockpos) & 3, 3);
	}
	else
	{
		if (getBlockResID() == BLOCK_COAGULATION)
		{
			int blockdata = pworld->getBlockData(blockpos);

			if (blockdata < 15)
			{
				pworld->setBlockData(blockpos, blockdata + GenRandomInt(3) / 2, 4);
			}

			int tick = getTickInterval() + GenRandomInt(10);
			if (container && !container->isContactLava)
			{
				container->m_iTotalTick += tick;
			}
			//pworld->getBlockTickMgr()->scheduleBlockUpdate(blockpos, getBlockResID(), tick);

			if (!canNeighborBurn(pworld->getWorldProxy(), blockpos))
			{
			}
			else if (!canBlockCatchFire(pworld->getWorldProxy(), DownCoord(blockpos)) && blockdata == 15 && GenRandomInt(4) == 0)
			{
			}
			else
			{
				Ecosystem *biome = pworld->getBiomeGen(blockpos.x, blockpos.z);
				bool highhumid = biome->isHighHumidity();
				int catchodds = 300;
				if (highhumid)
				{
					catchodds -= 50;
				}

				for (int i = 0; i < 6; i++)
				{
					tryToCatchBlockOnFire(pworld, NeighborCoord(blockpos, i), catchodds, blockdata);
				}

				for (int x = blockpos.x - 1; x <= blockpos.x + 1; ++x)
				{
					for (int z = blockpos.z - 1; z <= blockpos.z + 1; ++z)
					{
						for (int y = blockpos.y - 1; y <= blockpos.y + 4; ++y)
						{
							WCoord npos(x, y, z);
							if (npos == blockpos) continue;

							int r1 = 100;

							if (y > blockpos.y + 1)
							{
								r1 += (y - (blockpos.y + 1)) * 100;
							}

							int r2 = getChanceOfNeighborsEncouragingFire(pworld, npos);
							if (r2 > 0)
							{
								int r3 = (r2 + 40 + difficultySetting * 7) / (blockdata + 30);

								if (highhumid) r3 /= 2;

								if (r3 > 0 && GenRandomInt(r1) <= r3 && !IsTouchRain(pworld, npos))
								{
									int newdata = blockdata + GenRandomInt(5) / 4;
									if (newdata > 15) newdata = 15;

									TriggerBlockAddRemoveDisable Tmp2(pworld); // 剔除火势蔓延

									pworld->setBlockAll(npos, BLOCK_FIRE, newdata, 3);
								}
							}
						}
					}
				}
			}
		}
		
	}
}

bool BlockCoagulation::onActorCollidedWithBlock(World* pworld, const WCoord& blockpos, IClientActor* actor)
{
	//if (actor && !actor->isDead() && !actor->needClear())
	//{
	//	LOG_INFO("touch coagulation!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!");
	//	ActorAttrib* attrib = actor->getAttrib();
	//	if (attrib)
	//	{
	//		//if (attrib->immuneToFire() <= 0) actor->setFire(100, 1);
	//		if (attrib->immuneToFire() <= 1) actor->attackedFromType(ATTACK_FIRE, 2.0f * GetLuaInterfaceProxy().get_lua_const()->yanjiang_shanghai_beilv); 
	//	}
	//}
	return true;
}

void BlockCoagulation::onActorWalking(World *pworld, const WCoord &blockpos, IClientActor *actor)
{
	//黑凝浆块不会受到伤害 直接返回了
	if (getBlockResID() != BLOCK_COAGULATION)
		return;

	//爆爆蛋和炎炎蟹不会被伤害
	if (actor && !actor->isDead() && !actor->needClear() && actor->getDefID() != 3109 && actor->getDefID() != 3881)
	{
		auto attrib = actor->getAttribComponent();
		if (attrib)
		{
			bool result = attrib->Event2().Emit<>("BlockCoagulation_ActorWalk");
			Assert(result);
		}
	}
}

void BlockCoagulation::isTouchLava(World* pworld, const WCoord& blockpos)
{
	for (int i = 0;i < 6;i++)
	{
		int blockID = pworld->getBlockID(NeighborCoord(blockpos, i));
		if (BlockMaterialMgr::isLava(blockID)/*blockID == BLOCK_FLOW_LAVA || blockID == BLOCK_STILL_LAVA*/)
		{
			CoagualationContainer* curContainer = dynamic_cast<CoagualationContainer*>(pworld->getContainerMgr()->getContainer(blockpos));
			if (curContainer)
			{
				if (!curContainer->isContactLava)
				{
					curContainer->isContactLava = true;
					curContainer->m_iTotalTick = 0;
				}
			}
			break;
		}
	}
}

bool BlockCoagulation::canNeighborBurn(WorldProxy* pworld, const WCoord& blockpos)
{
	for (int i = 0; i < 6; i++)
	{
		if (canBlockCatchFire(pworld, NeighborCoord(blockpos, i))) return true;
	}
	return false;
}

bool BlockCoagulation::isWaterBlock(World* pworld, const WCoord& blockpos)
{
	auto blockid = pworld->getBlockID(blockpos + WCoord(0, 1, 0));
	return BlockMaterialMgr::isWater(blockid);
	//return pworld->getBlockID(blockpos + WCoord(0, 1, 0)) == BLOCK_STILL_WATER || pworld->getBlockID(blockpos + WCoord(0, 1, 0)) == BLOCK_FLOW_WATER;
}

bool BlockCoagulation::isOpaqueCube()
{
	if (m_BlockResID == BLOCK_BLACK_COAGULATION)
	{
		return false;//用于黑凝浆块被地形编辑器复制时，container也被复制
	}
	return true;
}

bool BlockCoagulation::canBlockCatchFire(WorldProxy* pworld, const WCoord& blockpos)
{
	return GetDefManagerProxy()->getBlockDef(pworld->getBlockID(blockpos))->BurnSpeed > 0;
}

bool BlockCoagulation::isTouchWater(World* pworld, const WCoord& blockpos)
{
	for (int i = 0; i < 6; i++)
	{
		int blockID = pworld->getBlockID(NeighborCoord(blockpos, i));
		if (BlockMaterialMgr::isWater(blockID))//(blockID == BLOCK_STILL_WATER || blockID == BLOCK_FLOW_WATER)
		{
			return true;
		}
	}
	return false;
}

int BlockCoagulation::getTextureType()
{
	return GETTEX_WITHDEFAULT;
}


void BlockCoagulation::tryToCatchBlockOnFire(World *pworld, const WCoord &blockpos, int max_catchfire, int firestrength)
{
	int blockid = pworld->getBlockID(blockpos);
	const BlockDef *blockdef = GetDefManagerProxy()->getBlockDef(blockid);

	if (GenRandomInt(max_catchfire) < blockdef->CatchFire)
	{
		BlockBangalore *oldmtl = dynamic_cast<BlockBangalore *>(g_BlockMtlMgr.getMaterial(blockid));
		int olddata = 0;
		if (oldmtl) olddata = pworld->getBlockData(blockpos);

		if (GenRandomInt(firestrength + 10) < 5 && !pworld->getBlockRaining(blockpos))
		{
			int blockdata = firestrength + GenRandomInt(5) / 4;
			if (blockdata > 15) blockdata = 15;

			pworld->setBlockAll(blockpos, BLOCK_FIRE, blockdata, 3);
		}
		else
		{
			pworld->setBlockAir(blockpos);
		}

		if (oldmtl)
		{
			oldmtl->DoOnBlockDestroyedBy(pworld, blockpos, blockid, olddata, BLOCK_DESTROY_FIRE, NULL);
			// 观察者事件接口
			ObserverEvent_Block obevent(blockpos.x, blockpos.y, blockpos.z, oldmtl->getBlockResID());
			GetObserverEventManager().OnTriggerEvent("Block.DestroyBy", &obevent);
		}
	}
}

int BlockCoagulation::getChanceToEncourageFire(World *pworld, const WCoord &blockpos, int speed)
{
	int maxspeed = GetDefManagerProxy()->getBlockDef(pworld->getBlockID(blockpos))->BurnSpeed * 5;
	return maxspeed > speed ? maxspeed : speed;
}

int BlockCoagulation::getChanceOfNeighborsEncouragingFire(World *pworld, const WCoord &blockpos)
{
	int burnspeed = 0;

	if (pworld->getBlockID(blockpos) != 0)
	{
		return 0;
	}
	else
	{
		for (int i = 0; i < 6; i++)
		{
			burnspeed = getChanceToEncourageFire(pworld, NeighborCoord(blockpos, i), burnspeed);
		}
		return burnspeed;
	}
}