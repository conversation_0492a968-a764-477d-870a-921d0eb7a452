
#include "blocks/BlockMaterialBase.h"
#include "SectionMesh.h"
#include "Collision.h"
#include "blocks/BlockMaterialMgr.h"
#include "section.h"
#include "BlockGeom.h"
#include "DefManagerProxy.h"
#include "world.h"
#include "WorldRender.h"
#include "Ecosystem.h"
#include "IClientActor.h"
#include "IClientItem.h"
#include "IActorLocoMotion.h"
#include "ClientActorDef.h"
#include "blocks/special_blockid.h"
#include "BlockBasic.h"
#include "Common/OgreShared.h"
#include "ActorManagerInterface.h"
#include "OgreScriptLuaVM.h"
#include "Graphics/Texture.h"
#include "RenderSection.h"
#include "SandboxCoreDriver.h"
#include "IClientPlayer.h"
#include "SandboxBlockService.h"
#include "BlockOperation.h"
#include "blocks/container_world.h"
#include "SandboxLuaFunction.h"
#include "base/reflex/SandboxReflexTypeEnumEx.h"
#include "LuaInterfaceProxy.h"
#include "EffectManager.h"
#include "Common/GameStatic.h"
#include "container.h"
#include "ShareRenderMaterial.h"
#include "IActorAttrib.h"
#include "PlayManagerInterface.h"
#include "MiniCraftRenderer.h"
#include "defdata.h"
using namespace MINIW;
using namespace MNSandbox;


const int LocalDefBlockIdMax = 2001;
unsigned char BlockMaterial::m_LightOpacity[SOC_BLOCKID_MAX] = { 0 };
unsigned char BlockMaterial::m_LightValue[SOC_BLOCKID_MAX] = { 0 };
unsigned char BlockMaterial::m_IsOpaqueCube[SOC_BLOCKID_MAX] = { 0 };
unsigned char BlockMaterial::m_RenderTypes[SOC_BLOCKID_MAX] = { 0 };
unsigned char BlockMaterial::m_NoData = 0;
char BlockMaterial::m_TemperatureValue[SOC_BLOCKID_MAX] = { 0 };
unsigned char BlockMaterial::m_TemperatureOpacity[SOC_BLOCKID_MAX] = { 0 };

int BlockMaterial::m_DigLuckEnchant = 0;
int BlockMaterial::m_DigLuckBuff = 0;
bool BlockMaterial::m_LoadOnlyLogic = false;
bool BlockMaterial::m_DisableCoverFaceOpt = false;


IMPLEMENT_SCENEOBJECTCLASS(BlockMaterial)

/**********反射Begin**********/

ReflexEnumDesc<BLOCKCOLLIDE> BlockMaterial::R_BLOCKCOLLIDE("BlockCollide", (int)REFLEXTYPEENUM_ENUM_BLOCKCOLLIDE, {
	{BLOCKCOLLIDE::AIR, "Air"},
	{BLOCKCOLLIDE::SOLID, "Solid"},
	{BLOCKCOLLIDE::LIQUID, "Liquid"},
	{BLOCKCOLLIDE::NO_PROJECTILE, "NoProjectile"},
	{BLOCKCOLLIDE::NO_ACTOR, "NoActor"},
});

ReflexClassParam<BlockMaterial, bool> BlockMaterial::R_ModifyByEditorTool(6, "block_modify_by_editor", "category", &BlockMaterial::isModifyByEditor, &BlockMaterial::setModifyByEditor, ReflexConfig::REG_NO_CALL);
ReflexClassMember<BlockMaterial, int> BlockMaterial::R_TemplateBlockId(4, "Template_ID", "category", &BlockMaterial::m_attrTemplateBlockId, ReflexConfig::REG_NO_CALL);
ReflexClassParam<BlockMaterial, int> BlockMaterial::R_BlockId(5, "Block_Res_ID", "category", &BlockMaterial::BlockResIdGet, &BlockMaterial::BlockResIdSet, ReflexConfig::REG_ONLY_SHOW);
ReflexClassParam<BlockMaterial, int> BlockMaterial::R_CsvDefId(3, "Def_ID", "category", &BlockMaterial::CsvDefIdGet, &BlockMaterial::CsvDefIdSet, ReflexConfig::REG_NO_CALL);
ReflexClassMember<BlockMaterial, std::string> BlockMaterial::R_BlockName(0, "Block_Name", "category", &BlockMaterial::m_attrName);
ReflexClassMember<BlockMaterial, std::string> BlockMaterial::R_BlockTypeName(1, "Type_Name", "category", &BlockMaterial::m_TypeName, ReflexConfig::REG_ONLY_SHOW);
ReflexClassMember<BlockMaterial, std::string> BlockMaterial::R_BlockDesc(2, "Block_Desc", "category", &BlockMaterial::m_attrDesc);
ReflexClassParam<BlockMaterial, int> BlockMaterial::R_ClickCollide(7, "Click_Collide", "attribute2", &BlockMaterial::ClickCollideGet, &BlockMaterial::ClickCollideSet);
ReflexClassParam<BlockMaterial, BLOCKCOLLIDE> BlockMaterial::R_MoveCollide(8, "Move_Collide", "attribute", &BlockMaterial::MoveCollideGet, &BlockMaterial::MoveCollideSet);
//ReflexClassParam<BlockMaterial, int> BlockMaterial::R_PhyCollide(9, "Phy_Collide", "attribute", &BlockMaterial::PhyCollideGet, ReflexConfig::NO_PUBLIC);
ReflexClassParam<BlockMaterial, int> BlockMaterial::R_BlockFlow(10, "Block_Flow", "attribute2", &BlockMaterial::BlockFlowGet, &BlockMaterial::BlockFlowSet);
ReflexClassParam<BlockMaterial, int> BlockMaterial::R_PushFlag(11, "Push_Flag", "attribute2", &BlockMaterial::PushFlagGet, &BlockMaterial::PushFlagSet);
ReflexClassParam<BlockMaterial, int> BlockMaterial::R_AntiExplode(12, "Anti_Explode", "attribute", &BlockMaterial::AntiExplodeGet, &BlockMaterial::AntiExplodeSet);
ReflexClassParam<BlockMaterial, float> BlockMaterial::R_Hardness(13, "Hardness", "attribute", &BlockMaterial::HardnessGet, &BlockMaterial::HardnessSet);
ReflexClassParam<BlockMaterial, float> BlockMaterial::R_Slipperiness(14, "Slipperiness", "attribute", &BlockMaterial::SlipperinessGet, &BlockMaterial::SlipperinessSet);
ReflexClassParam<BlockMaterial, int> BlockMaterial::R_BurnSpeed(15, "Burn_Speed", "attribute", &BlockMaterial::BurnSpeedGet, &BlockMaterial::BurnSpeedSet);
ReflexClassParam<BlockMaterial, int> BlockMaterial::R_CatchFire(16, "Catch_Fire", "attribute", &BlockMaterial::CatchFireGet, &BlockMaterial::CatchFireSet);
ReflexClassParam<BlockMaterial, int> BlockMaterial::R_ToolMineDrop1(17, "Tool_Mine_Drop1", "attribute2", &BlockMaterial::ToolMineDrop1Get, &BlockMaterial::ToolMineDrop1Set);
ReflexClassParam<BlockMaterial, int> BlockMaterial::R_ToolMineProb1(18, "Tool_Mine_Prob1", "attribute2", &BlockMaterial::ToolMineProb1Get, &BlockMaterial::ToolMineProb1Set);
ReflexClassParam<BlockMaterial, int> BlockMaterial::R_HandMineDrop(19, "Hand_Mine_Drop", "attribute2", &BlockMaterial::HandMineDropGet, &BlockMaterial::HandMineDropSet);
ReflexClassParam<BlockMaterial, int> BlockMaterial::R_HandMineProb(20, "Hand_Mine_Prob", "attribute2", &BlockMaterial::HandMineProbGet, &BlockMaterial::HandMineProbSet);
ReflexClassParam<BlockMaterial, int> BlockMaterial::R_MineExp(21, "Drop_Min_Exp", "attribute2", &BlockMaterial::DropExpGet, &BlockMaterial::DropExpSet);
ReflexClassParam<BlockMaterial, int> BlockMaterial::R_MineExpOdds(22, "Drop_Min_Exp_Prob", "attribute2", &BlockMaterial::DropExpOddsGet, &BlockMaterial::DropExpOddsSet);
ReflexClassParam<BlockMaterial, int> BlockMaterial::R_LightSrc(23, "Light_Src", "attribute", &BlockMaterial::LightSrcGet, &BlockMaterial::LightSrcSet);
ReflexClassMember<BlockMaterial, std::string> BlockMaterial::R_GeomName(24, "Geom_Name", "other", &BlockMaterial::m_attrGeomName);
ReflexClassParam<BlockMaterial, bool> BlockMaterial::R_IsReplaceable(25, "IsReplaceable", "other", &BlockMaterial::IsReplaceableGet, &BlockMaterial::IsReplaceableSet);
ReflexClassParam<BlockMaterial, std::string> BlockMaterial::R_DefaultRenderMtl(26, "Default_Material", "other", &BlockMaterial::DefaultRenderMtlGet, &BlockMaterial::DefaultRenderMtlSet);
ReflexClassParam<BlockMaterial, bool> BlockMaterial::R_AddToMgr(27, "Add_To_Mgr", "other", &BlockMaterial::AddToMgrGet, &BlockMaterial::AddToMgrSet, ReflexConfig::REG_NO_CALL);
ReflexClassMember<BlockMaterial, AutoRef<LuaFunction>> BlockMaterial::R_callbackPlacedBy(28, "CallbackPlacedBy", "callback", &BlockMaterial::m_methodPlacedBy, ReflexConfig::REG_ONLY_SCRIPT);
ReflexClassMember<BlockMaterial, AutoRef<LuaFunction>> BlockMaterial::R_callbackPlayRandEffect(29, "CallbackPlayRandEffect", "callback", &BlockMaterial::m_methodPlayRandEffect, ReflexConfig::REG_ONLY_SCRIPT);
ReflexClassMember<BlockMaterial, AutoRef<LuaFunction>> BlockMaterial::R_callbackNotify(30, "CallbackNotify", "callback", &BlockMaterial::m_methodNotify, ReflexConfig::REG_ONLY_SCRIPT);
ReflexClassMember<BlockMaterial, AutoRef<LuaFunction>> BlockMaterial::R_callbackBlockAdded(31, "CallbackBlockAdded", "callback", &BlockMaterial::m_methodBlockAdded, ReflexConfig::REG_ONLY_SCRIPT);
ReflexClassMember<BlockMaterial, AutoRef<LuaFunction>> BlockMaterial::R_callbackBlockRemoved(32, "CallbackBlockRemoved", "callback", &BlockMaterial::m_methodBlockRemoved, ReflexConfig::REG_ONLY_SCRIPT);
ReflexClassMember<BlockMaterial, AutoRef<LuaFunction>> BlockMaterial::R_callbackDestroyedBy(33, "CallbackDestroyedBy", "callback", &BlockMaterial::m_methodDestroyedBy, ReflexConfig::REG_ONLY_SCRIPT);
ReflexClassMember<BlockMaterial, AutoRef<LuaFunction>> BlockMaterial::R_callbackTrigger(34, "CallbackTrigger", "callback", &BlockMaterial::m_methodTrigger, ReflexConfig::REG_ONLY_SCRIPT);
ReflexClassMember<BlockMaterial, AutoRef<LuaFunction>> BlockMaterial::R_callbackEvent(35, "CallbackEvent", "callback", &BlockMaterial::m_methodEvent, ReflexConfig::REG_ONLY_SCRIPT);
ReflexClassMember<BlockMaterial, AutoRef<LuaFunction>> BlockMaterial::R_callbackActorCollidedWithBlock(36, "CallbackActorCollidedWithBlock", "callback", &BlockMaterial::m_methodActorCollidedWithBlock, ReflexConfig::REG_ONLY_SCRIPT);
ReflexClassMember<BlockMaterial, AutoRef<LuaFunction>> BlockMaterial::R_callbackActorWalking(37, "CallbackActorWalking", "callback", &BlockMaterial::m_methodActorWalking, ReflexConfig::REG_ONLY_SCRIPT);
ReflexClassMember<BlockMaterial, AutoRef<LuaFunction>> BlockMaterial::R_callbackFertilized(38, "CallbackFertilized", "callback", &BlockMaterial::m_methodFertilized, ReflexConfig::REG_ONLY_SCRIPT);
ReflexClassMember<BlockMaterial, AutoRef<LuaFunction>> BlockMaterial::R_callbackRefreshChunk(39, "CallbackRefreshChunk", "callback", &BlockMaterial::m_methodRefreshChunk, ReflexConfig::REG_ONLY_SCRIPT);
ReflexClassParam<BlockMaterial, bool> BlockMaterial::R_Breakable(40, "Breakable", "attribute", &BlockMaterial::BreakableGet, &BlockMaterial::BreakableSet);
ReflexClassMember<BlockMaterial, AutoRef<LuaFunction>> BlockMaterial::R_callbackClickByActor(41, "CallbackClickByActor", "callback", &BlockMaterial::m_methodClickByActor, ReflexConfig::REG_ONLY_SCRIPT);
//ReflexClassParam<BlockMaterial, int> BlockMaterial::R_BlockSettingAtt(42, "SettingAttr", "attribute2", &BlockMaterial::m_iBlockSettingAtt);


ReflexClassNotify<BlockMaterial, SandboxNode_Ref, WCoord, AutoRef<SandboxNode>, Rainbow::Vector3f, bool, int > BlockMaterial::R_Notify_PlacedBy("NotifyPlacedBy", "notify", &BlockMaterial::m_notifyPlacedBy);
ReflexClassNotify<BlockMaterial, SandboxNode_Ref, WCoord> BlockMaterial::R_Notify_PlayRandEffect("NotifyPlayRandEffect", "notify", &BlockMaterial::m_notifyPlayRandEffect);
ReflexClassNotify<BlockMaterial, SandboxNode_Ref, WCoord, int> BlockMaterial::R_Notify_Notify("NotifyNotify", "notify", &BlockMaterial::m_notifyNotify);
ReflexClassNotify<BlockMaterial, SandboxNode_Ref, WCoord> BlockMaterial::R_Notify_BlockAdded("NotifyBlockAdded", "notify", &BlockMaterial::m_notifyBlockAdded);
ReflexClassNotify<BlockMaterial, SandboxNode_Ref, WCoord, int> BlockMaterial::R_Notify_BlockRemoved("NotifyBlockRemoved", "notify", &BlockMaterial::m_notifyBlockRemoved);
ReflexClassNotify<BlockMaterial, SandboxNode_Ref, WCoord, int, int, AutoRef<SandboxNode> > BlockMaterial::R_Notify_DestroyedBy("NotifyDestroyedBy", "notify", &BlockMaterial::m_notifyDestroyedBy);
ReflexClassNotify<BlockMaterial, SandboxNode_Ref, WCoord, int, AutoRef<SandboxNode>, Rainbow::Vector3f > BlockMaterial::R_Notify_Trigger("NotifyTrigger", "notify", &BlockMaterial::m_notifyTrigger);
ReflexClassNotify<BlockMaterial, SandboxNode_Ref, WCoord, int, int > BlockMaterial::R_Notify_Event("NotifyEvent", "notify", &BlockMaterial::m_notifyEvent);
ReflexClassNotify<BlockMaterial, SandboxNode_Ref, WCoord, AutoRef<SandboxNode> > BlockMaterial::R_Notify_ActorCollidedWithBlock("NotifyActorCollidedWithBlock", "notify", &BlockMaterial::m_notifyActorCollidedWithBlock);
ReflexClassNotify<BlockMaterial, SandboxNode_Ref, WCoord, AutoRef<SandboxNode> > BlockMaterial::R_Notify_ActorWalking("NotifyActorWalking", "notify", &BlockMaterial::m_notifyActorWalking);
ReflexClassNotify<BlockMaterial, SandboxNode_Ref, WCoord, int > R_Notify_Fertilized("NotifyFertilized", "notify", &BlockMaterial::m_notifyFertilized);
ReflexClassNotify<BlockMaterial, SandboxNode_Ref, WCoord, SandboxNode_Ref> R_Notify_DigBegin("NotifyDigBegin", "notify", &BlockMaterial::m_notifyDigBegin);
ReflexClassNotify<BlockMaterial, SandboxNode_Ref, WCoord, SandboxNode_Ref> R_Notify_DigFinish("NotifyDigFinish", "notify", &BlockMaterial::m_notifyDigFinish);
ReflexClassNotify<BlockMaterial, SandboxNode_Ref, WCoord, SandboxNode_Ref> R_Notify_DigCancel("NotifyDigCancel", "notify", &BlockMaterial::m_notifyDigCancel);
ReflexClassNotify<BlockMaterial, SandboxNode_Ref, WCoord, SandboxNode_Ref> R_Notify_ClickByActor("NotifyClickByActor", "notify", &BlockMaterial::m_notifyClickByActor);


/**********反射End**********/

void __copyBlockDef(BlockDef* src, BlockDef* dest)
{

}

long parseLong(const char* str, int base, bool* success) {
	char* endptr = 0;
	errno = 0;
	long val = strtol(str, &endptr, base);

	if (success) {
		*success = endptr != str && errno == 0 && endptr && *endptr == '\0';
	}
	return val;
}

bool isCustomModel(const BlockDef *m_Def)
{
	if (m_Def && !m_Def->Texture2.empty())  //20220407 给外部的微缩方块插件用的，玩家自定义的微缩id存储在m_Def->Texture，存储只是复用
	{
		int textint = 0;
		std::stringstream sstr;
		sstr << m_Def->Texture2.c_str();
		sstr >> textint;
		if (textint != 0)
			return true;
	}

	return false;
}

BlockMaterial::BlockMaterial()
{
	SANDBOXPROFILE_PUSHDATA(this, BLOCKMATERIAL, GetRTTI()->GetType());
	////新引擎blockmaterial修改的内存使用方法，这里关掉自动引用计数
	////todo：后面再判断下是否使用新引擎管理方法
	//m_flagAutoRelease = 0; // 按计数释放的，必须不能是0，否则有内存泄露
	memset(m_ProtoMesh, 0, sizeof(m_ProtoMesh));

	assert(BlockToggle_MAX <= m_baseToggles.GetMaxSize()); // 如果 BlockToggle_MAX 过大，需要扩充 m_baseToggles
	//RegDefEvents();
}

BlockMaterial::~BlockMaterial()
{
	SANDBOXPROFILE_POPDATA(this);

	m_mtlMgr.clearMtls();

	Release();

	for (int i = 0; i < 16; ++i)
	{
		ENG_RELEASE(m_ProtoMesh[i]);
	}
	m_defaultMtlIndex = Rainbow::MAX_UINT;

	SANDBOX_DELETE(m_newDef);
	//移除放在blockmaterialmgr里 g_BlockMtlMgr.SetBlockNewDef(m_BlockResID, nullptr);
	m_Def = NULL;
	
	if (m_pBlockScriptComponent)
	{
		GetISandboxActorSubsystem()->BSComponentOnUnbindGameObject(m_pBlockScriptComponent, this);
		//m_pBlockScriptComponent->OnUnbindGameObject(this);
		m_pBlockScriptComponent = nullptr;
	}
}

void BlockMaterial::initBlockDef(int resid, int defId)
{
	m_Def = GetDefManagerProxy()->getBlockDef(defId);
	initNewBlockDef();

	assert(m_Def);
}

void BlockMaterial::setBlockResID(int resid)
{
	m_BlockResID = resid;
}


bool BlockMaterial::Init()
{
	if (m_bInited) return true;
	m_bInited = true;

	bool ret = Super::Init();
	if (ret)
	{
		//新引擎 要保证ScriptVM的调用在主线程中
		SetToggle(BlockToggle_IsColorableBlock, true);

		BlockColorInfoNew info;
		info.csvDefId = m_csvDefId;
		info.blockResId = getBlockResID();
		info.defaultBlockId = m_DefaultBlockId;
		info.colorNum = 0; // 保护，避免脏数据
		MINIW::ScriptVM::game()->callFunction("GetColorFromBlockInfoNew", "u", &info);
		if (info.colorNum != 16)
		{
			SetToggle(BlockToggle_IsColorableBlock, false);
		}
		// 保护，避免数据越界
		int colorTotalMin = std::min(info.colorNum, 16);
		for (int i = 0; i < colorTotalMin; ++i)
		{
			BlockColor tmp = info.color[i];
			m_Colors[i] = tmp.SwizzleToBGRA();
		}
		m_DefaultBlockId = info.defaultBlockId;

		InitScriptComponent();

		/*
		//初始化十六种颜色的Material
		for (int i = 0; i < 16; ++i)
		{
			int ret = -1;
			MINIW::ScriptVM::game()->callFunction("GetColorFromBlockInfo", "ii>i", getBlockResID(), i, &ret);
			if ((ret == -1) && (m_csvDefId > 0))
			{//不是内建方块id，要使用vsvdefid
				MINIW::ScriptVM::game()->callFunction("GetColorFromBlockInfo", "ii>i", m_csvDefId, i, &ret);
			}
			if (ret == -1)
			{
				SetToggle(BlockToggle_IsColorableBlock, false);
				break;
			}
			BlockColor tmp = (unsigned int)ret;
			m_Colors[i] = tmp.SwizzleToBGRA();// (unsigned int)ret;
		}
		//默认颜色的方块ID
		MINIW::ScriptVM::game()->callFunction("GetDefaultBlockId", "i>i", getBlockResID(), &m_DefaultBlockId);
		if ((m_DefaultBlockId < 0) && (m_csvDefId > 0))
		{//不是内建方块id，要使用vsvdefid
			MINIW::ScriptVM::game()->callFunction("GetDefaultBlockId", "i>i", m_csvDefId, &m_DefaultBlockId);
		}*/
	}
	return ret;
}

void BlockMaterial::init(int resid)
{
	memset(m_nSpecialLogicType, 0, sizeof(int) * 2);
	setBlockResID(resid);

	bool needInitDef = !m_Def;
	if (m_csvDefId < 0)
	{
		m_csvDefId = resid;
		needInitDef = true;
	}
	assert(m_csvDefId >= 0);
	if (needInitDef)
	{
		initBlockDef(resid, m_csvDefId);
	}
	

	/* 默认配置 */
	SetToggle(BlockToggle_RandomTick, false);
	SetToggle(BlockToggle_HasContainer, false);
	SetToggle(BlockToggle_IsOpaqueCube, false);
	SetToggle(BlockToggle_IsSolid, true);
	SetToggle(BlockToggle_IsLiquid, false);
	SetToggle(BlockToggle_IsAir, false);
	SetToggle(BlockToggle_CanProvidePower, false);
	SetToggle(BlockToggle_CanTickImmediate, true);
	SetToggle(BlockToggle_CanOutputQuantityEnergy, false);
	SetToggle(BlockToggle_DropItemImmuneFire, false);
	SetToggle(BlockToggle_IsColorableBlock, false);

	SetToggle(BlockToggle_Replaceable, GetBlockDef()->Replaceable > 0);
	SetToggle(BlockToggle_BurnSpeed, GetBlockDef()->BurnSpeed > 0);
	SetToggle(BlockToggle_BlockMove, (GetBlockDef()->MoveCollide & 1) != 0);
	SetToggle(BlockToggle_BlockRay, GetBlockDef()->MoveCollide == 1);
	SetToggle(BlockToggle_IsUseCustomModel, isCustomModel(GetBlockDef()));

	SetAttrRenderType(BLOCKRENDER_MODEL);
	initDrawType();

	m_tickInterval = 10;

	initGeom();

	//SetToggle(BlockToggle_IsColorableBlock, true);
	////初始化十六种颜色的Material
	//for (int i = 0; i < 16; ++i)
	//{
	//	int ret = -1;
	//	MINIW::ScriptVM::game()->callFunction("GetColorFromBlockInfo", "ii>i", getBlockResID(), i, &ret);
	//	if (ret == -1)
	//	{//不是内建方块id，要使用vsvdefid
	//		MINIW::ScriptVM::game()->callFunction("GetColorFromBlockInfo", "ii>i", m_csvDefId, i, &ret);
	//	}
	//	if(ret == -1)
	//	{
	//		SetToggle(BlockToggle_IsColorableBlock, false);
	//		break;
	//	}
	//	m_Colors[i] = (unsigned int)ret;
	//}
	////默认颜色的方块ID
	//MINIW::ScriptVM::game()->callFunction("GetDefaultBlockId", "i>i", getBlockResID(), &m_DefaultBlockId);
	//if (m_DefaultBlockId < 0)
	//{//不是内建方块id，要使用vsvdefid
	//	MINIW::ScriptVM::game()->callFunction("GetDefaultBlockId", "i>i", m_csvDefId, &m_DefaultBlockId);
	//}

	if (!m_LoadOnlyLogic)
	{
		initDefaultMtl();
	}
	
	initAttributes();
}

//void BlockMaterial::InitBeforeReflex()
//{
//	getRenderMtlMgr().clearMtls();
//
//	init(m_BlockResID);
//	Event().Emit("OnSetDefaultAttr");
//	//现在有脚本节点了，脚本相关的后期应该会使用脚本节点的方式
//	//if (!strScript.empty())
//	//{
//	//	ptr->LuaPluginMgr().BindLuaPluginByFileT<T>(strScript);
//	//}
//	Init();
//}

//void BlockMaterial::OnHandleInit()
//{
//}

bool BlockMaterial::SetParent(SandboxNode* parent)
{
	if (!parent)
	{
		return Super::SetParent(parent);
	}
	else if (parent == MNSandbox::GetCurrentBlockService().get())
	{
		if (Super::SetParent(parent))
		{
			setModifyByEditor(true);
			return true;
		}
		return false;
	}
	assert(false && "block just can mount to BlockMtrlRoot");
	return false;
}

void BlockMaterial::initDefaultGeom()
{
	if(!m_GeomLOD.HasGeom())
	{
		LOG_INFO("Block:%d has no model(%s)", getBlockResID(), m_geomName.c_str());
		if (!m_defaultGeomName.empty())
		{
			SetGeom(g_BlockMtlMgr.getGeomTemplate(m_defaultGeomName.c_str()));
		}
	}
}

void BlockMaterial::initGeom()
{
	initGeomName();

	if (!m_geomName.empty())
	{
		auto geomPtr = g_BlockMtlMgr.getGeomTemplate(m_geomName.c_str());
		if (geomPtr)
		{
			SetGeom(geomPtr);
			initLODGeom();
		}
		else
		{
			initDefaultGeom();
		}
		return;
	}
	initDefaultGeom();
}
void BlockMaterial::initGeomName()
{
}

void BlockMaterial::initLODGeom()
{
	if (m_GeomLOD.GetGeom(0) == nullptr)
		return;
	int lodCount = Rainbow::GetMiniCraftRenderer().GetLODCount() + 1;//+1 for last level
	for (int i = 1; i < lodCount; ++i)
	{
		m_GeomLOD.SetGeom(i, nullptr);
	}

	int lod = getLod();
	if (lod != 0)
	{
		m_GeomLOD.LoadLOD(m_geomName.c_str());
		for (int i = 1; i < lodCount; ++i)
		{
			if (m_GeomLOD.GetGeom(i) == nullptr)
			{
				m_GeomLOD.SetGeom(i, m_GeomLOD.GetGeom(i - 1));
			}
		}
		for (int i = lod; i < lodCount; ++i)
		{
			m_GeomLOD.SetGeom(i, nullptr);
		}
	}

	//if (isSupportGeomLOD()) 
	//{
	//	m_GeomLOD.LoadLOD(m_geomName.c_str());
	//}
}


void BlockMaterial::initDrawType()
{
	m_blockDrawType = BLOCKDRAW_OPAQUE;
}
void BlockMaterial::changeGeom()
{
	std::string name = getGeomName();
	const char *geomname = name.c_str();
	if (geomname[0])
	{
		BlockGeomTemplate *pGeom = g_BlockMtlMgr.getGeomTemplate(geomname);
		//m_Geom = g_BlockMtlMgr.getGeomTemplate(geomname);
		if (pGeom == NULL)
		{
			LOG_INFO("Block:has no model(%s)", geomname);
		}
		else
		{
			SetGeom(pGeom);
			initLODGeom();
		}
			
	}
	//else SetGeom(nullptr);
}


BlockGeomTemplate* BlockMaterial::getGeom(size_t level) 
{
	if (getLod() > 0) 
	{
		return m_GeomLOD.GetGeom(level);
	}
	else 
	{
		return m_GeomLOD.GetGeom(0);
	}
	
}


void BlockMaterial::update(unsigned int dtick)
{
	UpdateScriptComponent(dtick);
}

void BlockMaterial::UpdateScriptComponent(unsigned int dt)
{
	if (m_pBlockScriptComponent)
		GetISandboxActorSubsystem()->BSComponentUpdate(m_pBlockScriptComponent, dt);
		//m_pBlockScriptComponent->Update(dt);
}

void BlockMaterial::AddToScriptTickBlocks(int mapid, int x, int y, int z, int blockdata)
{
	if (m_pBlockScriptComponent)
		GetISandboxActorSubsystem()->BSComponentAddToTickBlocks(m_pBlockScriptComponent, mapid, x, y, z, blockdata);
		//m_pBlockScriptComponent->AddToTickBlocks(mapid, x, y, z, blockdata);
}

void BlockMaterial::ClearScriptTickBlocks(int mapid)
{
	if(m_pBlockScriptComponent)
		GetISandboxActorSubsystem()->BSComponentClearTickBlocks(m_pBlockScriptComponent, mapid);
		//m_pBlockScriptComponent->ClearTickBlocks(mapid);
}

bool BlockMaterial::IsScriptBlockNeedTick()
{
	if (!m_pBlockScriptComponent)
		return false;

	return GetISandboxActorSubsystem()->BSComponentNeedTick(m_pBlockScriptComponent);
	//return m_pBlockScriptComponent->NeedTick();
}

void BlockMaterial::TickScriptBlocks(unsigned int dt, World* pworld)
{
	if (!m_pBlockScriptComponent)
		return;
	GetISandboxActorSubsystem()->BSComponentTickBlocks(m_pBlockScriptComponent, pworld);
	//m_pBlockScriptComponent->TickBlocks(pworld);
}

void BlockMaterial::OnChunkEnterWorld(const std::vector<Rainbow::Vector3f>& blocks)
{
	if (!m_pBlockScriptComponent)
		return;
	GetISandboxActorSubsystem()->BSComponentOnChunkEnterWorld(m_pBlockScriptComponent, blocks);
	//m_pBlockScriptComponent->OnChunkEnterWorld(blocks);
}

void BlockMaterial::OnChunkLeaveWorld(const std::vector<Rainbow::Vector3f>& blocks)
{
	if (!m_pBlockScriptComponent)
		return;
	GetISandboxActorSubsystem()->BSComponentOnChunkLeaveWorld(m_pBlockScriptComponent, blocks);
	//m_pBlockScriptComponent->OnChunkLeaveWorld(blocks);
}


SectionMesh *BlockMaterial::getBlockProtoMesh(int protodata)
{
#ifndef IWORLD_SERVER_BUILD
	assert(protodata >=0 && protodata<16);
	if(protodata < 0 || protodata >= 16 )
		return NULL;
	if(m_ProtoMesh[protodata] == NULL)
	{
		m_ProtoMesh[protodata] = createBlockProtoMesh(protodata);
	}
	
	return m_ProtoMesh[protodata];
#else
	return NULL;
#endif
}

void BlockMaterial::clearBlockProtoMesh(int protodata /* = 0 */)
{
	assert(protodata >= 0 && protodata < 16);
	if (protodata < 0 || protodata >= 16)
		return;
	if (m_ProtoMesh[protodata])
	{
		m_ProtoMesh[protodata]->Release();
		m_ProtoMesh[protodata] = NULL;
	}

}

void BlockMaterial::setBlockSettingAttState(int att, bool b)
{
	if (m_iBlockSettingAtt < 0) { m_iBlockSettingAtt = 0xff; }

	if (b)
		m_iBlockSettingAtt |= att;
	else
		m_iBlockSettingAtt &= ~att;
}

int BlockMaterial::getBlockSettingAttState(int state)
{
	if (m_iBlockSettingAtt < 0)
		return 1;

	return ((m_iBlockSettingAtt & state) > 0 ? 2 : 0);
}

void BlockMaterial::createCollideData(CollisionDetect *coldetect, World *pworld, const WCoord &blockpos)
{
	coldetect->addObstacle(blockpos*BLOCK_SIZE, (blockpos+WCoord(1,1,1))*BLOCK_SIZE);
}

void BlockMaterial::createPickData(CollisionDetect *coldetect, World *pworld, const WCoord &blockpos)
{
	createCollideData(coldetect, pworld, blockpos);
}

bool BlockMaterial::getCollisionBoundingBox(CollideAABB &box, World *pworld, const WCoord &blockpos)
{
	box.pos = blockpos * BLOCK_SIZE;
	box.dim = WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE);

	return true;
}

void BlockMaterial::createBlockMeshWithContainer(const BuildSectionMeshData& data, const WCoord &blockpos, SectionMesh *poutmesh, WorldContainer *pContainer){
	createBlockMesh(data,blockpos,poutmesh);
}

void BlockMaterial::createWaterBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh, void* pdata, bool isCube)
{
}

void BlockMaterial::createPassWaterBlockMesh(const BuildSectionMeshData& data, const WCoord& waterblockpos, const WCoord& blockpos, SectionMesh* poutmesh, void* pdata)
{
}


void BlockMaterial::createBlockMesh(const BuildSectionMeshData& data, const WCoord &blockpos, SectionMesh *poutmesh)
{
}

void BlockMaterial::createBlockMeshEx(const BuildSectionMeshData& data, const WCoord &blockpos, SectionMesh *poutmesh)
{
}

void BlockMaterial::createBlockMeshPreview(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh) {
	createBlockMesh(data, blockpos, poutmesh);
}

SectionMesh *BlockMaterial::createBlockProtoMesh(int protodata)
{
	return NULL;
}

BlockTexElement *BlockMaterial::getDestroyTexture(Block pblock, BlockTexDesc &desc)
{
	return NULL;
}

Rainbow::SharePtr<Rainbow::Texture2D> BlockMaterial::getDestroyTextureWithPos(Block pblock, BlockTexDesc& desc, World* pworld, WCoord blockpos)
{
	auto ptex = getDestroyTexture(pblock, desc);
	if (ptex)
	{
		memcpy(desc.uvtile, ptex->m_UVTile, sizeof(desc.uvtile));
		return ptex->getTexture();
	}
	return NULL;
};

bool BlockMaterial::isNormalCube(int blockid)
{

	BlockMaterial *mtl = g_BlockMtlMgr.getMaterial(blockid);
	if(mtl == NULL) return false;
	return mtl->isOpaqueCube() && !mtl->canProvidePower();
}

bool BlockMaterial::isBuddyBlockID(int id1, int id2)
{
	if(id1 == id2) return true;
	BlockMaterial *mtl = g_BlockMtlMgr.getMaterial(id1);
	if(mtl){
		return mtl->isBuddyBlockID(id2);
	}
	else
	{
		return false;
	}
}

bool BlockMaterial::isSameType(int id1, int id2)
{
	if(id1 == id2) return true;

	BlockMaterial* blockMtl1 = g_BlockMtlMgr.getMaterial(id1);
	BlockMaterial* blockMtl2 = g_BlockMtlMgr.getMaterial(id2);
	if (blockMtl1 && blockMtl2)
	{
		return blockMtl1->m_TypeName == blockMtl2->m_TypeName;
	}
	return true;
}

bool BlockMaterial::getBlockIcing(WorldProxy *pworld, const WCoord &blockpos, bool onlyedge)
{
	Ecosystem *biome = pworld->getBiomeGen(blockpos.x, blockpos.z);
	if (!biome) return false;
	float temperature = biome->getHeat();

	if (temperature > 0.15f)
	{
		return false;
	}
	else
	{
		if (blockpos.y >= 0 && blockpos.y < 256 && pworld->getBlockSunIllum(blockpos) < 10)
		{
			int blockid = pworld->getBlockID(blockpos);

			if (BlockMaterialMgr::isWater(blockid)/*(blockid == BLOCK_STILL_WATER || blockid == BLOCK_FLOW_WATER)*/ && pworld->getBlockData(blockpos) == 0)
			{
				if (!onlyedge)
				{
					return true;
				}

				for (int dir = 0; dir < 4; dir++)
				{
					int nid = pworld->getBlockID(NeighborCoord(blockpos, dir));
					if (!BlockMaterialMgr::isWater(nid)) /*(nid != BLOCK_STILL_WATER && nid != BLOCK_FLOW_WATER)*/
					{
						return true;
					}
				}
			}
		}

		return false;
	}
}

bool BlockMaterial::getBlockSnowing(WorldProxy* pworld, const WCoord &pos)
{
	Ecosystem *biome = pworld->getBiomeGen(pos.x, pos.z);
	float temperature = biome->getHeat();

	if (temperature > 0.15f)
	{
		return false;
	}
	else
	{
		if (pos.y > 0 && pos.y < 256/* && getBlockSunIllum(pos) < 10*/)
		{
			int downid = pworld->getBlockID(DownCoord(pos));
			int blockid = pworld->getBlockID(pos);

			if (blockid == 0 && downid != 0/* && downid!=BLOCK_ICE*/)
			{
				if (g_BlockMtlMgr.getMaterial(BLOCK_SNOWPANE)->canPutOntoPos(pworld, pos)/* && g_BlockMtlMgr.getMaterial(downid)->defBlockMove()*/)
				{
					return true;
				}
			}
		}

		return false;
	}
}


bool BlockMaterial::canPlacedAgain(World *pworld, int placeblockid, const Rainbow::Vector3f &colpoint, const WCoord &blockpos, bool placeinto, int face)
{
	return false;
}

void BlockMaterial::onBlockPlacedBy(World *pworld, const WCoord &blockpos, IClientPlayer *player, const Rainbow::Vector3f &colpoint, bool placeinto, int face)
{

}

bool BlockMaterial::renderTexRotByDir()
{
	return false;
}

bool BlockMaterial::hasSolidTopSurface(int blockdata)
{
	if(isOpaqueCube()) return true;
	else return false;
}

bool BlockMaterial::isBuddyBlockID(int blockid)
{
	return getBlockResID()==blockid;
}

bool BlockMaterial::canBlocksMovement(World *pworld, const WCoord &blockpos)
{
	return defBlockMove();
}

void BlockMaterial::blockTick(World *pworld, const WCoord &blockpos)
{

}

void BlockMaterial::forceResh(World* pworld, const WCoord& blockpos)
{

}

void BlockMaterial::onPlayRandEffect(World *pworld, const WCoord &blockpos)
{

}

void BlockMaterial::onPlayEffect(World* pworld, const WCoord& blockpos)
{

}

void BlockMaterial::onStopEffect(World* pworld, const WCoord& blockpos)
{

}

bool BlockMaterial::CombineVertex()
{
	return true;
}

//int BlockMaterial::getTickInterval()
//{
//	return 10;
//}

//bool BlockMaterial::canTickImmediate()
//{
//	return true;
//}

void BlockMaterial::onBlockAdded(World *pworld, const WCoord &blockpos)
{
	
}

void BlockMaterial::onBlockRemoved(World *pworld, const WCoord &blockpos, int blockid, int blockdata)
{

}

int BlockMaterial::getPlaceBlockData(World *pworld, const WCoord &blockpos, DirectionType face, float hitptx, float hity, float hitpty, int def_blockdata)
{
	return def_blockdata;
}

int BlockMaterial::getPlaceBlockDataWithPlayer(World* pworld, IClientPlayer* player, const WCoord& blockpos, DirectionType face, float hitptx, float hitpty, float hitptz, int def_blockdata)
{
	int blockdata = getPlaceBlockData(pworld, blockpos, face, hitptx, hitpty, hitptz, def_blockdata);
	if (blockdata <= 0)
		blockdata = getPlaceBlockDataByPlayer(pworld, player);
	return blockdata;
}

int BlockMaterial::getPlaceBlockDataByPlayer(World *pworld, IClientPlayer *player)
{
	return 0;
}

void BlockMaterial::onBlockPlacedBy(World *pworld, const WCoord &blockpos, IClientPlayer *player)
{
}

void BlockMaterial::onBlockDestroyedBy(World *pworld, const WCoord &blockpos, int blockdata, BLOCK_DESTROY_REASON_T destroytype, IClientActor *bywho)
{
}

bool BlockMaterial::onTrigger(World *pworld, const WCoord &blockpos, DirectionType face, IClientPlayer *player, const Rainbow::Vector3f &colpoint)
{
	return false;
}

bool BlockMaterial::onBlockDamaged(World* pworld, const WCoord& blockpos, IClientPlayer* player, int attack_type,float damage)
{
#ifndef IWORLD_SERVER_BUILD
	pworld->addHarmedBlock(blockpos);
#endif
	//默认是0 血量 ， 最大值是255 ，累计到100就销毁
	if (pworld->isRemoteMode())
	{
		return false;
	}
	int hp = pworld->getBlockDataEx(blockpos);
	assert(damage >= 0);
	hp += (damage + 0.5); // 四舍五入取整
	if (hp > m_Def->MaxHP)
	{
		hp = m_Def->MaxHP;
	}
	if (hp > 255) hp = 255;
	pworld->setBlockDataEx(blockpos, hp);
	return true;
}

bool BlockMaterial::onBlockRepaired(World* pworld, const WCoord& blockpos, IClientPlayer* player, float amount)
{
	return false;
}

bool BlockMaterial::onBlockUpGrade(World* pworld, const WCoord& blockpos, int upgradeNum, IClientPlayer* player)
{
	return false;
}

bool BlockMaterial::onEvent(World *pworld, const WCoord &blockpos, int eventid, int eventparam)
{
	return false;
}

bool BlockMaterial::onActorCollidedWithBlock(World *pworld, const WCoord &blockpos, IClientActor *actor)
{
	return false;
}

void BlockMaterial::onActorWalking(World *pworld, const WCoord &blockpos, IClientActor *actor)
{

}

void BlockMaterial::onActorMoving(World* pworld, const WCoord& blockpos, IClientActor* actor)
{

}

void BlockMaterial::onNotify(World *pworld, const WCoord &blockpos, int blockid)
{

}

bool BlockMaterial::onFertilized(World *pworld, const WCoord &blockpos, int fertiliser)
{
	return false;
}

bool BlockMaterial::canPutOntoFace(WorldProxy *pworld, const WCoord &blockpos, int face)
{
	return canPutOntoPos(pworld, blockpos);
}

bool BlockMaterial::canStayOnPos(WorldProxy *pworld, const WCoord &blockpos)
{
	return true;
}

bool BlockMaterial::canPutOntoPos(WorldProxy *pworld, const WCoord &blockpos)
{
	int blockid = pworld->getBlockID(blockpos);
	return blockid==0 || ( g_BlockMtlMgr.getMaterial(blockid) && g_BlockMtlMgr.getMaterial(blockid)->isReplaceable());
}

bool BlockMaterial::canPutOntoPos(World *pworld, const WCoord &blockpos)
{
	MainWorldProxy worldproxy(pworld);
	return canPutOntoPos(&worldproxy, blockpos);
}
bool BlockMaterial::canPutOntoFace(World *pworld, const WCoord &blockpos, int face)
{
	MainWorldProxy worldproxy(pworld);
	return canPutOntoFace(&worldproxy, blockpos, face);
}
bool BlockMaterial::canStayOnPos(World *pworld, const WCoord &blockpos)
{
	MainWorldProxy worldproxy(pworld);
	return canStayOnPos(&worldproxy, blockpos);
}

bool BlockMaterial::canPutOntoPlayer(World *pworld, const WCoord &blockpos, IClientPlayer *player)
{
	//player可能为NULL
	return true;
}

int BlockMaterial::outputStrongEnergy(World *pworld, const WCoord &blockpos, DirectionType dir)
{
	return 0;
}

int BlockMaterial::outputWeakEnergy(World *pworld, const WCoord &blockpos, DirectionType dir)
{
	return 0;
}

int BlockMaterial::outputQuantityEnergy(World *pworld, const WCoord &blockpos, DirectionType dir)
{
	return 0;
}

static int CalLuckyEnchantProb(int level)
{
	int r = GenRandomInt(100);
	if(level == 1)
	{
		if(r < 75) return 0;
		else return 1;
	}
	else if(level == 2)
	{
		if(r < 50) return 0;
		else if(r < 75) return 1;
		else return 2;
	}
	else if(level == 3)
	{
		if(r < 40) return 0;
		else if(r < 60) return 1;
		else if(r < 80) return 2;
		else return 3;
	}

	return 0;
}

const int TOTAL_R = 10000;
inline int GetItemNumByOdds(int odds)
{
	int n = odds / TOTAL_R;
	if(GenRandomInt(TOTAL_R) < (odds%TOTAL_R)) n++;
	return n;
}

//增加接口 新增需求:掉落规则与使用工具等级不同 掉率不同//code by:tanzhenyu  默认复用老代码 特殊方块重写即可
void BlockMaterial::dropBlockAsItemWithToolId(World *pworld, const WCoord &blockpos, int blockdata, BLOCK_MINE_TYPE droptype, float chance, int useToolId, int uin)
{
	dropBlockAsItem(pworld,blockpos,blockdata,droptype,chance,uin);
}

void BlockMaterial::dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata, BLOCK_MINE_TYPE droptype, float chance, int uin)
{
	if(droptype == BLOCK_MINE_NONE) return;
	if(GenRandomFloat() > chance) return;
	if (pworld->IsUGCEditMode()) return;
	
	int blockid = m_BlockResID;
	const BlockDef *def = m_Def;
	if(def == NULL) return;
	if (pworld->CheckBlockSettingEnable(this, ENABLE_DROPITEM) == 0) { return; }
	std::multimap<int, int> itemMap;
	int r = std::rand()%10000;
	if(def->MineTool == 0 && droptype==BLOCK_MINE_NOTOOL) droptype = BLOCK_MINE_TOOLFIT;

	if(droptype == BLOCK_MINE_NOTOOL)
	{
		int itemnum = GetItemNumByOdds(def->HandMineDrops.odds);
		if (itemnum > 0)
		{
			int itemid = def->HandMineDrops.item;
			itemMap.insert(std::make_pair(itemid, itemnum));
		}
	}
	else if(droptype == BLOCK_MINE_PRECISE)
	{
		int itemid = def->PreciseDrop;
		itemMap.insert(std::make_pair(itemid, 1));
	}
	else
	{
		for(int i=0; i<MAX_TOOLMINE_DROP; i++)
		{
			int itemid = def->ToolMineDrops[i].item;
			if(itemid > 0)
			{
				int itemnum = GetItemNumByOdds(def->ToolMineDrops[i].odds);
				//if(itemnum > 0) break;
				itemMap.insert(std::make_pair(itemid, itemnum));//产品要求有几个掉落物就掉几个
			}
		}
	}

	float tooleff = 0;
	if (uin > 0)
	{
		int restype = def->ResType;
		do {
			if (restype == 0) break;
			IClientPlayer* player = pworld->getActorMgr()->iFindPlayerByUin(uin);
			if (!player) break;

			BackPackGrid* equipgrid = player->getEquipGrid(EQUIP_WEAPON);
			if (!equipgrid) break;

			int toolid = equipgrid->getItemID();
			if (toolid > 0 && equipgrid->getDuration() > 0)
			{
				auto toolDef = GetDefManagerProxy()->getToolDef(toolid);
				if (toolDef)
				{
					if (restype == 1)
					{
						tooleff = toolDef->WoodEfficiency;
					} else if (restype == 2)
					{
						tooleff = toolDef->MineEfficiency;
					}
				}
			}
		} while(false);
		if (tooleff > 0)
		{
			tooleff *= GetLuaInterfaceProxy().get_lua_const()->tool_efficiency_ratio;
		}
	}

	int protodata = 0;
	for (auto iter = itemMap.begin(); iter != itemMap.end(); ++iter)
	{
		int itemid = iter->first;
		int itemnum = iter->second;
		if (iter->first <= 4096)
		{
			protodata = (isColorableBlock() && g_BlockMtlMgr.getMaterial(itemid) && g_BlockMtlMgr.getMaterial(itemid)->isColorableBlock()) ? blockdata : 0;
		}

		if (itemid > 0 && itemnum > 0) {
			if (BlockMaterial::m_DigLuckBuff > 1) {
				itemnum *= BlockMaterial::m_DigLuckBuff;
			}
			if (tooleff > 0)
			{
				itemnum = (1.0f + tooleff)* itemnum + 0.5f;
			}
			doDropItem(pworld, blockpos, itemid, itemnum, protodata);
		}
	}

	// if(BlockMaterial::m_DigLuckEnchant>0 && droptype==BLOCK_MINE_TOOLFIT)
	// {
	// 	int itemid = def->ToolMineDrops[0].item;
	// 	if(itemid > 0)
	// 	{
	// 		float addprob[3];
	// 		memset(addprob, 0, sizeof(addprob));
	// 		int n = GetISandboxActorSubsystem()->PlayerCalDropItemCallCount(BlockMaterial::m_DigLuckEnchant, addprob) - 1;
	// 		for(int i=0; i<n; i++)
	// 		{
	// 			if(GenRandomInt(10000) < def->ToolMineDrops[0].odds)
	// 			{
	// 				doDropItem(pworld, blockpos, itemid, 1, protodata);
	// 			}
	// 		}
	// 	}
	// }
}

float BlockMaterial::getDestroyHardness(int blockdata, IClientPlayer *player)
{
	return m_Def->Hardness;
}

void BlockMaterial::doDropItem(World *pworld, const WCoord &blockpos, int itemid, int itemnum, int protodata)
{
	if(itemid>0 && itemnum>0 && pworld->CheckBlockSettingEnable(this, ENABLE_DROPITEM) > 0)
	{
		IClientItem*item = pworld->getActorMgr()->SpawnIClientItem(BlockCenterCoord(blockpos), itemid, itemnum, protodata);
		if (item)
		{
			item->SetItemSpawnType(DESTROYBLOCK);
		}
		if (dropItemImmuneFire())
		{
			auto attr = (dynamic_cast<IClientActor*>(item))->GetIActorAttrib();
			if (attr)
				attr->setImmuneToFire(1);
		}
		if (item && (itemid == BLOCK_FAR_DRIFTBOTTLE || itemid == BLOCK_DRIFTBOTTLE))
		{
			WorldStringContainer* container = dynamic_cast<WorldStringContainer*>(pworld->getContainerMgr()->getContainer(blockpos));
			if (container)
			{
				item->setUserData(container->m_Str);
			}
		}
		if (item && (itemid == BLOCK_COLOR_PALETTE_STAND || itemid == BLOCK_COLOR_PALETTE_HANG))
		{
			WorldContainer* container = pworld->getContainerMgr()->getContainer(blockpos);
			if (container)
			{
				std::string savedata = "";
				bool result = container->Event2().Emit<std::string&>("ColorPaletteContainer_getSaveData", savedata);
				if (result)
				{
					item->setUserData(savedata);
				}
				else
					Assert(false);
			}
		}	
	}
}


char* BlockMaterial::getPhisicMeshBit(BaseSection *psection, const WCoord &blockpos)
{
	if (isUseCustomModel())
	{
		int idbuf[32];
		int dirbuf[32];
		int ngeom = 1;
		BlockGeomMeshInfo meshinfo;
		Block pblock = psection->getBlock(blockpos);
		idbuf[0] = 0;
		dirbuf[0] = pblock.getData() % 4;

		if (!getGeom(0))
			return NULL;
		char key[16];
		int dir = dirbuf[0] & 0xffff;
		int mirrortype = (dirbuf[0] >> 16) & 3;
		sprintf(key, "%d_%d_%d", idbuf[0], dir, mirrortype);
		char* cache = getGeom(0)->getPhisicMeshBitBaseCache(key);
		if (cache)
			return cache;
		if (mirrortype > 0) getGeom(0)->getFaceVerts(meshinfo, idbuf[0], 1.0f, 0, dir, mirrortype);
		else getGeom(0)->getFaceVerts(meshinfo, idbuf[0], 1.0f, 0, dir);

		std::vector<BlockGeomMeshInfo*> infos;
		infos.push_back(&meshinfo);
		return getGeom(0)->getPhisicMeshBitBase(key, infos);
	}
	return NULL;
}
int BlockMaterial::getPhisicMeshVerts(BaseSection* psection, const WCoord& blockpos, dynamic_array<Rainbow::Vector3f>& verts, dynamic_array<UInt16>& idxs)
{
	return 0;
}

void BlockMaterial::getMultiPhisicMeshVerts(Section* psection, const WCoord& posInSection, dynamic_array<TriangleBlockPhyData>& physDatas)
{

}

//int BlockMaterial::getPhisicMeshVerts(BaseSection* psection, const WCoord& blockpos, std::vector<Rainbow::Vector3f>& verts, std::vector<unsigned int>& idxs)
//{
//	return 0;
//}
/*
BlockTexElement *BlockMaterial::getTexElementFallbackOrigin(int tex_index, int gettextype, bool alpha_need_4bit)
{
	assert(tex_index==1 || tex_index==2);
	BlockTexElement *tex = g_BlockMtlMgr.getTexElement(tex_index==1 ? m_Def->Texture1 : m_Def->Texture2, gettextype, alpha_need_4bit);
	if(tex == NULL)
	{
		const BlockDef *origindef = BlockDefCsv::getInstance()->getOrigin(m_BlockResID);
		if(origindef && origindef!=m_Def)
		{
			tex = g_BlockMtlMgr.getTexElement(tex_index==1 ? origindef->Texture1 : origindef->Texture2, gettextype, alpha_need_4bit);
		}
	}
	return tex;
}*/

bool BlockMaterial::hasActorCollided(World *pworld, const WCoord &blockpos,bool includeVehicle/* = false */)
{
	CollisionDetect cd;
	cd.reset();
	createCollideData(&cd, pworld, blockpos);

	CollideAABB box;
	box.pos = cd.getCollideMin();
	box.dim = cd.getCollideMax() - box.pos;

	std::vector<IClientActor *>actors;
	pworld->getActorsInBox(actors, box,-1,includeVehicle);

	for(size_t i=0; i<actors.size(); i++)
	{
		// 这里判断是否为物理actor
		if (actors[i]->getILocoMotion() && actors[i]->getILocoMotion()->IsPhysActor())
		{
			return true;
		}
		
		int actortype = actors[i]->getObjType();
		if(actortype==OBJ_TYPE_MONSTER || actortype==OBJ_TYPE_ROLE || actortype==OBJ_TYPE_MINECART || actortype==OBJ_TYPE_ARROW || actortype == OBJ_TYPE_COBBLE)
		{
			return true;
		}
	}
	return false;
}

BlockColor BlockMaterial::getBlockColor(int blockdata)
{
	if (blockdata < 0 || blockdata > 15)
	{
		blockdata = 0;
	}
	return m_Colors[blockdata];
}

void BlockMaterial::tickCS()
{
}

unsigned char BlockMaterial::getBlockLightSrc(int iBlockData)
{
	return BlockMaterial::getLightValue(getBlockResID());
}

bool BlockMaterial::isOpenEvent()
{
	return false;
}
int BlockMaterial::commonConvertDataByRotate(int destDir, int rotatetype)
{
	int curDir = destDir;

	if (rotatetype == ROTATE_90 || rotatetype == MIRROR_90)
	{
		curDir = RotateDir90(curDir);
	}
	else if (rotatetype == ROTATE_180 || rotatetype == MIRROR_180)
	{
		curDir = ReverseDirection(curDir);
	}
	else if (rotatetype == ROTATE_270 || rotatetype == MIRROR_270)
	{
		curDir = ReverseDirection(RotateDir90(curDir));
	}

	if (rotatetype == MIRROR_0 && curDir >= DIR_NEG_X && curDir <= DIR_POS_X)
	{
		curDir = ReverseDirection(curDir);
	}
	else if (rotatetype == MIRROR_90 && curDir >= DIR_NEG_Z && curDir <= DIR_POS_Z)
	{
		curDir = ReverseDirection(curDir);
	}
	else if (rotatetype == MIRROR_270 && curDir >= DIR_NEG_Z && curDir <= DIR_POS_Z)
	{
		curDir = ReverseDirection(curDir);
	}
	else if (rotatetype == MIRROR_180 && curDir >= DIR_NEG_X && curDir <= DIR_POS_X)
	{
		curDir = ReverseDirection(curDir);
	}

	return curDir;
}

int BlockMaterial::commonConvertDataByRotateWithBit(int blockdata, int rotatetype, int b1, int b2)
{
	int curDir = (blockdata & b1);
	if (curDir > DIR_POS_Z)
		return blockdata;

	curDir = this->commonConvertDataByRotate(curDir, rotatetype);

	return (blockdata & b2) + curDir;
}


RenderBlockMaterial* BlockMaterial::getDefaultMtl()
{
	return m_mtlMgr.getMtl(m_defaultMtlIndex);
}

RenderBlockMaterial* BlockMaterial::getSnowCoverMtl()
{
	return m_mtlMgr.getMtl(m_snowcoverMtlIndex);
}


void BlockMaterial::modifyFromJson(World* pworld, const WCoord& blockpos, const MNJsonObject& jsonObj)
{
	//blockpos.y < 0 非法

	//this->ParseFromJson(jsonObj);

	////MNJsonObject out;
	////this->SerializeToJson(out);

	//MNJsonArray outJsonArray;


	//SENDTOEDITOR(Send_MessageBlockAttrChanged, m_BlockResID, outJsonArray);
}


void BlockMaterial::initAttributes()
{
	if (m_attrGeomName.empty())
		m_attrGeomName = m_geomName.empty() ? m_defaultGeomName : m_geomName;

	//m_attrName = "";
	//m_attrDesc = "";
	#ifndef STUDIO_SERVER
	ItemDef* itemdef = GetDefManagerProxy()->getItemDef(m_BlockResID);
	if (itemdef)
	{
		m_name = itemdef->EnglishName.empty() ? "Block" : itemdef->EnglishName;
		if (m_attrName.empty())
			m_attrName = itemdef->Name;
		if (m_attrDesc.empty())
			m_attrDesc = itemdef->Desc;
	}
	#endif
}



//todo check delete-begin
int BlockMaterial::getBlockClickCollide()
{
	return GetBlockDef()->ClickCollide;
}

int BlockMaterial::getBlockMoveCollide()
{
	return GetBlockDef()->MoveCollide;
}

int BlockMaterial::getBlockBlockFlow()
{
	return m_Def->BlockFlow;
}

int BlockMaterial::getBlockPushFlag()
{
	return m_Def->PushFlag;
}

int BlockMaterial::getBlockAntiExplode()
{
	return m_Def->AntiExplode;
}

float BlockMaterial::getBlockHardness()
{
	return m_Def->Hardness;
}

float BlockMaterial::getBlockSlipperiness(int blockdata)
{
	return m_Def->Slipperiness;
}

int BlockMaterial::getBlockBurnSpeed()
{
	return m_Def->BurnSpeed;
}

int BlockMaterial::getBlockCatchFire()
{
	return m_Def->CatchFire;
}

int BlockMaterial::getBlockToolMineDropId(int index)
{
	//if (index == 0)
	//{
	//	return m_Def->ToolMineDrops[0].item;;
	//}
	if (index < MAX_TOOLMINE_DROP)
	{
		return m_Def->ToolMineDrops[index].item;
	}
	
	return 0;
}
int BlockMaterial::getBlockToolMineDropProb(int index)
{
	//if (index == 0)
	//{
	//	return m_attrToolMineProb1;
	//}
	if (index < MAX_TOOLMINE_DROP)
	{
		return m_Def->ToolMineDrops[index].odds;
	}

	return 0;
}

int BlockMaterial::getBlockHandMineDrop()
{
	return m_Def->HandMineDrops.item;
}

int BlockMaterial::getBlockHandMineProb()
{
	return GetBlockDef()->HandMineDrops.odds;
}

int BlockMaterial::getBlockDropMineExp()
{
	return m_Def->DropExp;
}

int BlockMaterial::getBlockDropMineExpProb()
{
	return m_Def->DropExpOdds;
}
//todo check delete-end

int BlockMaterial::getLod() 
{
	if(m_Def)
		return m_Def->LOD;

	return 0;
}

int BlockMaterial::getBlockHP(World* pworld, const WCoord& blockpos)
{
	int curvalue = pworld->getBlockDataEx(blockpos);
	return m_Def->MaxHP - curvalue;
}

void BlockMaterial::ShowBlockCrack(World* pworld, const WCoord& blockpos)
{
	pworld->ShowBlockCrackEffect(this, blockpos);
}

//碰撞
bool BlockMaterial::defBlockMove()
{
	//return (m_attrMoveCollide & 1) != 0;
	return GetToggle(BlockToggle_BlockMove);
}

bool BlockMaterial::getCanBurn()
{
	//return m_Def->BurnSpeed > 0;
	return GetToggle(BlockToggle_BurnSpeed);
}

bool BlockMaterial::isKeepRoundBlock(BlockMaterial* mtl)
{
	return !(mtl->getBlockSpecialLogicType(0) & BlockMaterial::BlockSpceialLogicTeam0::BlockFaceToPlane);
}


bool BlockMaterial::checkBlockMeshIsBuild(const BuildSectionMeshData& data, const WCoord& blockpos, const int& d, bool& isNoClip)
{
	auto psection = data.m_SharedSectionData;
	WCoord selfPos = psection->getOrigin() + blockpos;
	auto pblock = psection->getBlock(blockpos);
	int curblockdata = pblock.getData();
	float blockheight = 1.0f;

	DirectionType dir = (DirectionType)d;

	bool isBuild = true;

	const WCoord* vNeighbor/*[8]*/ = Rainbow::RoundBlockNeighbor[dir];

	Block samesaidBlock;
	BlockMaterial* samesaidMtl = NULL;
	if (psection && psection->getSectionType() == SECTION_VEHICLE)
	{
		WCoord sameDirPos = blockpos + g_DirectionCoord[d];
		if (psection->isValidPos(sameDirPos))
		{
			samesaidBlock = psection->getBlock(sameDirPos);
			samesaidMtl = g_BlockMtlMgr.getMaterial(samesaidBlock.getResID());
		}
	}
	else if (psection)
	{
		//WCoord sameDirPos = selfPos + g_DirectionCoord[d];
		//blockpos 在section中的位置
		Block b = psection->getNeighborBlock(blockpos, g_DirectionCoord[d]);
		if (!b.isEmpty())
		{
			samesaidBlock = b;
			samesaidMtl = g_BlockMtlMgr.getMaterial(samesaidBlock.getResID());
		}
	}
	if (samesaidMtl)
	{
		//要添加半砖
		//if (samesaidMtl->coverNeighbor(samesaidBlock.getData(), this, curblockdata, ReverseDirection(dir)))
		//{
		//	isBuild = false;
		//	{
		//		bool t_[8] = { false };
		//		int count = 0;
		//		Block neighborBlock;
		//		BlockMaterial* neighborMtl = NULL;
		//		for (int i = 0; i < 4; i++)
		//		{
		//			WCoord diagonalPos = NeighborCoord(blockpos + vNeighbor[i], dir);
		//			if (psection && psection->getSectionType() == SECTION_VEHICLE)
		//			{
		//				if (psection->isValidPos(diagonalPos))
		//				{
		//					neighborBlock = psection->getBlock(diagonalPos);
		//					neighborMtl = g_BlockMtlMgr.getMaterial(neighborBlock.getResID());
		//				}
		//			}
		//			else if (psection)
		//			{
		//				//WCoord sameDirPos = NeighborCoord(selfPos + vNeighbor[i], dir);
		//				Block b = psection->getNeighborBlock(blockpos, NeighborCoord(vNeighbor[i], dir));
		//				if (!b.isEmpty())
		//				{
		//					neighborBlock = b;
		//					neighborMtl = g_BlockMtlMgr.getMaterial(neighborBlock.getResID());
		//				}
		//			}

		//			if (neighborMtl)
		//			{
		//				if (neighborBlock.getResID() > 0 && neighborBlock.getResID() != 20 && isKeepRoundBlock(neighborMtl))
		//				{
		//					t_[i] = true;
		//					count++;
		//				}
		//			}
		//		}
		//		if (count <= 2)
		//		{
		//			for (int i = 4; i < 8; i++)
		//			{
		//				WCoord diagonalPos = NeighborCoord(blockpos + vNeighbor[i], dir);
		//				if (psection && psection->getSectionType() == SECTION_VEHICLE)
		//				{
		//					if (psection->isValidPos(diagonalPos))
		//					{
		//						neighborBlock = psection->getBlock(diagonalPos);
		//						neighborMtl = g_BlockMtlMgr.getMaterial(neighborBlock.getResID());
		//					}
		//				}
		//				else if (psection)
		//				{
		//					//WCoord sameDirPos = NeighborCoord(selfPos + vNeighbor[i], dir);
		//					Block b = psection->getNeighborBlock(blockpos, NeighborCoord(vNeighbor[i], dir));
		//					if (!b.isEmpty())
		//					{
		//						neighborBlock = b;
		//						neighborMtl = g_BlockMtlMgr.getMaterial(neighborBlock.getResID());
		//					}
		//				}

		//				if (neighborMtl)
		//				{
		//					if (neighborBlock.getResID() > 0 && neighborBlock.getResID() != 20 && isKeepRoundBlock(neighborMtl))
		//					{
		//						t_[i] = true;
		//					}
		//				}
		//			}
		//			bool t[8] = { false };
		//			for (int i = 0; i < 8; i++)
		//			{
		//				WCoord diagonalPos = blockpos + vNeighbor[i];
		//				if (psection && psection->getSectionType() == SECTION_VEHICLE)
		//				{
		//					if (psection->isValidPos(diagonalPos))
		//					{
		//						neighborBlock = psection->getBlock(diagonalPos);
		//						neighborMtl = g_BlockMtlMgr.getMaterial(neighborBlock.getResID());
		//					}
		//				}
		//				else if (psection)
		//				{
		//					//WCoord sameDirPos = selfPos + vNeighbor[i];
		//					Block b = psection->getNeighborBlock(blockpos, vNeighbor[i]);
		//					if (!b.isEmpty())
		//					{
		//						neighborBlock = b;
		//						neighborMtl = g_BlockMtlMgr.getMaterial(neighborBlock.getResID());
		//					}
		//				}

		//				if (neighborMtl)
		//				{
		//					if (neighborBlock.getResID() > 0 && neighborBlock.getResID() != 20 && isKeepRoundBlock(neighborMtl))
		//					{
		//						t[i] = true;
		//					}
		//				}
		//			}

		//			if (t[0] && t[5] && t[3] && !t_[0] && !t_[5] && !t_[3])
		//			{
		//				isBuild = true;
		//			}
		//			else if (t[3] && t[4] && t[1] && !t_[3] && !t_[4] && !t_[1])
		//			{
		//				isBuild = true;
		//			}
		//			else if (t[1] && t[6] && t[2] && !t_[1] && !t_[6] && !t_[2])
		//			{
		//				isBuild = true;
		//			}
		//			else if (t[2] && t[7] && t[0] && !t_[2] && !t_[7] && !t_[0])
		//			{
		//				isBuild = true;
		//			}
		//		}
		//	}
		//}
		if (samesaidMtl->BlockTypeId() == BlockMaterial::BlockType::BlockType_Slab || samesaidMtl->BlockTypeId() == BlockMaterial::BlockType::BlockType_Snow)
		{
			isNoClip = true;
		}
		else if (samesaidMtl->GetAttrRenderType() == this->GetAttrRenderType() && samesaidMtl->GetAttrRenderType() == BLOCKRENDER_SPECIAL_CUBE_MODEL)
		{
			isBuild = false;
		}
		else if ((samesaidBlock.getResID() > 0 && samesaidBlock.getResID() != 20) && isKeepRoundBlock(samesaidMtl)) //在外星空气和空气中方块都为圆角
		{//半砖只和半砖合并
			isNoClip = true;
		}
	}
	return isBuild;
}


int BlockMaterial::getBlockMeshAngleData(const BuildSectionMeshData& data, const WCoord& blockpos, BlockGeomMeshInfo& mesh, const int& d, bool isIgnoreTileUV, RenderBlockMaterial* pmtl, const bool& isNoClip, bool mergeface, bool isSnowSwell, int neighorData)
{
	auto psection = data.m_SharedSectionData;
	auto pblock = psection->getBlock(blockpos);
	int curblockdata = pblock.getData();

	float blockheight = 1.0f;
	DirectionType specialdir = DIR_NOT_INIT;
	if (blockheight > 0 && blockheight < 1.0f) specialdir = DIR_POS_Y;
	else if (blockheight<0 && blockheight>-1.0f) specialdir = DIR_NEG_Y;

	const BiomeDef* biome = 0;
	WCoord selfPos = psection->getOrigin() + blockpos;

	unsigned int avelt[16];
	int useround = 0;
	DirectionType dir = (DirectionType)d;

	int blockdata = 0;
	// 	bool isNoClip = false;

	const float* texuv = Rainbow::BLOCKUV[dir];
	const char* origin_c/*[3]*/ = /*{ 0.f };*/ Rainbow::RoundBlockOriginC[dir];
	const WCoord* vNeighbor/*[8]*/ = Rainbow::RoundBlockNeighbor[dir];


	const char* du = Rainbow::DU[dir];
	const char* dv = Rainbow::DV[dir];

	const dynamic_array<UInt16>* indices = Rainbow::OrgBlockIndex[dir];

	// 	BlockGeomMeshInfo mesh;

	dynamic_array<BlockGeomVert> verts;
	verts.resize_initialized(16, kIncreaseToExactSize);

	unsigned short dir_color = Normal2LightColor(g_DirectionCoord[d].toVector3());

	Vector3f normalVec = g_DirectionCoord[d].toVector3();
	normalVec.y = 1.0f;
	Rainbow::Normalize(normalVec);
	BlockVector normal_dir = PackVertNormal(normalVec, 0);
	BlockVector vertcolor;
	vertcolor.v = 0xffffffff;
	vertcolor.w = dir_color;

	int vertex_size = 0;
	do
	{
		const float* uvtile = NULL;
		if (isIgnoreTileUV)
			uvtile = pmtl->getUVTile();
		// 		InitVert(verts[0], origin_c, du, dv, 0, 0, texuv[0 * 2], texuv[0 * 2 + 1], vertcolor, uvtile, 255, normal_dir);
		// 		InitVert(verts[1], origin_c, du, dv, 1, 0, texuv[1 * 2], texuv[1 * 2 + 1], vertcolor, uvtile, 255, normal_dir);
		// 		InitVert(verts[2], origin_c, du, dv, 1, 1, texuv[2 * 2], texuv[2 * 2 + 1], vertcolor, uvtile, 255, normal_dir);
		// 		InitVert(verts[3], origin_c, du, dv, 0, 1, texuv[3 * 2], texuv[3 * 2 + 1], vertcolor, uvtile, 255, normal_dir);

		unsigned int lt_me = psection->getLight2(NeighborCoord(blockpos, dir), true);
		unsigned int src_lt_u = psection->getLight2(NeighborCoord(blockpos + vNeighbor[0], dir), true);   //&GetGrid(grids, u - 1, v);
		unsigned int src_ltu = psection->getLight2(NeighborCoord(blockpos + vNeighbor[1], dir), true);    // &GetGrid(grids, u + 1, v);
		unsigned int src_ltv = psection->getLight2(NeighborCoord(blockpos + vNeighbor[2], dir), true);    // &GetGrid(grids, u, v + 1);
		unsigned int src_lt_v = psection->getLight2(NeighborCoord(blockpos + vNeighbor[3], dir), true);   // &GetGrid(grids, u, v - 1);
		unsigned int src_lt_vu = psection->getLight2(NeighborCoord(blockpos + vNeighbor[4], dir), true);   // &GetGrid(grids, u + 1, v - 1);
		unsigned int src_lt_v_u = psection->getLight2(NeighborCoord(blockpos + vNeighbor[5], dir), true);   // &GetGrid(grids, u - 1, v - 1);
		unsigned int src_ltvu = psection->getLight2(NeighborCoord(blockpos + vNeighbor[6], dir), true);    // &GetGrid(grids, u + 1, v + 1);
		unsigned int src_ltv_u = psection->getLight2(NeighborCoord(blockpos + vNeighbor[7], dir), true);    // &GetGrid(grids, u - 1, v + 1);

		unsigned int block0 = psection->getLight2(blockpos + vNeighbor[0], true);   //&GetGrid(grids, u - 1, v);
		unsigned int block1 = psection->getLight2(blockpos + vNeighbor[1], true);    // &GetGrid(grids, u + 1, v);
		unsigned int block2 = psection->getLight2(blockpos + vNeighbor[2], true);    // &GetGrid(grids, u, v + 1);
		unsigned int block3 = psection->getLight2(blockpos + vNeighbor[3], true);   // &GetGrid(grids, u, v - 1);
		unsigned int block4 = psection->getLight2(blockpos + vNeighbor[4], true);   // &GetGrid(grids, u + 1, v - 1);
		unsigned int block5 = psection->getLight2(blockpos + vNeighbor[5], true);   // &GetGrid(grids, u - 1, v - 1);
		unsigned int block6 = psection->getLight2(blockpos + vNeighbor[6], true);    // &GetGrid(grids, u + 1, v + 1);
		unsigned int block7 = psection->getLight2(blockpos + vNeighbor[7], true);    // &GetGrid(grids, u - 1, v + 1);


		avelt[0] = ((lt_me + src_lt_u + src_lt_v + src_lt_v_u) >> 2) & 0xff00ff;
		avelt[1] = ((lt_me + src_ltu + src_lt_vu + src_lt_v) >> 2) & 0xff00ff;
		avelt[2] = ((lt_me + src_ltv + src_ltvu + src_ltu) >> 2) & 0xff00ff;
		avelt[3] = ((lt_me + src_lt_u + src_ltv_u + src_ltv) >> 2) & 0xff00ff;

		//-v  -v-u 顶上左
		avelt[4] = ((block3 + block5 + src_lt_v + src_lt_v_u) >> 2) & 0xff00ff;
		//-v  -v+u 顶上右
		avelt[5] = ((block3 + block4 + src_lt_v + src_lt_vu) >> 2) & 0xff00ff;
		// v  v-u 顶下左
		avelt[6] = ((block2 + block7 + src_ltv + src_ltv_u) >> 2) & 0xff00ff;
		// v  v+u 顶下右
		avelt[7] = ((block2 + block6 + src_ltv + src_ltvu) >> 2) & 0xff00ff;
		// -u -v-u  侧上左
		avelt[8] = ((block0 + block5 + src_lt_u + src_lt_v_u) >> 2) & 0xff00ff;
		// u  -v+u 侧上右
		avelt[9] = ((block1 + block4 + src_ltu + src_lt_vu) >> 2) & 0xff00ff;
		// -u v-u  侧下左
		avelt[10] = ((block0 + block7 + src_lt_u + src_ltv_u) >> 2) & 0xff00ff;
		// u v+u  侧下右
		avelt[11] = ((block1 + block6 + src_ltu + src_ltvu) >> 2) & 0xff00ff;

		for (int o = 0; o < 8; o++)
		{
			Block neighborBlock;
			BlockMaterial* neighborMtl = NULL;
			if (psection && psection->getSectionType() == SECTION_VEHICLE)
			{
				if (psection->isValidPos(blockpos + vNeighbor[o]))
				{
					neighborBlock = psection->getBlock(blockpos + vNeighbor[o]);
					neighborMtl = g_BlockMtlMgr.getMaterial(neighborBlock.getResID());
				}
			}
			else if (psection)
			{
				//WCoord sameDirPos = selfPos + vNeighbor[o];
				Block b = psection->getNeighborBlock(blockpos, vNeighbor[o]);
				if (!b.isEmpty())
				{
					neighborBlock = b;
					neighborMtl = g_BlockMtlMgr.getMaterial(neighborBlock.getResID());
				}
			}

			bool isEven = false;
			if (neighborMtl)
			{
				if ((neighborBlock.getResID() > 0 && neighborBlock.getResID() != 20) && isKeepRoundBlock(neighborMtl))
				{
					isEven = true;
				}
			}

			if (isEven)
			{
				if (0 == o)
				{// -u
					blockdata |= 1;
				}
				else if (1 == o)
				{// +u
					blockdata |= (1 << 1);
				}
				else if (2 == o)
				{// +v
					blockdata |= (1 << 2);
				}
				else if (3 == o)
				{// -v
					blockdata |= (1 << 3);
				}
				else if (4 == o)
				{// -v + u
					blockdata |= (1 << 4);
				}
				else if (5 == o)
				{// -v - u
					blockdata |= (1 << 5);
				}
				else if (6 == o)
				{// +v + u
					blockdata |= (1 << 6);
				}
				else if (7 == o)
				{// v - u
					blockdata |= (1 << 7);
				}
			}
		}


		for (int o = 0; o < 8; o++)
		{
			Block neighborBlock;
			BlockMaterial* neighborMtl = NULL;
			WCoord diagonalPos = NeighborCoord(blockpos + vNeighbor[o], dir);
			if (psection && psection->getSectionType() == SECTION_VEHICLE)
			{
				if (psection->isValidPos(diagonalPos))
				{
					neighborBlock = psection->getBlock(diagonalPos);
					neighborMtl = g_BlockMtlMgr.getMaterial(neighborBlock.getResID());
				}
			}
			else if (psection)
			{
				//WCoord sameDirPos = NeighborCoord(selfPos + vNeighbor[o], dir);
				Block b = psection->getNeighborBlock(blockpos, NeighborCoord(vNeighbor[o], dir));
				if (!b.isEmpty())
				{
					neighborBlock = b;
					neighborMtl = g_BlockMtlMgr.getMaterial(neighborBlock.getResID());
				}
			}

			bool isEven = false;
			if (neighborMtl)
			{
				if (neighborBlock.getResID() > 0 && neighborBlock.getResID() != 20 && isKeepRoundBlock(neighborMtl))
				{
					isEven = true;
				}
			}

			if (isEven)
			{
				if (0 == o)
				{// -u
					blockdata |= (1 << 8);
				}
				else if (1 == o)
				{// +u
					blockdata |= (1 << 9);
				}
				else if (2 == o)
				{// +v
					blockdata |= (1 << 10);
				}
				else if (3 == o)
				{// -v
					blockdata |= (1 << 11);
				}
				else if (4 == o)
				{// -v + u
					blockdata |= (1 << 12);
				}
				else if (5 == o)
				{// -v - u
					blockdata |= (1 << 13);
				}
				else if (6 == o)
				{// +v + u
					blockdata |= (1 << 14);
				}
				else if (7 == o)
				{// v - u
					blockdata |= (1 << 15);
				}
			}
		}
		if (isNoClip)
		{
			blockdata |= (1 << 16);
		}
		useround = 16;

		// 		const vertex_cache* cache = Section::getVertexCache(NULL, dir, blockdata, RoundAngleSize, mergeface);
		unsigned char hor_ver = 6;
		if (blockheight < 1.f)
		{
			if (this->BlockTypeId() == BlockMaterial::BlockType_VerticalSlab || this->BlockTypeId() == BlockMaterial::BlockType_VerticalHalfSlantBlock) hor_ver = curblockdata & 3;
		}
		const vertex_cache* cache = Section::getVertexCacheEx(blockheight, NULL, dir, blockdata, neighorData, hor_ver, isSnowSwell, RoundAngleSize, mergeface);
		if (cache)
		{
			const auto& find = *cache;
			vertex_size = find.vertex_size;
			useround = find.vertex_size | ((find.index_size) << 8);
			const dynamic_array<BlockVertUV>* uv_ = getBlcokMaterialVertexUV(dir, false, blockheight, hor_ver, isSnowSwell, neighorData);
			//BlockVertUV uv_[16];
			//uv_[0].x = verts[0].uv.x, uv_[0].y = verts[0].uv.y;
			//uv_[1].x = verts[1].uv.x, uv_[1].y = verts[1].uv.y;
			//uv_[2].x = verts[2].uv.x, uv_[2].y = verts[2].uv.y;
			//uv_[3].x = verts[3].uv.x, uv_[3].y = verts[3].uv.y;
			//uv_[4].x = uv_[0].x + (uv_[1].x - uv_[0].x) * ROUND_DIFF; uv_[4].y = uv_[0].y + (uv_[1].y - uv_[0].y) * ROUND_DIFF;
			//uv_[5].x = uv_[0].x + (uv_[1].x - uv_[0].x) * (1 - ROUND_DIFF); uv_[5].y = uv_[0].y + (uv_[1].y - uv_[0].y) * (1 - ROUND_DIFF);
			//uv_[6].x = uv_[1].x + (uv_[2].x - uv_[1].x) * ROUND_DIFF; uv_[6].y = uv_[1].y + (uv_[2].y - uv_[1].y) * ROUND_DIFF;
			//uv_[7].x = uv_[1].x + (uv_[2].x - uv_[1].x) * (1 - ROUND_DIFF); uv_[7].y = uv_[1].y + (uv_[2].y - uv_[1].y) * (1 - ROUND_DIFF);
			//uv_[8].x = uv_[2].x + (uv_[3].x - uv_[2].x) * ROUND_DIFF; uv_[8].y = uv_[2].y + (uv_[3].y - uv_[2].y) * ROUND_DIFF;
			//uv_[9].x = uv_[2].x + (uv_[3].x - uv_[2].x) * (1 - ROUND_DIFF); uv_[9].y = uv_[2].y + (uv_[3].y - uv_[2].y) * (1 - ROUND_DIFF);
			//uv_[10].x = uv_[3].x + (uv_[0].x - uv_[3].x) * ROUND_DIFF; uv_[10].y = uv_[3].y + (uv_[0].y - uv_[3].y) * ROUND_DIFF;
			//uv_[11].x = uv_[3].x + (uv_[0].x - uv_[3].x) * (1 - ROUND_DIFF); uv_[11].y = uv_[3].y + (uv_[0].y - uv_[3].y) * (1 - ROUND_DIFF);
			//uv_[12].x = uv_[11].x + (uv_[6].x - uv_[11].x) * ROUND_DIFF; uv_[12].y = uv_[11].y + (uv_[6].y - uv_[11].y) * ROUND_DIFF;
			//uv_[13].x = uv_[11].x + (uv_[6].x - uv_[11].x) * (1 - ROUND_DIFF); uv_[13].y = uv_[11].y + (uv_[6].y - uv_[11].y) * (1 - ROUND_DIFF);
			//uv_[14].x = uv_[10].x + (uv_[7].x - uv_[10].x) * (1 - ROUND_DIFF); uv_[14].y = uv_[10].y + (uv_[7].y - uv_[10].y) * (1 - ROUND_DIFF);
			//uv_[15].x = uv_[10].x + (uv_[7].x - uv_[10].x) * ROUND_DIFF; uv_[15].y = uv_[10].y + (uv_[7].y - uv_[10].y) * ROUND_DIFF;
			for (int i = 0; i < vertex_size; i++)
			{
				auto& vertex_temp = find.base[i];
				verts[i].color = vertex_temp.color;
				verts[i].color.r = vertcolor.x;
				verts[i].color.g = vertcolor.y;
				verts[i].color.b = vertcolor.z;
				verts[i].normal = vertex_temp.normal;
				verts[i].uv.x = (*uv_)[vertex_temp.uv_index].x;
				verts[i].uv.y = (*uv_)[vertex_temp.uv_index].y;
				verts[i].pos.x = vertex_temp.pos.x + (origin_c[0] - origin_zero[0]) * BLOCK_SIZE;
				verts[i].pos.y = vertex_temp.pos.y + (origin_c[1] - origin_zero[1]) * BLOCK_SIZE;
				verts[i].pos.z = vertex_temp.pos.z + (origin_c[2] - origin_zero[2]) * BLOCK_SIZE;
				int avelt_temp = 0;
				if (vertex_temp.avelt_size == 1)
				{
					avelt_temp = avelt[vertex_temp.avelt_index_0];
				}
				else if (vertex_temp.avelt_size == 2)
				{
					avelt_temp = (avelt[vertex_temp.avelt_index_0] + avelt[vertex_temp.avelt_index_1]) >> 1;
				}
				else
				{
					int lt1 = (((avelt[vertex_temp.avelt_index_0] >> 4) & 0xf) + ((avelt[vertex_temp.avelt_index_1] >> 4) & 0xf) + ((avelt[vertex_temp.avelt_index_2] >> 4) & 0xf)) / 3;
					int lt2 = (((avelt[vertex_temp.avelt_index_0] >> 20) & 0xf) + ((avelt[vertex_temp.avelt_index_1] >> 20) & 0xf) + ((avelt[vertex_temp.avelt_index_2] >> 20) & 0xf)) / 3;
					avelt_temp = (lt1 << 4) + (lt2 << 20);
				}
				InitBlockVertLight(verts[i], avelt_temp, uvtile);
			}
			indices = &find.index;
		}
	} while (0);


	mesh.vertices = verts;
	mesh.indices = *indices;
	if (!useround)
	{
		mesh.vertices.assign_range(mesh.vertices.begin(), mesh.vertices.begin() + 4);
	}
	return useround;
}

static MINIW::GameStatic<std::unordered_map<unsigned int, dynamic_array<BlockVertUV>>> s_vertex_mtl_uv;
std::unordered_map<unsigned int, dynamic_array<BlockVertUV>>& BlockMaterial::vertex_mtl_uv()
{
	return *s_vertex_mtl_uv.EnsureInitialized();
}

static MINIW::GameStatic<Rainbow::Mutex> s_mutex_block_uv_cache;
Rainbow::Mutex& BlockMaterial::mutex_block_uv_cache()
{
	return *s_mutex_block_uv_cache.EnsureInitialized();
}

const dynamic_array<BlockVertUV>* BlockMaterial::getBlcokMaterialVertexUV(int dir, bool isSanrioBlock, float height, unsigned char hor_ver, bool isSnow, unsigned int dataex)
{
	Rainbow::Mutex::AutoLock lock(mutex_block_uv_cache());
	bool specialdir = height < 0;
	unsigned int key = dir | (isSanrioBlock ? (1 << 3) : 0);
	unsigned int subKey = 0;
	if (height < 1.f)
	{
		height = specialdir ? -height : height;
		int realHeight = specialdir ? -(int)(height * BLOCK_SIZE) % 101 : (int)(height * BLOCK_SIZE) % 101;
		if ((6 == hor_ver && dir < 4) || (4 > hor_ver && (dir != hor_ver && dir != ReverseDirection(hor_ver))))
		{	//realHeight最大数值只有100占7为128就够了。hor_ver最大值为6占3位
			subKey = (realHeight) | (hor_ver << 7) | (specialdir ? (1 << (7 + 3)) : 0);
			key |= (subKey << 4);
		}
	}

	dynamic_array<BlockVertUV>* pqueue = NULL;
	auto queue = vertex_mtl_uv().find(key);
	if (queue == vertex_mtl_uv().end())
	{
		pqueue = &vertex_mtl_uv()[key];
	}
	else
	{
		pqueue = &queue->second;
	}
	if (pqueue->size() < 16)
	{
		float ROUND_DIFF_ = ROUND_DIFF;
		pqueue->resize_initialized(16);
		float texuv[8] = { 0 };
		memcpy(texuv, Rainbow::BLOCKUV[dir], 8 * sizeof(float));

		for (int i = 0; i < 4; i++)
		{
			(*pqueue)[i].x = texuv[i * 2] * BLOCKUV_SCALE;
			(*pqueue)[i].y = texuv[i * 2 + 1] * BLOCKUV_SCALE;
		}
		(*pqueue)[4].x = (*pqueue)[0].x + ((*pqueue)[1].x - (*pqueue)[0].x) * ROUND_DIFF_;
		(*pqueue)[4].y = (*pqueue)[0].y + ((*pqueue)[1].y - (*pqueue)[0].y) * ROUND_DIFF_;
		(*pqueue)[5].x = (*pqueue)[0].x + ((*pqueue)[1].x - (*pqueue)[0].x) * (1 - ROUND_DIFF_);
		(*pqueue)[5].y = (*pqueue)[0].y + ((*pqueue)[1].y - (*pqueue)[0].y) * (1 - ROUND_DIFF_);
		(*pqueue)[6].x = (*pqueue)[1].x + ((*pqueue)[2].x - (*pqueue)[1].x) * ROUND_DIFF_;
		(*pqueue)[6].y = (*pqueue)[1].y + ((*pqueue)[2].y - (*pqueue)[1].y) * ROUND_DIFF_;
		(*pqueue)[7].x = (*pqueue)[1].x + ((*pqueue)[2].x - (*pqueue)[1].x) * (1 - ROUND_DIFF_);
		(*pqueue)[7].y = (*pqueue)[1].y + ((*pqueue)[2].y - (*pqueue)[1].y) * (1 - ROUND_DIFF_);
		(*pqueue)[8].x = (*pqueue)[2].x + ((*pqueue)[3].x - (*pqueue)[2].x) * ROUND_DIFF_;
		(*pqueue)[8].y = (*pqueue)[2].y + ((*pqueue)[3].y - (*pqueue)[2].y) * ROUND_DIFF_;
		(*pqueue)[9].x = (*pqueue)[2].x + ((*pqueue)[3].x - (*pqueue)[2].x) * (1 - ROUND_DIFF_);
		(*pqueue)[9].y = (*pqueue)[2].y + ((*pqueue)[3].y - (*pqueue)[2].y) * (1 - ROUND_DIFF_);
		(*pqueue)[10].x = (*pqueue)[3].x + ((*pqueue)[0].x - (*pqueue)[3].x) * ROUND_DIFF_;
		(*pqueue)[10].y = (*pqueue)[3].y + ((*pqueue)[0].y - (*pqueue)[3].y) * ROUND_DIFF_;
		(*pqueue)[11].x = (*pqueue)[3].x + ((*pqueue)[0].x - (*pqueue)[3].x) * (1 - ROUND_DIFF_);
		(*pqueue)[11].y = (*pqueue)[3].y + ((*pqueue)[0].y - (*pqueue)[3].y) * (1 - ROUND_DIFF_);
		(*pqueue)[12].x = (*pqueue)[11].x + ((*pqueue)[6].x - (*pqueue)[11].x) * ROUND_DIFF_;
		(*pqueue)[12].y = (*pqueue)[11].y + ((*pqueue)[6].y - (*pqueue)[11].y) * ROUND_DIFF_;
		(*pqueue)[13].x = (*pqueue)[11].x + ((*pqueue)[6].x - (*pqueue)[11].x) * (1 - ROUND_DIFF_);
		(*pqueue)[13].y = (*pqueue)[11].y + ((*pqueue)[6].y - (*pqueue)[11].y) * (1 - ROUND_DIFF_);
		(*pqueue)[14].x = (*pqueue)[10].x + ((*pqueue)[7].x - (*pqueue)[10].x) * (1 - ROUND_DIFF_);
		(*pqueue)[14].y = (*pqueue)[10].y + ((*pqueue)[7].y - (*pqueue)[10].y) * (1 - ROUND_DIFF_);
		(*pqueue)[15].x = (*pqueue)[10].x + ((*pqueue)[7].x - (*pqueue)[10].x) * ROUND_DIFF_;
		(*pqueue)[15].y = (*pqueue)[10].y + ((*pqueue)[7].y - (*pqueue)[10].y) * ROUND_DIFF_;
	}
	return pqueue;
}


void BlockMaterial::initNewBlockDef()
{
	if (!m_attrModifyByEditorTool)
		return;

	if (m_Def == m_newDef)
		return;

	if (m_Def)
	{
		if (!m_newDef)
		{
			m_newDef = SANDBOX_NEW(BlockDef);
			g_BlockMtlMgr.SetBlockNewDef(m_BlockResID, m_newDef);
		}
		m_newDef->copy(m_Def);
		m_newDef->ID = m_BlockResID;
		m_Def = m_newDef;
	}
	else
	{
		SANDBOX_DELETE(m_newDef);
	}
}

void BlockMaterial::setModifyByEditor(bool value)
{
	m_attrModifyByEditorTool = value;
	initNewBlockDef();

	OnAttributeChanged(this, &R_ModifyByEditorTool);
}

bool BlockMaterial::DefaultRenderMtlGet(std::string& value) const
{
	auto mtl = m_mtlMgr.getMtl(m_defaultMtlIndex);
	if (mtl)
	{
		value = mtl->getName();
		return true;
	}
	value = "";
	return true;
}
void BlockMaterial::DefaultRenderMtlSet(const std::string& value)
{
	auto oldMtl = m_mtlMgr.getMtl(m_defaultMtlIndex);
	if (!oldMtl)
	{
		return;
	}

	auto mtl = g_BlockMtlMgr.createRenderMaterial(value.c_str(), GetBlockDef(), oldMtl->GetTexType(), (BlockDrawType)oldMtl->GetDrawType(), oldMtl->GetMipMethod());
	if (mtl)
	{
		m_defaultMtlIndex = getRenderMtlMgr().addMtl(mtl);
		OGRE_RELEASE(mtl);

		OnAttributeChanged(this, &R_DefaultRenderMtl);
	}
}

bool BlockMaterial::LightSrcGet(int& value) const
{
	if (!m_Def) return false;
	value = m_Def->LightSrc;
	return true;
}
void BlockMaterial::LightSrcSet(const int& value)
{
	if (m_newDef)
	{
		m_newDef->LightSrc = value;

		BlockMaterial::m_LightValue[m_BlockResID] = value;

		OnAttributeChanged(this, &R_LightSrc);
	}
}
bool BlockMaterial::DropExpOddsGet(int& value) const
{
	if (!m_Def) return false;
	value = m_Def->DropExpOdds;
	return true;
}

void BlockMaterial::DropExpOddsSet(const int& value)
{
	if (m_newDef)
	{
		m_newDef->DropExpOdds = value;

		OnAttributeChanged(this, &R_MineExpOdds);
	}
}

bool BlockMaterial::DropExpGet(int& value) const
{
	if (!m_Def) return false;
	value = m_Def->DropExp;
	return true;
}
void BlockMaterial::DropExpSet(const int& value)
{
	if (m_newDef)
	{
		m_newDef->DropExp = value;

		OnAttributeChanged(this, &R_MineExp);
	}
}

bool BlockMaterial::HandMineProbGet(int& value) const
{
	if (!m_Def) return false;
	value = m_Def->HandMineDrops.odds;
	return true;
}
void BlockMaterial::HandMineProbSet(const int& value)
{
	if (m_newDef)
	{
		m_newDef->HandMineDrops.odds = value;

		OnAttributeChanged(this, &R_HandMineProb);
	}
}

bool BlockMaterial::HandMineDropGet(int& value) const
{
	if (!m_Def) return false;
	value = m_Def->HandMineDrops.item;
	return true;
}
void BlockMaterial::HandMineDropSet(const int& value)
{
	if (m_newDef)
	{
		m_newDef->HandMineDrops.item = value;

		OnAttributeChanged(this, &R_HandMineDrop);
	}
}

bool BlockMaterial::CatchFireGet(int& value) const
{
	if (!m_Def) return false;
	value = m_Def->CatchFire;
	return true;
}
void BlockMaterial::CatchFireSet(const int& value)
{
	if (m_newDef)
	{
		m_newDef->CatchFire = value;

		OnAttributeChanged(this, &R_CatchFire);
	}
}

bool BlockMaterial::BurnSpeedGet(int& value) const
{
	if (!m_Def) return false;
	value = m_Def->BurnSpeed;
	return true;
}

void BlockMaterial::BurnSpeedSet(const int& value)
{
	if (m_newDef)
	{
		m_newDef->BurnSpeed = value;
		SetToggle(BlockToggle_BurnSpeed, m_Def->BurnSpeed > 0);

		OnAttributeChanged(this, &R_BurnSpeed);
	}
}

bool BlockMaterial::SlipperinessGet(float& value) const
{
	if (!m_Def) return false;
	value = m_Def->Slipperiness;
	return true;
}
void BlockMaterial::SlipperinessSet(const float& value)
{
	if (m_newDef)
	{
		m_newDef->Slipperiness = value;

		OnAttributeChanged(this, &R_Slipperiness);
	}
}

bool BlockMaterial::HardnessGet(float& value) const
{
	if (!m_Def) return false;
	value = m_Def->Hardness;
	return true;
}
void BlockMaterial::HardnessSet(const float& value)
{
	if (m_newDef)
	{
		m_newDef->Hardness = value;

		OnAttributeChanged(this, &R_Hardness);
	}
}

bool BlockMaterial::AntiExplodeGet(int& value) const
{
	if (!m_Def) return false;
	value = m_Def->AntiExplode;
	return true;
}
void BlockMaterial::AntiExplodeSet(const int& value)
{
	if (m_newDef)
	{
		m_newDef->AntiExplode = value;

		OnAttributeChanged(this, &R_AntiExplode);
	}
}

bool BlockMaterial::PushFlagGet(int& value) const
{
	if (!m_Def) return false;
	value = m_Def->PushFlag;
	return true;
}
void BlockMaterial::PushFlagSet(const int& value)
{
	if (m_newDef)
	{
		m_newDef->PushFlag = value;

		OnAttributeChanged(this, &R_PushFlag);
	}
}

bool BlockMaterial::ToolMineDrop1Get(int& value) const
{
	if (!m_Def) return false;
	value = m_Def->ToolMineDrops[0].item;
	return true;
}
void BlockMaterial::ToolMineDrop1Set(const int& value)
{
	if (m_newDef)
	{
		m_newDef->ToolMineDrops[0].item = value;

		OnAttributeChanged(this, &R_ToolMineDrop1);
	}
}

bool BlockMaterial::ToolMineProb1Get(int& value) const
{
	if (!m_Def) return false;
	value = m_Def->ToolMineDrops[0].odds;
	return true;
}
void BlockMaterial::ToolMineProb1Set(const int& value)
{
	if (m_newDef)
	{
		m_newDef->ToolMineDrops[0].odds = value;
		OnAttributeChanged(this, &R_ToolMineProb1);
	}
}

bool BlockMaterial::BlockFlowGet(int& value) const
{
	if (!m_Def) return false;
	value = m_Def->BlockFlow;
	return true;
}
void BlockMaterial::BlockFlowSet(const int& value)
{
	if (m_newDef)
	{
		m_newDef->BlockFlow = value;
		OnAttributeChanged(this, &R_BlockFlow);
	}
}

bool BlockMaterial::ClickCollideGet(int& value) const
{
	if (!m_Def)
	{
		SANDBOX_ASSERT(false);
		return false;
	}
	value = m_Def->ClickCollide;
	return true;
}

void BlockMaterial::ClickCollideSet(const int& value)
{
	if (m_newDef)
	{
		m_newDef->ClickCollide = value;

		OnAttributeChanged(this, &R_ClickCollide);
	}
}

void BlockMaterial::PhyCollideGet(int& value) const
{
	if (m_Def)
		value = m_Def->PhyCollide;
}


bool BlockMaterial::MoveCollideGet(BLOCKCOLLIDE& value) const
{
	if (!m_Def) return false;
	value = (BLOCKCOLLIDE)m_Def->MoveCollide;
	return true;
}
void BlockMaterial::MoveCollideSet(const BLOCKCOLLIDE& value)
{
	if (!m_newDef) return;

	int phyCollide = 0;
	if (value == BLOCKCOLLIDE::SOLID || value == BLOCKCOLLIDE::NO_PROJECTILE)
		phyCollide = 1;

	// 更新phyCollide
	if (m_newDef->PhyCollide != phyCollide)
	{
		m_newDef->PhyCollide = phyCollide;
	}

	if (value != (BLOCKCOLLIDE)m_newDef->MoveCollide)
	{
		m_newDef->MoveCollide = (int)value;
		SetToggle(BlockToggle_BlockMove, (m_newDef->MoveCollide & 1) != 0);
		SetToggle(BlockToggle_BlockRay, m_newDef->MoveCollide == 1);

		OnAttributeChanged(this, &R_MoveCollide);
	}
}

int BlockMaterial::CsvDefIdGet() const
{
	return m_csvDefId;
}

void BlockMaterial::CsvDefIdSet(const int& value)
{
	if (value == m_csvDefId)
	{
		return;
	}
	m_csvDefId = value;
	initBlockDef(m_BlockResID, m_csvDefId);

	OnAttributeChanged(this, &R_CsvDefId);
}

int BlockMaterial::BlockResIdGet() const
{
	return m_BlockResID;
}
void BlockMaterial::BlockResIdSet(const int& value)
{
	if (value == m_BlockResID)
	{
		return;
	}
	m_BlockResID = value;
	//init(value);

	OnAttributeChanged(this, &R_BlockId);
}

bool BlockMaterial::AddToMgrGet() const
{
	return g_BlockMtlMgr.hasMaterial(m_BlockResID);

}
void BlockMaterial::AddToMgrSet(const bool& value)
{
	if (!g_BlockMtlMgr.hasMaterial(m_BlockResID))
	{
		g_BlockMtlMgr.AddBlockMaterial(this);
		OnAttributeChanged(this, &R_AddToMgr);
	}
}

bool BlockMaterial::IsReplaceableGet() const
{
	return GetToggle(BlockToggle_Replaceable);
}

void BlockMaterial::IsReplaceableSet(const bool& value)
{
	SetToggle(BlockToggle_Replaceable, value);
	OnAttributeChanged(this, &R_IsReplaceable);
}



void BlockMaterial::AppendBlockEffect(BlockEffectType effectType)
{

	if ((m_BlockEffectFlag & effectType) == 0) 
	{
		m_BlockEffectFlag |= effectType;		
	}
	SetBlockEffectDirty(true);
}

void BlockMaterial::RemoveBlockEffect(BlockEffectType effectType)
{
	if ((m_BlockEffectFlag & effectType) != 0) 
	{
		m_BlockEffectFlag ^= effectType;		
	}
	SetBlockEffectDirty(true);
}

void BlockMaterial::SetBlockEffectDirty(bool value)
{
	m_BlockEffectDirty = value;
}

bool BlockMaterial::HasBlockEffect(BlockEffectType effectType) const
{
	bool ret = ((m_BlockEffectFlag & effectType) != 0);
	return ret;
}

const BlockDef* BlockMaterial::GetBlockDef()
{
	return m_newDef ? m_newDef : m_Def;
}


void BlockMaterial::SetToggle(int tag, bool toggle)
{ 
	m_baseToggles.SetFlag(tag, toggle); 
}

bool BlockMaterial::GetToggle(int tag)const 
{
	return m_baseToggles.CheckFlag(tag); 
}

void BlockMaterial::DoOnBlockPlacedBy(World* pworld, const WCoord& blockpos, IClientPlayer* player, int flag, const Rainbow::Vector3f& colpoint, bool placeinto, int face)
{
	if (m_methodPlacedBy)
	{
		m_methodPlacedBy->CallLuaFunction<SandboxNode_Ref, WCoord, SandboxNode_Ref, Rainbow::Vector3f, bool, int>(GetWorkspace(pworld), blockpos * BLOCK_SIZE, (SandboxNode*)player, colpoint, placeinto, face);
	}
	else
	{
		if (flag == 0)
		{
			onBlockPlacedBy(pworld, blockpos, player);
		}
		else
		{
			onBlockPlacedBy(pworld, blockpos, player, colpoint, placeinto, face);
		}
	}

	if (m_notifyPlacedBy.IsValid())
		m_notifyPlacedBy.Emit(GetWorkspace(pworld), blockpos * BLOCK_SIZE, dynamic_cast<MNSandbox::SandboxNode*>(player), colpoint, placeinto, face);
}

void BlockMaterial::DoOnPlayRandEffect(World* pworld, const WCoord& blockpos)
{
	if (m_methodPlayRandEffect)
	{
		m_methodPlayRandEffect->CallLuaFunction<SandboxNode_Ref, WCoord>(GetWorkspace(pworld), blockpos * BLOCK_SIZE);
	}
	else
	{
		onPlayRandEffect(pworld, blockpos);
	}

	if (m_notifyPlayRandEffect.IsValid())
		m_notifyPlayRandEffect.Emit(GetWorkspace(pworld), blockpos * BLOCK_SIZE);
}

void BlockMaterial::DoOnNotify(World* pworld, const WCoord& blockpos, int blockid)
{
	if (m_methodNotify)
	{
		m_methodNotify->CallLuaFunction<SandboxNode_Ref, WCoord, int>(GetWorkspace(pworld), blockpos * BLOCK_SIZE, blockid);
	}
	else
	{
		onNotify(pworld, blockpos, blockid);
	}

	if (m_notifyNotify.IsValid())
		m_notifyNotify.Emit(GetWorkspace(pworld), blockpos * BLOCK_SIZE, blockid);
}

void BlockMaterial::DoOnBlockAdded(World* pworld, const WCoord& blockpos)
{
	this->DoOnPlayRandEffect(pworld, blockpos);
	if (m_methodBlockAdded)
	{
		m_methodBlockAdded->CallLuaFunction<SandboxNode_Ref, WCoord>(GetWorkspace(pworld), blockpos * BLOCK_SIZE);
	}
	else
	{
		onBlockAdded(pworld, blockpos);
	}

	if (m_notifyBlockAdded.IsValid())
		m_notifyBlockAdded.Emit(GetWorkspace(pworld), blockpos * BLOCK_SIZE);
}

void BlockMaterial::DoOnBlockRemoved(World* pworld, const WCoord& blockpos, int blockid, int blockdata)
{
	this->onStopEffect(pworld, blockpos);
	auto pEffectMgr = pworld->getEffectMgr();
	if (nullptr != pEffectMgr)
	{
		auto pParticle = pEffectMgr->getParticle(blockpos);
		if (nullptr != pParticle)
		{
			pEffectMgr->removeBlockParticle(blockpos, pParticle);
		}
	}
	if (m_methodBlockRemoved)
	{
		m_methodBlockRemoved->CallLuaFunction<SandboxNode_Ref, WCoord, int>(GetWorkspace(pworld), blockpos * BLOCK_SIZE, blockid);
	}
	else
	{
		onBlockRemoved(pworld, blockpos, blockid, blockdata);
	}

	if (m_notifyBlockRemoved.IsValid())
		m_notifyBlockRemoved.Emit(GetWorkspace(pworld), blockpos * BLOCK_SIZE, blockid);
}

void BlockMaterial::DoOnBlockDestroyedBy(World* pworld, const WCoord& blockpos, int blockid, int blockdata, BLOCK_DESTROY_REASON_T destroytype, MNSandbox::SandboxNode_Ref bywho)//IClientActor* bywho)
{
	if (m_methodDestroyedBy)
	{
		m_methodDestroyedBy->CallLuaFunction<SandboxNode_Ref, WCoord, int, int, MNSandbox::SandboxNode_Ref>(GetWorkspace(pworld), blockpos * BLOCK_SIZE, blockid, (int)destroytype, bywho);
	}
	else
	{
		onBlockDestroyedBy(pworld, blockpos, blockdata, destroytype, bywho.ToCast<IClientActor>());
	}

	if (m_notifyDestroyedBy.IsValid())
		m_notifyDestroyedBy.Emit(GetWorkspace(pworld), blockpos * BLOCK_SIZE, blockid, destroytype, bywho);
}

bool BlockMaterial::DoOnTrigger(World* pworld, const WCoord& blockpos, DirectionType face, IClientPlayer* player, const Rainbow::Vector3f& colpoint/* = Rainbow::Vector3f(0, 0, 0)*/)
{
	bool b = false;
	if (m_methodTrigger)
	{
		m_methodTrigger->CallFunctionImmediatelyReturn<bool, SandboxNode_Ref, WCoord, int, SandboxNode_Ref, Rainbow::Vector3f>(b, GetWorkspace(pworld), blockpos * BLOCK_SIZE, (int)face, (SandboxNode*)player, colpoint);
	}
	else
	{
		b = onTrigger(pworld, blockpos, face, player, colpoint);
	}

	if (b && m_notifyTrigger.IsValid())
		m_notifyTrigger.Emit(GetWorkspace(pworld), blockpos * BLOCK_SIZE, face, dynamic_cast<MNSandbox::SandboxNode*>(player), colpoint);

	return b;
}

bool BlockMaterial::DoOnBlockDamaged(World* pworld, const WCoord& blockpos, IClientPlayer* player, int attack_type, float damage)
{
	bool b = false;
	if (pworld->IsBlockProtected(blockpos, m_BlockResID))
	{
		//char content[256];
		//sprintf(content, "%s", "You Are Not allow place block here");
		/*	PB_ChatHC chatHC;
			chatHC.set_chattype(5);
			chatHC.set_content(content);
			chatHC.set_speaker("");
			chatHC.set_uin(0);
			GetGameNetManagerPtr()->sendToClient(getUin(), PB_CHAT_HC, chatHC, 0, false);*/
		if (player) player->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 10000201);
		return b;
	}

	/*if (m_methodBlockDamaged)
	{
		m_methodBlockDamaged->CallFunctionImmediatelyReturn<bool, SandboxNode_Ref, WCoord, SandboxNode_Ref, float>(b, GetWorkspace(pworld), blockpos * BLOCK_SIZE, (SandboxNode*)player, damage);
	}
	else
	{*/
		b = onBlockDamaged(pworld, blockpos, player,  attack_type,damage);
	//}

	//if (b && m_notifyBlockDamaged.IsValid())
	//	m_notifyBlockDamaged.Emit(GetWorkspace(pworld), blockpos * BLOCK_SIZE, dynamic_cast<MNSandbox::SandboxNode*>(player), damage);

	return b;
}

bool BlockMaterial::DoOnBlockRepaired(World* pworld, const WCoord& blockpos, IClientPlayer* player, float amount)
{
	bool b = false;
	//if (m_methodBlockRepaired)
	//{
		//m_methodBlockRepaired->CallFunctionImmediatelyReturn<bool, SandboxNode_Ref, WCoord, SandboxNode_Ref, float>(b, GetWorkspace(pworld), blockpos * BLOCK_SIZE, (SandboxNode*)player, amount);
		/*}
		else
		{*/
		b = onBlockRepaired(pworld, blockpos, player, amount);
	//}

	//if (b && m_notifyBlockRepaired.IsValid())
	//	m_notifyBlockRepaired.Emit(GetWorkspace(pworld), blockpos * BLOCK_SIZE, dynamic_cast<MNSandbox::SandboxNode*>(player), amount);

	return b;
}


bool BlockMaterial::DoOnEvent(World* pworld, const WCoord& blockpos, int eventid, int eventparam)
{
	bool b = false;
	if (m_methodEvent)
	{
		m_methodEvent->CallFunctionImmediatelyReturn<bool, SandboxNode_Ref, WCoord, int, int>(b, GetWorkspace(pworld), blockpos * BLOCK_SIZE, eventid, eventparam);
	}
	else
	{
		b = onEvent(pworld, blockpos, eventid, eventparam);
	}

	if (b && m_notifyEvent.IsValid())
		m_notifyEvent.Emit(GetWorkspace(pworld), blockpos * BLOCK_SIZE, eventid, eventparam);

	return b;
}

bool BlockMaterial::DoOnActorCollidedWithBlock(World* pworld, const WCoord& blockpos, IClientActor* actor)
{
	bool b = false;
	if (m_methodActorCollidedWithBlock)
	{
		m_methodActorCollidedWithBlock->CallFunctionImmediatelyReturn<bool, SandboxNode_Ref, WCoord, SandboxNode_Ref>(b, GetWorkspace(pworld), blockpos * BLOCK_SIZE, (SandboxNode*)actor);
	}
	else
	{
		b = onActorCollidedWithBlock(pworld, blockpos, actor);
	}

	if (b && m_notifyActorCollidedWithBlock.IsValid())
		m_notifyActorCollidedWithBlock.Emit(GetWorkspace(pworld), blockpos * BLOCK_SIZE, dynamic_cast<MNSandbox::SandboxNode*>(actor));

	return b;
}

void BlockMaterial::DoOnActorWalking(World* pworld, const WCoord& blockpos, IClientActor* actor)
{
	if (m_methodActorWalking)
	{
		m_methodActorWalking->CallLuaFunction<SandboxNode_Ref, WCoord, SandboxNode_Ref>(GetWorkspace(pworld), blockpos * BLOCK_SIZE, (SandboxNode*)actor);
	}
	else
	{
		onActorWalking(pworld, blockpos, actor);
	}

	if (m_notifyActorWalking.IsValid())
		m_notifyActorWalking.Emit(GetWorkspace(pworld), blockpos * BLOCK_SIZE, dynamic_cast<MNSandbox::SandboxNode*>(actor));
}

void BlockMaterial::DoOnActorMoving(World* pworld, const WCoord& blockpos, IClientActor* actor)
{
	if (m_methodActorWalking)
	{
		m_methodActorWalking->CallLuaFunction<SandboxNode_Ref, WCoord, SandboxNode_Ref>(GetWorkspace(pworld), blockpos * BLOCK_SIZE, (SandboxNode*)actor);
	}
	else
	{
		onActorMoving(pworld, blockpos, actor);
	}

	if (m_notifyActorWalking.IsValid())
		m_notifyActorWalking.Emit(GetWorkspace(pworld), blockpos * BLOCK_SIZE, dynamic_cast<MNSandbox::SandboxNode*>(actor));
}

bool BlockMaterial::DoOnFertilized(World* pworld, const WCoord& blockpos, int fertiliser)
{
	bool b = false;
	if (m_methodFertilized)
	{
		m_methodFertilized->CallFunctionImmediatelyReturn<bool, SandboxNode_Ref, WCoord, int>(b, GetWorkspace(pworld), blockpos * BLOCK_SIZE, fertiliser);
	}
	else
	{
		b = onFertilized(pworld, blockpos, fertiliser);
	}

	if (b && m_notifyFertilized.IsValid())
		m_notifyFertilized.Emit(GetWorkspace(pworld), blockpos * BLOCK_SIZE, fertiliser);

	return b;
}

bool BlockMaterial::updateBlockLaser(World* pworld, const WCoord& blockpos, int dir, int delay)
{
	return false;
}

unsigned int BlockMaterial::electricEmitBlockLaserType(World* pworld, const WCoord& blockpos, int dir)
{
	return 0;
}

bool BlockMaterial::beginTransfer(World* pworld, const WCoord& blockpos, long long objID, int dir)
{
	int blockid = pworld->getBlockID(blockpos);

	WorldContainer* container = pworld->getContainerMgr()->getContainer(blockpos);
	if (container)
	{
		auto actor = pworld->getActorMgr()->iFindActorByWID(objID);
		IClientItem* item = dynamic_cast<IClientItem*>(actor);
		if (item)
		{
			int n = container->onInsertItem(*(item->getItemData()), item->getItemData()->getNum(), ReverseDirection(dir));
			if (n > 0)
			{
				item->getItemData()->clear();
				actor->setNeedClear();
				return true;
			}
		}
	}
	return false;
}

void BlockMaterial::endTransfer(World* pworld, const WCoord& blockpos)
{

}

bool BlockMaterial::canConnectToDir(World* pworld, const WCoord& blockpos, int dir, const BackPackGrid& grid)
{
	if (pworld == NULL)
	{
		return false;
	}
	WorldContainer* container = dynamic_cast<WorldContainer*>(pworld->getContainerMgr()->getContainer(blockpos));
	if (container == NULL)
	{
		return false;
	}
	return container->canInsertItem(grid, dir);
}

bool BlockMaterial::canSprayPaint(World* pworld, const WCoord& blockpos)
{
	return  m_renderType == BLOCKRENDER_CUBE;
}


void BlockMaterial::DoBlockTick(World* pworld, const WCoord& blockpos)
{
	// 编辑模式，不需要方块tick
	//if (Config::GetSingleton().IsEditorEditMode())
	//	return;

	//Event().Emit("OnBlockTick", SandboxContext()
	//	.SetData_Usertype<World>("world", pworld)
	//	.SetData_UserObject<WCoord>("pos", blockpos)
	//);
	blockTick(pworld, blockpos);
}

void BlockMaterial::DoDigBegin(World* pworld, const WCoord& blockpos, MNSandbox::SandboxNode_Ref triggerActor)
{
	auto workspace = MNSandbox::GetWorkspace(pworld);
	if (workspace)
		m_notifyDigBegin.Emit(workspace, blockpos * BLOCK_SIZE, triggerActor);
}

void BlockMaterial::DoDigFinish(World* pworld, const WCoord& blockpos, MNSandbox::SandboxNode_Ref triggerActor)
{
	auto workspace = MNSandbox::GetWorkspace(pworld);
	if (workspace)
		m_notifyDigFinish.Emit(workspace, blockpos * BLOCK_SIZE, triggerActor);
}

void BlockMaterial::DoDigCancel(World* pworld, const WCoord& blockpos, MNSandbox::SandboxNode_Ref triggerActor)
{
	auto workspace = MNSandbox::GetWorkspace(pworld);
	if (workspace)
		m_notifyDigCancel.Emit(workspace, blockpos * BLOCK_SIZE, triggerActor);
}

void BlockMaterial::DoOnClickByActor(World* pworld, const WCoord& blockpos, MNSandbox::SandboxNode_Ref triggerActor, bool bNotify)
{
	auto workspace = MNSandbox::GetWorkspace(pworld);
	if (workspace)
	{
		if (m_methodClickByActor)
		{
			m_methodClickByActor->CallLuaFunction<SandboxNode_Ref, WCoord, SandboxNode_Ref>(workspace, blockpos * BLOCK_SIZE, triggerActor);
		}
		if (bNotify)
		{
			DoOnClickByActorNotify(pworld, blockpos, triggerActor);
		}
	}
} 

void BlockMaterial::DoOnClickByActorNotify(World* pworld, const WCoord& blockpos, MNSandbox::SandboxNode_Ref triggerActor)
{
	auto workspace = MNSandbox::GetWorkspace(pworld);
	if (m_notifyClickByActor.IsValid())
	{
		m_notifyClickByActor.Emit(workspace, blockpos * BLOCK_SIZE, triggerActor);
	}
}

bool BlockMaterial::HasClickByActorCallback()
{
	return m_methodClickByActor;
}


bool BlockMaterial::BreakableGet(bool& value) const
{
	if (!m_Def) return false;
	value = m_Def->Breakable;
	return true;
}

void BlockMaterial::BreakableSet(const bool& value)
{
	if (m_newDef)
	{
		m_newDef->Breakable = value;

		OnAttributeChanged(this, &R_Breakable);
	}
}

bool BlockMaterial::placeSimpleBlock(World* pworld, int blockid, const WCoord& targetpos, DirectionType targetface)
{
	const ItemDef* itemdef = GetDefManagerProxy()->getItemDef(blockid);
	if (blockid > 0 && itemdef)
	{
		if (!itemdef->UseScript.empty() && (itemdef->UseTarget == ITEM_USE_CLICKBLOCK || itemdef->UseTarget == ITEM_USE_CLICKLIQUID || itemdef->UseTarget == ITEM_USE_PRESSFOOD))
		{
			static std::string itemuse = "MobEgg_OnUse";
			if (itemuse.compare(itemdef->UseScript.c_str()) != 0)
			{
				bool scripthandled = false;
				int setBlockAllRet = 0;
				MINIW::ScriptVM::game()->callFunction(itemdef->UseScript.c_str(), "u[IClientPlayer]u[World]iiii>bi", nullptr, pworld, targetpos.x, targetpos.y, targetpos.z, targetface, &scripthandled, &setBlockAllRet);
				return scripthandled && setBlockAllRet == 0;
			}
		}
		else if (blockid < SOC_BLOCKID_MAX)//SOC 方块ID 范围 0-4095 不支持扩展id
		{
			BlockDef* def = GetDefManagerProxy()->getBlockDef(blockid);
			if (!def)
				return false;

			//正在操作的方块
			BlockMaterial* cur_blockMat = g_BlockMtlMgr.getMaterial(blockid);
			if (!cur_blockMat)
				return false;

			//聚焦的方块
			int foc_blockid = pworld->getBlockID(targetpos);
			BlockMaterial* foc_blockMat = pworld->getBlockMaterial(targetpos);
			if (!foc_blockMat)
				return false;

			//将要放置的方块
			WCoord placePos = NeighborCoord(targetpos, targetface);
			DirectionType placeFace = ReverseDirection(targetface);
			BlockMaterial* place_blockMat = pworld->getBlockMaterial(placePos);
			if (!place_blockMat)
				return false;

			//逻辑校验
			WCoord t_pos;
			DirectionType hit_face;
			bool isagain;
			if (foc_blockMat->isReplaceable() && blockid != foc_blockid)
			{
				t_pos = targetpos;
				hit_face = DIR_NEG_Y;
				isagain = false;
			}
			else if(foc_blockMat->canPlacedAgain(pworld, blockid, Rainbow::Vector3f::zero, targetpos, true, placeFace))
			{
				t_pos = targetpos;
				hit_face = placeFace;
				isagain = true;
			}
			else
			{
				std::vector<int> sandCoverId = GetLuaInterfaceProxy().get_lua_const()->sandCoverId;
				auto iter = std::find(sandCoverId.begin(), sandCoverId.end(), blockid);
				if (iter != sandCoverId.end() && (blockid == BLOCK_SOLIDSAND || blockid == BLOCK_REDSAND))
				{
					t_pos = targetpos;
					hit_face = placeFace;
					isagain = false;
				}
				else
				{
					t_pos = placePos;
					hit_face = placeFace;
					isagain = place_blockMat->canPlacedAgain(pworld, blockid, Rainbow::Vector3f::zero, targetpos, false, placeFace);
					if (!place_blockMat->isReplaceable() && !isagain)
					{
						return false;
					}
				}
			}
			if (pworld->getBlockID(targetpos) == 150000 || pworld->getBlockID(targetpos) == 150001)
			{
				return false;
			}
			else if (blockid == ITEM_SEA_WEED)
			{
				return false;
			}
			if (blockid == BLOCK_LIGHTMUSHROOM && hit_face != DIR_NEG_Y)
			{
				return false;
			}

			BlockMaterial* blockMat = pworld->getBlockMaterial(t_pos);
			if(!blockMat) return false;
			int blockdata = blockMat->getPlaceBlockData(pworld, t_pos, hit_face, 0, 0, 0, 0);
			if (blockdata < 0) return false;
			if (!isagain)
			{
				pworld->setBlockAll(t_pos, blockid, blockdata, 3);
			}
			for (int i = 1; i < def->Height; i++)
			{
				pworld->setBlockAll(t_pos + WCoord(0, i, 0), blockid, blockdata | 8, 3);
			}
			return true;
		}
	}
	return false;
}

bool BlockMaterial::voidRandomBlockTick(World* pworld, const WCoord& blockpos)
{
	if (needCheckVoidNight())
	{
		return g_BlockMtlMgr.checkEnterVoidNight(pworld, blockpos);
	}

	return false;
}

bool BlockMaterial::voidblockTick(World* pworld, const WCoord& blockpos)
{
	return false;
}

// 开始虚空化
bool BlockMaterial::beginVoidNightMutant(World* pworld, const WCoord& blockpos)
{
	return false;
}

// 开始恢复虚空化
bool BlockMaterial::beginVoidNightResume(World* pworld, const WCoord& blockpos)
{
	return false;
}

void BlockMaterial::OnClearNotify()
{
	m_notifyPlacedBy.Clear();
	m_notifyPlayRandEffect.Clear();
	m_notifyNotify.Clear();
	m_notifyBlockAdded.Clear();
	m_notifyBlockRemoved.Clear();
	m_notifyDestroyedBy.Clear();
	m_notifyTrigger.Clear();
	m_notifyEvent.Clear();
	m_notifyActorCollidedWithBlock.Clear();
	m_notifyActorWalking.Clear();
	m_notifyFertilized.Clear();
	m_notifyRefreshChunk.Clear();
	m_notifyDigBegin.Clear();
	m_notifyDigFinish.Clear();
	m_notifyDigCancel.Clear();
	m_notifyClickByActor.Clear();

	Super::OnClearNotify();
}

void BlockMaterial::InitScriptComponent()
{
	if (!m_Def)
		return;

	if (m_pBlockScriptComponent)
		return;

	m_pBlockScriptComponent = GetISandboxActorSubsystem()->CreateBlockScriptComponent(this, m_Def->ID); // BlockScriptComponent::CreateBlockScriptComponent(this);
}


int BlockMaterial::getMoveCollider() const
{
	return m_Def->MoveCollide;
}
//////////////////////////////////////////////////////////


BlockRenderMaterialMgr::BlockRenderMaterialMgr()
{
	m_vMtls.reserve(256);
}
BlockRenderMaterialMgr::~BlockRenderMaterialMgr()
{
	clearMtls();
}
RenderBlockMaterial* BlockRenderMaterialMgr::getMtl(unsigned int index)const
{
	return index < m_vMtls.size() ? m_vMtls.at(index) : NULL;
}

unsigned int BlockRenderMaterialMgr::addMtl(RenderBlockMaterial* mtl)
{
	if (!mtl)
	{
		LOG_INFO("addMtl mtl is nil");
		//assert(false);
		return Rainbow::MAX_UINT;
	}
	mtl->AddRef();
	m_vMtls.push_back(mtl);

	return m_vMtls.size() - 1;
}

void BlockRenderMaterialMgr::removeMtl(unsigned int index)
{
	if (index >= m_vMtls.size())
	{
		return;
	}
	ENG_RELEASE(m_vMtls.at(index));
	m_vMtls.erase(m_vMtls.begin() + index);
}

void BlockRenderMaterialMgr::clearMtls()
{
	for (auto mtl : m_vMtls)
	{
		ENG_RELEASE(mtl);
	}
	m_vMtls.clear();
}

void BlockRenderMaterialMgr::changeMtl(unsigned int index, RenderBlockMaterial* mtl)
{
	if (index >= m_vMtls.size())
		return;

	ENG_RELEASE(m_vMtls.at(index));
	if (mtl)
		mtl->AddRef();
	m_vMtls.at(index) = mtl;
}

////////////////////////////////////////////////////////////////////////////////////////

///////////////////////////////////////////////////////////////////////////////////////// Begine BlockGeomTemplateLOD

void BlockGeomTemplateLOD::SetGeom(BlockGeomTemplate* gemo)
{
	m_GemoLOD[0] = gemo;// m_GemoLOD[1] = m_GemoLOD[2] = gemo;
}
void BlockGeomTemplateLOD::LoadLOD(const char* base_gemo_name)
{
	char gemo_lod_name[256];
	int lodCount = Rainbow::GetMiniCraftRenderer().GetLODCount() + 1;		//+1 for last level
	for (int lodIndex = 1; lodIndex < lodCount; ++lodIndex)
	{
		sprintf(gemo_lod_name, "%s_lod%d", base_gemo_name, lodIndex);
		BlockGeomTemplate* geomTemplate = g_BlockMtlMgr.getGeomTemplate(gemo_lod_name);
		if (geomTemplate != nullptr)
		{
			m_GemoLOD[lodIndex] = geomTemplate;
		}
	}
}


///////////////////////////////////////////////////////////////////////////////////////// End BlockGeomTemplateLOD


/// 实例
typedef BlockMaterial::BlockInstance BlockMaterialInstance;
IMPLEMENT_SCENEOBJECTCLASS_NO_REFLEXCONTAINER(BlockMaterialInstance)
MNSandbox::ReflexContainer BlockMaterialInstance::ms_reflexContainer(nullptr, (BlockMaterialInstance*)nullptr);

IMPLEMENT_BLOCKMATERIAL_INSTANCE_R_PARAM(BlockMaterial, R_Id, int)(0, "Instance_ID", "Block", &BlockMaterialInstance::GetBlockId, ReflexConfig::REG_NO_WRITE);
IMPLEMENT_BLOCKMATERIAL_INSTANCE_R_PARAM(BlockMaterial, R_Pos, Rainbow::Vector3f)(1, "Position", "Block", &BlockMaterialInstance::PosGet, ReflexConfig::REG_NO_WRITE);


MNSandbox::AutoRef<BlockMaterialInstance> BlockMaterial::GetBlockNode(World* pworld, const WCoord& pos)
{
	auto block = pworld->getBlock(pos);
	if (block.isEmpty())
		return nullptr;

	if (!BlockMaterialMgr::getSingletonPtr())
		return nullptr;

	auto mtrl = BlockMaterialMgr::getSingleton().getMaterial(block.getResID());
	if (!mtrl)
		return nullptr;

	MNSandbox::AutoRef<BlockMaterialInstance> ins = mtrl->GetBlockInstance();
	if (!ins)
		return nullptr;

	ins->m_pworld = pworld;
	ins->m_pos = pos;
	ins->m_blockMtrl = mtrl;
	ins->m_block = block;
	WorldContainer* container = pworld->getContainerMgr() ? pworld->getContainerMgr()->getContainer(pos) : nullptr;
	ins->m_Container = container;
	return ins;
}

bool BlockMaterialInstance::checkValid()
{
	auto block = m_pworld->getBlock(m_pos);
	if (!block.isEmpty())
	{
		return block.getResID() == m_blockMtrl->getBlockResID();
	}
	return false;
}

int BlockMaterialInstance::GetBlockData() const
{
	return m_pworld->getBlockData(m_pos);
}

void BlockMaterialInstance::SetBlockData(const int& data)
{
	if (!checkValid())
	{
		return;
	}
	m_pworld->setBlockData(m_pos, data);
}

bool BlockMaterialInstance::PosGet(Rainbow::Vector3f& value) const
{
	value = m_pos.toVector3();
	return true;
}

int BlockMaterialInstance::GetBlockId() const
{
	return m_blockMtrl->getBlockResID();
}

int BlockMaterialInstance::GetBlockDir() const
{
	return m_block.getData() & 3;
}

void BlockMaterialInstance::SetBlockDir(const int& dir)
{
	auto oldBlockData = m_block.getData();
	auto oldBlockId = m_block.getResID();
	auto oldBlockDir = GetBlockDir();
	WCoord downPos = m_pos;
	int count = 0;
	do
	{
		downPos = DownCoord(downPos);
		count++;
		if (!m_pworld->getBlock(downPos).isEmpty())
		{
			return;
		}
	} while ((m_pworld->getBlock(downPos).getResID() == oldBlockId) && count < 10);

	auto downBlock = m_pworld->getBlock(downPos);
	if (!downBlock.isEmpty() || (downBlock.getResID() == oldBlockId))
	{
		return;
	}
	m_pworld->SetBlockNodePreDelete(m_pos.x, m_pos.y, m_pos.z, oldBlockId, true, false);

	
	WCoord placePos;
	bool bRet = false;
	m_pworld->GetBlockOperation()->GetPlacePos(downBlock.getResID(), oldBlockId, downPos, placePos, bRet);

	if (!bRet)
	{
		return;
	}

	if (!m_pworld->CanPlaceBlockAt(placePos.x, placePos.y, placePos.z, oldBlockId, dir))
	{
		m_pworld->GetBlockOperation()->ResetBlockPosCache();
		return;
	}


	//销毁
	m_pworld->destroyBlockEx(m_pos.x, m_pos.y, m_pos.z, false);
	auto newBlockData = (oldBlockData & 0xFFFC) + dir;

	//放置
	BlockOperation::BlockOptPlace opt;
	opt._x = downPos.x;
	opt._y = downPos.y;
	opt._z = downPos.z;
	opt._blockid = oldBlockId;
	opt._targetface = DIR_POS_Y;
	opt._blockdata = newBlockData;
	m_pworld->GetBlockOperation()->PlaceBlock(opt);

	m_pworld->GetBlockOperation()->ResetBlockPosCache();
}