
#ifndef __BLOCK_SIMPLE_SOFA_H__
#define __BLOCK_SIMPLE_SOFA_H__

#include "BlockFurniture.h"

class BlockSimpleSofa : public BlockChair //tolua_exports
{ //tolua_exports
	DECLARE_BLOCKMATERIAL(BlockSimpleSofa)
public:
	//tolua_begin
	//virtual const char *getGeomName() override;
// 	virtual bool onTrigger(World *pworld, const WCoord &blockpos, DirectionType face, IClientPlayer *player, const Rainbow::Vector3f &colpoint=Rainbow::Vector3f(0, 0, 0));
	virtual void onNotify(World *pworld, const WCoord &blockpos, int blockid);
	virtual int getProtoBlockGeomID(int *idbuf, int *dirbuf) override;
	virtual void dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata = 0, BLOCK_MINE_TYPE droptype = BLOCK_MINE_NOTOOL, float chance = 1.0f, int uin = -1);
	virtual void createCollideData(CollisionDetect *coldetect, World *pworld, const WCoord &blockpos);
	virtual int convertDataByRotate(int blockdata, int rotatetype) override;
	virtual SectionMesh *createBlockProtoMesh(int protodata = 0);
	virtual void onBlockBeDig(World* pworld, const WCoord& blockpos, int process, IClientPlayer* player) override;

// 	virtual bool hasContainer();
	//tolua_end
private:
	virtual int getBlockGeomID(int *idbuf, int *dirbuf, const SectionDataHandler* sectionData, const WCoord &blockpos, World* world);
	virtual void initGeomName() override;
}; //tolua_exports

#endif