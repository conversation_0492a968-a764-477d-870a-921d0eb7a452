
#include "BlockOpenDoorMachine.h"
#include "world.h"
#include "container_sandboxGame.h"
#include "section.h"
#include "SectionMesh.h"
#include "BlockSocElectricDoor.h"
#include "ClientPlayer.h"

IMPLEMENT_BLOCKMATERIAL(BlockOpenDoorMachine);

using namespace MINIW;

BlockOpenDoorMachine::BlockOpenDoorMachine()
{

}

BlockOpenDoorMachine::~BlockOpenDoorMachine()
{

}

void  BlockOpenDoorMachine::init(int resid)
{
	ModelBlockMaterial::init(resid);
	//SetToggle(BlockToggle_HasContainer, true);
	if (BlockMaterial::m_LoadOnlyLogic) return;
}

bool BlockOpenDoorMachine::onTrigger(World* pworld, const WCoord& blockpos, DirectionType face, IClientPlayer* player, const Rainbow::Vector3f& colpoint)
{
	if (!player || !pworld)
	{
		return false;
	}
	if (pworld->isRemoteMode())
	{
		return true;
	}
	for (int x = -1; x <= 1; x++)
	{
		for (int y = -1; y <= 1; y++)
		{
			for (int z = -1; z <= 1; z++)
			{
				WCoord pos = blockpos + WCoord(x, y, z);
				auto pmtl = pworld->getBlockMaterial(pos);
				if (pmtl && pmtl->BlockTypeId() == BlockType_Door && pmtl->getBlockSpecialLogicType(0) & BlockMaterial::BlockSpceialLogicTeam0::IsElectricObject)
				{
					BlockSocElectricDoor* pdoor = dynamic_cast<BlockSocElectricDoor*>(pmtl);
					if (pdoor)
					{
						pdoor->openDoor(pworld, pos);
					}
				}
			}
		}
	}
	return true;
}

void BlockOpenDoorMachine::onBlockPlacedBy(World* pworld, const WCoord& blockpos, IClientPlayer* player)
{
	if (player)
	{
		ClientPlayer* playerTmp = player->GetPlayer();
		if (!playerTmp) return;
		int blockdata = playerTmp->getCurPlaceDir();
		pworld->setBlockData(blockpos, blockdata, 3);
	}
}

//void BlockOpenDoorMachine::createCollideData(CollisionDetect* coldetect, World* pworld, const WCoord& blockpos)
//{
//}

//WorldContainer* BlockOpenDoorMachine::createContainer(World* pworld, const WCoord& blockpos)
//{
//	containerLinkMachine* container = SANDBOX_NEW(containerLinkMachine, blockpos, m_BlockResID, ElectricSource);
//	 
//	return container;
//}

//void BlockOpenDoorMachine::createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh)
//{
//	Super::createBlockMesh(data, blockpos, poutmesh);
//}
