#include "BlockScreen.h"
#include "BlockMaterialMgr.h"
#include "section.h"
#include "SectionMesh.h"
#include "BlockGeom.h"
#include "Collision.h"
#include "world.h"
#include "WorldProxy.h"
#include "chunk.h"
#include "block_tickmgr.h"
#include "EffectManager.h"
#include "special_blockid.h"
#include "VehicleWorld.h"
#include "ClientPlayer.h"
#include "ActorLocoMotion.h"

//#include "OgreMaterial.h"

#include "SandBoxManager.h"


IMPLEMENT_BLOCKMATERIAL(BlockScreen)
IMPLEMENT_BLOCKMATERIAL(BlockInkScreen)
using namespace MINIW;

BlockScreen::BlockScreen() : m_upperMtlIndex(UINT_MAX), m_upperWoodMtlIndex(UINT_MAX), m_lowerMtlIndex(UINT_MAX), m_ignoreCheckUpBlock(false)
{

}

BlockScreen::~BlockScreen()
{
	//ENG_RELEASE(m_UpperMtl);
//	ENG_RELEASE(m_UpperWoodMtl);
	//ENG_RELEASE(m_LowerWoodMtl);
}

void BlockScreen::init(int resid)
{
	ModelBlockMaterial::init(resid);

	

	char texname[256];
	sprintf(texname, "%s_screengrass", GetBlockDef()->Texture1.c_str());
	RenderBlockMaterial* mtl = NULL;
	//m_UpperMtl 
	mtl	= g_BlockMtlMgr.createRenderMaterial(texname, GetBlockDef(), GETTEX_WITHDEFAULT, getDrawType());
	m_upperMtlIndex = getRenderMtlMgr().addMtl(mtl);
	ENG_RELEASE(mtl);
	//m_UpperWoodMtl
	mtl = g_BlockMtlMgr.createRenderMaterial(GetBlockDef()->Texture1.c_str(), GetBlockDef(), GETTEX_WITHDEFAULT, BLOCKDRAW_OPAQUE);
	m_upperWoodMtlIndex = getRenderMtlMgr().addMtl(mtl);
	ENG_RELEASE(mtl);
	//m_LowerWoodMtl 
	mtl	= g_BlockMtlMgr.createRenderMaterial(GetBlockDef()->Texture1.c_str(), GetBlockDef(), GETTEX_WITHDEFAULT, BLOCKDRAW_OPAQUE);
	m_lowerMtlIndex = getRenderMtlMgr().addMtl(mtl);
	ENG_RELEASE(mtl);
}

void BlockScreen::initDrawType()
{
	m_blockDrawType = BLOCKDRAW_XPARENT;
}

void BlockScreen::initGeomName()
{
	m_geomName = m_Def->Texture1.c_str();
}

//const char *BlockScreen::getGeomName()
//{
//	return GetBlockDef()->Texture1.c_str();
//}
const char *BlockScreen::getBaseTexName(char *texname, const BlockDef *def, int &gettextype)
{
	gettextype = GETTEX_WITHDEFAULT;
	//sprintf(texname, "%s_lower", def->Texture1);
	sprintf(texname, "%s_screengrass", def->Texture1.c_str());
	return texname;
}

//upper: bit0: mirror,  updown(bit2: 1),  open(bit3)
//down:  dir(bit0-bit1),  updown(bit2: 0),  open(bit3)
int BlockScreen::ParseDoorData(const SectionDataHandler* sectionData, const WCoord &blockpos, bool &isupper, bool &isopen, bool &mirror)
{
	if (!sectionData)
	{
		return DIR_NEG_X;
	}
	int blockdata = sectionData->getBlock(blockpos).getData();
	isupper = (blockdata & 4) != 0;
	isopen = (blockdata & 8) != 0;

	int placedir;
	if (isupper)
	{
		mirror = (blockdata & 1) != 0;
		//int downdata = sectionData->getBlock(DownCoord(blockpos)).getData();
		int downdata = sectionData->getNeighborBlock(blockpos, g_DirectionCoord[DIR_NEG_Y]).getData();
		placedir = downdata & 3;
	}
	else
	{
		//int updata = sectionData->getBlock(TopCoord(blockpos)).getData();
		int updata = sectionData->getNeighborBlock(blockpos, g_DirectionCoord[DIR_POS_Y]).getData();
		mirror = (updata & 1) != 0;
		placedir = blockdata & 3;
	}

	return placedir;

}

int BlockScreen::ParseDoorDataInVehicle(VehicleWorld* pworld, const WCoord &blockpos, bool &isupper, bool &isopen, bool &mirror)
{
	int blockdata = pworld->getBlockData(blockpos);
	isupper = (blockdata & 4) != 0;
	isopen = (blockdata & 8) != 0;

	int placedir;
	if (isupper)
	{
		mirror = (blockdata & 1) != 0;
		int downdata = pworld->getNeighborBlock(blockpos, DIR_NEG_Y).getData();
		placedir = downdata & 3;
	}
	else
	{
		int updata = pworld->getNeighborBlock(blockpos, DIR_POS_Y).getData();
		mirror = (updata & 1) != 0;
		placedir = blockdata & 3;
	}

	return placedir;
}

int BlockScreen::ParseDoorData(World *pworld, const WCoord &blockpos, bool &isupper, bool &isopen, bool &mirror)
{
	VehicleWorld *pVehicleWorld = dynamic_cast<VehicleWorld*>(pworld);
	if (pVehicleWorld)
	{
		return ParseDoorDataInVehicle(pVehicleWorld, blockpos, isupper, isopen, mirror);
	}
	else
	{
		Section *psection = pworld->getSection(blockpos);
		return ParseDoorData(&psection->GetSharedSectionData(), blockpos - psection->m_Origin, isupper, isopen, mirror);
	}
}

void BlockScreen::createBlockMesh(const BuildSectionMeshData& data, const WCoord &blockpos, SectionMesh *poutmesh)
{
	BlockGeomTemplate* geom = getGeom(data.m_LODLevel);
	if (!geom)
		return;
	auto psection = data.m_SharedSectionData;
	bool isupper, isopen, mirror;
	int dir = ParseDoorData(psection, blockpos, isupper, isopen, mirror);

	Rainbow::ColorRGBAf verts_light[1] = { Rainbow::ColorRGBAf::black };
	RenderBlockMaterial *mtl;
	RenderBlockMaterial *mtlwood;
	BlockGeomMeshInfo meshinfo;
	BlockGeomMeshInfo meshinfograss;

	//if (isopen) mirror = !mirror;

	if (isupper)
	{
		mtl = getRenderMtlMgr().getMtl(m_upperMtlIndex);//m_UpperMtl;
		geom->getFaceVerts(meshinfo, 1, 1.0f, 0, dir, mirror);
	}
	else
	{
		mtl = getDefaultMtl();
		geom->getFaceVerts(meshinfo, 0, 1.0f, 0, dir, mirror);
	}

	SectionSubMesh *psubmesh = poutmesh->getSubMesh(mtl);

	psection->getBlockVertexLight(blockpos, verts_light);
	psubmesh->addGeomBlockLight(meshinfo, &blockpos, verts_light, NULL, mtl->getUVTile());


	if (isupper)
	{
		mtlwood = getRenderMtlMgr().getMtl(m_upperWoodMtlIndex);//m_UpperWoodMtl;
		geom->getFaceVerts(meshinfograss, 3, 1.0f, 0, dir, mirror);
	}
	else
	{
		mtlwood = getRenderMtlMgr().getMtl(m_lowerMtlIndex);//m_LowerWoodMtl;
		geom->getFaceVerts(meshinfograss, 2, 1.0f, 0, dir, mirror);
	}

	psubmesh = poutmesh->getSubMesh(mtlwood);

	psection->getBlockVertexLight(blockpos, verts_light);
	psubmesh->addGeomBlockLight(meshinfograss, &blockpos, verts_light, NULL, mtlwood->getUVTile());
}

SectionMesh *BlockScreen::createBlockProtoMesh(int protodata)
{
	SectionMesh* pmesh = ENG_NEW(SectionMesh)();
	SectionSubMesh* psubmesh = pmesh->getSubMesh(getRenderMtlMgr().getMtl(m_upperMtlIndex), true);

	BlockGeomMeshInfo meshinfo;
	WCoord center(0, BLOCK_SIZE * 0.75, 0);

	//pmesh->setSectionOffset(WCoord(0, 0, 0) + center);
	getGeom()->getFaceVerts(meshinfo, 1, 1.0f, 0);
	psubmesh->addTriangleList(meshinfo.vertices.data(), meshinfo.vertices.size(), meshinfo.indices.data(), meshinfo.indices.size(), &center);

	psubmesh = pmesh->getSubMesh(getRenderMtlMgr().getMtl(m_upperWoodMtlIndex), true);
	//pmesh->setSectionOffset(WCoord(0, 0, 0) + center);
	getGeom()->getFaceVerts(meshinfo, 3, 1.0f, 0);
	psubmesh->addTriangleList(meshinfo.vertices.data(), meshinfo.vertices.size(), meshinfo.indices.data(), meshinfo.indices.size(), &center);


	psubmesh = pmesh->getSubMesh(getDefaultMtl(), true);
	//pmesh->setSectionOffset(WCoord(0, -BLOCK_SIZE, 0) + center);
	getGeom()->getFaceVerts(meshinfo, 0, 1.0f, 0);
	WCoord pos(0, -BLOCK_SIZE, 0);
	pos += center;
	psubmesh->addTriangleList(meshinfo.vertices.data(), meshinfo.vertices.size(), meshinfo.indices.data(), meshinfo.indices.size(), &pos);


	psubmesh = pmesh->getSubMesh(getRenderMtlMgr().getMtl(m_lowerMtlIndex), true);
	//pmesh->setSectionOffset(WCoord(0, -BLOCK_SIZE, 0) + center);
	getGeom()->getFaceVerts(meshinfo, 2, 1.0f, 0);
	WCoord pos1(0, -BLOCK_SIZE, 0);
	pos1 += center;
	psubmesh->addTriangleList(meshinfo.vertices.data(), meshinfo.vertices.size(), meshinfo.indices.data(), meshinfo.indices.size(), &pos1);


	return pmesh;
}
void BlockScreen::createCollideData(CollisionDetect *coldetect, World *pworld, const WCoord &blockpos)
{
	bool isupper, isopen, mirror;
	int dir = ParseDoorData(pworld, blockpos, isupper, isopen, mirror);

	WCoord origin = blockpos * BLOCK_SIZE;
	int thick = BLOCK_SIZE / 16;

	if (dir == DIR_NEG_X || dir == DIR_POS_X)
		coldetect->addObstacle(origin + WCoord(7 * thick, 0, 0), origin + WCoord(9 * thick, BLOCK_SIZE, BLOCK_SIZE));
	else if (dir == DIR_NEG_Z || dir == DIR_POS_Z)
		coldetect->addObstacle(origin + WCoord(0, 0, 7 * thick), origin + WCoord(BLOCK_SIZE, BLOCK_SIZE, 9 * thick));                         
}

bool BlockScreen::canPutOntoPos(WorldProxy *pworld, const WCoord &blockpos)
{
	if (!pworld->doesBlockHaveSolidTopSurface(blockpos + WCoord(0, -1, 0))) return false;

	if (!BlockMaterial::canPutOntoPos(pworld, blockpos)) return false;
	if (!BlockMaterial::canPutOntoPos(pworld, blockpos + WCoord(0, 1, 0))) return false;
	return true;
}

//void BlockScreen::onPoweredBlockChange(World *pworld, const WCoord &blockpos, bool open)
//{
//	bool isupper, isopen, mirror;
//	int placedir = ParseDoorData(pworld, blockpos, isupper, isopen, mirror);
//
//	if (isopen != open)
//	{
//		pworld->setBlockData(blockpos, pworld->getBlockData(blockpos) ^ 8);
//
//		//上面或者下面的门联动
//		WCoord ng = blockpos + WCoord(0, isupper ? -1 : 1, 0);
//		pworld->setBlockData(ng, pworld->getBlockData(ng) ^ 8);
//	}
//}

//bool BlockScreen::onTrigger(World *pworld, const WCoord &blockpos, DirectionType face, IClientPlayer *player, const Rainbow::Vector3f &colpoint=Rainbow::Vector3f(0, 0, 0))
//{
//	if (pworld->isRemoteMode())
//	{
//		return true;
//	}
//
//	if (IsWoodDoorBlock(getBlockResID()))
//	{
//		bool isupper, isopen, mirror;
//		int placedir = ParseDoorData(pworld, blockpos, isupper, isopen, mirror);
//
//		pworld->setBlockData(blockpos, pworld->getBlockData(blockpos) ^ 8);
//
//		WCoord ng = blockpos + WCoord(0, isupper ? -1 : 1, 0);
//		pworld->setBlockData(ng, pworld->getBlockData(ng) ^ 8);
//
//		pworld->getEffectMgr()->playSound(BlockCenterCoord(blockpos), isopen ? "misc.door_close" : "misc.door_open", 1.0f, GenRandomFloat()*0.2f + 0.8f);
//	}
//
//	return true;
//}

void BlockScreen::onNotify(World *pworld, const WCoord &blockpos, int blockid)
{
	int blockdata = pworld->getBlockData(blockpos);
	bool isupper = (blockdata & 4) != 0;

	if (isupper)
	{
		if (pworld->getBlockID(blockpos + WCoord(0, -1, 0)) != getBlockResID())
		{
			pworld->setBlockAir(blockpos);
		}
		if (blockid > 0 && blockid != getBlockResID())
		{
			onNotify(pworld, blockpos + WCoord(0, -1, 0), blockid);
		}
	}
	else
	{
		bool toair = false;
		WCoord upcoord = blockpos + WCoord(0, 1, 0);
		if (pworld->getBlockID(upcoord) != getBlockResID() && !m_ignoreCheckUpBlock)
		{
			pworld->setBlockAir(blockpos);
			toair = true;
		}

		if (!pworld->doesBlockHaveSolidTopSurface(blockpos + WCoord(0, -1, 0)))
		{
			int bid = pworld->getBlockID(blockpos);
			int blockdata = pworld->getBlockData(blockpos);

			pworld->setBlockAir(blockpos);
			toair = true;

			pworld->notifyToRecycleBlock(SandBoxMgrEventID::EVENT_HOMELAND_DESTROY_NOTIFY_RECYCLE, 0, 0, bid, blockdata);

			if (pworld->getBlockID(upcoord) == getBlockResID())
			{
				pworld->setBlockAir(upcoord);
			}
		}

		if (toair)
		{
			dropBlockAsItem(pworld, blockpos, blockdata);
		}
		/*else
		{
			bool powered = pworld->isBlockIndirectlyGettingPowered(blockpos) || pworld->isBlockIndirectlyGettingPowered(upcoord);
			if (powered || g_BlockMtlMgr.getMaterial(blockid)->canProvidePower() && blockid != getBlockResID())
			{
				onPoweredBlockChange(pworld, blockpos, powered);
			}
		}*/
	}
}

void BlockScreen::dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata/* =0 */, BLOCK_MINE_TYPE droptype/* =BLOCK_MINE_NOTOOL */, float chance/* =1.0f */, int uin)
{
	bool isupper = (blockdata & 4) != 0;
	if (!isupper)
	{
		ModelBlockMaterial::dropBlockAsItem(pworld, blockpos, blockdata, droptype, chance);
	}
}

bool BlockScreen::canBlocksMovement(World *pworld, const WCoord &blockpos)
{
	int blockdata = pworld->getBlockData(blockpos);
	return (blockdata & 8) == 0;
}

bool BlockScreen::isOpen(int blockdata)
{
	return (blockdata & 8) != 0;
}

int BlockScreen::convertDataByRotate(int blockdata, int rotatetype)
{
	if ((blockdata & 4) != 0)
		return blockdata;

	return this->commonConvertDataByRotateWithBit(blockdata, rotatetype, 3, 12);
}

/**************************************************************************BlockInkScreen**************************************************************************************/
void BlockInkScreen::createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh)
{
	BlockGeomTemplate* geom = getGeom(data.m_LODLevel);
	if (!geom)
		return;

	auto psection = data.m_SharedSectionData;
	if (!psection)
	{
		return;
	}
	bool isupper, isopen, mirror;
	int blockData = psection->getBlock(blockpos).getData();
	isupper = (blockData & 8) != 0;
	if (isupper)
	{
		return;
	}
	int dir = blockData & 3;
	isopen = false;
	mirror = false;
	int meshIndex = (blockData & 4) ? 1 : 0;
	if (meshIndex == 1)
	{
		dir = (psection->getNeighborBlock(blockpos, WCoord(0, 1, 0)).getData()) & 3;
	}

	Rainbow::ColorRGBAf verts_light[1] = { Rainbow::ColorRGBAf::black };
	RenderBlockMaterial* mtl;
	BlockGeomMeshInfo meshinfo;
	BlockGeomMeshInfo meshinfograss;

	//if (isopen) mirror = !mirror;
	mtl = getDefaultMtl();//m_UpperMtl;
	geom->getFaceVerts(meshinfo, meshIndex, 1.0f, 0, dir, mirror);

	SectionSubMesh* psubmesh = poutmesh->getSubMesh(mtl);

	psection->getBlockVertexLight(blockpos, verts_light);
	psubmesh->addGeomBlockLight(meshinfo, &blockpos, verts_light, NULL, mtl->getUVTile());
}

bool BlockInkScreen::canPutOntoPos(WorldProxy* pworld, const WCoord& blockpos)
{
	if (!pworld->doesBlockHaveSolidTopSurface(blockpos + WCoord(0, -1, 0))) return false;

	if (!BlockMaterial::canPutOntoPos(pworld, blockpos)) return false;
	if (!BlockMaterial::canPutOntoPos(pworld, blockpos + WCoord(0, 1, 0))) return false;
	return true;
}

void BlockInkScreen::onNotify(World* pworld, const WCoord& blockpos, int blockid)
{
	int blockdata = pworld->getBlockData(blockpos);
	bool isupper = (blockdata & 8) != 0;

	if (isupper)
	{
		if (pworld->getBlockID(blockpos + WCoord(0, -1, 0)) != getBlockResID())
		{
			pworld->setBlockAir(blockpos);
		}
	}
	else
	{
		WCoord upcoord = blockpos + WCoord(0, 1, 0);
		if (pworld->getBlockID(upcoord) != getBlockResID())
		{
			pworld->setBlockAir(blockpos);
		}
	}
}

SectionMesh* BlockInkScreen::createBlockProtoMesh(int protodata)
{
	if (getGeom() == NULL) return NULL;
	SectionMesh* pmesh = ENG_NEW(SectionMesh)();
	SectionSubMesh* psubmesh = pmesh->getSubMesh(getGeomMtlProto(protodata), true);

	BlockGeomMeshInfo meshinfo;

	//int idbuf[32];
	//int dirbuf[32];
	//int ngeom = getProtoBlockGeomID(idbuf, dirbuf);
	//只画一个就行了
	int ngeom = 1;
	for (int i = 0; i < ngeom; i++)
	{
		getGeom()->getFaceVerts(meshinfo, 1, 1.0f, 0, DIR_NEG_X);

	/*	if (isColorableBlock() && !isUseCustomModel())
		{
			BlockColor bv = getBlockColor(protodata);
			psubmesh->addGeomFaceLight(meshinfo, NULL, s_DefaultFaceVertexLights, &bv);
		}
		else
		{
			psubmesh->addGeomFaceLight(meshinfo, NULL, s_DefaultFaceVertexLights, NULL);
		}*/
		//getGeom()->getFaceVerts(meshinfo, 0, 1.0f, 0);
		WCoord pos(0, 0, 0);
		//pos += center;
		psubmesh->addTriangleList(meshinfo.vertices.data(), meshinfo.vertices.size(), meshinfo.indices.data(), meshinfo.indices.size(), &pos);
	}

	return pmesh;
}

void BlockInkScreen::createCollideData(CollisionDetect* coldetect, World* pworld, const WCoord& blockpos)
{
	WCoord origin = blockpos * BLOCK_SIZE;
	coldetect->addObstacle(origin, origin + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
}