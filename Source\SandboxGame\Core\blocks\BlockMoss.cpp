
#include "BlockMoss.h"
#include "world.h"
#include "BlockMaterialMgr.h"
#include "special_blockid.h"
#include "BlockMaterial.h"
//#include "OgreMaterial.h"
#include "WorldProxy.h"
#include "CoreCommonDef.h"
#include "section.h"
#include "BlockGeom.h"
#include "SectionMesh.h"
#include "coreMisc.h"

using namespace MINIW;

IMPLEMENT_BLOCKMATERIAL(MossBlockMaterial)
IMPLEMENT_BLOCKMATERIAL(MossHugeBlockMaterial)
//IMPLEMENT_BLOCKINSTANCE(MossBlockMaterial)

MossBlockMaterial::MossBlockMaterial()
{
    for (int i = 0; i < 19; i++)
    {
        MossPic[i] = nullptr;
    }
}

MossBlockMaterial::~MossBlockMaterial()
{
    for (int i = 0; i < 19; i++)
    {
        ENG_RELEASE(MossPic[i]);
    }
}


bool MossBlockMaterial::canPutOntoFace(WorldProxy *pworld, const WCoord &blockpos, int face)
{
    return canPlacedOn(pworld->getBlockID(NeighborCoord(blockpos,face)));
}

bool MossBlockMaterial::canPlacedOn(int blockid)
{
    BlockMaterial *pmtl = g_BlockMtlMgr.getMaterial(blockid);
    if(IsGlassBlockID(blockid))//(blockid > 631 && blockid < 649)
        return true;
    return pmtl && pmtl->isOpaqueCube() && pmtl->defBlockMove();
}

bool MossBlockMaterial::canStay(World *pworld, const WCoord &blockpos)
{
    int dir = (pworld->getBlockData(blockpos))&7;
    int nid = pworld->getBlockID(NeighborCoord(blockpos, dir));
    return canPlacedOn(nid);
}

bool MossBlockMaterial::canPut(World* pworld, const WCoord& blockpos,int dir)
{
    int nid = pworld->getBlockID(NeighborCoord(blockpos, dir));
    return canPlacedOn(nid);
}

int MossBlockMaterial::getPlaceBlockData(World *pworld, const WCoord &blockpos, DirectionType face, float hitptx, float hitpty, float hitptz, int def_blockdata)
{
    return face;
}

void MossBlockMaterial::onNotify(World *pworld, const WCoord &blockpos, int blockid)
{
    if(pworld->onServer() && !canStay(pworld, blockpos))
    {
        dropBlockAsItem(pworld, blockpos);
        pworld->setBlockAir(blockpos);
    }
}

BlockTexElement *MossBlockMaterial::getDestroyTexture(Block pblock, BlockTexDesc &desc)
{
    desc.blendmode = BLEND_ALPHATEST;
    desc.gray = false;
    return getDefaultMtl()->getTexElement();
}

void MossBlockMaterial::initDefaultMtl()
{
    std::string tex = GetBlockDef()->Texture1.c_str();
    tex = tex + "3_1";
    auto mtl = g_BlockMtlMgr.createRenderMaterial(tex.c_str(), GetBlockDef(), GETTEX_WITHDEFAULT, BLOCKDRAW_ALPHTEST);
    //auto mtl = g_BlockMtlMgr.createRenderMaterial(GetBlockDef()->Texture1.c_str(), GetBlockDef(), GETTEX_WITHDEFAULT, BLOCKDRAW_GRASS);
    m_defaultMtlIndex = getRenderMtlMgr().addMtl(mtl);
    OGRE_RELEASE(mtl);

}
void MossBlockMaterial::init(int resid)
{
    Super::init(resid);

    m_nSpecialLogicType[0] |= BlockMaterial::BlockSpceialLogicTeam0::BlockFaceToPlane;
    m_nSpecialLogicType[0] |= BlockMaterial::BlockSpceialLogicTeam0::BranchNotLink;
    if (m_LoadOnlyLogic) return;

    std::string tex = GetBlockDef()->Texture1.c_str();
    for (int i = 0; i < 15; i++)
    {
        int level = 1 + i / 5;
        int dir = 1 + i % 5;
        char texname[256];
        snprintf(texname, sizeof(texname), "%s%d_%d", tex.c_str(),level,dir);
        MossPic[i]= g_BlockMtlMgr.createRenderMaterial(texname, GetBlockDef(), GETTEX_WITHDEFAULT, BLOCKDRAW_ALPHTEST);
    }
    for (int i = 15; i < 19; i++)
    {
        int level = i - 14;
        char texname[256];
        if (i >= 17)
        {
            sprintf(texname, "%s_3_%d", tex.c_str(),i-16);
        }
        else
        {
            sprintf(texname, "%s_%d", tex.c_str(), level);
        }
        
        MossPic[i] = g_BlockMtlMgr.createRenderMaterial(texname, GetBlockDef(), GETTEX_WITHDEFAULT, BLOCKDRAW_ALPHTEST);
    }
}
int MossBlockMaterial::convertDataByRotate(int blockdata, int rotatetype)
{
    int curDir = blockdata;

	curDir = this->commonConvertDataByRotate(curDir, rotatetype);

    return curDir;
}

void MossBlockMaterial::createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh)
{
	BlockGeomTemplate* geom = getGeom(data.m_LODLevel);
	if (geom == NULL) return;

    auto psection = data.m_SharedSectionData;
    BlockGeomMeshInfo meshinfo;
    int blockdata = psection->getBlock(blockpos).getData();
    int piecedir = getPieceDir(blockdata);
    int meshid = getPlacedFace(blockdata);
    SectionSubMesh* psubmesh  = NULL;
    RenderBlockMaterial* pmtl = NULL;
    if (meshid == DIR_POS_Y || meshid == DIR_NEG_Y)
    {
        psubmesh = poutmesh->getSubMesh(MossPic[0]);
        pmtl = MossPic[0];
    }
    else
    {
        psubmesh = poutmesh->getSubMesh(MossPic[15]);
        pmtl = MossPic[15];
    }
    if (pmtl == NULL)return;
    if (!geom->getFaceVerts(meshinfo, meshid, 1.0f, 0, piecedir)) return;

    Rainbow::ColorRGBAf verts_light[1] = { Rainbow::ColorRGBAf::white };
    psection->getBlockVertexLight(blockpos, verts_light);
    BlockColor rc = getRenderColor();
    psubmesh->addGeomBlockLight(meshinfo, &blockpos, verts_light, &rc, pmtl->getUVTile());

    if (meshid == DIR_POS_Y || meshid == DIR_NEG_Y)
    {
        for (int i=0;i<4;i++)
        {
            WCoord offset = WCoord(0, 0, 0);

            BlockGeomMeshInfo meshinfo_temp;
            if (meshid == DIR_POS_Y)
            {
                if (i == DIR_NEG_X) { offset = WCoord(0, 0, -1); }
                else if (i == DIR_POS_X) { offset = WCoord(1, 0, 0); }
                else if (i == DIR_NEG_Z) { offset = WCoord(0, 0, 1); }
                else if (i == DIR_POS_Z) { offset = WCoord(-1, 0, 0); }
            }
            else
            {
                if (i == DIR_NEG_X) { offset = WCoord(0, 0, 1); }
                else if (i == DIR_POS_X) { offset = WCoord(1, 0, 0); }
                else if (i == DIR_NEG_Z) { offset = WCoord(0, 0, -1); }
                else if (i == DIR_POS_Z) { offset = WCoord(-1, 0, 0); }
            }
            const WCoord posTemp = offset + WCoord(blockpos.x, blockpos.y, blockpos.z);
            if (data.m_World && canPut(data.m_World, posTemp+ psection->getOrigin(), meshid))
            {
                SectionSubMesh* psubmesh_temp = poutmesh->getSubMesh(MossPic[1 + i]);
                RenderBlockMaterial* pmtl_temp = MossPic[1 + i];
                if (pmtl_temp == NULL)continue;
                if (!geom->getFaceVerts(meshinfo_temp, meshid, 1.0f, 0, piecedir)) continue;
                BlockColor rc_temp = getRenderColor();
                psubmesh_temp->addGeomBlockLight(meshinfo_temp, &posTemp, verts_light, &rc_temp, pmtl_temp->getUVTile());
            }
        }
    }
    //psubmesh->m_FarHide = isFarHide();
}

void MossBlockMaterial::dropBlockAsItem(World* pworld, const WCoord& blockpos, int blockdata, BLOCK_MINE_TYPE droptype, float chance, int uin/* = -1 */)
{
    if (GenRandomInt(0, 100) > 50)
    {
        //策划说改成2020002
        doDropItem(pworld, blockpos, 2020002);//掉落植物纤维
    }
    BlockMaterial::dropBlockAsItem(pworld, blockpos, blockdata, droptype, chance, uin);
    
    
}

void MossHugeBlockMaterial::dropBlockAsItem(World* pworld, const WCoord& blockpos, int blockdata, BLOCK_MINE_TYPE droptype, float chance, int uin/* = -1 */)
{
    int level = blockdata & 8;
    if (level)
    {
        BlockMaterial::dropBlockAsItem(pworld, blockpos, blockdata, droptype, chance, uin);
    }
    BlockMaterial::dropBlockAsItem(pworld, blockpos, blockdata, droptype, chance, uin);
    BlockMaterial::dropBlockAsItem(pworld, blockpos, blockdata, droptype, chance, uin);
    if (GenRandomInt(0, 100) > 50)
    {
        //策划说改成2020002
        doDropItem(pworld, blockpos, 2020002);//掉落植物纤维
    }
    
}


void MossHugeBlockMaterial::createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh)
{
    BlockGeomTemplate* geom = getGeom(data.m_LODLevel);
    if (geom == NULL) return;
    auto psection = data.m_SharedSectionData;
    BlockGeomMeshInfo meshinfo;
    int blockdata = psection->getBlock(blockpos).getData();
    int piecedir = getPieceDir(blockdata);
    int meshid = getPlacedFace(blockdata);
    int level = ((blockdata & 8) ? 2 : 1) ;
    SectionSubMesh* psubmesh = NULL;
    RenderBlockMaterial* pmtl = NULL;
    if (meshid == DIR_POS_Y || meshid == DIR_NEG_Y)
    {
        psubmesh = poutmesh->getSubMesh(MossPic[( 5 * level )]);
        pmtl = MossPic[(5 * level)];
    }
    else
    {
        psubmesh = poutmesh->getSubMesh(MossPic[15 + level]);
        pmtl = MossPic[15];
    }
    if (pmtl == NULL)return;
    if (!geom->getFaceVerts(meshinfo, meshid, 1.0f, 0, piecedir)) return;

    Rainbow::ColorRGBAf verts_light[1] = { Rainbow::ColorRGBAf::white };
    psection->getBlockVertexLight(blockpos, verts_light);
    BlockColor rc = getRenderColor();
    psubmesh->addGeomBlockLight(meshinfo, &blockpos, verts_light, &rc, pmtl->getUVTile());

    if (meshid == DIR_POS_Y || meshid == DIR_NEG_Y)
    {
        for (int i = 0; i < 4; i++)
        {
            WCoord offset = WCoord(0, 0, 0);

            BlockGeomMeshInfo meshinfo_temp;
            if (meshid == DIR_POS_Y)
            {
                if (i == DIR_NEG_X) { offset = WCoord(0, 0, -1); }
                else if (i == DIR_POS_X) { offset = WCoord(1, 0, 0); }
                else if (i == DIR_NEG_Z) { offset = WCoord(0, 0, 1); }
                else if (i == DIR_POS_Z) { offset = WCoord(-1, 0, 0); }
            }
            else
            {
                if (i == DIR_NEG_X) { offset = WCoord(0, 0, 1); }
                else if (i == DIR_POS_X) { offset = WCoord(1, 0, 0); }
                else if (i == DIR_NEG_Z) { offset = WCoord(0, 0, -1); }
                else if (i == DIR_POS_Z) { offset = WCoord(-1, 0, 0); }
            }
            const WCoord posTemp = offset + WCoord(blockpos.x, blockpos.y, blockpos.z);
            if (data.m_World && canPut(data.m_World, posTemp+ psection->getOrigin(), meshid))
            {
                SectionSubMesh* psubmesh_temp = poutmesh->getSubMesh(MossPic[(5 * level) + 1 + i]);
                RenderBlockMaterial* pmtl_temp = MossPic[(5 * level) + 1 + i];
                if (pmtl_temp == NULL)continue;
                if (!geom->getFaceVerts(meshinfo_temp, meshid, 1.0f, 0, piecedir)) continue;
                BlockColor rc_temp = getRenderColor();
                psubmesh_temp->addGeomBlockLight(meshinfo_temp, &posTemp, verts_light, &rc_temp, pmtl_temp->getUVTile());
            }
        }
    }
    else if (level == 2)
    {
        WCoord offset = WCoord(0, -1, 0);
        BlockGeomMeshInfo meshinfo_temp;
        const WCoord posTemp = offset + WCoord(blockpos.x, blockpos.y, blockpos.z);
        if (data.m_World && canPut(data.m_World, posTemp + psection->getOrigin(), meshid))
        {
            SectionSubMesh* psubmesh_temp = poutmesh->getSubMesh(MossPic[18]);
            RenderBlockMaterial* pmtl_temp = MossPic[18];
            if (pmtl_temp == NULL)return;
            if (!geom->getFaceVerts(meshinfo_temp, meshid, 1.0f, 0, piecedir)) return;
            BlockColor rc_temp = getRenderColor();
            psubmesh_temp->addGeomBlockLight(meshinfo_temp, &posTemp, verts_light, &rc_temp, pmtl_temp->getUVTile());
        }
    }
    //psubmesh->m_FarHide = isFarHide();
}

int MossHugeBlockMaterial::convertDataByRotate(int blockdata, int rotatetype)
{
    int curDir = getPlacedFace(blockdata);;

    curDir = this->commonConvertDataByRotate(curDir, rotatetype);

    return (blockdata & 8) + curDir;
}