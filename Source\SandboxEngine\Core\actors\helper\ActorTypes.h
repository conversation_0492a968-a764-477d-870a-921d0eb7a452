#ifndef __ACTORTYPES_H__
#define __ACTORTYPES_H__

#include "Common/OgreWCoord.h"
//tolua_begin
enum MOB_TYPE
{
	MOB_HOSTILE = 0,	// 怪物
	MOB_PASSIVE,		// 动物
	MOB_RARE,			// 稀有动物
	MOB_WATER,			// 水生物
	MOB_MOBILETOOL,		// 移动工具
	MOB_VILLAGER,		// 村民（商人）	
	MOB_BOSS,			// 世界boss
	MOB_NPC,			// npc
	MOB_FLY,			// 飞行生物

	MOB_TRIXENIE = 11,	// 三栖生物
	MAX_MOBTYPE
};

enum EQUIP_SLOT_TYPE
{
	EQUIP_NONE = -1,
	EQUIP_HEAD = 0,//头
	EQUIP_BREAST,//上身
	EQUIP_LEGGING,//脚
	EQUIP_SHOE,//鞋
	EQUIP_HEAD_LINING,//头内衬
	EQUIP_BREAST_LINING,//上身内衬
	EQUIP_LEGGING_LINING,//脚内衬
	EQUIP_SHOE_LINING,//鞋内衬
	EQUIP_PIFENG,//披风(作为特殊物品装备)
	EQUIP_WEAPON,//武器
	EQUIP_GROUP,//组合装备
	MAX_EQUIP_SLOTS = EQUIP_GROUP
};

enum EQUIP_BACK_INDEX
{
	EQUIP_BACK_INDEX_NONE = -1,
	EQUIP_BACK_INDEX_0 = 0,
	EQUIP_BACK_INDEX_1 = 1,
	EQUIP_BACK_INDEX_2 = 2,
	EQUIP_BACK_INDEX_3 = 3,
	EQUIP_BACK_INDEX_4 = 4,
	EQUIP_BACK_INDEX_5 = 5,
	EQUIP_BACK_INDEX_6 = 6,
	EQUIP_BACK_INDEX_7 = 7,
	//EQUIP_BACK_INDEX_8 = 8,//特殊空位。当使用整体套装后没有用满8格装备栏是可以装背包的。这时候挂件会有9个。
	EQUIP_BACK_INDEXMAX
};

enum ATTACK_TYPE
{
	ATTACK_ALL = -1,

	ATTACK_PUNCH = 0, //近程
	ATTACK_RANGE = 1,     //远程
	ATTACK_EXPLODE = 2, //爆炸

	MAX_PHYSICS_ATTACK = 3,   //物理攻击

	ATTACK_FIRE = 3,  //火
	
	// 新增的两个 老的magic不用了 值先不变
	ATTACK_RADIATION = 4, //辐射
	ATTACK_TEMPERATURE = 5, //温度

    ATTACK_POISON = 4,  //毒
    ATTACK_WITHER = 5,  //凋零
	ATTACK_FLASH = 6,	//电
	ATTACK_ICE = 7,		//冰
	MAX_MAGIC_ATTACK = 8, //元素攻击

	ATTACK_SUN = MAX_MAGIC_ATTACK, //日晒
	ATTACK_FALLING, //掉下来
    ATTACK_ANVIL, //被砸中
    ATTACK_CACTUS, //仙人掌
    ATTACK_WALL, //窒息伤害
    ATTACK_DROWN, //溺水
	ATTACK_SUFFOCATE,//水下生物在空气中窒息
    ATTACK_ANTIINJURY,  //反伤
    ATTACK_BLOCK_LASER, //被激光伤害
	ATTACK_FIXED, //固定伤害
	PHYSICS_ATTACK, //物理攻击
	TRUE_DAMAGE,  //真实伤害,无视护甲
	ATTACK_THORNBALL, //刺球
	ANIMAL_KILL,	  //野兽杀死
	RADIATION_KILL,		//辐射死亡
	HUNGER_KILL,		//饿死
	THIRSTY_KILL,		//渴死
	HEAD_KILL,			//爆头
	FOOD_KILL,			//食物死亡
	THIS_KILL,			//自杀
	SOCFIRE_KILL,		//新火
	SOCICE_KILL,		//新冰
	SOCFLASH_KILL,		//触电死亡
};

enum ATTACK_TARGET_TYPE
{
	ATTACK_TARGET_ALL = -1,

	ATTACK_TARGET_PLAYER,		//玩家
	ATTACK_TARGET_ANIMAL,		//动物
	ATTACK_TARGET_SAVAGE,		//野人
	ATTACK_TARGET_UNDEAD,		//亡灵
	ATTACK_TARGET_ANCIENT,		//远古
	ATTACK_TARGET_MECHANICAL,	//机械
	ATTACK_TARGET_ALIEN,		//外星
	ATTACK_TARGET_AQUATIC,		//水生
	ATTACK_TARGET_FLYING,		//飞行
	ATTACK_TARGET_ZOMBIE,		//僵尸
	ATTACK_TARGET_OTHERS = 100,	//其它
};

enum ENCHANT_TYPE
{
	ENCHANT_NULL = 0,
	ENCHANT_SHARP,   //锋利
	ENCHANT_KNOCK,   //击退: 增加击退别人的格子数
	ENCHANT_KNOCKUP, //击飞: 增加往上高度
	ENCHANT_ROB,     //掠夺
	ENCHANT_TARGET_FIRE, //攻击时给目标点燃
	ENCHANT_MULTIATTACK,  //多重攻击
	ENCHANT_VAMPIRE,      //吸血
	ENCHANT_DURABLE,      //耐久

	ENCHANT_PROTECTION,   //保护
	ENCHANT_THORNS,       //荆棘
	ENCHANT_ATTACKER_BUFF, //被攻击时给攻击者附加状态
	ENCHANT_KNOCK_RESIST, //减少被击退距离
	ENCHANT_KNOCK_ATTACKER, //受到近身攻击击退攻击者

	ENCHANT_BOW_ATTACK,     //提高弓伤害
	ENCHANT_ARROW_FREE,     //不消耗箭

	ENCHANT_DIG_SPEED,      //采集效率
	ENCHANT_DIG_PROB,       //时运(矿物)
	ENCHANT_DIG_MAXNUM,     //时运(植物)
	ENCHANT_DIG_PRECISE,    //精准采集

	ENCHANT_ARROW_EXPLODE,   //爆炸箭
	ENCHANT_FALL_PROTECT,    //掉落保护附魔

	ENCHANT_FALL_SPEED,      //速降
	ENCHANT_CLIMB,           //爬墙

	ENCHANT_TARGET_BLEED,	//攻击时给目标加流血buff
	ENCHANT_LIGHTNING_CHAIN,	//闪电链

	ENCHANT_FISH,			// 钓鱼幸运
	ENCHANT_DURABLE_PROTECT,      //耐久为0不删除道具
	ENCHANT_TEMPDEF,	// 附魔温度抗性
	ENCHANT_TARGET_ICE,	// 冰霜攻击附魔
	
	/// <summary>新增附魔效果 begin renjie
	ENCHANT_PUNCH_INC, //近程伤害加成
	ENCHANT_DAMAGE_DEC, //所有伤害减免
	ENCHANT_RANGE_DAMAGE_DEC, //远程伤害减免
	ENCHANT_EXPLODE_DEC, //爆炸伤害减免
	ENCHANT_TOUGH_REC, //韧性恢复速度
	ENCHANT_JUMP_INC, //增加跳跃能力
	ENCHANT_TRANSFORM1, //概率使敌方变身
	ENCHANT_DURABLE_PROB, //耐久保护
	ENCHANT_FISHING_TIME_DEC, //减少钓鱼时间
	ENCHANT_SLOWDOWN_PROB, //概率迟缓
	ENCHANT_DIGGING_STRENGTH_DEC, //挖掘体力降低
	ENCHANT_FALL_DAMAGE, //落地造成范围伤害
	ENCHANT_STAR_INC, //星星经验获取加成
	ENCHANT_WEAPON_SKILL_CD_DEC, //武器技能冷却时间降低
	ENCHANT_COMBO_LAST_INC, //使连招的最后一击伤害提升
	ENCHANT_REPEL_RES, //击退抗性 
	ENCHANT_MONSTER_DAMAGE, //对怪物伤害加成
	ENCHANT_ADDBUFF, //概率给对象添加buff
	/// </summary>新增附魔效果 end

	MAX_ENCHANT_TYPE
};
enum MODATTRIB_TYPE
{
    MODATTR_MOVE_SPEED = 0, //移动速度
    MODATTR_SWIM_SPEED,     //游泳速度
    MODATTR_JUMP_SPEED,     //往上跳的速度

    MODATTR_ATTACK_PUNCH, //近程攻击别人时加成的百分比
    MODATTR_ATTACK_RANGE, //远程攻击别人时加成的百分比
    MODATTR_ATTACK_EXPLODE, //爆炸攻击别人时加成的百分比
    MODATTR_ATTACK_FIRE,    //火攻击别人时加成的百分比
    MODATTR_ATTACK_POISON,  //毒攻击别人时加成的百分比
    MODATTR_ATTACK_WITHER,  //凋零攻击别人时加成的百分比
    MODATTR_ATTACK_PLAYER,  //攻击玩家时加成的百分比
    MODATTR_ATTACK_UNDEAD,  //攻击亡灵时加成的百分比
    MODATTR_ATTACK_ANIMAL,  //攻击动物别人时加成的百分比
	MODATTR_ATTACK_ICE,    //冰攻击别人时加成的百分比

    MODATTR_DAMAGED_PUNCH, //被近程攻击时加成的百分比
    MODATTR_DAMAGED_RANGE, //被远程攻击时加成的百分比
    MODATTR_DAMAGED_EXPLODE, //被爆炸攻击时加成的百分比
    MODATTR_DAMAGED_FIRE,    //被火攻击时加成的百分比
    MODATTR_DAMAGED_POISON,  //被毒攻击时加成的百分比
    MODATTR_DAMAGED_WITHER,  //被凋零攻击时加成的百分比
    MODATTR_DAMAGED_FALLING, //高处掉下伤害加成的百分比

    MODATTR_ARMOR_PUNCH,    //近程护甲加点
    MODATTR_ARMOR_RANGE,    //远程护甲加点
    MODATTR_ARMOR_EXPLODE,  //爆炸护甲加点

    MODATTR_DAMAGE_ABSORB,  //吸收伤害
    MODATTR_CRITICAL_HIT,   //暴击伤害加成
    MODATTR_KNOCK,          //增加攻击别人的击退距离
    MODATTR_KNOCK_RESIST,   //减少击退距离
    MODATTR_KNOCK_RESIST_PROB, //击退概率抵抗值, 0.2表示有20%概率不被击退
    MODATTR_ACTOR_SCALE,                 //角色大小  0 - 1.0

	MAX_MOB_MODATTR,		// 可以废弃，只有反作用 限制了怪物属性扩充 liya

    MODATTR_DIG_SPEED = MAX_MOB_MODATTR, //挖掘速度
    MODATTR_LUCK_DIG,                    //挖掘幸运,  0.2表示增加20%掉落概率
    MODATTR_LUCK_KILLMOB,                //怪物掉落
    MODATTR_VIEW_BRIGHT,                 //视野亮度  0 - 1.0
    //MODATTR_ACTOR_SCALE,                 //角色大小  0 - 1.0
	MODATTR_OXYGEN_SUPPLY,  //氧气提供1:只能水底用， 100: 只能太空用， 101：都能用 10000:恢复氧气

	MOBATTR_DAMAGED_ZOMBIE,		// 被僵尸攻击受到伤害降低（百分比）
	MOBATTR_ATTACK_GUN,			// 持枪攻击伤害加成（百分比）

	MAX_PLAYER_MODATTR,
	MAX_MOD_ATTRIB = MAX_PLAYER_MODATTR
};
//tolua_end

/*
@deprecated
*/
//tolua_begin
enum STAMINA_METHOD
{
	STAMINA_WALK = 0,
	STAMINA_SPRINT,
	STAMINA_SWIM,
	STAMINA_JUMP,
	STAMINA_SPRINTJUMP,
	STAMINA_DESTROYBLOCK,
	STAMINA_ATTACK,
	STAMINA_HURT,
	STAMINA_ADDLIFE,

	MAX_STAMINA_METHOD
};

enum PLAYER_GENIUS_TYPE
{
	GENIUS_NONE = -1, //没有任何特长加成
	GENIUS_BASEATK_INC = 0,  //增加基础攻击

    GENIUS_PUNCHHURT_INC = 1, //增加近程攻击
    GENIUS_RANGEHURT_INC = 2, //增加远程攻击
    GENIUS_EXPLODEHURT_INC = 3, //增加爆炸攻击
    GENIUS_PHYSICSHURT_INC = 4, //增加所有物理攻击

    GENIUS_FIREHURT_INC = 5,   //增加火攻击
    GENIUS_POISONHURT_INC = 6, //增加毒攻击
    GENIUS_WITHERHURT_INC = 7,  //增加凋零攻击
    GENIUS_MAGICHURT_INC = 8,   //增加所有元素攻击

    GENIUS_PUNCHHURT_DEC = 9,   //减少近程伤害
    GENIUS_RANGEHURT_DEC = 10, //减少远程伤害
    GENIUS_EXPLODEHURT_DEC = 11, //减少爆炸伤害
    GENIUS_PHYSICSHURT_DEC = 12,  //减少所有物理伤害

    GENIUS_FIREHURT_DEC = 13,    //减少火伤害
    GENIUS_POISONHURT_DEC = 14,   //减少毒伤害
    GENIUS_WITHERHURT_DEC = 15,   //减少凋零伤害
    GENIUS_MAGICHURT_DEC = 16,     //减少所有元素伤害

    GENIUS_ROB_MOBDROP = 17,  //打怪掉落幸运
    GENIUS_DIGSPEED_INC = 18,  //增加挖掘速度
    GENIUS_MOVESPEED_INC = 19,   //减少移动速度
	GENIUS_TOOLDUR = 20, //增加工具耐久
	GENIUS_ARCHEOLOGY = 30, //考古学
	GENIUS_SURVIVE = 31, //野外求生
	GENIUS_SEARCH_JAR = 32, //意外发现
    GENIUS_FOREBODE = 33, //预感
    GENIUS_TWOJUMP = 34, //二段跳
};
enum ACTORBODY_EFFECT
{
	BODYFX_HURT = 0,
	BODYFX_FIRE,
	BODYFX_PORTAL,
	BODYFX_ACCUMFIRE,
	BODYFX_DRAGONFIRE,
	BODYFX_DRAGONSUMMON,
	BODYFX_TAME_SUCCEED,
	BODYFX_TAME_FAILED,
	BODYFX_TAME_FOOD,
	BODYFX_TAME_NOFOOD,
	BODYFX_AI_NEEDREEDS,
	BODYFX_FEAR,
	BODYFX_ROLECOLLECT,
	BODYFX_ROLEJUMP,
	BODYFX_DEADPROTECT,
	BODYFX_DRAGONDIE0,
	BODYFX_DRAGONDIE1,
	BODYFX_DRAGONDIE2,
	BODYFX_HORSE_FLY,
	BODYFX_DISAPPEAR,
	BODYFX_HORSE_BENTENG,
	BODYFX_DANCE,
	TOOLFX_JETPACK2,

	BODYFX_INTERACTION,
	HUDFX_HEADSHOT,
	HUDFX_NORMALSHOT,
	HUDFX_VEHICLESHOT,

	BODYFX_MILKING,
	BODYFX_AI_ANGRY,
	BODYFX_AI_SLEEP,
	BODYFX_TRANSPORT,
	BODYFX_FORBIDDEN,

	BODYFX_CONCEAL,//潜行
	BODYFX_WEAPON_FIRE,//野人猎手武器上的火
	BODYFX_DIZZY,//眩晕
	BODYFX_MAKETROUBLE,
	BODYFX_TRAINMOVE, //火车移动
	BODYFX_AI_HUNGRY,//饥饿

	BODYFX_BALL_CHARGE, //蓄力踢球的特效
	BODYFX_BALL_SHOOT_RELEASE, //射球瞬间的爆发特效

	BODYFX_ENCH_FALL, //附魔速降
	BASKETBALL_OBSTRUCT,//篮球阻挡时特效
    BODYFX_BASKETBALL_DRIBBLERUSH,//玩家持球冲刺特效
    BODYFX_BASKETBALL_CHARGE,	  //篮球蓄力投篮特效
	BASKETBALL_GRAB,	//	篮球抢断特效
	BODYFX_AI_STAND_SLEEP,//站着睡觉

	BODYFX_BOT_SUSPEND, //引导机器人 悬浮
	BODYFX_BOT_PROJECT, //引导机器人 扫描/投影
	BODYFX_BOT_FISSURE, //引导机器人 空间裂隙
	BODYFX_BOT_FISSURE_FAST, //引导机器人 空间裂隙(快速)
	BODYFX_BOT_HIGHLIGHT, //引导机器人 高亮
	BODYFX_BOT_TRAILING, //引导机器人 拖尾
	BODYFX_SCORPION_CONCEAL,
	BODYFX_MILKING_TOXIC,		//毒奶
	HUDFX_DEADSHOT,//射击死亡
	BODYFX_EXPLODE_HURT,//爆炸伤害
	BODYFX_MAX,			//所有的添加的枚举值只能够在这个枚举值上面
};
enum PLAYER_SPECTATOR_MODE
{
SPECTATOR_MODE_NONE,
SPECTATOR_MODE_FAILURE,
SPECTATOR_MODE_JUDGE
};
enum PLAYER_SPECTATOR_TYPE
{
SPECTATOR_TYPE_FREE,
SPECTATOR_TYPE_FOLLW,
SPECTATOR_TYPE_OUTCONTROL
};

// 部位
enum ATTACK_BODY_TYPE
{
	ATTACK_BODY_HEAD = 0,      // 头部
	ATTACK_BODY_UPBODY = 1,    // 上身
	ATTACK_BODY_DOWNBODY = 2,  // 下身

	ATTACK_BODY_MAX,
};

namespace MNSandbox
{
	inline ATTACK_BODY_TYPE getAttackBodyType(const std::string& partname)
	{
		if (partname == "head")
		{
			return ATTACK_BODY_HEAD;
		}
		else if (partname == "upbody")
		{
			return ATTACK_BODY_UPBODY;
		}
		else if (partname == "downbody")
		{
			return ATTACK_BODY_DOWNBODY;
		}
		return ATTACK_BODY_UPBODY;
	}
}

//tolua_end

class IClientPlayer;
class IClientActor;
//tolua_begin
struct OneAttackData
{
	ATTACK_TYPE atktype;
	ATTACK_BODY_TYPE parttype;
	float atkpoints;
	float enchant_atk;
	float buff_atk;
	bool critical;
	bool damage_armor;
	bool ignore_resist;
	float knockback;
	float knockup;
	int buffId;
	int buffLevel;
	int explodePos_x;
	int explodePos_y;
	int explodePos_z;
	float explodeSize;
	IClientPlayer *fromplayer;
	bool isAttackHead;  //攻击头部标记
	WCoord atkpos;  //攻击的位置，只要为了无攻击者时的击退的方向
	bool triggerhit;// 是否触发命中事件
	int touReduce;	// 削韧

	/*
	* 新伤害系统新增属性
	* 攻击类型可叠加，不同位表示不同攻击类型
	*	10		9		8		7			6		5		4			3		2			1
	*	空元素	元素冰	元素电	元素混乱	元素毒	元素火	无距离物理	爆炸	远程物理	近战物理
	* 攻击点数用数组表示不同攻击类型的攻击点数
	* 独立爆炸保护物理和元素伤害，需要新的攻击点数参数
	*	7			6			5			4				3			2			1
	*	空元素		爆炸冰		爆炸电		爆炸混乱		爆炸毒		爆炸火		爆炸物理
	* buff需要接入到新伤害系统，需要增加固定攻击力buff，不受到玩家成长属性加成
	*/
	int atkTypeNew;			// 新攻击类型
	float atkPointsNew[10];	// 新攻击点数
	float explodePoints[7];	// 独立爆炸攻击点数
	float damping;			// 衰减系数（范围伤害衰减，默认1）
	float charge;			// 蓄力系数（实际蓄力时间/满蓄力时间， 默认1） 
	IClientActor* directAttacker;	// 直接攻击者（用于记录直接攻击投掷物）
	bool isFixAtkPoint;		// 固定攻击力（伤害不受到玩家成长攻击力加成）
	float bowDamageIns;		// 弓箭、枪械伤害加成
	float skillDamageIns;	// 伤害倍率
	bool isNotInherit;		// 是否不继承
	float damageFactor;		// 伤害修正（对整体伤害修正的参数）
	bool ignoreTriggerEvent;// 是否触发伤害事件

	OneAttackData()
	{
		memset(this, 0, sizeof(OneAttackData));
		damping = 1.0f;
		charge = 1.0f;
		skillDamageIns = 1.0f;
		damageFactor = 1.0f;
		parttype = ATTACK_BODY_UPBODY;
	}
};

enum GSOUND_TYPE
{
	GSOUND_DIG = 0,
	GSOUND_DESTROY,
	GSOUND_PLACE,
	GSOUND_FALLGROUND,
	GSOUND_WALK,


	MAX_GSOUND_TYPE
};
enum STATUS_EFFECT_TYPE
{
	STATUS_EFFECT_DIGGINGSPEED		= 1002,//改变挖掘速度
	STATUS_EFFECT_JUMP				= 1004,//跳跃
	STATUS_EFFECT_HUNGERLOSS		= 1011,//饥饿流失速度
	STATUS_EFFECT_VIEWBRIGHTNESS	= 1009,//视野亮度
	STATUS_EFFECT_REPELSTRENGTHEN	= 1013,//击退强化
	STATUS_EFFECT_REPELRESIST		= 1014,//击退抵抗
	STATUS_EFFECT_ABSORBDAMAGE		= 1022,//吸收伤害
	STATUS_EFFECT_STRIKEFLY			= 1024,//攻击击飞

	STATUS_EFFECT_MODELVOLUME		= 1015,//模型体积
	STATUS_EFFECT_MODELCHANGE		= 1016,//改变模型
	STATUS_EFFECT_MOVEREVERSE		= 1017,//晕头转向
	STATUS_EFFECT_INVULNERABLE		= 1018,//无敌
	STATUS_EFFECT_BUBBLE			= 1019,//泡泡
	STATUS_EFFECT_CANNOTMOVE		= 1020,//无法移动
	STATUS_EFFECT_CLIMBWALL			= 1028,//爬墙

	STATUS_EFFECT_FORBIDRUN         = 1040,//禁止奔跑
	STATUS_EFFECT_DROP = 1051, // 意识丧失 掉落 飞行生物
	STATUS_EFFECT_PERCIPIENCE = 1052, // 意识丧失  无法移动
};
//tolua_end

#define SLOW_BUFF 8
#define VIEW_BRIGHT_BUFF 16
#define MOVEREVERSE_BUFF 38
#define PAOPAO_BUFF 42 
#define BOUND_BUFF 43
#define FREEZING_BUFF 46
#define FOOTBALLWAY_BUFF 70
#define EMPTY_CONTROL_BUFF 203

#define VACANT_SAFE_BUFF_LITTLE 204
#define VACANT_SAFE_BUFF_EX 205

#define BASKETBALLWAY_BUFF 72
#define REVIVE_RUN_FAST 998
#define INVULNERABLE_BUFF 999
#define REVIVE_SPEED_BUFF 1000
#define REVIVE_OBSERVER_BUFF 1001
#define BECOME_CHIKEN_BUFF 1003
#define BECOME_TUANZI_BUFF 1008
#define BECOME_MOUNTS_BUFF 1009
#define SOLARDISK_MOUNTS_BUFF 1013
#define BECOME_COW_BUFF 1004
#define BECOME_PIG_BUFF 1007
#define BECOME_SHEEP_BUFF 1005
#define BECOME_DEMON_BUFF 1006
#define CATCH_FIRE_BUFF 1009
#define FISHING_BUFFER 1024
#define START_FOODLEVEL 20
#define START_LIFE      20
#define START_OXYGEN    10
#define OXYGEN_BACK_BUFF 65
#define HOT_BUFF 73 //炎热buff
#define MAX_BAGS_GRID 64
#define FIRSTENTERGAME_BUFF 94
#define SWIMMY_BUFF 230		// 20210926：眩晕  codeby： keguanqiang
#define BUBBLE_SLEEP_BUFF 2000		// 20220603：泡泡睡眠  codeby： keguanqiang
#define DUSTSTORM_BUFF 280		// 20220704 沙尘暴  codeby： zhangyusong

#define NERVE_PARALYSIS_VIGILANCE_BUFF 1017		//神经麻痹  code-by:Logo
#define VIGILANCE_BUFF 1015		// 20220422：警觉  codeby： demonyan
#define LOSS_OF_CONSCIOUSNESS_BUFF 1019		// 意识丧失 20220608 shitengkai
#define SCORPION_VENUM_BASE_BUFF 1018
#define MAX_SCORPION_VENUM_BUFF_LEVEL 5
#define VIRULENT_BUFF 1016
#define SUDDEN_ILLNESS_BUFF 1021		//彼岸菠萝 突发恶疾buff
#define SCALD_BUFF 1026  //烫伤buff
#define FORZEN_BUFF 1033 //冻结buff
#define PUSHSNOWBALLWAY_BUFF 1060  //推雪球buff
#define TOUGHNESSBREAK_PLAYER_BUFF	1070 // 玩家破韧buff
#define TOUGHNESSBREAK_MONSTER_BUFF	1069 // 怪物破韧buff
#define LOCK_MOB_BUFF 110 //生物锁定buff
#define IN_SAFE_ZONE 4001
#define DANGER_PLAYER 4002
#define HOSTILITY_PLAYER 4003

inline unsigned int EncodeActorAttr(int v, int uin)
{
	if(v < 0) v = 0;
	unsigned int mask = uin*0x384d3 + 0x3a8212b5;
	return ((v^mask)&(0xffffffff>>1)) | (1<<31);
}

inline int DecodeActorAttr(unsigned int v, int uin)
{
	unsigned int mask = uin*0x384d3 + 0x3a8212b5;
	if(v&(1<<31)) return (v^mask)&(0xffffffff>>1);
	else return v;
}

inline unsigned int EncodeActorAttr(int v)
{
	if(v < 0) v = 0;
	return (v^0x385fd4ac)&(0xffffffff>>1);
}

inline int DecodeActorAttr(unsigned int v)
{
	return (v^0x385fd4ac)&(0xffffffff>>1);
}
//avatar部件类型枚举
enum AVATAR_PART_TYPE
{
	BODY,
	HEAD,
	FACE,
	FACE_ORNAMENT,
	JACKET,
	HAND_ORNAMENT,
	TROUSERS,
	SHOE,
	BACK_ORNAMENT,
	FOOTPRINT,
	SKIN,
	RIGHT_HAND,	//右手avt特效挂点
	RIGHT_SHOE,//右脚avt特效挂点

	HEAD_EFFECT = 13,
	FACE_EFFECT = 14,
	WHOLE_BODY_EFFECT = 15,
	HAND_EFFECT = 16,
	TRAILING_EFFECT = 17,
	BG_EFFECT = 18,

	// 装备相关avt部位扩展
	AVATAR_EQUIP_HEAD = 19,
	AVATAR_EQUIP_BREAST,
	AVATAR_EQUIP_LEGGING,
	AVATAR_EQUIP_SHOE,
	AVATAR_EQUIP_HEAD_LINING,
	AVATAR_EQUIP_BREAST_LINING,
	AVATAR_EQUIP_LEGGING_LINING,
	AVATAR_EQUIP_SHOE_LINING,
	AVATAR_EQUIP_PIFENG,
	AVATAR_EQUIP_WEAPON,
	MAX,
};
namespace MNSandbox
{
	inline int PlayerIndex2Model(int playerindex)
	{
		return playerindex & 0xf;
	}
	inline int PlayerIndex2Genius(int playerindex)
	{
		return (playerindex >> 4) & 0xf;
	}
	inline int PlayerIndex2Skin(int playerindex)
	{
		return (playerindex >> 8) & 0xffff;
	}
}

//tolua_begin
enum
{
	DEMAND_NOT,
	DEMAND_GIFT,
	DEMAND_HAIR_DYE,
	DEMAND_HAIR_CUT,
	DEMAND_TAMED,
	DEMAND_FOOD,
	DEMAND_TOOL,
};
//tolua_end
//tolua_begin
enum
{
	PROFESSION_NOT,
	PROFESSION_WOODCUTTER,			//樵夫
	PROFESSION_MELEE_GUARD,			//近战守卫
	PROFESSION_FARMER,				//农夫
	PROFESSION_REMOTE_GUARD,		//远程守卫
	PROFESSION_HELPER,				//助手
	PROFESSION_HUNTER,              //猎人 和比远程守卫多拾取的功能
	PROFESSION_ARCHITECT,           //建造师
};
//tolua_end
#endif
