
#include "BlockSimpleDesk.h"
#include "BlockMaterialMgr.h"
//#include "OgreMaterial.h"
#include "section.h"
#include "SectionMesh.h"
#include "BlockGeom.h"
#include "Collision.h"
#include "world.h"
#include "Environment.h"
#include "ClientActorManager.h"
#include "IClientPlayer.h"
#include "ChunkGenerator.h"
//#include "GameEvent.h"
#include "DefManagerProxy.h"
#include "VehicleWorld.h"
#include "ActorVehicleAssemble.h"
#include "OgreScriptLuaVM.h"

using namespace MINIW;

IMPLEMENT_BLOCKMATERIAL(BlockSimpleDesk)
inline bool IsBedHead(int blockdata)
{
	return (blockdata & 4) != 0;
}

void BlockSimpleDesk::initGeomName()
{
	m_geomName = m_Def->Texture2.c_str();
}
int BlockSimpleDesk::getBlockGeomID(int *idbuf, int *dirbuf, const SectionDataHandler* sectionData, const WCoord &blockpos, World* world)
{
	int blockdata = sectionData->getBlock(blockpos).getData();
	if (IsBedHead(blockdata)) idbuf[0] = 1;
	else idbuf[0] = 0;

	dirbuf[0] = blockdata & 3;
	return 1;
}

int BlockSimpleDesk::getProtoBlockGeomID(int *idbuf, int *dirbuf)
{
	idbuf[0] = 0;
	dirbuf[0] = DIR_NEG_Z;
	return 1;
}

//const char *BlockSimpleDesk::getGeomName()
//{
//	return GetBlockDef()->Texture2.c_str();
//}

bool BlockSimpleDesk::onTrigger(World *pworld, const WCoord &input_blockpos, DirectionType face, IClientPlayer *player, const Rainbow::Vector3f &colpoint)
{
	return true;
}

void BlockSimpleDesk::onNotify(World *pworld, const WCoord &blockpos, int blockid)
{
	int blockdata = pworld->getBlockData(blockpos);
	int ishead = blockdata & 4;
	int placedir = blockdata & 3;
	placedir = RotateDir90(placedir);
	if (ishead)
	{
		if (pworld->getBlockID(NeighborCoord(blockpos, ReverseDirection(placedir))) != getBlockResID())
		{
			pworld->setBlockAir(blockpos);
		}
	}
	else
	{
		if (pworld->getBlockID(NeighborCoord(blockpos, placedir)) != getBlockResID())
		{
			pworld->setBlockAir(blockpos);
			dropBlockAsItem(pworld, blockpos, blockdata);
		}
	}
}

void BlockSimpleDesk::dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata, BLOCK_MINE_TYPE droptype/* =BLOCK_MINE_NOTOOL */, float chance/* =1.0f */, int uin)
{
	int ishead = blockdata & 4;
	if(!ishead) ModelBlockMaterial::dropBlockAsItem(pworld, blockpos, blockdata, droptype, chance, uin);
}

void BlockSimpleDesk::createCollideData(CollisionDetect *coldetect, World *pworld, const WCoord &blockpos)
{
	coldetect->addObstacle(blockpos*BLOCK_SIZE, blockpos*BLOCK_SIZE+WCoord(BLOCK_SIZE,BLOCK_SIZE,BLOCK_SIZE));
}

int BlockSimpleDesk::convertDataByRotate(int blockdata, int rotatetype)
{
	int curDir = (blockdata & 3);
	if (curDir > DIR_POS_Z)
		return blockdata;


	return (blockdata & 12) + curDir;
}
SectionMesh *BlockSimpleDesk::createBlockProtoMesh(int protodata)
{
	SectionMesh* pmesh = ENG_NEW(SectionMesh)();
	SectionSubMesh* psubmesh = pmesh->getSubMesh(getDefaultMtl(), true);

	BlockGeomMeshInfo meshinfo;

	WCoord center(-BLOCK_SIZE / 2, 0, 0);

	getGeom()->getFaceVerts(meshinfo, 0);
	psubmesh->addTriangleList(meshinfo.vertices.data(), meshinfo.vertices.size(), meshinfo.indices.data(), meshinfo.indices.size(), &center);

	getGeom()->getFaceVerts(meshinfo, 1);
	WCoord pos(BLOCK_SIZE, 0, 0);
	pos += center;
	psubmesh->addTriangleList(meshinfo.vertices.data(), meshinfo.vertices.size(), meshinfo.indices.data(), meshinfo.indices.size(), &pos);

	return pmesh;
}

/////////////////////////////////////////////////////////////////////////////////////////

IMPLEMENT_BLOCKMATERIAL_INSTANCE_BEGIN(BlockSimpleDesk)
	IMPLEMENT_BLOCKMATERIAL_INSTANCE_R_PARAM(BlockSimpleDesk, R_Dir, int)(0, "Dir", "Block", &BlockSimpleDeskInstance::GetBlockDir, &BlockSimpleDeskInstance::SetBlockDir);
IMPLEMENT_BLOCKMATERIAL_INSTANCE_END()
