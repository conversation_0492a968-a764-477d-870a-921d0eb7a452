
#ifndef __BLOCK_SIMPLE_DESK_H__
#define __BLOCK_SIMPLE_DESK_H__

#include "BlockMaterial.h"

class BlockSimpleDesk : public ModelBlockMaterial //tolua_exports
{ //tolua_exports
	DECLARE_BLOCKMATERIAL(BlockSimpleDesk)
public:
	//tolua_begin
	//virtual const char *getGeomName() override;
	virtual bool onTrigger(World *pworld, const WCoord &blockpos, DirectionType face, IClientPlayer *player, const Rainbow::Vector3f &colpoint=Rainbow::Vector3f(0, 0, 0));
	virtual void onNotify(World *pworld, const WCoord &blockpos, int blockid);
	virtual int getProtoBlockGeomID(int *idbuf, int *dirbuf) override;
	virtual void dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata = 0, BLOCK_MINE_TYPE droptype = BLOCK_MINE_NOTOOL, float chance = 1.0f, int uin = -1);
	virtual void createCollideData(CollisionDetect *coldetect, World *pworld, const WCoord &blockpos);
	virtual int convertDataByRotate(int blockdata, int rotatetype) override;
	virtual SectionMesh *createBlockProtoMesh(int protodata = 0);
// 	virtual bool hasContainer();
	//tolua_end
private:
	virtual int getBlockGeomID(int *idbuf, int *dirbuf, const SectionDataHandler* sectionData, const WCoord &blockpos, World* world);
	virtual void initGeomName() override;


public:
	DECLARE_BLOCKMATERIAL_INSTANCE_BEGIN(BlockSimpleDesk)
		DECLARE_BLOCKMATERIAL_INSTANCE_R_PARAM(R_Dir, int)
	DECLARE_BLOCKMATERIAL_INSTANCE_END(BlockSimpleDesk)

}; //tolua_exports

#endif