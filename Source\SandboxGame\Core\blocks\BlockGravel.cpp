
#include "BlockGravel.h"
#include "OgreUtils.h"
#include "special_blockid.h"
#include "world.h"
#include "DefManagerProxy.h"
#include "block_tickmgr.h"
#include "ChunkGenerator.h"
#include "OgreTimer.h"
#include "ActorLocoMotion.h"
#include "BlockMaterialMgr.h"
#include "ClientItem.h"
#include "BlockMesh.h"
#include "BlockScene.h"
#include "special_blockid.h"
#include "WorldManager.h"
#include "ActorFallingGravel.h"
#include "WorldRender.h"
#include "IClientPlayer.h"
#include "ClientActorManager.h"


using namespace MINIW;

IMPLEMENT_BLOCKMATERIAL(BlockGravel)

BlockGravel::BlockGravel()
{ 
	m_TickCount = Rainbow::Timer::getSystemTick();
}

void BlockGravel::init(int resid)
{
	Super::init(resid);
	SetToggle(BlockToggle_RandomTick, false);

	m_tickInterval = 2;
}

void BlockGravel::dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata, BLOCK_MINE_TYPE droptype, float chance, int uin/* = -1 */)
{
	if(droptype == BLOCK_MINE_NONE) return;
	if(GenRandomFloat() > chance) return;

	int blockid = getBlockResID();
	const BlockDef *def = GetBlockDef();

		int itemid = 0;
		int r = std::rand()%10000;
		if(droptype == BLOCK_MINE_NOTOOL)
		{
			if(r < def->HandMineDrops.odds) itemid = def->HandMineDrops.item;
		}
		else if(droptype == BLOCK_MINE_PRECISE)
		{
			itemid = def->PreciseDrop;
		}
		else
		{
			int odds = def->ToolMineDrops[1].odds;
			if(BlockMaterial::m_DigLuckEnchant == 1) odds += 1000;
			else if(BlockMaterial::m_DigLuckEnchant == 2) odds += 2300;
			else if(BlockMaterial::m_DigLuckEnchant == 3) odds += 9000;

			if(r < odds)
			{
				itemid = def->ToolMineDrops[1].item;
			}
			else
			{
				itemid = def->ToolMineDrops[0].item;
			}
		}

		if(itemid > 0) doDropItem(pworld, blockpos, itemid);
}

bool BlockGravel::canFallBelow(World *pworld, const WCoord &blockpos)
{
	int blockid = pworld->getBlockID(blockpos);
	if (blockid == 0)
	{
		return true;
	}
	else if (blockid == BLOCK_FIRE)
	{
		return true;
	}
	else if (inTheFallingQueue(blockpos))
	{
		return true;
	}
	else
	{
		return GetDefManagerProxy()->getBlockDef(blockid)->MoveCollide == 2;
	}
}

bool BlockGravel::inTheFallingQueue(const WCoord &blockpos)
{
	for (int i = 0; i < (int)m_FallingBlockQueue.size(); ++i)
	{
		if (blockpos == m_FallingBlockQueue.at(i).first)
		{
			return true;
		}
	}

	return false;
}

void BlockGravel::onBlockAdded(World *pworld, const WCoord &blockpos)
{
	if (pworld->getStrongestIndirectPower(blockpos) > 0)
	{
		checkoutFall(pworld, blockpos);
		return;
	}

	// 当周围存在通电方块的情况下，立刻进行掉落判断
// 	if (pworld->getBlockID(blockpos.x + 1, blockpos.y, blockpos.z) != 0
// 		&& pworld->getBlockData(blockpos + WCoord(1, 0, 0)) > 0)
// 	{
// 		checkoutFall(pworld, blockpos);
// 		return;
// 	}
// 	if (pworld->getBlockID(blockpos.x - 1, blockpos.y, blockpos.z) != 0
// 		&& pworld->getBlockData(blockpos + WCoord(-1, 0, 0)) > 0)
// 	{
// 		checkoutFall(pworld, blockpos);
// 		return;
// 	}
// 	if (pworld->getBlockID(blockpos.x, blockpos.y, blockpos.z + 1) != 0
// 		&& pworld->getBlockData(blockpos + WCoord(0, 0, 1)) > 0)
// 	{
// 		checkoutFall(pworld, blockpos);
// 		return;
// 	}
// 	if (pworld->getBlockID(blockpos.x, blockpos.y, blockpos.z - 1) != 0
// 		&& pworld->getBlockData(blockpos + WCoord(0, 0, -1)) > 0)
// 	{
// 		checkoutFall(pworld, blockpos);
// 		return;
// 	}
// 	if (pworld->getBlockID(blockpos.x, blockpos.y + 1, blockpos.z) != 0
// 		&& pworld->getBlockData(blockpos + WCoord(0, 1, 0)) > 0)
// 	{
// 		checkoutFall(pworld, blockpos);
// 		return;
// 	}
// 	if (pworld->getBlockID(blockpos.x, blockpos.y - 1, blockpos.z) != 0
// 		&& pworld->getBlockData(blockpos + WCoord(0, -1, 0)) > 0)
// 	{
// 		checkoutFall(pworld, blockpos);
// 		return;
// 	}
}

void BlockGravel::onNotify(World *pworld, const WCoord &blockpos, int blockid)
{

	//pworld->getBlockTickMgr()->scheduleBlockUpdate(blockpos, getBlockResID(), tickRate());

	// 当通电情况下才进行掉落判断
	if (blockid > 0)
	{
// 		pworld->getBlockTickMgr()->scheduleBlockUpdate(blockpos, getBlockResID(), tickRate());
// 		checkoutFall(pworld, blockpos);

		if (pworld->getStrongestIndirectPower(blockpos) > 0)
		//if (pworld->isBlockIndirectlyGettingPowered(blockpos))
		{
			checkoutFall(pworld, blockpos);
			return;
		}

// 		if (pworld->getBlockID(blockpos.x, blockpos.y - 1, blockpos.z) == blockid)
// 		{
// 			checkoutFall(pworld, blockpos);
// 			return;
// 		}

// 		if (pworld->getBlockID(blockpos.x + 1, blockpos.y, blockpos.z) == blockid
// 			&& pworld->getBlockData(blockpos + WCoord(1,0,0)) > 0)
// 		{
// 			checkoutFall(pworld, blockpos);
// 			return;
// 		}
// 		if (pworld->getBlockID(blockpos.x - 1, blockpos.y, blockpos.z) == blockid
// 			&& pworld->getBlockData(blockpos + WCoord(-1, 0, 0)) > 0)
// 		{
// 			checkoutFall(pworld, blockpos);
// 			return;
// 		}
// 		if (pworld->getBlockID(blockpos.x, blockpos.y, blockpos.z + 1) == blockid
// 			&& pworld->getBlockData(blockpos + WCoord(0, 0, 1)) > 0)
// 		{
// 			checkoutFall(pworld, blockpos);
// 			return;
// 		}
// 		if (pworld->getBlockID(blockpos.x, blockpos.y, blockpos.z - 1) == blockid
// 			&& pworld->getBlockData(blockpos + WCoord(0, 0, -1)) > 0)
// 		{
// 			checkoutFall(pworld, blockpos);
// 			return;
// 		}
// 		if (pworld->getBlockID(blockpos.x, blockpos.y + 1, blockpos.z) == blockid
// 			&& pworld->getBlockData(blockpos + WCoord(0, 1, 0)) > 0)
// 		{
// 			checkoutFall(pworld, blockpos);
// 			return;
// 		}
// 		if (pworld->getBlockID(blockpos.x, blockpos.y - 1, blockpos.z) == blockid
// 			&& pworld->getBlockData(blockpos + WCoord(0, -1, 0)) > 0)
// 		{
// 			checkoutFall(pworld, blockpos);
// 			return;
// 		}
	}
}

void BlockGravel::blockTick(World *pworld, const WCoord &blockpos)
{
}

void BlockGravel::tickCS()
{
	if (m_FallingBlockQueue.size() <= 0)
	{
		return;
	}

	if (Rainbow::Timer::getSystemTick() - m_TickCount < 140)
	{
		return;
	}

	m_TickCount = Rainbow::Timer::getSystemTick();

	auto it = m_FallingBlockQueue.front();
	World *pworld = it.second;

	// 检查该方块周围同一水平面的方块是否可以掉落
	if (pworld->getBlockID(it.first.x + 1, it.first.y, it.first.z) == BLOCK_GRAVEL)
	{
		checkoutFall(pworld, WCoord(it.first.x + 1, it.first.y, it.first.z));
	}
	if (pworld->getBlockID(it.first.x - 1, it.first.y, it.first.z) == BLOCK_GRAVEL)
	{
		checkoutFall(pworld, WCoord(it.first.x - 1, it.first.y, it.first.z));
	}
	if (pworld->getBlockID(it.first.x, it.first.y, it.first.z + 1) == BLOCK_GRAVEL)
	{
		checkoutFall(pworld, WCoord(it.first.x, it.first.y, it.first.z + 1));
	}
	if (pworld->getBlockID(it.first.x, it.first.y, it.first.z - 1) == BLOCK_GRAVEL)
	{
		checkoutFall(pworld, WCoord(it.first.x, it.first.y, it.first.z - 1));
	}
	if (pworld->getBlockID(it.first.x, it.first.y + 1, it.first.z) == BLOCK_GRAVEL)
	{
		checkoutFall(pworld, WCoord(it.first.x, it.first.y + 1, it.first.z));
	}

	m_FallingBlockQueue.pop_front();
	tryToFall(pworld, it.first);
}

void BlockGravel::update(unsigned int dtick)
{
	tickCS();
}

void BlockGravel::checkoutFall(World *pworld, const WCoord &blockpos)
{
	if (canFallBelow(pworld, DownCoord(blockpos)) && blockpos.y >= 0)
	{
		if (!inTheFallingQueue(blockpos))
		{
			if (m_FallingBlockQueue.size() == 0)
			{
				m_TickCount = Rainbow::Timer::getSystemTick();
			}

			m_FallingBlockQueue.push_back(std::make_pair(blockpos, pworld));
		}
		//pworld->getBlockTickMgr()->scheduleBlockUpdate(blockpos, m_BlockResID, tickRate());
	}
}

void BlockGravel::tryToFall(World *pworld, const WCoord &blockpos)
{
	if (canFallBelow(pworld, DownCoord(blockpos)) && blockpos.y >= 0)
	{
		int range = 32;

		if (pworld->checkChunksExist(blockpos - range, blockpos + range))
		{
			if (pworld->onServer())
			{
				if (m_BlockResID == BLOCK_GRAVEL)// ActorFallingGravel 这种物理方块添加强制BLOCK_GRAVEL类型绑定 插件互相引用会导致m_BlockResID不是BLOCK_GRAVEL
				{
					ActorFallingGravel *actor = SANDBOX_NEW(ActorFallingGravel, pworld, blockpos, m_BlockResID, pworld->getBlockData(blockpos));
					onStartFalling(actor);
					pworld->getActorMgr()->spawnActor(actor);
				}
			}
		}
	}
}

void BlockGravel::onActorWalking(World *pworld, const WCoord &blockpos, IClientActor *actor)
{
	IClientPlayer *player = dynamic_cast<IClientPlayer*>(actor);

	if (player)
	{
		checkoutFall(pworld, blockpos);
	}
}

void BlockGravel::onBlockDestroyedBy(World *pworld, const WCoord &blockpos, int blockdata, BLOCK_DESTROY_REASON_T destroytype, IClientActor *actor)
{
// 	IClientPlayer *player = dynamic_cast<IClientPlayer*>(actor);
// 
// 	if (player)
// 	{
// 		// 判断周围的方块是否可以掉落
// 		if (pworld->getBlockID(blockpos.x, blockpos.y + 1, blockpos.z) != 0)
// 		{
// 			checkoutFall(pworld, blockpos + WCoord(0, 1, 0));
// 		}
// 	}
}

