
#include "BlockArrowSigns.h"
#include "special_blockid.h"
#include "IClientPlayer.h"
#include "section.h"
#include "world.h"
#include "container_arrowsigns.h"
#include "Math/Quaternionf.h"
#include "ActorVehicleAssemble.h"
#include "ClientInfoProxy.h"
#include "DefManagerProxy.h"
#include "WorldStringManagerProxy.h"
#include "VehicleWorld.h"
#include "IBackpack.h"

/*
	Block说明：
	独立路牌方块模型占2个Block高度，所以创建了2个路牌方块（低位down和高位up block），上面的方块模型隐藏了，保留了Container
	独立路牌存在2种形态：
		1. 一个路牌（低位up block），使用自己Container 来展示文字
		2. 两个路牌（第二个路牌模型是靠下的，但是用的是高位up block的Container来展示文字）
*/
using namespace Rainbow;
IMPLEMENT_BLOCKMATERIAL(BlockArrowSigns)
//IMPLEMENT_BLOCKINSTANCE(BlockArrowSigns)
bool BlockArrowSigns::isFreestand(int resid)
{
	return resid == BLOCK_SIGNS_ARROW;
}

void BlockArrowSigns::init(int resid)
{
	ModelBlockMaterial::init(resid);

	SetToggle(BlockToggle_HasContainer, true);
	m_FreeStanding = isFreestand(resid);
}

void BlockArrowSigns::initGeomName()
{
	m_geomName = "arrownotice";

	if (m_Def && !m_Def->Texture2.empty()) {
		m_geomName = m_Def->Texture2.c_str();
	}
}

void BlockArrowSigns::onBlockPlacedBy(World *pworld, const WCoord &blockpos, IClientPlayer *player)
{
	WorldArrowSignsContainer *container = dynamic_cast<WorldArrowSignsContainer *>(pworld->getContainerMgr()->getContainer(blockpos));

	//广告牌有两种, 一种是独立的, 一种是贴附的
	if(m_FreeStanding)
	{
		Block pblock = pworld->getBlock(blockpos);
		if (container)
		{
			
			if (!isUpBlock(pblock.getData())) 
			{
				//只存储下方方块朝向，第二路牌触发时候再根据角色朝向用上方方块存储第二个路牌朝向
				pworld->setBlockData(blockpos, pblock.getData() | ReverseDirection(player->GetPlayerCurPlaceDir()), 2);
			}

			container->applyBlockDir();
		}
	}

	//设置放置人的uin, 只有放置的人可以再次编辑留言板
	if (container)
	{
		container->m_OwnerUin = player->getUin();
	}
}

int BlockArrowSigns::getPlaceBlockData(World *pworld, const WCoord &blockpos, DirectionType face, float hitptx, float hitpty, float hitptz, int def_blockdata)
{
	if(!m_FreeStanding)
	{
		if(face==DIR_NEG_Y || face==DIR_POS_Y) return DIR_NEG_X;
		else return face;
	}
	else
	{
		Block pblock = pworld->getBlock(DownCoord(blockpos));
		
		if (pblock.getResID() == BLOCK_SIGNS_ARROW && !isUpBlock(pblock.getData()))
		{
			return 4 | face;// 下面有路牌方块，并且不是路牌顶部方块，那自己就是顶部方块
		}
	}
	return 0;
}

WorldContainer *BlockArrowSigns::createContainer(World *pworld, const WCoord &blockpos)
{
	WorldArrowSignsContainer*c = SANDBOX_NEW(WorldArrowSignsContainer, blockpos);
	c->init();
	/*
	int r = 0;
	int g = 0;
	int b = 0;
	MINIW::ScriptVM::game()->callFunction("BlockArrowSigns_GetArrowFontColor", ">iii", &r, &g, &b);
	*/
	c->setFontColor(255, 255, 255);
	return c;
}

int BlockArrowSigns::getBlockGeomID(int* idbuf, int* dirbuf, const SectionDataHandler* sectionData, const WCoord& blockpos, World* world)
{
	int blockdata = sectionData->getBlock(blockpos).getData();
	int occupyBlockData = sectionData->getNeighborBlock(blockpos, DIR_POS_Y).getData();
	if (m_FreeStanding)
	{
		idbuf[0] = 0;
		dirbuf[0] = blockdata & 3;

		if (blockdata & 8)
		{
			idbuf[1] = 1;
			dirbuf[1] = (occupyBlockData & 3);

			return 2;
		}
	}
	else
	{
		idbuf[0] = 2;
		dirbuf[0] = blockdata;
	}

	return 1;
}

//佔位方塊
bool BlockArrowSigns::isUpBlock(int blockdata)
{
	return (blockdata & 4) == 4;
}

bool BlockArrowSigns::onTrigger(World *pworld, const WCoord &blockpos, DirectionType face, IClientPlayer *player, const Rainbow::Vector3f &colpoint)
{
	Block pblock = pworld->getBlock(blockpos);
	bool bUpBlock = isUpBlock(pblock.getData());

	WCoord upBlockPos = bUpBlock ? blockpos : TopCoord(blockpos);
	WCoord downBlockPos = bUpBlock ? DownCoord(blockpos) : blockpos;

	Block upBlock = pworld->getBlock(upBlockPos);
	Block downBlock = pworld->getBlock(downBlockPos);

	bool active = downBlock.getData() & 8;
	//有2个方块，下面方块存储单路牌朝向，双路牌开关，上面的方块模型不展示，blockdata存储了第二个路牌的朝向（第一个箭头模型下方的子路牌模型）
	if (player && player->getCurToolID() == getBlockResID())
	{
		if (!active)
		{
			pworld->setBlockData(downBlockPos, downBlock.getData() | 8, 2);
			pworld->setBlockData(upBlockPos, upBlock.getData() | ReverseDirection(face), 2);
			player->shortcutItemUsed(); //需要消耗一个路牌
			
			WorldArrowSignsContainer* container = dynamic_cast<WorldArrowSignsContainer*>(pworld->getContainerMgr()->getContainer(upBlockPos));
			if (container )
			{
				container->applyBlockDir();
			}
		}

		return true;
	}

	if(pworld->getContainerMgr() == NULL) return false;
	//打开编辑的路牌
	WCoord triggerBlockPos = blockpos;
	if (active) //2个路牌
	{
		if (bUpBlock)
		{
			triggerBlockPos = colpoint.y < 0.5f ? upBlockPos : downBlockPos; //上方方块的Container存储的文字是第二个（靠下的）路牌
		}
		else
		{
			triggerBlockPos = upBlockPos;
		}
	}
	else
	{
		triggerBlockPos = downBlockPos;
	}

	WorldArrowSignsContainer *container = dynamic_cast<WorldArrowSignsContainer *>(pworld->getContainerMgr()->getContainer(triggerBlockPos));
	if(container)
	{
		BackPackGrid* itemdata = player->getIBackPack()->index2Grid(player->getCurShortcut() + player->getIBackPack()->getShortcutStartIndex());
		int itemid = itemdata->getItemID(); //背包道具ID
		if(!pworld->isRemoteMode() && (itemid == 0 || itemid != BLOCK_SIGNS_ARROW) )
		{
			player->openContainer(container);
		}

		return true;
	}

	return false;
}

void BlockArrowSigns::onBlockRemoved(World* pworld, const WCoord& blockpos, int blockid, int blockdata)
{
	if (pworld && pworld->getContainerMgr()) //方块破坏前移除掉审核文本
	{
		WorldArrowSignsContainer* container = dynamic_cast<WorldArrowSignsContainer*>(pworld->getContainerMgr()->getContainer(blockpos));
		if (container)
		{
			container->removeReviewText(blockpos);
		}
	}

	if (isUpBlock(blockdata))
	{
		pworld->setBlockAir(DownCoord(blockpos));
	}
	else
	{
		pworld->setBlockAir(TopCoord(blockpos));
	}
}

bool BlockArrowSigns::computeTextXform(Rainbow::WorldPos &pos, Rainbow::Quaternionf &rot, World *pworld, VehicleWorld *vehicleWorld, const WCoord &blockpos)
{
	int blockid = 0;
	int blockdata = 0;
	if (vehicleWorld)
	{
		blockid = vehicleWorld->getBlockID(blockpos);
		blockdata = vehicleWorld->getBlockData(blockpos);
	}
	else
	{
		blockid = pworld->getBlockID(blockpos);
		blockdata = pworld->getBlockData(blockpos);
	}

	bool bFreestand = isFreestand(blockid);
	int i = blockdata & 3;

	float angle = 0.0f;
	int xOffset = 0;
	int zOffset = 0;
	int yOffset = 0;
	bool isUpBlock = (blockdata & 4) == 4;
	MINIW::ScriptVM::game()->callFunction("BlockArrowSigns_LoadArrowSignsCfg", "ibb>iiif", i, bFreestand, isUpBlock, &xOffset, &yOffset, &zOffset, &angle);

	WCoord offset(xOffset, yOffset, zOffset);

	rot = AxisAngleToQuaternionf(Rainbow::Vector3f(0.0f, 1.0f, 0.0f), Deg2Rad(angle));
	pos = (BlockBottomCenter(blockpos) + offset).toWorldPos();

	if (vehicleWorld)
	{
		ActorVehicleAssemble *assemble = vehicleWorld->getActorVehicleAssemble();
		if (assemble)
		{
			WCoord pos_ = pos;
			pos_ -= WCoord(BLOCK_SIZE/2, 0, BLOCK_SIZE/2);
			pos = assemble->convertWcoord(Rainbow::Vector3f((float)pos_.x, (float)pos_.y, (float)pos_.z), true).toWorldPos();
			// 新引擎的四元数乘法需要交换顺序
			rot = assemble->convertRot(blockpos) * rot;
		}
	}

	return bFreestand;
}

bool BlockArrowSigns::canAttachedToMecha(int curblockdata, DirectionType dir, int neighbor_id, int neighbor_data)
{
	if(m_FreeStanding)
	{
		return dir == DIR_NEG_Y;
	}
	else
	{
		return (curblockdata & 7) == dir;
	}
}

int BlockArrowSigns::getProtoBlockGeomID(int *idbuf, int *dirbuf)
{
	idbuf[0] = 0;
	dirbuf[0] = 0;

	return 1;
}

void BlockArrowSigns::dropBlockAsItem(World* pworld, const WCoord& blockpos, int blockdata, BLOCK_MINE_TYPE droptype, float chance, int uin/* = -1 */)
{
	BlockSigns::dropBlockAsItem(pworld, blockpos, blockdata, droptype, chance, uin);

	int active = false;
	
	WCoord downBlockPos = isUpBlock(blockdata) ? DownCoord(blockpos) : blockpos;
	Block downBlock = pworld->getBlock(downBlockPos);

	if (downBlock.getData() & 8)
	{
		BlockSigns::dropBlockAsItem(pworld, blockpos, blockdata, droptype, chance, uin);
	}
}

void BlockArrowSigns::createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh)
{
	auto psection = data.m_SharedSectionData;
	Block pblock = psection->getBlock(blockpos);
	
	if (isUpBlock(pblock.getData()))
	{
		return;
	}

	BlockSigns::createBlockMesh(data, blockpos, poutmesh);
}

int BlockArrowSigns::convertDataByRotate(int blockdata, int rotatetype)
{
	if (m_FreeStanding)
	{
		return blockdata;
	}
	
	return ModelBlockMaterial::convertDataByRotate(blockdata, rotatetype);
}

void BlockArrowSigns::initDrawType()
{
	m_blockDrawType = BLOCKDRAW_OPAQUE;
}

void BlockArrowSigns::onNotify(World* pworld, const WCoord& blockpos, int blockid)
{
	if (m_Def == NULL || m_FreeStanding)
	{
		return;
	}

	// 依附路牌的背靠方块被破坏了则一起被破坏
	int blockdata = pworld->getBlockData(blockpos);
	if (blockdata > 0)
	{
		int dir = blockdata & 3;
		if (pworld->getBlockID(NeighborCoord(blockpos, dir)) == 0)
		{
			dropBlockAsItem(pworld, blockpos);
			pworld->setBlockAir(blockpos);
		}
	}
}

/////////////////////////////////////////////////////////////////////////////////////////

IMPLEMENT_BLOCKMATERIAL_INSTANCE_BEGIN(BlockArrowSigns)
	IMPLEMENT_BLOCKMATERIAL_INSTANCE_R_PARAM(BlockArrowSigns, R_Dir, int)(0, "Dir", "Block", &BlockArrowSignsInstance::GetBlockDir, &BlockArrowSignsInstance::SetBlockData);
	IMPLEMENT_BLOCKMATERIAL_INSTANCE_R_PARAM(BlockArrowSigns, R_Text, std::string)(1, "Text", "Block", &BlockArrowSignsInstance::GetText, &BlockArrowSignsInstance::SetText);
IMPLEMENT_BLOCKMATERIAL_INSTANCE_END()

void BlockArrowSignsInstance::SetText(const std::string& text)
{
	WorldArrowSignsContainer* container = dynamic_cast<WorldArrowSignsContainer*>(m_Container);
	if (container)
		container->setText(text.c_str());
}

std::string BlockArrowSignsInstance::GetText() const
{
	WorldArrowSignsContainer* container = dynamic_cast<WorldArrowSignsContainer*>(m_Container);
	if (container)
		return container->getText();
	return "";
}



