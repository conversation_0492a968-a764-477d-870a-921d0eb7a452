#pragma once

#include "TerritoryBoundingBox.h"
#include "Math/Vector3f.h"
#include <vector>
#include <memory>

class TerritoryContainer;

/**
 * 领地八叉树节点
 * 用于高效的空间分割和查询
 */
struct TerritoryOctreeNode
{
    // 节点边界
    TerritoryBoundingBox bounds;
    
    // 子节点（8个，如果是叶子节点则为空）
    std::vector<std::unique_ptr<TerritoryOctreeNode>> children;
    
    // 该节点包含的领地列表
    std::vector<TerritoryContainer*> territories;
    
    // 最大深度和每个节点最大领地数量
    static const int MAX_DEPTH = 6;
    static const int MAX_TERRITORIES_PER_NODE = 4;
    
    TerritoryOctreeNode(const TerritoryBoundingBox& nodeBounds);
    ~TerritoryOctreeNode() = default;
    
    // 插入领地
    void Insert(TerritoryContainer* territory, int depth = 0);
    
    // 移除领地
    bool Remove(TerritoryContainer* territory);
    
    // 查询点是否在任何领地内
    std::vector<TerritoryContainer*> QueryPoint(const Rainbow::Vector3f& point) const;
    
    // 查询区域内的所有领地
    std::vector<TerritoryContainer*> QueryRegion(const TerritoryBoundingBox& region) const;
    
    // 获取包含指定点的所有领地（用于玩家权限检查）
    std::vector<TerritoryContainer*> GetTerritoriesContaining(const Rainbow::Vector3f& point) const;
    
private:
    // 判断是否应该分裂
    bool ShouldSubdivide(int depth) const;
    
    // 创建子节点
    void Subdivide();
    
    // 获取点应该属于哪个子节点
    int GetChildIndex(const Rainbow::Vector3f& point) const;
    
    // 检查领地是否与节点边界相交
    bool IntersectsTerritory(TerritoryContainer* territory) const;
};

/**
 * 领地八叉树管理器
 * 用于高效的领地空间查询
 */
class TerritoryOctree
{
public:
    TerritoryOctree(const TerritoryBoundingBox& worldBounds);
    ~TerritoryOctree() = default;
    
    // 添加领地到八叉树
    void AddTerritory(TerritoryContainer* territory);
    
    // 从八叉树中移除领地
    void RemoveTerritory(TerritoryContainer* territory);
    
    // 更新领地位置（先移除再添加）
    void UpdateTerritory(TerritoryContainer* territory);
    
    // 查询点是否在任何领地内
    bool IsPointInAnyTerritory(const Rainbow::Vector3f& point) const;
    
    // 获取包含指定点的所有领地
    std::vector<TerritoryContainer*> GetTerritoriesContaining(const Rainbow::Vector3f& point) const;
    
    // 检查玩家是否在自己或被授权的领地内
    bool IsPlayerInAuthorizedTerritory(const Rainbow::Vector3f& point, unsigned int playerUin) const;
    
    // 查询区域内的所有领地（用于范围查询）
    std::vector<TerritoryContainer*> QueryRegion(const TerritoryBoundingBox& region) const;
    
    // 清空八叉树
    void Clear();
    
    // 获取统计信息
    struct Stats
    {
        int totalNodes = 0;
        int totalTerritories = 0;
        int maxDepth = 0;
        float avgTerritoriesPerLeaf = 0.0f;
    };
    Stats GetStats() const;
    
private:
    std::unique_ptr<TerritoryOctreeNode> m_root;
    TerritoryBoundingBox m_worldBounds;
    
    // 递归统计信息收集
    void CollectStats(const TerritoryOctreeNode* node, Stats& stats, int depth) const;
};

