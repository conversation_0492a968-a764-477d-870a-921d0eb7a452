
#ifndef __BLOCKBUILDBLUEPRINT_H__
#define __BLOCKBUILDBLUEPRINT_H__

#include "BlockBasic.h"

class BlockBuildBluePrint : public ModelBlockMaterial //tolua_exports
{ //tolua_exports
	DECLARE_BLOCKMATERIAL(BlockBuildBluePrint)
public:
	//tolua_begin
	BlockBuildBluePrint();
	virtual ~BlockBuildBluePrint();
	virtual void init(int resid) override;
	virtual void onBlockPlacedBy(World *pworld, const WCoord &blockpos, IClientPlayer *player);
	virtual void onBlockAdded(World *pworld, const WCoord &blockpos) override;
	virtual void onBlockRemoved(World *pworld, const WCoord &blockpos, int blockid, int blockdata) override;
	virtual void createBlockMesh(const BuildSectionMeshData& data, const WCoord &blockpos, SectionMesh *poutmesh) override;

	virtual bool onTrigger(World *pworld, const WCoord &blockpos, DirectionType face, IClientPlayer *player, const Rainbow::Vector3f &colpoint=Rainbow::Vector3f(0, 0, 0)) override;
	virtual void onNotify(World *pworld, const WCoord &blockpos, int blockid);
	virtual void dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata = 0, BLOCK_MINE_TYPE droptype = BLOCK_MINE_NOTOOL, float chance = 1.0f, int uin = -1);
	/*virtual BLOCK_RENDERTYPE_T getRenderType() override
	{
		return BLOCKRENDER_MODEL;
	}*/
	virtual bool canDestroy(World *pworld, const WCoord &blockpos);
	//tolua_end
protected:
	//RenderBlockMaterial *m_SideLineMtl;
	//RenderBlockMaterial *m_WorkLineMtl;
}; //tolua_exports

#endif
