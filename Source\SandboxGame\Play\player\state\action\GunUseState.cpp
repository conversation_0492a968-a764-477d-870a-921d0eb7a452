#include "GunUseState.h"
#include "PlayerControl.h"
#include "InputInfo.h"
#include "PlayerStateController.h"
#include "DefManagerProxy.h"
#include "CameraModel.h"
#include "world.h"
#include "EffectManager.h"
#include "PlayerAnimation.h"
#include "ActorBody.h"

#include "OgreScriptLuaVM.h"
#include "OgreTimer.h"
#include "GunUseComponent.h"
#include "backpack.h"
#include "PCControl.h"

#include "SandboxCoreDriver.h"
#include "SandboxResult.h"
#include "SoundComponent.h"
#include "WorldManager.h"
using namespace MNSandbox;


GunUseState::GunUseState(PlayerControl* host) : PlayerState(host), m_GunCoolDown(0), m_GunDef(nullptr),
                                                m_FireOnceAnimDuration(0),
                                                m_IdleStateReady(false)
{
	m_StateID = "GunUse";
	m_ReloadStartMark = -1;
	m_FireStartMark = -1;
	m_PullingStartMark = -1;
	m_bHavePulled = false;
}

GunUseState::~GunUseState()
{

}

void GunUseState::doBeforeEntering()
{
	m_GunDef = GetDefManagerProxy()->getGunDef(m_Host->getCurToolID());
	if (!m_GunDef)
	{
		return;
	}
	m_CurToolID = m_Host->getCurToolID();
	m_CurShortcut = m_Host->getCurShortcut();
	//Auto reload
	if (m_GunDef->NeedBullet != 2 
		&& (m_Host->m_InputInfo->reload || m_Host->getGunLogical()->getMagazine() == 0))
	{
		bulletReload();
	}
	//狙击枪射击间隔时间
	if (m_GunDef->ContinuousFire == 2 && m_Host->getGunLogical()->getPulledGunId() != m_CurToolID)
	{
		m_PullingStartMark = Rainbow::Timer::getSystemTick();
		m_bHavePulled = false;
		m_Host->getGunLogical()->setIsReload(true);
		return ;
	}
}

std::string GunUseState::update(float dtime)
{
	if(m_CurToolID != m_Host->getCurToolID() || m_CurShortcut != m_Host->getCurShortcut() || m_Host->isDead())
	{
		m_Host->setOperate(PLAYEROP_NULL);
		m_Host->getGunLogical()->setIsFire(false);
		m_Host->m_PCCtrl->ResetKey(Rainbow::SDLK_LBUTTON);
		return "ToActionIdle";
	}

	//Reload 期间不处理射击逻辑
	if (m_ReloadStartMark > 0)
	{
		// 换弹时间超时后，再执行真实换弹操作
		float reloadTime = 1;
		if (Rainbow::Timer::getSystemTick() - m_ReloadStartMark > reloadTime)
		{
			if (GetWorldManagerPtr()->isGodMode())
			{
				m_Host->getGunLogical()->doReload();
			}
			else
			{
				int bulletCount = m_Host->getBackPack()->getItemCountInNormalPack(m_GunDef->GetCostItemId());
				//看数量是否够
				int maxMagazines = m_Host->getGunLogical()->getMaxMagazines();
				// SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("WeaponSkin_System_magazines", SandboxContext(nullptr)
				// 	.SetData_Number("itemid", m_GunDef->ID)
				// 	.SetData_Number("uin", m_Host ? m_Host->getUin() : 0)
				// );
				// if (result.IsExecSuccessed())
				// {
				// 	maxMagazines = maxMagazines + (int)result.GetData_Number("nvalue");
				// }
				//不可以补充完整一枪子弹,就全部补充掉
				if (0 != m_GunDef->NeedBullet && bulletCount < maxMagazines - m_Host->getGunLogical()->getMagazine())
				{
					m_Host->doReload(m_GunDef->GetCostItemId(), bulletCount);
					//m_Host->getGunLogical()->doReload(m_Host->getGunLogical()->getMagazine() + bulletCount);
					//m_Host->getBackPack()->removeItemInNormalPack(m_GunDef->BulletID, bulletCount);
				}
				//可以补充完整一枪子弹
				else if (0 == m_GunDef->NeedBullet || bulletCount >= maxMagazines - m_Host->getGunLogical()->getMagazine())
				{
					int dif = maxMagazines - m_Host->getGunLogical()->getMagazine();
					m_Host->doReload(m_GunDef->GetCostItemId(), dif);

				//	m_Host->getGunLogical()->doReload(m_GunDef->Magazines);
				//	m_Host->getBackPack()->removeItemInNormalPack(m_GunDef->BulletID, dif);
				}
			}

			m_Host->m_PlayerAnimation->performIdle();
			m_Host->getGunLogical()->setIsReload(false);
			m_ReloadStartMark = -1;
			return "ToActionIdle";
		}
		return "";
	}

	//修改为持枪时打完子弹自动填充
	//Auto reload
	if (m_GunDef->NeedBullet != 2
		&& (m_Host->getGunLogical()->getMagazine() == 0))
	{
		if (bulletReload())
		{
			return "";
		}
		else
		{
			if(m_Host && m_Host->getGunLogical()){
				m_Host->getGunLogical()->setZoom(false);
			}
			return "ToActionIdle";
		}
	}


	if(m_PullingStartMark > 0)
	{
		if ((Rainbow::Timer::getSystemTick() - m_PullingStartMark > m_GunDef->ManualDelayTime) && !m_bHavePulled)
		{
			m_Host->m_PlayerAnimation->performPullingGun();
			m_Host->getGunLogical()->setIsReload(true);
			m_bHavePulled = true;
			auto soundComp = m_Host->getSoundComponent();
			if (soundComp)
			{
				soundComp->playSound(GetDefManagerProxy()->getGunDef(m_Host->getCurToolID())->ManualSound, 1.0, 1.0f + (GenRandomFloat() - GenRandomFloat())*0.4f);
			}
		}

		if (Rainbow::Timer::getSystemTick() - m_PullingStartMark > m_GunDef->ManualTime)
		{
			m_ReloadStartMark = -1;
			m_Host->getGunLogical()->setPulledGunId(m_GunDef->ID);
			m_Host->m_PlayerAnimation->performIdle();
			m_Host->getGunLogical()->doReload(0);
			return "ToActionIdle";
		}

		return "";
	}

	if (IsLeftClick()/*m_Host->m_InputInfo->leftClick*/ || IsUseAction()/*m_Host->m_InputInfo->useAction*/)
	{
		if (Rainbow::Timer::getSystemTick() - m_FireStartMark > (unsigned int)m_GunDef->FireInterval)
		{
			// 不能连发
			if (m_FireStartMark != -1
				&& m_GunDef->ContinuousFire != 1)
			{
				m_Host->m_PlayerAnimation->performIdle();
				return "ToActionIdle";
			}

			m_IdleStateReady = false;
			m_FireStartMark = Rainbow::Timer::getSystemTick();

			if (m_Host->getGunLogical()->fireOnce())
			{
				m_Host->m_PlayerAnimation->performFireGun();
				auto soundComp = m_Host->getSoundComponent();
				if (soundComp)
				{
					soundComp->playSound(GetDefManagerProxy()->getGunDef(m_Host->getCurToolID())->ShootSound, 1.0, 1.0f + (GenRandomFloat() - GenRandomFloat())*0.4f);
				}
			}
			else
			{
				auto soundComp = m_Host->getSoundComponent();
				if (soundComp)
				{
					soundComp->playSound(GetDefManagerProxy()->getGunDef(m_Host->getCurToolID())->EmptyShootSound, 1.0, 1.0f + (GenRandomFloat() - GenRandomFloat())*0.4f);
				}
			}

			if (m_GunDef->ContinuousFire == 2)
			{
				m_PullingStartMark = Rainbow::Timer::getSystemTick();
				m_Host->getGunLogical()->setPulledGunId(-1);
				m_bHavePulled = false;
				return "";
			}
		}
	}
	else if (IsRightClick()/*m_Host->m_InputInfo->rightClick*/)
	{
		int itemid = m_Host->getCurToolID();
		const ItemDef *def = GetDefManagerProxy()->getItemDef(itemid);

		if (def->UseTarget == ITEM_USE_GUN)
		{
			if(m_Host && m_Host->getGunLogical()){
				m_Host->getGunLogical()->setZoom(!m_Host->getGunLogical()->getZoom());
				m_Host->m_PCCtrl->ResetKey(Rainbow::SDLK_RBUTTON);
			}
			return "";
		}
	}
	else
	{
  		if (!m_IdleStateReady && Rainbow::Timer::getSystemTick() - m_FireStartMark > 500)
  		{
  			m_IdleStateReady = true;
  			m_Host->m_PlayerAnimation->performIdle();
  		}
		if (Rainbow::Timer::getSystemTick() - m_FireStartMark > (unsigned int)m_GunDef->FireInterval)
		{
			m_Host->m_PlayerAnimation->performIdle();
			return "ToActionIdle";
		}
	}

	/*if (Rainbow::Timer::getSystemTick() - m_UseStartMark > m_GunCoolDown)
	{
		const ItemDef *itemdef = GetDefManagerProxy()->getItemDef(m_Host->getCurToolID());

		//判断当前拿的东西是不是能够use的
		if (itemdef == NULL || (!(itemdef->UseScript[0]) && itemdef->UseTarget != ITEM_USE_GUN))
		{
			m_Host->m_StateController->setActionTransition(ToActionIdle);
			return;
		}

		//判断点击的块是否有行为
		int itemid = m_Host->getCurToolID();
		m_Host->m_CurMouseX = m_Host->m_InputInfo->clickPosX;
		m_Host->m_CurMouseY = m_Host->m_InputInfo->clickPosY;

		bool pickliquid = itemid > 0 && GetDefManagerProxy()->getItemDef(itemid)->UseTarget == ITEM_USE_CLICKLIQUID;
		int picktype = m_Host->doPick(pickliquid);

		m_Host->useItem(, PLAYEROP_STATUS_BEGIN);
		if (m_Host->getViewMode() == 0)
		{
			m_Host->m_PlayerAnimation->performFireGun();
		}

		m_GunCoolDown = itemdef->CoolDown;
		if (m_UseCoolDown == 0)
		{
			m_UseCoolDown = 500;
		}
		m_UseStartMark = Rainbow::Timer::getSystemTick();
	}*/

	return "";
}

void GunUseState::doBeforeLeaving()
{
	if(m_Host && m_Host->getGunLogical()) m_Host->getGunLogical()->setIsFire(false);
	m_ReloadStartMark = -1;
	m_FireStartMark = -1;
	m_PullingStartMark = -1;
}

bool GunUseState::bulletReload()
{
	//创造模式直接reload
	if (GetWorldManagerPtr()->isGodMode() || m_GunDef->NeedBullet == 0)
	{
		m_ReloadStartMark = Rainbow::Timer::getSystemTick();
		m_Host->m_PlayerAnimation->performReloadGun();
		m_Host->getGunLogical()->setIsReload(true);
		auto soundComp = m_Host->getSoundComponent();
		if (soundComp)
		{
			soundComp->playSound(m_GunDef->ReloadSound, 1.0, 1.0f + (GenRandomFloat() - GenRandomFloat())*0.4f);
		}
		return true;
	}
	else
	{
		int bulletCount = m_Host->getBackPack()->getItemCountInNormalPack(m_GunDef->GetCostItemId());
		int maxMagazines = m_Host->getGunLogical()->getMaxMagazines();
		// SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("WeaponSkin_System_magazines", SandboxContext(nullptr)
		// 	.SetData_Number("itemid", m_GunDef->ID)
		// 	.SetData_Number("uin", m_Host ? m_Host->getUin() : 0)
		// );
		// if (result.IsExecSuccessed())
		// {
		// 	maxMagazines = maxMagazines + (int)result.GetData_Number("nvalue");
		// }

		//看数量是否够 
		//如果一颗子弹都没有了
		if (bulletCount == 0)
		{
			return false;
		}
		//不可以补充完整一枪子弹,就全部补充掉
		else if (bulletCount < maxMagazines - m_Host->getGunLogical()->getMagazine())
		{
			m_ReloadStartMark = Rainbow::Timer::getSystemTick();
			m_Host->m_PlayerAnimation->performReloadGun();
			m_Host->getGunLogical()->setIsReload(true);
			auto soundComp = m_Host->getSoundComponent();
			if (soundComp)
			{
				soundComp->playSound(m_GunDef->ReloadSound, 1.0, 1.0f + (GenRandomFloat() - GenRandomFloat())*0.4f);
			}
			return true;
		}
		//可以补充完整一枪子弹
		else if (bulletCount >= maxMagazines - m_Host->getGunLogical()->getMagazine())
		{
			m_ReloadStartMark = Rainbow::Timer::getSystemTick();
			m_Host->m_PlayerAnimation->performReloadGun();
			m_Host->getGunLogical()->setIsReload(true);
			auto soundComp = m_Host->getSoundComponent();
			if (soundComp)
			{
				soundComp->playSound(m_GunDef->ReloadSound, 1.0, 1.0f + (GenRandomFloat() - GenRandomFloat())*0.4f);
			}
			return true;
		}
	}
	//m_Host->m_StateController->setActionTransition(ToActionIdle);
	return false;
}
