
#include "BlockSocMachineSource.h"
#include "world.h"
#include "container_sandboxGame.h"
#include "section.h"
#include "SectionMesh.h"
#include "ClientPlayer.h"

IMPLEMENT_BLOCKMATERIAL(BlockSocMachineSource);

using namespace MINIW;

BlockSocMachineSource::BlockSocMachineSource()
{

}

BlockSocMachineSource::~BlockSocMachineSource()
{

}

void  BlockSocMachineSource::init(int resid)
{
	ModelBlockMaterial::init(resid);
	SetToggle(BlockToggle_HasContainer, true);
	if (BlockMaterial::m_LoadOnlyLogic) return;
}

bool BlockSocMachineSource::onTrigger(World* pworld, const WCoord& blockpos, DirectionType face, IClientPlayer* player, const Rainbow::Vector3f& colpoint)
{
	if (!player || !pworld)
	{
		return false;
	}

	if (pworld->isRemoteMode())
	{
		return true;
	}
	auto toolsid = player->getCurToolID();
	if (toolsid == 2020123)
	{
		containerLinkMachine* pcontainer = dynamic_cast<containerLinkMachine*>(pworld->getContainerMgr()->getContainer(blockpos));
		if (pcontainer)
		{
			pcontainer->activating();
		}
	}
	return true;
}

void BlockSocMachineSource::createCollideData(CollisionDetect* coldetect, World* pworld, const WCoord& blockpos)
{
	WCoord origin = blockpos * BLOCK_SIZE;
	coldetect->addObstacle(origin, origin + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
}

WorldContainer* BlockSocMachineSource::createContainer(World* pworld, const WCoord& blockpos)
{
	containerLinkMachine* container = SANDBOX_NEW(containerLinkMachine, blockpos, m_BlockResID, ElectricSource);
	 
	return container;
}

void BlockSocMachineSource::createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh)
{
	Super::createBlockMesh(data, blockpos, poutmesh);
}

void BlockSocMachineSource::onBlockPlacedBy(World* pworld, const WCoord& blockpos, IClientPlayer* player)
{
	if (player)
	{
		ClientPlayer* playerTmp = player->GetPlayer();
		if (!playerTmp) return;
		int blockdata = playerTmp->getCurPlaceDir();
		pworld->setBlockData(blockpos, blockdata, 3);
	}
}