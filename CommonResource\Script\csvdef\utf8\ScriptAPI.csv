接口ID,类型ID,类型名,类型备注,函数名-不用翻译,函数标题-不用翻译,描述1参数及类型,描述2返回值及类型-不用翻译,描述3该方法的主要作用,描述4具体使用案例如下-不用翻译
ID,Type,TypeName,,FunctionName,FunctionDesc,Desc1,Desc2,Desc3,Desc4
10001,1,游戏管理,Game,doGameEnd,Game:doGameEnd(),nil,ErrorCode.OK,结束比赛,local result = Game:doGameEnd()
10002,1,游戏管理,Game,setScriptVar,"Game:setScriptVar(index, val)","index:number从1开始索引, val:number自定义的值",ErrorCode.OK,设置脚本参数，供自定义使用,"local result = Game:setScriptVar(index, val)"
10003,1,游戏管理,Game,getScriptVar,Game:getScriptVar(index),index:number从1开始索引,"ErrorCode.OK, val:number自定义的值",获取脚本参数，自定义使用,local result = Game:getScriptVar(index)
10004,1,游戏管理,Game,sendScriptVars2Client,Game:sendScriptVars2Client(),nil,ErrorCode.OK,上传设置好的脚本参数,local result = Game:sendScriptVars2Client()
10005,1,游戏管理,Game,addRenderGlobalEffect,Game:addRenderGlobalEffect(path),path:string,"ErrorCode.OK, effectid:number",新增全局效果,local result = Game:addRenderGlobalEffect(path)
10006,1,游戏管理,Game,removeRenderGlobalEffect,Game:removeRenderGlobalEffect(effectid),effectid:number,ErrorCode.OK,移除全局效果,local result = Game:removeRenderGlobalEffect(effectid)
10007,1,游戏管理,Game,setRenderGlobalEffectPos,"Game:setRenderGlobalEffectPos(effectid, x, y, z)","effectid:number, x|y|z:number",ErrorCode.OK,设置全局效果位置,"local result = Game:setRenderGlobalEffectPos(effectid, x, y, z)"
10008,1,游戏管理,Game,setRenderGlobalEffectScale,"Game:setRenderGlobalEffectScale(effectid, scalex, scaley, scalez)","effectid:number, scalex|scaley|scalez:number",ErrorCode.OK,设置全局效果缩放,"local result = Game:setRenderGlobalEffectScale(effectid, scalex, scaley, scalez)"
10009,1,游戏管理,Game,msgBox,Game:msgBox(msg),,,messagebox,local result = Game:msgBox(msg)
20001,2,世界管理,World,isDaytime,World:isDaytime(),none,"ErrorCode.OK, result:boolean",是否是白天,"local isDayTx = World:isDaytime()
if isDayTx~=ErrorCode.OK then --如果是夜晚则给玩家发火把
	local itemId, itemCnt = 817, 2 --火把Id和数量
	Player:gainItems(playerId, itemId, itemCnt, 1)
end"
20002,2,世界管理,World,isCustomGame,World:isCustomGame(),none,"ErrorCode.OK, result:boolean",是否是自定义游戏,"local result = World:isCustomGame()
if result==ErrorCode.OK then print('当前游戏是自定义游戏') end"
20003,2,世界管理,World,isCreativeMode,World:isCreativeMode(),none,"ErrorCode.OK, result:boolean",是否是创造模式,"local result = World:isCreativeMode()
if result==ErrorCode.OK then print('当前模式：创造模式') end"
20004,2,世界管理,World,isGodMode,World:isGodMode(),none,"ErrorCode.OK, result:boolean",多人创造模式 或者 自制玩法的编辑模式,"local result = World:isGodMode()
if result==ErrorCode.OK then print('当前模式：多人创造模式') end"
20005,2,世界管理,World,isExtremityMode,World:isExtremityMode(),none,"ErrorCode.OK, result:boolean",极限模式,"local result = World:isExtremityMode()
if result==ErrorCode.OK then print('当前模式：极限模式') end"
20006,2,世界管理,World,isFreeMode,World:isFreeMode(),none,"ErrorCode.OK, result:boolean",冒险模式之自由模式,"local result = World:isFreeMode()
if result==ErrorCode.OK then print('当前模式：冒险模式之自由模式') end"
20007,2,世界管理,World,isSurviveMode,World:isSurviveMode(),none,"ErrorCode.OK, result:boolean",单人模式 或者 冒险模式之自由模式,"local result = World:isSurviveMode()
if result==ErrorCode.OK then print('当前模式：单人生存模式') end"
20008,2,世界管理,World,isCreateRunMode,World:isCreateRunMode(),none,"ErrorCode.OK, result:boolean",由创造模式转的生存,"local result = World:isCreateRunMode()
if result==ErrorCode.OK then print('当前模式：由创造模式转的生存') end"
20009,2,世界管理,World,isGameMakerMode,World:isGameMakerMode(),none,"ErrorCode.OK, result:boolean",自制玩法的编辑模式,"local result = World:isGameMakerMode()
if result==ErrorCode.OK then print('当前模式：自制玩法的编辑模式') end"
20010,2,世界管理,World,isGameMakerRunMode,World:isGameMakerRunMode(),none,"ErrorCode.OK, result:boolean",自制玩法的运行模式,"local result = World:isGameMakerRunMode()
if result==ErrorCode.OK then print('当前模式：自制玩法的运行模式') end"
20011,2,世界管理,World,getHours,World:getHours(),none,"ErrorCode.OK, result:number",获取游戏当前时间(h),"local result, dayTime = World:getHours()
if result==ErrorCode.OK then print('当前时间：', dayTime) end"
20012,2,世界管理,World,getCameraEditState,World:getCameraEditState(),none,"ErrorCode.OK, state:number0默认，1编辑状态，2测试状态",获取视角编辑状态,"local result, state = World:getCameraEditState()
if result==ErrorCode.OK then print('当前视角编辑状态：', state) end"
20013,2,世界管理,World,setCameraEditState,World:setCameraEditState(state),state:CameraEditState,ErrorCode.OK,设置视角编辑状态,"--获取到的是Camera对象(userdata)，此处返回的是nil
local result, config = World:getCustomCameraConfig()
if result==ErrorCode.OK and config~=nil then --自定义视角
	config:setOption(CAMERA_OPTION_INDEX_CONFIG_SET, CCG_FLATVIEW)
end"
20014,2,世界管理,World,getCustomCameraConfig,World:getCustomCameraConfig(),none,"ErrorCode.OK, config:CameraEditState",获取自定义相机配置,local result = World:getCustomCameraConfig()
20015,2,世界管理,World,getRangeXZ,World:getRangeXZ(),none,"ErrorCode.OK, startX|startZ|endX|endZ:number",获取区块(chunk)范围,"local result, startX,startZ,endX,endZ = World:getRangeXZ()
if result==ErrorCode.OK then print('StartXZ=(',startX,', ',startZ,'), EndXZ=(',endX,', ',endZ,')')  end"
20016,2,世界管理,World,getActorsByBox,"World:getActorsByBox(objtype, x1, y1, z1, x2, y2, z2)","type:number指定类型, x1|y1|z1:number起始位置, x2|y2|z2:number最终位置","ErrorCode.OK, num:number, objids:tableobjid数组",获取指定范围内actor,"local actorType = 0 --OBJ_TYPE_MONSTER
local x1, y1, z1 = -5, 1, -5
local x2, y2, z2 = 15, 9, 15
local ret, num, array = World:getActorsByBox(actorType, x1,y1,z1, x2,y2,z2)
if ret == ErrorCode.OK then print('Actors ===>> ', num, array) end"
20017,2,世界管理,World,getPlayerTotal,World:getPlayerTotal(alive),alive:number0表示阵亡，1表示存活，默认-1表示全体玩家,"ErrorCode.OK, num:number数量","获取全部玩家,可限制存活情况",local result = World:getPlayerTotal(alive)
20018,2,世界管理,World,getAllPlayers,World:getAllPlayers(alive),alive:number0表示阵亡，1表示存活，默认-1表示全体玩家,"ErrorCode.OK, num:number数量, array:table玩家uin数组","获取全部玩家,可限制存活情况","local aliveType = -1 --获取所有玩家数据
local ret, num, array = World:getAllPlayers(aliveType)
if ret == ErrorCode.OK then print('Actors ===>> ', num, array) end"
20019,2,世界管理,World,randomOnePlayer,World:randomOnePlayer(alive),alive:number0表示阵亡，1表示存活，默认-1表示全体玩家,"ErrorCode.OK, uin:number",随机出一个玩家,"local aliveType = 1 --随机一个存活玩家Id
local ret, playerId = World:randomOnePlayer(aliveType)
if ret == ErrorCode.OK then print('Player ===>> ', playerId) end"
20020,2,世界管理,World,despawnActor,World:despawnActor(objid),objid:number,ErrorCode.OK,移除actor,"local ret, num, array = World:getActorsByBox(aType, x1,y1,z1, x2,y2,z2)
if array and #array>0 then --移除Actor
	local result = World:despawnActor(array[#array])
	if result == ErrorCode.OK then print('Despawn the last actor') end
end"
20021,2,世界管理,World,spawnCreature,"World:spawnCreature(x, y, z, actorid, num)","x|y|z:number, mobid:number, num:number","ErrorCdoe.OK, objids:tableobjid数组",生成生物(包括怪物、NPC、动物等),"local x, y, z = 6, 7, 8
local actorId, actorCnt = 3812, 2 --小牛
local ret, objids = World:spawnCreature(x, y, z, actorId, actorCnt)
if objids and #objids>0 then
	for idx = 1, #objIds do --设置氧气依赖
		Creature:setOxygenNeed(objids[idx], true)
	end 
end"
20022,2,世界管理,World,despawnCreature,World:despawnCreature(objid),objid:number,ErrorCode.OK,移除生物,"local result = World:despawnCreature(objids[1])
if result == ErrorCode.OK then print('Despawn the creature') end"
20023,2,世界管理,World,spawnItem,"World:spawnItem(x, y, z, itemid, num)","x|y|z:number, itemid:number, num:number","ErrorCode.OK, objid:number",在指定位置生成道具,"local xPos, yPos, zPos = 5, 6, 7
local itemId, itemCnt = 15003, 30 --道具Id和数量
local ret, objIds = World:spawnItem(xPos, yPos, zPos, itemId, itemCnt)
if ret == ErrorCode.OK then print(""Item[1]====>>>"", objIds[1]) end"
20024,2,世界管理,World,despawnItemByBox,"World:despawnItemByBox(x1, y1, z1, x2, y2, z2)","x1|y1|z1:number起始位置, x2|y2|z2:number最终位置",ErrorCode.OK,移除道具(通过区域),"local x1, y1, z1 = -5, 5, -5
local x2, y2, z2 = 15, 9, 15
local result = World:despawnItemByBox(x1,y1,z1, x2,y2,z2)
if result == ErrorCode.OK then print('成功将指定区域的Item移除') end"
20025,2,世界管理,World,despawnItemByObjid,World:despawnItemByObjid(objid),objid:number,ErrorCode.OK,移除道具(通过ID),"local result = World:despawnItemByObjid(objIds[1])
if result == ErrorCode.OK then print('成功将指定的Item移除') end"
20026,2,世界管理,World,spawnProjectile,"World:spawnProjectile(shooter, itemid, x, y, z, dirx, diry, dirz, speed)","shooter:number, itemid:number, x|y|z:number, dirx|diry|dirz:number, speed:number",ErrorCode.OK,生成投掷物,"local result = World:spawnProjectile(shooter, itemid, x, y, z, dirx, diry, dirz, speed)"
30001,3,方块管理,Block,isSolidBlock,"Block:isSolidBlock(x, y, z)",x|y|z:number,"ErrorCode.OK, data:boolean",方块是否是实体,"local result = Block:isSolidBlock(x, y, z)"
30002,3,方块管理,Block,isLiquidBlock,"Block:isLiquidBlock(x, y, z)",x|y|z:number,"ErrorCode.OK, data:boolean",方块是否是流体,"local result = Block:isLiquidBlock(x, y, z)"
30003,3,方块管理,Block,isAirBlock,"Block:isAirBlock(x, y, z)",x|y|z:number,"ErrorCode.OK, data:boolean",方块是否是空气,"local result = Block:isAirBlock(x, y, z)"
30004,3,方块管理,Block,getBlockID,"Block:getBlockID(x, y, z)",x|y|z:number,"ErrorCode.OK, id:number",获取block对应id,"local result = Block:getBlockID(x, y, z)"
30005,3,方块管理,Block,setBlockAll,"Block:setBlockAll(x, y, z, blockid, data)","x|y|z:number, blockid:number, data:number, flag:number",ErrorCode.OK,设置blockalldata 更新+通知,"local result = Block:setBlockAll(x, y, z, blockid, data)"
30006,3,方块管理,Block,destroyBlock,"Block:destroyBlock(x, y, z, dropitem)","x|y|z:number, dropitem:boolean",ErrorCode.OK,销毁方块,"local result = Block:destroyBlock(x, y, z, dropitem)"
30007,3,方块管理,Block,placeBlock,"Block:placeBlock(blockid, x, y, z, face)","blockid:number, x|y|z:number, face:number","ErrorCode.OK, ret:boolean成功放置与否",放置方块,"local result = Block:placeBlock(blockid, x, y, z, face)"
30008,3,方块管理,Block,setBlockAllForUpdate,"Block:setBlockAllForUpdate(x, y, z, blockid)","x|y|z:number, blockid:number, data:number, flag:number",ErrorCode.OK,设置blockalldata 通知周围方块,"local result = Block:setBlockAllForUpdate(x, y, z, blockid)"
30009,3,方块管理,Block,setBlockAllForNotify,"Block:setBlockAllForNotify(x, y, z, blockid)","x|y|z:number, blockid:number, data:number, flag:number",ErrorCode.OK,设置blockalldata 更新当前位置方块,"local result = Block:setBlockAllForNotify(x, y, z, blockid)"
30010,3,方块管理,Block,setBlockSettingAttState,"Block:setBlockSettingAttState(blockid, atttype, switch)","blockid:number, atttype:BLOCKATTR, switch:boolean",ErrorCode.OK,设置方块设置属性状态,"local result = Block:setBlockSettingAttState(blockid, atttype, switch)"
30011,3,方块管理,Block,getBlockSwitchStatus,Block:getBlockSwitchStatus(pos),x|y|z:number,"ErrorCode.OK, isactive:boolean",获取功能方块的开关状态,local result = Block:getBlockSwitchStatus(pos)
30012,3,方块管理,Block,setBlockSwitchStatus,"Block:setBlockSwitchStatus(pos, isactive)","x|y|z:number, isactive:boolean",ErrorCode.OK,设置功能方块的开关状态,"local result = Block:setBlockSwitchStatus(pos, isactive)"
40001,4,游戏Actor,Actor,isPlayer,Actor:isPlayer(objid),objid,ErrorCode.OK是玩家是玩家,检测是否是玩家,local result = Actor:isPlayer(objid)
40002,4,游戏Actor,Actor,isMob,Actor:isMob(objid),objid,ErrorCode.OK是怪物是怪物,检测是否是怪物,local result = Actor:isMob(objid)
40003,4,游戏Actor,Actor,getObjType,Actor:getObjType(objid),objid,"ErrorCode.OK, objtype:number",获取actor类型:生物/玩家...,local result = Actor:getObjType(objid)
40004,4,游戏Actor,Actor,isInAir,Actor:isInAir(objid),objid:number,ErrorCode.OK,是否在空中,local result = Actor:isInAir(objid)
40005,4,游戏Actor,Actor,getPosition,Actor:getPosition(objid),objid,"ErrorCode.OK, x|y|z:number",获取actor位置,local result = Actor:getPosition(objid)
40006,4,游戏Actor,Actor,setPosition,"Actor:setPosition(objid, x, y, z)","objid:number, x|y|z:number",ErrorCode.OK,设置actor位置,"local result = Actor:setPosition(objid, x, y, z)"
40007,4,游戏Actor,Actor,jump,Actor:jump(objid),objid:number,ErrorCode.OK,跳跃,local result = Actor:jump(objid)
40008,4,游戏Actor,Actor,killSelf,Actor:killSelf(objid),objid:number,ErrorCode.OK,杀死自己,local result = Actor:killSelf(objid)
40009,4,游戏Actor,Actor,getCurPlaceDir,Actor:getCurPlaceDir(objid),objid:number,"ErrorCode.OK, dir:number",获取当前朝向,local result = Actor:getCurPlaceDir(objid)
40010,4,游戏Actor,Actor,tryMoveToActor,"Actor:tryMoveToActor(self_objid, target_objid, speed)","self_objid:number, target_objid:number, speed:number",ErrorCode.OK,向目标actor移动,"local result = Actor:tryMoveToActor(self_objid, target_objid, speed)"
40011,4,游戏Actor,Actor,tryMoveToPos,"Actor:tryMoveToPos(objid, x, y, z, speed)","objid:number, x|y|z:number, speed:number",ErrorCode.OK,向目标位置移动,"local result = Actor:tryMoveToPos(objid, x, y, z, speed)"
40012,4,游戏Actor,Actor,addBuff,"Actor:addBuff(objid, buffid, bufflv, customticks)","objid:number, buffid:number, bufflv:number, customticks:number",ErrorCode.OK,增加buff,"local result = Actor:addBuff(objid, buffid, bufflv, customticks)"
40013,4,游戏Actor,Actor,hasBuff,"Actor:hasBuff(objid, buffid)","objid:number, buffid:number","ErrorCode.OK, ret:boolean",是否具有某个buff,"local result = Actor:hasBuff(objid, buffid)"
40014,4,游戏Actor,Actor,removeBuff,"Actor:removeBuff(objid, buffid)","objid:number, buffid:number",ErrorCode.OK,删除buff,"local result = Actor:removeBuff(objid, buffid)"
40015,4,游戏Actor,Actor,clearAllBuff,Actor:clearAllBuff(objid),objid:number,ErrorCode.OK,清除所有buff,local result = Actor:clearAllBuff(objid)
40016,4,游戏Actor,Actor,clearAllBadBuff,Actor:clearAllBadBuff(objid),objid:number,ErrorCode.OK,清除所有负面buff,local result = Actor:clearAllBadBuff(objid)
40017,4,游戏Actor,Actor,getBuffList,Actor:getBuffList(objid),objid:number,"ErrorCode.OK, num:numberbuff数量, array:tablebuffid数组",获取buff列表,local result = Actor:getBuffList(objid)
40018,4,游戏Actor,Actor,getBuffLeftTick,"Actor:getBuffLeftTick(objid, buffid)","objid:number, buffid:number","ErrorCode.OK, ticks:number",获取当前index对应buff的剩余itck,"local result = Actor:getBuffLeftTick(objid, buffid)"
40019,4,游戏Actor,Actor,addHP,"Actor:addHP(objid, hp)","objid:number, hp:number",ErrorCode.OK,增加当前血量,"local result = Actor:addHP(objid, hp)"
40020,4,游戏Actor,Actor,getHP,Actor:getHP(objid),objid:number,"ErrorCode.OK, value:number",获取当前血量,local result = Actor:getHP(objid)
40021,4,游戏Actor,Actor,getMaxHP,Actor:getMaxHP(objid),objid:number,"ErrorCode.OK, value:number",获取当前最大血量,local result = Actor:getMaxHP(objid)
40022,4,游戏Actor,Actor,addOxygen,"Actor:addOxygen(objid, oxygen)","objid:number, oxygen:number",ErrorCode.OK,增加氧气值,"local result = Actor:addOxygen(objid, oxygen)"
40023,4,游戏Actor,Actor,getOxygen,Actor:getOxygen(objid),objid:number,"ErrorCode.OK, value:number",获取氧气值,local result = Actor:getOxygen(objid)
40024,4,游戏Actor,Actor,addEnchant,"Actor:addEnchant(objid, slot, enchantId, enchantLevel)","objid:number, slot:number, enchantId:number, enchantLevel:number",ErrorCode.OK,增加相关装备的附魔,"local result = Actor:addEnchant(objid, slot, enchantId, enchantLevel)"
40025,4,游戏Actor,Actor,removeEnchant,"Actor:removeEnchant(objid, slot, enchantId)","objid:number, slot:number, enchantId:number",ErrorCode.OK,去掉相关装备的附魔,"local result = Actor:removeEnchant(objid, slot, enchantId)"
40026,4,游戏Actor,Actor,findNearestBlock,"Actor:findNearestBlock(objid, blockid, blockRange)","objid:number, blockid:number, blockRange","ErrorCode.OK, x|y|z:number",寻找距离最近方块id,"local result = Actor:findNearestBlock(objid, blockid, blockRange)"
40027,4,游戏Actor,Actor,setFaceYaw,"Actor:setFaceYaw(objid, yaw)","objid:number, yaw:number角度",ErrorCode.OK,设置actor视角横向偏移角度,"local result = Actor:setFaceYaw(objid, yaw)"
40028,4,游戏Actor,Actor,getFaceYaw,Actor:getFaceYaw(objid),objid:number,"ErrorCode.OK, yaw:number",获取actor视角横向偏移角度,local result = Actor:getFaceYaw(objid)
40029,4,游戏Actor,Actor,turnFaceYaw,"Actor:turnFaceYaw(objid, offset)","objid:number, offset:number转动角度",ErrorCode.OK,转动actor横向偏移角度,"local result = Actor:turnFaceYaw(objid, offset)"
40030,4,游戏Actor,Actor,setFacePitch,"Actor:setFacePitch(objid, pitch)","objid:number, pitch:number仰望角度",ErrorCode.OK,设置actor视角仰望角度,"local result = Actor:setFacePitch(objid, pitch)"
40031,4,游戏Actor,Actor,getFacePitch,Actor:getFacePitch(objid),objid:number,"ErrorCode.OK, pitch:number",获取actor视角仰望角度,local result = Actor:getFacePitch(objid)
40032,4,游戏Actor,Actor,turnFacePitch,"Actor:turnFacePitch(objid, offset)","objid:number, offset:number转动角度",ErrorCode.OK,转动actor仰望偏移角度,"local result = Actor:turnFacePitch(objid, offset)"
40033,4,游戏Actor,Actor,playBodyEffect,"Actor:playBodyEffect(objid, type)","objid:number, type:number特效类型ACTORBODY_EFFECT",ErrorCode.OK,播放特效,"local result = Actor:playBodyEffect(objid, type)"
40034,4,游戏Actor,Actor,stopBodyEffect,"Actor:stopBodyEffect(objid, type)","objid:number, type:number特效类型ACTORBODY_EFFECT",ErrorCode.OK,停止特效,"local result = Actor:stopBodyEffect(objid, type)"
40035,4,游戏Actor,Actor,playBodyEffectByFile,"Actor:playBodyEffectByFile(objid, filename, sync)","objid:number, filename:string文件名, sync:bool同步",ErrorCode.OK,播放文件特效,"local result = Actor:playBodyEffectByFile(objid, filename, sync)"
40036,4,游戏Actor,Actor,stopBodyEffectByFile,"Actor:stopBodyEffectByFile(objid, filename)","objid:number, filename:string文件名",ErrorCode.OK,停止文件特效,"local result = Actor:stopBodyEffectByFile(objid, filename)"
40037,4,游戏Actor,Actor,playSound,"Actor:playSound(objid, name, volume, pitch, flag)","objid:number, name:string, volume:number, pitch:number, flag:number",ErrorCode.OK,播放声音,"local result = Actor:playSound(objid, name, volume, pitch, flag)"
40038,4,游戏Actor,Actor,playSoundSpecial,"Actor:playSoundSpecial(objid, name, type)","objid:number, name:string, type:number指定类型(GSOUND_TYPE)",ErrorCode.OK,播放声音(特定类型:GSOUND_TYPE),"local result = Actor:playSoundSpecial(objid, name, type)"
40039,4,游戏Actor,Actor,clearActorWithId,"Actor:clearActorWithId(actorid, bkill)","actorid:number, bkill:boolean",ErrorCode.OK,清除生物ID为actorid的生物,"local result = Actor:clearActorWithId(actorid, bkill)"
40040,4,游戏Actor,Actor,setAttackType,"Actor:setAttackType(objid, attacktype)","objid:number, attacktype:number",ErrorCode.OK,设置伤害类型,"local result = Actor:setAttackType(objid, attacktype)"
40041,4,游戏Actor,Actor,setImmuneType,"Actor:setImmuneType(objid, immunetype, isadd)","objid:number, immunetype:number, isadd:number",ErrorCode.OK,设置免疫伤害类型,"local result = Actor:setImmuneType(objid, immunetype, isadd)"
40042,4,游戏Actor,Actor,mountActor,"Actor:mountActor(objid, mountobjid, posindex)","objid:number, mountobjid:number, posindex:number骑乘位",ErrorCode.OK,登上、脱离载具,"local result = Actor:mountActor(objid, mountobjid, posindex)"
40043,4,游戏Actor,Actor,setActionAttrState,"Actor:setActionAttrState(objid, actionattr, switch)","objid:number, actionattr:numberPLAYERATTR, switch:boolean",ErrorCode.OK,设置生物行为属性状态,"local result = Actor:setActionAttrState(objid, actionattr, switch)"
40044,4,游戏Actor,Actor,tryNavigationToPos,"Actor:tryNavigationToPos(objid, x, y, z, cancontrol)","objid:number, x|y|z:number, cancontrol:boolean",ErrorCode.OK,寻路到目标位置,"local result = Actor:tryNavigationToPos(objid, x, y, z, cancontrol)"
40045,4,游戏Actor,Actor,getRidingActorObjId,Actor:getRidingActorObjId(objid),objid:number,ErrorCode.OK,获取骑乘生物的objid,local result = Actor:getRidingActorObjId(objid)
50001,5,游戏玩家,Player,getAttr,Player:getAttr(attrtype),"objid:number, atttype:number","ErrorCode.OK, value:number",nil,local result = Player:getAttr(attrtype)
50002,5,游戏玩家,Player,setAttr,"Player:setAttr(attrtype, val)","objid:number, val:number, atttype:number",ErrorCode.OK,nil,"local result = Player:setAttr(attrtype, val)"
50003,5,游戏玩家,Player,getHostUin,Player:getHostUin(),nil,"ErrorCode.OK, uin:number",获取房主,local result = Player:getHostUin()
50004,5,游戏玩家,Player,isMainPlayer,Player:isMainPlayer(objid),objid:number,ErrorCode.OK是本地玩家是本地玩家,检测是否是本地玩家,local result = Player:isMainPlayer(objid)
50005,5,游戏玩家,Player,getMainPlayerUin,Player:getMainPlayerUin(),nil,"ErrorCode.OK, uin:number",获取本地玩家的uin,local result = Player:getMainPlayerUin()
50006,5,游戏玩家,Player,getGameResults,Player:getGameResults(objid),objid:number,"ErrorCode.OK, value:number",获取玩家比赛结果,local result = Player:getGameResults(objid)
50007,5,游戏玩家,Player,setGameResults,"Player:setGameResults(objid, result)","objid:number, result",ErrorCode.OK,设置玩家比赛结果,"local result = Player:setGameResults(objid, result)"
50008,5,游戏玩家,Player,getGameScore,Player:getGameScore(objid),objid:number,"ErrorCode.OK, value:number",获取玩家游戏分数,local result = Player:getGameScore(objid)
50009,5,游戏玩家,Player,setGameScore,"Player:setGameScore(objid, score)",objid:number,"ErrorCode.OK, value:number",设置玩家游戏分数,"local result = Player:setGameScore(objid, score)"
50010,5,游戏玩家,Player,getGameRanking,Player:getGameRanking(objid),objid:number,"ErrorCode.OK, rank:number",获取玩家的排行,local result = Player:getGameRanking(objid)
50011,5,游戏玩家,Player,setGameRanking,"Player:setGameRanking(objid, rank)","objid:number, rank:number",ErrorCode.OK,给玩家设置排行,"local result = Player:setGameRanking(objid, rank)"
50012,5,游戏玩家,Player,gainItems,"Player:gainItems(objid, itemid, num, prioritytype)","objid:number, itemid:number, num:number, prioritytype:number(1是优先快捷栏2是优先背包栏)",ErrorCode.OK,给玩家增加道具,"local result = Player:gainItems(objid, itemid, num, prioritytype)"
50013,5,游戏玩家,Player,teleportHome,Player:teleportHome(objid),objid:number,ErrorCode.OK,传送到出生点,local result = Player:teleportHome(objid)
50014,5,游戏玩家,Player,getCurToolID,Player:getCurToolID(objid),objid:number,"ErrorCode.OK, ret",获取玩家当前手持的物品id,local result = Player:getCurToolID(objid)
50015,5,游戏玩家,Player,getNickname,Player:getNickname(objid),objid:number,"ErrorCode.OK, name:string",获取玩家昵称,local result = Player:getNickname(objid)
50016,5,游戏玩家,Player,removeBackpackItem,"Player:removeBackpackItem(objid, itemid, num)","objid:number, itemid:number, num:number",ErrorCode.OK,移走背包里的物品,"local result = Player:removeBackpackItem(objid, itemid, num)"
50017,5,游戏玩家,Player,getDieTimes,Player:getDieTimes(objid),objid:number,"ErrorCode.OK, value:number",获取玩家死亡次数,local result = Player:getDieTimes(objid)
50018,5,游戏玩家,Player,getLeftLifeNum,Player:getLeftLifeNum(objid),objid:number,"ErrorCode.OK, value:number",获取玩家剩下的生命次数,local result = Player:getLeftLifeNum(objid)
50019,5,游戏玩家,Player,setTeam,"Player:setTeam(objid, team)","objid:number, teamid:number",ErrorCode.OK,设置生物队伍,"local result = Player:setTeam(objid, team)"
50020,5,游戏玩家,Player,getTeam,Player:getTeam(objid),objid:number,"ErrorCode.OK, teamid:number",获取生物队伍,local result = Player:getTeam(objid)
50021,5,游戏玩家,Player,getFoodLevel,Player:getFoodLevel(objid),objid:number,ErrorCode.OK,获取当前饱食度,local result = Player:getFoodLevel(objid)
50022,5,游戏玩家,Player,setFoodLevel,"Player:setFoodLevel(objid, foodLevel)","objid:number, foodLevel:number",ErrorCode.OK,设置当前饱食度,"local result = Player:setFoodLevel(objid, foodLevel)"
50023,5,游戏玩家,Player,getCurShotcut,Player:getCurShotcut(objid),objid:number,"ErrorCode.OK, scutIdx:number",获取当前所用快捷栏键,local result = Player:getCurShotcut(objid)
50024,5,游戏玩家,Player,onCurToolUsed,"Player:onCurToolUsed(objid, num)","objid:number, num:number道具数量",ErrorCode.OK,设置当前玩家手持的道具消耗,"local result = Player:onCurToolUsed(objid, num)"
50025,5,游戏玩家,Player,setSkillCD,"Player:setSkillCD(objid, itemid, cd)","objid:number, itemid:number, cd:number",ErrorCode.OK,设置CD,"local result = Player:setSkillCD(objid, itemid, cd)"
50026,5,游戏玩家,Player,reviveToPos,"Player:reviveToPos(objid, x, y, z)","objid:number, x|y|z:number",ErrorCode.OK,复活玩家到指定点,"local result = Player:reviveToPos(objid, x, y, z)"
50027,5,游戏玩家,Player,setRevivePoint,"Player:setRevivePoint(objid, x, y, z)","objid:number, x|y|z:number",ErrorCode.OK,改变玩家复活点位置,"local result = Player:setRevivePoint(objid, x, y, z)"
50028,5,游戏玩家,Player,playAct,"Player:playAct(objid, actid)","objid:number, actid:number动作id",ErrorCode.OK,玩家播放动画,"local result = Player:playAct(objid, actid)"
50029,5,游戏玩家,Player,notifyGameInfo2Self,"Player:notifyGameInfo2Self(objid, info)","objid:number, info:string文字内容",ErrorCode.OK,对玩家显示飘窗文字,"local result = Player:notifyGameInfo2Self(objid, info)"
50030,5,游戏玩家,Player,useItem,"Player:useItem(objid, itemid, status, onshift)","objid:number, itemid:number, status:number使用状态, onshift:boolean是否按下shift键",ErrorCode.OK,使玩家使用当前道具,"local result = Player:useItem(objid, itemid, status, onshift)"
50031,5,游戏玩家,Player,rotateCamera,"Player:rotateCamera(objid, yaw, pitch)","objid:number, yaw:number, pitch:number",ErrorCode.OK,旋转玩家镜头,"local result = Player:rotateCamera(objid, yaw, pitch)"
50032,5,游戏玩家,Player,changeViewMode,"Player:changeViewMode(objid, viewmode, islock)","objid:number, viewmode:numberVIEWPORTTYPE, islock:boolean",ErrorCode.OK,改变玩家视角模式,"local result = Player:changeViewMode(objid, viewmode, islock)"
50033,5,游戏玩家,Player,setActionAttrState,"Player:setActionAttrState(objid, actionattr, switch)","objid:number, actionattr:numberPLAYERATTR, switch:boolean",ErrorCode.OK,设置生物行为属性状态,"local result = Player:setActionAttrState(objid, actionattr, switch)"
50034,5,游戏玩家,Player,checkActionAttrState,"Player:checkActionAttrState(objid, actionattr)","objid:number, actionattr:numberPLAYERATTR",ErrorCode.OK,玩家的行为属性状态开关,"local result = Player:checkActionAttrState(objid, actionattr)"
50035,5,游戏玩家,Player,getMaxHP,Player:getMaxHP(objid),objid:number,"ErrorCode.OK, value:number",获取当前最大血量,local result = Player:getMaxHP(objid)
50036,5,游戏玩家,Player,getCurHP,Player:getCurHP(objid),"objid:number, atttype:number","ErrorCode.OK, value:number",获取当前生命量,local result = Player:getCurHP(objid)
50037,5,游戏玩家,Player,getHpRecover,Player:getHpRecover(objid),"objid:number, atttype:number","ErrorCode.OK, value:number",HP恢复,local result = Player:getHpRecover(objid)
50038,5,游戏玩家,Player,getCurFood,Player:getCurFood(objid),"objid:number, atttype:number","ErrorCode.OK, value:number",当前饥饿值,local result = Player:getCurFood(objid)
50039,5,游戏玩家,Player,getMaxOxygen,Player:getMaxOxygen(objid),"objid:number, atttype:number","ErrorCode.OK, value:number",[[,local result = Player:getMaxOxygen(objid)
50040,5,游戏玩家,Player,getCurOxygen,Player:getCurOxygen(objid),"objid:number, atttype:number","ErrorCode.OK, value:number",当前氧气值,local result = Player:getCurOxygen(objid)
50041,5,游戏玩家,Player,getWalkSpeed,Player:getWalkSpeed(objid),"objid:number, atttype:number","ErrorCode.OK, value:number",行走速度,local result = Player:getWalkSpeed(objid)
50042,5,游戏玩家,Player,getSwimSpeed,Player:getSwimSpeed(objid),"objid:number, atttype:number","ErrorCode.OK, value:number",游泳速度（水中速度）,local result = Player:getSwimSpeed(objid)
50043,5,游戏玩家,Player,getJumpPower,Player:getJumpPower(objid),"objid:number, atttype:number","ErrorCode.OK, value:number",跳跃力,local result = Player:getJumpPower(objid)
50044,5,游戏玩家,Player,getRunSpeed,Player:getRunSpeed(objid),"objid:number, atttype:number","ErrorCode.OK, value:number",奔跑速度,local result = Player:getRunSpeed(objid)
50045,5,游戏玩家,Player,getSneakSpeed,Player:getSneakSpeed(objid),"objid:number, atttype:number","ErrorCode.OK, value:number",潜行速度,local result = Player:getSneakSpeed(objid)
50046,5,游戏玩家,Player,getDodge,Player:getDodge(objid),"objid:number, atttype:number","ErrorCode.OK, value:number",闪避,local result = Player:getDodge(objid)
50047,5,游戏玩家,Player,getPunchAttack,Player:getPunchAttack(objid),"objid:number, atttype:number","ErrorCode.OK, value:number",近程攻击,local result = Player:getPunchAttack(objid)
50048,5,游戏玩家,Player,getRangeAttack,Player:getRangeAttack(objid),"objid:number, atttype:number","ErrorCode.OK, value:number",远程攻击,local result = Player:getRangeAttack(objid)
50049,5,游戏玩家,Player,getPunchDefense,Player:getPunchDefense(objid),"objid:number, atttype:number","ErrorCode.OK, value:number",近程防御,local result = Player:getPunchDefense(objid)
50050,5,游戏玩家,Player,getRangeDefense,Player:getRangeDefense(objid),"objid:number, atttype:number","ErrorCode.OK, value:number",远程防御,local result = Player:getRangeDefense(objid)
50051,5,游戏玩家,Player,getStarNum,"Player:getStarNum(objid, val)","objid:number, atttype:number","ErrorCode.OK, value:number",星星数,"local result = Player:getStarNum(objid, val)"
50052,5,游戏玩家,Player,getModelScale,"Player:getModelScale(objid, val)","objid:number, atttype:number","ErrorCode.OK, value:number",模型大小,"local result = Player:getModelScale(objid, val)"
50053,5,游戏玩家,Player,setMaxHP,"Player:setMaxHP(objid, val)","objid:number, val:number, atttype:number",ErrorCode.OK,设置最大血量,"local result = Player:setMaxHP(objid, val)"
50054,5,游戏玩家,Player,setHP,"Player:setHP(objid, val)","objid:number, val:number, atttype:number",ErrorCode.OK,设置血量,"local result = Player:setHP(objid, val)"
50055,5,游戏玩家,Player,setHpRecover,Player:setHpRecover(objid),"objid:number, val:number, atttype:number",ErrorCode.OK,设置HP恢复,local result = Player:setHpRecover(objid)
50056,5,游戏玩家,Player,setCurFood,Player:setCurFood(objid),"objid:number, val:number, atttype:number",ErrorCode.OK,设置当前饥饿度,local result = Player:setCurFood(objid)
50057,5,游戏玩家,Player,setOxygen,"Player:setOxygen(objid, val)","objid:number, val:number, atttype:number",ErrorCode.OK,设置氧气,"local result = Player:setOxygen(objid, val)"
50058,5,游戏玩家,Player,setWalkSpeed,"Player:setWalkSpeed(objid, val)","objid:number, val:number, atttype:number",ErrorCode.OK,设置行走速度,"local result = Player:setWalkSpeed(objid, val)"
50059,5,游戏玩家,Player,setSwimSpeed,"Player:setSwimSpeed(objid, val)","objid:number, val:number, atttype:number",ErrorCode.OK,设置游泳速度（水中速度）,"local result = Player:setSwimSpeed(objid, val)"
50060,5,游戏玩家,Player,setJumpPower,"Player:setJumpPower(objid, val)","objid:number, val:number, atttype:number",ErrorCode.OK,设置跳跃力,"local result = Player:setJumpPower(objid, val)"
50061,5,游戏玩家,Player,setRunSpeed,"Player:setRunSpeed(objid, val)","objid:number, val:number, atttype:number",ErrorCode.OK,设置奔跑速度,"local result = Player:setRunSpeed(objid, val)"
50062,5,游戏玩家,Player,setSneakSpeed,"Player:setSneakSpeed(objid, val)","objid:number, val:number, atttype:number",ErrorCode.OK,设置潜行速度,"local result = Player:setSneakSpeed(objid, val)"
50063,5,游戏玩家,Player,setDodge,"Player:setDodge(objid, val)","objid:number, val:number, atttype:number",ErrorCode.OK,设置闪避,"local result = Player:setDodge(objid, val)"
50064,5,游戏玩家,Player,setPunchAttack,"Player:setPunchAttack(objid, val)","objid:number, val:number, atttype:number",ErrorCode.OK,设置近程攻击,"local result = Player:setPunchAttack(objid, val)"
50065,5,游戏玩家,Player,setRangeAttack,"Player:setRangeAttack(objid, val)","objid:number, val:number, atttype:number",ErrorCode.OK,设置远程攻击,"local result = Player:setRangeAttack(objid, val)"
50066,5,游戏玩家,Player,setPunchDefense,"Player:setPunchDefense(objid, val)","objid:number, val:number, atttype:number",ErrorCode.OK,设置近程防御,"local result = Player:setPunchDefense(objid, val)"
50067,5,游戏玩家,Player,setRangeDefense,"Player:setRangeDefense(objid, val)","objid:number, val:number, atttype:number",ErrorCode.OK,设置远程防御,"local result = Player:setRangeDefense(objid, val)"
50068,5,游戏玩家,Player,setStarNum,"Player:setStarNum(objid, val)","objid:number, val:number, atttype:number",ErrorCode.OK,设置星星数,"local result = Player:setStarNum(objid, val)"
50069,5,游戏玩家,Player,setModelScale,"Player:setModelScale(objid, val)","objid:number, val:number, atttype:number",ErrorCode.OK,设置模型大小,"local result = Player:setModelScale(objid, val)"
50070,5,游戏玩家,Player,setPosition,"Player:setPosition(objid, x, y, z)","objid:number, x|y|z:number",ErrorCode.OK,设置actor位置,"local result = Player:setPosition(objid, x, y, z)"
50071,5,游戏玩家,Player,getAimPos,Player:getAimPos(objid),objid:number,ErrorCode.OK,获取player准星位置,local result = Player:getAimPos(objid)
60001,6,游戏生物,Creature,isAdult,Creature:isAdult(objid),objid:number,ErrorCode.OK已经成年已经成年,是否已经成年,"local result = Creature:isAdult(objId)
if result == ErrorCode.OK then print(""该生物已成年"") end"
60002,6,游戏生物,Creature,setOxygenNeed,"Creature:setOxygenNeed(objid, v)","objid:number, v:boolean",ErrorCode.OK,设置是否依赖氧气,"local result = Creature:setOxygenNeed(objId, true)
if result == ErrorCode.OK then print(""该生物依赖氧气生存!"") end"
60003,6,游戏生物,Creature,getTamedOwnerID,Creature:getTamedOwnerID(objid),objid:number,"ErrorCode.OK, uin:number0表示未驯服",获取驯服主的ID,"local result, ownerId = Creature:getTamedOwnerID(objId)
if result == ErrorCode.OK and ownerId ~= 0 then
	local result, nickName = Player:getNickname(ownerId)
	if result == ErrorCode.OK then print('玩家<'+nickName+'>的宠物。。。')
end"
60004,6,游戏生物,Creature,setPanic,"Creature:setPanic(objid, v)","objid:number, v:boolean",ErrorCode.OK,设置是否恐慌,"local result = Creature:setPanic(objId, true)
if result == ErrorCode.OK then print(""该生物正在恐慌中"") end"
60005,6,游戏生物,Creature,setAIActive,"Creature:setAIActive(objid, v)","objid:number, v:boolean",ErrorCode.OK,设置AI是否生效,"local result = Creature:setAIActive(objId, true)
if result == ErrorCode.OK then print(""该生物的AI已经生效"") end"
60006,6,游戏生物,Creature,getActorID,Creature:getActorID(objid),objid:number,"ErrorCode.OK, actorid:number",获取怪物资源ID,"local wolfId, dogId = 3407, 3408 --狼/狗的资源Id
local result, actorId = Creature:getActorID(objId)
if result == ErrorCode.OK then
	if actorId == dogId then print(""The creature is A Dog!"") end
	if actorId == wolfId then print(""The creature is A Wolf!"") end
end"
60007,6,游戏生物,Creature,getActorName,Creature:getActorName(objid),objid:number,"ErrorCode.OK, name:string",获取怪物资源ID,local result = Creature:getActorName(objid)
60008,6,游戏生物,Creature,addModAttrib,"Creature:addModAttrib(objid, attrtype, value)","objid:number, attrtype:MODATTRIB_TYPE附魔属性类型, value:number",ErrorCode.OK,增加怪物ModAttrib值,"local modAttrType = MODATTRIB_TYPE.MODATTR_MOVE_SPEED
local result = Creature:addModAttrib(objid, modAttrType, 5)
if result == ErrorCode.OK then print('增加怪物移动速度') end"
60009,6,游戏生物,Creature,getModAttrib,"Creature:getModAttrib(objid, attrtype)","objid:number, attrtype:MODATTRIB_TYPE附魔属性类型","ErrorCode.OK, value:number",获取怪物ModAttrib值,"local modAttrType = MODATTRIB_TYPE.MODATTR_MOVE_SPEED
local result, modAttrVal = Creature:getModAttrib(objid, modAttrType)
if result == ErrorCode.OK then print('怪物的移动速度为', modAttrVal) end"
60010,6,游戏生物,Creature,setTeam,"Creature:setTeam(objid, teamid)","objid:number, teamid:number",ErrorCode.OK,设置生物队伍,"local result = Creature:setTeam(objid, teamid)"
60011,6,游戏生物,Creature,getTeam,Creature:getTeam(objid),objid:number,"ErrorCode.OK, teamid:number",获取生物队伍,local result = Creature:getTeam(objid)
60012,6,游戏生物,Creature,getMaxFood,Creature:getMaxFood(objid),"objid:number, atttype:number","ErrorCode.OK, value:number",获取最大饥饿度,local result = Creature:getMaxFood(objid)
60013,6,游戏生物,Creature,getFood,Creature:getFood(objid),"objid:number, atttype:number","ErrorCode.OK, value:number",获取饥饿度,local result = Creature:getFood(objid)
60014,6,游戏生物,Creature,setFood,"Creature:setFood(objid, val)","objid:number, val:number, atttype:number",ErrorCode.OK,设置饥饿度,"local result = Creature:setFood(objid, val)"
60015,6,游戏生物,Creature,getHpRecover,Creature:getHpRecover(objid),"objid:number, atttype:number","ErrorCode.OK, value:number",HP恢复,local result = Creature:getHpRecover(objid)
60016,6,游戏生物,Creature,getMaxOxygen,Creature:getMaxOxygen(objid),"objid:number, atttype:number","ErrorCode.OK, value:number",最大氧气值,local result = Creature:getMaxOxygen(objid)
60017,6,游戏生物,Creature,getWalkSpeed,Creature:getWalkSpeed(objid),"objid:number, atttype:number","ErrorCode.OK, value:number",行走速度,local result = Creature:getWalkSpeed(objid)
60018,6,游戏生物,Creature,getSwimSpeed,Creature:getSwimSpeed(objid),"objid:number, atttype:number","ErrorCode.OK, value:number",游泳速度（水中速度）,local result = Creature:getSwimSpeed(objid)
60019,6,游戏生物,Creature,getJumpPower,Creature:getJumpPower(objid),"objid:number, atttype:number","ErrorCode.OK, value:number",跳跃力,local result = Creature:getJumpPower(objid)
60020,6,游戏生物,Creature,getMass,Creature:getMass(objid),"objid:number, atttype:number","ErrorCode.OK, value:number",重量,local result = Creature:getMass(objid)
60021,6,游戏生物,Creature,getDodge,Creature:getDodge(objid),"objid:number, atttype:number","ErrorCode.OK, value:number",闪避,local result = Creature:getDodge(objid)
60022,6,游戏生物,Creature,getPunchAttack,Creature:getPunchAttack(objid),"objid:number, atttype:number","ErrorCode.OK, value:number",近程攻击,local result = Creature:getPunchAttack(objid)
60023,6,游戏生物,Creature,getRangeAttack,Creature:getRangeAttack(objid),"objid:number, atttype:number","ErrorCode.OK, value:number",远程攻击,local result = Creature:getRangeAttack(objid)
60024,6,游戏生物,Creature,getPunchDefense,Creature:getPunchDefense(objid),"objid:number, atttype:number","ErrorCode.OK, value:number",近程防御,local result = Creature:getPunchDefense(objid)
60025,6,游戏生物,Creature,getRangeDefense,Creature:getRangeDefense(objid),"objid:number, atttype:number","ErrorCode.OK, value:number",远程防御,local result = Creature:getRangeDefense(objid)
60026,6,游戏生物,Creature,setMaxHp,"Creature:setMaxHp(objid, val)","objid:number, val:number, atttype:number",ErrorCode.OK,设置血量,"local result = Creature:setMaxHp(objid, val)"
60027,6,游戏生物,Creature,setHP,"Creature:setHP(objid, val)","objid:number, val:number, atttype:number",ErrorCode.OK,设置血量,"local result = Creature:setHP(objid, val)"
60028,6,游戏生物,Creature,setHpRecover,Creature:setHpRecover(objid),"objid:number, val:number, atttype:number",ErrorCode.OK,设置HP恢复,local result = Creature:setHpRecover(objid)
60029,6,游戏生物,Creature,setOxygen,"Creature:setOxygen(objid, val)","objid:number, val:number, atttype:number",ErrorCode.OK,设置氧气,"local result = Creature:setOxygen(objid, val)"
60030,6,游戏生物,Creature,setWalkSpeed,"Creature:setWalkSpeed(objid, val)","objid:number, val:number, atttype:number",ErrorCode.OK,设置行走速度,"local result = Creature:setWalkSpeed(objid, val)"
60031,6,游戏生物,Creature,setSwimSpeed,"Creature:setSwimSpeed(objid, val)","objid:number, val:number, atttype:number",ErrorCode.OK,设置游泳速度（水中速度）,"local result = Creature:setSwimSpeed(objid, val)"
60032,6,游戏生物,Creature,setJumpPower,"Creature:setJumpPower(objid, val)","objid:number, val:number, atttype:number",ErrorCode.OK,设置跳跃力,"local result = Creature:setJumpPower(objid, val)"
60033,6,游戏生物,Creature,setDodge,"Creature:setDodge(objid, val)","objid:number, val:number, atttype:number",ErrorCode.OK,设置闪避,"local result = Creature:setDodge(objid, val)"
60034,6,游戏生物,Creature,setPunchAttack,"Creature:setPunchAttack(objid, val)","objid:number, val:number, atttype:number",ErrorCode.OK,设置近程攻击,"local result = Creature:setPunchAttack(objid, val)"
60035,6,游戏生物,Creature,setRangeAttack,"Creature:setRangeAttack(objid, val)","objid:number, val:number, atttype:number",ErrorCode.OK,设置远程攻击,"local result = Creature:setRangeAttack(objid, val)"
60036,6,游戏生物,Creature,setPunchDefense,"Creature:setPunchDefense(objid, val)","objid:number, val:number, atttype:number",ErrorCode.OK,设置近程防御,"local result = Creature:setPunchDefense(objid, val)"
60037,6,游戏生物,Creature,setRangeDefense,"Creature:setRangeDefense(objid, val)","objid:number, val:number, atttype:number",ErrorCode.OK,设置远程防御,"local result = Creature:setRangeDefense(objid, val)"
70001,7,UI管理,UI,setGBattleUI,"UI:setGBattleUI(name, value)","name:string设定名, value:string or boolean设定值",ErrorCode.OK,设置战斗总结UI,"local result = UI:setGBattleUI(name, value)"
70002,7,UI管理,UI,world2RadarPos,"UI:world2RadarPos(x, y)","x:number, z:number","ErrorCode.OK, x:number, z:number",世界坐标转换到小地图,"local result = UI:world2RadarPos(x, y)"
70003,7,UI管理,UI,world2RadarDist,UI:world2RadarDist(length),length:number,"ErrorCode.OK, length:number",世界长度转换到小地图,local result = UI:world2RadarDist(length)
70004,7,UI管理,UI,setMinimapRenderMode,UI:setMinimapRenderMode(mode),mode:number,ErrorCode.OK,设置小地图模式。1：45度旋转视角；2：全图俯视角,local result = UI:setMinimapRenderMode(mode)
70005,7,UI管理,UI,setShapeLine,"UI:setShapeLine(uiname, p1x, p1y, p2x, p2y)","uiname:string, p1x:number, p1y:number, p2x:number, p2y:number",ErrorCode.OK,地图标记形状设置，设置成线条,"local result = UI:setShapeLine(uiname, p1x, p1y, p2x, p2y)"
70006,7,UI管理,UI,setShapeCircle,"UI:setShapeCircle(uiname, x, y, radius)","uiname:string, x:number, y:number, radius:number",ErrorCode.OK,地图标记形状设置，设置成圆,"local result = UI:setShapeCircle(uiname, x, y, radius)"
70007,7,UI管理,UI,ShowScreenEffect,"UI:ShowScreenEffect(type, isloop, incspeed)","type:number效果类型, isloop:boolean是否循环, incspeed:number增速",ErrorCode.OK,显示屏幕效果,"local result = UI:ShowScreenEffect(type, isloop, incspeed)"
80001,8,聊天系统,Chat,sendChat,Chat:sendChat(content),"content:string, type:number0表示普通聊天，1表示系统消息",ErrorCode.OK,发送聊天消息,local result = Chat:sendChat(content)
80002,8,聊天系统,Chat,sendSystemMsg,Chat:sendSystemMsg(content),"content:string, type:number0表示普通聊天，1表示系统消息",ErrorCode.OK,发送系统消息,local result = Chat:sendSystemMsg(content)
90001,9,组队管理,Team,getNumTeam,Team:getNumTeam(),nil,"ErrorCode.OK, num:number",获取队伍数量,local result = Team:getNumTeam()
90002,9,组队管理,Team,getTeamPlayerNum,"Team:getTeamPlayerNum(teamid, alive)","teamid:number队伍ID，默认全部, alive:number是否存活，默认全部","ErrorCode.OK, num:number队伍玩家数量",获取指定队伍玩家,"local result = Team:getTeamPlayerNum(teamid, alive)"
90003,9,组队管理,Team,getTeamPlayers,"Team:getTeamPlayers(teamid, alive)","teamid:number队伍ID，默认全部, alive:number是否存活，默认全部","ErrorCode.OK, num:number队伍玩家数量, array:table成员uin数组",获取指定队伍玩家,"local result = Team:getTeamPlayers(teamid, alive)"
90004,9,组队管理,Team,randomTeamPlayer,"Team:randomTeamPlayer(teamid, alive)","teamid:number队伍ID，默认全部, alive:number是否存活，默认全部","ErrorCode.OK, uin:number随机出玩家的uin",随机一名玩家,"local result = Team:randomTeamPlayer(teamid, alive)"
90005,9,组队管理,Team,setTeamScore,"Team:setTeamScore(teamid, s)","teamid:number, score:number",ErrorCode.OK,设置组队分数,"local result = Team:setTeamScore(teamid, s)"
90006,9,组队管理,Team,getTeamScore,Team:getTeamScore(teamid),teamid:number,"ErrorCode.OK, score:number",获取组队分数,local result = Team:getTeamScore(teamid)
90007,9,组队管理,Team,addTeamScore,"Team:addTeamScore(teamid, score)","teamid:number, score:number",ErrorCode.OK,增加队伍分数,"local result = Team:addTeamScore(teamid, score)"
90008,9,组队管理,Team,setTeamResults,"Team:setTeamResults(teamid, result)",teamid:number result?number,ErrorCode.OK,设置队伍胜负,"local result = Team:setTeamResults(teamid, result)"
90009,9,组队管理,Team,getTeamResults,Team:getTeamResults(teamid),teamid:number,"ErrorCode.OK, teamresult:number",获取当前队伍胜负,local result = Team:getTeamResults(teamid)
90010,9,组队管理,Team,setTeamPlayersResults,"Team:setTeamPlayersResults(teamid, result)","teamid:number, result:number",ErrorCode.OK,设置玩家的队伍胜负,"local result = Team:setTeamPlayersResults(teamid, result)"
90011,9,组队管理,Team,setTeamDieTimes,"Team:setTeamDieTimes(teamid, times)","teamid:number, times:number",ErrorCode.OK,设置队伍总死亡数,"local result = Team:setTeamDieTimes(teamid, times)"
90012,9,组队管理,Team,getTeamDieTimes,Team:getTeamDieTimes(teamid),teamid:number,"ErrorCode.OK, times:number",获取队伍总死亡数,local result = Team:getTeamDieTimes(teamid)
90013,9,组队管理,Team,addTeamDieTimes,Team:addTeamDieTimes(teamid),teamid:number,ErrorCode.OK,增加队伍总死亡数,local result = Team:addTeamDieTimes(teamid)
90014,9,组队管理,Team,changePlayerTeam,"Game:changePlayerTeam(uin, teamid)","uin:number, teamid:number",ErrorCode.OK,改变玩家队伍,"local result = Team:changePlayerTeam(uin, teamid)"
100001,10,道具管理,Item,getItemName,Item:getItemName(itemid),itemid:number,"ErrorCode.OK, name:string",获取道具名称,local result = Item:getItemName(itemid)
100002,10,道具管理,Item,getItemId,Item:getItemId(objid),objid:number,"ErrorCode.OK, itemid:number",获取物品ID,local result = Item:getItemId(objid)
100003,10,道具管理,Item,getDropItemNum,Item:getDropItemNum(objid),objid:number,"ErrorCode.OK, itemnum:number",获取掉落物数量,local result = Item:getDropItemNum(objid)
110001,11,背包管理,BackPack,getBackpackBarIDRange,Backpack:getBackpackBarIDRange(bartype),bartype:number背包类型，快捷栏、存储栏、装备栏,"ErrorCode.OK, begid:number道具格起始ID, endid:number道具格末尾ID",获取道具背包栏ID范围(起始ID~结束ID),local result = Backpack:getBackpackBarIDRange(bartype)
110002,11,背包管理,BackPack,getBackpackBarSize,Backpack:getBackpackBarSize(bartype),bartype:number,"ErrorCode.OK, size:number",获取道具背包栏大小,local result = Backpack:getBackpackBarSize(bartype)
110003,11,背包管理,BackPack,setGridItem,"Backpack:setGridItem(playerid, gridid, itemid, num, durability)","playerid:number, gridid:number道具格ID, itemid:number, num:number默认1, durability:number耐久度，默认满耐久",ErrorCode.OK,设置背包格道具,"local result = Backpack:setGridItem(playerid, gridid, itemid, num, durability)"
110004,11,背包管理,BackPack,removeGridItem,"Backpack:removeGridItem(playerid, gridid, num)","playerid:number, gridid:number, num:number默认全部道具",ErrorCode.OK,移除背包格内一定数量道具，通过道具格移除，默认全部移除,"local result = Backpack:removeGridItem(playerid, gridid, num)"
110005,11,背包管理,BackPack,removeGridItemByItemID,"Backpack:removeGridItemByItemID(playerid, itemid, num)","playerid:number, itemid:number, num:number默认全部道具","ErrorCode.OK, num:number成功移除数量",移除背包内一定数量道具，通过道具ID移除，默认全部移除,"local result = Backpack:removeGridItemByItemID(playerid, itemid, num)"
110006,11,背包管理,BackPack,clearPack,"Backpack:clearPack(playerid, bartype)","playerid:number, bartype:number背包类型",ErrorCode.OK,清空指定背包栏,"local result = Backpack:clearPack(playerid, bartype)"
110007,11,背包管理,BackPack,clearAllPack,Backpack:clearAllPack(playerid),playerid:number,,清空全部背包(包含背包栏、快捷栏、装备栏),local result = Backpack:clearAllPack(playerid)
110008,11,背包管理,BackPack,moveGridItem,"Backpack:moveGridItem(playerid, gridsrc, griddst, num)","playerid:number, gridsrc:number, griddst:number, num:number默认全部转移",ErrorCode.OK,移动背包道具，默认全部转移,"local result = Backpack:moveGridItem(playerid, gridsrc, griddst, num)"
110009,11,背包管理,BackPack,swapGridItem,"Backpack:swapGridItem(playerid, gridsrc, griddst)","playerid:number, gridsrc:number, griddst:number",ErrorCode.OK,交换背包道具,"local result = Backpack:swapGridItem(playerid, gridsrc, griddst)"
110010,11,背包管理,BackPack,enoughSpaceForItem,"Backpack:enoughSpaceForItem(playerid, itemid, num)","playerid:number, itemid:number, num:number默认1",ErrorCode.OK,背包(包含背包栏、快捷栏)是否有足够的空间存放一定数量的道具,"local result = Backpack:enoughSpaceForItem(playerid, itemid, num)"
110011,11,背包管理,BackPack,calcSpaceNumForItem,"Backpack:calcSpaceNumForItem(playerid, itemid)","playerid:number, itemid:number","ErrorCode.OK, num:number",计算背包(包含背包栏、快捷栏)能存放的道具剩余总数量,"local result = Backpack:calcSpaceNumForItem(playerid, itemid)"
110012,11,背包管理,BackPack,getBackpackBarValidList,"Backpack:getBackpackBarValidList(playerid, bartype)","playerid:number, bartype:number背包类型","ErrorCode.OK, num:number数量, array:table背包格ID数组",获取道具背包栏有效格ID列表(道具已存在)，背包格ID数组,"local result = Backpack:getBackpackBarValidList(playerid, bartype)"
110013,11,背包管理,BackPack,getBackpackBarItemList,"Backpack:getBackpackBarItemList(playerid, bartype)","playerid:number, bartype:number背包类型","ErrorCode.OK, num:number数量, arr:table道具ID数组",获取道具背包栏已拥有道具，道具ID数组,"local result = Backpack:getBackpackBarItemList(playerid, bartype)"
110014,11,背包管理,BackPack,hasItemByBackpackBar,"Backpack:hasItemByBackpackBar(playerid, bartype, itemid)","playerid:number, bartype:number, itemid:number",ErrorCode.OK,检测背包是否持有某个道具,"local result = Backpack:hasItemByBackpackBar(playerid, bartype, itemid)"
110015,11,背包管理,BackPack,getItemNumByBackpackBar,"Backpack:getItemNumByBackpackBar(playerid, bartype, itemid)","playerid:number, bartype:number, itemid:number","ErrorCode.OK, num:number道具总数量, arr:table道具格ID数组",获取背包持有某个道具总数量，同时返回装有道具的背包格数组,"local result = Backpack:getItemNumByBackpackBar(playerid, bartype, itemid)"
110016,11,背包管理,BackPack,getGridItemID,"Backpack:getGridItemID(playerid, gridid)","playerid:number, gridid:number","ErrorCode.OK, itemid:number, num:number",获取背包格道具ID和数量,"local result = Backpack:getGridItemID(playerid, gridid)"
110017,11,背包管理,BackPack,getGridItemName,"Backpack:getGridItemName(playerid, gridid)","playerid:number, gridid:number","ErrorCode.OK, name:string",获取背包格道具名称,"local result = Backpack:getGridItemName(playerid, gridid)"
110018,11,背包管理,BackPack,getGridStack,"Backpack:getGridStack(playerid, gridid)","playerid:number, gridid:number","ErrorCode.OK, num:number, maxstack:number",获取背包格道具数量和最大堆叠数,"local result = Backpack:getGridStack(playerid, gridid)"
110019,11,背包管理,BackPack,getGridDurability,"Backpack:getGridDurability(playerid, gridid)","playerid:number, gridid:number","ErrorCode.OK, durcur:number, durmax:number",获取背包格道具耐久度和最大耐久度,"local result = Backpack:getGridDurability(playerid, gridid)"
110020,11,背包管理,BackPack,getGridEnchantList,"Backpack:getGridEnchantList(playerid, gridid)","playerid:number, gridid:number","ErrorCode.OK, num:number数量, arr:table附魔ID数组",获取背包格道具附魔，返回附魔id数组,"local result = Backpack:getGridEnchantList(playerid, gridid)"
110021,11,背包管理,BackPack,getGridToolType,"Backpack:getGridToolType(playerid, gridid)","playerid:number, gridid:number","ErrorCode.OK, type:number工具类型",获取背包道具工具类型(<0表示非工具),"local result = Backpack:getGridToolType(playerid, gridid)"
110022,11,背包管理,BackPack,addItem,"Backpack:addItem(playerid, itemid, num)","playerid:number, itemid:number, num:number","ErrorCode.OK, successNum:number成功添加的数量",添加道具到背包,"local result = Backpack:addItem(playerid, itemid, num)"
110023,11,背包管理,BackPack,discardItem,"Backpack:discardItem(playerid, gridid, num)","playerid:number, gridid:number, num:number",ErrorCode.OK,丢弃背包道具,"local result = Backpack:discardItem(playerid, gridid, num)"
110024,11,背包管理,BackPack,getGridNum,"Backpack:getGridNum(playerid, gridid)","playerid:number, gridid:number",ErrorCode.OK,获取背包某个格子的道具数量,"local result = Backpack:getGridNum(playerid, gridid)"
120001,12,小地图管理,Mapmark,newShape,"MapMark:newShape(type, isshow, r, g, b)","type:number, isshow:boolean, r:number, g:number, b:number","ErrorCode.OK, shapeid:number",新增一个形状(线，矩形，圆形),"local result = MapMark:newShape(type, isshow, r, g, b)"
120002,12,小地图管理,Mapmark,deleteShape,MapMark:deleteShape(shapeid),shapeid:number,ErrorCode.OK,删除一个形状,local result = MapMark:deleteShape(shapeid)
120003,12,小地图管理,Mapmark,setShapeColor,"MapMark:setShapeColor(shapeid, r, g, b)","shapeid:number, r:number, g:number, b:number",ErrorCode.OK,设置形状颜色,"local result = MapMark:setShapeColor(shapeid, r, g, b)"
120004,12,小地图管理,Mapmark,showShape,"MapMark:showShape(shapeid, showflag)","shapeid:number, showflag:boolean",ErrorCode.OK,设置形状显示or隐藏,"local result = MapMark:showShape(shapeid, showflag)"
120005,12,小地图管理,Mapmark,updateLine,"MapMark:updateLine(shapeid, sx, sz, ex, ez)","shapeid:number, sx:number, sz:number, ex:number, ez:number",ErrorCode.OK,更新形状(线形)，传入起始坐标和末尾坐标,"local result = MapMark:updateLine(shapeid, sx, sz, ex, ez)"
120006,12,小地图管理,Mapmark,updateRectangle,"MapMark:updateRectangle(shapeid, sx, sz, w, h)","shapeid:number, sx:number, sz:number, w:number, h:number",ErrorCode.OK,更新形状(矩形)，传入起始坐标和尺寸,"local result = MapMark:updateRectangle(shapeid, sx, sz, w, h)"
120007,12,小地图管理,Mapmark,updateCircle,"MapMark:updateCircle(shapeid, cx, cz, r)","shapeid:number, cx:number, cz:number, r:number",ErrorCode.OK,更新形状(圆形)，传入圆心坐标和半径,"local result = MapMark:updateCircle(shapeid, cx, cz, r)"
130001,13,出生点管理,Spawnport,getSpawnPoint,Spawnport:getSpawnPoint(),nil,"ErrorCode.OK, x:number, y:number, z:number",获取出生点位置,local result = Spawnport:getSpawnPoint()
130002,13,出生点管理,Spawnport,getChunkValidSpawnPos,"Spawnport:getChunkValidSpawnPos(x, y, z)","x:number, y:number, z:number","ErrorCode.OK, x:number, y:number, z:number",获取区块可以作为出生点的位置，传入区块内任一方块位置,"local result = Spawnport:getChunkValidSpawnPos(x, y, z)"
