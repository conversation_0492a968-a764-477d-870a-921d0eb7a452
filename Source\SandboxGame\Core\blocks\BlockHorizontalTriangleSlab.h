
#ifndef __BLOCK_HORIZONTAL_TRIANGLE_SLAB_H__
#define __BLOCK_HORIZONTAL_TRIANGLE_SLAB_H__

#include "BlockWholeTriangle.h"

class HorizontalTriangleHalfSlabMaterial : public WholeTriangleMaterial //tolua_exports
{ //tolua_exports
	//typedef CubeBlockMaterial Super;
	DECLARE_SCENEOBJECTCLASS(HorizontalTriangleHalfSlabMaterial)
public:
	HorizontalTriangleHalfSlabMaterial() {}
	virtual ~HorizontalTriangleHalfSlabMaterial() {}
	//tolua_begin

	void initHalfFaceVertData();
	virtual void initVertData();
	virtual void initHalfWholeFaceVertData();
	virtual void initTriangleFaceVertData();
	virtual void initSlantFaceVertData();
	virtual void initTurnSlantFaceVertData();
	virtual void initPhyModelData();

	virtual void dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata=0, BLOCK_MINE_TYPE droptype=BLOCK_MINE_NOTOOL, float chance=1.0f, int uin = -1) override;
	virtual int getPhisicMeshVerts(BaseSection* psection, const WCoord& blockpos, dynamic_array<Rainbow::Vector3f>& verts, dynamic_array<UInt16>& idxs);


	BLOCK_RENDERTYPE_T GetAttrRenderType() const;/* 渲染类型 */
	virtual void onBlockPlacedBy(World* pworld, const WCoord& blockpos, IClientPlayer* player);
	virtual void createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh) override;
	virtual SectionMesh* createBlockProtoMesh(int protodata = 0);
	virtual void createCollideData(CollisionDetect* coldetect, World* pworld, const WCoord& blockpos);
	virtual bool coverNeighbor(int curblockdata, SolidBlockMaterial* neighbor, int neighbor_data, DirectionType dir) override;
	virtual BlockMaterial::BlockType BlockTypeId() { return BlockMaterial::BlockType::BlockType_HorizontalHalfSlantBlock; }
private:
	virtual float getBlockHeight(int blockdata);
protected:

	static dynamic_array<dynamic_array<BlockGeomVert>>& m_mHalfWholeFace();
	static dynamic_array<dynamic_array<BlockGeomVert>>& m_mHalfFace();

public:
	class BlockInstance : public Super::BlockInstance
	{
		DECLARE_SCENEOBJECTCLASS(BlockInstance)
	public:
		static MNSandbox::ReflexClassParam<BlockInstance, int> R_Dir;
	};
	virtual MNSandbox::AutoRef<BlockMaterial::BlockInstance> GetBlockInstance() override
	{
		return HorizontalTriangleHalfSlabMaterial::BlockInstance::NewInstance();
	}
}; //tolua_exports


class HorizontalTriangleSlabMaterial : public HorizontalTriangleHalfSlabMaterial //tolua_exports
{ //tolua_exports
	//typedef CubeBlockMaterial Super;
	DECLARE_SCENEOBJECTCLASS(HorizontalTriangleSlabMaterial)
public:
	HorizontalTriangleSlabMaterial() {}
	virtual ~HorizontalTriangleSlabMaterial() {}
	//tolua_begin

	virtual void initSlantFaceVertData();
	virtual void initTriangleFaceVertData();
	virtual void initTurnSlantFaceVertData();
	virtual void initPhyModelData();
	virtual SectionMesh* createBlockProtoMesh(int protodata = 0) override;
	virtual void dropBlockAsItem(World* pworld, const WCoord& blockpos, int blockdata = 0, BLOCK_MINE_TYPE droptype = BLOCK_MINE_NOTOOL, float chance = 1.0f, int uin = -1) override;
	virtual int getPhisicMeshVerts(BaseSection* psection, const WCoord& blockpos, dynamic_array<Rainbow::Vector3f>& verts, dynamic_array<UInt16>& idxs);

	virtual void createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh) override;
	virtual void createCollideData(CollisionDetect* coldetect, World* pworld, const WCoord& blockpos);
	virtual bool coverNeighbor(int curblockdata, SolidBlockMaterial* neighbor, int neighbor_data, DirectionType dir) override;
	virtual BlockMaterial::BlockType BlockTypeId() { return BlockMaterial::BlockType::BlockType_HorizontalSlantBlock; }

private:
	virtual float getBlockHeight(int blockdata);
public:
	class BlockInstance : public Super::BlockInstance
	{
		DECLARE_SCENEOBJECTCLASS(BlockInstance)
	public:
		static MNSandbox::ReflexClassParam<BlockInstance, int> R_Dir;
	};
	virtual MNSandbox::AutoRef<BlockMaterial::BlockInstance> GetBlockInstance() override
	{
		return HorizontalTriangleSlabMaterial::BlockInstance::NewInstance();
	}
}; //tolua_exports
#endif