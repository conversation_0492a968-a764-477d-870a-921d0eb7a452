#include "TeamComponent.h"
#include "ClientPlayer.h"
#include "ClientMob.h"
#include "GameMode.h"
#include "WorldManager.h"
#include "PlayerAttrib.h"
#include "ActionAttrStateComponent.h"
#include "AIFollowDirection.h"
#include "SandboxEventDispatcherManager.h"
#include "ClientInfoProxy.h"
#include "ActorBodyUIComponent.h"

IMPLEMENT_COMPONENTCLASS(TeamComponent)


TeamComponent::TeamComponent():m_TeamID(0)
{

}

void TeamComponent::setTeam(int id, bool ResetAttr)
{
	/*if (!GetOwner()) return ;
	ActorLiving* m_owner = dynamic_cast<ActorLiving*>(GetOwner());
	if (!m_owner) return ;
	m_TeamID = id;
	m_owner->setHPProgressDirty();
	ClientMob* pMob = dynamic_cast<ClientMob*>(m_owner);
	if (pMob)
	{
		auto pAITask = pMob->getAITask();
		if (pAITask)
		{
			AIFollowDirection* p = static_cast<AIFollowDirection*>(pAITask->getTaskAI<AIFollowDirection>());
			if (p)
			{
				p->updateTeamid();
			}
		}
	}*/
	

}

bool TeamComponent::isSameTeam(ActorLiving *target)
{
	if (target == NULL)
		return false;

	return m_TeamID == target->getTeam() && m_TeamID > 0;
}

void TeamComponent::addGameScoreByRule(int ruleid, int num)// = 1
{

}

IMPLEMENT_COMPONENTCLASS(MobTeamComponent)

MobTeamComponent::MobTeamComponent():TeamComponent()
{

}

bool MobTeamComponent::isSameTeam(ActorLiving *target)
{
	if (!GetOwner()) return NULL;
	m_ownerMob = dynamic_cast<ClientMob*>(GetOwner());
	if (!m_ownerMob) return NULL;
	if(m_ownerMob->getTamed())
	{
		ClientPlayer *owner = m_ownerMob->getTamedOwner();
		if(target == owner)
		{
			return true;
		}

		if(owner) return owner->isSameTeam(target);
	}

	return TeamComponent::isSameTeam(target);
}

void MobTeamComponent::addGameScoreByRule(int ruleid, int num)// = 1
{
	GameMode *gmaker = static_cast<GameMode*>(GetWorldManagerPtr()->m_RuleMgr);
	if(gmaker)
	{
		int s = int(gmaker->getRuleOptionVal((GAMEMAKER_RULE)ruleid)*num);

		gmaker->addTeamScore(m_TeamID, s);
	}	
}

IMPLEMENT_COMPONENTCLASS(PlayerTeamComponent)

PlayerTeamComponent::PlayerTeamComponent():TeamComponent(), m_mainteam(0)
{

}

void PlayerTeamComponent::onChangeTeam(int oldteamid)
{
	/*if (!GetOwner()) return;
	m_ownerPlayer = dynamic_cast<ClientPlayer*>(GetOwner());
	int opWay = m_ownerPlayer->getOPWay();
	if ((opWay == PLAYEROP_WAY_FOOTBALLER || opWay == PLAYEROP_WAY_BASKETBALLER || opWay == PLAYEROP_WAY_PUSHSNOWBALL) && !m_ownerPlayer->getCatchBall())
	{
		ActorLiving* m_owner = dynamic_cast<ActorLiving*>(GetOwner());
		char effname[256];
		sprintf(effname, "%s%d", "ball_team_", oldteamid);

		ActorBody* body = m_owner->getBody();
		m_owner->stopMotion(effname);

		m_ownerPlayer->updateBallEffect();
	}*/
}

void PlayerTeamComponent::setSocTeam(const std::vector<int>& uins, int teamid,int mainteam)
{
	if (!GetOwner()) return;
	int ouin = ((ClientPlayer*)GetOwner())->getUin();
	auto find_result = std::find_if(uins.begin(), uins.end(), [ouin](int o) {
		return ouin == o;
	});
	//这个团队没有自己
	if (find_result == uins.end()) {
		LOG_WARNING("setSocTeam error");
		return;
	}

	m_uins = uins;
	m_TeamID = teamid;
	m_mainteam = mainteam;
}

bool PlayerTeamComponent::checkTeam(int uin)
{
	auto find_result = std::find_if(m_uins.begin(), m_uins.end(), [uin](int o) {
		return uin == o;
	});

	return find_result != m_uins.end();
}

void PlayerTeamComponent::AddMainTag(int x, int z, int tagindex, int tagcolor, const std::string& tagname, bool broadcast)
{
	AddMainTag(WCoord(x,0,z), tagindex, tagcolor, tagname, broadcast);
}

void PlayerTeamComponent::RemoveMainTag(int x, int z)
{
	RemoveMainTag(WCoord(x, 0, z));
}

//std::string PlayerTeamComponent::GetMainTagLua()
//{
//	//const std::unordered_map<WCoord, uint32_t, WCoordHashCoder>& maintag = GetMainTag();
//	jsonxx::Array arr;
//	for (const auto& it : GetMainTag())
//	{
//		jsonxx::Object item;
//		item << "x" << it.first.x;
//		item << "y" << it.first.y;
//		item << "z" << it.first.z;
//
//		int tagindex = it.second >> 16;
//		int colorindex = it.second & 0x0000ffff;
//		item << "tagindex" << tagindex;
//		item << "colorindex" << colorindex;
//
//		arr.import(item);
//	}
//
//	return arr.json();
//}

void PlayerTeamComponent::AddMainTag(const WCoord& pos, int tagindex, int tagcolor, const std::string& tagname, bool broadcast)
{
	if (!GetOwner()) return;
	int ouin = ((ClientPlayer*)GetOwner())->getUin();
	//只有队长可以同步标记
	if (ouin != m_mainteam) return;

	int data = tagindex << 16 | (tagcolor & 0x0000ffff);
	mainteamtag[pos] = std::make_tuple(data, tagname);
	if (!broadcast) return;
#ifndef IWORLD_SERVER_BUILD
	PB_SocTeamTagDataCH send_data;
	send_data.set_type(1);
	send_data.set_x(pos.x);
	send_data.set_z(pos.z);
	send_data.set_data(data);
	send_data.set_tagname(tagname);

	GetGameNetManagerPtr()->sendToHost(PB_SocTeamTagData_CH, send_data);
#endif
}

void PlayerTeamComponent::RemoveMainTag(const WCoord& pos)
{
	if (!GetOwner()) return;
	int ouin = ((ClientPlayer*)GetOwner())->getUin();
	//只有队长可以同步标记
	if (ouin != m_mainteam) return;

	mainteamtag.erase(pos);
#ifndef IWORLD_SERVER_BUILD
	PB_SocTeamTagDataCH send_data;
	send_data.set_type(2);
	send_data.set_x(pos.x);
	send_data.set_z(pos.z);

	GetGameNetManagerPtr()->sendToHost(PB_SocTeamTagData_CH, send_data);
#endif
}

void PlayerTeamComponent::ClearMainTag()
{
	mainteamtag.clear();
}

void PlayerTeamComponent::SynMainTags()
{
	int ouin = ((ClientPlayer*)GetOwner())->getUin();
	if (ouin == m_mainteam) return;
	ClientPlayer* iplayer = (ClientPlayer*)(g_WorldMgr->getPlayerByUin(m_mainteam));
	if (!iplayer) return;
	PlayerTeamComponent* teamc = iplayer->GetComponent<PlayerTeamComponent>();
	if (!teamc) return;
	//mainteamtag = teamc->mainteamtag;
	teamc->broadcastMainTags();
}

void PlayerTeamComponent::Clear()
{
	int ouin = ((ClientPlayer*)GetOwner())->getUin();

	//之前的队友先设置名字不能穿透
	for (const auto& it : m_uins)
	{
		if (it == ouin) continue;
		ClientPlayer* iplayer = (ClientPlayer*)(g_WorldMgr->getPlayerByUin(it));
		if (!iplayer) continue;
#ifndef IWORLD_SERVER_BUILD
		iplayer->changeSocTeamConfig(false);
#endif // !IWORLD_SERVER_BUILD
		PlayerTeamComponent* teamc = iplayer->GetComponent<PlayerTeamComponent>();
		if (!teamc) continue;
		//teamc->mainteamtag.clear();
		teamc->m_uins.clear();
		teamc->m_mainteam = 0;
		m_TeamID = 0;
	}

	//mainteamtag.clear();
	m_uins.clear();
	m_mainteam = 0;
	m_TeamID = 0;
}

void PlayerTeamComponent::UpdataTeam()
{
#ifndef IWORLD_SERVER_BUILD
	std::string socteaminfo = GetClientInfoProxy()->getSocTeamInfo();
	jsonxx::Object socteamjson;
	socteamjson.parse(socteaminfo);
	std::vector<int> teamuins;
	PB_SOCTEAMDATA socteamdata;
	if (socteamjson.has<jsonxx::Array>("uins"))
	{
		jsonxx::Array uins = socteamjson.get<jsonxx::Array>("uins");
		for (int i = 0; i < uins.num; i++)
		{
			int itemuin = uins.get<jsonxx::Number>(i);
			teamuins.push_back(itemuin);
			socteamdata.add_uins(itemuin);
		}
	}

	int teammainuin = 0;
	if (socteamjson.has<jsonxx::Number>("mainuin"))
	{
		int mainuin = socteamjson.get<jsonxx::Number>("mainuin");
		teammainuin = mainuin;
		socteamdata.set_mainuin(mainuin);
	}

	int socteamid = 0;
	if (socteamjson.has<jsonxx::Number>("teamid"))
	{
		int teamid = socteamjson.get<jsonxx::Number>("teamid");
		socteamid = teamid;
		socteamdata.set_teamid(socteamid);
	}

	if (!GetOwner()) return;
	int ouin = ((ClientPlayer*)GetOwner())->getUin();
	PlayerTeamComponent* teamc = ((ClientPlayer*)GetOwner())->GetComponent<PlayerTeamComponent>();
	if (!teamc) return;
	teamc->Clear();

	setSocTeam(teamuins, socteamid, teammainuin);
	//给客户端其他player设置
	for (const auto& it : teamuins)
	{
		if (it == ouin) continue;
		ClientPlayer* iplayer = (ClientPlayer*)(g_WorldMgr->getPlayerByUin(it));
		if (!iplayer) continue;
		PlayerTeamComponent *teamc = iplayer->GetComponent<PlayerTeamComponent>();
		if (!teamc) continue;
		teamc->setSocTeam(teamuins, socteamid, teammainuin);
		iplayer->changeSocTeamConfig(true);
	}

	GetGameNetManagerPtr()->sendToHost(PB_SocTeamUpData_CH, socteamdata);
#endif
}

void PlayerTeamComponent::OnClientMainTags(const PB_SocTeamTagDataCH& datach)
{
#ifdef IWORLD_SERVER_BUILD
	//add
	int tagindex = datach.data() >> 16;
	int tagcolor = datach.data() & 0x0000ffff;
	if (datach.type() == 1) AddMainTag(datach.x(), datach.z(), tagindex, tagcolor, datach.tagname());
	if (datach.type() == 2) RemoveMainTag(datach.x(), datach.z());
	broadcastMainTags();
#endif
}

void PlayerTeamComponent::OnServerMainTags(const PB_SocTeamTagDataHC& datahc)
{
#ifndef IWORLD_SERVER_BUILD
	if (!GetOwner()) return;
	int ouin = ((ClientPlayer*)GetOwner())->getUin();
	//是队长自己
	if (ouin == m_mainteam && !mainteamtag.empty())
	{
		return;
	}

	jsonxx::Array arr;
	for (int i = 0; i < datahc.dataitems_size(); i++)
	{
		const TagDataItem& item = datahc.dataitems(i);
		int tagindex = item.data() >> 16;
		int tagcolor = item.data() & 0x0000ffff;
		if (ouin == m_mainteam) AddMainTag(item.x(), item.z(), tagindex, tagcolor, item.tagname(),false);

		jsonxx::Object item_json;
		item_json << "x" << item.x();
		item_json << "z" << item.z();
		item_json << "tagindex" << tagindex;
		item_json << "tagcolor" << tagcolor;
		item_json << "tagname" << item.tagname();
		arr.import(item_json);
	}

	MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("SocMainTeamTags",
		MNSandbox::SandboxContext(nullptr)
		.SetData_Number("code", 0)
		.SetData_String("data", arr.json())
	);
#endif
}

void PlayerTeamComponent::broadcastPosition()
{
	auto now = std::chrono::system_clock::now();
	int current_time = static_cast<uint64_t>(
		std::chrono::duration_cast<std::chrono::seconds>(
			now.time_since_epoch()
		).count()
	);
	if ((current_time - m_lastBroadcastTime) < 1) return;

	m_lastBroadcastTime = current_time;

	if (!GetOwner()) return;
	ClientPlayer* player = ((ClientPlayer*)GetOwner());
	int ouin = player->getUin();
	if (player->getWorld() == nullptr || player->getWorld()->getActorMgr() == nullptr) return;
	ClientActorMgr* actormgr = player->getWorld()->getActorMgr()->ToCastMgr();
	if (!actormgr) return;

	PB_SocTeamPositions senddata;
	bool issend = false;
	for (const auto& it : m_uins)
	{
		//不用发自己的
		if (ouin == it) continue;
		ClientPlayer* itemplayer = actormgr->findPlayerByUin(it);
		if (!itemplayer) continue;
		issend = true;
		const WCoord& playerpos = itemplayer->getPosition();

		PB_ItemSocTeam *ItemSocTeam = senddata.add_teams();
		ItemSocTeam->set_uin(itemplayer->getUin());
		ItemSocTeam->set_rotyaw(itemplayer->getFaceYaw() + 180);

		PB_Vector3 *sendpos = ItemSocTeam->mutable_pos();
		sendpos->set_x(playerpos.x);
		sendpos->set_y(playerpos.y);
		sendpos->set_z(playerpos.z);

		//ItemSocTeam->set_name(itemplayer->GetName());
	}

	if (!issend) return;

	GetGameNetManagerPtr()->sendToClient(ouin,PB_SocTeamPositions_HC, senddata);
}

void PlayerTeamComponent::broadcastMainTags()
{
	if (!GetOwner()) return;
	ClientPlayer* player = ((ClientPlayer*)GetOwner());
	int ouin = player->getUin();
	if (player->getWorld() == nullptr || player->getWorld()->getActorMgr() == nullptr) return;
	ClientActorMgr* actormgr = player->getWorld()->getActorMgr()->ToCastMgr();
	if (!actormgr) return;

	PB_SocTeamTagDataHC send_data;
	for (const auto& it : mainteamtag)
	{
		TagDataItem* dataitem = send_data.add_dataitems();
		dataitem->set_x(it.first.x);
		dataitem->set_z(it.first.z);
		dataitem->set_data(std::get<0>(it.second));
		dataitem->set_tagname(std::get<1>(it.second));
	}

	for (const auto& itemuin : m_uins)
	{
		ClientPlayer* itemplayer = actormgr->findPlayerByUin(itemuin);
		if (!itemplayer) continue;
		GetGameNetManagerPtr()->sendToClient(itemuin, PB_SocTeamTagData_HC, send_data);
	}
}

void PlayerTeamComponent::setTeam(int id, bool ResetAttr)
{
	//if (getTeam() != id)
	//	onChangeTeam(getTeam());

	//TeamComponent::setTeam(id, ResetAttr);

	//if (!GetOwner()) return;
	//m_ownerPlayer = dynamic_cast<ClientPlayer*>(GetOwner());

	//m_ownerPlayer->applyDisplayName();

	//if (GetWorldManagerPtr())
	//{
	//	std::vector<IClientPlayer *> players;
	//	GetWorldManagerPtr()->getAllPlayers(players);
	//	for (auto it = players.begin(); it != players.end(); it++)  // 客机刷新其他玩家的昵称显示等
	//	{
	//		ClientPlayer* itTemp = (*it) ? (*it)->GetPlayer() : nullptr;
	//		if (itTemp && m_ownerPlayer != itTemp)
	//		{
	//			itTemp->applyDisplayName();
	//		}
	//	}
	//}
	//if (ResetAttr)
	//{
	//	ResetPlayerAttr();
	//}
	

}

void PlayerTeamComponent::addGameScoreByRule(int ruleid, int num)// = 1
{
	/*GameMode* gmaker = static_cast<GameMode*>(GetWorldManagerPtr()->m_RuleMgr);
	if (gmaker)
	{
		m_ownerPlayer = dynamic_cast<ClientPlayer*>(GetOwner());
		int s = int(gmaker->getRuleOptionVal((GAMEMAKER_RULE)ruleid) * num);
		
		int curGameScore = m_ownerPlayer->getGameScore();
		curGameScore += s;
		if (curGameScore < 0) curGameScore = 0;
		m_ownerPlayer->setGameScore(curGameScore);

		gmaker->addTeamScore(m_TeamID, s);
	}		*/
}

void PlayerTeamComponent::ResetPlayerAttr()
{
	////设置队伍数据
	//if (GetWorldManagerPtr() && GetWorldManagerPtr()->isGameMakerRunMode())
	//{
	//	int id = getTeam();
	//	auto manager = GetWorldManagerPtr()->getBaseSettingManager();
	//	if (manager && manager->getTeamEnable(id))
	//	{
	//		if (!GetOwner()) return ;
	//		ActorLiving* m_owner = dynamic_cast<ActorLiving*>(GetOwner());
	//		if (!m_owner) return ;
	//		//初始权限
	//		unsigned int statesValue = manager->getPermitStatesValue(id);
	//		auto ActionAttrStateComp = m_owner->getActionAttrStateComponent();
	//		if (ActionAttrStateComp)
	//		{
	//			ActionAttrStateComp->setAllActionAttrState(statesValue & ENABLE_INITVALUE);
	//		}

	//		//基础属性
	//		PlayerAttrib* pAttrib = m_ownerPlayer->getPlayerAttrib();
	//		if (pAttrib) pAttrib->initPlayerBaseAttr(id);

	//		//基础模型
	//		m_ownerPlayer->setBaseModel(id);

	//		////初始物品
	//		//GameMode *makerMgr = GetWorldManagerPtr()->m_RuleMgr;
	//		//if (makerMgr == NULL) return;
	//		//makerMgr->initPlayerBaseItem(m_ownerPlayer, m_ownerPlayer->getShortcutStartIndex(), id);
	//		//makerMgr->initPlayerBaseItem(m_ownerPlayer, BACKPACK_START_INDEX, id);
	//	}
	//}
}

