# TerritoryBuffComponent 领地Buff组件

## 功能概述

TerritoryBuffComponent是一个玩家组件，负责管理玩家在领地范围内的buff状态。

## 核心功能

### 1. 授权领地Buff管理（维护材料充足）
- **Buff ID**: 3020
- **触发条件**: 玩家进入自己拥有或被授权的领地范围，且所有腐蚀方块都有足够材料进行至少一次维护
- **移除条件**: 玩家离开授权领地范围或维护材料变为不足

### 2. 授权领地Buff管理（建筑腐蚀中）
- **Buff ID**: 3021
- **触发条件**: 玩家进入自己拥有或被授权的领地范围，但至少有一个腐蚀方块缺乏维护材料
- **移除条件**: 玩家离开授权领地范围或所有方块的维护材料都变为充足

### 3. 非授权领地Buff管理  
- **Buff ID**: 3022
- **触发条件**: 玩家进入他人的领地范围（未被授权）
- **移除条件**: 玩家离开非授权领地范围

### 4. 智能位置监测
- 基于位置变化的智能检查（只有位置变化超过5个单位时才检查）
- 高性能优化：避免不必要的重复检查
- 纯Tick驱动：简单可靠的实现方式

## 使用方法

### 自动使用
组件会自动添加到所有ClientPlayer对象上，无需手动配置。

### 手动检查（Lua接口）
```lua
-- 获取玩家的TerritoryBuffComponent
local territoryBuffComp = player:GetComponent("TerritoryBuffComponent")

-- 手动检查当前位置的领地状态
territoryBuffComp:CheckCurrentTerritoryStatus()

-- 获取当前的领地buff状态
local currentBuffId = territoryBuffComp:GetCurrentTerritoryBuffId()

-- 检查是否在领地内
local isInTerritory = territoryBuffComp:IsInTerritory()

-- 检查是否在授权领地内
local isInAuthorizedTerritory = territoryBuffComp:IsInAuthorizedTerritory()
```

## 技术实现

### 核心类
- `TerritoryBuffComponent`: 主要的组件类
- `TerritoryQuery`: 领地查询接口
- `LivingAttrib`: Buff管理接口

### 关键方法
1. `CheckTerritoryAndUpdateBuff()`: 检查领地状态并更新buff
2. `AddTerritoryBuff(int buffId)`: 添加领地buff
3. `RemoveTerritoryBuff(int buffId)`: 移除领地buff
4. `ClearAllTerritoryBuffs()`: 清除所有领地buff

### 性能优化
- **位置变化检测**: 只有玩家位置变化超过5个单位时才进行领地检查
- **智能缓存**: 记录上次检查的位置，避免重复计算
- **Tick驱动**: 简单可靠的实现方式，无复杂事件依赖
- **零开销**: 玩家静止时完全不进行领地查询

## 状态管理

组件维护以下状态：
- `m_isInTerritory`: 是否在任意领地内
- `m_isInAuthorizedTerritory`: 是否在授权领地内
- `m_hasMaintenanceMaterials`: 是否有足够的维护材料
- `m_currentTerritoryBuffId`: 当前激活的领地buff ID
- `m_lastCheckedBlockPosition`: 上次检查的方块位置

## 日志输出

组件会输出以下日志信息：
- 玩家进入授权领地（材料充足）: `Player %u entered authorized territory with sufficient maintenance materials, added buff %d`
- 玩家进入授权领地（材料不足）: `Player %u entered authorized territory with insufficient maintenance materials (erosion), added buff %d`
- 玩家进入非授权领地: `Player %u entered unauthorized territory, added buff %d`
- 玩家离开领地: `Player %u left territory, cleared all territory buffs`

## 错误处理

- 如果玩家对象无效，组件会安全退出
- 如果领地查询失败，不会影响其他功能
- Buff添加/移除失败会被安全忽略

## 依赖关系

- `TerritoryManager`: 领地管理器
- `TerritoryQuery`: 领地查询接口
- `ClientPlayer`: 玩家对象
- `LivingAttrib`: 生物属性（buff管理）
