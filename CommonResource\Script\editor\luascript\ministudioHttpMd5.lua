--从游戏侧迁移过来的 微服务MD5校验方式


--增加URL参数
function ministudio_url_addParams( url_,uin )
	js_getUrlParams()
	local uin_   = uin or AccountManager:getUin();
	if  uin_ < 1000 then
		uin_ = get_default_uin();
	end

	local apiid_ = GetClientInfo():GetAppId() or "nil"	
	local ver_   = GetClientInfo():clientVersionToStr(GetClientInfo():GetClientVersion()) or "nil"
	local lang_  = get_game_lang()    or "nil"
	local cnty_  = get_game_country() or "nil"
	--for minicode
	local channel_id = isEducationalVersion and (GetClientInfo():getChannelId() or 0) or 0;
	local production_id = isEducationalVersion and (GetClientInfo():getProductionId() or 0) or 0;
	--local headInd_ = GetHeadIconIndex() or "nil"


	local long_url_;
	if  string.find( url_, '%?' ) then
		long_url_ = url_ .. '&';
	else
		long_url_ = url_ .. '?';
	end
	long_url_ = long_url_ .. "uin="      .. uin_
	                      .. "&ver="     .. ver_
						  .. (((channel_id > 0) and ("&channel_id="..channel_id)) or "")
						  .. (((production_id > 0) and ("&production_id="..production_id)) or "")	                      
						  .. "&apiid="   .. apiid_
	                      .. "&lang="    .. lang_
						  .. "&country=" .. cnty_
	local params = {
		uin = uin_,
		ver = ver_,
		apiid = apiid_,
		lang =  lang_,
		country =  cnty_,
		channel_id = channel_id > 0 and channel_id or '',
		production_id = production_id > 0 and production_id or '',
	}

	--增加安全
	if  string.sub( long_url_, 1, 1 ) == 's' and string.sub( long_url_, 3, 3 ) == "_" then
		long_url_ =  getUrlSafeInfo( long_url_ );    --增加url安全
	end

	return long_url_, params;
end


function ministudio_http_getParamMD5(tParam, useServerTime)
	local privateKey = '3dbc5f33add11d1af78ba2af365e0952'
	local md5_exclude_list = {
        log = 1,
        test = 1,
        json = 1,
        nickname = 1,
        title = 1,
        content = 1,
        md5 = 1,
        auth = 1,
		content_ctx = 1,
    }

	local tempParam = copy_table(tParam)
	tempParam.time =getServerTime()
	local s2, s2t = get_login_sign();

	local function urlEncode(s)
		s = string.gsub(s, "([^%w%.%- _])", function(c) return string.format("%%%02X", string.byte(c)) end)
	   return string.gsub(s, " ", "+")
	end

	--创建局部方法，避免使用时方法未加载出现报错
	local function urlDncode(s)
		s = string.gsub(s, '%%(%x%x)', function(h) return string.char(tonumber(h, 16)) end)
		return s
	end

	s2 = urlEncode(s2)
	tempParam.s2 = s2;
	tempParam.s2t = string.gsub(s2t, '&s2t=', '')
	tempParam.encrypt_ver = 3 -- 加密类型

	local _, morParam = url_addParams('')
	for k, v in pairs(morParam) do
		if v ~= '' then
			tempParam[k] = v
		end
	end

    local needEnctyptKey = {}
    for k, _ in pairs(tempParam) do
        if not md5_exclude_list[k] then
            needEnctyptKey[#needEnctyptKey+1] = k
        end
    end
	table.sort(needEnctyptKey, function (a, b)
		return a < b
	end)

	local md5_table = {}
	for i, v in ipairs(needEnctyptKey) do
		if tempParam[v] then
			local decode_parm = urlDncode(tempParam[v])	--防止业务层进行过Encode，所以先decode再Encode
			local encode_parm = urlEncode(decode_parm)
			table.insert(md5_table, v .. '=' .. encode_parm)
		end
	end
	local md5Str = table.concat(md5_table, '&')
	local md5 = gFunc_getmd5(md5Str .. privateKey)
	
	local allParames = {}
	for k, v in pairs(tempParam) do
		if k ~= 's2' then
		table.insert(allParames, k .. '=' .. v)
		end
	end

	local paramStr = table.concat(allParames, '&')
	-- local urlFix = md5Str .. '&md5=' .. md5
	return paramStr , md5
end