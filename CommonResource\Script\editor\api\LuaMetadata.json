{"table": {".": ["concat", "insert", "move", "pack", "remove", "sort", "unpack"]}, "string": {".": ["byte", "char", "dump", "find", "format", "gmatch", "gsub", "len", "lower", "match", "pack", "<PERSON><PERSON>", "rep", "reverse", "sub", "unpack", "upper"]}, "coroutine": {".": ["create", "isyieldable", "resume", "running", "status", "wrap", "yield"]}, "debug": {".": ["debug", "gethook", "getinfo", "getlocal", "getmetatable", "getregistry", "getupvalue", "getuservalue", "sethook", "setlocal", "setmetatable", "setupvalue", "setuservalue", "traceback", "upvalueid", "upvaluejoin"]}, "io": {".": ["close", "flush", "input", "lines", "open", "output", "popen", "read", "stderr", "stdin", "stdout", "tmpfile", "type", "write"]}, "file": {":": ["close", "flush", "lines", "read", "seek", "setvbuf", "write"]}, "math": {".": ["abs", "acos", "asin", "atan", "ceil", "cos", "deg", "exp", "floor", "fmod", "huge", "log", "max", "maxinteger", "min", "mininteger", "modf", "pi", "rad", "random", "randomseed", "sin", "sqrt", "tan", "toint<PERSON>r", "type", "ult"]}, "os": {".": ["clock", "date", "difftime", "execute", "exit", "getenv", "remove", "rename", "<PERSON><PERSON><PERSON>", "time", "tmpname"]}, "package": {".": ["config", "cpath", "loaded", "loadlib", "path", "preload", "searchers", "searchpath"]}, "utf8": {".": ["char", "char<PERSON><PERSON>n", "codepoint", "codes", "len", "offset"]}}