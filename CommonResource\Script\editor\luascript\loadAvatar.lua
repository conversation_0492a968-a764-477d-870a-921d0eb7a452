--[==[
	旧版tolua++向bindings-gererator的重构里单例table的迁移
	需自行在C++导出获取单例的静态函数
    或将创建迁移到Lua
	Created on 2021-07-05 at 17:11:29
]==]
-- function replaceSingleton()
-- 	print("replaceSingleton():");
--     -- if条件判断为新旧开关的接口。下同。
--     if ClientManager and ClientManager.getInstance then
--         _G.ClientMgr = ClientManager:getInstance();
--         -- print("replaceSingleton(): type(ClientManager) = " .. type(ClientManager));
--     end

--     if LuaInterface and LuaInterface.new then
-- 	    _G.LuaInterface = LuaInterface:new();
--     end

--     if PlatformSdkManager and PlatformSdkManager.getSingletonPtr then
-- 	    _G.SdkManager = PlatformSdkManager:getSingletonPtr();
--     end

--     if GameUI and GameUI.getInstance then
-- 	    _G.GameUI = GameUI:getInstance();
--     end
    
--     if RoomSyncResMgr and RoomSyncResMgr.getSingletonPtr then
-- 	    _G.RoomSyncResMgr = RoomSyncResMgr:getSingletonPtr();
--     end
-- end

-- if replaceSingleton then
--     replaceSingleton();
--     replaceSingleton = nil;
-- end

--本文件无法做自动更新, 因为本文件是从script.toc里面加载的
local ok, msg = pcall(function ()
    --{{{
	
   
    if not LuaInterface then return end 
    local filecache = {}
    if not LuaInterface.log then LuaInterface.log = function () end end 
    --if 'test' then LuaInterface.geturl = function () return 'http://**************:4000/' end end
    local www_dir = LuaInterface and LuaInterface.geturl and LuaInterface:geturl():gsub('/$',''):gsub('/','_'):gsub(':', '_') .. '/' or ''

    local mode = nil or 1
    local event_listeners = {}
    if mode == 0 then 
        --{{{ api 模式
        _G.ResetScreenSize = function (w, h)
            print('ResetScreenSize ', w, h)
        end 
        --}}}
    end 

    --后续账号服热更只保留热更配置文件，逻辑文件走新引擎客户端热更流程 code by:keguanqiang, time:2023/2/22
    local hotfixfiles = "online_def|account_def|"

    local ishotfixfile = function(filename)
        if string.find(filename, "res.") then --热更列表里res.下的都是配置文件
            return true;
        elseif string.find(hotfixfiles, filename.."|") then
            return true
        end

        return false;
    end

    local loadpackage = function (pkgname)
        --{{{
        LuaInterface:log(' loadpackage ' .. pkgname)
        if LuaInterface and LuaInterface.loadpackage then 
            if not filecache[pkgname] then 
                local ok, pkg = pcall(LuaInterface.loadpackage, pkgname)
                if not ok then 
                    filecache[pkgname] = 'nil'
                else
                    filecache[pkgname] = pkg
                end 
            end 
            local t = filecache[pkgname]
            if t == 'nil' then return nil end 
            return t
        end 
        --}}}
    end 

	
    --lua的调试已经迁移到Bin/DebugConfig.json 配置中 m_LuaDebugModel = 1 打开

    local StdioRoot = LuaInterface:getStdioRoot()
    local hotfixsuccess = true -- 表示热更新是不是完全成功了. 如果完全成功了,那么使用热更新的逻辑

    local ministudio_loadwwwcache = function (filename, force, notcheckhotfix)
       -- return require("luascript."..filename);
        if notcheckhotfix then
            if not force and filecache[filename] then return filecache[filename][2] end 
            local pkgname = filename:gsub('%.lua$', ''):gsub('%.', '/'):match('([^/]+)$')
            local path = 'luascript/' .. (filename:gsub('%.data$', ''):gsub('%.lua$', ''):gsub('%.', '/') .. '.lua')
            local ok = LuaInterface and LuaInterface:loadpackage(path)
            if ok then 
                filecache[filename] = {ok, gPackages[pkgname]}
                MiniLog('dzw notcheckhotfix true', filecache[filename][2])
                return filecache[filename][2]
            else
                MiniLog('loadwwwcache loadpackage error', path)
                return nil
            end 
            return nil
        else
            MiniLog('dzw notcheckhotfix false', filename)
            return ministudio_LoadWWWFile(filename, force)
        end
    end 

    local ministudio_LoadWWWFile = function (filename, force)
        --{{{
        --from cache 

        if not force and filecache[filename] then return filecache[filename][2] end 

        --from ./data/cache/{{{
        --local prefix = StdioRoot.. 'data/www/' .. www_dir .. LuaInterface:getCltVersion() .. '/'
        --filename = filename:gsub('/', '.')

        if hotfixsuccess then 
            -- load xxx.xxx.data2 {{{
            MiniLog('dzw io.open', filename)
            local f = io.open(filename, 'r')
            if f then 
                local ss = f:read('*a')
                f:close()
                --校验合法性
                if ss and #ss > 32 then 
                    local md5 = string.sub(ss, 1, 32)
                    local b64 = string.sub(ss, 33)
                    if #b64 > 0 and md5 == g_recharge_md5_related.md5sum(b64, #b64) then 
                        local str = xxtea.decrypt_unzip(b64, string.len(b64), 'b64')
                        if str then 
                            local ok, func = pcall(loadstring, str, filename)
                            if ok and func then 
                                filecache[filename] = {ok, func()}
                                MiniLog('loadwwwcache dzw', cachepath)
                                return filecache[filename][2]
                            end 
                        end
                    end 
                end 
            end 
            -- }}}

            --load xxx.xx.data{{{
            local cachepath = (filename:match('%.data$') and filename) or (filename .. '.data')
            MiniLog('loadwwwcache', cachepath)
            MiniLog('loadwwwcache dzw', cachepath)
            local f = io.open(prefix..cachepath, 'r')
            if f then 
                local b64 = f:read('*a')
                f:close()
                if string.len(b64) > 0 then 
                    local str = xxtea.decrypt_unzip(b64, string.len(b64), 'b64')
                    if str then 
                        local ok, func = pcall(loadstring, str, filename)
                        if ok and func then 
                            filecache[filename] = {ok, func()}
                            MiniLog('loadwwwcache dzw', filecache[filename][2])
                            return filecache[filename][2]
                        end 
                    end 
                end 
            else
            end 
            --}}}
        end 

        --}}}
        --from luascript {{{
        --LuaInterface:loadpackage('luascript/init.lua')
        local pkgname = filename:gsub('%.lua$', ''):gsub('%.', '/'):match('([^/]+)$')
        local path = 'luascript/' .. (filename:gsub('%.data$', ''):gsub('%.lua$', ''):gsub('%.', '/') .. '.lua')
        local ok = LuaInterface and LuaInterface:loadpackage(path)
        if ok then 
            filecache[filename] = {ok, gPackages[pkgname]}
            MiniLog('loadwwwcache dzw', filecache[filename][2])
            return filecache[filename][2]
        else
            MiniLog('loadwwwcache loadpackage error', path)
            return nil
        end 
        --}}}
        return nil
        --}}}
    end
    --}}}

    _G.ministudio_loadwwwcache = ministudio_loadwwwcache
    _G.ministudio_LoadWWWFile = ministudio_LoadWWWFile
end )

