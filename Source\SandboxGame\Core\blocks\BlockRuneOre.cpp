#include "BlockRuneOre.h"
#include "special_blockid.h"
#include "IClientPlayer.h"
#include "DefManagerProxy.h"
#include "defdata.h"
#include "world_types.h"
#include "ClientActorManager.h"
#include "OgreUtils.h"
#include "RuneDef.h"
#include "world.h"
#include "ActorManager.h"


IMPLEMENT_BLOCKMATERIAL(BlockRuneOre)

static void spawnMonster(World *pworld, const WCoord& basepos)
{
	if(!pworld)
		return;

	int monsterid = 3892;//3892:符文怪
	const MonsterDef* def = GetDefManagerProxy()->getMonsterDef(monsterid);
	if (!def)
		return;
	ActorManager *actormgr = dynamic_cast<ActorManager*>(pworld->getActorMgr());
	if(!actormgr)
		return;
	//40%概率产生
	int pro = GenRandomInt(0, 100);
	if(pro > 40)
		return;
	int num = 3;//GenRandomInt(3, 5);
	WCoord createPos = BlockBottomCenter(basepos);
	for (int i = 0; i < num; i++)
		actormgr->spawnMob(createPos, monsterid, false, false);
	//ClientMob *mob = 
	//if(mob) mob->playSaySound();
}
/*
根据镐的材料不同，掉落各个等级的符文石概率也不同
- 铜镐：低级75%，中级20%，高级5%   100
- 铁镐：低级40%，中级45%，高级15%  100
- 钛镐：低级30%，中级40%，高级30%  100
- 钻头：低级15%，中级30%，高级55%  100
- 符文怪：产生概率40%，数量3-5只
*/
void BlockRuneOre::dropBlockAsItemWithToolId(World *pworld, const WCoord &blockpos, int blockdata, BLOCK_MINE_TYPE droptype, float chance, int useToolId, int uin)
{
	const ToolDef *tooldef = GetDefManagerProxy()->getToolDef(useToolId);
	if (tooldef && tooldef->Type == 2 && tooldef->Level >= 2){//铜镐以上 才可以掉符文石
		int proArray[] = { 75,20,5,  40,45,15,  30,40,30,  15,30,55 };
		int startIndex = (tooldef->Level - 2) * 3;
		if(startIndex > 9) startIndex = 9;
		int *base = ((int*) proArray) + startIndex;
		int index = SelectFromOddsArray(base,3);
		if(index >= 0 && index <= 2){//0,1,2
			int category = getRuneStoneCategory(getBlockResID());
			if(category != RuneStoneCategoryNone){
				int itemid = getUnAuthItemIDByRuneLevelAndCategory( index + 1,category);
				doDropItem(pworld, blockpos, itemid); //
			}
		}
		
	}
	spawnMonster(pworld, blockpos);
}