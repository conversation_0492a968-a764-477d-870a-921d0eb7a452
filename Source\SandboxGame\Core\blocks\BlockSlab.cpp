
#include "BlockSlab.h"
#include "BlockMaterialMgr.h"
#include "section.h"
#include "SectionMesh.h"
#include "BlockGeom.h"
#include "world.h"
#include "DefManagerProxy.h"
#include "WorldManager.h"
#include "WeatherManager.h"
//#include "OgreMaterial.h"

IMPLEMENT_SCENEOBJECTCLASS(SlabMaterial)
//IMPLEMENT_BLOCKINSTANCE(SlabMaterial)
using namespace MINIW;

void SlabMaterial::init(int resid)
{
	CubeBlockMaterial::init(resid);
	SetToggle(BlockToggle_IsOpaqueCube, false);
	SetAttrRenderType(BLOCKRENDER_MODEL);

	if(m_LoadOnlyLogic) return;

	char texname[256];
	RenderBlockMaterial* topmtl = nullptr, * sidemtl = nullptr, * bottommtl = nullptr;

	sprintf(texname, "%s_top", GetBlockDef()->Texture1.c_str());
	topmtl = g_BlockMtlMgr.createRenderMaterial(texname, GetBlockDef());
	if(topmtl == NULL)
	{
		topmtl = g_BlockMtlMgr.createRenderMaterial(GetBlockDef()->Texture1.c_str(), GetBlockDef(), GETTEX_WITHDEFAULT);

		sidemtl = topmtl;
		sidemtl->AddRef();
		bottommtl = topmtl;
		bottommtl->AddRef();
	}
	else
	{
		if(!GetBlockDef()->Texture2.empty()) sidemtl = g_BlockMtlMgr.createRenderMaterial(GetBlockDef()->Texture2.c_str(), GetBlockDef(), GETTEX_WITHDEFAULT);
		else
		{
			sprintf(texname, "%s_side", GetBlockDef()->Texture1.c_str());
			sidemtl = g_BlockMtlMgr.createRenderMaterial(texname, GetBlockDef(), GETTEX_WITHDEFAULT);
		}

		sprintf(texname, "%s_bottom", GetBlockDef()->Texture1.c_str());
		bottommtl = g_BlockMtlMgr.createRenderMaterial(texname, GetBlockDef());
		if(bottommtl == NULL)
		{
			bottommtl = topmtl;
			bottommtl->AddRef();
		}
	}
	sprintf(texname, "ice_snow_top");
	snowTopMtl = g_BlockMtlMgr.createRenderMaterial(texname, GetBlockDef());
	sprintf(texname, "%s_snow_side", GetBlockDef()->Texture1.c_str());
	snowSideMtl = g_BlockMtlMgr.createRenderMaterial(texname, GetBlockDef());
	if (!snowTopMtl)
	{
		snowTopMtl = topmtl;
		snowTopMtl->AddRef();
	}
	if (!snowSideMtl)
	{
		snowSideMtl = sidemtl;
		snowSideMtl->AddRef();
	}
	
	
	setFaceMtl(DIR_POS_X, sidemtl);
	setFaceMtl(DIR_NEG_Z, sidemtl);
	setFaceMtl(DIR_NEG_X, sidemtl);
	setFaceMtl(DIR_POS_Z, sidemtl);

	setFaceMtl(DIR_POS_Y, topmtl);
	setFaceMtl(DIR_NEG_Y, bottommtl);

	ENG_RELEASE(sidemtl);
	ENG_RELEASE(topmtl);
	ENG_RELEASE(bottommtl);
}
SlabMaterial::SlabMaterial()
{
	snowSideMtl=NULL;
	snowTopMtl=NULL;
}

SlabMaterial::~SlabMaterial()
{
	ENG_RELEASE(snowSideMtl);
	ENG_RELEASE(snowTopMtl);
}

void SlabMaterial::initGeomName()
{
	m_geomName = m_Def && !m_Def->Texture2.empty() ? m_Def->Texture2.c_str() : "cube";
}

int SlabMaterial::getPlaceBlockData(World *pworld, const WCoord &blockpos, DirectionType face, float hitptx, float hitpty, float hitptz, int def_blockdata)
{
	if(face == DIR_NEG_Y) return 3;
	else if(face == DIR_POS_Y) return 4;
	else return hitpty>0.5f? 4:3;
}

bool SlabMaterial::hasSolidTopSurface(int blockdata)
{
	if(blockdata == 0) return false;
	else return true;
}

float SlabMaterial::getBlockHeight(int blockdata)
{
	if(blockdata == 0)
	{
		return 0.49f;
	}
	else if(blockdata == 1)
	{
		return -0.51f;
	}
	else if (blockdata == 3)
	{
		return 0.25f;
	}
	else if (blockdata == 4)
	{
		return -0.25f;
	}
	else if (blockdata == 5)
	{
		return 0.75f;
	}
	else if (blockdata == 6)
	{
		return -0.75f;
	}
	else return 1.0f;
}

bool SlabMaterial::canAttachedToMecha(int curblockdata, DirectionType dir, int neighbor_id, int neighbor_data)
{
	if(dir == DIR_NEG_Y)
	{
		return curblockdata != 1 && curblockdata != 4 && curblockdata != 6;
	}
	else if(dir == DIR_POS_Y)
	{
		return curblockdata != 0 && curblockdata != 3 && curblockdata != 5;
	}
	else return true;
}

void SlabMaterial::dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata, BLOCK_MINE_TYPE droptype, float chance, int uin/* = -1 */)
{
	CubeBlockMaterial::dropBlockAsItem(pworld, blockpos, blockdata, droptype, chance, uin);
	int exCount = blockdata == 2 ? 3 : blockdata > 4 ? 2 : blockdata < 2 ? 1 : 0;
	for (int i = 0; i < exCount; i++)
	{
		CubeBlockMaterial::dropBlockAsItem(pworld, blockpos, blockdata, droptype, chance, uin);
	}
}

void SlabMaterial::createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh)
{
#ifndef IWORLD_SERVER_BUILD
	if (isUseCustomModel())
	{
		createBlockMeshForCustomModel(data, blockpos, poutmesh);
	}
	else
	{
		createBlockMeshAngle(data, blockpos, poutmesh);
	}
#endif
}

void SlabMaterial::createBlockMeshAngle(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh)
{
	auto psection = data.m_SharedSectionData;

	FaceVertexLight faceVertexLight;
	auto pblock = psection->getBlock(blockpos);
	const BiomeDef* biome = GetDefManagerProxy()->getBiomeDef(1);
	bool isSnowing = false;//积雪lock
	int weather = 0;
	//通过world获取chunk已经不保险了，chunk可能是野指针造成崩溃，改成通过BuildSectionMeshData里面的m_SharedChunkData
	const BiomeDef* boomedef = data.m_SharedChunkData ? data.m_SharedChunkData->getBiome(blockpos.x, blockpos.z) : nullptr;
	int biomeGroupID = boomedef ? boomedef->BiomeGroupID : 1;
	if (data.m_World && data.m_World->getWeatherMgr())
	{
		weather = data.m_World->getWeatherMgr()->getBiomeGroupWeather(biomeGroupID);
	}
	if (weather == GROUP_BLIZZARD_WEATHER || weather == GROUP_SNOW_WEATHER)
	{
		isSnowing = true;
		WCoord blockpos_tmp = blockpos + psection->getOrigin();
		int y = data.m_World->getTopHeight(blockpos_tmp.x, blockpos_tmp.z);
		if ((y - 1) != blockpos_tmp.y)
		{
			isSnowing = false;
		}
	}
	for (int d = 0; d < 6; d++)
	{
		DirectionType dir = (DirectionType)d;
		bool isNoClip = false;
		if (checkBlockMeshIsBuild(data, blockpos, d, isNoClip))
		{
			bool flipQuad = psection->getCubeFaceVertexLight(blockpos, dir, faceVertexLight);

			BlockColor facecolor(255, 255, 255, 0);
			RenderBlockMaterial* pmtl = NULL;
			if (isSnowing == true)
			{
				if (dir <= DIR_POS_Z) 
				{
					pmtl = snowSideMtl;
				}
				else
				{
					if (dir == DIR_POS_Y)
					{
						pmtl = snowTopMtl;
					}
					else if (dir == DIR_NEG_Y)
					{
						pmtl = getFaceMtl(biome, dir, pblock.getData(), facecolor);
					}
				}
			}

			if (pmtl == NULL)
			{
				pmtl = getFaceMtl(biome, dir, pblock.getData(), facecolor);
			}
			
			if (pmtl == NULL) continue;
			SectionSubMesh* psubmesh = poutmesh->getSubMesh(pmtl);

			BlockGeomMeshInfo mesh;
			if (0 == BlockMaterialMgr::m_BlockShape)
			{
				getBlockMeshAngleData(data, blockpos, mesh, d, psubmesh && !psubmesh->IsIgnoreTileUV(), pmtl, isNoClip);
				if (psubmesh)
				{
					psubmesh->addGeomFace(mesh, &blockpos);
				}
			}
			else
			{
				Block pblock = psection->getBlock(blockpos);
				int curblockdata = pblock.getData();
				float blockheight = getBlockHeight(curblockdata);
				DirectionType specialdir = DIR_NOT_INIT;
				if (blockheight > 0 && blockheight < 1.0f) specialdir = DIR_POS_Y;
				else if (blockheight<0 && blockheight>-1.0f) specialdir = DIR_NEG_Y;
				if (getGeom() == NULL) continue;
				if (blockheight == 1.0f) getGeom()->getFaceVerts(mesh, dir, flipQuad);
				else getGeom()->getFaceVerts(mesh, dir, blockheight, 1, DIR_NEG_Z, 0, nullptr, 0, flipQuad);
				if (psubmesh) psubmesh->addGeomFaceLight(mesh, &blockpos, faceVertexLight, &facecolor, pmtl->getUVTile());
			}
		}
	}
}


char* SlabMaterial::getPhisicMeshBit(BaseSection* psection, const WCoord& blockpos)
{
	if (isUseCustomModel())
	{
		int idbuf[32];
		int dirbuf[32];
		int ngeom = getBlockGeomID(idbuf, dirbuf, &psection->GetSharedSectionData(), blockpos, psection->getWorld());
		BlockGeomMeshInfo meshinfo;
		Block pblock = psection->GetSharedSectionData().getBlock(blockpos);
		if (!getGeom())
			return NULL;
		int dir = dirbuf[0] & 0xffff;
		int mirrortype = (dirbuf[0] >> 16) & 3;

		char key[16];
		sprintf(key, "%d_%d_%d", idbuf[0], dir, mirrortype);
		char* cache = getGeom()->getPhisicMeshBitBaseCache(key);
		if (cache)
			return cache;
		if (mirrortype > 0) getGeom()->getFaceVerts(meshinfo, idbuf[0], 1.0f, 0, dir, mirrortype);
		else getGeom()->getFaceVerts(meshinfo, idbuf[0], 1.0f, 0, dir);
		std::vector<BlockGeomMeshInfo*> infos;
		infos.push_back(&meshinfo);
		return getGeom()->getPhisicMeshBitBase(key, infos);
	}
	else
	{
		//		float verts_light[8];
		Block pblock = psection->getBlock(blockpos);
		int curblockdata = pblock.getData();

		float blockheight = getBlockHeight(curblockdata);
		DirectionType specialdir = DIR_NOT_INIT;
		if (blockheight > 0 && blockheight < 1.0f) specialdir = DIR_POS_Y;
		else if (blockheight<0 && blockheight>-1.0f) specialdir = DIR_NEG_Y;

		char key[32];
		sprintf(key, "%f", blockheight);
        if (getGeom())
        {
            char* cache = getGeom()->getPhisicMeshBitBaseCache(key);
            if (cache)
                return cache;
        }

		std::vector<BlockGeomMeshInfo*> infos;
		float min_dist = Rainbow::MAX_FLOAT;
		float dist = 0;
		const BiomeDef* biome = GetDefManagerProxy()->getBiomeDef(1);
		for (int d = 0; d < 6; d++)
		{
			DirectionType dir = (DirectionType)d;
			//if (m_DisableCoverFaceOpt || dir == specialdir )
			{
				if (getGeom() == NULL) continue;
				BlockGeomMeshInfo* meshinfo = ENG_NEW_LABEL(BlockGeomMeshInfo, kMemTempAlloc);
				if (blockheight == 1.0f) getGeom()->getFaceVerts(*meshinfo, dir);
				else getGeom()->getFaceVerts(*meshinfo, dir, blockheight, 1);

				if (meshinfo->vertices.size() > 0)
				{
					infos.push_back(meshinfo);
				}
				else
				{
					ENG_DELETE_LABEL(meshinfo, kMemTempAlloc);
				}
			}
		}
        if (!getGeom())
            return NULL;
		char* ret =  getGeom()->getPhisicMeshBitBase(key, infos);
		for (int i = 0; i < infos.size(); i++)
		{
			ENG_DELETE_LABEL(infos[i], kMemTempAlloc);
		}
		return ret;
	}
}

bool SlabMaterial::coverNeighbor(int curblockdata, SolidBlockMaterial* neighbor, int neighbor_data, DirectionType dir)
{
	float height = getBlockHeight(curblockdata);
	if (1.f == height)
	{
		return true;
	}
	else
	{
		if ((dir == DIR_NEG_Y && height > 0) || (dir == DIR_POS_Y && height < 0))
		{
			return true;
		}
	}
	return false;
}

SectionMesh* SlabMaterial::createBlockProtoMesh(int protodata)
{
	return Super::createBlockProtoMesh(3);
}


/*
void SolidBlockMaterial::createBlockMesh(const BuildSectionMeshData& data, const WCoord &blockpos, SectionMesh *poutmesh)
{
#ifndef IWORLD_SERVER_BUILD
	if (isUseCustomModel())
	{
		float verts_light[8];
		psection->getBlockVertexLight(blockpos, verts_light);

		int idbuf[32];
		int dirbuf[32];
		int ngeom = getBlockGeomID(idbuf, dirbuf, psection, blockpos);
		BlockGeomMeshInfo meshinfo;
		RenderBlockMaterial *pmtl = m_Mtl; //getGeomMtl(psection, blockpos);
		SectionSubMesh *psubmesh = poutmesh->getSubMesh(pmtl);
		Block pblock = psection->getBlock(blockpos);

		for (int i = 0; i < ngeom; i++)
		{
			if (!m_Geom)
				break;

			int dir = dirbuf[i] & 0xffff;
			int mirrortype = (dirbuf[i] >> 16) & 3;

			if (mirrortype > 0) m_Geom->getFaceVerts(meshinfo, idbuf[i], 1.0f, 0, dir, mirrortype);
			else m_Geom->getFaceVerts(meshinfo, idbuf[i], 1.0f, 0, dir);

			if (isColorableBlock())
			{
				BlockColor bv = getBlockColor(pblock.getData());
				psubmesh->addGeomBlockLight(meshinfo, &blockpos, verts_light, &bv, pmtl->getUVTile());
			}
			else
			{
				psubmesh->addGeomBlockLight(meshinfo, &blockpos, verts_light, NULL, pmtl->getUVTile());
			}
		}
	}
	else
	{
		float verts_light[8];
		Block pblock = psection->getBlock(blockpos);
		int curblockdata = pblock.getData();

		float blockheight = getBlockHeight(curblockdata);
		DirectionType specialdir = DIR_NOT_INIT;
		if (blockheight > 0 && blockheight < 1.0f) specialdir = DIR_POS_Y;
		else if (blockheight<0 && blockheight>-1.0f) specialdir = DIR_NEG_Y;

		const BiomeDef *biome = GetDefManagerProxy()->getBiomeDef(1);

		for (int d = 0; d < 6; d++)
		{
			DirectionType dir = (DirectionType)d;
			//if((psection->getViewPosFlags() & (1<<d)) == 0) continue;

			if (m_DisableCoverFaceOpt || dir == specialdir || !psection->getNeighborCover(blockpos, this, curblockdata, dir))
			{
				//psection->calVertexLights(pblock, blockpos, dir, cover, verts_light);
				psection->getFaceVertexLight(blockpos, dir, verts_light);

				BlockColor facecolor(255, 255, 255, 255);
				RenderBlockMaterial *pmtl = getFaceMtl(biome, dir, pblock.getData(), facecolor);
				if (pmtl == NULL) continue;
				SectionSubMesh *psubmesh = poutmesh->getSubMesh(pmtl);

				BlockGeomMeshInfo mesh;

				if(m_Geom == NULL) continue;
				if (blockheight == 1.0f) m_Geom->getFaceVerts(mesh, dir);
				else m_Geom->getFaceVerts(mesh, dir, blockheight, 1);
				

				if (psubmesh) psubmesh->addGeomFaceLight(mesh, &blockpos, verts_light, &facecolor, pmtl->getUVTile());
			}
		}
	}
#endif
}
*/

/////////////////////////////////////////////////////////////////////////////////////////

IMPLEMENT_BLOCKMATERIAL_INSTANCE_BEGIN(SlabMaterial)
	IMPLEMENT_BLOCKMATERIAL_INSTANCE_R_PARAM(SlabMaterial, R_Dir, int)(0, "Dir", "Block", &SlabMaterialInstance::GetBlockData, &SlabMaterialInstance::SetBlockData);
IMPLEMENT_BLOCKMATERIAL_INSTANCE_END()
