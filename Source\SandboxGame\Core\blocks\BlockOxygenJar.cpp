
#include "BlockOxygenJar.h"
#include "BlockMaterialMgr.h"
#include "section.h"
#include "SectionMesh.h"
#include "BlockGeom.h"
#include "world.h"
#include "special_blockid.h"
#include "WorldProxy.h"
//#include "OgreMaterial.h"
#include "BlockFurnaceOxygen.h"
#include "BlockMaterialMgr.h"
#include "container_world.h"

using namespace MINIW;


IMPLEMENT_BLOCKMATERIAL(OxygenJarMaterial)
OxygenJarMaterial::OxygenJarMaterial()
{
	//for(int i=0; i<4; i++)
	//{
	//	m_StageMtls[i] = NULL;
	//}
}

OxygenJarMaterial::~OxygenJarMaterial()
{
	//for(int i=0; i<4; i++)
	//{
	//	ENG_RELEASE(m_StageMtls[i]);
	//}
	memset(m_stageMtlsIndex, UINT_MAX, sizeof(m_stageMtlsIndex));
}

void OxygenJarMaterial::init(int resid)
{
	ModelBlockMaterial::init(resid);

	
	if(m_LoadOnlyLogic) return;
}

void OxygenJarMaterial::initDefaultMtl()
{
	RenderBlockMaterial* stageMtls[Mtl_Num];
	memset(stageMtls, NULL, sizeof(stageMtls));

	g_BlockMtlMgr.createRenderMaterialStages(stageMtls, Mtl_Num, GetBlockDef(), GetBlockDef()->Texture1.c_str(), BLOCKDRAW_GRASS);
	memset(m_stageMtlsIndex, UINT_MAX, sizeof(m_stageMtlsIndex));
	for (int i = 0; i < Mtl_Num; i++)
	{
		m_stageMtlsIndex[i] = getRenderMtlMgr().addMtl(stageMtls[i]);
		ENG_RELEASE(stageMtls[i]);
	}
	m_defaultMtlIndex = m_stageMtlsIndex[0];
	//m_Mtl = m_StageMtls[0];
	//m_Mtl->addRef();
}


void OxygenJarMaterial::initGeomName()
{
	m_geomName = m_Def->Texture2.c_str();
}
//const char *OxygenJarMaterial::getGeomName()
//{
	//return m_Def->Texture2.c_str();
//}

void OxygenJarMaterial::createBlockMesh(const BuildSectionMeshData& data, const WCoord &blockpos, SectionMesh *poutmesh)
{
	BlockGeomTemplate* geom = getGeom(data.m_LODLevel);
	if (!geom)
		return;

	auto psection = data.m_SharedSectionData;
	Block pblock = psection->getBlock(blockpos);
	int id = 0;

	World* pworld = data.m_World;
	if(pworld)
	{
		WCoord wpos = WCoord(blockpos.x, blockpos.y-1, blockpos.z)+ psection->getOrigin();
		Block pblockDown = pworld->getBlock(wpos);
		if (pblockDown.isEmpty() == false && pblockDown.getResID() == 1045)
		   id = 1;
	}
	Rainbow::ColorRGBAf verts_light[1] = { Rainbow::ColorRGBAf::black };
	int stage = pblock.getData() % 5;
	if (stage > 0)
	   stage--;
	assert(stage <= 3);

	if (BLOCK_OXYGEN_JAR_FULL == pblock.getResID())
	{
		id = 0;
		stage = 3;
	}

	RenderBlockMaterial* pmtl = getRenderMtlMgr().getMtl(stage);//m_StageMtls[stage];

	SectionSubMesh *psubmesh = poutmesh->getSubMesh(pmtl);

	psection->getBlockVertexLight(blockpos, verts_light);

	BlockGeomMeshInfo meshinfo;

	geom->getFaceVerts(meshinfo, id, 1.0f, 0, DIR_NEG_Z);
	
	psubmesh->addGeomBlockLight(meshinfo, &blockpos, verts_light, NULL, pmtl->getUVTile());
}

SectionMesh *OxygenJarMaterial::createBlockProtoMesh(int protodata)
{
	SectionMesh* pmesh = ENG_NEW(SectionMesh)();
	SectionSubMesh* psubmesh = pmesh->getSubMesh(getDefaultMtl(), true);

	BlockGeomMeshInfo meshinfo;

	getGeom()->getFaceVerts(meshinfo, 0);
	psubmesh->addTriangleList(meshinfo.vertices.data(), meshinfo.vertices.size(), meshinfo.indices.data(), meshinfo.indices.size(), nullptr);

	//pmesh->setScale(Vector3f(2.0f, 2.0f, 2.0f));
	return pmesh;
}

BlockTexElement *OxygenJarMaterial::getDestroyTexture(Block pblock, BlockTexDesc &desc)
{
	desc.blendmode = BLEND_ALPHATEST;
	desc.gray = false;
	return getDefaultMtl()->getTexElement();
}

void OxygenJarMaterial::onNotify(World *pworld, const WCoord &blockpos, int blockid)
{
	int stage = pworld->getBlockData(blockpos);
	WCoord blockdown(blockpos.x, blockpos.y - 1, blockpos.z); 
	if(stage > 0 && pworld->getBlockID(blockdown) != BLOCK_FURNACE_OXYGEN)
	{
		dropBlockAsItem(pworld, blockpos, pworld->getBlockData(blockpos));
		pworld->setBlockAll(blockpos, 0, 0, 2);
	}
}

void OxygenJarMaterial::dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata/* =0 */, BLOCK_MINE_TYPE droptype/* =BLOCK_MINE_NOTOOL */, float chance/* =1.0f */, int uin)
{
	int stage = blockdata % 5;
	if(stage <= 3)
		doDropItem(pworld, blockpos, GetBlockDef()->ToolMineDrops[0].item);
	else
		doDropItem(pworld, blockpos, GetBlockDef()->ToolMineDrops[1].item); 
}

void OxygenJarMaterial::onBlockRemoved(World *pworld, const WCoord &blockpos, int blockid, int blockdata)
{
	ModelBlockMaterial::onBlockRemoved(pworld, blockpos, blockid, blockdata);

	WorldContainer *container = pworld->getContainerMgr()->getContainer(WCoord(blockpos.x, blockpos.y-1, blockpos.z));
	if(container)
	{
		WorldFurnaceOxy* oxy = dynamic_cast<WorldFurnaceOxy *>(container);
		if(oxy)
		oxy->ShowCover();
	}

}
/*
int OxygenJarMaterial::getBlockGeomID(int *idbuf, int *dirbuf, const SharedSectionData* sectionData, const WCoord &blockpos, World* world)
{
	Block pblock = psection->getBlock(blockpos);

	idbuf[0] = 0;
	dirbuf[0] = DIR_NEG_Z;

	return 1;
} */