
#include "BlockTriangularPrism.h"
#include "BlockMaterialMgr.h"
#include "section.h"
#include "SectionMesh.h"
#include "BlockGeom.h"
#include "world.h"
#include "DefManagerProxy.h"
#include "IPlayerControl.h"
#include "BlockMeshVert.h"
#include "WorldManager.h"
#include "Common/GameStatic.h"
#include "special_blockid.h"
#include "ClientPlayer.h"
IMPLEMENT_SCENEOBJECTCLASS(TriangularPrismMaterial)
using namespace Rainbow;


static MINIW::GameStatic<dynamic_array<dynamic_array<BlockGeomVert>>> s_mWholeFace;
dynamic_array<dynamic_array<BlockGeomVert>>& TriangularPrismMaterial::m_mWholeFace()
{
	return *s_mWholeFace.EnsureInitialized();
}

dynamic_array<UInt16>* TriangularPrismMaterial::m_dNegIndices/* = { 0, 3, 2, 0, 2, 1 }*/;
dynamic_array<UInt16>* TriangularPrismMaterial::m_dPosIndices/* = { 0, 1, 2, 0, 2, 3 }*/;
dynamic_array<UInt16>* TriangularPrismMaterial::m_NegTrIndices/* = { 0, 2, 1 }*/;
dynamic_array<UInt16>* TriangularPrismMaterial::m_PosTrIndices/* = { 0, 1, 2 }*/;
Rainbow::Vector3f* TriangularPrismMaterial::ms_LightVec;
void initTriangularTriangleIndex(void* data)
{
	TriangularPrismMaterial::m_dNegIndices = new dynamic_array<UInt16>();
	*TriangularPrismMaterial::m_dNegIndices = { 0, 3, 2, 0, 2, 1 };
	TriangularPrismMaterial::m_dPosIndices = new dynamic_array<UInt16>();
	*TriangularPrismMaterial::m_dPosIndices = { 0, 1, 2, 0, 2, 3 };
	TriangularPrismMaterial::m_NegTrIndices = new dynamic_array<UInt16>();
	*TriangularPrismMaterial::m_NegTrIndices = { 0, 2, 1 };
	TriangularPrismMaterial::m_PosTrIndices = new dynamic_array<UInt16>();
	*TriangularPrismMaterial::m_PosTrIndices = { 0, 1, 2 };
	TriangularPrismMaterial::ms_LightVec = new Rainbow::Vector3f(0.f, 0.8944f, 0.4472f);
}
void uninitTriangularTriangleIndex(void* data)
{
	TriangularPrismMaterial::m_dNegIndices->clear_dealloc();
	delete(TriangularPrismMaterial::m_dNegIndices);
	TriangularPrismMaterial::m_dPosIndices->clear_dealloc();
	delete(TriangularPrismMaterial::m_dPosIndices);
	TriangularPrismMaterial::m_NegTrIndices->clear_dealloc();
	delete(TriangularPrismMaterial::m_NegTrIndices);
	TriangularPrismMaterial::m_PosTrIndices->clear_dealloc();
	delete(TriangularPrismMaterial::m_PosTrIndices);
	delete(TriangularPrismMaterial::ms_LightVec);
}
MINIW::GameRuntimeInitializeAndCleanup initTrianglarPrismIdx(initTriangularTriangleIndex, uninitTriangularTriangleIndex);

TriangularPrismMaterial::TriangularPrismMaterial()
{

}

TriangularPrismMaterial::~TriangularPrismMaterial()
{
}

void TriangularPrismMaterial::init(int resid)
{
	CubeBlockMaterial::init(resid);
	SetToggle(BlockToggle_IsOpaqueCube, false);
	SetAttrRenderType(BLOCKRENDER_CUBE_MODEL);

	if (m_LoadOnlyLogic) return;

	char texname[256];
	RenderBlockMaterial* topmtl = nullptr , * sidemtl = nullptr, * bottommtl = nullptr;

	snprintf(texname, sizeof(texname), "%s_top", GetBlockDef()->Texture1.c_str());
	topmtl = g_BlockMtlMgr.createRenderMaterial(texname, GetBlockDef());
	if (topmtl == NULL)
	{
		topmtl = g_BlockMtlMgr.createRenderMaterial(GetBlockDef()->Texture1.c_str(), GetBlockDef(), GETTEX_WITHDEFAULT);

		sidemtl = topmtl;
		sidemtl->AddRef();
		bottommtl = topmtl;
		bottommtl->AddRef();
	}
	else
	{
		if (!GetBlockDef()->Texture2.empty()) sidemtl = g_BlockMtlMgr.createRenderMaterial(GetBlockDef()->Texture2.c_str(), GetBlockDef(), GETTEX_WITHDEFAULT);
		else
		{
			snprintf(texname, sizeof(texname), "%s_side", GetBlockDef()->Texture1.c_str());
			sidemtl = g_BlockMtlMgr.createRenderMaterial(texname, GetBlockDef(), GETTEX_WITHDEFAULT);
		}

		snprintf(texname, sizeof(texname), "%s_bottom", GetBlockDef()->Texture1.c_str());
		bottommtl = g_BlockMtlMgr.createRenderMaterial(texname, GetBlockDef());
		if (bottommtl == NULL)
		{
			bottommtl = topmtl;
			bottommtl->AddRef();
		}
	}

	setFaceMtl(DIR_POS_X, sidemtl);
	setFaceMtl(DIR_NEG_Z, sidemtl);
	setFaceMtl(DIR_NEG_X, sidemtl);
	setFaceMtl(DIR_POS_Z, sidemtl);

	setFaceMtl(DIR_POS_Y, topmtl);
	setFaceMtl(DIR_NEG_Y, bottommtl);


	ENG_RELEASE(sidemtl);
	ENG_RELEASE(topmtl);
	ENG_RELEASE(bottommtl);

	m_nSpecialLogicType[0] |= RotateMechaStopNoChangePos;
	initVertData();
}

float TriangularPrismMaterial::getBlockHeight(int blockdata)
{
	if (blockdata & 8)
	{
		return 1.f;
	}
	else if (blockdata & 4)
	{
		return -0.5f;
	}
	return 0.5f;
}

unsigned char TriangularPrismMaterial::TriangleNormal2LightColor(const Rainbow::Vector3f& normal)
{
	Rainbow::Vector3f tmp;
	tmp.x = Rainbow::Abs(normal.x);
	tmp.y = normal.y;
	tmp.z = Rainbow::Abs(normal.z);

	//Vector3 v = s_LightVec;
	//Normalize(v);
	float t = DotProduct(tmp, *ms_LightVec);
	t = t * 0.2f + 0.8f;

	return (unsigned char)(t * 255);
}

int TriangularPrismMaterial::convertDataByRotate(int blockdata, int rotatetype)
{
	int dir = commonConvertDataByRotate(blockdata & 3, rotatetype);
	return ((blockdata >> 2) << 2) | dir;
}

void TriangularPrismMaterial::createCollideData(CollisionDetect* coldetect, World* pworld, const WCoord& blockpos)
{
	float blockheight = getBlockHeight(pworld->getBlockData(blockpos));
	WCoord pos = blockpos * BLOCK_SIZE;

	int step = 10;
	float movesize = 1.f / float(step);
	float heightOffset = 1.f / (float(step));
	int size = BLOCK_SIZE;
	int curDir = pworld->getBlockData(blockpos) & 3;
	
	auto pblock = pworld->getBlock(blockpos);

	MergeType mergeType = MergeNone;
	int nearBlockNum1 = 0; //ǰ�����ڷ�������
	int nearBlockNum2 = 0; //�������ڷ�������
	int bOnlyOneSidePlace = 0; // 0 ��û��ֱ��
	//ǰ���з���
	auto frontBlock = pworld->getBlock(blockpos + g_DirectionCoord[curDir]);
	if (IsTriangularPrismBlock(frontBlock.getResID()) && isSameUpsidedown(pblock, frontBlock))
	{
		int frontDir = frontBlock.getData() & 3;
		if (frontDir != curDir && frontDir != ReverseDirection(curDir))
		{
			bOnlyOneSidePlace ^= 2;
			mergeType = MergeType(mergeType | MergeFront);
		}

		nearBlockNum1++;
	}

	//���з���
	auto backBlock = pworld->getBlock(blockpos + g_DirectionCoord[ReverseDirection(curDir)]);
	if (IsTriangularPrismBlock(backBlock.getResID()) && isSameUpsidedown(pblock, backBlock))
	{
		int backDir = backBlock.getData() & 3;
		if (backDir != curDir && backDir != ReverseDirection(curDir))
		{
			bOnlyOneSidePlace ^= 2;
			mergeType = MergeType(mergeType | MergeBack);
		}

		nearBlockNum1++;
	}

	//���ƴ��
	auto leftBlock = pworld->getBlock(blockpos + g_DirectionCoord[RotateDir90(curDir)]);
	if (IsTriangularPrismBlock(leftBlock.getResID()) && isSameUpsidedown(pblock, leftBlock))
	{
		int leftDir = leftBlock.getData() & 3;
		if (leftDir == curDir || leftDir == ReverseDirection(curDir))
		{
			bOnlyOneSidePlace ^= 1;
			mergeType = MergeType(mergeType | MergeLeft);
		}

		nearBlockNum2++;
	}
	auto rightBlock = pworld->getBlock(blockpos + g_DirectionCoord[RotateDirPos90(curDir)]);
	if (IsTriangularPrismBlock(rightBlock.getResID()) && isSameUpsidedown(pblock, rightBlock))
	{
		int rightDir = rightBlock.getData() & 3;
		if (rightDir == curDir || rightDir == ReverseDirection(curDir))
		{
			bOnlyOneSidePlace ^= 1;
			mergeType = MergeType(mergeType | MergeRight);
		}

		nearBlockNum2++;
	}

	//�������º���������һ�������з���
	if ((bOnlyOneSidePlace & 1) && (bOnlyOneSidePlace & 2) && nearBlockNum1 == 1 && nearBlockNum2 == 1)
	{
		mergeType = MergeType(mergeType | MergeTurn);
	}

	bool isTurn = mergeType & MergeTurn;
	bool isUpsidedown = pworld->getBlockData(blockpos) & 4;
	
	for (int i = 1; i <= step; i++)
	{
		int minX = 0;
		int minY = 0;
		int minZ = 0;
		int maxX = 0;
		int maxY = 0;
		int maxZ = 0;

		int index = isUpsidedown ? (step + 1 - i) : i;
		float offset = (movesize * index * BLOCK_SIZE) * 0.5f;
		if (!isTurn)
		{
			if (curDir == 0 || curDir == 1)
			{
				minX = offset;
				minY = heightOffset * (i - 1) * size;
				minZ = 0;

				maxX = BLOCK_SIZE - offset;
				maxY = heightOffset * i * size;
				maxZ = BLOCK_SIZE;
			}
			else if (curDir == 2 || curDir == 3)
			{
				minX = 0;
				minY = heightOffset * (i - 1) * size;
				minZ = offset;

				maxX = BLOCK_SIZE;
				maxY = heightOffset * i * size;
				maxZ = BLOCK_SIZE - offset;
			}
		}
		else
		{
			//ת�����׶����ײ��
			if (curDir == 0 || curDir == 1)
			{
				minX = 0;
				minY = heightOffset * (i - 1) * size;
				minZ = offset;

				maxX = BLOCK_SIZE - offset;
				maxY = heightOffset * i * size;
				maxZ = maxX;
			}
			else if (curDir == 2 || curDir == 3)
			{
				minX = offset;
				minY = heightOffset * (i - 1) * size;
				minZ = 0;

				maxX = BLOCK_SIZE - offset;
				maxY = heightOffset * i * size;
				maxZ = maxX;
			}
		}

		if (maxX - minX < 0)
		{
			minX = BLOCK_SIZE / 2;
			maxX = BLOCK_SIZE / 2;
		}

		coldetect->addObstacle(pos + WCoord(minX, minY, minZ), pos + WCoord(maxX, maxY, maxZ));
	}
}

bool TriangularPrismMaterial::coverNeighbor(int curblockdata, SolidBlockMaterial* neighbor, int neighbor_data, DirectionType dir)
{
	return false;
}

void TriangularPrismMaterial::dropBlockAsItem(World* pworld, const WCoord& blockpos, int blockdata, BLOCK_MINE_TYPE droptype, float chance, int uin)
{
	CubeBlockMaterial::dropBlockAsItem(pworld, blockpos, blockdata, droptype, chance);
}

BLOCK_RENDERTYPE_T TriangularPrismMaterial::GetAttrRenderType() const
{
	return BLOCKRENDER_MODEL;
}

void TriangularPrismMaterial::onBlockPlacedBy(World* pworld, const WCoord& blockpos, IClientPlayer* player)
{
	ClientPlayer* playerTmp = player->GetPlayer();
	if (!playerTmp) return;
	int dir = playerTmp->getCurPlaceDir();
	float x, y, z;
	playerTmp->getFaceDir(x, y, z);
	int data = dir;
	if (y > 0)
	{
		data += 4;
	}
	pworld->setBlockData(blockpos, data);
}

bool TriangularPrismMaterial::isSameUpsidedown(const Block &pblockA, const Block pblockB)
{
	if (pblockA == NULL || pblockB == NULL)
		return false;

	return (pblockA.getData() & 4) == (pblockB.getData() & 4);
}

void TriangularPrismMaterial::getFaceIdxVecByDir(vector<int> &vec, int curDir)
{
	int angle = 0;
	if (curDir == DIR_NEG_X)
	{
		angle = 0;
	}
	else if (curDir == DIR_POS_X)
	{
		angle = 180;
	}
	else if (curDir == DIR_NEG_Z)
	{
		angle = 90;
	}
	else if (curDir == DIR_POS_Z)
	{
		angle = 270;
	}

	int a[2][2] = { {0, 1}, {2, 3}};
	int b[2][2];
	for (int i = 0; i < 2; i++)
	{
		for (int j = 0; j < 2; j++)
		{
			if (angle == 90)
			{
				b[i][j] = a[1 - j][i];//˳ʱ����ת90�ȣ������㷨
			}
			else if (angle == 180)
			{
				b[i][j] = a[1 - i][1 - j];// ˳ʱ����ת180�ȣ������㷨
			}
			else if (angle == 270) 
			{
				b[i][j] = a[j][1 - i];//˳ʱ����ת270�ȣ������㷨
			}
			else
			{
				b[i][j] = a[i][j];
			}

			vec.push_back(b[i][j]);
		}
	}
}

MergeType TriangularPrismMaterial::getMergeType(const SectionDataHandler* psection, Block& pblock, const WCoord& blockpos, int curDir)
{
	int bOnlyOneSidePlace = 0; // 0 ��û��ֱ��
	MergeType mergeType = MergeNone;
	int nearBlockNum1 = 0; //ǰ�����ڷ�������
	int nearBlockNum2 = 0; //�������ڷ�������
	//ǰ���з���
	auto frontBlock = psection->getNeighborBlock(blockpos, g_DirectionCoord[curDir]);
	
	if (IsTriangularPrismBlock(frontBlock.getResID()) && isSameUpsidedown(pblock, frontBlock))
	{
		int frontDir = frontBlock.getData() & 3;
		if (frontDir != curDir && frontDir != ReverseDirection(curDir))
		{
			bOnlyOneSidePlace ^= 2;
			mergeType = MergeType(mergeType | MergeFront);
		}

		nearBlockNum1++;
	}

	//���з���
	auto backBlock = psection->getNeighborBlock(blockpos, g_DirectionCoord[ReverseDirection(curDir)]);
	
	if (IsTriangularPrismBlock(backBlock.getResID()) && isSameUpsidedown(pblock, backBlock))
	{
		int backDir = backBlock.getData() & 3;
		if (backDir != curDir && backDir != ReverseDirection(curDir))
		{
			bOnlyOneSidePlace ^= 2;
			mergeType = MergeType(mergeType | MergeBack);
		}
		nearBlockNum1++;
	}

	//���ƴ��
	auto leftBlock = psection->getNeighborBlock(blockpos, g_DirectionCoord[RotateDir90(curDir)]);
	if (IsTriangularPrismBlock(leftBlock.getResID()) && isSameUpsidedown(pblock, leftBlock))
	{
		int leftDir = leftBlock.getData() & 3;
		if (leftDir == curDir || leftDir == ReverseDirection(curDir))
		{
			bOnlyOneSidePlace ^= 1;
			mergeType = MergeType(mergeType | MergeLeft);
		}
		nearBlockNum2++;
	}
	auto rightBlock = psection->getNeighborBlock(blockpos, g_DirectionCoord[RotateDirPos90(curDir)]);
	if (IsTriangularPrismBlock(rightBlock.getResID()) && isSameUpsidedown(pblock, rightBlock))
	{
		int rightDir = rightBlock.getData() & 3;
		if (rightDir == curDir || rightDir == ReverseDirection(curDir))
		{
			bOnlyOneSidePlace ^= 1;
			mergeType = MergeType(mergeType | MergeRight);
		}
		nearBlockNum2++;
	}
	
	//�������º���������һ�������з���
	if ((bOnlyOneSidePlace & 1) && (bOnlyOneSidePlace & 2) && nearBlockNum1 == 1 && nearBlockNum2 == 1)
	{
		mergeType = MergeType(mergeType | MergeTurn);
	}

	return mergeType;
}

void TriangularPrismMaterial::createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh)
{
	auto psection = data.m_SharedSectionData;
#ifndef IWORLD_SERVER_BUILD
	FaceVertexLight faceVertexLight;

	Block pblock = psection->getBlock(blockpos);
	int curblockdata = pblock.getData();

	float blockheight = getBlockHeight(curblockdata);
	DirectionType specialdir = DIR_NOT_INIT;
	if (blockheight > 0 && blockheight < 1.0f) specialdir = DIR_POS_Y;
	else if (blockheight<0 && blockheight>-1.0f) specialdir = DIR_NEG_Y;

	const BiomeDef* biome = GetDefManagerProxy()->getBiomeDef(1);

	std::vector<int> wholeFace;
	std::vector<int> triangleFace;
	std::vector<int> slantFace;
	std::vector<int> triangleSlantFace;
	std::vector<int> trapezoidFace;
	DirectionType curDir = DirectionType(curblockdata & 3);
	
	MergeType mergeType = getMergeType(psection, pblock, blockpos, curDir);
	
	if (specialdir == DIR_POS_Y)
		wholeFace.push_back(4);
	else
		wholeFace.push_back(5);

	//�ս�ƴ��
	if (mergeType & MergeTurn)
	{
		vector<int> faceIdxVec;
		getFaceIdxVecByDir(faceIdxVec, curDir);

		int faceIdx = 0;
		if ((mergeType & MergeLeft) && (mergeType & MergeFront))
		{
			faceIdx = faceIdxVec[0];
		}
		if ((mergeType & MergeRight) && (mergeType & MergeFront))
		{
			faceIdx = faceIdxVec[1];
		}
		if ((mergeType & MergeLeft) && (mergeType & MergeBack))
		{
			faceIdx = faceIdxVec[2];
		}
		if ((mergeType & MergeRight) && (mergeType & MergeBack))
		{
			faceIdx = faceIdxVec[3];
		}

		vector<Rainbow::Vector2f> trapezoidFaceVec;
		vector<Rainbow::Vector2f> triangleSlantFaceVec;
		if (specialdir == DIR_POS_Y)
		{
			trapezoidFaceVec.push_back(Rainbow::Vector2f(3, 5)); // dir Ϊ0 ʱ�� 3��5������
			trapezoidFaceVec.push_back(Rainbow::Vector2f(6, 1));
			trapezoidFaceVec.push_back(Rainbow::Vector2f(0, 7));
			trapezoidFaceVec.push_back(Rainbow::Vector2f(4, 2));

			triangleSlantFaceVec.push_back(Rainbow::Vector2f(6, 0));
			triangleSlantFaceVec.push_back(Rainbow::Vector2f(4, 3));
			triangleSlantFaceVec.push_back(Rainbow::Vector2f(2, 5));
			triangleSlantFaceVec.push_back(Rainbow::Vector2f(7, 1));
		}
		else
		{
			trapezoidFaceVec.push_back(Rainbow::Vector2f(11, 13));
			trapezoidFaceVec.push_back(Rainbow::Vector2f(14, 9));
			trapezoidFaceVec.push_back(Rainbow::Vector2f(8, 15));
			trapezoidFaceVec.push_back(Rainbow::Vector2f(10, 12));

			triangleSlantFaceVec.push_back(Rainbow::Vector2f(8, 14));
			triangleSlantFaceVec.push_back(Rainbow::Vector2f(12, 11));
			triangleSlantFaceVec.push_back(Rainbow::Vector2f(10, 13));
			triangleSlantFaceVec.push_back(Rainbow::Vector2f(15, 9));
		}

		Rainbow::Vector2f& trapezoidFaceVecf = trapezoidFaceVec[faceIdx];
		trapezoidFace.push_back(trapezoidFaceVecf.x);
		trapezoidFace.push_back(trapezoidFaceVecf.y);

		Rainbow::Vector2f& triangleSlantFaceVecf = triangleSlantFaceVec[faceIdx];
		triangleSlantFace.push_back(triangleSlantFaceVecf.x);
		triangleSlantFace.push_back(triangleSlantFaceVecf.y);
	}
	else
	{
		if (curDir == DIR_NEG_X || curDir == DIR_POS_X)
		{

			if (specialdir == DIR_POS_Y)
			{
				triangleFace.push_back(2);
				triangleFace.push_back(3);
				slantFace.push_back(0);
				slantFace.push_back(1);

				if (curDir == DIR_NEG_X)
				{
					if (mergeType & MergeFront || mergeType & MergeBoth)
					{
						triangleSlantFace.push_back(3);
						triangleSlantFace.push_back(6);
					}

					if (mergeType & MergeBack || mergeType & MergeBoth)
					{
						triangleSlantFace.push_back(2);
						triangleSlantFace.push_back(7);
					}
				}
				else if (curDir == DIR_POS_X)
				{
					if (mergeType & MergeFront || mergeType & MergeBoth)
					{
						triangleSlantFace.push_back(2);
						triangleSlantFace.push_back(7);
					}

					if (mergeType & MergeBack || mergeType & MergeBoth)
					{
						triangleSlantFace.push_back(3);
						triangleSlantFace.push_back(6);
					}
				}
			}
			else
			{
				triangleFace.push_back(6);
				triangleFace.push_back(7);
				slantFace.push_back(4);
				slantFace.push_back(5);

				if (curDir == DIR_NEG_X)
				{
					if (mergeType & MergeFront || mergeType & MergeBoth)
					{
						triangleSlantFace.push_back(11);
						triangleSlantFace.push_back(14);
					}

					if (mergeType & MergeBack || mergeType & MergeBoth)
					{
						triangleSlantFace.push_back(10);
						triangleSlantFace.push_back(15);
					}
				}
				else if (curDir == DIR_POS_X)
				{
					if (mergeType & MergeFront || mergeType & MergeBoth)
					{
						triangleSlantFace.push_back(10);
						triangleSlantFace.push_back(15);
					}

					if (mergeType & MergeBack || mergeType & MergeBoth)
					{
						triangleSlantFace.push_back(11);
						triangleSlantFace.push_back(14);
					}
				}
			}
		}
		else if (curDir == DIR_NEG_Z || curDir == DIR_POS_Z)
		{
			if (specialdir == DIR_POS_Y)
			{
				//��ֱ�νӷ���
				triangleFace.push_back(0);
				triangleFace.push_back(1);
				slantFace.push_back(2);
				slantFace.push_back(3);

				if (curDir == DIR_NEG_Z)
				{
					if (mergeType & MergeFront || mergeType & MergeBoth)
					{
						triangleSlantFace.push_back(0);
						triangleSlantFace.push_back(5);
					}

					if (mergeType & MergeBack || mergeType & MergeBoth)
					{
						triangleSlantFace.push_back(1);
						triangleSlantFace.push_back(4);
					}
				}
				else if (curDir == DIR_POS_Z)
				{
					if (mergeType & MergeFront || mergeType & MergeBoth)
					{
						triangleSlantFace.push_back(1);
						triangleSlantFace.push_back(4);
					}

					if (mergeType & MergeBack || mergeType & MergeBoth)
					{
						triangleSlantFace.push_back(0);
						triangleSlantFace.push_back(5);
					}
				}
			}
			else
			{
				//�����泯��X�ᣬб�泯��Z��
				triangleFace.push_back(4);
				triangleFace.push_back(5);
				slantFace.push_back(6);
				slantFace.push_back(7);

				if (curDir == DIR_NEG_Z)
				{
					if (mergeType & MergeFront || mergeType & MergeBoth)
					{
						triangleSlantFace.push_back(8);
						triangleSlantFace.push_back(13);
					}

					if (mergeType & MergeBack || mergeType & MergeBoth)
					{
						triangleSlantFace.push_back(9);
						triangleSlantFace.push_back(12);
					}
				}
				else if (curDir == DIR_POS_Z)
				{
					if (mergeType & MergeFront || mergeType & MergeBoth)
					{
						triangleSlantFace.push_back(9);
						triangleSlantFace.push_back(12);
					}

					if (mergeType & MergeBack || mergeType & MergeBoth)
					{
						triangleSlantFace.push_back(8);
						triangleSlantFace.push_back(13);
					}
				}
			}
		}
	}

	BlockColor facecolor(255, 255, 255, 0);
	for (auto& d : wholeFace)
	{
		DirectionType dir = (DirectionType)d;
		bool flipQuad = psection->getFaceVertexLight(blockpos, dir, faceVertexLight);

		dynamic_array<UInt16>* indices = m_dPosIndices;

		RenderBlockMaterial* pmtl = getFaceMtl(biome, dir, pblock.getData(), facecolor);

		if (pmtl == NULL) pmtl = getFaceMtl(biome, dir, pblock.getData(), facecolor);
		if (pmtl == NULL)
			continue;
		SectionSubMesh* psubmesh = poutmesh->getSubMesh(pmtl);
		const float* uvtile = nullptr;
		if (psubmesh && !psubmesh->IsIgnoreTileUV())
			uvtile = pmtl->getUVTile();


		BlockGeomMeshInfo mesh;
		mesh.vertices = m_mWholeFace()[d];
		mesh.indices = *indices;
		// unsigned short dir_color = TriangleNormal2LightColor(g_DirectionCoord[dir].toVector3());

		if (psubmesh)
			psubmesh->addGeomFaceLight(mesh, &blockpos, faceVertexLight, &facecolor, pmtl->getUVTile());
	}
	for (auto& d : triangleFace)
	{
		DirectionType dir = (DirectionType)(d % 4);
		bool flipQuad = psection->getFaceVertexLight(blockpos, dir, faceVertexLight);

		dynamic_array<UInt16>* indices = m_PosTrIndices;

		RenderBlockMaterial* pmtl = getFaceMtl(biome, dir, pblock.getData(), facecolor);
		if (pmtl == NULL)
			continue;
		SectionSubMesh* psubmesh = poutmesh->getSubMesh(pmtl);
		const float* uvtile = nullptr;
		if (psubmesh && !psubmesh->IsIgnoreTileUV())
			uvtile = pmtl->getUVTile();

		BlockGeomMeshInfo mesh;
		mesh.vertices = m_mTriangleFace[d];
		mesh.indices = *indices;

		if (psubmesh)
			psubmesh->addGeomFaceLight(mesh, &blockpos, faceVertexLight, &facecolor, pmtl->getUVTile());
	}
	for (auto& d : slantFace)
	{
		DirectionType dir = (DirectionType)(d % 4);
		FaceVertexLight faceUpDownVertexLight;
		bool flipQuad = psection->getFaceVertexLight(blockpos, dir, faceVertexLight);
		psection->getFaceVertexLight(blockpos, specialdir, faceUpDownVertexLight);
		for (int i = 0; i < 4; i++)
		{
			faceVertexLight.m_Light[i] = (faceVertexLight.m_Light[i] + faceUpDownVertexLight.m_Light[i]) / 2;
			faceVertexLight.m_AmbientOcclusion[i] = (faceVertexLight.m_AmbientOcclusion[i] + faceUpDownVertexLight.m_AmbientOcclusion[i]) / 2;
		}

		dynamic_array<UInt16>* indices = /*flipQuad ? &m_dNegIndices : */m_dPosIndices;

		RenderBlockMaterial* pmtl = getFaceMtl(biome, dir, pblock.getData(), facecolor);
		if (pmtl == NULL)
			continue;

		SectionSubMesh* psubmesh = poutmesh->getSubMesh(pmtl);

		const float* uvtile = nullptr;
		if (psubmesh && !psubmesh->IsIgnoreTileUV())
			uvtile = pmtl->getUVTile();

		BlockGeomMeshInfo mesh;

		// 		mesh.vertices = vertices;
		mesh.vertices = m_mSlantFace[d];
		mesh.indices = *indices;


		// unsigned short dir_color1 = TriangleNormal2LightColor(g_DirectionCoord[dir].toVector3());
		// unsigned short dir_color2 = TriangleNormal2LightColor(g_DirectionCoord[specialdir].toVector3());
		BlockColor vertcolor = Rainbow::ColorRGBA32::white;
		//vertcolor.v = 0xffffffff;
		vertcolor.a = 0;//(dir_color1 + 2 * dir_color2) / 3;
		for (int n = 0; n < m_mSlantFace[d].size(); n++)
		{
			auto& vert = mesh.vertices[n];
			int aveltMe = 0;
			int aveltNeight[3] = { 0 };
			int avelt = 0;
			if (vert.pos.y > 0 && specialdir == DIR_POS_Y || vert.pos.y == 0 && specialdir == DIR_NEG_Y)
			{
				auto selfPos = blockpos + g_DirectionCoord[specialdir];
				aveltMe = psection->getLight2(selfPos, true);
				bool isXdir = dir < 2;
				int xdir = isXdir ? dir : vert.pos.x == 0 ? 0 : 1;
				int zdir = !isXdir ? dir : vert.pos.z == 0 ? 2 : 3;
				aveltNeight[0] = psection->getLight2(selfPos + g_DirectionCoord[xdir], true);
				aveltNeight[1] = psection->getLight2(selfPos + g_DirectionCoord[zdir], true);
				aveltNeight[2] = psection->getLight2(selfPos + g_DirectionCoord[xdir] + g_DirectionCoord[zdir], true);
				avelt = ((aveltMe + aveltNeight[0] + aveltNeight[1] + aveltNeight[2]) >> 2) & 0xff00ff;
			}
			else
			{
				auto selfPos = blockpos + g_DirectionCoord[dir];
				aveltMe = psection->getLight2(selfPos, true);
				int xdir = vert.pos.x == 0 ? 0 : 1;
				int zdir = vert.pos.z == 0 ? 2 : 3;
				int sideDir = dir > 1 ? xdir : zdir;
				aveltNeight[0] = psection->getLight2(selfPos + g_DirectionCoord[sideDir], true);
				avelt = ((aveltMe + aveltNeight[0]) >> 1) & 0xff00ff;
			}
			//int lt1 = (((avelt >> 4) & 0xf) * vertcolor.a) >> 5;
			//int lt2 = (((avelt >> 20) & 0xf) * vertcolor.a) >> 5;
			//vert.pos.w = (lt1 << 8) | lt2;
			//vert.color = vertcolor;
			vert.color = vertcolor;
			InitBlockVertLight(vert, avelt, uvtile);
		}
		if (psubmesh) psubmesh->addGeomFace(mesh, &blockpos);// addGeomFaceLight(mesh, &blockpos, faceVertexLight, &facecolor, pmtl->getUVTile());
	}
	for (auto& d : triangleSlantFace)
	{
		DirectionType dir = (DirectionType)(d % 4);
		
		dynamic_array<UInt16>* indices = m_PosTrIndices;

		RenderBlockMaterial* pmtl = getFaceMtl(biome, dir, pblock.getData(), facecolor);

		if (pmtl == NULL) pmtl = getFaceMtl(biome, dir, pblock.getData(), facecolor);
		if (pmtl == NULL) continue;

		SectionSubMesh* psubmesh = poutmesh->getSubMesh(pmtl);
		const float* uvtile = nullptr;
		if (psubmesh && !psubmesh->IsIgnoreTileUV())
			uvtile = pmtl->getUVTile();

		BlockGeomMeshInfo mesh;
		mesh.vertices = m_mTriangleSlantFace[d];
		mesh.indices = *indices;

		unsigned short dir_color1 = TriangleNormal2LightColor(g_DirectionCoord[dir].toVector3());
		unsigned short dir_color2 = TriangleNormal2LightColor(g_DirectionCoord[specialdir].toVector3());
		BlockColor vertcolor = Rainbow::ColorRGBA32::white;
		vertcolor.a = (dir_color1 + 2 * dir_color2) / 3;

		for (int n = 0; n < mesh.vertices.size(); n++)
		{
			auto& vert = mesh.vertices[n];
			int aveltMe = 0;
			int aveltNeight[3] = { 0 };
			int avelt = 0;
			if (vert.pos.y > 0 && specialdir == DIR_POS_Y || vert.pos.y == 0 && specialdir == DIR_NEG_Y)
			{
				auto selfPos = blockpos + g_DirectionCoord[specialdir];
				aveltMe = psection->getLight2(selfPos, true);
				bool isXdir = dir < 2;
				int xdir = isXdir ? dir : vert.pos.x == 0 ? 0 : 1;
				int zdir = !isXdir ? dir : vert.pos.z == 0 ? 2 : 3;
				aveltNeight[0] = psection->getLight2(selfPos + g_DirectionCoord[xdir], true);
				aveltNeight[1] = psection->getLight2(selfPos + g_DirectionCoord[zdir], true);
				aveltNeight[2] = psection->getLight2(selfPos + g_DirectionCoord[xdir] + g_DirectionCoord[zdir], true);
				avelt = ((aveltMe + aveltNeight[0] + aveltNeight[1] + aveltNeight[2]) >> 2) & 0xff00ff;
			}
			else
			{
				auto selfPos = blockpos + g_DirectionCoord[dir];
				aveltMe = psection->getLight2(selfPos, true);
				int xdir = vert.pos.x == 0 ? 0 : 1;
				int zdir = vert.pos.z == 0 ? 2 : 3;
				int sideDir = dir > 1 ? xdir : zdir;
				aveltNeight[0] = psection->getLight2(selfPos + g_DirectionCoord[sideDir], true);
				avelt = ((aveltMe + aveltNeight[0]) >> 1) & 0xff00ff;
			}

			vert.color = vertcolor;
			InitBlockVertLight(vert, avelt, uvtile);
		}

		if (psubmesh)
			psubmesh->addGeomFace(mesh, &blockpos);
	}
	for (auto& d : trapezoidFace)
	{
		DirectionType dir = (DirectionType)(d % 4);
		dynamic_array<UInt16>* indices = m_dPosIndices;

		RenderBlockMaterial* pmtl = getFaceMtl(biome, dir, pblock.getData(), facecolor);
		if (pmtl == NULL)
			continue;

		SectionSubMesh* psubmesh = poutmesh->getSubMesh(pmtl);

		const float* uvtile = nullptr;
		if (psubmesh && !psubmesh->IsIgnoreTileUV())
			uvtile = pmtl->getUVTile();

		BlockGeomMeshInfo mesh;
		mesh.vertices = m_mTrapezoidSlantFace[d];
		mesh.indices = *indices;

		// unsigned short dir_color1 = TriangleNormal2LightColor(g_DirectionCoord[dir].toVector3());
		// unsigned short dir_color2 = TriangleNormal2LightColor(g_DirectionCoord[specialdir].toVector3());
		BlockColor vertcolor = Rainbow::ColorRGBA32::white;

		vertcolor.a = 0;//(dir_color1 + 2 * dir_color2) / 3;
		for (int n = 0; n < m_mTrapezoidSlantFace[d].size(); n++)
		{
			auto& vert = mesh.vertices[n];
			int aveltMe = 0;
			int aveltNeight[3] = { 0 };
			int avelt = 0;
			if (vert.pos.y > 0 && specialdir == DIR_POS_Y || vert.pos.y == 0 && specialdir == DIR_NEG_Y)
			{
				auto selfPos = blockpos + g_DirectionCoord[specialdir];
				aveltMe = psection->getLight2(selfPos, true);
				bool isXdir = dir < 2;
				int xdir = isXdir ? dir : vert.pos.x == 0 ? 0 : 1;
				int zdir = !isXdir ? dir : vert.pos.z == 0 ? 2 : 3;
				aveltNeight[0] = psection->getLight2(selfPos + g_DirectionCoord[xdir], true);
				aveltNeight[1] = psection->getLight2(selfPos + g_DirectionCoord[zdir], true);
				aveltNeight[2] = psection->getLight2(selfPos + g_DirectionCoord[xdir] + g_DirectionCoord[zdir], true);
				avelt = ((aveltMe + aveltNeight[0] + aveltNeight[1] + aveltNeight[2]) >> 2) & 0xff00ff;
			}
			else
			{
				auto selfPos = blockpos + g_DirectionCoord[dir];
				aveltMe = psection->getLight2(selfPos, true);
				int xdir = vert.pos.x == 0 ? 0 : 1;
				int zdir = vert.pos.z == 0 ? 2 : 3;
				int sideDir = dir > 1 ? xdir : zdir;
				aveltNeight[0] = psection->getLight2(selfPos + g_DirectionCoord[sideDir], true);
				avelt = ((aveltMe + aveltNeight[0]) >> 1) & 0xff00ff;
			}
			vert.color = vertcolor;
			InitBlockVertLight(vert, avelt, uvtile);
		}

		if (psubmesh) psubmesh->addGeomFace(mesh, &blockpos);// addGeomFaceLight(mesh, &blockpos, faceVertexLight, &facecolor, pmtl->getUVTile());
	}
#endif
}

void TriangularPrismMaterial::initVertData()
{
	initWholeFaceVertData();
	initTriangleFaceVertData();
	initSlantFaceVertData();
	initTriangleSlantFaceVertData();
	initTrapezoidSlantFaceVertData();
	initTriangleSlantFace2VertData();
	initPhyModelData();
}

void TriangularPrismMaterial::initWholeFaceVertData()
{
	if (m_mWholeFace().size() != 0)
	{
		return;
	}
	for (int d = 0; d < 6; d++)
	{
		DirectionType dir = (DirectionType)d;
		dynamic_array<UInt16>* indices = m_dPosIndices;

		dynamic_array<BlockGeomVert> vertices;
		Rainbow::Vector3f normalVec = g_DirectionCoord[d].toVector3();
		Normalize(normalVec);
		BlockVector normal_dir = PackVertNormal(normalVec);
		BlockGeomVert vert[4];
		//unsigned short dir_color = Normal2LightColor(g_DirectionCoord[dir].toVector3());
		// unsigned short dir_color = TriangleNormal2LightColor(g_DirectionCoord[dir].toVector3());
		BlockVector vertcolor;
		vertcolor.v = 0xffffffff;
		vertcolor.w = 0;
		if (0 == d)
		{
			vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
			vert[1].pos = Rainbow::Vector4f(0, 0, 100, 0);
			vert[2].pos = Rainbow::Vector4f(0, 100, 100, 0);
			vert[3].pos = Rainbow::Vector4f(0, 100, 0, 0);

			vert[0].uv = { 1, 1 };
			vert[1].uv = { 0, 1 };
			vert[2].uv = { 0, 0 };
			vert[3].uv = { 1, 0 };
		}
		else if (1 == d)
		{
			vert[0].pos = Rainbow::Vector4f(100, 0, 0, 0);
			vert[1].pos = Rainbow::Vector4f(100, 100, 0, 0);
			vert[2].pos = Rainbow::Vector4f(100, 100, 100, 0);
			vert[3].pos = Rainbow::Vector4f(100, 0, 100, 0);

			vert[0].uv = { 0, 1 };
			vert[1].uv = { 0, 0 };
			vert[2].uv = { 1, 0 };
			vert[3].uv = { 1, 1 };
		}
		else if (2 == d)
		{
			vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
			vert[1].pos = Rainbow::Vector4f(0, 100, 0, 0);
			vert[2].pos = Rainbow::Vector4f(100, 100, 0, 0);
			vert[3].pos = Rainbow::Vector4f(100, 0, 0, 0);

			vert[0].uv = { 0, 1 };
			vert[1].uv = { 0, 0 };
			vert[2].uv = { 1, 0 };
			vert[3].uv = { 1, 1 };
		}
		else if (3 == d)
		{
			vert[0].pos = Rainbow::Vector4f(0, 0, 100, 0);
			vert[1].pos = Rainbow::Vector4f(100, 0, 100, 0);
			vert[2].pos = Rainbow::Vector4f(100, 100, 100, 0);
			vert[3].pos = Rainbow::Vector4f(0, 100, 100, 0);

			vert[0].uv = { 1, 1 };
			vert[1].uv = { 0, 1 };
			vert[2].uv = { 0, 0 };
			vert[3].uv = { 1, 0 };
		}
		else if (4 == d)
		{
			vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
			vert[1].pos = Rainbow::Vector4f(100, 0, 0, 0);
			vert[2].pos = Rainbow::Vector4f(100, 0, 100, 0);
			vert[3].pos = Rainbow::Vector4f(0, 0, 100, 0);

			vert[0].uv = { 1, 0 };
			vert[1].uv = { 0, 0 };
			vert[2].uv = { 0, 1 };
			vert[3].uv = { 1, 1 };
		}
		else if (5 == d)
		{
			vert[0].pos = Rainbow::Vector4f(0, 100, 0, 0);
			vert[1].pos = Rainbow::Vector4f(0, 100, 100, 0);
			vert[2].pos = Rainbow::Vector4f(100, 100, 100, 0);
			vert[3].pos = Rainbow::Vector4f(100, 100, 0, 0);

			vert[0].uv = { 0, 0 };
			vert[1].uv = { 0, 1 };
			vert[2].uv = { 1, 1 };
			vert[3].uv = { 1, 0 };
		}

		for (int oo = 0; oo < 4; oo++)
		{
			vert[oo].uv = { (short)(vert[oo].uv.x * BLOCKUV_SCALE), (short)(vert[oo].uv.y * BLOCKUV_SCALE) };
			vert[oo].normal = normal_dir;
			vert[oo].color.SetUInt32(vertcolor.v);
			// 			vert[oo].color = 0xffffffff;
			vert[oo].pos.w = 0;//(lt1 << 8) | lt2;
			vertices.push_back(vert[oo]);
		}
		m_mWholeFace().push_back(vertices);
		// 		m_mWholeFace()[d] = vertices;
	}
}

void TriangularPrismMaterial::initTriangleFaceVertData()
{
	for (int i = 0; i < 2; i++)
	{
		for (int j = 0; j < 4; j++)
		{
			DirectionType dir = (DirectionType)j;
			dynamic_array<UInt16>* indices = m_PosTrIndices;
			int d = i * 4 + j;
			// unsigned short dir_color = TriangleNormal2LightColor(g_DirectionCoord[dir].toVector3());
			dynamic_array<BlockGeomVert> vertices;
			Rainbow::Vector3f normalVec = g_DirectionCoord[j].toVector3();
			////unsigned short dir_color = Normal2LightColor(normalVec);
			Normalize(normalVec);
			BlockVector normal_dir = PackVertNormal(normalVec);
			BlockVector vertcolor;
			vertcolor.v = 0xffffffff;
			vertcolor.w = 0;
			
			const int vertLen = 3;
			BlockGeomVert vert[vertLen];

			std::vector<Rainbow::Vector2f> uvMap;
			uvMap.resize(0);
			//ParseLuaVertCfg("lua_initTriangleFaceVertData_CFG", m_mTriangleFace, normal_dir, vertcolor, d, vert, vertLen);
			if (0 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
				vert[1].pos = Rainbow::Vector4f(0, 0, 100, 0);
				vert[2].pos = Rainbow::Vector4f(0, 100, 50, 0);
			}
			else if (1 == d)
			{
				vert[0].pos = Rainbow::Vector4f(100, 0, 100, 0);
				vert[1].pos = Rainbow::Vector4f(100, 0, 0, 0);
				vert[2].pos = Rainbow::Vector4f(100, 100, 50, 0);
			}
			else if (2 == d)
			{
				vert[0].pos = Rainbow::Vector4f(100, 0, 0, 0);
				vert[1].pos = Rainbow::Vector4f(0, 0, 0, 0);
				vert[2].pos = Rainbow::Vector4f(50, 100, 0, 0);
			}
			else if (3 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 0, 100, 0);
				vert[1].pos = Rainbow::Vector4f(100, 0, 100, 0);
				vert[2].pos = Rainbow::Vector4f(50, 100, 100, 0);
			}
			else if (4 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 100, 100, 0);
				vert[1].pos = Rainbow::Vector4f(0, 100, 0, 0);
				vert[2].pos = Rainbow::Vector4f(0, 0, 50, 0);
			}
			else if (5 == d)
			{
				vert[0].pos = Rainbow::Vector4f(100, 100, 0, 0);
				vert[1].pos = Rainbow::Vector4f(100, 100, 100, 0);
				vert[2].pos = Rainbow::Vector4f(100, 0, 50, 0);
			}
			else if (6 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 100, 0, 0);
				vert[1].pos = Rainbow::Vector4f(100, 100, 0, 0);
				vert[2].pos = Rainbow::Vector4f(50, 0, 0, 0);
			}
			else if (7 == d)
			{
				vert[0].pos = Rainbow::Vector4f(100, 100, 100, 0);
				vert[1].pos = Rainbow::Vector4f(0, 100, 100, 0);
				vert[2].pos = Rainbow::Vector4f(50, 0, 100, 0);
			}
			uvMap.push_back({ 1, 1 });
			uvMap.push_back({ 0, 1 });
			uvMap.push_back({ 0.5, 0 });

			for (int oo = 0; oo < vertLen; oo++)
			{
				vert[oo].uv = { short(uvMap[oo].x * BLOCKUV_SCALE), short(uvMap[oo].y * BLOCKUV_SCALE) };
				//vert[oo].uv = { (short)(vert[oo].uv.x * BLOCKUV_SCALE), (short)(vert[oo].uv.y * BLOCKUV_SCALE) };
				vert[oo].normal = normal_dir;
				vert[oo].color.SetUInt32(vertcolor.v);
				vert[oo].pos.w = 0;//(lt1 << 8) | lt2;
				vertices.push_back(vert[oo]);
			}
			m_mTriangleFace.push_back(vertices);
		}
	}
}

void TriangularPrismMaterial::initSlantFaceVertData()
{
	for (int i = 0; i < 2; i++)
	{
		for (int j = 0; j < 4; j++)
		{
			DirectionType dir = (DirectionType)j;
			dynamic_array<UInt16>* indices = m_dPosIndices;
			int d = i * 4 + j;
			dynamic_array<BlockGeomVert> vertices;
			Rainbow::Vector3f normalVec = (g_DirectionCoord[j] + g_DirectionCoord[i ? 4 : 5]).toVector3();
			//unsigned short dir_color = Normal2LightColor(normalVec);
			BlockVector vertcolor;
			vertcolor.v = 0xffffffff;
			vertcolor.w = 0;
			Normalize(normalVec);
			BlockVector normal_dir = PackVertNormal(normalVec);

			const int vertLen = 4;
			BlockGeomVert vert[vertLen];
			std::vector<Rainbow::Vector2f> uvMap;
			uvMap.resize(0);
			if (0 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
				vert[1].pos = Rainbow::Vector4f(0, 0, 100, 0);
				vert[2].pos = Rainbow::Vector4f(50, 100, 100, 0);
				vert[3].pos = Rainbow::Vector4f(50, 100, 0, 0);
			}
			else if (1 == d)
			{
				vert[0].pos = Rainbow::Vector4f(100, 0, 100, 0);
				vert[1].pos = Rainbow::Vector4f(100, 0, 0, 0);
				vert[2].pos = Rainbow::Vector4f(50, 100, 0, 0);
				vert[3].pos = Rainbow::Vector4f(50, 100, 100, 0);
			}
			else if (2 == d)
			{
				vert[0].pos = Rainbow::Vector4f(100, 0, 0, 0);
				vert[1].pos = Rainbow::Vector4f(0, 0, 0, 0);
				vert[2].pos = Rainbow::Vector4f(0, 100, 50, 0);
				vert[3].pos = Rainbow::Vector4f(100, 100, 50, 0);
			}
			else if (3 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 0, 100, 0);
				vert[1].pos = Rainbow::Vector4f(100, 0, 100, 0);
				vert[2].pos = Rainbow::Vector4f(100, 100, 50, 0);
				vert[3].pos = Rainbow::Vector4f(0, 100, 50, 0);
			}
			else if (4 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 100, 100, 0);
				vert[1].pos = Rainbow::Vector4f(0, 100, 0, 0);
				vert[2].pos = Rainbow::Vector4f(50, 0, 0, 0);
				vert[3].pos = Rainbow::Vector4f(50, 0, 100, 0);
			}
			else if (5 == d)
			{
				vert[0].pos = Rainbow::Vector4f(100, 100, 0, 0);
				vert[1].pos = Rainbow::Vector4f(100, 100, 100, 0);
				vert[2].pos = Rainbow::Vector4f(50, 0, 100, 0);
				vert[3].pos = Rainbow::Vector4f(50, 0, 0, 0);
			}
			else if (6 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 100, 0, 0);
				vert[1].pos = Rainbow::Vector4f(100, 100, 0, 0);
				vert[2].pos = Rainbow::Vector4f(100, 0, 50, 0);
				vert[3].pos = Rainbow::Vector4f(0, 0, 50, 0);
			}
			else if (7 == d)
			{
				vert[0].pos = Rainbow::Vector4f(100, 100, 100, 0);
				vert[1].pos = Rainbow::Vector4f(0, 100, 100, 0);
				vert[2].pos = Rainbow::Vector4f(0, 0, 50, 0);
				vert[3].pos = Rainbow::Vector4f(100, 0, 50, 0);
			}
			uvMap.push_back({ 1, 1 });
			uvMap.push_back({ 0, 1 });
			uvMap.push_back({ 0, 0 });
			uvMap.push_back({ 1, 0 });

			for (int oo = 0; oo < vertLen; oo++)
			{
				vert[oo].uv = { short(uvMap[oo].x * BLOCKUV_SCALE), short(uvMap[oo].y * BLOCKUV_SCALE) };
				vert[oo].normal = normal_dir;
				vert[oo].color.SetUInt32(vertcolor.v);
				vert[oo].pos.w = 0;
				vertices.push_back(vert[oo]);
			}
			m_mSlantFace.push_back(vertices);
			//ParseLuaVertCfg("lua_initSlantFaceVertData_CFG", m_mSlantFace, normal_dir, vertcolor, d, vert, vertLen);
		}
	}
}

void TriangularPrismMaterial::initTriangleSlantFaceVertData()
{
	for (int i = 0; i < 2; i++) 
	{
		for (int k = 0; k < 2; k++)
		{
			for (int j = 0; j < 4; j++)
			{
				DirectionType dir = (DirectionType)j;
				dynamic_array<UInt16>* indices = m_PosTrIndices;
				int d = i * 8 + k * 4 + j;
				dynamic_array<BlockGeomVert> vertices;
				Rainbow::Vector3f normalVec = g_DirectionCoord[j].toVector3();
				Normalize(normalVec);
				BlockVector normal_dir = PackVertNormal(normalVec);
				BlockVector vertcolor;
				vertcolor.v = 0xffffffff;
				vertcolor.w = 0;

				const int vertLen = 3;
				BlockGeomVert vert[vertLen];
				std::vector<Rainbow::Vector2f> uvMap;
				uvMap.resize(0);
				if (0 == d)
				{
					vert[0].pos = Rainbow::Vector4f(50, 100, 50, 0);
					vert[1].pos = Rainbow::Vector4f(50, 100, 0, 0);
					vert[2].pos = Rainbow::Vector4f(0, 0, 0, 0);
				}
				else if (1 == d)
				{
					vert[0].pos = Rainbow::Vector4f(50, 100, 50, 0);
					vert[1].pos = Rainbow::Vector4f(50, 100, 100, 0);
					vert[2].pos = Rainbow::Vector4f(100, 0, 100, 0);
				}
				else if (2 == d)
				{
					vert[0].pos = Rainbow::Vector4f(50, 100, 50, 0);
					vert[1].pos = Rainbow::Vector4f(100, 100, 50, 0);
					vert[2].pos = Rainbow::Vector4f(100, 0, 0, 0);
				}
				else if (3 == d)
				{
					vert[0].pos = Rainbow::Vector4f(50, 100, 50, 0);
					vert[1].pos = Rainbow::Vector4f(0, 100, 50, 0);
					vert[2].pos = Rainbow::Vector4f(0, 0, 100, 0);
				}
				else if (4 == d)
				{
					vert[0].pos = Rainbow::Vector4f(50, 100, 50, 0);
					vert[1].pos = Rainbow::Vector4f(0, 0, 100, 0);
					vert[2].pos = Rainbow::Vector4f(50, 100, 100, 0);
				}
				else if (5 == d)
				{
					vert[0].pos = Rainbow::Vector4f(50, 100, 50, 0);
					vert[1].pos = Rainbow::Vector4f(100, 0, 0, 0);
					vert[2].pos = Rainbow::Vector4f(50, 100, 0, 0);
				}
				else if (6 == d)
				{
					vert[0].pos = Rainbow::Vector4f(50, 100, 50, 0);
					vert[1].pos = Rainbow::Vector4f(0, 0, 0, 0);
					vert[2].pos = Rainbow::Vector4f(0, 100, 50, 0);
				}
				else if (7 == d)
				{
					vert[0].pos = Rainbow::Vector4f(50, 100, 50, 0);
					vert[1].pos = Rainbow::Vector4f(100, 0, 100, 0);
					vert[2].pos = Rainbow::Vector4f(100, 100, 50, 0);
				}else if (8 == d)
				{
					vert[0].pos = Rainbow::Vector4f(50, 0, 50, 0);
					vert[1].pos = Rainbow::Vector4f(0, 100, 0, 0);
					vert[2].pos = Rainbow::Vector4f(50, 0, 0, 0);
				}
				else if (9 == d)
				{
					vert[0].pos = Rainbow::Vector4f(50, 0, 50, 0);
					vert[1].pos = Rainbow::Vector4f(100, 100, 100, 0);
					vert[2].pos = Rainbow::Vector4f(50, 0, 100, 0);
				}
				else if (10 == d)
				{
					vert[0].pos = Rainbow::Vector4f(50, 0, 50, 0);
					vert[1].pos = Rainbow::Vector4f(100, 100, 0, 0);
					vert[2].pos = Rainbow::Vector4f(100, 0, 50, 0);
				}
				else if (11 == d)
				{
					vert[0].pos = Rainbow::Vector4f(50, 0, 50, 0);
					vert[1].pos = Rainbow::Vector4f(0, 100, 100, 0);
					vert[2].pos = Rainbow::Vector4f(0, 0, 50, 0);
				}
				else if (12 == d)
				{
					vert[0].pos = Rainbow::Vector4f(50, 0, 50, 0);
					vert[1].pos = Rainbow::Vector4f(50, 0, 100, 0);
					vert[2].pos = Rainbow::Vector4f(0, 100, 100, 0);
				}
				else if (13 == d)
				{
					vert[0].pos = Rainbow::Vector4f(50, 0, 50, 0);
					vert[1].pos = Rainbow::Vector4f(50, 0, 0, 0);
					vert[2].pos = Rainbow::Vector4f(100, 100, 0, 0);
				}
				else if (14 == d)
				{
					vert[0].pos = Rainbow::Vector4f(50, 0, 50, 0);
					vert[1].pos = Rainbow::Vector4f(0, 0, 50, 0);
					vert[2].pos = Rainbow::Vector4f(0, 100, 0, 0);
				}
				else if (15 == d)
				{
					vert[0].pos = Rainbow::Vector4f(50, 0, 50, 0);
					vert[1].pos = Rainbow::Vector4f(100, 0, 50, 0);
					vert[2].pos = Rainbow::Vector4f(100, 100, 100, 0);
				}
				if ((d >= 0 && d <= 3) || (d >= 12 && d <= 15))
				{
					uvMap.push_back({ 0.5, 0 });
					uvMap.push_back({ 1, 0 });
					uvMap.push_back({ 1, 1 });

				}
				else
				{
					uvMap.push_back({ 0.5, 0 });
					uvMap.push_back({ 0, 1 });
					uvMap.push_back({ 0, 0 });
				}
				
				for (int oo = 0; oo < vertLen; oo++)
				{
					vert[oo].uv = { short(uvMap[oo].x * BLOCKUV_SCALE), short(uvMap[oo].y * BLOCKUV_SCALE) };
					vert[oo].normal = normal_dir;
					vert[oo].color.SetUInt32(vertcolor.v);
					vert[oo].pos.w = 0;
					vertices.push_back(vert[oo]);
				}
				m_mTriangleSlantFace.push_back(vertices);
			}
		}
		
	}
}

void TriangularPrismMaterial::initTriangleSlantFace2VertData()
{
	for (int i = 0; i < 2; i++)
	{
		for (int j = 0; j < 4; j++)
		{
			DirectionType dir = (DirectionType)j;
			dynamic_array<UInt16>* indices = m_PosTrIndices;
			int d = i * 4 + j;
			// unsigned short dir_color = TriangleNormal2LightColor(g_DirectionCoord[dir].toVector3());
			dynamic_array<BlockGeomVert> vertices;
			Rainbow::Vector3f normalVec = g_DirectionCoord[j].toVector3();
			////unsigned short dir_color = Normal2LightColor(normalVec);
			Normalize(normalVec);
			BlockVector normal_dir = PackVertNormal(normalVec);
			BlockVector vertcolor;
			vertcolor.v = 0xffffffff;
			vertcolor.w = 0;

			const int vertLen = 3;
			BlockGeomVert vert[vertLen];

			std::vector<Rainbow::Vector2f> uvMap;
			uvMap.resize(0);
			if (0 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
				vert[1].pos = Rainbow::Vector4f(0, 0, 100, 0);
				vert[2].pos = Rainbow::Vector4f(50, 100, 50, 0);
			}
			else if (1 == d)
			{
				vert[0].pos = Rainbow::Vector4f(100, 0, 0, 0);
				vert[1].pos = Rainbow::Vector4f(50, 100, 50, 0);
				vert[2].pos = Rainbow::Vector4f(100, 0, 100, 0);
			}
			else if (2 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
				vert[1].pos = Rainbow::Vector4f(50, 100, 50, 0);
				vert[2].pos = Rainbow::Vector4f(100, 0, 0, 0);
			}
			else if (3 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 0, 100, 0);
				vert[1].pos = Rainbow::Vector4f(100, 0, 100, 0);
				vert[2].pos = Rainbow::Vector4f(50, 100, 50, 0);
			}
			else if (4 == d)
			{
				vert[0].pos = Rainbow::Vector4f(50, 0, 50, 0);
				vert[1].pos = Rainbow::Vector4f(0, 100, 100, 0);
				vert[2].pos = Rainbow::Vector4f(0, 100, 0, 0);
			}
			else if (5 == d)
			{
				vert[0].pos = Rainbow::Vector4f(100, 100, 0, 0);
				vert[1].pos = Rainbow::Vector4f(100, 100, 100, 0);
				vert[2].pos = Rainbow::Vector4f(50, 0, 50, 0);
			}
			else if (6 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 100, 0, 0);
				vert[1].pos = Rainbow::Vector4f(100,100, 0, 0);
				vert[2].pos = Rainbow::Vector4f(50, 0, 50, 0);
			}
			else if (7 == d)
			{
				vert[0].pos = Rainbow::Vector4f(50, 0, 50, 0);
				vert[1].pos = Rainbow::Vector4f(100, 100, 100, 0);
				vert[2].pos = Rainbow::Vector4f(0, 100, 100, 0);
			}
			
			uvMap.push_back({ 0.5, 0 });
			uvMap.push_back({ 1, 1 });
			uvMap.push_back({ 0, 1 });
			for (int oo = 0; oo < vertLen; oo++)
			{
				vert[oo].uv = { short(uvMap[oo].x * BLOCKUV_SCALE), short(uvMap[oo].y * BLOCKUV_SCALE) };
				vert[oo].normal = normal_dir;
				vert[oo].color.SetUInt32(vertcolor.v);
				vert[oo].pos.w = 0;
				vertices.push_back(vert[oo]);
			}
			m_mTriangleSlantFace2.push_back(vertices);

			//ParseLuaVertCfg("lua_initTriangleFace2VertData_CFG", m_mTriangleSlantFace2, normal_dir, vertcolor, d, vert, vertLen);
		}
	}
}

void TriangularPrismMaterial::initTrapezoidSlantFaceVertData()
{
	for (int i = 0; i < 2; i++) //����
	{
		for (int k = 0; k < 2; k++) // ÿ������2������
		{
			for (int j = 0; j < 4; j++)
			{
				DirectionType dir = (DirectionType)j;
				dynamic_array<UInt16>* indices = m_dPosIndices;
				int d = i * 8 + k * 4 + j;
				dynamic_array<BlockGeomVert> vertices;
				Rainbow::Vector3f normalVec = (g_DirectionCoord[j] + g_DirectionCoord[i ? 4 : 5]).toVector3();
				//unsigned short dir_color = Normal2LightColor(normalVec);
				BlockVector vertcolor;
				vertcolor.v = 0xffffffff;
				vertcolor.w = 0;
				Normalize(normalVec);
				BlockVector normal_dir = PackVertNormal(normalVec);

				const int vertLen = 4;
				BlockGeomVert vert[vertLen];

				std::vector<Rainbow::Vector2f> uvMap;
				uvMap.resize(0);
				if (0 == d)
				{
					vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
					vert[1].pos = Rainbow::Vector4f(0, 0, 100, 0);
					vert[2].pos = Rainbow::Vector4f(50, 100, 50, 0);
					vert[3].pos = Rainbow::Vector4f(50, 100, 0, 0);
				}
				else if (1 == d)
				{
					vert[0].pos = Rainbow::Vector4f(100, 0, 100, 0);
					vert[1].pos = Rainbow::Vector4f(100, 0, 0, 0);
					vert[2].pos = Rainbow::Vector4f(50, 100, 50, 0);
					vert[3].pos = Rainbow::Vector4f(50, 100, 100, 0);
				}
				else if (2 == d)
				{
					vert[0].pos = Rainbow::Vector4f(100, 0, 0, 0);
					vert[1].pos = Rainbow::Vector4f(0, 0, 0, 0);
					vert[2].pos = Rainbow::Vector4f(50, 100, 50, 0);
					vert[3].pos = Rainbow::Vector4f(100, 100, 50, 0);
				}
				else if (3 == d)
				{
					vert[0].pos = Rainbow::Vector4f(0, 0, 100, 0);
					vert[1].pos = Rainbow::Vector4f(100, 0, 100, 0);
					vert[2].pos = Rainbow::Vector4f(50, 100, 50, 0);
					vert[3].pos = Rainbow::Vector4f(0, 100, 50, 0);
				}
				else if (4 == d)
				{
					vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
					vert[1].pos = Rainbow::Vector4f(0, 0, 100, 0);
					vert[2].pos = Rainbow::Vector4f(50, 100, 100, 0);
					vert[3].pos = Rainbow::Vector4f(50, 100, 50, 0);
				}
				else if (5 == d)
				{
					vert[0].pos = Rainbow::Vector4f(100, 0, 100, 0);
					vert[1].pos = Rainbow::Vector4f(100, 0, 0, 0);
					vert[2].pos = Rainbow::Vector4f(50, 100, 0, 0);
					vert[3].pos = Rainbow::Vector4f(50, 100, 50, 0);
				}
				else if (6 == d)
				{
					vert[0].pos = Rainbow::Vector4f(100, 0, 0, 0);
					vert[1].pos = Rainbow::Vector4f(0, 0, 0, 0);
					vert[2].pos = Rainbow::Vector4f(0, 100, 50, 0);
					vert[3].pos = Rainbow::Vector4f(50, 100, 50, 0);
				}
				else if (7 == d)
				{
					vert[0].pos = Rainbow::Vector4f(0, 0, 100, 0);
					vert[1].pos = Rainbow::Vector4f(100, 0, 100, 0);
					vert[2].pos = Rainbow::Vector4f(100, 100, 50, 0);
					vert[3].pos = Rainbow::Vector4f(50, 100, 50, 0);
				}
				else if (8 == d)
				{
					vert[0].pos = Rainbow::Vector4f(0, 100, 100, 0);
					vert[1].pos = Rainbow::Vector4f(0, 100, 0, 0);
					vert[2].pos = Rainbow::Vector4f(50, 0, 0, 0);
					vert[3].pos = Rainbow::Vector4f(50, 0, 50, 0);
				}
				else if (9 == d)
				{
					vert[0].pos = Rainbow::Vector4f(100, 100, 0, 0);
					vert[1].pos = Rainbow::Vector4f(100, 100, 100, 0);
					vert[2].pos = Rainbow::Vector4f(50, 0, 100, 0);
					vert[3].pos = Rainbow::Vector4f(50, 0, 50, 0);
				}
				else if (10 == d)
				{
					vert[0].pos = Rainbow::Vector4f(0, 100, 0, 0);
					vert[1].pos = Rainbow::Vector4f(100, 100, 0, 0);
					vert[2].pos = Rainbow::Vector4f(100, 0, 50, 0);
					vert[3].pos = Rainbow::Vector4f(50, 0, 50, 0);
				}
				else if (11 == d)
				{
					vert[0].pos = Rainbow::Vector4f(100, 100, 100, 0);
					vert[1].pos = Rainbow::Vector4f(0, 100, 100, 0);
					vert[2].pos = Rainbow::Vector4f(0, 0, 50, 0);
					vert[3].pos = Rainbow::Vector4f(50, 0, 50, 0);
				}
				else if (12 == d)
				{
					vert[0].pos = Rainbow::Vector4f(0, 100, 100, 0);
					vert[1].pos = Rainbow::Vector4f(0, 100, 0, 0);
					vert[2].pos = Rainbow::Vector4f(50, 0, 50, 0);
					vert[3].pos = Rainbow::Vector4f(50, 0, 100, 0);
				}
				else if (13 == d)
				{
					vert[0].pos = Rainbow::Vector4f(100, 100, 0, 0);
					vert[1].pos = Rainbow::Vector4f(100, 100, 100, 0);
					vert[2].pos = Rainbow::Vector4f(50, 0, 50, 0);
					vert[3].pos = Rainbow::Vector4f(50, 0, 0, 0);
				}
				else if (14 == d)
				{
					vert[0].pos = Rainbow::Vector4f(0, 100, 0, 0);
					vert[1].pos = Rainbow::Vector4f(100, 100, 0, 0);
					vert[2].pos = Rainbow::Vector4f(50, 0, 50, 0);
					vert[3].pos = Rainbow::Vector4f(0, 0, 50, 0);
				}
				else if (15 == d)
				{
					vert[0].pos = Rainbow::Vector4f(100, 100, 100, 0);
					vert[1].pos = Rainbow::Vector4f(0, 100, 100, 0);
					vert[2].pos = Rainbow::Vector4f(50, 0, 50, 0);
					vert[3].pos = Rainbow::Vector4f(100, 0, 50, 0);
				}
				
				if ((d >= 0 && d <= 3) || (d >= 12 && d <= 15))
				{
					uvMap.push_back({ 1, 1 });
					uvMap.push_back({ 0, 1 });
					uvMap.push_back({ 0.5, 0 });
					uvMap.push_back({ 1, 0 });
				}
				else
				{
					uvMap.push_back({ 1, 1 });
					uvMap.push_back({ 0, 1 });
					uvMap.push_back({ 0, 0 });
					uvMap.push_back({ 0.5, 0 });
				}

				for (int oo = 0; oo < 4; oo++)
				{
					vert[oo].uv = { short(uvMap[oo].x * BLOCKUV_SCALE), short(uvMap[oo].y * BLOCKUV_SCALE) };
					vert[oo].normal = normal_dir;
					vert[oo].color.SetUInt32(vertcolor.v);
					vert[oo].pos.w = 0;
					vertices.push_back(vert[oo]);
				}
				m_mTrapezoidSlantFace.push_back(vertices);
				//ParseLuaVertCfg("lua_initTrapezoidSlantFaceVertData_CFG", m_mTrapezoidSlantFace, normal_dir, vertcolor, d, vert, vertLen);
			}
		}
	}
}

int TriangularPrismMaterial::getPhisicMeshVerts(BaseSection* psection, const WCoord& blockpos, dynamic_array<Rainbow::Vector3f>& verts, dynamic_array<UInt16>& idxs)
{
#ifdef IWORLD_SERVER_BUILD
	if (!m_mPhyModel.size())
	{
		initVertData();
	}
#endif	
	Block pblock = psection->getBlock(blockpos);
	int blockdata = pblock.getData();

	float blockheight = getBlockHeight(blockdata);
	DirectionType specialdir = DIR_NOT_INIT;
	if (blockheight > 0 && blockheight < 1.0f) specialdir = DIR_POS_Y;
	else if (blockheight<0 && blockheight>-1.0f) specialdir = DIR_NEG_Y;

	DirectionType curDir = DirectionType(blockdata & 3);
	
	if (getMergeType(psection, pblock, blockpos, curDir) & MergeTurn)
	{
		if (m_mPhyModel.find(10000 + blockdata) != m_mPhyModel.end())
		{
			TriangularPrismPhyModel* pTag = &m_mPhyModel[10000 + blockdata];
			verts = pTag->verts;
			idxs = pTag->idxs;
			return  pTag->triangleCount;
		}
	}
	else
	{
		if (m_mPhyModel.find(blockdata) != m_mPhyModel.end())
		{
			TriangularPrismPhyModel* pTag = &m_mPhyModel[blockdata];
			verts = pTag->verts;
			idxs = pTag->idxs;
			return  pTag->triangleCount;
		}
	}

	return 0;
}

void TriangularPrismMaterial::initPhyModelData()
{
	if (m_mWholeFace().size() == 0 || m_mTriangleFace.size() == 0 || m_mSlantFace.size() == 0 || m_mTrapezoidSlantFace.size() == 0 || m_mTriangleSlantFace.size() == 0)
	{
		return;
	}

	// ������
	int triangleFaceId[4] = { 2, 2, 0, 0 };
	int slantFaceId[4] = { 0, 0, 2, 2 };
	for (int i = 0; i < 2; i++)
	{
		for (int j = 0; j < 4; j++)
		{
			MergeType mergeType = MergeNone;

			int bOnlyOneSidePlace = 0; // 0 ��û��ֱ��

			TriangularPrismPhyModel model;
			dynamic_array<dynamic_array<BlockGeomVert>*> faceList;
			//4 vert
			faceList.push_back(0 == i ? &m_mWholeFace()[4] : &m_mWholeFace()[5]);
			//faceList.push_back(&m_mWholeFace()[ReverseDirection(j)]);
			faceList.push_back(&m_mTriangleFace[4 * i + triangleFaceId[j]]);
			faceList.push_back(&m_mTriangleFace[4 * i + triangleFaceId[j] + 1]);

			faceList.push_back(&m_mSlantFace[4 * i + slantFaceId[j]]);
			faceList.push_back(&m_mSlantFace[4 * i + slantFaceId[j] + 1]);


			int triangleCount = 0;
			for (auto face : faceList)
			{
				dynamic_array<short> indexlist;
				for (auto& vertdata : (*face))
				{
					Rainbow::Vector3f pos(vertdata.pos.x, vertdata.pos.y, vertdata.pos.z);
					short index = -1;
					for (int o = 0; o < model.verts.size(); o++)
					{
						if (model.verts[o] == pos)
						{
							index = o;
							break;
						}
					}
					if (index == -1)
					{
						index = (short)model.verts.size();
						model.verts.push_back(pos);
					}
					indexlist.push_back(index);
				}
				dynamic_array<UInt16>* posIndeices = m_dPosIndices;
				if (indexlist.size() == 3)
				{
					posIndeices = m_PosTrIndices;
					triangleCount++;
				}
				else if (indexlist.size() > 3)
				{
					triangleCount += indexlist.size() - 2;
				}
				for (auto& idxex : (*posIndeices))
				{
					model.idxs.push_back(indexlist[idxex]);
				}
			}
			model.triangleCount = triangleCount;
			m_mPhyModel.insert(make_pair(i * 4 + j, model));
		}
	}

	// ׶��
	for (int i = 0; i < 2; i++)
	{
		for (int j = 0; j < 4; j++)
		{
			MergeType mergeType = MergeNone;

			int bOnlyOneSidePlace = 0; // 0 ��û��ֱ��

			TriangularPrismPhyModel model;
			dynamic_array<dynamic_array<BlockGeomVert>*> faceList;

			//4 vert
			faceList.push_back(0 == i ? &m_mWholeFace()[4] : &m_mWholeFace()[5]);
			faceList.push_back(&m_mTriangleSlantFace2[4 * i + 0]);
			faceList.push_back(&m_mTriangleSlantFace2[4 * i + 1]);
			faceList.push_back(&m_mTriangleSlantFace2[4 * i + 2]);
			faceList.push_back(&m_mTriangleSlantFace2[4 * i + 3]);

			int triangleCount = 0;
			for (auto face : faceList)
			{
				dynamic_array<short> indexlist;
				for (auto& vertdata : (*face))
				{
					Rainbow::Vector3f pos(vertdata.pos.x, vertdata.pos.y, vertdata.pos.z);
					short index = -1;
					for (int o = 0; o < model.verts.size(); o++)
					{
						if (model.verts[o] == pos)
						{
							index = o;
							break;
						}
					}
					if (index == -1)
					{
						index = (short)model.verts.size();
						model.verts.push_back(pos);
					}
					indexlist.push_back(index);
				}
				dynamic_array<UInt16>* posIndeices = m_dPosIndices;
				if (indexlist.size() == 3)
				{
					posIndeices = m_PosTrIndices;
					triangleCount++;
				}
				else if (indexlist.size() > 3)
				{
					triangleCount += indexlist.size() - 2;
				}
				for (auto& idxex : (*posIndeices))
				{
					model.idxs.push_back(indexlist[idxex]);
				}
			}
			model.triangleCount = triangleCount;
			m_mPhyModel.insert(make_pair(10000 + i * 4 + j, model));
		}
	}
}

SectionMesh* TriangularPrismMaterial::createBlockProtoMesh(int protodata)
{
	SectionMesh* pmesh = ENG_NEW(SectionMesh)();
	int blockdata = protodata;

	const BiomeDef* biome = GetDefManagerProxy()->getBiomeDef(0);
	//  	dynamic_array<UInt16>* indices = &m_dPosIndices;

	char list[5] = { 4,2,3,0,1 };
	BlockColor facecolor(255, 255, 255, 0);
	for (int i = 0; i < 5; i++)
	{
		DirectionType dir = (DirectionType)(list[i] % 4);
		RenderBlockMaterial* pmtl = getFaceMtl(biome, dir, blockdata, facecolor);
		SectionSubMesh* psubmesh = pmesh->getSubMesh(pmtl, true);

		BlockGeomMeshInfo meshinfo;
		if (i < 1)
		{
			meshinfo.vertices = m_mWholeFace()[list[i]];
			meshinfo.indices = *m_dPosIndices;
		}
		else if (i < 3)
		{
			meshinfo.vertices = m_mTriangleFace[list[i]];
			meshinfo.indices = *m_PosTrIndices;
		}
		else
		{
			meshinfo.vertices = m_mSlantFace[list[i]];
			meshinfo.indices = *m_dPosIndices;
		}
		if (psubmesh)
			psubmesh->addGeomFaceLight(meshinfo, NULL, s_DefaultFaceVertexLights, &facecolor);
	}
	return pmesh;
}

typedef TriangularPrismMaterial::BlockInstance TriangularPrismMaterialInstance;
IMPLEMENT_SCENEOBJECTCLASS(TriangularPrismMaterialInstance)
MNSandbox::ReflexClassParam<TriangularPrismMaterial::BlockInstance, int> TriangularPrismMaterialInstance::R_Dir(0, "Dir", "Block", &TriangularPrismMaterial::BlockInstance::GetBlockData, &TriangularPrismMaterial::BlockInstance::SetBlockData);

void TriangularPrismMaterial::ParseLuaVertCfg(const char* strFuncName, dynamic_array<dynamic_array<BlockGeomVert>>& vecFaces, BlockVector& normal_dir, BlockVector& vertcolor, int dir, BlockGeomVert vert[], const int vertLen)
{
	jsonxx::Object vertJsonObject;
	char cfg[10240] = "";
	MINIW::ScriptVM::game()->callFunction(strFuncName, "i>s", dir, cfg);
	vertJsonObject.parse(cfg);
	
	if (vertJsonObject.has<jsonxx::Array>("faceVerCfg"))
	{
		jsonxx::Array cfgArray = vertJsonObject.get<jsonxx::Array>("faceVerCfg");
		for (size_t idx = 0; idx < cfgArray.size(); idx++)
		{
			if (cfgArray.values().at(idx)->is<jsonxx::Object>())
			{
				jsonxx::Object cfg = cfgArray.get<jsonxx::Object>(idx);
				if (cfg.has<jsonxx::Number>("dir"))
				{
					int d = (int)cfg.get<jsonxx::Number>("dir");
					if (d == dir)
					{
						if (cfg.has<jsonxx::String>("vertPos"))
						{
							core::string str = cfg.get<jsonxx::String>("vertPos");
							std::vector<std::string> strVec;
							Rainbow::StringUtil::split(strVec, str, ",");

							std::vector<int> iVec;
							for (vector<std::string>::iterator it = strVec.begin(); it != strVec.end(); ++it)
							{
								std::string posIdx = *(it);
								iVec.push_back(atoi(posIdx.c_str()));
							}

							int colLen = 4;
							for (int verIdx = 0; verIdx < vertLen; verIdx++)
							{
								vert[verIdx].pos = Rainbow::Vector4f(iVec[verIdx * colLen + 0], iVec[verIdx * colLen + 1], iVec[verIdx * colLen + 2], iVec[verIdx * colLen + 3]);
							}
						}

						std::vector<Rainbow::Vector2f> uvMap;
						uvMap.resize(0);

						if (cfg.has<jsonxx::String>("uvMap"))
						{
							core::string str = cfg.get<jsonxx::String>("uvMap");
							std::vector<std::string> strVec;
							Rainbow::StringUtil::split(strVec, str, ",");

							std::vector<float> fVec;
							for (vector<std::string>::iterator it = strVec.begin(); it != strVec.end(); ++it)
							{
								std::string posIdx = *(it);
								fVec.push_back(atof(posIdx.c_str()));
							}

							const int colLen = 2;
							for (int verIdx = 0; verIdx < vertLen; verIdx++)
							{
								uvMap.push_back({ fVec[verIdx * colLen + 0] , fVec[verIdx * colLen + 1] });
							}
						}

						dynamic_array<BlockGeomVert> vertices;
						
						for (int oo = 0; oo < vertLen; oo++)
						{
							vert[oo].uv = { short(uvMap[oo].x * BLOCKUV_SCALE), short(uvMap[oo].y * BLOCKUV_SCALE) };
							vert[oo].normal = normal_dir;
							vert[oo].color.SetUInt32(vertcolor.v);
							vert[oo].pos.w = 0;//(lt1 << 8) | lt2;
							vertices.push_back(vert[oo]);
						}
						vecFaces.push_back(vertices);

						return;
					}
				}
			}
		}
	}
	
	
}

void TriangularPrismMaterial::ParseLuaFaceCfg(const char* strFuncName, vector<int>& vec, int dir)
{
	jsonxx::Object vertJsonObject;
	char cfg[10240] = "";
	MINIW::ScriptVM::game()->callFunction(strFuncName, "i>s", dir, cfg);
	vertJsonObject.parse(cfg);

	if (vertJsonObject.has<jsonxx::Array>("faceCfg"))
	{
		jsonxx::Array cfgArray = vertJsonObject.get<jsonxx::Array>("faceCfg");
		for (size_t idx = 0; idx < cfgArray.size(); idx++)
		{
			if (cfgArray.values().at(idx)->is<jsonxx::Object>())
			{
				jsonxx::Object cfg = cfgArray.get<jsonxx::Object>(idx);
				if (cfg.has<jsonxx::Number>("dir"))
				{
					int d = (int)cfg.get<jsonxx::Number>("dir");
					if (d == dir)
					{
						if (cfg.has<jsonxx::String>("faceIdx"))
						{
							core::string str = cfg.get<jsonxx::String>("faceIdx");
							std::vector<std::string> strVec;
							Rainbow::StringUtil::split(strVec, str, ",");

							for (vector<std::string>::iterator it = strVec.begin(); it != strVec.end(); ++it)
							{
								std::string posIdx = *(it);
								vec.push_back(atoi(posIdx.c_str()));
							}
						}
						
						return;
					}
				}
			}
		}
	}


}
