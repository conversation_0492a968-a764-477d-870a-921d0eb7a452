#include "BlockSimpleWindow.h"
#include "BlockGeom.h"
#include "BlockMaterialMgr.h"
#include "WorldProxy.h"
#include "WorldManager.h"
#include "SandBoxManager.h"
#include "SectionMesh.h"

IMPLEMENT_BLOCKMATERIAL(BlockSimpleWindow)
IMPLEMENT_BLOCKMATERIAL(BlockFridge)
BlockSimpleWindow::BlockSimpleWindow()
{

}

BlockSimpleWindow::~BlockSimpleWindow()
{
}


void BlockSimpleWindow::init(int resid)
{
	ModelBlockMaterial::init(resid);
}

void BlockSimpleWindow::initDrawType()
{
	m_blockDrawType = BLOCKDRAW_XPARENT;
}

void BlockSimpleWindow::initGeomName()
{
	m_geomName = m_Def->Texture1.c_str();
}

//const char * BlockSimpleWindow::getGeomName()
//{
//	return GetBlockDef()->Texture1.c_str();
//}
const char * BlockSimpleWindow::getBaseTexName(char * texname, const BlockDef * def, int & gettextype)
{
	gettextype = GETTEX_WITHDEFAULT;

	return GetBlockDef()->Texture1.c_str();
}

void BlockSimpleWindow::onNotify(World * pworld, const WCoord & blockpos, int blockid)
{
	int blockdata = pworld->getBlockData(blockpos);
	bool isupper = (blockdata & 4) != 0;

	if (isupper)
	{
		if (pworld->getBlockID(blockpos + WCoord(0, -1, 0)) != getBlockResID())
		{
			pworld->setBlockAir(blockpos);
		}
		if (blockid > 0 && blockid != getBlockResID())
		{
			onNotify(pworld, blockpos + WCoord(0, -1, 0), blockid);
		}
	}
	else
	{
		bool toair = false;
		WCoord upcoord = blockpos + WCoord(0, 1, 0);
		if (pworld->getBlockID(upcoord) != getBlockResID() )
		{
			pworld->setBlockAir(blockpos);
			toair = true;
		}

		if (!pworld->doesBlockHaveSolidTopSurface(blockpos + WCoord(0, -1, 0)))
		{
			int bid = pworld->getBlockID(blockpos);
			int blockdata = pworld->getBlockData(blockpos);

			pworld->setBlockAir(blockpos);
			toair = true;

			pworld->notifyToRecycleBlock(SandBoxMgrEventID::EVENT_HOMELAND_DESTROY_NOTIFY_RECYCLE, 0, 0, bid, blockdata);

			if (pworld->getBlockID(upcoord) == getBlockResID())
			{
				pworld->setBlockAir(upcoord);
			}
		}

		if (toair)
		{
			//dropBlockAsItem(pworld, blockpos, blockdata);
		}
	}
}

void BlockSimpleWindow::dropBlockAsItem(World * pworld, const WCoord & blockpos, int blockdata, BLOCK_MINE_TYPE droptype, float chance, int uin)
{
	bool isupper = (blockdata & 4) != 0;
	if (!isupper)
	{
		//ModelBlockMaterial::dropBlockAsItem(pworld, blockpos, blockdata, droptype, chance);
	}
}

bool BlockSimpleWindow::canPutOntoPos(WorldProxy * pworld, const WCoord & blockpos)
{
	if (!pworld->doesBlockHaveSolidTopSurface(blockpos + WCoord(0, -1, 0))) return false;

	if (!BlockMaterial::canPutOntoPos(pworld, blockpos)) return false;
	if (!BlockMaterial::canPutOntoPos(pworld, blockpos + WCoord(0, 1, 0))) return false;
	return true;
}

void BlockSimpleWindow::createBlockMesh(const BuildSectionMeshData& data, const WCoord & blockpos, SectionMesh * poutmesh)
{
	BlockGeomTemplate* geom = getGeom(data.m_LODLevel);
	if (!geom)
		return;

	auto psection = data.m_SharedSectionData;
	bool mirror = false;
	int blockdata = psection->getBlock(blockpos).getData();

	int data1 = blockdata >> 2;
	int dir = blockdata & 3;
	Rainbow::ColorRGBAf verts_light[1] = { Rainbow::ColorRGBAf::black };
	RenderBlockMaterial *mtl = getDefaultMtl();
	BlockGeomMeshInfo meshinfo;
	int mirrorY = 2;
	if (data1 == 1)
		geom->getFaceVerts(meshinfo, 0, 1.0f, 0, dir, mirrorY);
	else
		geom->getFaceVerts(meshinfo, 0, 1.0f, 0, dir, 0);

	SectionSubMesh *psubmesh = poutmesh->getSubMesh(mtl);

	psection->getBlockVertexLight(blockpos, verts_light);
	psubmesh->addGeomBlockLight(meshinfo, &blockpos, verts_light, NULL, mtl->getUVTile());
}
SectionMesh *BlockSimpleWindow::createBlockProtoMesh(int protodata)
{
	SectionMesh* pmesh = ENG_NEW(SectionMesh)();
	SectionSubMesh* psubmesh = pmesh->getSubMesh(getDefaultMtl(), true);

	BlockGeomMeshInfo meshinfo;

	WCoord center(0, -BLOCK_SIZE / 2, 0);

	getGeom()->getFaceVerts(meshinfo, 0, 1.0f, 0, 2, 0);
	psubmesh->addTriangleList(meshinfo.vertices.data(), meshinfo.vertices.size(), meshinfo.indices.data(), meshinfo.indices.size(), &center);

	getGeom()->getFaceVerts(meshinfo, 0, 1.0f, 0, 2, 2);
	WCoord pos(0, BLOCK_SIZE, 0);
	pos += center;
	psubmesh->addTriangleList(meshinfo.vertices.data(), meshinfo.vertices.size(), meshinfo.indices.data(), meshinfo.indices.size(), &pos);
	return pmesh;
}

void BlockFridge::init(int resid)
{
	BlockSimpleWindow::init(resid);

	
}

void BlockFridge::initDrawType()
{
	m_blockDrawType = BLOCKDRAW_OPAQUE;
}
int BlockFridge::getBlockGeomID(int *idbuf, int *dirbuf, const SectionDataHandler* sectionData, const WCoord &blockpos, World* world)
{
	int blockdata = sectionData->getBlock(blockpos).getData();

	int modelId = blockdata >> 2;
	int dir = blockdata & 3;
	idbuf[0] = modelId;
	dirbuf[0] = dir;
	return 1;
}

void BlockFridge::createBlockMesh(const BuildSectionMeshData& data, const WCoord & blockpos, SectionMesh * poutmesh)
{
	ModelBlockMaterial::createBlockMesh(data, blockpos, poutmesh);
}
