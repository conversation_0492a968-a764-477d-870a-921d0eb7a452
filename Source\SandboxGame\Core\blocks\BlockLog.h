
#ifndef __BLOCKLOG_H__
#define __BLOCKLOG_H__

#include "BlockMaterial.h"

class LogBlockMaterial : public CubeBlockMaterial //tolua_exports
{ //tolua_exports
	DECLARE_BLOCKMATERIAL(LogBlockMaterial)
	//DECLARE_BLOCKINSTANCE(LogBlockMaterial)
public:
	//tolua_begin
	virtual void init(int resid);

	virtual RenderBlockMaterial *getFaceMtl(const BiomeDef *biome, DirectionType dir, int blockdata, BlockColor &facecolor) override;
	virtual int getPlaceBlockData(World *pworld, const WCoord &blockpos, DirectionType face, float hitptx, float hitpty, float hitptz, int def_blockdata);
	virtual void onBlockRemoved(World *pworld, const WCoord &blockpos, int blockid, int blockdata);
	virtual void onBlockPlacedBy(World *pworld, const WCoord &blockpos, IClientPlayer *player);
	virtual void onBlockAdded(World *pworld, const WCoord &blockpos);
	virtual bool renderTexRotByDir();
	virtual int convertDataByRotate(int blockdata, int rotatetype);
	virtual int getDirBlockData(int pOriData);   //低三位用来存储方向
	virtual int MergeBlockData(int pOriData,int pNewDirData);   //和第四位合并
	//tolua_end
public:
	DECLARE_BLOCKMATERIAL_INSTANCE_BEGIN(LogBlockMaterial)
		DECLARE_BLOCKMATERIAL_INSTANCE_R_PARAM(R_Dir, int)

		void SetBlockDir(const int& dir);
		int GetBlockDir() const;
	DECLARE_BLOCKMATERIAL_INSTANCE_END(LogBlockMaterial)

}; //tolua_exports

class LogCropperBlockMaterial : public LogBlockMaterial //tolua_exports
{ //tolua_exports
	DECLARE_BLOCKMATERIAL(LogCropperBlockMaterial)
public:
	//tolua_begin
	virtual void onBlockRemoved(World *pworld, const WCoord &blockpos, int blockid, int blockdata);
	virtual void onBlockAdded(World *pworld, const WCoord &blockpos);
	//tolua_end
}; //tolua_exports

class BlockScalyFruit : public LogBlockMaterial //tolua_exports
{ //tolua_exports
	DECLARE_BLOCKMATERIAL(BlockScalyFruit)

public:
	virtual void init(int resid) override;
	//tolua_begin
	virtual void dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata/* =0 */, BLOCK_MINE_TYPE droptype/* =BLOCK_MINE_NOTOOL */, float chance/* =1.0f */, int uin = -1);
	virtual bool beginVoidNightMutant(World* pworld, const WCoord& blockpos);
	//tolua_end
}; //tolua_exports

class BlockBlueFruit : public LogBlockMaterial //tolua_exports
{ //tolua_exports
	DECLARE_BLOCKMATERIAL(BlockBlueFruit)

public:
	virtual void init(int resid) override;
	//tolua_begin
	virtual void dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata/* =0 */, BLOCK_MINE_TYPE droptype/* =BLOCK_MINE_NOTOOL */, float chance/* =1.0f */, int uin = -1);
	//tolua_end
	virtual void onBlockAdded(World* pworld, const WCoord& blockpos);
	virtual bool beginVoidNightMutant(World* pworld, const WCoord& blockpos);
}; //tolua_exports

#endif