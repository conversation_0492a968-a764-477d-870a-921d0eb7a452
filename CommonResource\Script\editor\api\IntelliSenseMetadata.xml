<?xml version="1.0" encoding="UTF-8" ?>
<StudioAutocomplete>
  <LuaLibrary name="math">
    <StaticFunction name ="abs" tooltip="number math.abs(x)%1Returns the absolute value of x." />
    <StaticFunction name ="acos" tooltip="number math.acos(x)%1Returns the arc cosine of x (in radians)." />
    <StaticFunction name ="asin" tooltip="number math.asin(x)%1Returns the arc sine of x (in radians)." />
    <StaticFunction name ="atan" tooltip="number math.atan(x)%1Returns the arc tangent of x (in radians)." />
    <StaticFunction name ="atan2" tooltip="number math.atan2(y, x)%1Returns the arc tangent of y/x (in radians), but uses the signs of both parameters to find the quadrant of the result. (It also handles correctly the case of x being zero.)" />
    <StaticFunction name ="ceil" tooltip="number math.ceil(x)%1Returns the smallest integer larger than or equal to x." />
    <StaticFunction name ="cos" tooltip="number math.cos(x)%1Returns the cosine of x (assumed to be in radians)." />
    <StaticFunction name ="cosh" tooltip="number math.cosh(x)%1Returns the hyperbolic cosine of x." />
    <StaticFunction name ="deg" tooltip="number math.deg(x)%1Returns the angle x (given in radians) in degrees." />
    <StaticFunction name ="exp" tooltip="number math.exp(x)%1Returns the value e^x." />
    <StaticFunction name ="floor" tooltip="number math.floor(x)%1Returns the largest integer smaller than or equal to x." />
    <StaticFunction name ="fmod" tooltip="number math.fmod(x, y)%1Returns the remainder of the division of x by y that rounds the quotient towards zero." />
    <StaticFunction name ="frexp" tooltip="number math.frexp(x)%1Returns m and e such that x = m2^e, e is an integer and the absolute value of m is in the range [0.5, 1) (or zero when x is zero)." />
    <StaticFunction name ="ldexp" tooltip="number math.ldexp(x, e)%1Returns m2^e (e should be an integer)." />
    <StaticFunction name ="log" tooltip="number math.log(x)%1Returns the natural logarithm of x." />
    <StaticFunction name ="log10" tooltip="number math.log10(x)%1Returns the base-10 logarithm of x." />
    <StaticFunction name ="max" tooltip="number math.max(x, ...)%1Returns the maximum value among its arguments." />
    <StaticFunction name ="min" tooltip="number math.min(x, ...)%1Returns the minimum value among its arguments." />
    <StaticFunction name ="modf" tooltip="number math.modf(x)%1Returns two numbers, the integral part of x and the fractional part of x." />
	<StaticFunction name ="noise" tooltip="number math.noise(x [, y [, z]])%1Returns a value between -0.5 and 0.5 generated from its arguments" />
    <StaticFunction name ="pow" tooltip="number math.pow(x, y)%1Returns x^y. (You can also use the expression x^y to compute this value.)" />
    <StaticFunction name ="rad" tooltip="number math.rad(x)%1Returns the angle x (given in degrees) in radians." />
    <StaticFunction name ="random" tooltip="number math.random([m [, n]])%1This function is an interface to the simple pseudo-random generator function rand provided by ANSI C. (No guarantees can be given for its statistical properties.)%1%1When called without arguments, returns a uniform pseudo-random real number in the range [0,1). When called with an integer number m, math.random returns a uniform%1pseudo-random integer in the range [1, m]. When called with two integer numbers m and n, math.random returns a uniform pseudo-random integer in the range [m, n]." />
    <StaticFunction name ="randomseed" tooltip="number math.randomseed(x)%1Sets x as the seed for the pseudo-random generator: equal seeds produce equal sequences of numbers." />
    <StaticFunction name ="sin" tooltip="number math.sin(x)%1Returns the sine of x (assumed to be in radians)." />
    <StaticFunction name ="sinh" tooltip="number math.sinh(x)%1Returns the hyperbolic sine of x." />
    <StaticFunction name ="sqrt" tooltip="number math.sqrt(x)%1Returns the square root of x. (You can also use the expression x^0.5 to compute this value.)" />
    <StaticFunction name ="tan" tooltip="number math.tan(x)%1Returns the tangent of x (assumed to be in radians)." />
    <StaticFunction name ="tanh" tooltip="number math.tanh(x)%1Returns the hyperbolic tangent of x." />
    <StaticProperty name="huge" tooltip="The value HUGE_VAL, a value larger than or equal to any other numerical value."/>
    <StaticProperty name ="pi" tooltip="The value of pi."/>
  </LuaLibrary>
  <LuaLibrary name="string">
    <StaticFunction name="byte" tooltip="string string.byte(s[,i[,j]])%1Returns the internal numerical codes of the characters s[i], s[i+1], ..., s[j]. The default value for i is 1; the default value for j is i. These indices are corrected following the same rules of function string.sub."/>
    <StaticFunction name="char" tooltip="string string.char(...)%1Receives zero or more integers. Returns a string with length equal to the number of arguments, in which each character has the internal numerical code equal to its corresponding argument."/>
    <StaticFunction name="dump" tooltip="string string.dump(function)%1Returns a string containing a binary representation of the given function, so that a later load on this string returns a copy of the function (but with new upvalues)."/>
    <StaticFunction name="find" tooltip="number string.find(s, pattern [, init [, plain]])%1Looks for the first match of pattern in the string s. If it finds a match, then find returns the indices of s where this occurrence starts and ends; otherwise, it returns nil. A third, optional numerical argument init specifies where to start the search; its default value is 1 and can be negative. A value of true as a fourth, optional argument plain turns off the pattern matching facilities, so the function does a plain 'find='' substring=''' operation"/>
    <StaticFunction name="format" tooltip="string string.format(formatstring, ...)%1Returns a formatted version of its variable number of arguments following the description given in its first argument (which must be a string)."/>
    <StaticFunction name="gmatch" tooltip="function string.gmatch(s, pattern)%1Returns an iterator function that, each time it is called, returns the next captures from pattern over the string s."/>
    <StaticFunction name="gsub" tooltip="string string.gsub(s, pattern, repl [, n])%1Returns a copy of s in which all (or the first n, if given) occurrences of the pattern have been replaced by a replacement string specified by repl, which can be a string, a table, or a function. gsub also returns, as its second value, the total number of matches that occurred."/>
    <StaticFunction name="len" tooltip="number string.len(s)%1Receives a string and returns its length."/>
    <StaticFunction name="lower" tooltip="string string.lower(s)%1Receives a string and returns a copy of this string with all uppercase letters changed to lowercase."/>
    <StaticFunction name="rep" tooltip="string string.rep(s, n [, sep])%1Returns a string that is the concatenation of n copies of the string s separated by the string sep."/>
    <StaticFunction name="reverse" tooltip="string string.reverse(s)%1Returns a string that is the string s reversed."/>
    <StaticFunction name="sub" tooltip="string string.sub(s, i [, j])%1Returns the substring of s that starts at i and continues until j; i and j can be negative. If j is absent, then it is assumed to be equal to -1 (which is the same as the string length). "/>
    <StaticFunction name="upper" tooltip="string string.upper(s)%1Receives a string and returns a copy of this string with all lowercase letters changed to uppercase. All other characters are left unchanged."/>
  </LuaLibrary>
  <LuaLibrary name="table">
    <StaticFunction name="concat" tooltip="string table.concat(list [, sep [, i [, j]]])%1Given a list where all elements are strings or numbers, returns the string list[i]..sep..list[i+1] ... sep..list[j]. The default value for sep is the empty string, the default for i is 1, and the default for j is #list. If i is greater than j, returns the empty string."/>
    <StaticFunction name="foreach" tooltip="void table.foreach(table, f)%1Apply the function f to the elements of the table passed" />
    <StaticFunction name="foreachi" tooltip="void table.foreachi(table, f)%1This is similar to table.foreach() except that index-value pairs are passed, not key-value pairs." />
    <StaticFunction name="getn" tooltip="number table.getn(table)%1Returns the number of elements in the table passed" />
    <StaticFunction name="insert" tooltip="void table.insert(list, [pos,] value)%1Inserts element value at position pos in list, shifting up the elements list[pos], list[pos+1], ..., list[#list]. The default value for pos is #list+1, so that a call table.insert(t,x) inserts x at the end of list t."/>
    <StaticFunction name="remove" tooltip="value table.remove(list [, pos])%1Removes from list the element at position pos, returning the value of the removed element. When pos is an integer between 1 and #list, it shifts down the elements list[pos+1], list[pos+2], ..., list[#list] and erases element list[#list]; The index pos can also be 0 when #list is 0, or #list + 1; in those cases, the function erases the element list[pos]."/>
    <StaticFunction name="sort" tooltip="bool table.sort(list [, comp])%1Sorts list elements in a given order, in-place, from list[1] to list[#list]. If comp is given, then it must be a function that receives two list elements and returns true when the first element must come before the second in the final order (so that not comp(list[i+1],list[i]) will be true after the sort). If comp is not given, then the standard Lua operator [less than] is used instead."/>
  </LuaLibrary>
  <LuaLibrary name="coroutine">
    <StaticFunction name="create" tooltip="thread coroutine.create(f)%1Creates a new coroutine, with body f. f must be a Lua function."/>
    <StaticFunction name="resume" tooltip="bool coroutine.resume(co [, val1, ...])%1Starts or continues the execution of coroutine co. The first time you resume a coroutine, it starts running its body. The values val1, ... are passed as the arguments to the body function. If the coroutine has yielded, resume restarts it; the values val1, ... are passed as the results from the yield. If the coroutine runs without any errors, resume returns true plus any values passed to yield (if the coroutine yields) or any values returned by the body function (if the coroutine terminates). If there is any error, resume returns false plus the error message."/>
    <StaticFunction name="running" tooltip="thread coroutine.running()%1Returns the running coroutine plus a boolean, true when the running coroutine is the main one."/>
    <StaticFunction name="status" tooltip="string coroutine.status(co)%1Returns the status of coroutine co, as a string: 'running', if the coroutine is running (that is, it called status); 'suspended', if the coroutine is suspended in a call to yield, or if it has not started running yet; 'normal' if the coroutine is active but not running (that is, it has resumed another coroutine); and 'dead' if the coroutine has finished its body function, or if it has stopped with an error."/>
    <StaticFunction name="wrap" tooltip="function coroutine.wrap(f)%1Creates a new coroutine, with body f. f must be a Lua function. Returns a function that resumes the coroutine each time it is called. Any arguments passed to the function behave as the extra arguments to resume. Returns the same values returned by resume, except the first boolean. In case of error, propagates the error."/>
    <StaticFunction name="yield" tooltip="void coroutine.yield(...)%1Suspends the execution of the calling coroutine. Any arguments to yield are passed as extra results to resume."/>
  </LuaLibrary>
  <ItemStruct name="Instance">
    <StaticFunction name="new" tooltip="Instance Instance.new(val [, parent])%1Creates an new object of type val. The parent argument is optional; If it is supplied, the object will be parented to that object"/>
  </ItemStruct>
  <LuaLibrary name="os">
    <StaticFunction name="time" tooltip="number os.time()%1Returns the number of seconds since the epoch (1 January 1970, 00:00:00)" />
    <StaticFunction name="difftime" tooltip="number os.difftime(number t1, number t2)%1Returns the number of seconds from t1 to t2" />
  </LuaLibrary>
  <ItemStruct name="Vector2">
    <StaticFunction name="new" tooltip="Vector2 Vector2.new(x, y)%1Creates a new Vector2 using ordinates x and y"/>
    <Property name="X" tooltip="Number Vector2.X%1The x-coordinate"/>
    <Property name="Y" tooltip="Number Vector2.Y%1The y-coordinate"/>
    <Property name="unit" tooltip="Vector2 Vector2.unit%1A normalized copy of the vector"/>
    <Property name="magnitude" tooltip="Number Vector2.magnitudeThe length of the vector"/>
    <Function name="lerp" tooltip="Vector2 Vector2:lerp(Vector2 v, number alpha)%1Returns a Vector2 linearly interpolated between this Vector2 and v by the fraction alpha"/>
  </ItemStruct>
  <ItemStruct name="Vector2int16">
    <StaticFunction name="new" tooltip="Vector2int16 Vector2int16.new(x, y)%1Creates a new Vector2int16 using ordinates x and y. Similar to Vector2, but uses integral coordinates"/>
    <Property name="X" tooltip="Number Vector2int16.X%1The x-coordinate"/>
    <Property name="Y" tooltip="Number Vector2int16.Y%1The y-coordinate"/>
  </ItemStruct>
  <ItemStruct name="Vector3">
    <StaticFunction name="new" tooltip="Vector3 Vector3.new(x, y, z)%1Constructs a new Vector3 using coordinates x, y, z." />
    <StaticFunction name="FromNormalId" tooltip="Vector3 Vector3.FromNormalID(Enum.NormalId normalId)%1Constructs a new Vector3 in a particular direction." />
    <StaticFunction name="FromAxis" tooltip="Vector3 Vector3.FromAxis(Enum.Axis axis)%1Constructs a new Vector3 for a particular axis." />
    <Property name="X" tooltip="number Vector3.X%1The x-coordinate" />
    <Property name="Y" tooltip="number Vector3.Y%1The y-coordinate" />
    <Property name="Z" tooltip="number Vector3.Z%1The z-coordinate" />
    <Property name="Unit" tooltip="Vector3 Vector3.Unit%1A normalized copy of the vector - one which has the same direction as the original but a magnitude of 1"/>
    <Property name="Magnitude" tooltip="Number Vector3.Magnitude%1The length of the vector"/>
    <Function name="Lerp" tooltip="Vector3 Vector3:Lerp(Vector3 goal, number alpha)%1Returns a Vector3 linearly interpolated between this Vector3 and the goal by the fraction alpha"/>
    <Function name="Dot" tooltip="number Vector3:Dot(Vector3 other)%1Returns a scalar dot product of the two vectors"/>
    <Function name="Cross" tooltip="Vector3 Vector3:Cross(Vector3 other)%1Returns the cross product of the two vectors"/>
    <Function name="isClose" tooltip="bool Vector3:isClose(Vector3 other [, number epsilon]%1"/>
  </ItemStruct>
  <ItemStruct name="Vector3int16">
    <StaticFunction name="new" tooltip="Vector3int16 Vector3int16.new(x, y, z)%1Creates a new Vector3int16 using coordinate x, y, z."/>
    <Property name="X" tooltip="Number Vector3int16.X%1The x-coordinate" />
    <Property name="Y" tooltip="Number Vector3int16.Y%1The y-coordinate" />
    <Property name="Z" tooltip="Number Vector3int16.Z%1The z-coordinate" />
  </ItemStruct>
  <ItemStruct name="CFrame">
    <StaticFunction name="new" tooltip="CFrame CFrame.new(number x, number y, number z)%1Creates a CFrame from position (x, y, z) &lt;a href=&#34;http://wiki.roblox.com/index.php/CFrame&#34;&gt;More Info&lt;/a&gt;"/>
    <StaticFunction name="fromEulerAnglesXYZ" tooltip="CFrame CFrame.fromEulerAnglesXYZ(number rx, number ry, number rz)%1Creates a rotated CFrame using angles (rx, ry, rz) in radians"/>
    <StaticFunction name="Angles" tooltip="CFrame CFrame.Angles(number rx, number ry, number rz)%1Equivalent to fromEulerAnglesXYZ"/>
    <StaticFunction name="fromAxisAngle" tooltip="CFrame CFrame.fromAxisAngle(Vector3 v, number r)%1Creates a rotated CFrame from a Unit Vector3 and a rotation in radians"/>
    <Property name="p" tooltip="Vector3 CFrame.p%1The 3D position of the CFrame"/>
    <Property name="X" tooltip="Number CFrame.X%1The x-coordinate of the position" />
    <Property name="Y" tooltip="Number CFrame.Y%1The y-coordinate of the position" />
    <Property name="Z" tooltip="Number CFrame.Z%1The z-coordinate of the position" />
    <Property name="lookVector" tooltip="Vector3 CFrame.lookVector%1Returns the facing direction"/>
    <Function name="inverse" tooltip="CFrame CFrame:inverse()%1Returns the inverse of this CFrame"/>
    <Function name="lerp" tooltip="CFrame CFrame:lerp(CFrame goal, float alpha)%1Returns a CFrame interpolated between this CFrame and the goal by the fraction alpha"/>
    <Function name="toWorldSpace" tooltip="CFrame CFrame:toWorldSpace(CFrame cf)%1Returns a CFrame transformed from Object to World space. Equivalent to [CFrame * cf]"/>
    <Function name="toObjectSpace" tooltip="CFrame CFrame:toObjectSpace(CFrame cf)%1Returns a CFrame transformed from World to Object space. Equivalent to [CFrame:inverse() * cf]"/>
    <Function name="pointToWorldSpace" tooltip="Vector3 CFrame:pointToWorldSpace(Vector3 v3)%1Returns a Vector3 transformed from Object to World space. Equivalent to [CFrame * v3]"/>
    <Function name="pointToObjectSpace" tooltip="Vector3 CFrame:pointtoObjectSpace(Vector3 v3)%1Returns a Vector3 transformed from World to Object space. Equivalent to [CFrame:inverse() * v3]"/>
    <Function name="vectorToWorldSpace" tooltip="Vector3 CFrame:vectorToWorldSpace(Vector3 v3)%1Returns a Vector3 rotated from Object to World space. Equivalent to [(CFrame - CFrame.p) *v3]"/>
    <Function name="vectorToObjectSpace" tooltip="Vector3 CFrame:vectorToObjectSpace(Vector3 v3)%1Returns a Vector3 rotated from World to Object space.%1Equivalent to [(CFrame:inverse() - CFrame:inverse().p) * v3]"/>
    <Function name="components" tooltip="... CFrame:components()%1Returns the values: x, y, z, R00, R01, R02, R10, R11, R12, R20, R21, R22"/>
    <Function name="toEulerAnglesXYZ" tooltip="... CFrame:toEulerAnglesXYZ()%1Returns approximate angles that could be used to generate CFrame"/>
  </ItemStruct>
  <ItemStruct name="Region3">
    <StaticFunction name="new" tooltip="Region3 Region3.new(Vector3, Vector3)%1Creates a new Region3 out of two Vector3 values."/>
    <Property name="CFrame" tooltip="CFrame%1The center location and rotation of the Region3"/>
    <Property name="Size" tooltip="Vector3%1The 3D size of the Region3"/>
  </ItemStruct>
  <ItemStruct name="Region3int16">
    <StaticFunction name="new" tooltip="Region3int16 Region3int16.new(Vector3int16, Vector3int16)%1Creates a new Region3int16 out of two Vector3int16 structs"/>
  </ItemStruct>
  <ItemStruct name="BrickColor">
    <StaticFunction name="new" tooltip="BrickColor BrickColor.new(r, g, b)%1Returns a BrickColor from the values r, g and b. &lt;a href=&#34;http://wiki.roblox.com/index.php/BrickColor&#34;&gt;More Info&lt;/a&gt;"/>
    <StaticFunction name="palette" tooltip="BrickColor BrickColor.palette(val)%1Returns a BrickColor from val"/>
    <StaticFunction name="Random" tooltip="BrickColor BrickColor.Random()%1Returns a random BrickColor"/>
    <StaticFunction name="White" tooltip="BrickColor BrickColor.White()%1Returns the BrickColor white"/>
    <StaticFunction name="Gray" tooltip="BrickColor BrickColor.Gray()%1Returns the BrickColor Medium stone grey"/>
    <StaticFunction name="DarkGray" tooltip="BrickColor BrickColor.DarkGray()%1Returns the BrickColor Dark stone grey" />
    <StaticFunction name="Black" tooltip="BrickColor BrickColor.Black()%1Returns the BrickColor Black" />
    <StaticFunction name="Red" tooltip="BrickColor BrickColor.Red()%1Returns the BrickColor Bright Red" />
    <StaticFunction name="Yellow" tooltip="BrickColor BrickColor.Yellow()%1Returns the BrickColor Bright Yellow" />
    <StaticFunction name="Green" tooltip="BrickColor BrickColor.Green()%1Returns the BrickColor Dark Green" />
    <StaticFunction name="Blue" tooltip="BrickColor BrickColor.Blue()%1Returns the BrickColor Bright Blue" />
    <Property name="Number" tooltip="Number BrickColor.Number%1The unique number that identifies the BrickColor"/>
    <Property name="Name" tooltip="String BrickColor.Name%1The name associated with the BrickColor"/>
    <Property name="Color" tooltip="Color3 BrickColor.Color%1The Color3 associated with the BrickColor"/>
    <Property name="r" tooltip="Number BrickColor.R%1The red component (between 0 and 1)"/>
    <Property name="g" tooltip="Number BrickColor.G%1The green component (between 0 and 1)"/>
    <Property name="b" tooltip="Number BrickColor.B%1The blue component (between 0 and 1)"/>
  </ItemStruct>
  <ItemStruct name="Ray">
    <StaticFunction name="new" tooltip="Ray Ray.new(Vector3 Origin, Vector3 Direction)%1Creates a new Ray with given Origin and Direction"/>
    <Property name="Origin" tooltip="Vector3 Ray.Origin%1The position of the origin"/>
    <Property name="Direction" tooltip="Vector3 Ray.Direction%1The direction vector of the ray"/>
    <Property name="Unit" tooltip="Ray Ray.Unit%1The Ray with a normalized direction"/>
    <Function name="ClosestPoint" tooltip="Vector3 Ray:ClosestPoint(Vector3 point)%1Returns the closest point on the Ray to point. Note Rays are unidirectional"/>
    <Function name="Distance" tooltip="Number Ray:Distance(Vector3 point)%1 Returns the distance from point to ClosestPoint(point)"/>
  </ItemStruct>
  <ItemStruct name="UDim">
    <StaticFunction name="new" tooltip="UDim UDim.new(number Scale, number Offset)%1Creates a new UDim from components"/>
    <Property name="Scale" tooltip="Number UDim.Scale%1The scale value"/>
    <Property name="Offset" tooltip="Number UDim.Offset%1The offset value"/>
  </ItemStruct>
  <ItemStruct name="UDim2">
    <StaticFunction name="new" tooltip="UDim2 UDim2.new(Number XScale, Number XOffset, Number YScale, Number YOffset)%1"/>
    <Property name="X" tooltip="The x dimension scale and offset" />
    <Property name="Y" tooltip="The y dimension scale and offset" />
  </ItemStruct>
  <ItemStruct name="Color3">
    <StaticFunction name="new" tooltip="Color3 Color3.new(r, g, b)%1Returns a Color3 from the values r, g and b. &lt;a href=&#34;http://wiki.roblox.com/index.php/Color3&#34;&gt;More Info&lt;/a&gt;"/>
    <Property name="r" tooltip="Number Color3.r%1The red value of the color"/>
    <Property name="g" tooltip="Number Color3.g%1The green value of the color"/>
    <Property name="b" tooltip="Number Color3.b%1The blue value of the color"/>
  </ItemStruct>
  <ItemStruct name="ColorSequence">
    <StaticFunction name="new" tooltip="ColorSequence ColorSequence.new(Color3 c)%1Creates a sequence of two keypoints with `c` for each value"/>
	<StaticFunction name="new" tooltip="ColorSequence ColorSequence.new(Color3 c0, Color3 c1)%1Creates a sequence of two keypoints with `c0` and `c1` as the value"/>
    <Property name="Keypoints" tooltip="Array ColorSequence.Keypoints%1An array containing keypoint values for the ColorSequence"/>
  </ItemStruct>
  <ItemStruct name="NumberSequence">
    <StaticFunction name="new" tooltip="NumberSequence NumberSequence.new(float n)%1Creates a sequence of two keypoints with `n` for each value"/>
	<StaticFunction name="new" tooltip="NumberSequence NumberSequence.new(float n0, float n1)%1Creates a sequence of two keypoints with `n0` and `n1` as the value"/>
	<StaticFunction name="new" tooltip="NumberSequence NumberSequence.new(Array keypoints)%1Creates a sequence from a list of keypoints"/>
    <Property name="Keypoints" tooltip="Array NumberSequence.Keypoints%1An array containing keypoint values for the NumberSequence"/>
  </ItemStruct>
  <ItemStruct name="NumberRange">
    <StaticFunction name="new" tooltip="NumberRange NumberRange.new(float value)%1Creates a new NumberRange with the same minimum and maximum"/>
	<StaticFunction name="new" tooltip="NumberRange NumberRange.new(float min, float max)%1Creates a new NumberRange with the provided minimum and maximum."/>
    <Property name="Min" tooltip="float NumberRange.Min%1Minimum value. Will always be less than or equal to the maximum."/>
    <Property name="Max" tooltip="float NumberRange.Max%1Maximum value. Will always be greater than or equal to the minimum."/>
  </ItemStruct>
  <ItemStruct name="Faces">
    <StaticFunction name="new" tooltip="Faces Faces.new(NormalId, NormalId, ...)%1Creates a new Faces using list of faces" />
	<Property name="Top" tooltip="bool Faces.Top%1Whether the top face is included" />
    <Property name="Bottom" tooltip="bool Faces.Bottom%1Whether the bottom face is included" />
	<Property name="Left" tooltip="bool Faces.Left%1Whether the left face is included" />
    <Property name="Right" tooltip="bool Faces.Right%1Whether the right face is included" />
    <Property name="Back" tooltip="bool Faces.Back%1Whether the back face is included" />
    <Property name="Front" tooltip="bool Faces.Front%1Whether the front face is included" />
  </ItemStruct>
  <ItemStruct name="Axes">
    <StaticFunction name="new" tooltip="Axes Axes.new(Axis\NormalId, Axis/NormalId, ...)%1Creates a new Axes using list of axes and/or faces. NormalIds (faces) are converted to the corresponding axes." />
    <Property name="X" tooltip="bool Axes.X%1Whether the X axis is enabled" />
    <Property name="Y" tooltip="bool Axes.Y%1Whether the Y axis is enabled" />
    <Property name="Z" tooltip="bool Axes.Z%1Whether the Z axis is enabled" />
	<Property name="Top" tooltip="bool Axes.Top%1Whether the top face is included" />
    <Property name="Bottom" tooltip="bool Axes.Bottom%1Whether the bottom face is included" />
	<Property name="Left" tooltip="bool Axes.Left%1Whether the left face is included" />
    <Property name="Right" tooltip="bool Axes.Right%1Whether the right face is included" />
    <Property name="Back" tooltip="bool Axes.Back%1Whether the back face is included" />
    <Property name="Front" tooltip="bool Axes.Front%1Whether the front face is included" />
  </ItemStruct>
  <ItemStruct name="ColorSequenceKeypoint">
    <StaticFunction name="new" tooltip="ColorSequenceKeypoint ColorSequenceKeypoint.new(float time, Color3 color)%1Creates a keypoint with a specified time and color." />
    <Property name="Time" tooltip="float ColorSequenceKeypoint.Time%1The relative time at which the keypoint is located." />
    <Property name="Value" tooltip="Color3 ColorSequenceKeypoint.Value%1The Color3 value at the keypoint." />
  </ItemStruct>
  <ItemStruct name="NumberSequenceKeypoint">
    <StaticFunction name="new" tooltip="NumberSequenceKeypoint NumberSequenceKeypoint.new(float time, float value)%1Creates a keypoint with a specified time and value." />
    <StaticFunction name="new" tooltip="NumberSequenceKeypoint NumberSequenceKeypoint.new(float time, float value, float envelop)%1Creates a keypoint with a specified time, value, and envelope." />
    <Property name="Envelope" tooltip="float NumberSequenceKeypoint.Envelope%1Indicates the amount of variance allowed from the Value. A computed value." />
    <Property name="Time" tooltip="float NumberSequenceKeypoint.Time%1The relative time at which the keypoint is positioned." />
  </ItemStruct>
  <ItemStruct name="Rect">
    <StaticFunction name="new" tooltip="Rect Rect.new(min, max)%1Constructs a new Rect with Vector2 min as top left corner and Vector2 max as bottom right corner." />
    <Property name="Min" tooltip="Number Rect.Min%1The top-left corner" />
    <Property name="Max" tooltip="Number Rect.Max%1The bottom-right corner" />
    <Property name="Width" tooltip="Number Rect.Width%1The x width of rect" />
    <Property name="Height" tooltip="Number Rect.Height%1The y height of rect"/>
  </ItemStruct>
  <ItemStruct name="PhysicalProperties">
	<StaticFunction name="new" tooltip="PhysicalProperties PhysicalProperties.new(material)%1 Constructs a new PhysicalProperties container from the Enum.Material object." />
	<StaticFunction name="new" tooltip="PhysicalProperties PhysicalProperties.new(density, friction, elasticity, frictionWeight, elasticityWeight)%1Constructs a new PhysicalProperties container." />
	<Property name="Density" tooltip="Number PhysicalProperties.Density%1Density" />
	<Property name="Friction" tooltip="Number PhysicalProperties.Friction%1Friction" />
	<Property name="Elasticity" tooltip="Number PhysicalProperties.Elasticity%1Elasticity" />
	<Property name="FrictionWeight" tooltip="Number PhysicalProperties.FrictionWeight%1FrictionWeight determines how dominant the object's friction is when interacting with other objects. Default = 1"/>
	<Property name="ElasticityWeight" tooltip="Number PhysicalProperties.ElasticityWeight%1ElasticityWeight determins how dominant the object's elasticity is when interacting with other objects. Default = 1"/>
  </ItemStruct>
</StudioAutocomplete>
