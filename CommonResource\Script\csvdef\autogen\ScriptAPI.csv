接口ID,类型ID,类型名,函数名-不用翻译,函数标题-不用翻译,描述1参数及类型,描述2返回值及类型-不用翻译,描述3该方法的主要作用,描述4具体使用案例如下-不用翻译,繁体,英文,泰语,西班牙语,巴西葡萄牙语,法语,阿拉伯语,日语,韩语,越南语,俄语,土耳其语,意大利语,德语,印尼语,英文,中文繁體,泰语,西班牙语,巴西葡萄牙语,法语,阿拉伯语,日语,韩语,越南语,俄语,土耳其语,意大利语,德语,印尼语,英文,中文繁體,泰语,西班牙语,巴西葡萄牙语,法语,阿拉伯语,日语,韩语,越南语,俄语,土耳其语,意大利语,德语,印尼语,印尼语
ID,Type,TypeName,FunctionName,FunctionDesc,Desc1,Desc2,Desc3,Desc4,TWTypeName,ENTypeName,THATypeName,ESNTypeName,PTBTypeName,FRATypeName,ARATypeName,JPNTypeName,KORTypeName,VIETypeName,RUSTypeName,TURTypeName,ITATypeName,GERTypeName,INDTypeName,ENDesc1,TWDesc1,THADesc1,ESNDesc1,PTBDesc1,FRADesc1,ARADesc1,JPNDesc1,KORDesc1,VIEDesc1,RUSDesc1,TURDesc1,ITADesc1,GERDesc1,INDDesc1,ENDesc3,TWDesc3,THADesc3,ESNDesc3,PTBDesc3,FRADesc3,ARADesc3,JPNDesc3,KORDesc3,VIEDesc3,RUSDesc3,TURDesc3,ITADesc3,GERDesc3,INDDesc3,INDDesc4
10001,1,游戏管理,doGameEnd,Game:doGameEnd(),nil,ErrorCode.OK,结束比赛,local result = Game:doGameEnd(),遊戲管理,Game ,การจัดการเกม,Juego,Jogo,Jeu,新增待译0717,ゲーム管理,경기,Game,Игра,Oyun,Gioco,Spiel,Game ,nil,nil,nil,nil,nil,nil,nil,nil,nil,nil,nil,nil,nil,nil,nil,End the game,結束比賽,จบเกม,Terminar juego,Terminar o jogo,La fin du jeu,新增待译0717,ゲームを終了,게임을 종료,Kết Thúc game,Конец игры,Oyunu bitir,Termina il gioco,Beenden Sie das Spiel,Akhiri Permainan,local result = Game:doGameEnd()
10002,1,游戏管理,setScriptVar,"Game:setScriptVar(index, val)","index:number从1开始索引, val:number自定义的值",ErrorCode.OK,设置脚本参数，供自定义使用,"local result = Game:setScriptVar(index, val)",遊戲管理,Game ,การจัดการเกม,Juego,Jogo,Jeu,新增待译0717,ゲーム管理,경기,Game,Игра,Oyun,Gioco,Spiel,Game ,"index:number index from 1 , val:number customized value","index:number index from 1 , val:number customized value","index:number index from 1 , val:number customized value","index:number index from 1 , val:number customized value","index:number index from 1 , val:number customized value","index:number index from 1 , val:number customized value","index:number index from 1 , val:number customized value","index:number index from 1 , val:number customized value","index:number index from 1 , val:number customized value","index:number index from 1 , val:number customized value","index:number index from 1 , val:number customized value","index:number index from 1 , val:number customized value","index:number index from 1 , val:number customized value","index:number index from 1 , val:number customized value","index:number index from 1 , val:number customized value",Set the script's attribute for custom ,設置腳本參數，供自定義使用,ตั้งค่าพารามิเตอร์สคริปต์สำหรับการใช้งานที่กำหนดเอง,Definir parámetros de script para personalizar,Definir atributo do script para personalizado,Définir l'attribut pour la coutume du script,新增待译0717,スクリプトの属性をカスタムに設定します,사용자 정의를위한 스크립트의 속성을 설정합니다,Cài đặt đặc tính của script để tùy chỉnh,Установить атрибут скрипта для пользовательских,Özel betiğin özniteliğini ayarla,Impostare l'attributo dello script per la personalizzazione,Setzen Sie das Attribut Skript für benutzerdefinierte,Atur skrip properti utk penggunaan khusus,"local result = Game:setScriptVar(index, val)"
10003,1,游戏管理,getScriptVar,Game:getScriptVar(index),index:number从1开始索引,"ErrorCode.OK, val:number自定义的值",获取脚本参数，自定义使用,local result = Game:getScriptVar(index),遊戲管理,Game ,การจัดการเกม,Juego,Jogo,Jeu,新增待译0717,ゲーム管理,경기,Game,Игра,Oyun,Gioco,Spiel,Game ,index:number index from 1 ,index:number index from 1 ,index:number index from 1 ,index:number index from 1 ,index:number index from 1 ,index:number index from 1 ,index:number index from 1 ,index:number index from 1 ,index:number index from 1 ,index:number index from 1 ,index:number index from 1 ,index:number index from 1 ,index:number index from 1 ,index:number index from 1 ,index:number index from 1 ,Receive script attribute for custom,獲取腳本參數，自定義使用,รับพารามิเตอร์สคริปต์ การใช้งานที่กำหนดเอง,Recibir parámetros de script para personalizar,Receber atributo de script para personalizado,Recevoir attribut de script pour personnalisés,新增待译0717,スクリプトの属性をカスタムに受け取ります。,사용자 정의를위한 스크립트 속성을 수신,Nhận đặc tính của script để tùy chỉnh,Получить атрибут сценария для пользовательских,Görenek için senaryo özniteliği al,Ricevi attributo di script per la personalizzazione,Erhalten Sie Skript-Attribut für benutzerdefinierte,Menerima skrip properti utk penggunaan khusus,local result = Game:getScriptVar(index)
10004,1,游戏管理,sendScriptVars2Client,Game:sendScriptVars2Client(),nil,ErrorCode.OK,上传设置好的脚本参数,local result = Game:sendScriptVars2Client(),遊戲管理,Game ,การจัดการเกม,Juego,Jogo,Jeu,新增待译0717,ゲーム管理,경기,Game,Игра,Oyun,Gioco,Spiel,Game ,nil,nil,nil,nil,nil,nil,nil,nil,nil,nil,nil,nil,nil,nil,nil,Upload the script's attribute,上傳設置好的腳本參數,อัปโหลดพารามิเตอร์สคริปต์ที่ตั้งค่าไว้,Subir parámetros de script,Carregar atributo do script,Téléchargez l'attribut du script,新增待译0717,スクリプトの属性をアップロードします,스크립트의 속성을 업로드,Tải lên đặc tính của script,Загрузить атрибут скрипта,senaryonun özniteliğini yükleyin,Caricate l'attributo dello script,Laden Sie das Attribut des Skripts,Upload properti skrip,local result = Game:sendScriptVars2Client()
10005,1,游戏管理,addRenderGlobalEffect,Game:addRenderGlobalEffect(path),path:string,"ErrorCode.OK, effectid:number",新增全局效果,local result = Game:addRenderGlobalEffect(path),遊戲管理,Game ,การจัดการเกม,Juego,Jogo,Jeu,新增待译0717,ゲーム管理,경기,Game,Игра,Oyun,Gioco,Spiel,Game ,path:string,path:string,path:string,path:string,path:string,path:string,path:string,path:string,path:string,path:string,path:string,path:string,path:string,path:string,path:string,New global effect,新增全局效果,เพิ่มเอฟเฟ็กต์ทั่วโลก,Nuevo Efecto Global,Novo efeito global,Nouvel effet global,新增待译0717,新グローバルな効果,새로운 글로벌 효과,Hiệu ứng toàn map mới,Новый глобальный эффект,Yeni global etki,Nuovo effetto globale,Neue globale Wirkung,Efek global,local result = Game:addRenderGlobalEffect(path)
10006,1,游戏管理,removeRenderGlobalEffect,Game:removeRenderGlobalEffect(effectid),effectid:number,ErrorCode.OK,移除全局效果,local result = Game:removeRenderGlobalEffect(effectid),遊戲管理,Game ,การจัดการเกม,Juego,Jogo,Jeu,新增待译0717,ゲーム管理,경기,Game,Игра,Oyun,Gioco,Spiel,Game ,effectid:number,effectid:number,effectid:number,effectid:number,effectid:number,effectid:number,effectid:number,effectid:number,effectid:number,effectid:number,effectid:number,effectid:number,effectid:number,effectid:number,effectid:number,Clear global effect,移除全局效果,เอาเอฟเฟ็กต์ทั่วโลกออก,Remover Efecto Global,Limpar efeito global,effet global Effacer,新增待译0717,グローバルな効果をクリア,클리어 글로벌 효과,Xóa hiệu ứng toàn map,Ясно глобальный эффект,Global etkiyi temizle,effetto globale chiaro,Klare globale Wirkung,Hapus efek global,local result = Game:removeRenderGlobalEffect(effectid)
10007,1,游戏管理,setRenderGlobalEffectPos,"Game:setRenderGlobalEffectPos(effectid, x, y, z)","effectid:number, x|y|z:number",ErrorCode.OK,设置全局效果位置,"local result = Game:setRenderGlobalEffectPos(effectid, x, y, z)",遊戲管理,Game ,การจัดการเกม,Juego,Jogo,Jeu,新增待译0717,ゲーム管理,경기,Game,Игра,Oyun,Gioco,Spiel,Game ,"effectid:number, x|y|z:number","effectid:number, x|y|z:number","effectid:number, x|y|z:number","effectid:number, x|y|z:number","effectid:number, x|y|z:number","effectid:number, x|y|z:number","effectid:number, x|y|z:number","effectid:number, x|y|z:number","effectid:number, x|y|z:number","effectid:number, x|y|z:number","effectid:number, x|y|z:number","effectid:number, x|y|z:number","effectid:number, x|y|z:number","effectid:number, x|y|z:number","effectid:number, x|y|z:number",Set the position of global effect,設置全局效果位置,ตั้งค่าตำแหน่งเอฟเฟ็กต์ทั่วโลก,Definir la posición del efecto global,Definir a posição do efeito global,Régler la position de l'effet global,新增待译0717,グローバル効果の位置を設定,글로벌 효과의 위치를 ​​설정합니다,Cài đặt vị trí của hiệu ứng toàn map,Установите положение глобального эффекта,Global etkinin konumunu belirle,Impostare la posizione dell'effetto globale,Stellen Sie die Position der globalen Wirkung,Atur posisi efek global,"local result = Game:setRenderGlobalEffectPos(effectid, x, y, z)"
10008,1,游戏管理,setRenderGlobalEffectScale,"Game:setRenderGlobalEffectScale(effectid, scalex, scaley, scalez)","effectid:number, scalex|scaley|scalez:number",ErrorCode.OK,设置全局效果缩放,"local result = Game:setRenderGlobalEffectScale(effectid, scalex, scaley, scalez)",遊戲管理,Game ,การจัดการเกม,Juego,Jogo,Jeu,新增待译0717,ゲーム管理,경기,Game ,Игра,Oyun,Gioco,Spiel,Game ,"effectid:number, scalex|scaley|scalez:number","effectid:number, scalex|scaley|scalez:number","effectid:number, scalex|scaley|scalez:number","effectid:number, scalex|scaley|scalez:number","effectid:number, scalex|scaley|scalez:number","effectid:number, scalex|scaley|scalez:number","effectid:number, scalex|scaley|scalez:number","effectid:number, scalex|scaley|scalez:number","effectid:number, scalex|scaley|scalez:number","effectid:number, scalex|scaley|scalez:number","effectid:number, scalex|scaley|scalez:number","effectid:number, scalex|scaley|scalez:number","effectid:number, scalex|scaley|scalez:number","effectid:number, scalex|scaley|scalez:number","effectid:number, scalex|scaley|scalez:number",Set global zoom effect,設置全局效果縮放,ตั้งค่าการปรับสเกลเอฟเฟกต์ทั่วโลก,Definir efecto global de Zoom,Definir efeito de zoom global,Définition de l'effet de zoom global,新增待译0717,グローバルな効果を縮んで設定する,설정 글로벌 줌 효과,Cài đặt hiệu ứng phóng to toàn map,Установить эффект глобального масштаба,Global yakınlaştırma efektini ayarla,Impostare effetto zoom globale,Set globaler Zoom-Effekt,Atur ukuran efek global,"local result = Game:setRenderGlobalEffectScale(effectid, scalex, scaley, scalez)"
10009,1,游戏管理,msgBox,Game:msgBox(msg),,,messagebox,local result = Game:msgBox(msg),遊戲管理,Game ,การจัดการเกม,Juego,Jogo,Jeu,新增待译0717,ゲーム管理,경기,Game ,Игра,Oyun,Gioco,Spiel,Game ,,,,,,,,,,,,,,,,messagebox,messagebox,กล่องข้อความ,Caja Mensaje,Caixa de mensagem,messagerie,新增待译0717,メッセージボックス,메세지 박스,Hộp thư,окно сообщения,Mesaj Kutusu,casella dei messaggi,Nachrichtenbox,Inbox,local result = Game:msgBox(msg)
20001,2,世界管理,isDaytime,World:isDaytime(),none,"ErrorCode.OK, result:boolean",是否是白天,"local isDayTx = World:isDaytime()
if isDayTx~=ErrorCode.OK then --如果是夜晚则给玩家发火把
	local itemId, itemCnt = 817, 2 --火把Id和数量
	Player:gainItems(playerId, itemId, itemCnt, 1)
end",世界管理,World ,การจัดการโลก,Mundo,Mundo,Monde,新增待译0717,ワールド管理,세계,Thế giới,Мир,Dünya,Mondo,Welt,Peta,none,none,none,none,none,none,none,none,none,none,none,none,none,none,none,Whether it is daytime,是否是白天,ใช่กลางวันหรือไม่,Si es diurno o no,Clima diurno,Que ce soit le jour,新增待译0717,昼間かどうか,이 낮 여부,Thời tiết vào ban ngày,Является ли это в дневное время,Gündüz olup olmadığı,Sia che si tratti di giorno,Ob es Tag,Apakah disiang hari?,"local isDayTx = World:isDaytime()
if isDayTx~=ErrorCode.OK then --If it is night, then give the torch to players
        local itemId, itemCnt = 817, 2 --Torch id and numbers
        Player:gainItems(playerId, itemId, itemCnt, 1)
end"
20002,2,世界管理,isCustomGame,World:isCustomGame(),none,"ErrorCode.OK, result:boolean",是否是自定义游戏,"local result = World:isCustomGame()
if result==ErrorCode.OK then print('当前游戏是自定义游戏') end",世界管理,World ,การจัดการโลก,Mundo,Mundo,Monde,新增待译0717,ワールド管理,세계,Thế giới,Мир,Dünya,Mondo,Welt,Peta,none,none,none,none,none,none,none,none,none,none,none,none,none,none,none,Whether it is customize game,是否是自定義遊戲,ใช่เกมที่กำหนดเองหรือไม่,Si es juego personalizado o no,Se for um jogo personalizado,Que ce soit personnaliser jeu,新增待译0717,カスタマイズゲームかどうか,이 게임을 사용자 정의 여부,Nếu là game tùy chỉnh,Является ли это настроить игру,Özelleştirmek oyunu olup olmadığını,Che si tratti di personalizzare gioco,Ob es sich um Spiel anpassen,Apakah termasuk customize game?,"local result = World:isCustomGame()
if result==ErrorCode.OK then print('Customize mode') end"
20003,2,世界管理,isCreativeMode,World:isCreativeMode(),none,"ErrorCode.OK, result:boolean",是否是创造模式,"local result = World:isCreativeMode()
if result==ErrorCode.OK then print('当前模式：创造模式') end",世界管理,World ,การจัดการโลก,Mundo,Mundo,Monde,新增待译0717,ワールド管理,세계,Thế giới,Мир,Dünya,Mondo,Welt,Peta,none,none,none,none,none,none,none,none,none,none,none,none,none,none,none,Whether it is creation mode,是否是創造模式,ใช่โหมดสร้างสรรค์หรือไม่,Si es modo de creación o no,Se for um jogo de modo de criação,Que ce soit le mode de création,新增待译0717,創造モードかどうか,여부는 생성 모드,Đây có phải là một game sáng tạo,Является ли это режим создания,Yaratma modu olup olmadığı,Che si tratti di modalità di creazione,Ob es sich um Erstellungsmodus,Apakah dimode kreasi?,"local result = World:isCreativeMode()
if result==ErrorCode.OK then print('Creation mode') end"
20004,2,世界管理,isGodMode,World:isGodMode(),none,"ErrorCode.OK, result:boolean",多人创造模式 或者 自制玩法的编辑模式,"local result = World:isGodMode()
if result==ErrorCode.OK then print('当前模式：多人创造模式') end",世界管理,World ,การจัดการโลก,Mundo,Mundo,Monde,新增待译0717,ワールド管理,세계,Thế giới,Мир,Dünya,Mondo,Welt,Peta,none,none,none,none,none,none,none,none,none,none,none,none,none,none,none,Multiplayer creation mode or customized edit mode,多人創造模式 或者 自製玩法的編輯模式,โหมดสร้างสรรค์หลายคนหรือโหมดแก้ไขที่กำหนดวิธีการเล่น,Modo de Creación Multijugador o Modo de Editar Personalizado,Modo de criação multijogador ou modo de edição personalizado,mode de création multi-joueurs ou en mode d'édition personnalisé,新增待译0717,マルチプレイヤー創造モード　または　DIY編集モード,멀티 플레이어 생성 모드 또는 사용자 정의 편집 모드,Chế độ nhiều người chơi hoặc chế độ tùy chỉnh,Режим создания Многопользовательский режим или настроить редактировать,Çoklu oyuncu oluşturma modu veya özelleştirilmiş düzenleme modu,la modalità di creazione multiplayer o modalità di modifica su misura,Multiplayer-Erstellungsmodus oder kundenspezifische Bearbeitungsmodus,Mode kreasi multiplayer atau mode customized edit?,"local result = World:isGodMode()
if result==ErrorCode.OK then print('Multiplayer mode') end"
20005,2,世界管理,isExtremityMode,World:isExtremityMode(),none,"ErrorCode.OK, result:boolean",极限模式,"local result = World:isExtremityMode()
if result==ErrorCode.OK then print('当前模式：极限模式') end",世界管理,World ,การจัดการโลก,Mundo,Mundo,Monde,新增待译0717,ワールド管理,세계,Thế giới,Мир,Dünya,Mondo,Welt,Peta,none,none,none,none,none,none,none,none,none,none,none,none,none,none,none,Hardcore,極限模式,โหมดจำกัด,Extremo,Hardcore,Hardcore,新增待译0717,ハードモード,하드 코어,Cực hạn,Hardcore,Limit modu,Hardcore,Hardcore,Hardcore,"local result = World:isExtremityMode()
if result==ErrorCode.OK then print('Hardcore mode') end"
20006,2,世界管理,isFreeMode,World:isFreeMode(),none,"ErrorCode.OK, result:boolean",冒险模式之自由模式,"local result = World:isFreeMode()
if result==ErrorCode.OK then print('当前模式：冒险模式之自由模式') end",世界管理,World ,การจัดการโลก,Mundo,Mundo,Monde,新增待译0717,ワールド管理,세계,Thế giới,Мир,Dünya,Mondo,Welt,Peta,none,none,none,none,none,none,none,none,none,none,none,none,none,none,none,Survival mode - Free mode,冒險模式之自由模式,โหมดอิสระของโหมดผจญภัย,Modo Supervivencia-Modo Libre,Modo Sobrevivencia - Modo Livre,Survival Mode - Mode gratuit,新增待译0717,サバイバルモード - フリーモード,서바이벌 모드 - 무료 모드,Chế độ mạo hiểm - Chế độ tự do,Режим выживания - Свободный режим,Hayatta kalma modu - Serbest mod,Modalità Survival - Modalità Libera,Survival-Modus - Free-Modus,Mode petualang-Free mode,"local result = World:isFreeMode()
if result==ErrorCode.OK then print('Survival mode-Free mode') end"
20007,2,世界管理,isSurviveMode,World:isSurviveMode(),none,"ErrorCode.OK, result:boolean",单人模式 或者 冒险模式之自由模式,"local result = World:isSurviveMode()
if result==ErrorCode.OK then print('当前模式：单人生存模式') end",世界管理,World ,การจัดการโลก,Mundo,Mundo,Monde,新增待译0717,ワールド管理,세계,Thế giới,Мир,Dünya,Mondo,Welt,Peta,none,none,none,none,none,none,none,none,none,none,none,none,none,none,none,Single mode or Survival mode - Free mode,單人模式 或者 冒險模式之自由模式,โหมดเล่นเดี่ยวหรือโหมดอิสระของโหมดผจญภัย,Modo Solo o Modo Supervivencia-Modo Libre,Modo Solo ou Sobrevivencia - Modo Livre,mode simple ou en mode Survie - Mode gratuit,新增待译0717,シングルモードまたはサバイバルモード - フリーモード,싱글 모드 나 서바이벌 모드 - 무료 모드,Chế độ chơi đơn - Chế độ mạo hiểm - Chế độ tự do,Одиночный режим или режим Survival - Свободный режим,Tek mod veya Hayatta kalma modu - Serbest mod,modalità singola o la modalità Survival - Modalità Libera,Single-Modus oder Survival-Modus - Free-Modus,Mode offline atau mode petualang-Free mode,"local result = World:isSurviveMode()
if result==ErrorCode.OK then print('Single Survival mode') end"
20008,2,世界管理,isCreateRunMode,World:isCreateRunMode(),none,"ErrorCode.OK, result:boolean",由创造模式转的生存,"local result = World:isCreateRunMode()
if result==ErrorCode.OK then print('当前模式：由创造模式转的生存') end",世界管理,World ,การจัดการโลก,Mundo,Mundo,Monde,新增待译0717,ワールド管理,세계,Thế giới,Мир,Dünya,Mondo,Welt,Peta,none,none,none,none,none,none,none,none,none,none,none,none,none,none,none,Survival mode turned from creation mode,由創造模式轉的生存,โหมดผจญภัยที่สลับจากโหมดสร้างสรรค์,Modo Supervivencia convertido desde Creación,Modo de Sobrevivência mudou do modo de Criação,Mode Survie tourné du mode de création,新增待译0717,創造モードから生存モードに変わりました,서바이벌 모드는 생성 모드에서 설정,Chuyển từ chế độ sáng tạo sang sinh tồn,Режим выживания превратился из режима создания,Hayatta kalma modu oluşturma modundan döndü,modalità di sopravvivenza trasformato dalla modalità di creazione,Survival-Modus eingeschaltet aus Erstellungsmodus,Mode petualangan berubah menjadi mode kreasi,"local result = World:isCreateRunMode()
if result==ErrorCode.OK then print('Survival mode, turned from creation mode') end"
20009,2,世界管理,isGameMakerMode,World:isGameMakerMode(),none,"ErrorCode.OK, result:boolean",自制玩法的编辑模式,"local result = World:isGameMakerMode()
if result==ErrorCode.OK then print('当前模式：自制玩法的编辑模式') end",世界管理,World ,การจัดการโลก,Mundo,Mundo,Monde,新增待译0717,ワールド管理,세계,Thế giới,Мир,Dünya,Mondo,Welt,Peta,none,none,none,none,none,none,none,none,none,none,none,none,none,none,none,Customized edit mode,自製玩法的編輯模式,โหมดแก้ไขที่กำหนดวิธีการเล่น,Modo Editar Personalizado,Modo de edição personalizado,mode d'édition sur mesure,新增待译0717,DIY編集モード,사용자 정의 편집 모드,Chế độ tự chỉnh sửa,Режим редактирования Индивидуального,Düzenleme modunu özelleştirin,modalità di modifica personalizzata,Maßgeschneiderte Bearbeitungsmodus,Customized edit mode,"local result = World:isGameMakerMode()
if result==ErrorCode.OK then print('Customized edit mode') end"
20010,2,世界管理,isGameMakerRunMode,World:isGameMakerRunMode(),none,"ErrorCode.OK, result:boolean",自制玩法的运行模式,"local result = World:isGameMakerRunMode()
if result==ErrorCode.OK then print('当前模式：自制玩法的运行模式') end",世界管理,World ,การจัดการโลก,Mundo,Mundo,Monde,新增待译0717,ワールド管理,세계,Thế giới,Мир,Dünya,Mondo,Welt,Peta,none,none,none,none,none,none,none,none,none,none,none,none,none,none,none,Customized operate mode,自製玩法的運行模式,โโหมดการใช้งานที่กำหนดวิธีการเล่น,Modo Operación Personalizada,Modo de operação personalizado,Mode personnalisé fonctionnent,新增待译0717,DIY操作モード,사용자 지정 모드를 작동,Chế độ vận hành tùy chỉnh,Индивидуальный режим эксплуатации,Özelleştirilmiş çalışma modu,modalità personalizzata operare,Customized Betriebsmodus,Customized operate mode,"local result = World:isGameMakerRunMode()
if result==ErrorCode.OK then print('Customized operation mode') end"
20011,2,世界管理,getHours,World:getHours(),none,"ErrorCode.OK, result:number",获取游戏当前时间(h),"local result, dayTime = World:getHours()
if result==ErrorCode.OK then print('当前时间：', dayTime) end",世界管理,World ,การจัดการโลก,Mundo,Mundo,Monde,新增待译0717,ワールド管理,세계,Thế giới,Мир,Dünya,Mondo,Welt,Peta,none,none,none,none,none,none,none,none,none,none,none,none,none,none,none,Current time(h),獲取遊戲當前時間(h),รับเวลาปัจจุบันของเกม(h),Tiempo Actual(h),Tempo Atual(h),Heure actuelle (h),新增待译0717,現在時刻(h),현재 시간 (h),Thời gian hiện tại(h),Текущее время (ч),Geçerli saat (s),Ora corrente (h),Aktuelle Zeit (h),Waktu sekarang(h),"local result, dayTime = World:getHours()
if result==ErrorCode.OK then print('Current time:', dayTime) end"
20012,2,世界管理,getCameraEditState,World:getCameraEditState(),none,"ErrorCode.OK, state:number0默认，1编辑状态，2测试状态",获取视角编辑状态,"local result, state = World:getCameraEditState()
if result==ErrorCode.OK then print('当前视角编辑状态：', state) end",世界管理,World ,การจัดการโลก,Mundo,Mundo,Monde,新增待译0717,ワールド管理,세계,Thế giới,Мир,Dünya,Mondo,Welt,Peta,none,none,none,none,none,none,none,none,none,none,none,none,none,none,none,Obtain view editing state ,獲取視角編輯狀態,รับสถานะการแก้ไขมุมอง,Obtenere el Estado de Edición Visión,Obter estado de edição de visualização,Obtenir afficher l'état d'édition,新增待译0717,視角編集状態を取得,보기 편집 상태를 구합니다,Chỉnh sửa góc nhìn,Получить вид редактирования состояния,Görünüm düzenleme durumunu edinin,Ottenere lo stato di visualizzazione di modifica,Erhalten Ansicht Bearbeitungszustand,Dapat status view editing,"local result, state = World:getCameraEditState()
if result==ErrorCode.OK then print('Current view editing state:', state) end"
20013,2,世界管理,setCameraEditState,World:setCameraEditState(state),state:CameraEditState,ErrorCode.OK,设置视角编辑状态,"--获取到的是Camera对象(userdata)，此处返回的是nil
local result, config = World:getCustomCameraConfig()
if result==ErrorCode.OK and config~=nil then --自定义视角
	config:setOption(CAMERA_OPTION_INDEX_CONFIG_SET, CCG_FLATVIEW)
end",世界管理,World ,การจัดการโลก,Mundo,Mundo,Monde,新增待译0717,ワールド管理,세계,Thế giới,Мир,Dünya,Mondo,Welt,Peta,state:CameraEditState,state:CameraEditState,state:CameraEditState,state:CameraEditState,state:CameraEditState,state:CameraEditState,state:CameraEditState,state:CameraEditState,state:CameraEditState,state:CameraEditState,state:CameraEditState,state:CameraEditState,state:CameraEditState,state:CameraEditState,state:CameraEditState,Set view editing state ,設置視角編輯狀態,ตั้งค่าสถานะการแก้ไขมุมอง,Establecer Estado de Edición Visión,Definir o estado de edição da vista,Set afficher l'état d'édition,新增待译0717,視角編集状態を設定する,설정보기 편집 상태,Cài đặt chỉnh sửa góc nhìn,Установить вид состояния редактирования,Görünüm düzenleme durumunu ayarla,Impostare lo stato di visualizzazione di modifica,Set Ansicht Bearbeitungszustand,Atur status view editing,"--Get Camera(userdata)，Back to nil
local result, config = World:getCustomCameraConfig()
if result==ErrorCode.OK and config~=nil then --Customized view
        config:setOption(CAMERA_OPTION_INDEX_CONFIG_SET, CCG_FLATVIEW)
end"
20014,2,世界管理,getCustomCameraConfig,World:getCustomCameraConfig(),none,"ErrorCode.OK, config:CameraEditState",获取自定义相机配置,local result = World:getCustomCameraConfig(),世界管理,World ,การจัดการโลก,Mundo,Mundo,Monde,新增待译0717,ワールド管理,세계,Thế giới,Мир,Dünya,Mondo,Welt,Peta,none,none,none,none,none,none,none,none,none,none,none,none,none,none,none,Obtain customized camera configuration,獲取自定義相機配置,รับการกำหนดค่ากล้องที่กำหนดเอง,Obtener Config. de Cámara Personalizada,Obter configuração de câmera personalizada,Obtenir la configuration de la caméra sur mesure,新增待译0717,DIYカメラ配置を受け取る,맞춤형 카메라 구성을 얻습니다,Nhận cấu hình máy ảnh tùy chỉnh,Получить настроенную конфигурацию камеры,Özel kamera yapılandırması edinin,Ottenere configurazione della telecamera personalizzata,Erhalten Sie kundenspezifische Konfiguration der Kamera,Dapat mengatur pengaturan kamera,local result = World:getCustomCameraConfig()
20015,2,世界管理,getRangeXZ,World:getRangeXZ(),none,"ErrorCode.OK, startX|startZ|endX|endZ:number",获取区块(chunk)范围,"local result, startX,startZ,endX,endZ = World:getRangeXZ()
if result==ErrorCode.OK then print('StartXZ=(',startX,', ',startZ,'), EndXZ=(',endX,', ',endZ,')')  end",世界管理,World ,การจัดการโลก,Mundo,Mundo,Monde,新增待译0717,ワールド管理,세계,Thế giới,Мир,Dünya,Mondo,Welt,Peta,none,none,none,none,none,none,none,none,none,none,none,none,none,none,none,Obtain the range of block(chunk) ,獲取區塊(chunk)範圍,รับขอบเขตของบล็อก(chunk),Obtener Rango de Bloque (chunk),Obter o intervalo de bloco (pedaço),Obtenir la plage de bloc (bloc),新增待译0717,ブロック(chunk)の範囲を取得する,블록의 범위를 구하는 (청크),Nhận được phạm vi của khối (chunk),Получить диапазон блока (фрагмент),Blok aralığını elde etme (chunk) ,Ottenere la gamma di blocco (blocco),"Erhalten, um den Bereich des Blocks (chunk)",Dapat block (chunk),"local result, startX,startZ,endX,endZ = World:getRangeXZ()
if result==ErrorCode.OK then print('StartXZ=(',startX,', ',startZ,'), EndXZ=(',endX,', ',endZ,')')  end"
20016,2,世界管理,getActorsByBox,"World:getActorsByBox(objtype, x1, y1, z1, x2, y2, z2)","type:number指定类型, x1|y1|z1:number起始位置, x2|y2|z2:number最终位置","ErrorCode.OK, num:number, objids:tableobjid数组",获取指定范围内actor,"local actorType = 0 --OBJ_TYPE_MONSTER
local x1, y1, z1 = -5, 1, -5
local x2, y2, z2 = 15, 9, 15
local ret, num, array = World:getActorsByBox(actorType, x1,y1,z1, x2,y2,z2)
if ret == ErrorCode.OK then print('Actors ===>> ', num, array) end",世界管理,World ,การจัดการโลก,Mundo,Mundo,Monde,新增待译0717,ワールド管理,세계,Thế giới,Мир,Dünya,Mondo,Welt,Peta,"type:number designate type, x1|y1|z1:number starting point, x2|y2|z2:number ending point","type:number designate type, x1|y1|z1:number starting point, x2|y2|z2:number ending point","type:number designate type, x1|y1|z1:number starting point, x2|y2|z2:number ending point","type:number designate type, x1|y1|z1:number starting point, x2|y2|z2:number ending point","type:number designate type, x1|y1|z1:number starting point, x2|y2|z2:number ending point","type:number designate type, x1|y1|z1:number starting point, x2|y2|z2:number ending point","type:number designate type, x1|y1|z1:number starting point, x2|y2|z2:number ending point","type:number designate type, x1|y1|z1:number starting point, x2|y2|z2:number ending point","type:number designate type, x1|y1|z1:number starting point, x2|y2|z2:number ending point","type:number designate type, x1|y1|z1:number starting point, x2|y2|z2:number ending point","type:number designate type, x1|y1|z1:number starting point, x2|y2|z2:number ending point","type:number designate type, x1|y1|z1:number starting point, x2|y2|z2:number ending point","type:number designate type, x1|y1|z1:number starting point, x2|y2|z2:number ending point","type:number designate type, x1|y1|z1:number starting point, x2|y2|z2:number ending point","type:number designate type, x1|y1|z1:number starting point, x2|y2|z2:number ending point",Obtain actor in designated area,獲取指定範圍內actor,รับactorที่อยู่ขอบเขตที่กำหนด,Obtener actor en la área designada,Obter ator na área designada,Obtenir acteur dans une zone désignée,新增待译0717,指定された地域でのactorを獲得する,지정된 장소에서 배우를 구합니다,Có được actor trong phạm vi chỉ định ,Получить актер в обозначенном районе,Belirlenen alanda oyuncu edinme,Ottenere attore in zona designata,Erhalten Schauspieler in dafür vorgesehenen Bereich,Dapat actor di area yg telah ditentukan,"local actorType = 0 --OBJ_TYPE_MONSTER
local x1, y1, z1 = -5, 1, -5
local x2, y2, z2 = 15, 9, 15
local ret, num, array = World:getActorsByBox(actorType, x1,y1,z1, x2,y2,z2)
if ret == ErrorCode.OK then print('Actors ===>> ', num, array) end"
20017,2,世界管理,getPlayerTotal,World:getPlayerTotal(alive),alive:number0表示阵亡，1表示存活，默认-1表示全体玩家,"ErrorCode.OK, num:number数量","获取全部玩家,可限制存活情况",local result = World:getPlayerTotal(alive),世界管理,World ,การจัดการโลก,Mundo,Mundo,Monde,新增待译0717,ワールド管理,세계,Thế giới,Мир,Dünya,Mondo,Welt,Peta,alive:number0 means being killed，1 means survival，default-1 means all the players,alive:number0 means being killed，1 means survival，default-1 means all the players,alive:number0 means being killed，1 means survival，default-1 means all the players,alive:number0 means being killed，1 means survival，default-1 means all the players,alive:number0 means being killed，1 means survival，default-1 means all the players,alive:number0 means being killed，1 means survival，default-1 means all the players,alive:number0 means being killed，1 means survival，default-1 means all the players,alive:number0 means being killed，1 means survival，default-1 means all the players,alive:number0 means being killed，1 means survival，default-1 means all the players,alive:number0 means being killed，1 means survival，default-1 means all the players,alive:number0 means being killed，1 means survival，default-1 means all the players,alive:number0 means being killed，1 means survival，default-1 means all the players,alive:number0 means being killed，1 means survival，default-1 means all the players,alive:number0 means being killed，1 means survival，default-1 means all the players,alive:number0 means being killed，1 means survival，default-1 means all the players,Obtain all the players. Can control their survival situation,"獲取全部玩家,可限制存活情況",รับผู้เล่นทั้งหมด สามารถจำกัดสภาพการอยู่รอด,Obtener todos los jugadores. Se puede controlar el estado supervivencia,Obtenha todos os jogadores. Pode controlar sua situação de sobrevivência,Obtenir tous les joueurs. Peut contrôler leur situation de survie,新增待译0717,すべてのプレイヤーを獲得する。生存状況をコントロールできます,모든 선수를 얻습니다. 생존 상황을 제어 할 수,Có được tất cả người chơi. Có thể kiểm soát tình hình sinh tồn của họ,Получить все игроки. Может контролировать их положение выживания,Tüm oyuncuları al. Hayatta kalma durumlarını kontrol edebilir,Ottenere tutti i giocatori. Può controllare la loro situazione di sopravvivenza,Erhalten alle Spieler. Kann ihr Überleben Situation unter Kontrolle,Dapat mengontrol situasi semua pemain di mode petualang,local result = World:getPlayerTotal(alive)
20018,2,世界管理,getAllPlayers,World:getAllPlayers(alive),alive:number0表示阵亡，1表示存活，默认-1表示全体玩家,"ErrorCode.OK, num:number数量, array:table玩家uin数组","获取全部玩家,可限制存活情况","local aliveType = -1 --获取所有玩家数据
local ret, num, array = World:getAllPlayers(aliveType)
if ret == ErrorCode.OK then print('Actors ===>> ', num, array) end",世界管理,World ,การจัดการโลก,Mundo,Mundo,Monde,新增待译0717,ワールド管理,세계,Thế giới,Мир,Dünya,Mondo,Welt,Peta,alive:number0 means being killed，1 means survival，default-1 means all the players,alive:number0 means being killed，1 means survival，default-1 means all the players,alive:number0 means being killed，1 means survival，default-1 means all the players,alive:number0 means being killed，1 means survival，default-1 means all the players,alive:number0 means being killed，1 means survival，default-1 means all the players,alive:number0 means being killed，1 means survival，default-1 means all the players,alive:number0 means being killed，1 means survival，default-1 means all the players,alive:number0 means being killed，1 means survival，default-1 means all the players,alive:number0 means being killed，1 means survival，default-1 means all the players,alive:number0 means being killed，1 means survival，default-1 means all the players,alive:number0 means being killed，1 means survival，default-1 means all the players,alive:number0 means being killed，1 means survival，default-1 means all the players,alive:number0 means being killed，1 means survival，default-1 means all the players,alive:number0 means being killed，1 means survival，default-1 means all the players,alive:number0 means being killed，1 means survival，default-1 means all the players,Obtain all the players. Can control their survival situation,"獲取全部玩家,可限制存活情況",รับผู้เล่นทั้งหมด สามารถจำกัดสภาพการอยู่รอด,Obtener todos los jugadores. Se puede controlar el estado supervivencia,Obtenha todos os jogadores. Pode controlar sua situação de sobrevivência,Obtenir tous les joueurs. Peut contrôler leur situation de survie,新增待译0717,すべてのプレイヤーを獲得する。生存状況をコントロールできます,모든 선수를 얻습니다. 생존 상황을 제어 할 수,Có được tất cả người chơi. Có thể kiểm soát tình hình sinh tồn của họ,Получить все игроки. Может контролировать их положение выживания,Tüm oyuncuları al. Hayatta kalma durumlarını kontrol edebilir,Ottenere tutti i giocatori. Può controllare la loro situazione di sopravvivenza,Erhalten alle Spieler. Kann ihr Überleben Situation unter Kontrolle,Dapat mengontrol situasi semua pemain di mode petualang,"local aliveType = -1 --Get all players data
local ret, num, array = World:getAllPlayers(aliveType)
if ret == ErrorCode.OK then print('Actors ===>> ', num, array) end"
20019,2,世界管理,randomOnePlayer,World:randomOnePlayer(alive),alive:number0表示阵亡，1表示存活，默认-1表示全体玩家,"ErrorCode.OK, uin:number",随机出一个玩家,"local aliveType = 1 --随机一个存活玩家Id
local ret, playerId = World:randomOnePlayer(aliveType)
if ret == ErrorCode.OK then print('Player ===>> ', playerId) end",世界管理,World ,การจัดการโลก,Mundo,Mundo,Monde,新增待译0717,ワールド管理,세계,Thế giới,Мир,Dünya,Mondo,Welt,Peta,alive:number0 means being killed，1 means survival，default-1 means all the players,alive:number0 means being killed，1 means survival，default-1 means all the players,alive:number0 means being killed，1 means survival，default-1 means all the players,alive:number0 means being killed，1 means survival，default-1 means all the players,alive:number0 means being killed，1 means survival，default-1 means all the players,alive:number0 means being killed，1 means survival，default-1 means all the players,alive:number0 means being killed，1 means survival，default-1 means all the players,alive:number0 means being killed，1 means survival，default-1 means all the players,alive:number0 means being killed，1 means survival，default-1 means all the players,alive:number0 means being killed，1 means survival，default-1 means all the players,alive:number0 means being killed，1 means survival，default-1 means all the players,alive:number0 means being killed，1 means survival，default-1 means all the players,alive:number0 means being killed，1 means survival，default-1 means all the players,alive:number0 means being killed，1 means survival，default-1 means all the players,alive:number0 means being killed，1 means survival，default-1 means all the players,Random Player,隨機出一個玩家,สุ่มเอาผู้เล่นหนึ่งคนออก,Jugador Aleatorio,Jogador Aleatório,Joueur aléatoire,新增待译0717,ランダムのプレイヤー,랜덤 플레이어,Người chơi ngẫu nhiên,Случайные игрока,Rastgele Oyuncu,casuale Player,zufällige Spieler,Random Player,"local aliveType = 1 --Any survivors Id
local ret, playerId = World:randomOnePlayer(aliveType)
if ret == ErrorCode.OK then print('Player ===>> ', playerId) end"
20020,2,世界管理,despawnActor,World:despawnActor(objid),objid:number,ErrorCode.OK,移除actor,"local ret, num, array = World:getActorsByBox(aType, x1,y1,z1, x2,y2,z2)
if array and #array>0 then --移除Actor
	local result = World:despawnActor(array[#array])
	if result == ErrorCode.OK then print('Despawn the last actor') end
end",世界管理,World ,การจัดการโลก,Mundo,Mundo,Monde,新增待译0717,ワールド管理,세계,Thế giới,Мир,Dünya,Mondo,Welt,Peta,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,Remove actor,移除actor,ลบactorออก,Remover Actor,Remover ator,Retirez acteur,新增待译0717,actorを削除,배우 제거,Di chuyển actor,Удалить актер,Aktör kaldır,rimuovere attore,entfernen Schauspieler,Hapus actor,"local ret, num, array = World:getActorsByBox(aType, x1,y1,z1, x2,y2,z2)
if array and #array>0 then --Remove Actor
        local result = World:despawnActor(array[#array])
        if result == ErrorCode.OK then print('Despawn the last actor') end
end"
20021,2,世界管理,spawnCreature,"World:spawnCreature(x, y, z, actorid, num)","x|y|z:number, mobid:number, num:number","ErrorCdoe.OK, objids:tableobjid数组",生成生物(包括怪物、NPC、动物等),"local x, y, z = 6, 7, 8
local actorId, actorCnt = 3812, 2 --小牛
local ret, objids = World:spawnCreature(x, y, z, actorId, actorCnt)
if objids and #objids>0 then
	for idx = 1, #objIds do --设置氧气依赖
		Creature:setOxygenNeed(objids[idx], true)
	end 
end",世界管理,World ,การจัดการโลก,Mundo,Mundo,Monde,新增待译0717,ワールド管理,세계,Thế giới,Мир,Dünya,Mondo,Welt,Peta,"x|y|z:number, mobid:number, num:number","x|y|z:number, mobid:number, num:number","x|y|z:number, mobid:number, num:number","x|y|z:number, mobid:number, num:number","x|y|z:number, mobid:number, num:number","x|y|z:number, mobid:number, num:number","x|y|z:number, mobid:number, num:number","x|y|z:number, mobid:number, num:number","x|y|z:number, mobid:number, num:number","x|y|z:number, mobid:number, num:number","x|y|z:number, mobid:number, num:number","x|y|z:number, mobid:number, num:number","x|y|z:number, mobid:number, num:number","x|y|z:number, mobid:number, num:number","x|y|z:number, mobid:number, num:number","Generate creature(including monster, NPC, animals etc.)",生成生物(包括怪物、NPC、動物等),กำเนิดสิ่งมีชีวิต(รวมถึงมอน NPC สัตวต์เป็นต้น),"Generar Criatura(incluyendo monstruos, NPC, animales)","Gere criatura (incluindo monstro, NPC, animais, etc.)","Générer créature (y compris monstre, NPC, animaux, etc.)",新增待译0717,生き物を生成する（モンスター、NPC、動物などを含む）,"(등 몬스터, NPC, 동물 포함) 생물을 생성","Tạo sinh vật ( bao gồm quái thú, NPC, động vật..v..v)","Сформировать существо (в том числе монстров, NPC, животных и т.д.)","Yaratık üret (canavar, NPC, hayvanlar vb. Dahil)","Generare creatura (compreso mostro, NPC, animali etc.)","Gene Kreatur (einschließlich Monster, NPC, Tiere etc.)","Menghasilkan makhluk (termasuk monster, NPC, hewan dll)","local x, y, z = 6, 7, 8
local actorId, actorCnt = 3812, 2 --Little cow
local ret, objids = World:spawnCreature(x, y, z, actorId, actorCnt)
if objids and #objids>0 then
        for idx = 1, #objIds do --Set the Oxygen dependence
                Creature:setOxygenNeed(objids[idx], true)
        end 
end"
20022,2,世界管理,despawnCreature,World:despawnCreature(objid),objid:number,ErrorCode.OK,移除生物,"local result = World:despawnCreature(objids[1])
if result == ErrorCode.OK then print('Despawn the creature') end",世界管理,World ,การจัดการโลก,Mundo,Mundo,Monde,新增待译0717,ワールド管理,세계,Thế giới,Мир,Dünya,Mondo,Welt,Peta,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,Remove creature,移除生物,ลบสิ่งมีชีวิตออก,Remover Criatura,Remover Criatura,Retirez la créature,新增待译0717,生き物を取り除く,생물을 제거,Di chuyển sinh vật,Удалить существо,Yaratığı kaldır,rimuovere creatura,entfernen Kreatur,Hapus makhluk,"local result = World:despawnCreature(objids[1])
if result == ErrorCode.OK then print('Despawn the creature') end"
20023,2,世界管理,spawnItem,"World:spawnItem(x, y, z, itemid, num)","x|y|z:number, itemid:number, num:number","ErrorCode.OK, objid:number",在指定位置生成道具,"local xPos, yPos, zPos = 5, 6, 7
local itemId, itemCnt = 15003, 30 --道具Id和数量
local ret, objIds = World:spawnItem(xPos, yPos, zPos, itemId, itemCnt)
if ret == ErrorCode.OK then print(""Item[1]====>>>"", objIds[1]) end",世界管理,World ,การจัดการโลก,Mundo,Mundo,Monde,新增待译0717,ワールド管理,세계,Thế giới,Мир,Dünya,Mondo,Welt,Peta,"x|y|z:number, itemid:number, num:number","x|y|z:number, itemid:number, num:number","x|y|z:number, itemid:number, num:number","x|y|z:number, itemid:number, num:number","x|y|z:number, itemid:number, num:number","x|y|z:number, itemid:number, num:number","x|y|z:number, itemid:number, num:number","x|y|z:number, itemid:number, num:number","x|y|z:number, itemid:number, num:number","x|y|z:number, itemid:number, num:number","x|y|z:number, itemid:number, num:number","x|y|z:number, itemid:number, num:number","x|y|z:number, itemid:number, num:number","x|y|z:number, itemid:number, num:number","x|y|z:number, itemid:number, num:number",Generate items in designate location,在指定位置生成道具,กำเนิดไอเทมในตำแหน่งที่กำหนด,Generar Objetos en lugar designado,Gerar itens em localização designada,Générer des éléments dans l'emplacement désigné,新增待译0717,指定の場所にアイテムを生成する,지정 위치에서 항목을 생성,Tạo công cụ ở vị trí chỉ định,Сформировать элементы в обозначенном месте,Belirtilen konumda öğeler üret,Genera elementi in posizione designato,Generieren Sie Elemente in designierten Standort,Menghasilkan item di lokasi yg ditentukan,"local xPos, yPos, zPos = 5, 6, 7
local itemId, itemCnt = 15003, 30 --Item's id and number
local ret, objIds = World:spawnItem(xPos, yPos, zPos, itemId, itemCnt)
if ret == ErrorCode.OK then print(""Item[1]====>>>"", objIds[1]) end"
20024,2,世界管理,despawnItemByBox,"World:despawnItemByBox(x1, y1, z1, x2, y2, z2)","x1|y1|z1:number起始位置, x2|y2|z2:number最终位置",ErrorCode.OK,移除道具(通过区域),"local x1, y1, z1 = -5, 5, -5
local x2, y2, z2 = 15, 9, 15
local result = World:despawnItemByBox(x1,y1,z1, x2,y2,z2)
if result == ErrorCode.OK then print('成功将指定区域的Item移除') end",世界管理,World ,การจัดการโลก,Mundo,Mundo,Monde,新增待译0717,ワールド管理,세계,Thế giới,Мир,Dünya,Mondo,Welt,Peta,"x1|y1|z1:number starting point, x2|y2|z2:number ending point","x1|y1|z1:number starting point, x2|y2|z2:number ending point","x1|y1|z1:number starting point, x2|y2|z2:number ending point","x1|y1|z1:number starting point, x2|y2|z2:number ending point","x1|y1|z1:number starting point, x2|y2|z2:number ending point","x1|y1|z1:number starting point, x2|y2|z2:number ending point","x1|y1|z1:number starting point, x2|y2|z2:number ending point","x1|y1|z1:number starting point, x2|y2|z2:number ending point","x1|y1|z1:number starting point, x2|y2|z2:number ending point","x1|y1|z1:number starting point, x2|y2|z2:number ending point","x1|y1|z1:number starting point, x2|y2|z2:number ending point","x1|y1|z1:number starting point, x2|y2|z2:number ending point","x1|y1|z1:number starting point, x2|y2|z2:number ending point","x1|y1|z1:number starting point, x2|y2|z2:number ending point","x1|y1|z1:number starting point, x2|y2|z2:number ending point",Remove items(By region),移除道具(通過區域),ลบไอเทมออก(ผ่านขอบเขต),Remover Objetos (por región),Remover itens (por região),Supprimer des éléments (par région),新增待译0717,アイテムを削除する（地域別）,(지역) 항목 제거,Di chuyển công cụ (Thông qua khu vực),Удалить элементы (по регионам),Öğeleri kaldır (Bölgeye göre),Rimuovere gli elementi (per regione),Entfernen Artikel (nach Region),Hapus item (berdasarkan wilayah),"local x1, y1, z1 = -5, 5, -5
local x2, y2, z2 = 15, 9, 15
local result = World:despawnItemByBox(x1,y1,z1, x2,y2,z2)
if result == ErrorCode.OK then print('Successfully remove Item in designate region') end"
20025,2,世界管理,despawnItemByObjid,World:despawnItemByObjid(objid),objid:number,ErrorCode.OK,移除道具(通过ID),"local result = World:despawnItemByObjid(objIds[1])
if result == ErrorCode.OK then print('成功将指定的Item移除') end",世界管理,World ,การจัดการโลก,Mundo,Mundo,Monde,新增待译0717,ワールド管理,세계,Thế giới,Мир,Dünya,Mondo,Welt,Peta,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,Remove item(By ID),移除道具(通過ID),ลบไอเทมออก(ผ่าน ID),Remover Objetos (por ID),Remover item (por ID),Supprimer l'élément (par ID),新增待译0717,アイテムを削除する（ID順）,(ID 별) 항목을 제거,Di chuyển công cụ ( Thông qua ID),Удалить элемент (по ID),Öğeyi kaldır (Kimliğe göre),Elimina voce (Per ID),Entfernen Artikel (von ID),Hapus item (berdasarkan ID),"local result = World:despawnItemByObjid(objIds[1])
if result == ErrorCode.OK then print('Successfully remove designate item') end"
20026,2,世界管理,spawnProjectile,"World:spawnProjectile(shooter, itemid, x, y, z, dirx, diry, dirz, speed)","shooter:number, itemid:number, x|y|z:number, dirx|diry|dirz:number, speed:number",ErrorCode.OK,生成投掷物,"local result = World:spawnProjectile(shooter, itemid, x, y, z, dirx, diry, dirz, speed)",世界管理,World ,การจัดการโลก,Mundo,Mundo,Monde,新增待译0717,ワールド管理,세계,Thế giới,Мир,Dünya,Mondo,Welt,Peta,"shooter:number, itemid:number, x|y|z:number, dirx|diry|dirz:number, speed:number","shooter:number, itemid:number, x|y|z:number, dirx|diry|dirz:number, speed:number","shooter:number, itemid:number, x|y|z:number, dirx|diry|dirz:number, speed:number","shooter:number, itemid:number, x|y|z:number, dirx|diry|dirz:number, speed:number","shooter:number, itemid:number, x|y|z:number, dirx|diry|dirz:number, speed:number","shooter:number, itemid:number, x|y|z:number, dirx|diry|dirz:number, speed:number","shooter:number, itemid:number, x|y|z:number, dirx|diry|dirz:number, speed:number","shooter:number, itemid:number, x|y|z:number, dirx|diry|dirz:number, speed:number","shooter:number, itemid:number, x|y|z:number, dirx|diry|dirz:number, speed:number","shooter:number, itemid:number, x|y|z:number, dirx|diry|dirz:number, speed:number","shooter:number, itemid:number, x|y|z:number, dirx|diry|dirz:number, speed:number","shooter:number, itemid:number, x|y|z:number, dirx|diry|dirz:number, speed:number","shooter:number, itemid:number, x|y|z:number, dirx|diry|dirz:number, speed:number","shooter:number, itemid:number, x|y|z:number, dirx|diry|dirz:number, speed:number","shooter:number, itemid:number, x|y|z:number, dirx|diry|dirz:number, speed:number",Generate Projectile,生成投擲物,กำเนิดสิ่ิ่งที่โยน,Generar Proyectil,Gerar Projétil,générer Projectile,新增待译0717,投げ物を生成,발사체를 생성,Tạo vật ném,Сформировать Projectile,Mermi Üret,genera proiettile,generieren Projektil,Menghasilkan projektil,"local result = World:spawnProjectile(shooter, itemid, x, y, z, dirx, diry, dirz, speed)"
30001,3,方块管理,isSolidBlock,"Block:isSolidBlock(x, y, z)",x|y|z:number,"ErrorCode.OK, data:boolean",方块是否是实体,"local result = Block:isSolidBlock(x, y, z)",方塊管理,Block ,การจัดการบล็อก,Bloque,Bloco,Bloc,新增待译0717,ブロック管理,블록,Khối vuông,блок,Blok,Bloccare,Block,Block ,x|y|z:number,x|y|z:number,x|y|z:number,x|y|z:number,x|y|z:number,x|y|z:number,x|y|z:number,x|y|z:number,x|y|z:number,x|y|z:number,x|y|z:number,x|y|z:number,x|y|z:number,x|y|z:number,x|y|z:number,Whether the block is entity,方塊是否是實體,บล็อกใช่ของแข็งหรือไม่,Si o no el bloque es entidad,Se o bloco é entidade,Si le bloc est l'entité,新增待译0717,ブロックが実体かどうか,블록이 실체 여부,Khối vuông có phải là thực thể,Будет ли блок предприятие,Bloğun varlık olup olmadığı,Se il blocco è un'entità,Ob der Block Einheit,Apakah blok berbentuk benda?,"local result = Block:isSolidBlock(x, y, z)"
30002,3,方块管理,isLiquidBlock,"Block:isLiquidBlock(x, y, z)",x|y|z:number,"ErrorCode.OK, data:boolean",方块是否是流体,"local result = Block:isLiquidBlock(x, y, z)",方塊管理,Block ,การจัดการบล็อก,Bloque,Bloco,Bloc,新增待译0717,ブロック管理,블록,Khối vuông,блок,Blok,Bloccare,Block,Block ,x|y|z:number,x|y|z:number,x|y|z:number,x|y|z:number,x|y|z:number,x|y|z:number,x|y|z:number,x|y|z:number,x|y|z:number,x|y|z:number,x|y|z:number,x|y|z:number,x|y|z:number,x|y|z:number,x|y|z:number,Whether the block is fluid,方塊是否是流體,บล็อกใช่ของเหลวหรือไม่,Si o no el bloque es fluído,Se o bloco é fluido,Si le bloc est fluide,新增待译0717,ブロックが流動的かどうか,블록이 유체 여부,Khối vuông có phải là chất lỏng,Будет ли блок жидкости,Bloğun sıvı olup olmadığı,Se il blocco è fluido,Ob der Block Fluid,Apakah blok berbentuk cairan?,"local result = Block:isLiquidBlock(x, y, z)"
30003,3,方块管理,isAirBlock,"Block:isAirBlock(x, y, z)",x|y|z:number,"ErrorCode.OK, data:boolean",方块是否是空气,"local result = Block:isAirBlock(x, y, z)",方塊管理,Block ,การจัดการบล็อก,Bloque,Bloco,Bloc,新增待译0717,ブロック管理,블록,Khối vuông,блок,Blok,Bloccare,Block,Block ,x|y|z:number,x|y|z:number,x|y|z:number,x|y|z:number,x|y|z:number,x|y|z:number,x|y|z:number,x|y|z:number,x|y|z:number,x|y|z:number,x|y|z:number,x|y|z:number,x|y|z:number,x|y|z:number,x|y|z:number,Whether the block is air,方塊是否是空氣,บล็อกใช่อากาศหรือไม่,Si o no el bloque es aire,Se o bloco é de ar,Si le bloc est l'air,新增待译0717,ブロックが空気かどうか,블록 공기 여부,Khối vuông có phải là không khí,Будет ли блок воздуха,Bloğun hava olup olmadığı,Se il blocco è dotato di aria,Ob der Block Luft,Apakah blok berbentuk udara?,"local result = Block:isAirBlock(x, y, z)"
30004,3,方块管理,getBlockID,"Block:getBlockID(x, y, z)",x|y|z:number,"ErrorCode.OK, id:number",获取block对应id,"local result = Block:getBlockID(x, y, z)",方塊管理,Block ,การจัดการบล็อก,Bloque,Bloco,Bloc,新增待译0717,ブロック管理,블록,Khối vuông,блок,Blok,Bloccare,Block,Block ,x|y|z:number,x|y|z:number,x|y|z:number,x|y|z:number,x|y|z:number,x|y|z:number,x|y|z:number,x|y|z:number,x|y|z:number,x|y|z:number,x|y|z:number,x|y|z:number,x|y|z:number,x|y|z:number,x|y|z:number,Obtain corresponding block id,獲取block對應id,รับ id ที่สอดคล้องกันของblock,Obtener ID correspodiente del Bloque,Obtenha o id de bloco correspondente,Obtenir id bloc correspondant,新增待译0717,ブロックに対応するidを取得,해당 블록 ID를 구하는,Nhận khối vuông với ID tương ứng,Получить соответствующий идентификатор блока,İlgili blok kimliğini alın,Ottenere corrispondente blocco id,Erhalten entsprechende Block-ID,Dapat blok sesuai dengan ID,"local result = Block:getBlockID(x, y, z)"
30005,3,方块管理,setBlockAll,"Block:setBlockAll(x, y, z, blockid, data)","x|y|z:number, blockid:number, data:number, flag:number",ErrorCode.OK,设置blockalldata 更新+通知,"local result = Block:setBlockAll(x, y, z, blockid, data)",方塊管理,Block ,การจัดการบล็อก,Bloque,Bloco,Bloc,新增待译0717,ブロック管理,블록,Khối vuông,блок,Blok,Bloccare,Block,Block ,"x|y|z:number, blockid:number, data:number, flag:number","x|y|z:number, blockid:number, data:number, flag:number","x|y|z:number, blockid:number, data:number, flag:number","x|y|z:number, blockid:number, data:number, flag:number","x|y|z:number, blockid:number, data:number, flag:number","x|y|z:number, blockid:number, data:number, flag:number","x|y|z:number, blockid:number, data:number, flag:number","x|y|z:number, blockid:number, data:number, flag:number","x|y|z:number, blockid:number, data:number, flag:number","x|y|z:number, blockid:number, data:number, flag:number","x|y|z:number, blockid:number, data:number, flag:number","x|y|z:number, blockid:number, data:number, flag:number","x|y|z:number, blockid:number, data:number, flag:number","x|y|z:number, blockid:number, data:number, flag:number","x|y|z:number, blockid:number, data:number, flag:number",Set blockalldata update and inform,設置blockalldata 更新+通知,ตั้งค่าblockalldata อัปเดต+การแจ้งเตือน,Definir Actualización de blockalldata e inform. ,Defina a atualização do blockalldata e informe,Mettre à jour blockalldata et informer,新增待译0717,blockalldataの更新＋お知らせを設定する,blockalldata 업데이 트를 설정하고 통보,Cài đặt blockalldata Cập nhật+ Thông báo,Установите blockalldata обновление и сообщить,Blockalldata güncellemesini ayarlayın ve bilgilendirin,Impostare aggiornamento blockalldata e informare,Set blockalldata Update und informieren,Atur blockalldata update dan beri pengumuman,"local result = Block:setBlockAll(x, y, z, blockid, data)"
30006,3,方块管理,destroyBlock,"Block:destroyBlock(x, y, z, dropitem)","x|y|z:number, dropitem:boolean",ErrorCode.OK,销毁方块,"local result = Block:destroyBlock(x, y, z, dropitem)",方塊管理,Block ,การจัดการบล็อก,Bloque,Bloco,Bloc,新增待译0717,ブロック管理,블록,Khối vuông,блок,Blok,Bloccare,Block,Block ,"x|y|z:number, dropitem:boolean","x|y|z:number, dropitem:boolean","x|y|z:number, dropitem:boolean","x|y|z:number, dropitem:boolean","x|y|z:number, dropitem:boolean","x|y|z:number, dropitem:boolean","x|y|z:number, dropitem:boolean","x|y|z:number, dropitem:boolean","x|y|z:number, dropitem:boolean","x|y|z:number, dropitem:boolean","x|y|z:number, dropitem:boolean","x|y|z:number, dropitem:boolean","x|y|z:number, dropitem:boolean","x|y|z:number, dropitem:boolean","x|y|z:number, dropitem:boolean",Destroy the block,銷毀方塊,ทำลายบล็อก,Destruir el bloque,Destrua o bloco,Détruire le bloc,新增待译0717,ブロックを破壊する,블록 승,Phá khối ,Уничтожить блок,Bloğu yok et,Distruggere il blocco,Zerstören Sie den Block,Hancurkan blok,"local result = Block:destroyBlock(x, y, z, dropitem)"
30007,3,方块管理,placeBlock,"Block:placeBlock(blockid, x, y, z, face)","blockid:number, x|y|z:number, face:number","ErrorCode.OK, ret:boolean成功放置与否",放置方块,"local result = Block:placeBlock(blockid, x, y, z, face)",方塊管理,Block ,การจัดการบล็อก,Bloque,Bloco,Bloc,新增待译0717,ブロック管理,블록,Khối vuông,блок,Blok,Bloccare,Block,Block ,"blockid:number, x|y|z:number, face:number","blockid:number, x|y|z:number, face:number","blockid:number, x|y|z:number, face:number","blockid:number, x|y|z:number, face:number","blockid:number, x|y|z:number, face:number","blockid:number, x|y|z:number, face:number","blockid:number, x|y|z:number, face:number","blockid:number, x|y|z:number, face:number","blockid:number, x|y|z:number, face:number","blockid:number, x|y|z:number, face:number","blockid:number, x|y|z:number, face:number","blockid:number, x|y|z:number, face:number","blockid:number, x|y|z:number, face:number","blockid:number, x|y|z:number, face:number","blockid:number, x|y|z:number, face:number",Place the block,放置方塊,วางบล็อก,Colocar el Bloque,Coloque o bloco,Placez le bloc,新增待译0717,ブロックを置く,블록 배치,Cài đặt khối,Поместите блок,Bloğu yerleştirin,Posizionare il blocco,Platzieren Sie den Block,Letakkan blok,"local result = Block:placeBlock(blockid, x, y, z, face)"
30008,3,方块管理,setBlockAllForUpdate,"Block:setBlockAllForUpdate(x, y, z, blockid)","x|y|z:number, blockid:number, data:number, flag:number",ErrorCode.OK,设置blockalldata 通知周围方块,"local result = Block:setBlockAllForUpdate(x, y, z, blockid)",方塊管理,Block ,การจัดการบล็อก,Bloque,Bloco,Bloc,新增待译0717,ブロック管理,블록,Khối vuông,блок,Blok,Bloccare,Block,Block ,"x|y|z:number, blockid:number, data:number, flag:number","x|y|z:number, blockid:number, data:number, flag:number","x|y|z:number, blockid:number, data:number, flag:number","x|y|z:number, blockid:number, data:number, flag:number","x|y|z:number, blockid:number, data:number, flag:number","x|y|z:number, blockid:number, data:number, flag:number","x|y|z:number, blockid:number, data:number, flag:number","x|y|z:number, blockid:number, data:number, flag:number","x|y|z:number, blockid:number, data:number, flag:number","x|y|z:number, blockid:number, data:number, flag:number","x|y|z:number, blockid:number, data:number, flag:number","x|y|z:number, blockid:number, data:number, flag:number","x|y|z:number, blockid:number, data:number, flag:number","x|y|z:number, blockid:number, data:number, flag:number","x|y|z:number, blockid:number, data:number, flag:number",Set blockalldata and inform surrounding blocks,設置blockalldata 通知周圍方塊,ตั้งค่าblockalldata แจ้งเตือนบล็อกรอบข้าง,Definir blockalldata e inform bloques alrededor,Defina blockalldata e informe blocos adjacentes,Définir blockalldata et informer les blocs environnants,新增待译0717,blockalldataを設定して周囲のブロックにお知らせをする,blockalldata 설정하고 주변의 블록을 통지,Cài đặt blockalldata Thông báo các khối xung quanh,Установить blockalldata и информировать окружающие блоки,Blockalldata'yı ayarlayın ve çevreleyen blokları bilgilendirin,Impostare blockalldata e informare i blocchi circostanti,Stellen Sie blockalldata und informieren umliegenden Blöcke,Atur blockalldata and umumkan ke blok sekitarnya,"local result = Block:setBlockAllForUpdate(x, y, z, blockid)"
30009,3,方块管理,setBlockAllForNotify,"Block:setBlockAllForNotify(x, y, z, blockid)","x|y|z:number, blockid:number, data:number, flag:number",ErrorCode.OK,设置blockalldata 更新当前位置方块,"local result = Block:setBlockAllForNotify(x, y, z, blockid)",方塊管理,Block ,การจัดการบล็อก,Bloque,Bloco,Bloc,新增待译0717,ブロック管理,블록,Khối vuông,блок,Blok,Bloccare,Block,Block ,"x|y|z:number, blockid:number, data:number, flag:number","x|y|z:number, blockid:number, data:number, flag:number","x|y|z:number, blockid:number, data:number, flag:number","x|y|z:number, blockid:number, data:number, flag:number","x|y|z:number, blockid:number, data:number, flag:number","x|y|z:number, blockid:number, data:number, flag:number","x|y|z:number, blockid:number, data:number, flag:number","x|y|z:number, blockid:number, data:number, flag:number","x|y|z:number, blockid:number, data:number, flag:number","x|y|z:number, blockid:number, data:number, flag:number","x|y|z:number, blockid:number, data:number, flag:number","x|y|z:number, blockid:number, data:number, flag:number","x|y|z:number, blockid:number, data:number, flag:number","x|y|z:number, blockid:number, data:number, flag:number","x|y|z:number, blockid:number, data:number, flag:number",Set blockalldata and update current location blocks,設置blockalldata 更新當前位置方塊,ตั้งค่าblockalldata อัปเดตบล็อกที่อยู่ตำแหน่งปัจจุบัน,Definir blockalldata y actualizar bloques en lugar actual,Defina blockalldata e atualize os blocos de localização atuais,Définir blockalldata et mettre à jour les blocs de position actuelle,新增待译0717,blockalldataを設定し、現在位置のブロックを更新します,현재 위치 블록을 blockalldata 설정 및 업데이트,Cài đặt blockalldata Cập nhật vị trí khối vuông hiện tại,Установите blockalldata и обновлять текущие блоки местоположения,Blockalldata'yı ayarla ve geçerli konum bloklarını güncelle,Impostare blockalldata e aggiornare blocchi posizione corrente,Stellen Sie blockalldata und aktualisieren aktuellen Standort Blöcke,Atur blockalldata and update lokasi blok saat ini,"local result = Block:setBlockAllForNotify(x, y, z, blockid)"
30010,3,方块管理,setBlockSettingAttState,"Block:setBlockSettingAttState(blockid, atttype, switch)","blockid:number, atttype:BLOCKATTR, switch:boolean",ErrorCode.OK,设置方块设置属性状态,"local result = Block:setBlockSettingAttState(blockid, atttype, switch)",方塊管理,Block ,การจัดการบล็อก,Bloque,Bloco,Bloc,新增待译0717,ブロック管理,블록,Khối vuông,блок,Blok,Bloccare,Block,Block ,"blockid:number, atttype:BLOCKATTR, switch:boolean","blockid:number, atttype:BLOCKATTR, switch:boolean","blockid:number, atttype:BLOCKATTR, switch:boolean","blockid:number, atttype:BLOCKATTR, switch:boolean","blockid:number, atttype:BLOCKATTR, switch:boolean","blockid:number, atttype:BLOCKATTR, switch:boolean","blockid:number, atttype:BLOCKATTR, switch:boolean","blockid:number, atttype:BLOCKATTR, switch:boolean","blockid:number, atttype:BLOCKATTR, switch:boolean","blockid:number, atttype:BLOCKATTR, switch:boolean","blockid:number, atttype:BLOCKATTR, switch:boolean","blockid:number, atttype:BLOCKATTR, switch:boolean","blockid:number, atttype:BLOCKATTR, switch:boolean","blockid:number, atttype:BLOCKATTR, switch:boolean","blockid:number, atttype:BLOCKATTR, switch:boolean",Set block's attribute and state,設置方塊設置屬性狀態,ตั้งค่าสถานะลักษณะของบล็อก,Definir atributos y estado de bloque,Definir atributo e estado do bloco,Définir l'attribut de bloc et de l'état,新增待译0717,ブロックの属性と状態を設定する,설정 블록의 속성과 상태,Cài đặt trạng thái thuộc tính khối vuông,Атрибут набора блоков и состояние,Bloğun niteliğini ve durumunu ayarla,attributi e lo stato del blocco Set,Stellen Block des Attributs und Zustand,Atur properti dan status blok,"local result = Block:setBlockSettingAttState(blockid, atttype, switch)"
30011,3,方块管理,getBlockSwitchStatus,Block:getBlockSwitchStatus(pos),x|y|z:number,"ErrorCode.OK, isactive:boolean",获取功能方块的开关状态,local result = Block:getBlockSwitchStatus(pos),方塊管理,Block ,การจัดการบล็อก,Bloque,Bloco,Bloc,新增待译0717,ブロック管理,블록,Khối vuông,блок,Blok,Bloccare,Block,Block ,x|y|z:number,x|y|z:number,x|y|z:number,x|y|z:number,x|y|z:number,x|y|z:number,x|y|z:number,x|y|z:number,x|y|z:number,x|y|z:number,x|y|z:number,x|y|z:number,x|y|z:number,x|y|z:number,x|y|z:number,Obtain on-off state of function block,獲取功能方塊的開關狀態,รับสถานะสวิตช์ของบล็อกฟังก์ชั่น,Obtener estado on-off del bloque funcional,Obter estado on-off do bloco de funções,Obtenir état de marche-arrêt du bloc fonctionnel,新增待译0717,機能ブロックのオンオフ状態を取得,기능 블록의 유무를 구하는,Có được trạng thái bật- tắt của khối chức năng,Получить двухпозиционный состояние функционального блока,Açık-kapalı fonksiyon bloğu durumunu al,Ottenere stato on-off del blocco funzionale,Erhalten Ein-Aus-Zustand des Funktionsbausteins,Dapat status on-off blok,local result = Block:getBlockSwitchStatus(pos)
30012,3,方块管理,setBlockSwitchStatus,"Block:setBlockSwitchStatus(pos, isactive)","x|y|z:number, isactive:boolean",ErrorCode.OK,设置功能方块的开关状态,"local result = Block:setBlockSwitchStatus(pos, isactive)",方塊管理,Block ,การจัดการบล็อก,Bloque,Bloco,Bloc,新增待译0717,ブロック管理,블록,Khối vuông,блок,Blok,Bloccare,Block,Block ,"x|y|z:number, isactive:boolean","x|y|z:number, isactive:boolean","x|y|z:number, isactive:boolean","x|y|z:number, isactive:boolean","x|y|z:number, isactive:boolean","x|y|z:number, isactive:boolean","x|y|z:number, isactive:boolean","x|y|z:number, isactive:boolean","x|y|z:number, isactive:boolean","x|y|z:number, isactive:boolean","x|y|z:number, isactive:boolean","x|y|z:number, isactive:boolean","x|y|z:number, isactive:boolean","x|y|z:number, isactive:boolean","x|y|z:number, isactive:boolean",Set on-off state of function block,設置功能方塊的開關狀態,ตั้งค่าสถานะสวิตช์ของบล็อกฟังก์ชั่น,Definir estado on-off del bloque funcional,Definir estado on-off do bloco de funções,Mettre en état d'arrêt du bloc fonctionnel,新增待译0717,機能ブロックのオンオフ状態を設定する,기능 블록의 설정 유무,Cài đặt trạng thái bật - tắt khối chức năng,Установите двухпозиционный состояние функционального блока,Fonksiyon bloğunun açma-kapama durumunu ayarlama,Set on-off stato di blocco funzionale,An-Aus-Zustand des Funktionsbausteins,Atur status on-off pada blok,"local result = Block:setBlockSwitchStatus(pos, isactive)"
40001,4,游戏Actor,isPlayer,Actor:isPlayer(objid),objid,ErrorCode.OK是玩家是玩家,检测是否是玩家,local result = Actor:isPlayer(objid),遊戲Actor,Game Actor,Actor เกม,Actor de Juego,Ator de Jogo,Acteur de jeu,新增待译0717,ゲームActor,게임 배우,Game Actor,Игра актера,Oyun Aktörü,Gioco Attore,Spiel Schauspieler,Aktor Game,objid,objid,objid,objid,objid,objid,objid,objid,objid,objid,objid,objid,objid,objid,objid,Detect if it's a player,檢測是否是玩家,ตรวจสอบว่าใช่ผู้เล่นหรือไม่,Detecta si eres un jugador.,Detectar se é um jogador,Détecter s'il est un joueur,新增待译0717,プレイヤーかどうかを検出,이 플레이어의 경우 감지,Kiểm tra xem có phải là người chơi,"Обнаружить, если это игрок",Oyuncu olup olmadığını tespit et,Rileva se si tratta di un giocatore,"Erkennen, ob es ein Spieler ist",Tes apakah dia seorang pemain,local result = Actor:isPlayer(objid)
40002,4,游戏Actor,isMob,Actor:isMob(objid),objid,ErrorCode.OK是怪物是怪物,检测是否是怪物,local result = Actor:isMob(objid),遊戲Actor,Game Actor,Actor เกม,Actor de Juego,Ator de Jogo,Acteur de jeu,新增待译0717,ゲームActor,게임 배우,Game Actor,Игра актера,Oyun Aktörü,Gioco Attore,Spiel Schauspieler,Aktor Game,objid,objid,objid,objid,objid,objid,objid,objid,objid,objid,objid,objid,objid,objid,objid,Detect if it's a monster,檢測是否是怪物,ตรวจสอบว่าใช่สิ่งมีชีวิตหรือไม่,Detectar si es un monstruo.,Detectar se é um monstro,Détecter si elle est un monstre,新增待译0717,それがモンスターかどうかを検出,이 괴물 경우 감지,Kiểm tra xem có phải là quái vật,"Обнаружить, если это монстр",Bir canavar olup olmadığını tespit et,Rileva se si tratta di un mostro,"Erkennen, ob es ein Monster ist",Tes apakah dia monster,local result = Actor:isMob(objid)
40003,4,游戏Actor,getObjType,Actor:getObjType(objid),objid,"ErrorCode.OK, objtype:number",获取actor类型:生物/玩家...,local result = Actor:getObjType(objid),遊戲Actor,Game Actor,Actor เกม,Actor de Juego,Ator de Jogo,Acteur de jeu,新增待译0717,ゲームActor,게임 배우,Game Actor,Игра актера,Oyun Aktörü,Gioco Attore,Spiel Schauspieler,Aktor Game,objid,objid,objid,objid,objid,objid,objid,objid,objid,objid,objid,objid,objid,objid,objid,Get the type of actor: creature/player...,獲取actor類型:生物/玩家...,รับประเภทactor:สิ่งมีชีวิต/ผู้เล่น...,Obtener tipo de actor: criatura / jugador ...,Obtém o tipo de ator: criatura/jogador...,Obtenez le type d'acteur: créature / joueur ...,新增待译0717,actorのタイプを取得：生き物/プレイヤー...,배우의 유형 가져 오기 : 생물 / 플레이어를 ...,Nhận được loại actor: sinh vật/ người chơi,Получить тип актера: существо / игрок ...,Oyuncu tipini al: yaratık/oyuncu ...,Prendi il tipo di attore: creatura / giocatore ...,Holen Sie sich das Art von Schauspieler: Kreatur / Spieler ...,Dapat tipe aktor : makhluk/pemain dll..,local result = Actor:getObjType(objid)
40004,4,游戏Actor,isInAir,Actor:isInAir(objid),objid:number,ErrorCode.OK,是否在空中,local result = Actor:isInAir(objid),遊戲Actor,Game Actor,Actor เกม,Actor de Juego,Ator de Jogo,Acteur de jeu,新增待译0717,ゲームActor,게임 배우,Game Actor,Игра актера,Oyun Aktörü,Gioco Attore,Spiel Schauspieler,Aktor Game,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,If it's in the air,是否在空中,อยู่ในอากาศหรือไม่,Si esta en el aire,Se estiver no ar,Si elle est dans l'air,新增待译0717,空にいるかどうか,그것은 공중에 있다면,Có phải đang trên không trung,Если он находится в воздухе,Havadaysa,Se è nell'aria,Wenn es in der Luft,Apakah di udara?,local result = Actor:isInAir(objid)
40005,4,游戏Actor,getPosition,Actor:getPosition(objid),objid,"ErrorCode.OK, x|y|z:number",获取actor位置,local result = Actor:getPosition(objid),遊戲Actor,Game Actor,Actor เกม,Actor de Juego,Ator de Jogo,Acteur de jeu,新增待译0717,ゲームActor,게임 배우,Game Actor,Игра актера,Oyun Aktörü,Gioco Attore,Spiel Schauspieler,Aktor Game,objid,objid,objid,objid,objid,objid,objid,objid,objid,objid,objid,objid,objid,objid,objid,Get the location of actor,獲取actor位置,รับตำแหน่งของสักแสดง,Obtener la ubicación del actor,Obter a localização do ator,Obtenez l'emplacement de l'acteur,新增待译0717,actorの位置を取得,배우의 위치를 ​​가져옵니다,Nhận vị trí actor,Получить расположение актера,Aktörün yerini al,Ottenere la posizione dell'attore,Holen Sie sich die Position Schauspieler,Dapat lokasi aktor,local result = Actor:getPosition(objid)
40006,4,游戏Actor,setPosition,"Actor:setPosition(objid, x, y, z)","objid:number, x|y|z:number",ErrorCode.OK,设置actor位置,"local result = Actor:setPosition(objid, x, y, z)",遊戲Actor,Game Actor,Actor เกม,Actor de Juego,Ator de Jogo,Acteur de jeu,新增待译0717,ゲームActor,게임 배우,Game Actor,Игра актера,Oyun Aktörü,Gioco Attore,Spiel Schauspieler,Aktor Game,"objid:number, x|y|z:number","objid:number, x|y|z:number","objid:number, x|y|z:number","objid:number, x|y|z:number","objid:number, x|y|z:number","objid:number, x|y|z:number","objid:number, x|y|z:number","objid:number, x|y|z:number","objid:number, x|y|z:number","objid:number, x|y|z:number","objid:number, x|y|z:number","objid:number, x|y|z:number","objid:number, x|y|z:number","objid:number, x|y|z:number","objid:number, x|y|z:number",Set the location of actor,設置actor位置,ตั้งค่าตำแหน่งของสักแสดง,Establecer ubicación del actor,Definir a localização do ator,Définissez l'emplacement de l'acteur,新增待译0717,actorの位置を設定,배우의 위치를 ​​설정합니다,Cài đặt vị trí actor,Установить местонахождение актера,Aktörün konumunu ayarla,Impostare la posizione dell'attore,Stellen Sie die Lage von Schauspieler,Atur lokasi aktor,"local result = Actor:setPosition(objid, x, y, z)"
40007,4,游戏Actor,jump,Actor:jump(objid),objid:number,ErrorCode.OK,跳跃,local result = Actor:jump(objid),遊戲Actor,Game Actor,Actor เกม,Actor de Juego,Ator de Jogo,Acteur de jeu,新增待译0717,ゲームActor,게임 배우,Game Actor,Игра актера,Oyun Aktörü,Gioco Attore,Spiel Schauspieler,Aktor Game,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,Jump,跳躍,กระโดด,Saltar,Saltar,Saut,新增待译0717,ジャンプ,도약,Nhảy,Прыгать,Atlama,Saltare,Springen,Lompat,local result = Actor:jump(objid)
40008,4,游戏Actor,killSelf,Actor:killSelf(objid),objid:number,ErrorCode.OK,杀死自己,local result = Actor:killSelf(objid),遊戲Actor,Game Actor,Actor เกม,Actor de Juego,Ator de Jogo,Acteur de jeu,新增待译0717,ゲームActor,게임 배우,Game Actor,Игра актера,Oyun Aktörü,Gioco Attore,Spiel Schauspieler,Aktor Game,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,Kill yourself,殺死自己,ฆ่าตัวตาย,Suicidate,Mate-se,Tuez-vous,新增待译0717,自殺,자신을 죽여,Tự sát,Убей себя,Kendini öldür,Ucciditi,kill yourself,Serang diri sendiri,local result = Actor:killSelf(objid)
40009,4,游戏Actor,getCurPlaceDir,Actor:getCurPlaceDir(objid),objid:number,"ErrorCode.OK, dir:number",获取当前朝向,local result = Actor:getCurPlaceDir(objid),遊戲Actor,Game Actor,Actor เกม,Actor de Juego,Ator de Jogo,Acteur de jeu,新增待译0717,ゲームActor,게임 배우,Game Actor,Игра актера,Oyun Aktörü,Gioco Attore,Spiel Schauspieler,Aktor Game,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,Get the current facing direction,獲取當前朝向,รับทิศทางที่หันหน้าปัจจุบัน,Obtener direccion actual,Obter a direção atual,Obtenez la direction actuelle face,新增待译0717,現在の向きを取得,현재 직면하고 방향을 받기,Nhận hướng hiện tại,Получить текущее направление обращенного,Geçerli yönü göster,Prendi la direzione di fronte a corrente,Holen Sie sich die aktuelle Blickrichtung,Dapat arah pandang utama,local result = Actor:getCurPlaceDir(objid)
40010,4,游戏Actor,tryMoveToActor,"Actor:tryMoveToActor(self_objid, target_objid, speed)","self_objid:number, target_objid:number, speed:number",ErrorCode.OK,向目标actor移动,"local result = Actor:tryMoveToActor(self_objid, target_objid, speed)",遊戲Actor,Game Actor,Actor เกม,Actor de Juego,Ator de Jogo,Acteur de jeu,新增待译0717,ゲームActor,게임 배우,Game Actor,Игра актера,Oyun Aktörü,Gioco Attore,Spiel Schauspieler,Aktor Game,"self_objid:number, target_objid:number, speed:number","self_objid:number, target_objid:number, speed:number","self_objid:number, target_objid:number, speed:number","self_objid:number, target_objid:number, speed:number","self_objid:number, target_objid:number, speed:number","self_objid:number, target_objid:number, speed:number","self_objid:number, target_objid:number, speed:number","self_objid:number, target_objid:number, speed:number","self_objid:number, target_objid:number, speed:number","self_objid:number, target_objid:number, speed:number","self_objid:number, target_objid:number, speed:number","self_objid:number, target_objid:number, speed:number","self_objid:number, target_objid:number, speed:number","self_objid:number, target_objid:number, speed:number","self_objid:number, target_objid:number, speed:number",Move toward target actor,向目標actor移動,เคลื่อนที่ไปยังเป้าหมายactor,Mover al actor objetivo,Mova-se para o ator alvo,Déplacer vers l'acteur cible,新增待译0717,ターゲットのactorに向かって移す,목표 배우를 향해 이동,Di chuyển về phía actor mục tiêu,Двигайтесь к цели актера,Hedef aktörya doğru hareket et,Muoversi verso attore bersaglio,Bewegen sich in Richtung Ziel Schauspieler,Bergerak ke target aktor,"local result = Actor:tryMoveToActor(self_objid, target_objid, speed)"
40011,4,游戏Actor,tryMoveToPos,"Actor:tryMoveToPos(objid, x, y, z, speed)","objid:number, x|y|z:number, speed:number",ErrorCode.OK,向目标位置移动,"local result = Actor:tryMoveToPos(objid, x, y, z, speed)",遊戲Actor,Game Actor,Actor เกม,Actor de Juego,Ator de Jogo,Acteur de jeu,新增待译0717,ゲームActor,게임 배우,Game Actor,Игра актера,Oyun Aktörü,Gioco Attore,Spiel Schauspieler,Aktor Game,"objid:number, x|y|z:number, speed:number","objid:number, x|y|z:number, speed:number","objid:number, x|y|z:number, speed:number","objid:number, x|y|z:number, speed:number","objid:number, x|y|z:number, speed:number","objid:number, x|y|z:number, speed:number","objid:number, x|y|z:number, speed:number","objid:number, x|y|z:number, speed:number","objid:number, x|y|z:number, speed:number","objid:number, x|y|z:number, speed:number","objid:number, x|y|z:number, speed:number","objid:number, x|y|z:number, speed:number","objid:number, x|y|z:number, speed:number","objid:number, x|y|z:number, speed:number","objid:number, x|y|z:number, speed:number",Move toward target actor,向目標位置移動,เคลื่อนที่ไปยังเป้าหมายตำแหน่ง,Mover al actor objetivo,Mova-se para o ator alvo,Déplacer vers l'acteur cible,新增待译0717,ターゲット場所に向かって移動,목표 배우를 향해 이동,Di chuyển về phía vị trí mục tiêu,Двигайтесь к цели актера,Hedef aktörya doğru hareket et,Muoversi verso attore bersaglio,Bewegen sich in Richtung Ziel Schauspieler,Bergerak ke lokasi target aktor,"local result = Actor:tryMoveToPos(objid, x, y, z, speed)"
40012,4,游戏Actor,addBuff,"Actor:addBuff(objid, buffid, bufflv, customticks)","objid:number, buffid:number, bufflv:number, customticks:number",ErrorCode.OK,增加buff,"local result = Actor:addBuff(objid, buffid, bufflv, customticks)",遊戲Actor,Game Actor,Actor เกม,Actor de Juego,Ator de Jogo,Acteur de jeu,新增待译0717,ゲームActor,게임 배우,Game Actor,Игра актера,Oyun Aktörü,Gioco Attore,Spiel Schauspieler,Aktor Game,"objid:number, buffid:number, bufflv:number, customticks:number","objid:number, buffid:number, bufflv:number, customticks:number","objid:number, buffid:number, bufflv:number, customticks:number","objid:number, buffid:number, bufflv:number, customticks:number","objid:number, buffid:number, bufflv:number, customticks:number","objid:number, buffid:number, bufflv:number, customticks:number","objid:number, buffid:number, bufflv:number, customticks:number","objid:number, buffid:number, bufflv:number, customticks:number","objid:number, buffid:number, bufflv:number, customticks:number","objid:number, buffid:number, bufflv:number, customticks:number","objid:number, buffid:number, bufflv:number, customticks:number","objid:number, buffid:number, bufflv:number, customticks:number","objid:number, buffid:number, bufflv:number, customticks:number","objid:number, buffid:number, bufflv:number, customticks:number","objid:number, buffid:number, bufflv:number, customticks:number",Obtain Buff,增加buff,เพิ่มbuff,Obtener buff,Obter Buff,obtenir Buff,新增待译0717,buffを追加する,버프를 얻,Thêm buff,Получить Буфф,Buff al,ottenere Buff,erhalten Buff,Dapat buff,"local result = Actor:addBuff(objid, buffid, bufflv, customticks)"
40013,4,游戏Actor,hasBuff,"Actor:hasBuff(objid, buffid)","objid:number, buffid:number","ErrorCode.OK, ret:boolean",是否具有某个buff,"local result = Actor:hasBuff(objid, buffid)",遊戲Actor,Game Actor,Actor เกม,Actor de Juego,Ator de Jogo,Acteur de jeu,新增待译0717,ゲームActor,게임 배우,Game Actor,Игра актера,Oyun Aktörü,Gioco Attore,Spiel Schauspieler,Aktor Game,"objid:number, buffid:number","objid:number, buffid:number","objid:number, buffid:number","objid:number, buffid:number","objid:number, buffid:number","objid:number, buffid:number","objid:number, buffid:number","objid:number, buffid:number","objid:number, buffid:number","objid:number, buffid:number","objid:number, buffid:number","objid:number, buffid:number","objid:number, buffid:number","objid:number, buffid:number","objid:number, buffid:number",If it has any buff,是否具有某個buff,มีbuffบางอย่างหรือไม่,Si tienes alguna afición,Se tiver algum buff,Si elle a un buff,新增待译0717,それに何かbuffがあるかどうか,그것은 어떤 버프가있는 경우,Có phải có buff không,Если это имеет какой-либо положительный эффект,Herhangi bir buff tutkusu varsa,Se ha qualsiasi appassionato,Wenn es hat jede Buff,Apakah ada buff?,"local result = Actor:hasBuff(objid, buffid)"
40014,4,游戏Actor,removeBuff,"Actor:removeBuff(objid, buffid)","objid:number, buffid:number",ErrorCode.OK,删除buff,"local result = Actor:removeBuff(objid, buffid)",遊戲Actor,Game Actor,Actor เกม,Actor de Juego,Ator de Jogo,Acteur de jeu,新增待译0717,ゲームActor,게임 배우,Game Actor,Игра актера,Oyun Aktörü,Gioco Attore,Spiel Schauspieler,Aktor Game,"objid:number, buffid:number","objid:number, buffid:number","objid:number, buffid:number","objid:number, buffid:number","objid:number, buffid:number","objid:number, buffid:number","objid:number, buffid:number","objid:number, buffid:number","objid:number, buffid:number","objid:number, buffid:number","objid:number, buffid:number","objid:number, buffid:number","objid:number, buffid:number","objid:number, buffid:number","objid:number, buffid:number",Delete the buff,刪除buff,ลบbuff,Eliminar el beneficio,Excluir o buff,Supprimer le buff,新增待译0717,buffを削除,버프 삭제,Xóa buff,Удалить бафф,Buff'ı sil,Eliminare il buff,Löschen Sie den Buff,Hapus buff?,"local result = Actor:removeBuff(objid, buffid)"
40015,4,游戏Actor,clearAllBuff,Actor:clearAllBuff(objid),objid:number,ErrorCode.OK,清除所有buff,local result = Actor:clearAllBuff(objid),遊戲Actor,Game Actor,Actor เกม,Actor de Juego,Ator de Jogo,Acteur de jeu,新增待译0717,ゲームActor,게임 배우,Game Actor,Игра актера,Oyun Aktörü,Gioco Attore,Spiel Schauspieler,Aktor Game,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,Clear all the buff,清除所有buff,ลบbuffทั้งหมด,Borrar todo buff,Limpar todo o buff,Effacer tout le buff,新增待译0717,すべてのバフをクリア,모두 지우기 버프,Xóa tất cả buff,Очистить все бафф,Tüm buff'ı temizle,Cancella tutto il buff,Alle löschen der Buff,Hapus semua buff?,local result = Actor:clearAllBuff(objid)
40016,4,游戏Actor,clearAllBadBuff,Actor:clearAllBadBuff(objid),objid:number,ErrorCode.OK,清除所有负面buff,local result = Actor:clearAllBadBuff(objid),遊戲Actor,Game Actor,Actor เกม,Actor de Juego,Ator de Jogo,Acteur de jeu,新增待译0717,ゲームActor,게임 배우,Game Actor,Игра актера,Oyun Aktörü,Gioco Attore,Spiel Schauspieler,Aktor Game,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,Clear all the negative buff,清除所有負面buff,ลบdebuffทั้งหมด,Borrar todos los efectos negativos,Limpar todo o buff negativo,Effacer tout le buff négatif,新增待译0717,ネガティブbuffをすべてクリアする,모두 지우기 부정적인 버프,Xóa các buff xấu,Очистить весь негативный любитель,Tüm negatif buff'ı temizle,Cancella tutto il buff negativo,Alle löschen die negativen Buff,Hapus semua buff negative?,local result = Actor:clearAllBadBuff(objid)
40017,4,游戏Actor,getBuffList,Actor:getBuffList(objid),objid:number,"ErrorCode.OK, num:numberbuff数量, array:tablebuffid数组",获取buff列表,local result = Actor:getBuffList(objid),遊戲Actor,Game Actor,Actor เกม,Actor de Juego,Ator de Jogo,Acteur de jeu,新增待译0717,ゲームActor,게임 배우,Game Actor,Игра актера,Oyun Aktörü,Gioco Attore,Spiel Schauspieler,Aktor Game,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,Get the buff list,獲取buff列表,รับรายการbuff,Obtener la lista de buff,Obter a lista de buff,Obtenez la liste des buff,新增待译0717,buffリストを入手する,버프의 목록을 가져옵니다,Nhận danh sách buff,Получить список баффов,Buff listesini al,Ottenere l'elenco appassionato,Holen Sie sich die Buff-Liste,Dapat list buff,local result = Actor:getBuffList(objid)
40018,4,游戏Actor,getBuffLeftTick,"Actor:getBuffLeftTick(objid, buffid)","objid:number, buffid:number","ErrorCode.OK, ticks:number",获取当前index对应buff的剩余itck,"local result = Actor:getBuffLeftTick(objid, buffid)",遊戲Actor,Game Actor,Actor เกม,Actor de Juego,Ator de Jogo,Acteur de jeu,新增待译0717,ゲームActor,게임 배우,Game Actor,Игра актера,Oyun Aktörü,Gioco Attore,Spiel Schauspieler,Aktor Game,"objid:number, buffid:number","objid:number, buffid:number","objid:number, buffid:number","objid:number, buffid:number","objid:number, buffid:number","objid:number, buffid:number","objid:number, buffid:number","objid:number, buffid:number","objid:number, buffid:number","objid:number, buffid:number","objid:number, buffid:number","objid:number, buffid:number","objid:number, buffid:number","objid:number, buffid:number","objid:number, buffid:number",Get the remaining itck of corresponding buff of current index,獲取當前index對應buff的剩餘itck,รับitckที่เหลือของbuffที่สอดคล้องกันของindexปัจจุบัน,Obtenga el itck restante del buff correspondiente del índice actual.,Obtenha o itck restante do buff correspondente do índice atual,Obtenez le itck restante de buff correspondante de l'indice courant,新增待译0717,現在のindexの対応するbuffの残りのitckを取得します,현재 인덱스의 해당 버프의 남은 itck를 가져옵니다,Nhận phần còn lại của buff tương ứng index hiện tại,Получить оставшийся itck из соответствующих буфф текущего индекса,İlgili endeks tutkunu kalan kaşıntıyı alın,Ottenere l'ITCK rimanente corrispondente appassionato di indice corrente,Holen Sie sich das verbleibende ITCK von entsprechendem Buff des aktuellen Index,Dapatkan sisa itck dari buff yang terkait,"local result = Actor:getBuffLeftTick(objid, buffid)"
40019,4,游戏Actor,addHP,"Actor:addHP(objid, hp)","objid:number, hp:number",ErrorCode.OK,增加当前血量,"local result = Actor:addHP(objid, hp)",遊戲Actor,Game Actor,Actor เกม,Actor de Juego,Ator de Jogo,Acteur de jeu,新增待译0717,ゲームActor,게임 배우,Game Actor,Игра актера,Oyun Aktörü,Gioco Attore,Spiel Schauspieler,Aktor Game,"objid:number, hp:number","objid:number, hp:number","objid:number, hp:number","objid:number, hp:number","objid:number, hp:number","objid:number, hp:number","objid:number, hp:number","objid:number, hp:number","objid:number, hp:number","objid:number, hp:number","objid:number, hp:number","objid:number, hp:number","objid:number, hp:number","objid:number, hp:number","objid:number, hp:number",Increase current HP,增加當前血量,เพิ่มค่า HP ปัจจุบัน,Aumente HP actual,Aumentar HP atual,Augmenter HP courant,新增待译0717,現在のHPを増やす,현재 HP를 증가,Thêm máu,Увеличение тока HP,Mevcut HP'yi artır,Aumentare HP corrente,Erhöhen des aktuellen HP,Meningkatkan nyawa saat ini,"local result = Actor:addHP(objid, hp)"
40020,4,游戏Actor,getHP,Actor:getHP(objid),objid:number,"ErrorCode.OK, value:number",获取当前血量,local result = Actor:getHP(objid),遊戲Actor,Game Actor,Actor เกม,Actor de Juego,Ator de Jogo,Acteur de jeu,新增待译0717,ゲームActor,게임 배우,Game Actor,Игра актера,Oyun Aktörü,Gioco Attore,Spiel Schauspieler,Aktor Game,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,Get current HP,獲取當前血量,รับค่า HP ปัจจุบัน,Obtén el HP actual,Obtenha o HP atual,Obtenez HP en cours,新增待译0717,現在のHPを入手する,현재 HP 받기,Nhận máu,Получить текущий HP,Mevcut HP'yi al,Get HP corrente,Holen Sie sich aktuelle HP,Dapat nyawa,local result = Actor:getHP(objid)
40021,4,游戏Actor,getMaxHP,Actor:getMaxHP(objid),objid:number,"ErrorCode.OK, value:number",获取当前最大血量,local result = Actor:getMaxHP(objid),遊戲Actor,Game Actor,Actor เกม,Actor de Juego,Ator de Jogo,Acteur de jeu,新增待译0717,ゲームActor,게임 배우,Game Actor,Игра актера,Oyun Aktörü,Gioco Attore,Spiel Schauspieler,Aktor Game,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,Get current max HP,獲取當前最大血量,รับค่า HP สูงสุดปัจจุบัน,Obtener HP actual máximo,Obter o HP max atual,Obtenez HP actuelle max,新增待译0717,現在の最大HPを取得,현재 최대 HP를 가져옵니다,Nhận lượng máu nhiều nhất,Получить текущую максимальную HP,Geçerli maksimum HP'yi alın,Ottenere corrente massima di HP,Holen Sie sich aktuelle max HP,Dapat nyawa max,local result = Actor:getMaxHP(objid)
40022,4,游戏Actor,addOxygen,"Actor:addOxygen(objid, oxygen)","objid:number, oxygen:number",ErrorCode.OK,增加氧气值,"local result = Actor:addOxygen(objid, oxygen)",遊戲Actor,Game Actor,Actor เกม,Actor de Juego,Ator de Jogo,Acteur de jeu,新增待译0717,ゲームActor,게임 배우,Game Actor,Игра актера,Oyun Aktörü,Gioco Attore,Spiel Schauspieler,Aktor Game,"objid:number, oxygen:number","objid:number, oxygen:number","objid:number, oxygen:number","objid:number, oxygen:number","objid:number, oxygen:number","objid:number, oxygen:number","objid:number, oxygen:number","objid:number, oxygen:number","objid:number, oxygen:number","objid:number, oxygen:number","objid:number, oxygen:number","objid:number, oxygen:number","objid:number, oxygen:number","objid:number, oxygen:number","objid:number, oxygen:number",Increase oxygen volume,增加氧氣值,เพิ่มค่าออกซิเจน,Aumentar el volumen de oxigeno,Aumentar o volume de oxigênio,Augmenter le volume d'oxygène,新增待译0717,酸素量を増やす,산소의 양을 증가,Thêm oxy,Увеличение объема кислорода,Oksijen hacmini arttır,Aumentare il volume di ossigeno,Erhöhen Sauerstoffvolumen,Meningkatkan volume oksigen,"local result = Actor:addOxygen(objid, oxygen)"
40023,4,游戏Actor,getOxygen,Actor:getOxygen(objid),objid:number,"ErrorCode.OK, value:number",获取氧气值,local result = Actor:getOxygen(objid),遊戲Actor,Game Actor,Actor เกม,Actor de Juego,Ator de Jogo,Acteur de jeu,新增待译0717,ゲームActor,게임 배우,Game Actor,Игра актера,Oyun Aktörü,Gioco Attore,Spiel Schauspieler,Aktor Game,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,Get oxygen volume,獲取氧氣值,รับค่าออกซิเจน,Obtener volumen de oxigeno,Obter volume de oxigênio,Obtenez le volume d'oxygène,新增待译0717,酸素量を取得する,산소 볼륨을 얻기,Nhận oxy,Получить объем кислорода,Oksijen hacmini alın,Ottenere il volume di ossigeno,Erhalten Sauerstoffvolumen,Dapat volume oksigen,local result = Actor:getOxygen(objid)
40024,4,游戏Actor,addEnchant,"Actor:addEnchant(objid, slot, enchantId, enchantLevel)","objid:number, slot:number, enchantId:number, enchantLevel:number",ErrorCode.OK,增加相关装备的附魔,"local result = Actor:addEnchant(objid, slot, enchantId, enchantLevel)",遊戲Actor,Game Actor,Actor เกม,Actor de Juego,Ator de Jogo,Acteur de jeu,新增待译0717,ゲームActor,게임 배우,Game Actor,Игра актера,Oyun Aktörü,Gioco Attore,Spiel Schauspieler,Aktor Game,"objid:number, slot:number, enchantId:number, enchantLevel:number","objid:number, slot:number, enchantId:number, enchantLevel:number","objid:number, slot:number, enchantId:number, enchantLevel:number","objid:number, slot:number, enchantId:number, enchantLevel:number","objid:number, slot:number, enchantId:number, enchantLevel:number","objid:number, slot:number, enchantId:number, enchantLevel:number","objid:number, slot:number, enchantId:number, enchantLevel:number","objid:number, slot:number, enchantId:number, enchantLevel:number","objid:number, slot:number, enchantId:number, enchantLevel:number","objid:number, slot:number, enchantId:number, enchantLevel:number","objid:number, slot:number, enchantId:number, enchantLevel:number","objid:number, slot:number, enchantId:number, enchantLevel:number","objid:number, slot:number, enchantId:number, enchantLevel:number","objid:number, slot:number, enchantId:number, enchantLevel:number","objid:number, slot:number, enchantId:number, enchantLevel:number",Incease the enchantment of corresponding equipment,增加相關裝備的附魔,เพิ่มการร่ายมนตร์ของอุปกรณ์ที่เกี่ยวข้อง,Incrementa el encanto de los equipos correspondientes.,Aumentar o encanto do equipamento correspondente,Incease le charme de l'équipement correspondant,新增待译0717,対応する装備品の強化を追加する,해당 장치의 마법을 Incease,Thêm phụ ma liên quan,Incease очарования соответствующего оборудования,İlgili ekipmanın büyüsünü arttırın,Incease l'incanto delle attrezzature corrispondente,Incease die Zauber entsprechender Ausrüstung,Meningkatkan nilai pesona pada peralatan yang terkait,"local result = Actor:addEnchant(objid, slot, enchantId, enchantLevel)"
40025,4,游戏Actor,removeEnchant,"Actor:removeEnchant(objid, slot, enchantId)","objid:number, slot:number, enchantId:number",ErrorCode.OK,去掉相关装备的附魔,"local result = Actor:removeEnchant(objid, slot, enchantId)",遊戲Actor,Game Actor,Actor เกม,Actor de Juego,Ator de Jogo,Acteur de jeu,新增待译0717,ゲームActor,게임 배우,Game Actor,Игра актера,Oyun Aktörü,Gioco Attore,Spiel Schauspieler,Aktor Game,"objid:number, slot:number, enchantId:number","objid:number, slot:number, enchantId:number","objid:number, slot:number, enchantId:number","objid:number, slot:number, enchantId:number","objid:number, slot:number, enchantId:number","objid:number, slot:number, enchantId:number","objid:number, slot:number, enchantId:number","objid:number, slot:number, enchantId:number","objid:number, slot:number, enchantId:number","objid:number, slot:number, enchantId:number","objid:number, slot:number, enchantId:number","objid:number, slot:number, enchantId:number","objid:number, slot:number, enchantId:number","objid:number, slot:number, enchantId:number","objid:number, slot:number, enchantId:number",Clear enchantment of corresponding equipment,去掉相關裝備的附魔,ลบการร่ายมนตร์ของอุปกรณ์ที่เกี่ยวข้องออก,Encantamiento ligero de los equipos correspondientes.,Encantamento claro do equipamento correspondente,enchantement clair de l'équipement correspondant,新增待译0717,対応する装備品のenchantmentをクリアする。,해당 장비의 클리어 마법,Hủy các phụ ma liên quan,Очистить зачарование соответствующего оборудования,İlgili ekipmanın büyüsünü temizleyin,Chiara incanto delle attrezzature corrispondente,Klar Zauber der entsprechenden Ausrüstung,Hapus nilai pesona pada peralatan yang terkait,"local result = Actor:removeEnchant(objid, slot, enchantId)"
40026,4,游戏Actor,findNearestBlock,"Actor:findNearestBlock(objid, blockid, blockRange)","objid:number, blockid:number, blockRange","ErrorCode.OK, x|y|z:number",寻找距离最近方块id,"local result = Actor:findNearestBlock(objid, blockid, blockRange)",遊戲Actor,Game Actor,Actor เกม,Actor de Juego,Ator de Jogo,Acteur de jeu,新增待译0717,ゲームActor,게임 배우,Game Actor,Игра актера,Oyun Aktörü,Gioco Attore,Spiel Schauspieler,Aktor Game,"objid:number, blockid:number, blockRange","objid:number, blockid:number, blockRange","objid:number, blockid:number, blockRange","objid:number, blockid:number, blockRange","objid:number, blockid:number, blockRange","objid:number, blockid:number, blockRange","objid:number, blockid:number, blockRange","objid:number, blockid:number, blockRange","objid:number, blockid:number, blockRange","objid:number, blockid:number, blockRange","objid:number, blockid:number, blockRange","objid:number, blockid:number, blockRange","objid:number, blockid:number, blockRange","objid:number, blockid:number, blockRange","objid:number, blockid:number, blockRange",Find the nearest block's id,尋找距離最近方塊id,ค้นหาบล็อกidที่ใกล้ที่สุด,Encuentra el ID de bloque más cercano,Encontre o id do bloco mais próximo,Trouver tous les ID du bloc le plus proche,新增待译0717,最も近いブロックのIDを探す,가장 가까운 블록의 ID 찾기,Tìm id khối vuông gần nhất,Найти идентификатор ближайшего блока,En yakın bloğun kimliğini bulun,Trova id del blocco più vicino,Finden Sie den nächsten Block von id,Cari blok terdekat,"local result = Actor:findNearestBlock(objid, blockid, blockRange)"
40027,4,游戏Actor,setFaceYaw,"Actor:setFaceYaw(objid, yaw)","objid:number, yaw:number角度",ErrorCode.OK,设置actor视角横向偏移角度,"local result = Actor:setFaceYaw(objid, yaw)",遊戲Actor,Game Actor,Actor เกม,Actor de Juego,Ator de Jogo,Acteur de jeu,新增待译0717,ゲームActor,게임 배우,Game Actor,Игра актера,Oyun Aktörü,Gioco Attore,Spiel Schauspieler,Aktor Game,"objid:number, yaw:number angle","objid:number, yaw:number angle","objid:number, yaw:number angle","objid:number, yaw:number angle","objid:number, yaw:number angle","objid:number, yaw:number angle","objid:number, yaw:number angle","objid:number, yaw:number angle","objid:number, yaw:number angle","objid:number, yaw:number angle","objid:number, yaw:number angle","objid:number, yaw:number angle","objid:number, yaw:number angle","objid:number, yaw:number angle","objid:number, yaw:number angle",Set the crosswise deviation angle of actor's  view,設置actor視角橫向偏移角度,ตั้งค่ามุมเลื่อนที่แนวนอนของมุมมองactor,Establecer el ángulo de desviación transversal de la visión del actor.,Definir o ângulo de desvio transversal da visão do ator,Réglez l'angle de déviation en travers de la vue de l'acteur,新增待译0717,actorの視角の横方向の偏移を設定する,배우의보기의 횡 방향 편차 각도를 설정,Cài đặt góc lệch ngang actor theo hướng di chuyển ,Установите крестообразный угол отклонения зрения актера,Aktörün görüşünün çapraz sapma açısını ayarlayın,Impostare l'angolo di deviazione trasversale di vista dell'attore,Stellen Sie den queren Abweichungswinkel von Schauspielern Sicht,Atur sudut pandang vertikal pada aktor,"local result = Actor:setFaceYaw(objid, yaw)"
40028,4,游戏Actor,getFaceYaw,Actor:getFaceYaw(objid),objid:number,"ErrorCode.OK, yaw:number",获取actor视角横向偏移角度,local result = Actor:getFaceYaw(objid),遊戲Actor,Game Actor,Actor เกม,Actor de Juego,Ator de Jogo,Acteur de jeu,新增待译0717,ゲームActor,게임 배우,Game Actor,Игра актера,Oyun Aktörü,Gioco Attore,Spiel Schauspieler,Aktor Game,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,Get the crosswise deviation angle of actor's  view,獲取actor視角橫向偏移角度,รับมุมเลื่อนที่แนวนอนของมุมมองactor,Obtener el ángulo transversal de la visión del actor,Obter o ângulo de desvio transversal da visão do ator,Obtenez l'angle de déviation en travers de la vue de l'acteur,新增待译0717,actorの視角の横方向の偏移を受け取る。,배우의보기의 횡 방향 편차 각도를 얻기,Nhận góc ngang actor theo hướng di chuyển,Получить крестообразный угол отклонения зрения актера,Aktörün görünümünün çapraz sapma açısını alın,Ottenere l'angolo di deviazione trasversale di vista dell'attore,Holen Sie sich den queren Abweichungswinkel von Schauspielern Sicht,Dapat sudut pandang vertikal aktor,local result = Actor:getFaceYaw(objid)
40029,4,游戏Actor,turnFaceYaw,"Actor:turnFaceYaw(objid, offset)","objid:number, offset:number转动角度",ErrorCode.OK,转动actor横向偏移角度,"local result = Actor:turnFaceYaw(objid, offset)",遊戲Actor,Game Actor,Actor เกม,Actor de Juego,Ator de Jogo,Acteur de jeu,新增待译0717,ゲームActor,게임 배우,Game Actor,Игра актера,Oyun Aktörü,Gioco Attore,Spiel Schauspieler,Aktor Game,"objid:number, offset:number rotation angle","objid:number, offset:number rotation angle","objid:number, offset:number rotation angle","objid:number, offset:number rotation angle","objid:number, offset:number rotation angle","objid:number, offset:number rotation angle","objid:number, offset:number rotation angle","objid:number, offset:number rotation angle","objid:number, offset:number rotation angle","objid:number, offset:number rotation angle","objid:number, offset:number rotation angle","objid:number, offset:number rotation angle","objid:number, offset:number rotation angle","objid:number, offset:number rotation angle","objid:number, offset:number rotation angle",Change the crosswise deviation angle of actor,轉動actor橫向偏移角度,หมุนมุมเลื่อนที่แนวนอนของมุมมองactor,Cambiar el ángulo transversal del actor,Alterar o ângulo de desvio transversal do ator,Changer l'angle de déviation en travers de l'acteur,新增待译0717,actorの視角の横方向の偏移を回転させる。,배우의 횡 방향 편차 각도 변경,Đổi góc ngang actor theo hướng góc bù,Изменение крестообразного угла отклонения актера,Aktörün çapraz sapma açısını değiştirin,Modificare l'angolo di deviazione trasversale dell'attore,Ändern Sie den quer Abweichungswinkel von Schauspieler,Ubah sudut pandang vertikal pada aktor,"local result = Actor:turnFaceYaw(objid, offset)"
40030,4,游戏Actor,setFacePitch,"Actor:setFacePitch(objid, pitch)","objid:number, pitch:number仰望角度",ErrorCode.OK,设置actor视角仰望角度,"local result = Actor:setFacePitch(objid, pitch)",遊戲Actor,Game Actor,Actor เกม,Actor de Juego,Ator de Jogo,Acteur de jeu,新增待译0717,ゲームActor,게임 배우,Game Actor,Игра актера,Oyun Aktörü,Gioco Attore,Spiel Schauspieler,Aktor Game,"objid:number, pitch:number upward view","objid:number, pitch:number upward view","objid:number, pitch:number upward view","objid:number, pitch:number upward view","objid:number, pitch:number upward view","objid:number, pitch:number upward view","objid:number, pitch:number upward view","objid:number, pitch:number upward view","objid:number, pitch:number upward view","objid:number, pitch:number upward view","objid:number, pitch:number upward view","objid:number, pitch:number upward view","objid:number, pitch:number upward view","objid:number, pitch:number upward view","objid:number, pitch:number upward view",Set the upward angle of actor's view,設置actor視角仰望角度,ตั้งค่ามุมมองขึ้นของมุมมองactor,Establecer el ángulo ascendente de la visión del actor.,Definir o ângulo ascendente da visão do ator,Réglez l'angle vers le haut de la vue de l'acteur,新增待译0717,actorの視角の上向き角度を設定する。,배우의 뷰의 상승 각도를 설정,Cài đặt góc nhìn lên của actor,Установите вверх угол зрения актера,Oyuncu görünümünün yukarı açısını ayarlayın,Impostare l'angolo ascendente di vista dell'attore,Stellen Sie den Aufwärtswinkel von Schauspieler Sicht,Atur sudut tampilan pada aktor,"local result = Actor:setFacePitch(objid, pitch)"
40031,4,游戏Actor,getFacePitch,Actor:getFacePitch(objid),objid:number,"ErrorCode.OK, pitch:number",获取actor视角仰望角度,local result = Actor:getFacePitch(objid),遊戲Actor,Game Actor,Actor เกม,Actor de Juego,Ator de Jogo,Acteur de jeu,新增待译0717,ゲームActor,게임 배우,Game Actor,Игра актера,Oyun Aktörü,Gioco Attore,Spiel Schauspieler,Aktor Game,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,Get the upward angle of actor's view,獲取actor視角仰望角度,รับมุมมองขึ้นของมุมมองactor,Obtener el ángulo ascendente de la visión del actor.,Obter o ângulo ascendente da visão do ator,Obtenez l'angle vers le haut de la vue de l'acteur,新增待译0717,actorの視角の上向き角度を獲得する。,배우의 뷰의 상승 각도를 얻기,Nhận góc nhìn lên của actor,Получить вверх угол зрения актера,Oyuncu görünümünün yukarı açısını alın,Prendi l'angolo verso l'alto di vista dell'attore,Holen Sie sich das Aufwärtswinkel von Schauspieler Sicht,Dapat sudut tampilan aktor,local result = Actor:getFacePitch(objid)
40032,4,游戏Actor,turnFacePitch,"Actor:turnFacePitch(objid, offset)","objid:number, offset:number转动角度",ErrorCode.OK,转动actor仰望偏移角度,"local result = Actor:turnFacePitch(objid, offset)",遊戲Actor,Game Actor,Actor เกม,Actor de Juego,Ator de Jogo,Acteur de jeu,新增待译0717,ゲームActor,게임 배우,Game Actor,Игра актера,Oyun Aktörü,Gioco Attore,Spiel Schauspieler,Aktor Game,"objid:number, offset:number rotation angle","objid:number, offset:number rotation angle","objid:number, offset:number rotation angle","objid:number, offset:number rotation angle","objid:number, offset:number rotation angle","objid:number, offset:number rotation angle","objid:number, offset:number rotation angle","objid:number, offset:number rotation angle","objid:number, offset:number rotation angle","objid:number, offset:number rotation angle","objid:number, offset:number rotation angle","objid:number, offset:number rotation angle","objid:number, offset:number rotation angle","objid:number, offset:number rotation angle","objid:number, offset:number rotation angle",Change the upward deviation angle of actor,轉動actor仰望偏移角度,หมุนมุมมองขึ้นของมุมมองactor,Cambiar el ángulo de desviación hacia arriba del actor,Alterar o ângulo de desvio ascendente do ator,Changer l'angle de déviation vers le haut de l'acteur,新增待译0717,actorの視角の上向き角度を回転させる。,배우의 상향 편향 각을 변경,Đổi góc nhìn lên của actor,Изменение угла отклонения вверх актера,Aktörün yukarı doğru sapma açısını değiştirin,Modificare l'angolo di deviazione verso l'alto dell'attore,Ändern Sie die Abweichung nach oben Winkel von Schauspieler,Ubah sudut tampilan pada aktor,"local result = Actor:turnFacePitch(objid, offset)"
40033,4,游戏Actor,playBodyEffect,"Actor:playBodyEffect(objid, type)","objid:number, type:number特效类型ACTORBODY_EFFECT",ErrorCode.OK,播放特效,"local result = Actor:playBodyEffect(objid, type)",遊戲Actor,Game Actor,Actor เกม,Actor de Juego,Ator de Jogo,Acteur de jeu,新增待译0717,ゲームActor,게임 배우,Game Actor,Игра актера,Oyun Aktörü,Gioco Attore,Spiel Schauspieler,Aktor Game,"objid:number, type:number effect type ACTORBODY_EFFECT","objid:number, type:number effect type ACTORBODY_EFFECT","objid:number, type:number effect type ACTORBODY_EFFECT","objid:number, type:number effect type ACTORBODY_EFFECT","objid:number, type:number effect type ACTORBODY_EFFECT","objid:number, type:number effect type ACTORBODY_EFFECT","objid:number, type:number effect type ACTORBODY_EFFECT","objid:number, type:number effect type ACTORBODY_EFFECT","objid:number, type:number effect type ACTORBODY_EFFECT","objid:number, type:number effect type ACTORBODY_EFFECT","objid:number, type:number effect type ACTORBODY_EFFECT","objid:number, type:number effect type ACTORBODY_EFFECT","objid:number, type:number effect type ACTORBODY_EFFECT","objid:number, type:number effect type ACTORBODY_EFFECT","objid:number, type:number effect type ACTORBODY_EFFECT",Play the effects,播放特效,เล่นเอฟเฟ็กต์,Empieza los efectos,Comece os efeitos,Jouez les effets,新增待译0717,エフェクトを再生する,효과를 재생,Phát các hiệu ứng,Играть эффекты,Etkileri oyna,Gioca gli effetti,Spielen Sie die Effekte,Pakai efek,"local result = Actor:playBodyEffect(objid, type)"
40034,4,游戏Actor,stopBodyEffect,"Actor:stopBodyEffect(objid, type)","objid:number, type:number特效类型ACTORBODY_EFFECT",ErrorCode.OK,停止特效,"local result = Actor:stopBodyEffect(objid, type)",遊戲Actor,Game Actor,Actor เกม,Actor de Juego,Ator de Jogo,Acteur de jeu,新增待译0717,ゲームActor,게임 배우,Game Actor,Игра актера,Oyun Aktörü,Gioco Attore,Spiel Schauspieler,Aktor Game,"objid:number, type:number effect type ACTORBODY_EFFECT","objid:number, type:number effect type ACTORBODY_EFFECT","objid:number, type:number effect type ACTORBODY_EFFECT","objid:number, type:number effect type ACTORBODY_EFFECT","objid:number, type:number effect type ACTORBODY_EFFECT","objid:number, type:number effect type ACTORBODY_EFFECT","objid:number, type:number effect type ACTORBODY_EFFECT","objid:number, type:number effect type ACTORBODY_EFFECT","objid:number, type:number effect type ACTORBODY_EFFECT","objid:number, type:number effect type ACTORBODY_EFFECT","objid:number, type:number effect type ACTORBODY_EFFECT","objid:number, type:number effect type ACTORBODY_EFFECT","objid:number, type:number effect type ACTORBODY_EFFECT","objid:number, type:number effect type ACTORBODY_EFFECT","objid:number, type:number effect type ACTORBODY_EFFECT",Stop the effects,停止特效,หยุดเล่นเอฟเฟ็กต์,Detén los efectos,Pare os efeitos,Arrêtez les effets,新增待译0717,エフェクトを止める,효과를 중지,Dừng các hiệu ứng,Прекратить эффекты,Etkileri durdur,Fermare gli effetti,Stoppen Sie die Effekte,Hentikan efek,"local result = Actor:stopBodyEffect(objid, type)"
40035,4,游戏Actor,playBodyEffectByFile,"Actor:playBodyEffectByFile(objid, filename, sync)","objid:number, filename:string文件名, sync:bool同步",ErrorCode.OK,播放文件特效,"local result = Actor:playBodyEffectByFile(objid, filename, sync)",遊戲Actor,Game Actor,Actor เกม,Actor de Juego,Ator de Jogo,Acteur de jeu,新增待译0717,ゲームActor,게임 배우,Game Actor,Игра актера,Oyun Aktörü,Gioco Attore,Spiel Schauspieler,Aktor Game,"objid:number, filename:string file name, sync:bool synchronize","objid:number, filename:string file name, sync:bool synchronize","objid:number, filename:string file name, sync:bool synchronize","objid:number, filename:string file name, sync:bool synchronize","objid:number, filename:string file name, sync:bool synchronize","objid:number, filename:string file name, sync:bool synchronize","objid:number, filename:string file name, sync:bool synchronize","objid:number, filename:string file name, sync:bool synchronize","objid:number, filename:string file name, sync:bool synchronize","objid:number, filename:string file name, sync:bool synchronize","objid:number, filename:string file name, sync:bool synchronize","objid:number, filename:string file name, sync:bool synchronize","objid:number, filename:string file name, sync:bool synchronize","objid:number, filename:string file name, sync:bool synchronize","objid:number, filename:string file name, sync:bool synchronize",Play the file effects,播放文件特效,เล่นเอฟเฟ็กต์ของไฟล์,Reproducir efectos de archivo,Jogue os efeitos do arquivo,Jouez les effets de fichiers,新增待译0717,ファイル効果を再生する,파일 효과를 재생,Phát file hiệu ứng,Воспроизведение файлов эффектов,Dosya efektlerini oynat,Gioca gli effetti di file,Spielen Sie die Datei Effekte,Pakai file efek,"local result = Actor:playBodyEffectByFile(objid, filename, sync)"
40036,4,游戏Actor,stopBodyEffectByFile,"Actor:stopBodyEffectByFile(objid, filename)","objid:number, filename:string文件名",ErrorCode.OK,停止文件特效,"local result = Actor:stopBodyEffectByFile(objid, filename)",遊戲Actor,Game Actor,Actor เกม,Actor de Juego,Ator de Jogo,Acteur de jeu,新增待译0717,ゲームActor,게임 배우,Game Actor,Игра актера,Oyun Aktörü,Gioco Attore,Spiel Schauspieler,Aktor Game,"objid:number, filename:string file name","objid:number, filename:string file name","objid:number, filename:string file name","objid:number, filename:string file name","objid:number, filename:string file name","objid:number, filename:string file name","objid:number, filename:string file name","objid:number, filename:string file name","objid:number, filename:string file name","objid:number, filename:string file name","objid:number, filename:string file name","objid:number, filename:string file name","objid:number, filename:string file name","objid:number, filename:string file name","objid:number, filename:string file name",Stop the file effects,停止文件特效,หยุดเล่นเอฟเฟ็กต์ของไฟล์,Detener los efectos del archivo.,Pare os efeitos do arquivo,Arrêtez les effets de fichiers,新增待译0717,ファイル効果を停止する,파일 효과를 중지,Dừng file hiệu ứng,Прекратить эффекты файлов,Dosya efektlerini durdur,Fermare gli effetti di file,Stoppen Sie die Datei Effekte,Hentikan file efek,"local result = Actor:stopBodyEffectByFile(objid, filename)"
40037,4,游戏Actor,playSound,"Actor:playSound(objid, name, volume, pitch, flag)","objid:number, name:string, volume:number, pitch:number, flag:number",ErrorCode.OK,播放声音,"local result = Actor:playSound(objid, name, volume, pitch, flag)",遊戲Actor,Game Actor,Actor เกม,Actor de Juego,Ator de Jogo,Acteur de jeu,新增待译0717,ゲームActor,게임 배우,Game Actor,Игра актера,Oyun Aktörü,Gioco Attore,Spiel Schauspieler,Aktor Game,"objid:number, name:string, volume:number, pitch:number, flag:number","objid:number, name:string, volume:number, pitch:number, flag:number","objid:number, name:string, volume:number, pitch:number, flag:number","objid:number, name:string, volume:number, pitch:number, flag:number","objid:number, name:string, volume:number, pitch:number, flag:number","objid:number, name:string, volume:number, pitch:number, flag:number","objid:number, name:string, volume:number, pitch:number, flag:number","objid:number, name:string, volume:number, pitch:number, flag:number","objid:number, name:string, volume:number, pitch:number, flag:number","objid:number, name:string, volume:number, pitch:number, flag:number","objid:number, name:string, volume:number, pitch:number, flag:number","objid:number, name:string, volume:number, pitch:number, flag:number","objid:number, name:string, volume:number, pitch:number, flag:number","objid:number, name:string, volume:number, pitch:number, flag:number","objid:number, name:string, volume:number, pitch:number, flag:number",Play sound,播放聲音,เล่นเสียง,Tocar musica,Tocar música,Jouer son,新增待译0717,音を出す,소리 재생,Phát âm thanh,Воспроизведение звука,Sesi Oynat,Riprodurre l'audio,Ton abspielen,Suara,"local result = Actor:playSound(objid, name, volume, pitch, flag)"
40038,4,游戏Actor,playSoundSpecial,"Actor:playSoundSpecial(objid, name, type)","objid:number, name:string, type:number指定类型(GSOUND_TYPE)",ErrorCode.OK,播放声音(特定类型:GSOUND_TYPE),"local result = Actor:playSoundSpecial(objid, name, type)",遊戲Actor,Game Actor,Actor เกม,Actor de Juego,Ator de Jogo,Acteur de jeu,新增待译0717,ゲームActor,게임 배우,Game Actor,Игра актера,Oyun Aktörü,Gioco Attore,Spiel Schauspieler,Aktor Game,"objid:number, name:string, type:number designate name (GSOUND_TYPE)","objid:number, name:string, type:number designate name (GSOUND_TYPE)","objid:number, name:string, type:number designate name (GSOUND_TYPE)","objid:number, name:string, type:number designate name (GSOUND_TYPE)","objid:number, name:string, type:number designate name (GSOUND_TYPE)","objid:number, name:string, type:number designate name (GSOUND_TYPE)","objid:number, name:string, type:number designate name (GSOUND_TYPE)","objid:number, name:string, type:number designate name (GSOUND_TYPE)","objid:number, name:string, type:number designate name (GSOUND_TYPE)","objid:number, name:string, type:number designate name (GSOUND_TYPE)","objid:number, name:string, type:number designate name (GSOUND_TYPE)","objid:number, name:string, type:number designate name (GSOUND_TYPE)","objid:number, name:string, type:number designate name (GSOUND_TYPE)","objid:number, name:string, type:number designate name (GSOUND_TYPE)","objid:number, name:string, type:number designate name (GSOUND_TYPE)",Play sound(specific type:GSOUND_TYPE),播放聲音(特定類型:GSOUND_TYPE),เล่นเสียง(กำหนดประเภท:GSOUND_TYPE),Reproducir música (tipo específico: GSOUND_TYPE),Tocar Música(specific type:GSOUND_TYPE),Lire le son (type spécifique: GSOUND_TYPE),新增待译0717,音を鳴らす（特定タイプ：GSOUND_TYPE）,사운드 재생 (특정 유형 : GSOUND_TYPE),Phát âm thanh (Loại:GSOUND_TYPE),Воспроизведение звука (определенный тип: GSOUND_TYPE),Ses çal (belirli tür:GSOUND_TYPE),Riproduci suono (tipo specifico: GSOUND_TYPE),Wiedergabe starten (spezifische Aktivität: GSOUND_TYPE),Suara(specific type:GSOUND_TYPE),"local result = Actor:playSoundSpecial(objid, name, type)"
40039,4,游戏Actor,clearActorWithId,"Actor:clearActorWithId(actorid, bkill)","actorid:number, bkill:boolean",ErrorCode.OK,清除生物ID为actorid的生物,"local result = Actor:clearActorWithId(actorid, bkill)",遊戲Actor,Game Actor,Actor เกม,Actor de Juego,Ator de Jogo,Acteur de jeu,新增待译0717,ゲームActor,게임 배우,Game Actor,Игра актера,Oyun Aktörü,Gioco Attore,Spiel Schauspieler,Aktor Game,"actorid:number, bkill:boolean","actorid:number, bkill:boolean","actorid:number, bkill:boolean","actorid:number, bkill:boolean","actorid:number, bkill:boolean","actorid:number, bkill:boolean","actorid:number, bkill:boolean","actorid:number, bkill:boolean","actorid:number, bkill:boolean","actorid:number, bkill:boolean","actorid:number, bkill:boolean","actorid:number, bkill:boolean","actorid:number, bkill:boolean","actorid:number, bkill:boolean","actorid:number, bkill:boolean",Clear the creature's id whose id is actorid,清除生物ID為actorid的生物,ลบสิ่งมีชีวิตที่IDสิ่งมีชีวิตเป็นactorid,Borrar id de criatura cuya id es actorid,Limpar o id da criatura cujo id é o actorid,Effacer id de la créature dont l'identifiant est ActorID,新增待译0717,IDがactoridの生き物のIDをクリアする,그 ID가 actorid있는 생물의 ID를 취소합니다,Xóa id sinh vật với actorid của sinh vật,"Очистить идентификатор существ, чей идентификатор actorid",Kimliği kimliği kesik olan yaratığın kimliğini temizle,Cancellare id della creatura il cui ID è actorid,"Deaktivieren der Kreatur-ID, deren ID ist ActorID",Hapus ID makhluk yang bersifat actorid,"local result = Actor:clearActorWithId(actorid, bkill)"
40040,4,游戏Actor,setAttackType,"Actor:setAttackType(objid, attacktype)","objid:number, attacktype:number",ErrorCode.OK,设置伤害类型,"local result = Actor:setAttackType(objid, attacktype)",遊戲Actor,Game Actor,Actor เกม,Actor de Juego,Ator de Jogo,Acteur de jeu,新增待译0717,ゲームActor,게임 배우,Game Actor,Игра актера,Oyun Aktörü,Gioco Attore,Spiel Schauspieler,Aktor Game,"objid:number, attacktype:number","objid:number, attacktype:number","objid:number, attacktype:number","objid:number, attacktype:number","objid:number, attacktype:number","objid:number, attacktype:number","objid:number, attacktype:number","objid:number, attacktype:number","objid:number, attacktype:number","objid:number, attacktype:number","objid:number, attacktype:number","objid:number, attacktype:number","objid:number, attacktype:number","objid:number, attacktype:number","objid:number, attacktype:number",Set the damage type,設置傷害類型,ตั้งค่าประเภทดาเมจ,Establecer el tipo de daño,Defina o tipo de dano,Définissez le type de dégâts,新增待译0717,ダメージタイプを設定,피해 유형을 설정,Cài đặt các loại sát thương,Установите тип повреждения,Hasar tipini ayarlayın,Impostare il tipo di danni,Stellen Sie die Schadensart,Atur tipe cedera,"local result = Actor:setAttackType(objid, attacktype)"
40041,4,游戏Actor,setImmuneType,"Actor:setImmuneType(objid, immunetype, isadd)","objid:number, immunetype:number, isadd:number",ErrorCode.OK,设置免疫伤害类型,"local result = Actor:setImmuneType(objid, immunetype, isadd)",遊戲Actor,Game Actor,Actor เกม,Actor de Juego,Ator de Jogo,Acteur de jeu,新增待译0717,ゲームActor,게임 배우,Game Actor,Игра актера,Oyun Aktörü,Gioco Attore,Spiel Schauspieler,Aktor Game,"objid:number, immunetype:number, isadd:number","objid:number, immunetype:number, isadd:number","objid:number, immunetype:number, isadd:number","objid:number, immunetype:number, isadd:number","objid:number, immunetype:number, isadd:number","objid:number, immunetype:number, isadd:number","objid:number, immunetype:number, isadd:number","objid:number, immunetype:number, isadd:number","objid:number, immunetype:number, isadd:number","objid:number, immunetype:number, isadd:number","objid:number, immunetype:number, isadd:number","objid:number, immunetype:number, isadd:number","objid:number, immunetype:number, isadd:number","objid:number, immunetype:number, isadd:number","objid:number, immunetype:number, isadd:number",Set damage immunity type,設置免疫傷害類型,ตั้งค่าประเภทภูมิคุ้มกันดาเมจ,Definir el tipo de inmunidad al daño.,Defina o tipo de imunidade a danos,Régler le type d'immunité de dommages,新增待译0717,ダメージ耐性タイプを設定,설정 손상 내성 유형,Cài đặt miễn nhiễm sát thương,Установить повреждение типа иммунитета,Hasar bağışıklık tipini ayarla,Set danni tipo di immunità,Set Schaden Immunität Typ,Atur tipe kekebalan,"local result = Actor:setImmuneType(objid, immunetype, isadd)"
40042,4,游戏Actor,mountActor,"Actor:mountActor(objid, mountobjid, posindex)","objid:number, mountobjid:number, posindex:number骑乘位",ErrorCode.OK,登上、脱离载具,"local result = Actor:mountActor(objid, mountobjid, posindex)",遊戲Actor,Game Actor,Actor เกม,Actor de Juego,Ator de Jogo,Acteur de jeu,新增待译0717,ゲームActor,게임 배우,Game Actor,Игра актера,Oyun Aktörü,Gioco Attore,Spiel Schauspieler,Aktor Game,"objid:number, mountobjid:number, posindex:number ride","objid:number, mountobjid:number, posindex:number ride","objid:number, mountobjid:number, posindex:number ride","objid:number, mountobjid:number, posindex:number ride","objid:number, mountobjid:number, posindex:number ride","objid:number, mountobjid:number, posindex:number ride","objid:number, mountobjid:number, posindex:number ride","objid:number, mountobjid:number, posindex:number ride","objid:number, mountobjid:number, posindex:number ride","objid:number, mountobjid:number, posindex:number ride","objid:number, mountobjid:number, posindex:number ride","objid:number, mountobjid:number, posindex:number ride","objid:number, mountobjid:number, posindex:number ride","objid:number, mountobjid:number, posindex:number ride","objid:number, mountobjid:number, posindex:number ride","Get on, off the vehicle",登上、脫離載具,ขึ้น/ลงจากยานพาหนะ,"Entra, sal del vehículo.","Entrar, sair do veículo","Enfourchez, hors du véhicule",新增待译0717,乗り降りする,차량 떨어져에 얻기,"Lên xe, xuống xe","Получить, от автомобиля",Araçtan inin,"Get on, off veicolo","Holen Sie sich auf, aus dem Fahrzeug",Naik atau turun dari kendaraan,"local result = Actor:mountActor(objid, mountobjid, posindex)"
40043,4,游戏Actor,setActionAttrState,"Actor:setActionAttrState(objid, actionattr, switch)","objid:number, actionattr:numberPLAYERATTR, switch:boolean",ErrorCode.OK,设置生物行为属性状态,"local result = Actor:setActionAttrState(objid, actionattr, switch)",遊戲Actor,Game Actor,Actor เกม,Actor de Juego,Ator de Jogo,Acteur de jeu,新增待译0717,ゲームActor,게임 배우,Game Actor,Игра актера,Oyun Aktörü,Gioco Attore,Spiel Schauspieler,Aktor Game,"objid:number, actionattr:numberPLAYERATTR, switch:boolean","objid:number, actionattr:numberPLAYERATTR, switch:boolean","objid:number, actionattr:numberPLAYERATTR, switch:boolean","objid:number, actionattr:numberPLAYERATTR, switch:boolean","objid:number, actionattr:numberPLAYERATTR, switch:boolean","objid:number, actionattr:numberPLAYERATTR, switch:boolean","objid:number, actionattr:numberPLAYERATTR, switch:boolean","objid:number, actionattr:numberPLAYERATTR, switch:boolean","objid:number, actionattr:numberPLAYERATTR, switch:boolean","objid:number, actionattr:numberPLAYERATTR, switch:boolean","objid:number, actionattr:numberPLAYERATTR, switch:boolean","objid:number, actionattr:numberPLAYERATTR, switch:boolean","objid:number, actionattr:numberPLAYERATTR, switch:boolean","objid:number, actionattr:numberPLAYERATTR, switch:boolean","objid:number, actionattr:numberPLAYERATTR, switch:boolean","Set the behavior, attribute and state of creature",設置生物行為屬性狀態,ตั้งค่าสถานะลักษณะการกระทำของสิ่งมีชีวิต,"Definir comportamiento de criatura, atributo y estado","Definir o comportamento, atributo e estado da criatura","Définir le comportement, l'attribut et l'état de créature",新增待译0717,生き物の振る舞い、属性、状態を設定する,생물의 행동 특성 및 상태를 설정,"Cài đặt hành vi, thuộc tính và trạng thái của sinh vật","Установите поведение, атрибут и состояние существ","Davranışı, niteliği ve yaratığın durumunu ayarlayın","Impostare il comportamento, attributi e lo stato di creatura","Stellen Sie das Verhalten, Attribut und den Zustand der Kreatur","Atur perilaku, peralatan dan keadaan makhluk","local result = Actor:setActionAttrState(objid, actionattr, switch)"
40044,4,游戏Actor,tryNavigationToPos,"Actor:tryNavigationToPos(objid, x, y, z, cancontrol)","objid:number, x|y|z:number, cancontrol:boolean",ErrorCode.OK,寻路到目标位置,"local result = Actor:tryNavigationToPos(objid, x, y, z, cancontrol)",遊戲Actor,Game Actor,Actor เกม,Actor de Juego,Ator de Jogo,Acteur de jeu,新增待译0717,ゲームActor,게임 배우,Game Actor,Игра актера,Oyun Aktörü,Gioco Attore,Spiel Schauspieler,Aktor Game,"objid:number, x|y|z:number, cancontrol:boolean","objid:number, x|y|z:number, cancontrol:boolean","objid:number, x|y|z:number, cancontrol:boolean","objid:number, x|y|z:number, cancontrol:boolean","objid:number, x|y|z:number, cancontrol:boolean","objid:number, x|y|z:number, cancontrol:boolean","objid:number, x|y|z:number, cancontrol:boolean","objid:number, x|y|z:number, cancontrol:boolean","objid:number, x|y|z:number, cancontrol:boolean","objid:number, x|y|z:number, cancontrol:boolean","objid:number, x|y|z:number, cancontrol:boolean","objid:number, x|y|z:number, cancontrol:boolean","objid:number, x|y|z:number, cancontrol:boolean","objid:number, x|y|z:number, cancontrol:boolean","objid:number, x|y|z:number, cancontrol:boolean",Find the way to target location,尋路到目標位置,หาทางไปที่เป้าหมายตำแหน่ง,Encuentra la manera de apuntar la ubicación,Encontre o caminho para segmentar o local,Trouver le chemin vers la cible,新增待译0717,ターゲット場所への道を見つける,위치를 대상으로 방법을 찾아,Tìm đường tới vị trí mục tiêu,Найти путь к целевому местоположения,Konumu hedeflemenin yolunu bulun,Trovare il modo di indirizzare posizione,Finden Sie den Weg an den Zielort,Temukan cara untuk menargetkan lokasi,"local result = Actor:tryNavigationToPos(objid, x, y, z, cancontrol)"
40045,4,游戏Actor,getRidingActorObjId,Actor:getRidingActorObjId(objid),objid:number,ErrorCode.OK,获取骑乘生物的objid,local result = Actor:getRidingActorObjId(objid),遊戲Actor,Game Actor,Actor เกม,Actor de Juego,Ator de Jogo,Acteur de jeu,新增待译0717,ゲームActor,게임 배우,Game Actor,Игра актера,Oyun Aktörü,Gioco Attore,Spiel Schauspieler,Aktor Game,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,Get the Pferd's objid,獲取騎乘生物的objid,รับobjidของสิ่งมีชีวิตที่ขี่,Obtener objid de Pferd,Obtenha o Pferd's objid,Obtenez objid du Pferd,新增待译0717,乗る生き物のobjidを受取る,Pferd는의 objid를 받기,Nhận objib của sinh vật thú cưỡi,Получить ObjId в Пферд в,Pferd'in itirazını al,Prendi objid del Pferd,Holen Sie sich das Pferd des objid,Dapat berat Pferd,local result = Actor:getRidingActorObjId(objid)
50001,5,游戏玩家,getAttr,Player:getAttr(attrtype),"objid:number, atttype:number","ErrorCode.OK, value:number",nil,local result = Player:getAttr(attrtype),遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,"objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number",nil,nil,nil,nula,nil,néant,新增待译0717,nil,무,nil,ноль,nil,zero,Null,nil,local result = Player:getAttr(attrtype)
50002,5,游戏玩家,setAttr,"Player:setAttr(attrtype, val)","objid:number, val:number, atttype:number",ErrorCode.OK,nil,"local result = Player:setAttr(attrtype, val)",遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,"objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number",nil,nil,nil,nula,nil,néant,新增待译0717,nil,무,nil,ноль,nil,zero,Null,nil,"local result = Player:setAttr(attrtype, val)"
50003,5,游戏玩家,getHostUin,Player:getHostUin(),nil,"ErrorCode.OK, uin:number",获取房主,local result = Player:getHostUin(),遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,nil,nil,nil,nil,nil,nil,nil,nil,nil,nil,nil,nil,nil,nil,nil,Get the host,獲取房主,รับเจ้าของห้อง,Recibir el host,Receba o host,Obtenez l'hôte,新增待译0717,ホストを取得,호스트를 가져옵니다,Xem chủ phòng,Получить хост,Oda sahibi alın,Prendi l'host,Holen Sie sich das Host,Dapat tuan rumah,local result = Player:getHostUin()
50004,5,游戏玩家,isMainPlayer,Player:isMainPlayer(objid),objid:number,ErrorCode.OK是本地玩家是本地玩家,检测是否是本地玩家,local result = Player:isMainPlayer(objid),遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,Detect if it's local player,檢測是否是本地玩家,ตรวจสอบว่าใช่ผู้เล่นท้องถิ่นหรือไม่,Detecta si eres jugador local.,Detectar se é jogador local,Détecter s'il est joueur local,新增待译0717,地元のプレイヤーかどうかを検出する,이 지역의 플레이어가 있는지 감지,Kiểm tra xem liệu có phải là người chơi bản địa,"Обнаружить, если это местный игрок",Yerel oyuncu olup olmadığını tespit et,Rilevare se è giocatore locale,"Erkennen, ob es lokale Spieler",Test apakah ia pemain lokal,local result = Player:isMainPlayer(objid)
50005,5,游戏玩家,getMainPlayerUin,Player:getMainPlayerUin(),nil,"ErrorCode.OK, uin:number",获取本地玩家的uin,local result = Player:getMainPlayerUin(),遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,nil,nil,nil,nil,nil,nil,nil,nil,nil,nil,nil,nil,nil,nil,nil,Get the local player's uin,獲取本地玩家的uin,รับuinของผู้เล่นท้องถิ่น,Consigue jugadores locales en,Receba os jogadores locais em,Obtenez le uin du joueur local,新增待译0717,地元のプレイヤーのuinを受取る,로컬 플레이어의 UIN 받기,Xem uin của người chơi bản địa,Получить UIN местного игрока,Yerel oyuncunun uin'ini alın,Prendi uin del giocatore locale,Holen Sie sich das lokale Spielers uin,Dapat uin pemain lokal,local result = Player:getMainPlayerUin()
50006,5,游戏玩家,getGameResults,Player:getGameResults(objid),objid:number,"ErrorCode.OK, value:number",获取玩家比赛结果,local result = Player:getGameResults(objid),遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,Get result of the game,獲取玩家比賽結果,รับผลการแข่งขันของผู้เล่น,Obtener el resultado del juego,Obter o resultado do jogo,Obtenir le résultat du jeu,新增待译0717,ゲームの結果を入手,게임의 결과를 얻을 수,Xem kết quả thi đấu của người chơi,Получить результат игры,Oyunun sonucunu al,Prendi risultato del gioco,Erhalten Ergebnis des Spiels,Lihat hasil permainan,local result = Player:getGameResults(objid)
50007,5,游戏玩家,setGameResults,"Player:setGameResults(objid, result)","objid:number, result",ErrorCode.OK,设置玩家比赛结果,"local result = Player:setGameResults(objid, result)",遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,"objid:number, result","objid:number, result","objid:number, result","objid:number, result","objid:number, result","objid:number, result","objid:number, result","objid:number, result","objid:number, result","objid:number, result","objid:number, result","objid:number, result","objid:number, result","objid:number, result","objid:number, result",Set result of the game,設置玩家比賽結果,ตั้งค่าผลการแข่งขันของผู้เล่น,Establecer el resultado del partido,Definir resultado do jogo,Définir résultat du jeu,新增待译0717,ゲームの設定結果,게임의 설정 결과,Cài đặt kết quả thi đấu của người chơi,Установить результат игры,Oyunun sonucunu ayarla,Impostare risultato del gioco,Set Ergebnis des Spiels,Atur hasil permainan,"local result = Player:setGameResults(objid, result)"
50008,5,游戏玩家,getGameScore,Player:getGameScore(objid),objid:number,"ErrorCode.OK, value:number",获取玩家游戏分数,local result = Player:getGameScore(objid),遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,Get score of the game,獲取玩家遊戲分數,รับคะแนนของผู้เล่น,Obtener puntuación del juego,Obter pontuação do jogo,Obtenir un meilleur score du jeu,新增待译0717,ゲームの得点を取得する,게임의 점수를 얻을,Xem điểm của người chơi,Получить счет игры,Oyunun puanını alın,Ottenere il punteggio del gioco,Erhalten Punktzahl des Spiels,Lihat jumlah skor permainan,local result = Player:getGameScore(objid)
50009,5,游戏玩家,setGameScore,"Player:setGameScore(objid, score)",objid:number,"ErrorCode.OK, value:number",设置玩家游戏分数,"local result = Player:setGameScore(objid, score)",遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,Set score of the game,設置玩家遊戲分數,ตั้งค่าคะแนนของผู้เล่น,Establecer puntuación del juego,Definir pontuação do jogo,Set score de jeu,新增待译0717,ゲームのスコアを設定する,게임의 설정 점수,Cài đặt điểm của người chơi,Установить счет игры,Oyunun puanını ayarla,Set punteggio del gioco,Set-Score des Spiels,Atur jumlah skor permainan,"local result = Player:setGameScore(objid, score)"
50010,5,游戏玩家,getGameRanking,Player:getGameRanking(objid),objid:number,"ErrorCode.OK, rank:number",获取玩家的排行,local result = Player:getGameRanking(objid),遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,Get the rank of player,獲取玩家的排行,รับอันดับของผู้เล่น,Obtener puntuación de jugador,Obter a classificação do jogador,Obtenez le rang de joueur,新增待译0717,プレイヤーのランクを取得,플레이어의 순위를 가져 오기,Xem xếp hạng của người chơi,"Получить звание игрока,",Oyuncunun rütbesini al,Prendi il rango di giocatore,Holen Sie sich den Rang des Spielers,Lihat ranking pemain,local result = Player:getGameRanking(objid)
50011,5,游戏玩家,setGameRanking,"Player:setGameRanking(objid, rank)","objid:number, rank:number",ErrorCode.OK,给玩家设置排行,"local result = Player:setGameRanking(objid, rank)",遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,"objid:number, rank:number","objid:number, rank:number","objid:number, rank:number","objid:number, rank:number","objid:number, rank:number","objid:number, rank:number","objid:number, rank:number","objid:number, rank:number","objid:number, rank:number","objid:number, rank:number","objid:number, rank:number","objid:number, rank:number","objid:number, rank:number","objid:number, rank:number","objid:number, rank:number",Set the rank of player,給玩家設置排行,จัดอันดับให้กับผู้เล่น,Establecer puntuación de jugador,Defina a classificação do jogador,Réglez le rang de joueur,新增待译0717,プレイヤーのランクを設定,플레이어의 순위를 설정,Cài đặt xếp hạng của người chơi,"Установите ранг игрока,",Oyuncunun rütbesini ayarla,Impostare il rango di giocatore,Stellen Sie den Rang des Spielers,Atur ranking pemain,"local result = Player:setGameRanking(objid, rank)"
50012,5,游戏玩家,gainItems,"Player:gainItems(objid, itemid, num, prioritytype)","objid:number, itemid:number, num:number, prioritytype:number(1是优先快捷栏2是优先背包栏)",ErrorCode.OK,给玩家增加道具,"local result = Player:gainItems(objid, itemid, num, prioritytype)",遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,"objid:number, itemid:number, num:number, prioritytype:number(1 is shortcut bar prior 2 is backpack bar prior)","objid:number, itemid:number, num:number, prioritytype:number(1 is shortcut bar prior 2 is backpack bar prior)","objid:number, itemid:number, num:number, prioritytype:number(1 is shortcut bar prior 2 is backpack bar prior)","objid:number, itemid:number, num:number, prioritytype:number(1 is shortcut bar prior 2 is backpack bar prior)","objid:number, itemid:number, num:number, prioritytype:number(1 is shortcut bar prior 2 is backpack bar prior)","objid:number, itemid:number, num:number, prioritytype:number(1 is shortcut bar prior 2 is backpack bar prior)","objid:number, itemid:number, num:number, prioritytype:number(1 is shortcut bar prior 2 is backpack bar prior)","objid:number, itemid:number, num:number, prioritytype:number(1 is shortcut bar prior 2 is backpack bar prior)","objid:number, itemid:number, num:number, prioritytype:number(1 is shortcut bar prior 2 is backpack bar prior)","objid:number, itemid:number, num:number, prioritytype:number(1 is shortcut bar prior 2 is backpack bar prior)","objid:number, itemid:number, num:number, prioritytype:number(1 is shortcut bar prior 2 is backpack bar prior)","objid:number, itemid:number, num:number, prioritytype:number(1 is shortcut bar prior 2 is backpack bar prior)","objid:number, itemid:number, num:number, prioritytype:number(1 is shortcut bar prior 2 is backpack bar prior)","objid:number, itemid:number, num:number, prioritytype:number(1 is shortcut bar prior 2 is backpack bar prior)","objid:number, itemid:number, num:number, prioritytype:number(1 is shortcut bar prior 2 is backpack bar prior)",Add items for players,給玩家增加道具,เพิ่มไอเทมให้กับผู้เล่น,Añadir objetos a los jugadores.,Adicionar itens para jogadores,Ajouter des articles pour les joueurs,新增待译0717,プレーヤー用のアイテムを追加する,선수에 대한 항목 추가,Thêm công cụ cho người chơi,Добавление элементов для игроков,Oyuncular için öğe ekle,Aggiungere elementi per i giocatori,Hinzufügen von Elementen für die Spieler,Tambahkan item utk pemain,"local result = Player:gainItems(objid, itemid, num, prioritytype)"
50013,5,游戏玩家,teleportHome,Player:teleportHome(objid),objid:number,ErrorCode.OK,传送到出生点,local result = Player:teleportHome(objid),遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,Transfer to spawn point,傳送到出生點,วาร์ปไปยังจุดเกิด,Transferencia a punto de espirales,Transferir para o ponto de spaw,Transfert à frayer le point,新增待译0717,出生地に移動する,포인트를 생성하기 위해 이동,Chuyển đến điểm bắt đầu,Перевести на нерест точки,Yumurtlama noktasına transfer,Trasferimento a deporre le uova punto,Übergabepunkt zum Laichen,Transfer ke titik bangkit,local result = Player:teleportHome(objid)
50014,5,游戏玩家,getCurToolID,Player:getCurToolID(objid),objid:number,"ErrorCode.OK, ret",获取玩家当前手持的物品id,local result = Player:getCurToolID(objid),遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,Get the item id currently held by the player,獲取玩家當前手持的物品id,รับidไอเทมที่ผู้เล่นถืออยู่,Obtener identificación de,Obter o id de,Obtenez l'ID d'article actuellement détenu par le joueur,新增待译0717,現在プレーヤーが持っているアイテムIDを取得します,항목 ID는 현재 플레이어가 보유한하기,Nhận id vật phẩm hiện tại của người chơi,Получить идентификатор элемента в настоящее время проводится игрок,Kimliğini al,Prendete l'oggetto id attualmente tenuto dal giocatore,Holen Sie sich das Element id zur Zeit vom Spieler gehalten,Lihat ID item pemain,local result = Player:getCurToolID(objid)
50015,5,游戏玩家,getNickname,Player:getNickname(objid),objid:number,"ErrorCode.OK, name:string",获取玩家昵称,local result = Player:getNickname(objid),遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,Get the player's name,獲取玩家暱稱,รับชื่อของผู้เล่น,Obtiene la ID del elemento que ocupa actualmente el jugador.,Obtém o ID do item atualmente ocupado pelo jogador,Obtenez le nom du joueur,新增待译0717,プレイヤーの名前を取得,플레이어의 이름을 가져옵니다,Xem tên của người chơi,Получить имя игрока,Şu anda oynatıcı tarafından tutulan öğe kimliğini al,Ottenere il nome del giocatore,Holen Sie den Namen des Spielers,Lihat nama pemain,local result = Player:getNickname(objid)
50016,5,游戏玩家,removeBackpackItem,"Player:removeBackpackItem(objid, itemid, num)","objid:number, itemid:number, num:number",ErrorCode.OK,移走背包里的物品,"local result = Player:removeBackpackItem(objid, itemid, num)",遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,"objid:number, itemid:number, num:number","objid:number, itemid:number, num:number","objid:number, itemid:number, num:number","objid:number, itemid:number, num:number","objid:number, itemid:number, num:number","objid:number, itemid:number, num:number","objid:number, itemid:number, num:number","objid:number, itemid:number, num:number","objid:number, itemid:number, num:number","objid:number, itemid:number, num:number","objid:number, itemid:number, num:number","objid:number, itemid:number, num:number","objid:number, itemid:number, num:number","objid:number, itemid:number, num:number","objid:number, itemid:number, num:number",Remove items in backpack,移走背包裡的物品,ลบไอเทมในเป้ออก,Quitar los artículos en la mochila.,Remover itens na mochila,Supprimer des éléments dans sac à dos,新增待译0717,バックパックの中身を取り除く,배낭에서 항목을 삭제,Di chuyển vật phẩm trong túi,Удалить элементы в рюкзаке,Sırt çantasındaki eşyaları çıkarın,Rimuovi elementi nello zaino,Entfernen Sie Gegenstände im Rucksack,Pindahkan item ke tas,"local result = Player:removeBackpackItem(objid, itemid, num)"
50017,5,游戏玩家,getDieTimes,Player:getDieTimes(objid),objid:number,"ErrorCode.OK, value:number",获取玩家死亡次数,local result = Player:getDieTimes(objid),遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,Get the number of deaths,獲取玩家死亡次數,รับจำนวนครั้งที่ตายของผู้เล่น,Obtener el número de muertos,Obter o número de mortes,Obtenez le nombre de décès,新增待译0717,死亡数を取得する,사망자 수를 가져옵니다,Xem số lần chết của người chơi,Получить количество смертей,Ölüm sayısını al,Prendi il numero di morti,Holen die Zahl der Todesfälle,Lihat jumlah angka kematian,local result = Player:getDieTimes(objid)
50018,5,游戏玩家,getLeftLifeNum,Player:getLeftLifeNum(objid),objid:number,"ErrorCode.OK, value:number",获取玩家剩下的生命次数,local result = Player:getLeftLifeNum(objid),遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,Get the number of remaining lives left,獲取玩家剩下的生命次數,รับจำนวนชีวิตที่เหลืออยู่ของผู้เล่น,Consigue el número de vidas que quedan,Obter o número de vidas restantes,Obtenez le nombre de vies restantes à gauche,新增待译0717,残りの生命回数を取得します,삶 왼쪽 남아의 수를 가져옵니다,Xem số mạng còn lại của người chơi,Получить количество оставшихся жизней остались,Kalan can sayısını azalt,Prendi il numero di vite rimanenti lasciati,Holen Sie die Anzahl der verbleibenden Leben gelassen,Lihat jumlah nyawa yang tersisa,local result = Player:getLeftLifeNum(objid)
50019,5,游戏玩家,setTeam,"Player:setTeam(objid, team)","objid:number, teamid:number",ErrorCode.OK,设置生物队伍,"local result = Player:setTeam(objid, team)",遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,"objid:number, teamid:number","objid:number, teamid:number","objid:number, teamid:number","objid:number, teamid:number","objid:number, teamid:number","objid:number, teamid:number","objid:number, teamid:number","objid:number, teamid:number","objid:number, teamid:number","objid:number, teamid:number","objid:number, teamid:number","objid:number, teamid:number","objid:number, teamid:number","objid:number, teamid:number","objid:number, teamid:number",Set the creature team,設置生物隊伍,ตั้งค่าทีมของผู้เล่น,Establece el equipo de criaturas.,Defina a equipe de criaturas,Définir l'équipe de créature,新增待译0717,生き物のチームを設定する,생물 팀을 설정,Cài đặt nhóm sinh vật,Установите команду существ,Yaratık ekibini ayarla,Impostare la squadra creatura,Stellen Sie die Kreatur Team,Atur team makhluk,"local result = Player:setTeam(objid, team)"
50020,5,游戏玩家,getTeam,Player:getTeam(objid),objid:number,"ErrorCode.OK, teamid:number",获取生物队伍,local result = Player:getTeam(objid),遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,Get the creature team,獲取生物隊伍,รับทิีมของผู้เล่น,Consigue equipo de criaturas.,Obter a equipe de criaturas,Obtenez l'équipe de créature,新增待译0717,生き物のチームを設定する,생물 팀을 얻으십시오,Xem nhóm sinh vật,Получить команду существо,Yaratık ekibini al,Prendi il team di creatura,Holen Sie sich das Wesen Team,Dapat team makhluk,local result = Player:getTeam(objid)
50021,5,游戏玩家,getFoodLevel,Player:getFoodLevel(objid),objid:number,ErrorCode.OK,获取当前饱食度,local result = Player:getFoodLevel(objid),遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,Get the current satiety,獲取當前飽食度,รับค่าความหิวปัจจุบัน,Consigue la saciedad actual,Obter a saciedade atual,Obtenez le courant satiété,新增待译0717,現在の満腹感を受取る,현재 포만감을 얻을,Xem độ đói hiện tại,Получить текущую сытость,Mevcut doygunluğu alın,Prendi il sazietà corrente,Holen Sie sich das aktuelle Sättigungs,Cek tingkat kelaparan saat ini,local result = Player:getFoodLevel(objid)
50022,5,游戏玩家,setFoodLevel,"Player:setFoodLevel(objid, foodLevel)","objid:number, foodLevel:number",ErrorCode.OK,设置当前饱食度,"local result = Player:setFoodLevel(objid, foodLevel)",遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,"objid:number, foodLevel:number","objid:number, foodLevel:number","objid:number, foodLevel:number","objid:number, foodLevel:number","objid:number, foodLevel:number","objid:number, foodLevel:number","objid:number, foodLevel:number","objid:number, foodLevel:number","objid:number, foodLevel:number","objid:number, foodLevel:number","objid:number, foodLevel:number","objid:number, foodLevel:number","objid:number, foodLevel:number","objid:number, foodLevel:number","objid:number, foodLevel:number",Set the current satiety,設置當前飽食度,ตั้งค่าค่าความหิวปัจจุบัน,Definir la saciedad actual.,Definir a saciedade atual,Réglez le courant satiété,新增待译0717,現在の満腹感を設定する,현재 포만감을 설정,Cài đặt độ đói hiện tại,Установите текущую сытость,Geçerli doygunluğu ayarla,Impostare la sazietà corrente,Stellen Sie die aktuelle Sättigung,Atur tingkat kelaparan,"local result = Player:setFoodLevel(objid, foodLevel)"
50023,5,游戏玩家,getCurShotcut,Player:getCurShotcut(objid),objid:number,"ErrorCode.OK, scutIdx:number",获取当前所用快捷栏键,local result = Player:getCurShotcut(objid),遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,Get the current shortcut bar,獲取當前所用快捷欄鍵,รับปุ่มแถบไอเทมที่ใช้อยู่ในปัจจุบัน,Obtener barra de acceso directo actual,Obter a barra de atalhos atual,Obtenez la barre de raccourcis en cours,新增待译0717,現在のショートカットバーを取得する,현재 바로 가기 표시 줄을 받기,Xem thanh phím tắt hiện tại,Получить текущую панель быстрого доступа,Geçerli kısayol çubuğunu alın,Prendi la barra di collegamento corrente,Holen Sie sich die aktuelle Shortcut-Leiste,Lihat shortcut bar saat ini,local result = Player:getCurShotcut(objid)
50024,5,游戏玩家,onCurToolUsed,"Player:onCurToolUsed(objid, num)","objid:number, num:number道具数量",ErrorCode.OK,设置当前玩家手持的道具消耗,"local result = Player:onCurToolUsed(objid, num)",遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,"objid:number, num:number items number","objid:number, num:number items number","objid:number, num:number items number","objid:number, num:number items number","objid:number, num:number items number","objid:number, num:number items number","objid:number, num:number items number","objid:number, num:number items number","objid:number, num:number items number","objid:number, num:number items number","objid:number, num:number items number","objid:number, num:number items number","objid:number, num:number items number","objid:number, num:number items number","objid:number, num:number items number",Set the consumption of item held by player,設置當前玩家手持的道具消耗,ตั้งค่าการใช้ไอเท็มที่ถืออยู่ในมือผู้เล่นปัจจุบัน,Define el consumo de elementos retenidos por el jugador.,Definir o consumo de item retido pelo jogador,Régler la consommation du produit détenu par joueur,新增待译0717,プレイヤーが持っているアイテムの消耗を設定します,플레이어가 보유 항목의 소비를 설정,Đặt mức tiêu thụ vật phẩm đang cầm trên tay của người chơi ,Установите потребление пункта проводится игроком,Oyuncu tarafından tutulan öğenin tüketimini ayarla,Impostare il consumo di prodotto detenuto da giocatore,Stellen Sie den Verbrauch von Artikel durch Spieler gehalten,Atur pemakaian item oleh pemain,"local result = Player:onCurToolUsed(objid, num)"
50025,5,游戏玩家,setSkillCD,"Player:setSkillCD(objid, itemid, cd)","objid:number, itemid:number, cd:number",ErrorCode.OK,设置CD,"local result = Player:setSkillCD(objid, itemid, cd)",遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,"objid:number, itemid:number, cd:number","objid:number, itemid:number, cd:number","objid:number, itemid:number, cd:number","objid:number, itemid:number, cd:number","objid:number, itemid:number, cd:number","objid:number, itemid:number, cd:number","objid:number, itemid:number, cd:number","objid:number, itemid:number, cd:number","objid:number, itemid:number, cd:number","objid:number, itemid:number, cd:number","objid:number, itemid:number, cd:number","objid:number, itemid:number, cd:number","objid:number, itemid:number, cd:number","objid:number, itemid:number, cd:number","objid:number, itemid:number, cd:number",Set CD,設置CD,ตั้งค่า CD,Conjunto de CD,Conjunto de CD,Set CD,新增待译0717,CD設定,설정 CD,Cài đặt CD,Набор CD,CD'yi ayarla,set CD,Legen Sie CD,Set CD,"local result = Player:setSkillCD(objid, itemid, cd)"
50026,5,游戏玩家,reviveToPos,"Player:reviveToPos(objid, x, y, z)","objid:number, x|y|z:number",ErrorCode.OK,复活玩家到指定点,"local result = Player:reviveToPos(objid, x, y, z)",遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,"objid:number, x|y|z:number","objid:number, x|y|z:number","objid:number, x|y|z:number","objid:number, x|y|z:number","objid:number, x|y|z:number","objid:number, x|y|z:number","objid:number, x|y|z:number","objid:number, x|y|z:number","objid:number, x|y|z:number","objid:number, x|y|z:number","objid:number, x|y|z:number","objid:number, x|y|z:number","objid:number, x|y|z:number","objid:number, x|y|z:number","objid:number, x|y|z:number",Respawn player at designated point,復活玩家到指定點,ผู้เล่นคืนชีพไปยังจุดที่กำหนด,Jugador de reaparición en el punto designado,Respawn player no ponto designado,joueur respawn au point désigné,新增待译0717,指定された時点でプレイヤーを再出現させる,지정된 지점에서 리스폰 플레이어,Hồi sinh về vị trí mặc định,Respawn игрок в назначенном месте,Respawn oyuncu belirtilen noktada,giocatore Respawn a punto designato,Respawn Spieler an bestimmten Punkt,Atur pemain bangkit pada titik yang ditentukan,"local result = Player:reviveToPos(objid, x, y, z)"
50027,5,游戏玩家,setRevivePoint,"Player:setRevivePoint(objid, x, y, z)","objid:number, x|y|z:number",ErrorCode.OK,改变玩家复活点位置,"local result = Player:setRevivePoint(objid, x, y, z)",遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,"objid:number, x|y|z:number","objid:number, x|y|z:number","objid:number, x|y|z:number","objid:number, x|y|z:number","objid:number, x|y|z:number","objid:number, x|y|z:number","objid:number, x|y|z:number","objid:number, x|y|z:number","objid:number, x|y|z:number","objid:number, x|y|z:number","objid:number, x|y|z:number","objid:number, x|y|z:number","objid:number, x|y|z:number","objid:number, x|y|z:number","objid:number, x|y|z:number",Change the respawn point ,改變玩家復活點位置,เปลี่ยนตำแหน่งจุดคืนชีพของผู้เล่น,Cambia el punto de reaparición,Mude o ponto de respawn,Modifier le point de respawn,新增待译0717,出生地を変更する,다시 생성 지점 변경,Chỉnh sửa vị trí hồi sinh của người chơi,Изменение точки респауна,Respawn noktasını değiştir,Cambiare il punto di respawn,Ändern Sie den Respawn-Punkt,Ganti titik bangkit,"local result = Player:setRevivePoint(objid, x, y, z)"
50028,5,游戏玩家,playAct,"Player:playAct(objid, actid)","objid:number, actid:number动作id",ErrorCode.OK,玩家播放动画,"local result = Player:playAct(objid, actid)",遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,"objid:number, actid:number动作id","objid:number, actid:number动作id","objid:number, actid:number动作id","objid:number, actid:number动作id","objid:number, actid:number动作id","objid:number, actid:number动作id","objid:number, actid:number动作id","objid:number, actid:number动作id","objid:number, actid:number动作id","objid:number, actid:number动作id","objid:number, actid:number动作id","objid:number, actid:number动作id","objid:number, actid:number动作id","objid:number, actid:number动作id","objid:number, actid:number动作id",Player play the cartoon,玩家播放動畫,ผู้เล่นเล่นท่าทาง,Jugador comenzó dibujos animados,Jogador iniciou o desenho animado,Joueur joue le dessin animé,新增待译0717,プレイヤーは漫画を再生する,플레이어는 만화를 재생,Người chơi phát hoạt hình,Игрок играет мультфильм,Oyuncu çizgi film oynamak,Giocatore di giocare il cartone animato,Spieler spielen die Karikatur,Pemain memainkan kartun,"local result = Player:playAct(objid, actid)"
50029,5,游戏玩家,notifyGameInfo2Self,"Player:notifyGameInfo2Self(objid, info)","objid:number, info:string文字内容",ErrorCode.OK,对玩家显示飘窗文字,"local result = Player:notifyGameInfo2Self(objid, info)",遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,"objid:number, info:string text content","objid:number, info:string text content","objid:number, info:string text content","objid:number, info:string text content","objid:number, info:string text content","objid:number, info:string text content","objid:number, info:string text content","objid:number, info:string text content","objid:number, info:string text content","objid:number, info:string text content","objid:number, info:string text content","objid:number, info:string text content","objid:number, info:string text content","objid:number, info:string text content","objid:number, info:string text content",Display texts in popup window,對玩家顯示飄窗文字,แสดงหน้าต่างข้อความให้ผู้เล่น,Mostrar textos en ventana emergente,Exibir textos na janela pop-up,Les textes affichés dans la fenêtre pop-up,新增待译0717,ポップアップウィンドウにあるテキストを表示する,팝업 창에 표시 텍스트,Hiển thị văn bản của người chơi trong cửa sổ,Показать тексты в всплывающем окне,Metinleri açılır pencerede görüntüle,Mostra testi in finestra pop-up,Anzeigetexte in Popup-Fenster,Tampilkan teks di pop up window,"local result = Player:notifyGameInfo2Self(objid, info)"
50030,5,游戏玩家,useItem,"Player:useItem(objid, itemid, status, onshift)","objid:number, itemid:number, status:number使用状态, onshift:boolean是否按下shift键",ErrorCode.OK,使玩家使用当前道具,"local result = Player:useItem(objid, itemid, status, onshift)",遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,"objid:number, itemid:number, status:number state of using, onshift:boolean whether to press shift","objid:number, itemid:number, status:number state of using, onshift:boolean whether to press shift","objid:number, itemid:number, status:number state of using, onshift:boolean whether to press shift","objid:number, itemid:number, status:number state of using, onshift:boolean whether to press shift","objid:number, itemid:number, status:number state of using, onshift:boolean whether to press shift","objid:number, itemid:number, status:number state of using, onshift:boolean whether to press shift","objid:number, itemid:number, status:number state of using, onshift:boolean whether to press shift","objid:number, itemid:number, status:number state of using, onshift:boolean whether to press shift","objid:number, itemid:number, status:number state of using, onshift:boolean whether to press shift","objid:number, itemid:number, status:number state of using, onshift:boolean whether to press shift","objid:number, itemid:number, status:number state of using, onshift:boolean whether to press shift","objid:number, itemid:number, status:number state of using, onshift:boolean whether to press shift","objid:number, itemid:number, status:number state of using, onshift:boolean whether to press shift","objid:number, itemid:number, status:number state of using, onshift:boolean whether to press shift","objid:number, itemid:number, status:number state of using, onshift:boolean whether to press shift",Make player use current item,使玩家使用當前道具,ทำให้ผู้เล่นใช้ไอเทมปัจจุบัน,Hacer que el jugador use el objeto actual,Faça o jogador usar o item atual,Assurez-vous joueur en utilisant l'élément courant,新增待译0717,プレイヤーに現在のアイテムを使用させる,플레이어가 현재 항목을 사용해야합니다,Cho phép người chơi sử dụng công cụ hiện tại,Сделать плеер использовать текущий элемент,Oynatıcının geçerli öğeyi kullanmasını sağlayın,Fare giocatore usa elemento corrente,Make-Player aktuelles Element verwenden,Atur pemain menggunakan item ini,"local result = Player:useItem(objid, itemid, status, onshift)"
50031,5,游戏玩家,rotateCamera,"Player:rotateCamera(objid, yaw, pitch)","objid:number, yaw:number, pitch:number",ErrorCode.OK,旋转玩家镜头,"local result = Player:rotateCamera(objid, yaw, pitch)",遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,"objid:number, yaw:number, pitch:number","objid:number, yaw:number, pitch:number","objid:number, yaw:number, pitch:number","objid:number, yaw:number, pitch:number","objid:number, yaw:number, pitch:number","objid:number, yaw:number, pitch:number","objid:number, yaw:number, pitch:number","objid:number, yaw:number, pitch:number","objid:number, yaw:number, pitch:number","objid:number, yaw:number, pitch:number","objid:number, yaw:number, pitch:number","objid:number, yaw:number, pitch:number","objid:number, yaw:number, pitch:number","objid:number, yaw:number, pitch:number","objid:number, yaw:number, pitch:number",Rotate player's lens,旋轉玩家鏡頭,หมุมเลนส์ของผู้เล่น,Rotar la lente del jugador,Girar a lente do jogador,Faire pivoter la lentille du lecteur,新增待译0717,プレイヤーのレンズを回転させる,플레이어의 렌즈를 회전,Cuộn ống kính của người chơi,Поворот объектива игрока,Oyuncunun lensini döndür,Ruotare l'obiettivo del giocatore,Drehen der Linse des Players,Putar lensa penglihatan,"local result = Player:rotateCamera(objid, yaw, pitch)"
50032,5,游戏玩家,changeViewMode,"Player:changeViewMode(objid, viewmode, islock)","objid:number, viewmode:numberVIEWPORTTYPE, islock:boolean",ErrorCode.OK,改变玩家视角模式,"local result = Player:changeViewMode(objid, viewmode, islock)",遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,"objid:number, viewmode:numberVIEWPORTTYPE, islock:boolean","objid:number, viewmode:numberVIEWPORTTYPE, islock:boolean","objid:number, viewmode:numberVIEWPORTTYPE, islock:boolean","objid:number, viewmode:numberVIEWPORTTYPE, islock:boolean","objid:number, viewmode:numberVIEWPORTTYPE, islock:boolean","objid:number, viewmode:numberVIEWPORTTYPE, islock:boolean","objid:number, viewmode:numberVIEWPORTTYPE, islock:boolean","objid:number, viewmode:numberVIEWPORTTYPE, islock:boolean","objid:number, viewmode:numberVIEWPORTTYPE, islock:boolean","objid:number, viewmode:numberVIEWPORTTYPE, islock:boolean","objid:number, viewmode:numberVIEWPORTTYPE, islock:boolean","objid:number, viewmode:numberVIEWPORTTYPE, islock:boolean","objid:number, viewmode:numberVIEWPORTTYPE, islock:boolean","objid:number, viewmode:numberVIEWPORTTYPE, islock:boolean","objid:number, viewmode:numberVIEWPORTTYPE, islock:boolean",Change angle mode of player,改變玩家視角模式,เปลี่ยนโหมดมุมมองของผู้เล่น,Cambiar el modo de ángulo del jugador.,Alterar o modo de ângulo do jogador,Changer le mode d'angle de lecteur,新增待译0717,プレーヤーの視角モードを変更する,플레이어의 각 모드를 변경,Đổi chế độ góc nhìn của người chơi,Изменение угла режим плеера,Oyuncunun açı modunu değiştir,Cambiare modalità angolo di giocatore,Ändern Winkelmodus des Spielers,Ubah mode angle penglihatan,"local result = Player:changeViewMode(objid, viewmode, islock)"
50033,5,游戏玩家,setActionAttrState,"Player:setActionAttrState(objid, actionattr, switch)","objid:number, actionattr:numberPLAYERATTR, switch:boolean",ErrorCode.OK,设置生物行为属性状态,"local result = Player:setActionAttrState(objid, actionattr, switch)",遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,"objid:number, actionattr:numberPLAYERATTR, switch:boolean","objid:number, actionattr:numberPLAYERATTR, switch:boolean","objid:number, actionattr:numberPLAYERATTR, switch:boolean","objid:number, actionattr:numberPLAYERATTR, switch:boolean","objid:number, actionattr:numberPLAYERATTR, switch:boolean","objid:number, actionattr:numberPLAYERATTR, switch:boolean","objid:number, actionattr:numberPLAYERATTR, switch:boolean","objid:number, actionattr:numberPLAYERATTR, switch:boolean","objid:number, actionattr:numberPLAYERATTR, switch:boolean","objid:number, actionattr:numberPLAYERATTR, switch:boolean","objid:number, actionattr:numberPLAYERATTR, switch:boolean","objid:number, actionattr:numberPLAYERATTR, switch:boolean","objid:number, actionattr:numberPLAYERATTR, switch:boolean","objid:number, actionattr:numberPLAYERATTR, switch:boolean","objid:number, actionattr:numberPLAYERATTR, switch:boolean","Set the behavior, attribute and state of creature",設置生物行為屬性狀態,ตั้งค่าสถานะลักษณะการกระทำของสิ่งมีชีวิต,"Definir comportamiento de criatura, atributo y estado","Definir o comportamento, atributo e estado da criatura","Définir le comportement, l'attribut et l'état de créature",新增待译0717,生き物の振る舞い、属性、状態を設定する,생물의 행동 특성 및 상태를 설정,"Cài đặt hành vi, thuộc tính và trạng thái của sinh vật","Установите поведение, атрибут и состояние существ","Davranışı, niteliği ve yaratığın durumunu ayarlayın","Impostare il comportamento, attributi e lo stato di creatura","Stellen Sie das Verhalten, Attribut und den Zustand der Kreatur","Atur perilaku, peralatan dan keadaan makhluk","local result = Player:setActionAttrState(objid, actionattr, switch)"
50034,5,游戏玩家,checkActionAttrState,"Player:checkActionAttrState(objid, actionattr)","objid:number, actionattr:numberPLAYERATTR",ErrorCode.OK,玩家的行为属性状态开关,"local result = Player:checkActionAttrState(objid, actionattr)",遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,"objid:number, actionattr:numberPLAYERATTR","objid:number, actionattr:numberPLAYERATTR","objid:number, actionattr:numberPLAYERATTR","objid:number, actionattr:numberPLAYERATTR","objid:number, actionattr:numberPLAYERATTR","objid:number, actionattr:numberPLAYERATTR","objid:number, actionattr:numberPLAYERATTR","objid:number, actionattr:numberPLAYERATTR","objid:number, actionattr:numberPLAYERATTR","objid:number, actionattr:numberPLAYERATTR","objid:number, actionattr:numberPLAYERATTR","objid:number, actionattr:numberPLAYERATTR","objid:number, actionattr:numberPLAYERATTR","objid:number, actionattr:numberPLAYERATTR","objid:number, actionattr:numberPLAYERATTR","The toggle of behavior, attribute and state of player",玩家的行為屬性狀態開關,สวิตช์ของสถานะลักษณะการกระทำของผู้เล่น,"La alternancia de comportamiento del jugador, atributo y estado.","A alternância de comportamento, atributo e estado do jogador","La bascule du comportement, attribut et état de lecteur",新增待译0717,プレイヤーの行動、属性、状態の切り替え,"동작, 속성의 전환과 플레이어의 상태","Bật - Tắt trạng thái thuộc tính, hành vi của người chơi","Тумблер поведения, атрибут и состояние игрока","Davranışın geçişi, oyuncunun niteliği ve durumu","La leva di comportamento, attributo e lo stato di giocatore","Das Umschalten des Verhaltens, Attribut und den Zustand der Spieler","Tombol on off perilaku, peralatan dan keadaan makhluk","local result = Player:checkActionAttrState(objid, actionattr)"
50035,5,游戏玩家,getMaxHP,Player:getMaxHP(objid),objid:number,"ErrorCode.OK, value:number",获取当前最大血量,local result = Player:getMaxHP(objid),遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,Get the current max HP,獲取當前最大血量,รับค่า HP สูงสุดปัจจุบัน,Obtener HP actual máximo,Obter o HP max atual,Obtenez le HP courant max,新增待译0717,現在の最大HPを取得,현재 최대 HP를 가져옵니다,Xem lượng HP tối đa,Получить текущую максимальную HP,Geçerli maksimum HP'yi alın,Prendi il max HP corrente,Holen Sie sich den aktuellen max HP,Lihat max nyawa,local result = Player:getMaxHP(objid)
50036,5,游戏玩家,getCurHP,Player:getCurHP(objid),"objid:number, atttype:number","ErrorCode.OK, value:number",获取当前生命量,local result = Player:getCurHP(objid),遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,"objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number",Get the current HP,獲取當前生命量,รับค่า HP ปัจจุบัน,Obtén el HP actual,Obtenha o HP atual,Obtenez le HP en cours,新增待译0717,現在のHPを入手する,현재 HP를 가져옵니다,Xem mức HP hiện tại,Получить текущий HP,Mevcut HP'yi alın,Prendi la corrente di HP,Holen Sie sich das aktuelle HP,Lihat nyawa saat ini,local result = Player:getCurHP(objid)
50037,5,游戏玩家,getHpRecover,Player:getHpRecover(objid),"objid:number, atttype:number","ErrorCode.OK, value:number",HP恢复,local result = Player:getHpRecover(objid),遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,"objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number",HP recover,HP恢復,ฟื้นคืน HP,HP se recupera,HP recupera,HP récupérer,新增待译0717,HP回復,HP 회복,Hp hồi phục,HP восстановить,HP kurtarma,HP recuperare,HP erholen,Pemulihan nyawa,local result = Player:getHpRecover(objid)
50038,5,游戏玩家,getCurFood,Player:getCurFood(objid),"objid:number, atttype:number","ErrorCode.OK, value:number",当前饥饿值,local result = Player:getCurFood(objid),遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,"objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number",Current satiety,當前飢餓值,ค่าความหิวปัจจุบัน,Saciedad actual,Saciedade atual,courant satiété,新增待译0717,現在の満腹度,현재 물림,Độ đói hiện tại,ток насыщения,Mevcut tokluk,sazietà corrente,aktuelle Sättigungs,Tingkat kelaparan saat ini,local result = Player:getCurFood(objid)
50039,5,游戏玩家,getMaxOxygen,Player:getMaxOxygen(objid),"objid:number, atttype:number","ErrorCode.OK, value:number",[[,local result = Player:getMaxOxygen(objid),遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,"objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number",[[,[[,[[,[[,[[,[[,新增待译0717,[[,[[,[[,[[,[[,[[,[[,[[,local result = Player:getMaxOxygen(objid)
50040,5,游戏玩家,getCurOxygen,Player:getCurOxygen(objid),"objid:number, atttype:number","ErrorCode.OK, value:number",当前氧气值,local result = Player:getCurOxygen(objid),遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,"objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number",Current oxygen volume,當前氧氣值,ค่าออกซิเจนปัจจุบัน,Volumen actual de oxigeno,Volume de oxigênio atual,le volume d'oxygène actuel,新增待译0717,現在の酸素量,현재 산소 볼륨,Mức oxy hiện tại,Текущий объем кислорода,Mevcut oksijen hacmi,Volume ossigeno corrente,Aktuelle Sauerstoffvolumen,Volume oksigen saat ini,local result = Player:getCurOxygen(objid)
50041,5,游戏玩家,getWalkSpeed,Player:getWalkSpeed(objid),"objid:number, atttype:number","ErrorCode.OK, value:number",行走速度,local result = Player:getWalkSpeed(objid),遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,"objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number",Walking speed,行走速度,ความเร็วการเดิน,Velocidad de caminar,Velocidade de caminhada,Vitesse de marche,新增待译0717,歩くスピード,걷는 속도,Tốc độ đi bộ,скорость ходьбы,Yürüme hızı,velocità Walking,Schrittgeschwindigkeit,Kecepatan berjalan,local result = Player:getWalkSpeed(objid)
50042,5,游戏玩家,getSwimSpeed,Player:getSwimSpeed(objid),"objid:number, atttype:number","ErrorCode.OK, value:number",游泳速度（水中速度）,local result = Player:getSwimSpeed(objid),遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,"objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number",Swimming speed(in water),游泳速度（水中速度）,ความเร็วการว่าย(ความเร็วในนํ้า),Velocidad de natación (en el agua),Velocidade de natação (na água),Vitesse de nage (dans l'eau),新增待译0717,水泳スピード（水中）,수영 속도 (물에),Tốc độ bơi ( trong nước),Скорость плавания (в воде),Yüzme hızı (suda),velocità Piscina (in acqua),Schwimmgeschwindigkeit (in Wasser),Kecepatan berenang,local result = Player:getSwimSpeed(objid)
50043,5,游戏玩家,getJumpPower,Player:getJumpPower(objid),"objid:number, atttype:number","ErrorCode.OK, value:number",跳跃力,local result = Player:getJumpPower(objid),遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,"objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number",Jump force,跳躍力,แรงกระโดด,Fuerza de salto,Força de Salto,force de saut,新增待译0717,ジャンプ力,점프의 힘,Lực nhảy,Перейти сила,Atlama kuvveti,la forza di salto,Sprungkraft,Kekuatan lompat,local result = Player:getJumpPower(objid)
50044,5,游戏玩家,getRunSpeed,Player:getRunSpeed(objid),"objid:number, atttype:number","ErrorCode.OK, value:number",奔跑速度,local result = Player:getRunSpeed(objid),遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,"objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number",Running speed,奔跑速度,ความเร็วการวิ่ง,Velocidad de carrera,Velocidade de corrida,Vitesse de course,新增待译0717,走る速度,실행 속도,Tốc độ chạy,скорость движения,Koşu hızı,velocità di marcia,Laufgeschwindigkeit,Kecepatan berlari,local result = Player:getRunSpeed(objid)
50045,5,游戏玩家,getSneakSpeed,Player:getSneakSpeed(objid),"objid:number, atttype:number","ErrorCode.OK, value:number",潜行速度,local result = Player:getSneakSpeed(objid),遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,"objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number",Sneaking speed,潛行速度,ความเร็วการย่องเดิน,Velocidad de sigilo,Velocidade furtiva,vitesse faufilant,新增待译0717,潜水スピード,몰래 속도,Tốc độ trườn,Sneaking скорость,Gizlice hız,velocità sneaking,schleichen Geschwindigkeit,Kecepatan menyelinap,local result = Player:getSneakSpeed(objid)
50046,5,游戏玩家,getDodge,Player:getDodge(objid),"objid:number, atttype:number","ErrorCode.OK, value:number",闪避,local result = Player:getDodge(objid),遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,"objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number",Dodging speed,閃避,หลบ,Velocidad de esquivar,Esquivando-se da velocidade,vitesse esquiver,新增待译0717,避難,피하고 속도,Tốc độ tránh,Dodging скорость,Kaçma hızı,velocità schivare,Dodging Geschwindigkeit,Kecepatan mengelak,local result = Player:getDodge(objid)
50047,5,游戏玩家,getPunchAttack,Player:getPunchAttack(objid),"objid:number, atttype:number","ErrorCode.OK, value:number",近程攻击,local result = Player:getPunchAttack(objid),遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,"objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number",Melee attack,近程攻擊,โจมตีระยะใกล้,Ataque cuerpo a cuerpo,Ataque corpo a corpo,Attaque en mêlée,新增待译0717,近接攻撃,근접 공격,Tấn công cận chiến,Атака ближнего боя,Yakın muharebe saldırısı,Attacco corpo a corpo,Nahkampfangriff,Serangan jarak dekat,local result = Player:getPunchAttack(objid)
50048,5,游戏玩家,getRangeAttack,Player:getRangeAttack(objid),"objid:number, atttype:number","ErrorCode.OK, value:number",远程攻击,local result = Player:getRangeAttack(objid),遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,"objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number",Ranged attack,遠程攻擊,โจมตีระยะไกล,Ataque a distancia,Ataque à distância,Attaque à distance,新增待译0717,遠距離攻撃,원거리 공격,Tấn công viễn chiến,Стрелковая атака,Saldırıya uğramış saldırı,Attacco a distanza,Fernangriff,Serangan jarak jauh,local result = Player:getRangeAttack(objid)
50049,5,游戏玩家,getPunchDefense,Player:getPunchDefense(objid),"objid:number, atttype:number","ErrorCode.OK, value:number",近程防御,local result = Player:getPunchDefense(objid),遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,"objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number",Melee defense,近程防禦,ปกป้องระยะใกล้,Defensa cuerpo a cuerpo,Defesa corpo a corpo,défense Melee,新增待译0717,近接防御,근접 방어,Phòng thủ cận chiến,Melee защита,Yakın muharebe savunması,difesa melee,Melee Verteidigung,Perlindungan serangan jarak dekat,local result = Player:getPunchDefense(objid)
50050,5,游戏玩家,getRangeDefense,Player:getRangeDefense(objid),"objid:number, atttype:number","ErrorCode.OK, value:number",远程防御,local result = Player:getRangeDefense(objid),遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,"objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number",Ranged defense,遠程防禦,ปกป้องระยะไกล,Defensa a distancia,Defesa à distância,défense Ranged,新增待译0717,遠距離防衛,원거리 방어,Phòng thủ viễn chiến,Стрелковая защита,Menzilli savunma,difesa a Distanza,lag im Bereich Verteidigung,Perlindungan serangan jarak jauh,local result = Player:getRangeDefense(objid)
50051,5,游戏玩家,getStarNum,"Player:getStarNum(objid, val)","objid:number, atttype:number","ErrorCode.OK, value:number",星星数,"local result = Player:getStarNum(objid, val)",遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,"objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number",Number of stars,星星數,จำนวนดาว,Calificación de estrellas,Número de estrelas,Nombre d'étoiles,新增待译0717,星の数,별의 수,Số sao,Количество звезд,Yıldız sayısı,Numero di stelle,Anzahl der Sterne,Jumlah bintang,"local result = Player:getStarNum(objid, val)"
50052,5,游戏玩家,getModelScale,"Player:getModelScale(objid, val)","objid:number, atttype:number","ErrorCode.OK, value:number",模型大小,"local result = Player:getModelScale(objid, val)",遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,"objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number",Size of model,模型大小,ขนาดโมเดล,Tamaño de la plantilla,Tamanho do modelo,Taille du modèle,新增待译0717,モデルの大きさ,모델의 크기,Kích thước to nhỏ,Размер модели,Modelin boyutu,Dimensioni del modello,Größe des Modells,Ukuran model,"local result = Player:getModelScale(objid, val)"
50053,5,游戏玩家,setMaxHP,"Player:setMaxHP(objid, val)","objid:number, val:number, atttype:number",ErrorCode.OK,设置最大血量,"local result = Player:setMaxHP(objid, val)",遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,"objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number",Set the max HP,設置最大血量,ตั้งค่าค่า HP สูงสุด,Establecer max HP,Definir o max HP,Réglez le HP max,新增待译0717,最大HPを設定,최대 HP를 설정,Cài lượng HP tối đa,Установите максимальное HP,Maksimum HP'yi ayarlayın,Impostare il max HP,Stellen Sie den max HP,Atur jumlah max pada nyawa,"local result = Player:setMaxHP(objid, val)"
50054,5,游戏玩家,setHP,"Player:setHP(objid, val)","objid:number, val:number, atttype:number",ErrorCode.OK,设置血量,"local result = Player:setHP(objid, val)",遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,"objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number",Set HP,設置血量,ตั้งค่าค่า HP,Establecer HP,Definir HP,Set HP,新增待译0717,HPを設定,HP 설정,Cài HP,Набор HP,HP'yi ayarla,set HP,Set HP,Atur nyawa,"local result = Player:setHP(objid, val)"
50055,5,游戏玩家,setHpRecover,Player:setHpRecover(objid),"objid:number, val:number, atttype:number",ErrorCode.OK,设置HP恢复,local result = Player:setHpRecover(objid),遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,"objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number",Set HP recovery,設置HP恢復,ตั้งค่าการฟื้นคืนค่า HP,Establecer la recuperación de HP,Definir a recuperação da HP,Définissez l'option HP,新增待译0717,HP回復を設定する,HP 복구 설정,Cài đặt khôi phục HP,Установка восстановления HP,HP kurtarma işlemini ayarlayın,Set di recupero HP,Stellen Sie HP Recovery,Atur pemulihan pada nyawa,local result = Player:setHpRecover(objid)
50056,5,游戏玩家,setCurFood,Player:setCurFood(objid),"objid:number, val:number, atttype:number",ErrorCode.OK,设置当前饥饿度,local result = Player:setCurFood(objid),遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,"objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number",Set current satiety ,設置當前飢餓度,ตั้งค่าค่าความหิวปัจจุบัน,Definir la saciedad actual.,Definir a saciedade atual,Set courant satiété,新增待译0717,現在の満腹度を設定する,설정 현재 물림,Cài đặt độ đói hiện tại,Установить сытости тока,Geçerli doygunluğu ayarla,Imposta sazietà corrente,Eingestellte Stromsättigungs,Atur tingkat kelaparan,local result = Player:setCurFood(objid)
50057,5,游戏玩家,setOxygen,"Player:setOxygen(objid, val)","objid:number, val:number, atttype:number",ErrorCode.OK,设置氧气,"local result = Player:setOxygen(objid, val)",遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,"objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number",Set the oxygen,設置氧氣,ตั้งค่าออกซิเจน,Ajustar oxigeno,Ajuste o oxigênio,Réglez l'oxygène,新增待译0717,酸素を設定する,산소를 설정,Cài đặt oxy,Установите кислород,Oksijeni ayarla,Impostare l'ossigeno,Stellen Sie den Sauerstoff,Atur oksigen,"local result = Player:setOxygen(objid, val)"
50058,5,游戏玩家,setWalkSpeed,"Player:setWalkSpeed(objid, val)","objid:number, val:number, atttype:number",ErrorCode.OK,设置行走速度,"local result = Player:setWalkSpeed(objid, val)",遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,"objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number",Set walking speed,設置行走速度,ตั้งค่าความเร็วการเดิน,Establecer la velocidad de caminar,Definir velocidade de caminhada,Réglage de la vitesse de marche,新增待译0717,歩く速度を設定する,설정 보행 속도,Cài đặt tốc độ đi bộ,Установить скорость ходьбы,Yürüme hızını ayarla,Impostare la velocità a piedi,Set Gehgeschwindigkeit,Atur kecepatan jalan,"local result = Player:setWalkSpeed(objid, val)"
50059,5,游戏玩家,setSwimSpeed,"Player:setSwimSpeed(objid, val)","objid:number, val:number, atttype:number",ErrorCode.OK,设置游泳速度（水中速度）,"local result = Player:setSwimSpeed(objid, val)",遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,"objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number",Set swimming speed(in water),設置游泳速度（水中速度）,ตั้งค่าความเร็วการว่าย(ความเร็วในนํ้า),Ajuste la velocidad de natación (en agua),Definir velocidade de natação (na água),Régler la vitesse de nage (dans l'eau),新增待译0717,水泳速度を設定する（水中）,설정 수영 속도 (물에),Cài đặt tốc độ bơi ( trong nước),Установка скорость плавания (в воде),Yüzme hızını ayarlayın (suda),Impostare la velocità di nuoto (in acqua),Stellen Schwimmgeschwindigkeit (in Wasser),Atur kecepatan berenang,"local result = Player:setSwimSpeed(objid, val)"
50060,5,游戏玩家,setJumpPower,"Player:setJumpPower(objid, val)","objid:number, val:number, atttype:number",ErrorCode.OK,设置跳跃力,"local result = Player:setJumpPower(objid, val)",遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,"objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number",Set jump force,設置跳躍力,ตั้งค่าแรงกระโดด,Establecer fuerza de salto,Definir força de salto,Set force de saut,新增待译0717,ジャンプ力を設定,설정 점프의 힘,Cài đặt lực nhảy,Установить сила прыжка,Atlama kuvveti ayarla,Set forza salto,Set Sprungkraft,Atur kekuatan lompat,"local result = Player:setJumpPower(objid, val)"
50061,5,游戏玩家,setRunSpeed,"Player:setRunSpeed(objid, val)","objid:number, val:number, atttype:number",ErrorCode.OK,设置奔跑速度,"local result = Player:setRunSpeed(objid, val)",遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,"objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number",Set running speed,設置奔跑速度,ตั้งค่าความเร็วการวิ่ง,Establecer velocidad de carrera,Definir velocidade de corrida,Régler la vitesse de course,新增待译0717,走るスピードを設定する,설정 실행 속도,Cài đặt tốc độ chạy,Установить скорость бега,Koşu hızını ayarla,Imposta velocità di marcia,Stellen Sie Laufgeschwindigkeit,Atur kecepatan berlari,"local result = Player:setRunSpeed(objid, val)"
50062,5,游戏玩家,setSneakSpeed,"Player:setSneakSpeed(objid, val)","objid:number, val:number, atttype:number",ErrorCode.OK,设置潜行速度,"local result = Player:setSneakSpeed(objid, val)",遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,"objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number",Set sneaking speed,設置潛行速度,ตั้งค่าความเร็วการย่องเดิน,Establecer velocidad de sigilo,Definir velocidade furtiva,Régler la vitesse faufilant,新增待译0717,潜水スピードを設定,설정 몰래 속도,Cài đặt tốc độ trườn,Заданная скорость красться,Sinsi hızını ayarla,Impostare la velocità furtivamente,Stellen Sie schleichen Geschwindigkeit,Atur kecepatan menyelinap,"local result = Player:setSneakSpeed(objid, val)"
50063,5,游戏玩家,setDodge,"Player:setDodge(objid, val)","objid:number, val:number, atttype:number",ErrorCode.OK,设置闪避,"local result = Player:setDodge(objid, val)",遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,"objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number",Set dodging speed,設置閃避,ตั้งค่าการหลบ,Establecer velocidad de esquivar,Definir velocidade de esquiva,Réglage de la vitesse en esquivant,新增待译0717,回避を設定する,설정 닷징 속도,Cài đặt tốc độ tránh,Установить маневрирование скорости,Kaçma hızını ayarla,Impostare la velocità schivando,Stellen Sie ausweich Geschwindigkeit,Atur kekuatan mengelak,"local result = Player:setDodge(objid, val)"
50064,5,游戏玩家,setPunchAttack,"Player:setPunchAttack(objid, val)","objid:number, val:number, atttype:number",ErrorCode.OK,设置近程攻击,"local result = Player:setPunchAttack(objid, val)",遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,"objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number",Set melee attack,設置近程攻擊,ตั้งค่าโจมตีระยะใกล้,Establecer ataque cuerpo a cuerpo,Definir ataque corpo a corpo,Set attaque au corps à corps,新增待译0717,近接攻撃を設定する,설정 근접 공격,Cài đặt tấn công cận chiến,Установить атаки в ближнем бою,Yakın muharebe saldırısını ayarlayın,Set attacco in mischia,Set Nahkampfangriff,Atur serangan jarak dekat,"local result = Player:setPunchAttack(objid, val)"
50065,5,游戏玩家,setRangeAttack,"Player:setRangeAttack(objid, val)","objid:number, val:number, atttype:number",ErrorCode.OK,设置远程攻击,"local result = Player:setRangeAttack(objid, val)",遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,"objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number",Set ranged attack,設置遠程攻擊,ตั้งค่าโจมตีระยะไกล,Establecer ataque a distancia,Definir ataque à distância,Set attaque à distance,新增待译0717,遠隔攻撃を設定する,설정 원거리 공격을,Cài đặt tấn công viễn chiến,Набор дальней атаки,Menzilli saldırı ayarla,Set attacco a distanza,Set Distanzangriff,Atur serangan jarak jauh,"local result = Player:setRangeAttack(objid, val)"
50066,5,游戏玩家,setPunchDefense,"Player:setPunchDefense(objid, val)","objid:number, val:number, atttype:number",ErrorCode.OK,设置近程防御,"local result = Player:setPunchDefense(objid, val)",遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,"objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number",Set melee defense,設置近程防禦,ตั้งค่าปกป้องระยะใกล้,Definir defensa cuerpo a cuerpo.,Defina defesa corpo-a-corpo,Définir la défense de mêlée,新增待译0717,近接防御を設定する,설정 근접 방어,Cài đặt phòng vệ cận chiến,Набор рукопашная защита,Yakın muharebe savunmasını ayarla,Impostare la difesa in mischia,Set Nahkampfverteidigung,Atur perlindungan serangan jarak dekat,"local result = Player:setPunchDefense(objid, val)"
50067,5,游戏玩家,setRangeDefense,"Player:setRangeDefense(objid, val)","objid:number, val:number, atttype:number",ErrorCode.OK,设置远程防御,"local result = Player:setRangeDefense(objid, val)",遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,"objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number",Set ranged defense,設置遠程防禦,ตั้งค่าปกป้องระยะไกล,Establecer distancia de defensa,Definir defesa à distância,Set allait défense,新增待译0717,遠距離防御を設定する,설정은 방어 원거리,Cài đặt phòng vệ viễn chiến,Установить колебалась защита,Menzilli savunmayı ayarla,Set variava difesa,Set reichte Verteidigung,Atur perlindungan serangan jarak jauh,"local result = Player:setRangeDefense(objid, val)"
50068,5,游戏玩家,setStarNum,"Player:setStarNum(objid, val)","objid:number, val:number, atttype:number",ErrorCode.OK,设置星星数,"local result = Player:setStarNum(objid, val)",遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,"objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number",Set the number of stars,設置星星數,ตั้งค่าจำนวนดาว,Establecer número de estrella,Definir o número de estrelas,Définissez le nombre d'étoiles,新增待译0717,星の数を設定する,별의 수를 설정,Cài đặt số sao,Установите количество звезд,Yıldız sayısını ayarlayın,Impostare il numero di stelle,Legen Sie die Anzahl der Sterne,Atur jumlah bintang,"local result = Player:setStarNum(objid, val)"
50069,5,游戏玩家,setModelScale,"Player:setModelScale(objid, val)","objid:number, val:number, atttype:number",ErrorCode.OK,设置模型大小,"local result = Player:setModelScale(objid, val)",遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,"objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number",Set the size of model,設置模型大小,ตั้งค่าขนาดโมเดล,Establecer tamaño de plantilla,Definir o tamanho do modelo,Définissez la taille du modèle,新增待译0717,モデルのサイズを設定する,모델의 크기를 설정,Cài đặt kích thước mô hình,Установить размер модели,Modelin boyutunu ayarla,Impostare le dimensioni del modello di,Legen Sie die Größe des Modells,Atur ukuran model,"local result = Player:setModelScale(objid, val)"
50070,5,游戏玩家,setPosition,"Player:setPosition(objid, x, y, z)","objid:number, x|y|z:number",ErrorCode.OK,设置actor位置,"local result = Player:setPosition(objid, x, y, z)",遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,"objid:number, x|y|z:number","objid:number, x|y|z:number","objid:number, x|y|z:number","objid:number, x|y|z:number","objid:number, x|y|z:number","objid:number, x|y|z:number","objid:number, x|y|z:number","objid:number, x|y|z:number","objid:number, x|y|z:number","objid:number, x|y|z:number","objid:number, x|y|z:number","objid:number, x|y|z:number","objid:number, x|y|z:number","objid:number, x|y|z:number","objid:number, x|y|z:number",Set the location of actor,設置actor位置,ตั้งค่าตำแหน่งของactor,Establecer ubicación del actor,Definir a localização do ator,Définissez l'emplacement de l'acteur,新增待译0717,actorの位置を設定する,배우의 위치를 ​​설정합니다,Cài đặt vị trí actor,Установить местонахождение актера,Aktörün konumunu ayarla,Impostare la posizione dell'attore,Stellen Sie die Lage von Schauspieler,Atur lokasi aktor,"local result = Player:setPosition(objid, x, y, z)"
50071,5,游戏玩家,getAimPos,Player:getAimPos(objid),objid:number,ErrorCode.OK,获取player准星位置,local result = Player:getAimPos(objid),遊戲玩家,Player,ผู้เล่นเกม,Jugador,Jogador,Joueur,新增待译0717,プレイヤー,플레이어,Người chơi,игрок,Oyuncu,Giocatore,Spieler,Pemain,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,Get the crosshair position of player,獲取player準星位置,รับตำแหน่งเป้าเล็งของplayer,Obtener la posición de puntería del jugador,Obter a posição de mira do jogador,Obtenez la position du joueur crosshair,新增待译0717,プレイヤーの十字線の位置を取得する,플레이어의 십자선 위치를 가져옵니다,Nhận vị trí của người chơi,"Получить позицию перекрестие игрока,",Oyuncunun artı pozisyonunu al,Prendi la posizione mirino di giocatore,Holen Sie sich das Fadenkreuz Position des Spielers,Lihat posisi pemain dgn tepat,local result = Player:getAimPos(objid)
60001,6,游戏生物,isAdult,Creature:isAdult(objid),objid:number,ErrorCode.OK已经成年已经成年,是否已经成年,"local result = Creature:isAdult(objId)
if result == ErrorCode.OK then print(""该生物已成年"") end",遊戲生物,Creature,สิ่งมีชีวิต,Criatura,Criatura,Créature,新增待译0717,生き物,생물,Sinh Vật,Существо,Yaratık,Creatura,Kreatur,Makhluk,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,Is it adult?,是否已經成年,เป็นผู้ใหญ่หรือยัง,¿Eres un adulto?,É adulto?,Est-il des adultes?,新增待译0717,大人ですか？,그것은 성인가요?,Đã đủ tuổi vị thành niên chưa?,Является ли это для взрослых?,Yetişkin mi?,E 'adulta?,Ist es für Erwachsene?,Apakah seorang dewasa?,"local result = Creature:isAdult(objId)
if result == ErrorCode.OK then print(""This creature is adult"") end"
60002,6,游戏生物,setOxygenNeed,"Creature:setOxygenNeed(objid, v)","objid:number, v:boolean",ErrorCode.OK,设置是否依赖氧气,"local result = Creature:setOxygenNeed(objId, true)
if result == ErrorCode.OK then print(""该生物依赖氧气生存!"") end",遊戲生物,Creature,สิ่งมีชีวิต,Criatura,Criatura,Créature,新增待译0717,生き物,생물,Sinh Vật,Существо,Yaratık,Creatura,Kreatur,Makhluk,"objid:number, v:boolean","objid:number, v:boolean","objid:number, v:boolean","objid:number, v:boolean","objid:number, v:boolean","objid:number, v:boolean","objid:number, v:boolean","objid:number, v:boolean","objid:number, v:boolean","objid:number, v:boolean","objid:number, v:boolean","objid:number, v:boolean","objid:number, v:boolean","objid:number, v:boolean","objid:number, v:boolean",Set if oxygen is necessary,設置是否依賴氧氣,ตั้งค่าว่าจะพึ่งพาออกซิเจนหรือไม่,Definir si se requiere oxígeno.,Definir se o oxigênio é necessário,Positionné si l'oxygène est nécessaire,新增待译0717,酸素が必要どうかを設定する,산소가 필요한 경우 설정,Cài đặt xem liệu có cần oxy,"Срабатывает, если кислород необходим",Oksijen gerekliyse ayarlayın,Impostare se l'ossigeno è necessario,"Gesetzt, wenn Sauerstoff ist notwendig,",Atur oksigen jika diperlukan,"local result = Creature:setOxygenNeed(objId, true)
if result == ErrorCode.OK then print(""This creature relies on oxygen!"") end"
60003,6,游戏生物,getTamedOwnerID,Creature:getTamedOwnerID(objid),objid:number,"ErrorCode.OK, uin:number0表示未驯服",获取驯服主的ID,"local result, ownerId = Creature:getTamedOwnerID(objId)
if result == ErrorCode.OK and ownerId ~= 0 then
	local result, nickName = Player:getNickname(ownerId)
	if result == ErrorCode.OK then print('玩家<'+nickName+'>的宠物。。。')
end",遊戲生物,Creature,สิ่งมีชีวิต,Criatura,Criatura,Créature,新增待译0717,生き物,생물,Sinh Vật,Существо,Yaratık,Creatura,Kreatur,Makhluk,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,Get the id of player who has tamed creature,獲取馴服主的ID,รับ ID ของผู้เล่นที่เชื่องสัตว์,Consigue el id del jugador que domó a la criatura.,Obter o id do jogador que tenha domesticado a criatura,Obtenez l'identifiant du joueur qui a apprivoisé créature,新增待译0717,生き物を飼っていたプレイヤーのIDを取得する,생물을 지배 한 플레이어의 ID를 가져옵니다,Nhận ID của người thuần phục,"Получить идентификатор игрока, который приручил существо",Yaratığı evcilleştiren oyuncunun kimliğini alın,Ottenere l'id del giocatore che ha domato creatura,"Holen Sie sich das ID von Spielern, die Kreatur gezähmt hat",Lihat ID pemain yang telah menjinakkan makhluk atau hewan,"local result, ownerId = Creature:getTamedOwnerID(objId)
if result == ErrorCode.OK and ownerId ~= 0 then
        local result, nickName = Player:getNickname(ownerId)
        if result == ErrorCode.OK then print('Player<'+nickName+'>pet...')
end"
60004,6,游戏生物,setPanic,"Creature:setPanic(objid, v)","objid:number, v:boolean",ErrorCode.OK,设置是否恐慌,"local result = Creature:setPanic(objId, true)
if result == ErrorCode.OK then print(""该生物正在恐慌中"") end",遊戲生物,Creature,สิ่งมีชีวิต,Criatura,Criatura,Créature,新增待译0717,生き物,생물,Sinh Vật,Существо,Yaratık,Creatura,Kreatur,Makhluk,"objid:number, v:boolean","objid:number, v:boolean","objid:number, v:boolean","objid:number, v:boolean","objid:number, v:boolean","objid:number, v:boolean","objid:number, v:boolean","objid:number, v:boolean","objid:number, v:boolean","objid:number, v:boolean","objid:number, v:boolean","objid:number, v:boolean","objid:number, v:boolean","objid:number, v:boolean","objid:number, v:boolean",Set if it causes panic,設置是否恐慌,ตั้งค่าหวาดกลัวหรือไม่,Definir si provoca pánico.,Definir se causa pânico,Définir si elle provoque la panique,新增待译0717,パニックを引き起こすことを設定する,이 공황 발생하는 경우 설정,Cài đặt xem liệu có phải là bạo lực,"Срабатывает, если это вызывает панику",Paniğe neden olup olmadığını ayarla,Impostare se provoca il panico,"Gesetzt, wenn es Panik verursacht",Atur tingkat kepanikan,"local result = Creature:setPanic(objId, true)
if result == ErrorCode.OK then print(""The creature is in panic"") end"
60005,6,游戏生物,setAIActive,"Creature:setAIActive(objid, v)","objid:number, v:boolean",ErrorCode.OK,设置AI是否生效,"local result = Creature:setAIActive(objId, true)
if result == ErrorCode.OK then print(""该生物的AI已经生效"") end",遊戲生物,Creature,สิ่งมีชีวิต,Criatura,Criatura,Créature,新增待译0717,生き物,생물,Sinh Vật,Существо,Yaratık,Creatura,Kreatur,Makhluk,"objid:number, v:boolean","objid:number, v:boolean","objid:number, v:boolean","objid:number, v:boolean","objid:number, v:boolean","objid:number, v:boolean","objid:number, v:boolean","objid:number, v:boolean","objid:number, v:boolean","objid:number, v:boolean","objid:number, v:boolean","objid:number, v:boolean","objid:number, v:boolean","objid:number, v:boolean","objid:number, v:boolean",Set if AI takes effect,設置AI是否生效,ตั้งค่าว่า AI จะมีผลหรือไม่,Establecer si la IA entra en vigor,Defina se AI entrar em vigor,Définir si AI prend effet,新增待译0717,AIが有効になったことを設定する,AI가 적용됩니다 경우 설정,Cài đặt AI liệu có hiệu quả không,"Устанавливается, если AI вступает в силу",AI'nın etkili olup olmayacağını ayarlayın,Impostare se AI ha effetto,"Gesetzt, wenn AI Wirksam",Atur efek AI,"local result = Creature:setAIActive(objId, true)
if result == ErrorCode.OK then print(""This creatures AI takes effect"") end"
60006,6,游戏生物,getActorID,Creature:getActorID(objid),objid:number,"ErrorCode.OK, actorid:number",获取怪物资源ID,"local wolfId, dogId = 3407, 3408 --狼/狗的资源Id
local result, actorId = Creature:getActorID(objId)
if result == ErrorCode.OK then
	if actorId == dogId then print(""The creature is A Dog!"") end
	if actorId == wolfId then print(""The creature is A Wolf!"") end
end",遊戲生物,Creature,สิ่งมีชีวิต,Criatura,Criatura,Créature,新增待译0717,生き物,생물,Sinh Vật,Существо,Yaratık,Creatura,Kreatur,Makhluk,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,Get ID of monster resource,獲取怪物資源ID,รับ ID ทรัพยากรของมอน,Obtener ID de recurso de Monster,Obter ID do recurso de monstro,Obtenir l'identifiant de ressource de monstre,新增待译0717,モンスターのIDを取得する,괴물 리소스의 ID를 가져옵니다,Nhận nguồn ID sinh vật,Получить идентификатор ресурса монстра,Canavar kaynağının kimliğini al,Ottenere ID di risorsa mostro,Get ID von Monster Ressource,Lihat ID dan meterial monster,"local wolfId, dogId = 3407, 3408 --Wolf/Dogs resource Id
local result, actorId = Creature:getActorID(objId)
if result == ErrorCode.OK then
        if actorId == dogId then print(""The creature is A Dog!"") end
        if actorId == wolfId then print(""The creature is A Wolf!"") end
end"
60007,6,游戏生物,getActorName,Creature:getActorName(objid),objid:number,"ErrorCode.OK, name:string",获取怪物资源ID,local result = Creature:getActorName(objid),遊戲生物,Creature,สิ่งมีชีวิต,Criatura,Criatura,Créature,新增待译0717,生き物,생물,Sinh Vật,Существо,Yaratık,Creatura,Kreatur,Makhluk,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,Get ID of monster resource,獲取怪物資源ID,รับ ID ทรัพยากรของมอน,Obtener ID de recurso de Monster,Obter ID do recurso de monstro,Obtenir l'identifiant de ressource de monstre,新增待译0717,モンスターのIDを取得する,괴물 리소스의 ID를 가져옵니다,Nhận nguồn ID sinh vật,Получить идентификатор ресурса монстра,Canavar kaynağının kimliğini al,Ottenere ID di risorsa mostro,Get ID von Monster Ressource,Lihat ID dan meterial monster,local result = Creature:getActorName(objid)
60008,6,游戏生物,addModAttrib,"Creature:addModAttrib(objid, attrtype, value)","objid:number, attrtype:MODATTRIB_TYPE附魔属性类型, value:number",ErrorCode.OK,增加怪物ModAttrib值,"local modAttrType = MODATTRIB_TYPE.MODATTR_MOVE_SPEED
local result = Creature:addModAttrib(objid, modAttrType, 5)
if result == ErrorCode.OK then print('增加怪物移动速度') end",遊戲生物,Creature,สิ่งมีชีวิต,Criatura,Criatura,Créature,新增待译0717,生き物,생물,Sinh Vật,Существо,Yaratık,Creatura,Kreatur,Makhluk,"objid:number, attrtype:MODATTRIB_TYPE Enchantment attribute type, value:number","objid:number, attrtype:MODATTRIB_TYPE Enchantment attribute type, value:number","objid:number, attrtype:MODATTRIB_TYPE Enchantment attribute type, value:number","objid:number, attrtype:MODATTRIB_TYPE Enchantment attribute type, value:number","objid:number, attrtype:MODATTRIB_TYPE Enchantment attribute type, value:number","objid:number, attrtype:MODATTRIB_TYPE Enchantment attribute type, value:number","objid:number, attrtype:MODATTRIB_TYPE Enchantment attribute type, value:number","objid:number, attrtype:MODATTRIB_TYPE Enchantment attribute type, value:number","objid:number, attrtype:MODATTRIB_TYPE Enchantment attribute type, value:number","objid:number, attrtype:MODATTRIB_TYPE Enchantment attribute type, value:number","objid:number, attrtype:MODATTRIB_TYPE Enchantment attribute type, value:number","objid:number, attrtype:MODATTRIB_TYPE Enchantment attribute type, value:number","objid:number, attrtype:MODATTRIB_TYPE Enchantment attribute type, value:number","objid:number, attrtype:MODATTRIB_TYPE Enchantment attribute type, value:number","objid:number, attrtype:MODATTRIB_TYPE Enchantment attribute type, value:number",Increase the ModAttrib of monster,增加怪物ModAttrib值,เพิ่มค่าModAttribของมอน,Aumentar Monster ModAttrib,Aumentar o ModAttrib do monstro,Augmenter le ModAttrib du monstre,新增待译0717,モンスターのModAttribを増やす,괴물의 ModAttrib을 증가,Thêm lượng ModAttrib của quái vật,Увеличение ModAttrib монстра,Canavarın ModAttrib'ini arttır,Aumentare la ModAttrib del mostro,Erhöhen Sie die ModAttrib von Monster,Tingkatkan ModAttrib monster,"local modAttrType = MODATTRIB_TYPE.MODATTR_MOVE_SPEED
local result = Creature:addModAttrib(objid, modAttrType, 5)
if result == ErrorCode.OK then print('Increase the moving speed of monster') end"
60009,6,游戏生物,getModAttrib,"Creature:getModAttrib(objid, attrtype)","objid:number, attrtype:MODATTRIB_TYPE附魔属性类型","ErrorCode.OK, value:number",获取怪物ModAttrib值,"local modAttrType = MODATTRIB_TYPE.MODATTR_MOVE_SPEED
local result, modAttrVal = Creature:getModAttrib(objid, modAttrType)
if result == ErrorCode.OK then print('怪物的移动速度为', modAttrVal) end",遊戲生物,Creature,สิ่งมีชีวิต,Criatura,Criatura,Créature,新增待译0717,生き物,생물,Sinh Vật,Существо,Yaratık,Creatura,Kreatur,Makhluk,"objid:number, attrtype:MODATTRIB_TYPE Enchantment attribute type","objid:number, attrtype:MODATTRIB_TYPE Enchantment attribute type","objid:number, attrtype:MODATTRIB_TYPE Enchantment attribute type","objid:number, attrtype:MODATTRIB_TYPE Enchantment attribute type","objid:number, attrtype:MODATTRIB_TYPE Enchantment attribute type","objid:number, attrtype:MODATTRIB_TYPE Enchantment attribute type","objid:number, attrtype:MODATTRIB_TYPE Enchantment attribute type","objid:number, attrtype:MODATTRIB_TYPE Enchantment attribute type","objid:number, attrtype:MODATTRIB_TYPE Enchantment attribute type","objid:number, attrtype:MODATTRIB_TYPE Enchantment attribute type","objid:number, attrtype:MODATTRIB_TYPE Enchantment attribute type","objid:number, attrtype:MODATTRIB_TYPE Enchantment attribute type","objid:number, attrtype:MODATTRIB_TYPE Enchantment attribute type","objid:number, attrtype:MODATTRIB_TYPE Enchantment attribute type","objid:number, attrtype:MODATTRIB_TYPE Enchantment attribute type",Increase the ModAttrib of monster,獲取怪物ModAttrib值,เพิ่มค่าModAttribของมอน,Aumentar Monster ModAttrib,Aumentar o ModAttrib do monstro,Augmenter le ModAttrib du monstre,新增待译0717,モンスターのModAttribを獲得する。,괴물의 ModAttrib을 증가,Nhận lượng ModAttrib của quái vật,Увеличение ModAttrib монстра,Canavarın ModAttrib'ini arttır,Aumentare la ModAttrib del mostro,Erhöhen Sie die ModAttrib von Monster,Tingkatkan ModAttrib monster,"local modAttrType = MODATTRIB_TYPE.MODATTR_MOVE_SPEED
local result, modAttrVal = Creature:getModAttrib(objid, modAttrType)
if result == ErrorCode.OK then print('Moving speed of monster is', modAttrVal) end"
60010,6,游戏生物,setTeam,"Creature:setTeam(objid, teamid)","objid:number, teamid:number",ErrorCode.OK,设置生物队伍,"local result = Creature:setTeam(objid, teamid)",遊戲生物,Creature,สิ่งมีชีวิต,Criatura,Criatura,Créature,新增待译0717,生き物,생물,Sinh Vật,Существо,Yaratık,Creatura,Kreatur,Makhluk,"objid:number, teamid:number","objid:number, teamid:number","objid:number, teamid:number","objid:number, teamid:number","objid:number, teamid:number","objid:number, teamid:number","objid:number, teamid:number","objid:number, teamid:number","objid:number, teamid:number","objid:number, teamid:number","objid:number, teamid:number","objid:number, teamid:number","objid:number, teamid:number","objid:number, teamid:number","objid:number, teamid:number",Set the creature team,設置生物隊伍,ตั้งค่าทีมของสิ่งมีชีวิต,Establece el equipo de criaturas.,Defina a equipe de criaturas,Définir l'équipe de créature,新增待译0717,生き物のチームを設定する,생물 팀을 설정,Cài đặt nhóm sinh vật,Установите команду существ,Yaratık ekibini ayarla,Impostare la squadra creatura,Stellen Sie die Kreatur Team,Atur team makhluk,"local result = Creature:setTeam(objid, teamid)"
60011,6,游戏生物,getTeam,Creature:getTeam(objid),objid:number,"ErrorCode.OK, teamid:number",获取生物队伍,local result = Creature:getTeam(objid),遊戲生物,Creature,สิ่งมีชีวิต,Criatura,Criatura,Créature,新增待译0717,生き物,생물,Sinh Vật,Существо,Yaratık,Creatura,Kreatur,Makhluk,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,Set the creature team,獲取生物隊伍,รับทีมของสิ่งมีชีวิต,Establece el equipo de criaturas.,Defina a equipe de criaturas,Définir l'équipe de créature,新增待译0717,生き物のチームを受取る,생물 팀을 설정,Cài đặt nhóm sinh vật,Установите команду существ,Yaratık ekibini ayarla,Impostare la squadra creatura,Stellen Sie die Kreatur Team,Atur team makhluk,local result = Creature:getTeam(objid)
60012,6,游戏生物,getMaxFood,Creature:getMaxFood(objid),"objid:number, atttype:number","ErrorCode.OK, value:number",获取最大饥饿度,local result = Creature:getMaxFood(objid),遊戲生物,Creature,สิ่งมีชีวิต,Criatura,Criatura,Créature,新增待译0717,生き物,생물,Sinh Vật,Существо,Yaratık,Creatura,Kreatur,Makhluk,"objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number",Get max satiety,獲取最大饑餓度,รับค่าความหิวสูงสุด,Consigue la máxima saciedad,Obter a saciedade máxima,Obtenez max satiété,新增待译0717,最大の満腹感を得る,최대 포만감을 얻을,Xem độ đói tối đa,Получить максимальную сытость,Maksimum tokluk alın,Get sazietà max,Erhalten Sie max Sättigung,Tingkat max kelaparan,local result = Creature:getMaxFood(objid)
60013,6,游戏生物,getFood,Creature:getFood(objid),"objid:number, atttype:number","ErrorCode.OK, value:number",获取饥饿度,local result = Creature:getFood(objid),遊戲生物,Creature,สิ่งมีชีวิต,Criatura,Criatura,Créature,新增待译0717,生き物,생물,Sinh Vật,Существо,Yaratık,Creatura,Kreatur,Makhluk,"objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number",Get satiety,獲取飢餓度,รับค่าความหิว,Obtener la saciedad,Obter saciedade,Obtenez la satiété,新增待译0717,満腹度を受取る,포만감을 얻을,Xem độ đói,Получить сытость,Tokluk almak,Get sazietà,erhalten Sättigung,Tingkat kelaparan,local result = Creature:getFood(objid)
60014,6,游戏生物,setFood,"Creature:setFood(objid, val)","objid:number, val:number, atttype:number",ErrorCode.OK,设置饥饿度,"local result = Creature:setFood(objid, val)",遊戲生物,Creature,สิ่งมีชีวิต,Criatura,Criatura,Créature,新增待译0717,生き物,생물,Sinh Vật,Существо,Yaratık,Creatura,Kreatur,Makhluk,"objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number",Set satiety,設置飢餓度,ตั้งค่าค่าความหิว,Establecer la saciedad,Definir saciedade,Set satiété,新增待译0717,満腹感を設定する,설정 물림,Cài đặt độ đói  ,Набор сытости,Doygunluğu ayarla,set sazietà,Set Sättigung,Atur tingkat kelaparan,"local result = Creature:setFood(objid, val)"
60015,6,游戏生物,getHpRecover,Creature:getHpRecover(objid),"objid:number, atttype:number","ErrorCode.OK, value:number",HP恢复,local result = Creature:getHpRecover(objid),遊戲生物,Creature,สิ่งมีชีวิต,Criatura,Criatura,Créature,新增待译0717,生き物,생물,Sinh Vật,Существо,Yaratık,Creatura,Kreatur,Makhluk,"objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number",HP recover,HP恢復,ฟื้นคืน HP,HP recupera,HP recuperar,HP récupérer,新增待译0717,HP回復,HP 회복,Hp hồi phục,HP восстановить,HP kurtarma,HP recuperare,HP erholen,Pemulihan nyawa,local result = Creature:getHpRecover(objid)
60016,6,游戏生物,getMaxOxygen,Creature:getMaxOxygen(objid),"objid:number, atttype:number","ErrorCode.OK, value:number",最大氧气值,local result = Creature:getMaxOxygen(objid),遊戲生物,Creature,สิ่งมีชีวิต,Criatura,Criatura,Créature,新增待译0717,生き物,생물,Sinh Vật,Существо,Yaratık,Creatura,Kreatur,Makhluk,"objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number",Max oxygen volume,最大氧氣值,ค่าออกซิเจนสูงสุด,Volumen máximo de oxígeno,Volume máximo de oxigênio,Max volume d'oxygène,新增待译0717,最大酸素量,최대 산소 볼륨,Lượng oxy tối đa,Максимальный объем кислорода,Maksimum oksijen hacmi,volume massimo di ossigeno,Max Sauerstoffvolumen,Volume max oksigen,local result = Creature:getMaxOxygen(objid)
60017,6,游戏生物,getWalkSpeed,Creature:getWalkSpeed(objid),"objid:number, atttype:number","ErrorCode.OK, value:number",行走速度,local result = Creature:getWalkSpeed(objid),遊戲生物,Creature,สิ่งมีชีวิต,Criatura,Criatura,Créature,新增待译0717,生き物,생물,Sinh Vật,Существо,Yaratık,Creatura,Kreatur,Makhluk,"objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number",Walking speed,行走速度,ความเร็วการเดิน,Velocidad de caminar,Velocidade de caminhada,Vitesse de marche,新增待译0717,歩くスピード,걷는 속도,Tốc độ đi bộ,скорость ходьбы,Yürüme hızı,velocità Walking,Schrittgeschwindigkeit,Kecepatan berjalan,local result = Creature:getWalkSpeed(objid)
60018,6,游戏生物,getSwimSpeed,Creature:getSwimSpeed(objid),"objid:number, atttype:number","ErrorCode.OK, value:number",游泳速度（水中速度）,local result = Creature:getSwimSpeed(objid),遊戲生物,Creature,สิ่งมีชีวิต,Criatura,Criatura,Créature,新增待译0717,生き物,생물,Sinh Vật,Существо,Yaratık,Creatura,Kreatur,Makhluk,"objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number",Swimming speed(in water),游泳速度（水中速度）,ความเร็วการว่าย(ความเร็วในนํ้า),Velocidad de natación (en el agua),Velocidade de natação (na água),Vitesse de nage (dans l'eau),新增待译0717,水泳スピード（水中）,수영 속도 (물에),Tốc độ bơi ( trong nước),Скорость плавания (в воде),Yüzme hızı (suda),velocità Piscina (in acqua),Schwimmgeschwindigkeit (in Wasser),Kecepatan berenang,local result = Creature:getSwimSpeed(objid)
60019,6,游戏生物,getJumpPower,Creature:getJumpPower(objid),"objid:number, atttype:number","ErrorCode.OK, value:number",跳跃力,local result = Creature:getJumpPower(objid),遊戲生物,Creature,สิ่งมีชีวิต,Criatura,Criatura,Créature,新增待译0717,生き物,생물,Sinh Vật,Существо,Yaratık,Creatura,Kreatur,Makhluk,"objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number",Jump force,跳躍力,แรงกระโดด,Fuerza de salto,Força de Salto,force de saut,新增待译0717,ジャンプ力,점프의 힘,Lực nhảy,Перейти сила,Atlama kuvveti,la forza di salto,Sprungkraft,Kekuatan lompat,local result = Creature:getJumpPower(objid)
60020,6,游戏生物,getMass,Creature:getMass(objid),"objid:number, atttype:number","ErrorCode.OK, value:number",重量,local result = Creature:getMass(objid),遊戲生物,Creature,สิ่งมีชีวิต,Criatura,Criatura,Créature,新增待译0717,生き物,생물,Sinh Vật,Существо,Yaratık,Creatura,Kreatur,Makhluk,"objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number",Weight,重量,นํ้าหนัก,Peso,Peso,Poids,新增待译0717,重さ,무게,Trọng lượng,Вес,Ağırlık,Peso,Gewicht,Berat,local result = Creature:getMass(objid)
60021,6,游戏生物,getDodge,Creature:getDodge(objid),"objid:number, atttype:number","ErrorCode.OK, value:number",闪避,local result = Creature:getDodge(objid),遊戲生物,Creature,สิ่งมีชีวิต,Criatura,Criatura,Créature,新增待译0717,生き物,생물,Sinh Vật,Существо,Yaratık,Creatura,Kreatur,Makhluk,"objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number",Dodge,閃避,หลบ,Esquivar,Esquivar,Esquiver,新增待译0717,回避,다지,Tránh,изворачиваться,kaçamak yapmak,Schivare,Ausweichen,Mengelak,local result = Creature:getDodge(objid)
60022,6,游戏生物,getPunchAttack,Creature:getPunchAttack(objid),"objid:number, atttype:number","ErrorCode.OK, value:number",近程攻击,local result = Creature:getPunchAttack(objid),遊戲生物,Creature,สิ่งมีชีวิต,Criatura,Criatura,Créature,新增待译0717,生き物,생물,Sinh Vật,Существо,Yaratık,Creatura,Kreatur,Makhluk,"objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number",Melee attack,近程攻擊,โจมตีระยะใกล้,Ataque cuerpo a cuerpo,Ataque corpo a corpo,Attaque en mêlée,新增待译0717,近接攻撃,근접 공격,Tấn công cận chiến,Атака ближнего боя,Yakın muharebe saldırısı,Attacco corpo a corpo,Nahkampfangriff,Serangan jarak dekat,local result = Creature:getPunchAttack(objid)
60023,6,游戏生物,getRangeAttack,Creature:getRangeAttack(objid),"objid:number, atttype:number","ErrorCode.OK, value:number",远程攻击,local result = Creature:getRangeAttack(objid),遊戲生物,Creature,สิ่งมีชีวิต,Criatura,Criatura,Créature,新增待译0717,生き物,생물,Sinh Vật,Существо,Yaratık,Creatura,Kreatur,Makhluk,"objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number",Ranged attack,遠程攻擊,โจมตีระยะไกล,Ataque a distancia,Ataque à distância,Attaque à distance,新增待译0717,遠隔攻撃,원거리 공격,Tấn công viễn chiến,Стрелковая атака,Saldırıya uğramış saldırı,Attacco a distanza,Fernangriff,Srangan jarak jauh,local result = Creature:getRangeAttack(objid)
60024,6,游戏生物,getPunchDefense,Creature:getPunchDefense(objid),"objid:number, atttype:number","ErrorCode.OK, value:number",近程防御,local result = Creature:getPunchDefense(objid),遊戲生物,Creature,สิ่งมีชีวิต,Criatura,Criatura,Créature,新增待译0717,生き物,생물,Sinh Vật,Существо,Yaratık,Creatura,Kreatur,Makhluk,"objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number",Melee defense,近程防禦,ปกป้องระยะใกล้,Defensa cuerpo a cuerpo,Defesa corpo a corpo,défense Melee,新增待译0717,近接防御,근접 방어,Phòng vệ cận chiến,Melee защита,Yakın muharebe savunması,difesa melee,Melee Verteidigung,Perlindungan serangan jarak dekat,local result = Creature:getPunchDefense(objid)
60025,6,游戏生物,getRangeDefense,Creature:getRangeDefense(objid),"objid:number, atttype:number","ErrorCode.OK, value:number",远程防御,local result = Creature:getRangeDefense(objid),遊戲生物,Creature,สิ่งมีชีวิต,Criatura,Criatura,Créature,新增待译0717,生き物,생물,Sinh Vật,Существо,Yaratık,Creatura,Kreatur,Makhluk,"objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number","objid:number, atttype:number",Ranged defense,遠程防禦,ปกป้องระยะไกล,Defensa a distancia,Defesa à distância,défense Ranged,新增待译0717,遠隔防衛,원거리 방어,Phòng vệ viễn chiến,Стрелковая защита,Menzilli savunma,difesa a Distanza,lag im Bereich Verteidigung,Perlindungan serangan jarak jauh,local result = Creature:getRangeDefense(objid)
60026,6,游戏生物,setMaxHp,"Creature:setMaxHp(objid, val)","objid:number, val:number, atttype:number",ErrorCode.OK,设置血量,"local result = Creature:setMaxHp(objid, val)",遊戲生物,Creature,สิ่งมีชีวิต,Criatura,Criatura,Créature,新增待译0717,生き物,생물,Sinh Vật,Существо,Yaratık,Creatura,Kreatur,Makhluk,"objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number",Set HP,設置血量,ตั้งค่า HP,Establecer HP,Definir HP,Set HP,新增待译0717,HPを設定,HP 설정,Cài đặt HP,Набор HP,Menzilli savunma,set HP,Set HP,Atur nyawa,"local result = Creature:setMaxHp(objid, val)"
60027,6,游戏生物,setHP,"Creature:setHP(objid, val)","objid:number, val:number, atttype:number",ErrorCode.OK,设置血量,"local result = Creature:setHP(objid, val)",遊戲生物,Creature,สิ่งมีชีวิต,Criatura,Criatura,Créature,新增待译0717,生き物,생물,Sinh Vật,Существо,Yaratık,Creatura,Kreatur,Makhluk,"objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number",Set HP,設置血量,ตั้งค่า HP,Establecer HP,Definir HP,Set HP,新增待译0717,HPを設定,HP 설정,Cài đặt HP,Набор HP,Yıldız sayısı,set HP,Set HP,Atur nyawa,"local result = Creature:setHP(objid, val)"
60028,6,游戏生物,setHpRecover,Creature:setHpRecover(objid),"objid:number, val:number, atttype:number",ErrorCode.OK,设置HP恢复,local result = Creature:setHpRecover(objid),遊戲生物,Creature,สิ่งมีชีวิต,Criatura,Criatura,Créature,新增待译0717,生き物,생물,Sinh Vật,Существо,Yaratık,Creatura,Kreatur,Makhluk,"objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number",Set HP recovery,設置HP恢復,ตั้งค่าการฟื้นคืนค่า HP,Establecer la recuperación de HP,Definir Recuperacao HP,Définissez l'option HP,新增待译0717,HPの回復を設定する,HP 복구 설정,Cài đặt hồi phục HP,Установка восстановления HP,Modelin boyutu,Set di recupero HP,Stellen Sie HP Recovery,Atur pemulihan nyawa,local result = Creature:setHpRecover(objid)
60029,6,游戏生物,setOxygen,"Creature:setOxygen(objid, val)","objid:number, val:number, atttype:number",ErrorCode.OK,设置氧气,"local result = Creature:setOxygen(objid, val)",遊戲生物,Creature,สิ่งมีชีวิต,Criatura,Criatura,Créature,新增待译0717,生き物,생물,Sinh Vật,Существо,Yaratık,Creatura,Kreatur,Makhluk,"objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number",Set oxygen,設置氧氣,ตั้งค่าออกซิเจน,Definir oxigeno,Definir Oxigenio,Set oxygène,新增待译0717,最大HPを設定,설정 산소,Cài đặt oxy,Набор кислорода,Maksimum HP'yi ayarlayın,set di ossigeno,Set Sauerstoff,Atur oksigen,"local result = Creature:setOxygen(objid, val)"
60030,6,游戏生物,setWalkSpeed,"Creature:setWalkSpeed(objid, val)","objid:number, val:number, atttype:number",ErrorCode.OK,设置行走速度,"local result = Creature:setWalkSpeed(objid, val)",遊戲生物,Creature,สิ่งมีชีวิต,Criatura,Criatura,Créature,新增待译0717,生き物,생물,Sinh Vật,Существо,Yaratık,Creatura,Kreatur,Makhluk,"objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number",Set walking speed,設置行走速度,ตั้งค่าความเร็วการเดิน,Establecer la velocidad de caminar,Definir velocidade de caminhada,Réglage de la vitesse de marche,新增待译0717,走るスピードを設定する,설정 보행 속도,Cài đặt tốc độ đi bộ,Установить скорость ходьбы,HP'yi ayarla,Impostare la velocità a piedi,Set Gehgeschwindigkeit,Atur kecepatan berjalan,"local result = Creature:setWalkSpeed(objid, val)"
60031,6,游戏生物,setSwimSpeed,"Creature:setSwimSpeed(objid, val)","objid:number, val:number, atttype:number",ErrorCode.OK,设置游泳速度（水中速度）,"local result = Creature:setSwimSpeed(objid, val)",遊戲生物,Creature,สิ่งมีชีวิต,Criatura,Criatura,Créature,新增待译0717,生き物,생물,Sinh Vật,Существо,Yaratık,Creatura,Kreatur,Makhluk,"objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number",Set swimming speed(in water),設置游泳速度（水中速度）,ตั้งค่าความเร็วการว่าย(ความเร็วในนํ้า),Ajuste la velocidad de natación (en agua),Definir velocidade de natação (na água),Régler la vitesse de nage (dans l'eau),新增待译0717,水泳スピードを設定する,설정 수영 속도 (물에),Cài đặt tốc độ bơi ( trong nước),Установка скорость плавания (в воде),HP kurtarma işlemini ayarlayın,Impostare la velocità di nuoto (in acqua),Stellen Schwimmgeschwindigkeit (in Wasser),Atur kecepatan berenang,"local result = Creature:setSwimSpeed(objid, val)"
60032,6,游戏生物,setJumpPower,"Creature:setJumpPower(objid, val)","objid:number, val:number, atttype:number",ErrorCode.OK,设置跳跃力,"local result = Creature:setJumpPower(objid, val)",遊戲生物,Creature,สิ่งมีชีวิต,Criatura,Criatura,Créature,新增待译0717,生き物,생물,Sinh Vật,Существо,Yaratık,Creatura,Kreatur,Makhluk,"objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number",Set the jump force,設置跳躍力,ตั้งค่าแรงกระโดด,Establecer la fuerza de salto.,Definir a força de salto,Régler la force de saut,新增待译0717,ジャンプ力を設定する,점프의 힘을 설정,Cài đặt lực nhảy,Установите скачок силы,Atlama kuvveti ayarlayın,Impostare la forza di salto,Stellen Sie die Sprungkraft,Atur kekuatan lompat,"local result = Creature:setJumpPower(objid, val)"
60033,6,游戏生物,setDodge,"Creature:setDodge(objid, val)","objid:number, val:number, atttype:number",ErrorCode.OK,设置闪避,"local result = Creature:setDodge(objid, val)",遊戲生物,Creature,สิ่งมีชีวิต,Criatura,Criatura,Créature,新增待译0717,生き物,생물,Sinh Vật,Существо,Yaratık,Creatura,Kreatur,Makhluk,"objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number",Set dodging,設置閃避,ตั้งค่าการหลบ,Establece el esquivar,Definir a esquiva,Set esquivant,新增待译0717,回避設定,설정 닷징,Cài đặt tránh,Набор маневрировании,Kaçmayı ayarla,set schivare,Set ausweich,Atur kecepatan mengelak,"local result = Creature:setDodge(objid, val)"
60034,6,游戏生物,setPunchAttack,"Creature:setPunchAttack(objid, val)","objid:number, val:number, atttype:number",ErrorCode.OK,设置近程攻击,"local result = Creature:setPunchAttack(objid, val)",遊戲生物,Creature,สิ่งมีชีวิต,Criatura,Criatura,Créature,新增待译0717,生き物,생물,Sinh Vật,Существо,Yaratık,Creatura,Kreatur,Makhluk,"objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number",Set melee attack,設置近程攻擊,ตั้งค่าโจมตีระยะใกล้,Establecer ataque cuerpo a cuerpo,Definir ataque corpo a corpo,Set attaque au corps à corps,新增待译0717,近接攻撃を設定する,설정 근접 공격,Cài đặt tấn công cận chiến,Установить атаки в ближнем бою,Yakın muharebe saldırısını ayarlayın,Set attacco in mischia,Set Nahkampfangriff,Atur serangan jarak dekat,"local result = Creature:setPunchAttack(objid, val)"
60035,6,游戏生物,setRangeAttack,"Creature:setRangeAttack(objid, val)","objid:number, val:number, atttype:number",ErrorCode.OK,设置远程攻击,"local result = Creature:setRangeAttack(objid, val)",遊戲生物,Creature,สิ่งมีชีวิต,Criatura,Criatura,Créature,新增待译0717,生き物,생물,Sinh Vật,Существо,Yaratık,Creatura,Kreatur,Makhluk,"objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number",Set ranged attack,設置遠程攻擊,ตั้งค่าโจมตีระยะไกล,Establecer ataque a distancia,Definir ataque à distância,Set attaque à distance,新增待译0717,遠隔攻撃を設定する,설정 원거리 공격을,Cài đặt tấn công viễn chiến,Набор дальней атаки,Menzilli saldırı ayarla,Set attacco a distanza,Set Distanzangriff,Atur serangan jarak jauh,"local result = Creature:setRangeAttack(objid, val)"
60036,6,游戏生物,setPunchDefense,"Creature:setPunchDefense(objid, val)","objid:number, val:number, atttype:number",ErrorCode.OK,设置近程防御,"local result = Creature:setPunchDefense(objid, val)",遊戲生物,Creature,สิ่งมีชีวิต,Criatura,Criatura,Créature,新增待译0717,生き物,생물,Sinh Vật,Существо,Yaratık,Creatura,Kreatur,Makhluk,"objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number",Set melee defense,設置近程防禦,ตั้งค่าปกป้องระยะใกล้,Definir defensa cuerpo a cuerpo.,Defina defesa corpo-a-corpo,Définir la défense de mêlée,新增待译0717,近接防御を設定する,설정 근접 방어,Cài đặt phòng thủ cận chiến ,Набор рукопашная защита,Yakın muharebe savunmasını ayarla,Impostare la difesa in mischia,Set Nahkampfverteidigung,Atur perlindungan serangan jarak dekat,"local result = Creature:setPunchDefense(objid, val)"
60037,6,游戏生物,setRangeDefense,"Creature:setRangeDefense(objid, val)","objid:number, val:number, atttype:number",ErrorCode.OK,设置远程防御,"local result = Creature:setRangeDefense(objid, val)",遊戲生物,Creature,สิ่งมีชีวิต,Criatura,Criatura,Créature,新增待译0717,生き物,생물,Sinh Vật,Существо,Yaratık,Creatura,Kreatur,Makhluk,"objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number","objid:number, val:number, atttype:number",Set ranged defense,設置遠程防禦,ตั้งค่าปกป้องระยะไกล,Establecer distancia de defensa,Definir defesa à distância,Set allait défense,新增待译0717,遠距離防御を設定する,설정은 방어 원거리,Cài đặt phòng thủ viễn chiến,Установить колебалась защита,Menzilli savunmayı ayarla,Set variava difesa,Set reichte Verteidigung,Atur perlindungan serangan jarak jauh,"local result = Creature:setRangeDefense(objid, val)"
70001,7,UI管理,setGBattleUI,"UI:setGBattleUI(name, value)","name:string设定名, value:string or boolean设定值",ErrorCode.OK,设置战斗总结UI,"local result = UI:setGBattleUI(name, value)",UI管理,UI,การจัดการUI,UI,UI,UI,新增待译0717,UI管理,UI,Giao Diện,UI,UI,UI,UI,UI,"name:string set name, value:string or boolean set value","name:string set name, value:string or boolean set value","name:string set name, value:string or boolean set value","name:string set name, value:string or boolean set value","name:string set name, value:string or boolean set value","name:string set name, value:string or boolean set value","name:string set name, value:string or boolean set value","name:string set name, value:string or boolean set value","name:string set name, value:string or boolean set value","name:string set name, value:string or boolean set value","name:string set name, value:string or boolean set value","name:string set name, value:string or boolean set value","name:string set name, value:string or boolean set value","name:string set name, value:string or boolean set value","name:string set name, value:string or boolean set value",Set summary UI of fight,設置戰鬥總結UI,UI ตั้งค่าข้อสรุปต่อสู้,Establecer el resumen de la lucha UI,Definir a UI de resumo da luta,Définir l'interface utilisateur sommaire de combat,新增待译0717,戦いの概要UIを設定する,싸움의 설정 요약 UI,Cài đặt UI cơ bản của cuộc chiến,Набор резюме UI борьбы,Kavga'nın kullanıcı arabirimini ayarla,Set UI sintesi di lotta,Set Zusammenfassung UI des Kampfes,Atur pertarungan UI,"local result = UI:setGBattleUI(name, value)"
70002,7,UI管理,world2RadarPos,"UI:world2RadarPos(x, y)","x:number, z:number","ErrorCode.OK, x:number, z:number",世界坐标转换到小地图,"local result = UI:world2RadarPos(x, y)",UI管理,UI,การจัดการUI,UI,UI,UI,新增待译0717,UI管理,UI,Giao Diện,UI,UI,UI,UI,UI,"x:number, z:number","x:number, z:number","x:number, z:number","x:number, z:number","x:number, z:number","x:number, z:number","x:number, z:number","x:number, z:number","x:number, z:number","x:number, z:number","x:number, z:number","x:number, z:number","x:number, z:number","x:number, z:number","x:number, z:number",World coordinate changed to minimap,世界坐標轉換到小地圖,เปลี่ยนพิกัดโลกไปในแมพเล็ก,Coordenadas mundiales cambiadas a mini mapa,Coordenada mundial alterada para mini mapa,World Coordinate changé minicarte,新增待译0717,ワールドの座標がミニマップに変更される,세계는 미니 맵으로 변경 좌표,"
Thế giới tọa độ thay đổi thành mini map",Мир координате изменен на миникарте,Dünya koordinatı küçük harita olarak değiştirildi,Mondiale coordinata cambiato in minimappa,Weltkoordinaten geändert Minimap,Koordinat dunia atau peta berubah menjadi mini map,"local result = UI:world2RadarPos(x, y)"
70003,7,UI管理,world2RadarDist,UI:world2RadarDist(length),length:number,"ErrorCode.OK, length:number",世界长度转换到小地图,local result = UI:world2RadarDist(length),UI管理,UI,การจัดการUI,UI,UI,UI,新增待译0717,UI管理,UI,Giao Diện,UI,UI,UI,UI,UI,length:number,length:number,length:number,length:number,length:number,length:number,length:number,length:number,length:number,length:number,length:number,length:number,length:number,length:number,length:number,Wolrd length changed to minimap,世界長度轉換到小地圖,เปลี่ยนความยาวโลกไปในแมพเล็ก,Se ha cambiado la longitud del mundo a mini mapa.,Comprimento do Mundo alterado para mini mapa,longueur Wolrd changé minicarte,新增待译0717,ワールドの長さがミニマップに変更される,WOLRD 길이는 미니 맵으로 변경,Chiều dài thế giới thay đổi thành mini map,Wolrd длина изменяется на миникарте,Dünya uzunluğu küçük harita olarak değiştirildi,lunghezza wolrd cambiato minimappa,Wolrd Länge geändert Minimap,Ukuran dunia atau peta berubah menjadi mini map,local result = UI:world2RadarDist(length)
70004,7,UI管理,setMinimapRenderMode,UI:setMinimapRenderMode(mode),mode:number,ErrorCode.OK,设置小地图模式。1：45度旋转视角；2：全图俯视角,local result = UI:setMinimapRenderMode(mode),UI管理,UI,การจัดการUI,UI,UI,UI,新增待译0717,UI管理,UI,Giao Diện,UI,UI,UI,UI,UI,mode:number,mode:number,mode:number,mode:number,mode:number,mode:number,mode:number,mode:number,mode:number,mode:number,mode:number,mode:number,mode:number,mode:number,mode:number,Set minimap mode: 1. 45° rotation angle. 2. Full pic overlook angle,設置小地圖模式。 1：45度旋轉視角；2：全圖俯視角,ตั้งค่าโหมดแมพเล็ก 1.มุมมองการหมุน 45 องศา 2.มุมมองจากข้างบนลงมา,Configure el modo de minimapa: 1. Ángulo de rotación de 45 °. 2. Vista de ángulo de imagen completa,Defina o modo minimapa: 1. Ângulo de rotação de 45 °. 2. Imagem completa ângulo vista,Régler le mode minicarte: 1. angle de rotation de 45 °. 2. pic plein angle de surveillance,新增待译0717,ミニマップモードを設定します。1. 45°の回転角度。 2.全面写真のトップビュー,설정 미니 맵 모드 : 1. 45 ° 회전 각도. 2. 전체 그림 간과 각도,Cài đặt mô hình mini map. 1: Góc nhìn 45 độ; 2: góc trên cùng toàn cảnh,Установить режим миникарты: 1. 45 ° угла поворота. Угол обозревает 2. Полный рис,Küçük harita modunu ayarlayın: 1. 45° dönüş açısı. 2. Tam görünüm,Modalità minimappa set: 1. 45 ° angolo di rotazione. Angolo di Overlook 2. pic completa,Set Minimap-Modus: 1. 45 ° Drehwinkel. 2. Voll pic Überschauwinkel,Atur mode mini map: 1. Sudut rotasi 45 °. 2. Sudut pandang gambar yang penuh,local result = UI:setMinimapRenderMode(mode)
70005,7,UI管理,setShapeLine,"UI:setShapeLine(uiname, p1x, p1y, p2x, p2y)","uiname:string, p1x:number, p1y:number, p2x:number, p2y:number",ErrorCode.OK,地图标记形状设置，设置成线条,"local result = UI:setShapeLine(uiname, p1x, p1y, p2x, p2y)",UI管理,UI,การจัดการUI,UI,UI,UI,新增待译0717,UI管理,UI,Giao Diện,UI,UI,UI,UI,UI,"uiname:string, p1x:number, p1y:number, p2x:number, p2y:number","uiname:string, p1x:number, p1y:number, p2x:number, p2y:number","uiname:string, p1x:number, p1y:number, p2x:number, p2y:number","uiname:string, p1x:number, p1y:number, p2x:number, p2y:number","uiname:string, p1x:number, p1y:number, p2x:number, p2y:number","uiname:string, p1x:number, p1y:number, p2x:number, p2y:number","uiname:string, p1x:number, p1y:number, p2x:number, p2y:number","uiname:string, p1x:number, p1y:number, p2x:number, p2y:number","uiname:string, p1x:number, p1y:number, p2x:number, p2y:number","uiname:string, p1x:number, p1y:number, p2x:number, p2y:number","uiname:string, p1x:number, p1y:number, p2x:number, p2y:number","uiname:string, p1x:number, p1y:number, p2x:number, p2y:number","uiname:string, p1x:number, p1y:number, p2x:number, p2y:number","uiname:string, p1x:number, p1y:number, p2x:number, p2y:number","uiname:string, p1x:number, p1y:number, p2x:number, p2y:number",Shape setting of map marks. Set as line,地圖標記形狀設置，設置成線條,การตั้งค่ารูปร่างเครื่องหมายแมพ ตั้งค่าเป็นเส้น,Ajuste de la forma de las marcas del mapa. Establecer como línea,Configuração de forma de marcas de mapa. Definir como linha,Forme Réglage des marques de carte. Définir comme ligne,新增待译0717,マップのマークの形の設定。線に設定させる,지도 마크의 설정을 모양. 라인으로 설정,"Cài đặt hình dạng đánh dấu Map, được đặt thành các dòng",Форма настройки карты знаков. Установить как линии,Harita işaretlerinin şekil ayarı. Çizgi olarak ayarla,Forma impostazione dei marchi della mappa. Imposta come linea,Form-Einstellung von Kartenmarkierungen. Stellen Sie als Linie,Pengaturan bentuk pada tanda peta. Tetapkan sbg garis,"local result = UI:setShapeLine(uiname, p1x, p1y, p2x, p2y)"
70006,7,UI管理,setShapeCircle,"UI:setShapeCircle(uiname, x, y, radius)","uiname:string, x:number, y:number, radius:number",ErrorCode.OK,地图标记形状设置，设置成圆,"local result = UI:setShapeCircle(uiname, x, y, radius)",UI管理,UI,การจัดการUI,UI,UI,UI,新增待译0717,UI管理,UI,Giao Diện,UI,UI,UI,UI,UI,"uiname:string, x:number, y:number, radius:number","uiname:string, x:number, y:number, radius:number","uiname:string, x:number, y:number, radius:number","uiname:string, x:number, y:number, radius:number","uiname:string, x:number, y:number, radius:number","uiname:string, x:number, y:number, radius:number","uiname:string, x:number, y:number, radius:number","uiname:string, x:number, y:number, radius:number","uiname:string, x:number, y:number, radius:number","uiname:string, x:number, y:number, radius:number","uiname:string, x:number, y:number, radius:number","uiname:string, x:number, y:number, radius:number","uiname:string, x:number, y:number, radius:number","uiname:string, x:number, y:number, radius:number","uiname:string, x:number, y:number, radius:number",Shape setting of map marks. Set as circle,地圖標記形狀設置，設置成圓,การตั้งค่ารูปร่างเครื่องหมายแมพ ตั้งค่าเป็นวงกลม,Ajuste de la forma de las marcas del mapa. Establecer como circulo,Configuração de forma de marcas de mapa. Definir como círculo,Forme Réglage des marques de carte. Définir comme cercle,新增待译0717,マップのマークの形の設定。丸に設定させる,지도 마크의 설정을 모양. 원으로 설정,"Cài đặt hình dạng đánh dấu Map, đặt thành vòng tròn",Форма настройки карты знаков. Установить как круг,Harita işaretlerinin şekil ayarı. Daire olarak ayarla,Forma impostazione dei marchi della mappa. Imposta come cerchio,Form-Einstellung von Kartenmarkierungen. Stellen Sie als Kreis,Pengaturan bentuk pada tanda peta. Tetapkan sbg lingkaran,"local result = UI:setShapeCircle(uiname, x, y, radius)"
70007,7,UI管理,ShowScreenEffect,"UI:ShowScreenEffect(type, isloop, incspeed)","type:number效果类型, isloop:boolean是否循环, incspeed:number增速",ErrorCode.OK,显示屏幕效果,"local result = UI:ShowScreenEffect(type, isloop, incspeed)",UI管理,UI,การจัดการUI,UI,UI,UI,新增待译0717,UI管理,UI,Giao Diện,UI,UI,UI,UI,UI,"type:number effect type, isloop:boolean if it is a loop, incspeed:number speed increasing","type:number effect type, isloop:boolean if it is a loop, incspeed:number speed increasing","type:number effect type, isloop:boolean if it is a loop, incspeed:number speed increasing","type:number effect type, isloop:boolean if it is a loop, incspeed:number speed increasing","type:number effect type, isloop:boolean if it is a loop, incspeed:number speed increasing","type:number effect type, isloop:boolean if it is a loop, incspeed:number speed increasing","type:number effect type, isloop:boolean if it is a loop, incspeed:number speed increasing","type:number effect type, isloop:boolean if it is a loop, incspeed:number speed increasing","type:number effect type, isloop:boolean if it is a loop, incspeed:number speed increasing","type:number effect type, isloop:boolean if it is a loop, incspeed:number speed increasing","type:number effect type, isloop:boolean if it is a loop, incspeed:number speed increasing","type:number effect type, isloop:boolean if it is a loop, incspeed:number speed increasing","type:number effect type, isloop:boolean if it is a loop, incspeed:number speed increasing","type:number effect type, isloop:boolean if it is a loop, incspeed:number speed increasing","type:number effect type, isloop:boolean if it is a loop, incspeed:number speed increasing",Display screen effect,顯示屏幕效果,แสดงเอฟเฟ็กต์หน้าจอ,Efecto de pantalla,Efeito de Tela,Affichage effet de l'écran,新增待译0717,表示画面効果,디스플레이 화면 효과,Xem hiệu ứng màn hình,Дисплей экранный эффект,Ekran etkisi,Effetto schermo di visualizzazione,Display-Screen-Effekt,Efek tampilan layar,"local result = UI:ShowScreenEffect(type, isloop, incspeed)"
80001,8,聊天系统,sendChat,Chat:sendChat(content),"content:string, type:number0表示普通聊天，1表示系统消息",ErrorCode.OK,发送聊天消息,local result = Chat:sendChat(content),聊天系統,Chat,ระบบแชท,Charla,Chat,Bavarder,新增待译0717,チャット,잡담,Giao Diện,чат,Sohbet,Chiacchierare,Plaudern,Chat,"content:string, type:number0 means chat，1 means system message","content:string, type:number0 means chat，1 means system message","content:string, type:number0 means chat，1 means system message","content:string, type:number0 means chat，1 means system message","content:string, type:number0 means chat，1 means system message","content:string, type:number0 means chat，1 means system message","content:string, type:number0 means chat，1 means system message","content:string, type:number0 means chat，1 means system message","content:string, type:number0 means chat，1 means system message","content:string, type:number0 means chat，1 means system message","content:string, type:number0 means chat，1 means system message","content:string, type:number0 means chat，1 means system message","content:string, type:number0 means chat，1 means system message","content:string, type:number0 means chat，1 means system message","content:string, type:number0 means chat，1 means system message",Send chat message,發送聊天消息,ส่งข้อความแชท,Enviar mensaje de chat,Enviar mensagem de chat,Envoyer un message dans le chat,新增待译0717,チャットメッセージを送信する,채팅 메시지 보내기,Gửi văn bản chat,Отправить сообщение чата,Sohbet mesajı gönder,Invia un messaggio chat,Senden Chat-Nachricht,Kirim pesan chat,local result = Chat:sendChat(content)
80002,8,聊天系统,sendSystemMsg,Chat:sendSystemMsg(content),"content:string, type:number0表示普通聊天，1表示系统消息",ErrorCode.OK,发送系统消息,local result = Chat:sendSystemMsg(content),聊天系統,Chat,ระบบแชท,Charla,Chat,Bavarder,新增待译0717,チャット,잡담,Chat,чат,Sohbet,Chiacchierare,Plaudern,Chat,"content:string, type:number0 means chat，1 means system message","content:string, type:number0 means chat，1 means system message","content:string, type:number0 means chat，1 means system message","content:string, type:number0 means chat，1 means system message","content:string, type:number0 means chat，1 means system message","content:string, type:number0 means chat，1 means system message","content:string, type:number0 means chat，1 means system message","content:string, type:number0 means chat，1 means system message","content:string, type:number0 means chat，1 means system message","content:string, type:number0 means chat，1 means system message","content:string, type:number0 means chat，1 means system message","content:string, type:number0 means chat，1 means system message","content:string, type:number0 means chat，1 means system message","content:string, type:number0 means chat，1 means system message","content:string, type:number0 means chat，1 means system message",Send system message,發送系統消息,ส่งข้อความระบบ,Enviar mensaje del sistema,Enviar mensagem do sistema,Envoyer un message système,新增待译0717,システムメッセージを送信する,시스템 메시지 보내기,Gửi văn bản chat,Отправить сообщение системы,Sistem mesajı gönder,Invia un messaggio di sistema,Senden Systemmeldung,Kirim pesan chat,local result = Chat:sendSystemMsg(content)
90001,9,组队管理,getNumTeam,Team:getNumTeam(),nil,"ErrorCode.OK, num:number",获取队伍数量,local result = Team:getNumTeam(),組隊管理,Team,การจัดการทีม,Equipo,Equipe,Équipe,新增待译0717,チーム,팀,Nhóm,команда,Takım,Squadra,Mannschaft,Team,nil,nil,nil,nil,nil,nil,nil,nil,nil,nil,nil,nil,nil,nil,nil,Get the number of teams,獲取隊伍數量,รับจำนวนของทีม,Consigue el número de equipos.,Obter o número de equipes,Obtenez le nombre d'équipes,新增待译0717,チーム数を取得する,팀의 수를 가져옵니다,Nhận số nhóm,Получить количество команд,Takım sayısını al,Ottenere il numero di squadre,Gibt die Anzahl der Mannschaften,Lihat jumlah team,local result = Team:getNumTeam()
90002,9,组队管理,getTeamPlayerNum,"Team:getTeamPlayerNum(teamid, alive)","teamid:number队伍ID，默认全部, alive:number是否存活，默认全部","ErrorCode.OK, num:number队伍玩家数量",获取指定队伍玩家,"local result = Team:getTeamPlayerNum(teamid, alive)",組隊管理,Team,การจัดการทีม,Equipo,Equipe,Équipe,新增待译0717,チーム,팀,Nhóm,команда,Takım,Squadra,Mannschaft,Team,"teamid:number team ID, default all, alive:number whether survive, default all","teamid:number team ID, default all, alive:number whether survive, default all","teamid:number team ID, default all, alive:number whether survive, default all","teamid:number team ID, default all, alive:number whether survive, default all","teamid:number team ID, default all, alive:number whether survive, default all","teamid:number team ID, default all, alive:number whether survive, default all","teamid:number team ID, default all, alive:number whether survive, default all","teamid:number team ID, default all, alive:number whether survive, default all","teamid:number team ID, default all, alive:number whether survive, default all","teamid:number team ID, default all, alive:number whether survive, default all","teamid:number team ID, default all, alive:number whether survive, default all","teamid:number team ID, default all, alive:number whether survive, default all","teamid:number team ID, default all, alive:number whether survive, default all","teamid:number team ID, default all, alive:number whether survive, default all","teamid:number team ID, default all, alive:number whether survive, default all",Get the designate player team,獲取指定隊伍玩家,รับผู้เล่นที่กำหนดทีม,Consigue el equipo de jugadores designados.,Obter o time de jogadores designados,Obtenez l'équipe de joueur désigné,新增待译0717,指定選手チームを獲得する,지정 선수 팀을 얻기,Nhận nhóm người chơi chỉ định,Получить команду Назначенный игрок,Belirlenen oyuncu ekibini alın,Prendi il team di giocatore designato,Holen Sie sich das designierte Spieler Team,Lihat team pemain yg ditunjuk,"local result = Team:getTeamPlayerNum(teamid, alive)"
90003,9,组队管理,getTeamPlayers,"Team:getTeamPlayers(teamid, alive)","teamid:number队伍ID，默认全部, alive:number是否存活，默认全部","ErrorCode.OK, num:number队伍玩家数量, array:table成员uin数组",获取指定队伍玩家,"local result = Team:getTeamPlayers(teamid, alive)",組隊管理,Team,การจัดการทีม,Equipo,Equipe,Équipe,新增待译0717,チーム,팀,Nhóm,команда,Takım,Squadra,Mannschaft,Team,"teamid:number team ID, default all, alive:number whether survive, default all","teamid:number team ID, default all, alive:number whether survive, default all","teamid:number team ID, default all, alive:number whether survive, default all","teamid:number team ID, default all, alive:number whether survive, default all","teamid:number team ID, default all, alive:number whether survive, default all","teamid:number team ID, default all, alive:number whether survive, default all","teamid:number team ID, default all, alive:number whether survive, default all","teamid:number team ID, default all, alive:number whether survive, default all","teamid:number team ID, default all, alive:number whether survive, default all","teamid:number team ID, default all, alive:number whether survive, default all","teamid:number team ID, default all, alive:number whether survive, default all","teamid:number team ID, default all, alive:number whether survive, default all","teamid:number team ID, default all, alive:number whether survive, default all","teamid:number team ID, default all, alive:number whether survive, default all","teamid:number team ID, default all, alive:number whether survive, default all",Get the designate player team,獲取指定隊伍玩家,รับผู้เล่นที่กำหนดทีม,Consigue el equipo de jugadores designados.,Obter o time de jogadores designados,Obtenez l'équipe de joueur désigné,新增待译0717,指定選手チームを獲得する,지정 선수 팀을 얻기,Nhận nhóm người chơi chỉ định,Получить команду Назначенный игрок,Belirlenen oyuncu ekibini alın,Prendi il team di giocatore designato,Holen Sie sich das designierte Spieler Team,Lihat team pemain yg ditunjuk,"local result = Team:getTeamPlayers(teamid, alive)"
90004,9,组队管理,randomTeamPlayer,"Team:randomTeamPlayer(teamid, alive)","teamid:number队伍ID，默认全部, alive:number是否存活，默认全部","ErrorCode.OK, uin:number随机出玩家的uin",随机一名玩家,"local result = Team:randomTeamPlayer(teamid, alive)",組隊管理,Team,การจัดการทีม,Equipo,Equipe,Équipe,新增待译0717,チーム,팀,Nhóm,команда,Takım,Squadra,Mannschaft,Team,"teamid:number team ID, default all, alive:number whether survive, default all","teamid:number team ID, default all, alive:number whether survive, default all","teamid:number team ID, default all, alive:number whether survive, default all","teamid:number team ID, default all, alive:number whether survive, default all","teamid:number team ID, default all, alive:number whether survive, default all","teamid:number team ID, default all, alive:number whether survive, default all","teamid:number team ID, default all, alive:number whether survive, default all","teamid:number team ID, default all, alive:number whether survive, default all","teamid:number team ID, default all, alive:number whether survive, default all","teamid:number team ID, default all, alive:number whether survive, default all","teamid:number team ID, default all, alive:number whether survive, default all","teamid:number team ID, default all, alive:number whether survive, default all","teamid:number team ID, default all, alive:number whether survive, default all","teamid:number team ID, default all, alive:number whether survive, default all","teamid:number team ID, default all, alive:number whether survive, default all",Random Player,隨機一名玩家,ผู้เล่นสุ่มคนหนึ่ง,Jugador aleatorio,Jogador Aleatório,Joueur aléatoire,新增待译0717,ランダムのプレイヤー一人様,랜덤 플레이어,Người chơi ngẫu nhiên,Случайные игрока,Rastgele Oyuncu,casuale Player,zufällige Spieler,Random Player,"local result = Team:randomTeamPlayer(teamid, alive)"
90005,9,组队管理,setTeamScore,"Team:setTeamScore(teamid, s)","teamid:number, score:number",ErrorCode.OK,设置组队分数,"local result = Team:setTeamScore(teamid, s)",組隊管理,Team,การจัดการทีม,Equipo,Equipe,Équipe,新增待译0717,チーム,팀,Nhóm,команда,Takım,Squadra,Mannschaft,Team,"teamid:number, score:number","teamid:number, score:number","teamid:number, score:number","teamid:number, score:number","teamid:number, score:number","teamid:number, score:number","teamid:number, score:number","teamid:number, score:number","teamid:number, score:number","teamid:number, score:number","teamid:number, score:number","teamid:number, score:number","teamid:number, score:number","teamid:number, score:number","teamid:number, score:number",Set the team score,設置組隊分數,ตั้งค่าคะแนนของทีม,Establecer la puntuación del equipo,Definir a pontuação da equipe,Réglez le score de l'équipe,新增待译0717,チームの得点を設定する,팀 점수를 설정,Cài đặt điểm số nhóm,Установите счет команды,Takım puanını ayarla,Impostare il punteggio di squadra,Stellen Sie die Mannschaftswertung,Atur skor team,"local result = Team:setTeamScore(teamid, s)"
90006,9,组队管理,getTeamScore,Team:getTeamScore(teamid),teamid:number,"ErrorCode.OK, score:number",获取组队分数,local result = Team:getTeamScore(teamid),組隊管理,Team,การจัดการทีม,Equipo,Equipe,Équipe,新增待译0717,チーム,팀,Nhóm,команда,Takım,Squadra,Mannschaft,Team,teamid:number,teamid:number,teamid:number,teamid:number,teamid:number,teamid:number,teamid:number,teamid:number,teamid:number,teamid:number,teamid:number,teamid:number,teamid:number,teamid:number,teamid:number,Get the team score,獲取組隊分數,รับคะแนนของทีม,Obtener puntaje del equipo,Obter a pontuação da equipe,Obtenez le score de l'équipe,新增待译0717,チームの得点を取得する,팀 점수를 얻을,Nhận điểm số nhóm,Получить счет команды,Takım skorunu al,Prendi il punteggio di squadra,Holen Sie sich das Team der Gäste,Lihat skor team,local result = Team:getTeamScore(teamid)
90007,9,组队管理,addTeamScore,"Team:addTeamScore(teamid, score)","teamid:number, score:number",ErrorCode.OK,增加队伍分数,"local result = Team:addTeamScore(teamid, score)",組隊管理,Team,การจัดการทีม,Equipo,Equipe,Équipe,新增待译0717,チーム,팀,Nhóm,команда,Takım,Squadra,Mannschaft,Team,"teamid:number, score:number","teamid:number, score:number","teamid:number, score:number","teamid:number, score:number","teamid:number, score:number","teamid:number, score:number","teamid:number, score:number","teamid:number, score:number","teamid:number, score:number","teamid:number, score:number","teamid:number, score:number","teamid:number, score:number","teamid:number, score:number","teamid:number, score:number","teamid:number, score:number",Increase the team score,增加隊伍分數,เพิ่มคะแนนของทีม,Aumentar el puntaje del equipo,Aumentar a pontuação da equipe,Augmenter le score de l'équipe,新增待译0717,チームの得点を増やす,팀 점수를 증가,Thêm điểm số nhóm,Увеличение результата команды,Takım puanını arttır,Aumentare il punteggio di squadra,Erhöhen Sie die Teamwertung,Tambah skor team,"local result = Team:addTeamScore(teamid, score)"
90008,9,组队管理,setTeamResults,"Team:setTeamResults(teamid, result)",teamid:number result?number,ErrorCode.OK,设置队伍胜负,"local result = Team:setTeamResults(teamid, result)",組隊管理,Team,การจัดการทีม,Equipo,Equipe,Équipe,新增待译0717,チーム,팀,Nhóm,команда,Takım,Squadra,Mannschaft,Team,teamid:number result?number,teamid:number result?number,teamid:number result?number,teamid:number result?number,teamid:number result?number,teamid:number result?number,teamid:number result?number,teamid:number result?number,teamid:number result?number,teamid:number result?number,teamid:number result?number,teamid:number result?number,teamid:number result?number,teamid:number result?number,teamid:number result?number,Set the win-los of team,設置隊伍勝負,ตั้งค่าชนะและแพ้ของทีม,Establecer ganar el equipo,Defina o win-los da equipe,Réglez la victoire de l'équipe-los,新增待译0717,チームの勝敗を設定する,팀의 승리 - 로스 설정,Cài đặt thắng- thua của nhóm,Установите выигрыш Лос-команды,Takımın kazan-atlarını ayarla,Impostare il win-los della squadra,Stellen Sie die Win-los von Team,Atur team yg menang/kalah,"local result = Team:setTeamResults(teamid, result)"
90009,9,组队管理,getTeamResults,Team:getTeamResults(teamid),teamid:number,"ErrorCode.OK, teamresult:number",获取当前队伍胜负,local result = Team:getTeamResults(teamid),組隊管理,Team,การจัดการทีม,Equipo,Equipe,Équipe,新增待译0717,チーム,팀,Nhóm,команда,Takım,Squadra,Mannschaft,Team,teamid:number,teamid:number,teamid:number,teamid:number,teamid:number,teamid:number,teamid:number,teamid:number,teamid:number,teamid:number,teamid:number,teamid:number,teamid:number,teamid:number,teamid:number,Get the current win-los of team,獲取當前隊伍勝負,รับสภาพชนะและแพ้ของทีม,Consigue el equipo actual de ganarlos.,Receba o atual win-los da equipe,Obtenez le courant gagnant-los de l'équipe,新增待译0717,チームの今の勝利を取る,팀의 현재 윈 - 로스 받기,Xem thắng- thua của nhóm,Получить текущий выигрыш Лос-команды,Takımın şu anki kazancını kazan,Prendi l'attuale win-los della squadra,Holen Sie sich die aktuelle Win-los von Team,Lihat team yg menang/kalah saat ini,local result = Team:getTeamResults(teamid)
90010,9,组队管理,setTeamPlayersResults,"Team:setTeamPlayersResults(teamid, result)","teamid:number, result:number",ErrorCode.OK,设置玩家的队伍胜负,"local result = Team:setTeamPlayersResults(teamid, result)",組隊管理,Team,การจัดการทีม,Equipo,Equipe,Équipe,新增待译0717,チーム,팀,Nhóm,команда,Takım,Squadra,Mannschaft,Team,"teamid:number, result:number","teamid:number, result:number","teamid:number, result:number","teamid:number, result:number","teamid:number, result:number","teamid:number, result:number","teamid:number, result:number","teamid:number, result:number","teamid:number, result:number","teamid:number, result:number","teamid:number, result:number","teamid:number, result:number","teamid:number, result:number","teamid:number, result:number","teamid:number, result:number",Set the win-los of player team,設置玩家的隊伍勝負,ตั้งค่าชนะและแพ้ของทีมของผู้เล่น,Set gánalos los jugadores del equipo,Defina o win-los da equipe de jogadores,Réglez le gagnant-los de l'équipe des joueurs,新增待译0717,選手チームの勝敗を設定する,플레이어 팀의 승리 - 로스 설정,Cài đặt thắng- thua của người chơi,Установите выигрыш Лос-игрок команды,Oyuncu takımının kazancını ayarlayın,Impostare il win-los del team giocatore,Stellen Sie die Win-los von Spieler-Team,Atur team yg menang/kalah,"local result = Team:setTeamPlayersResults(teamid, result)"
90011,9,组队管理,setTeamDieTimes,"Team:setTeamDieTimes(teamid, times)","teamid:number, times:number",ErrorCode.OK,设置队伍总死亡数,"local result = Team:setTeamDieTimes(teamid, times)",組隊管理,Team,การจัดการทีม,Equipo,Equipe,Équipe,新增待译0717,チーム,팀,Nhóm,команда,Takım,Squadra,Mannschaft,Team,"teamid:number, times:number","teamid:number, times:number","teamid:number, times:number","teamid:number, times:number","teamid:number, times:number","teamid:number, times:number","teamid:number, times:number","teamid:number, times:number","teamid:number, times:number","teamid:number, times:number","teamid:number, times:number","teamid:number, times:number","teamid:number, times:number","teamid:number, times:number","teamid:number, times:number",Set the total deaths of team,設置隊伍總死亡數,ตั้งค่าจำนวนผู้เสียชีวิตทั้งหมดในทีม,Establecer las muertes totales del equipo.,Definir o total de mortes da equipe,Régler la mort au total de l'équipe,新增待译0717,チームの総計死亡人数を設定する,팀의 총 사망자 설정,Cài đặt số lượng người chết trong nhóm,Установить общее число смертей от команды,Takımın toplam ölümünü ayarla,Impostare le morti totali di squadra,Stellen Sie die Gesamtzahl der Todesfälle von Team,Atur tingkat kematian dari team,"local result = Team:setTeamDieTimes(teamid, times)"
90012,9,组队管理,getTeamDieTimes,Team:getTeamDieTimes(teamid),teamid:number,"ErrorCode.OK, times:number",获取队伍总死亡数,local result = Team:getTeamDieTimes(teamid),組隊管理,Team,การจัดการทีม,Equipo,Equipe,Équipe,新增待译0717,チーム,팀,Nhóm,команда,Takım,Squadra,Mannschaft,Team,teamid:number,teamid:number,teamid:number,teamid:number,teamid:number,teamid:number,teamid:number,teamid:number,teamid:number,teamid:number,teamid:number,teamid:number,teamid:number,teamid:number,teamid:number,Get the total deaths of team,獲取隊伍總死亡數,รับจำนวนผู้เสียชีวิตทั้งหมดในทีม,Consigue las muertes totales del equipo,Obter o total de mortes da equipe,Obtenez le nombre total de décès de l'équipe,新增待译0717,チームの総計死亡人数を獲得する。,팀의 총 사망자를 가져옵니다,Xem số người chết trong nhóm,Получить общее число смертей от команды,Takımın toplam ölümünü al,I morti totali di squadra,Holen Sie sich die Gesamtzahl der Todesfälle von Team,Lihat tingkat kematian team,local result = Team:getTeamDieTimes(teamid)
90013,9,组队管理,addTeamDieTimes,Team:addTeamDieTimes(teamid),teamid:number,ErrorCode.OK,增加队伍总死亡数,local result = Team:addTeamDieTimes(teamid),組隊管理,Team,การจัดการทีม,Equipo,Equipe,Équipe,新增待译0717,チーム,팀,Nhóm,команда,Takım,Squadra,Mannschaft,Team,teamid:number,teamid:number,teamid:number,teamid:number,teamid:number,teamid:number,teamid:number,teamid:number,teamid:number,teamid:number,teamid:number,teamid:number,teamid:number,teamid:number,teamid:number,Increase the total deaths of team,增加隊伍總死亡數,เพิ่มจำนวนผู้เสียชีวิตทั้งหมดในทีม,Aumentar las muertes totales del equipo.,Aumentar o total de mortes da equipe,Augmenter la mort au total de l'équipe,新增待译0717,チームの総計死亡人数を増やす。,팀의 총 사망을 증가,Thêm số người chết trong nhóm,Увеличение общего числа смертей от команды,Takımın toplam ölümünü arttır,Aumentare le morti totali di squadra,Erhöhen Sie die Gesamtzahl der Todesfälle von Team,Tambah tingkat kematian team,local result = Team:addTeamDieTimes(teamid)
90014,9,组队管理,changePlayerTeam,"Game:changePlayerTeam(uin, teamid)","uin:number, teamid:number",ErrorCode.OK,改变玩家队伍,"local result = Team:changePlayerTeam(uin, teamid)",組隊管理,Team,การจัดการทีม,Equipo,Equipe,Équipe,新增待译0717,チーム,팀,Nhóm,команда,Takım,Squadra,Mannschaft,Team,"uin:number, teamid:number","uin:number, teamid:number","uin:number, teamid:number","uin:number, teamid:number","uin:number, teamid:number","uin:number, teamid:number","uin:number, teamid:number","uin:number, teamid:number","uin:number, teamid:number","uin:number, teamid:number","uin:number, teamid:number","uin:number, teamid:number","uin:number, teamid:number","uin:number, teamid:number","uin:number, teamid:number",Change player's team,改變玩家隊伍,เปลี่ยนทีมของผู้เล่น,Cambiar Equipo de Jugador,Mudar a equipe do jogador,L'équipe de changement joueur,新增待译0717,プレイヤーのチームを変える,변경 플레이어의 팀,Thay đổi nhóm của người chơi,Команда Изменить игрока,Oyuncunun takımını değiştir,squadra Cambio del giocatore,Änderung des Spielerteams,Ganti team,"local result = Team:changePlayerTeam(uin, teamid)"
100001,10,道具管理,getItemName,Item:getItemName(itemid),itemid:number,"ErrorCode.OK, name:string",获取道具名称,local result = Item:getItemName(itemid),道具管理,Props,การจัดการไอเทม,Objetos,Props,Props,新增待译0717,道具管理,소품,Công Cụ,Реквизит,Props,puntelli,Requisiten,Alat,itemid:number,itemid:number,itemid:number,itemid:number,itemid:number,itemid:number,itemid:number,itemid:number,itemid:number,itemid:number,itemid:number,itemid:number,itemid:number,itemid:number,itemid:number,Get the name of item,獲取道具名稱,รับชื่อของไอเทม,Obtener nombre del elemento,Obter o nome do item,Obtenez le nom de l'article,新增待译0717,アイテム名を取得する,항목의 이름을 가져옵니다,Xem tên công cụ,Получить имя элемента,Öğenin adını al,Ottenere il nome di voce,Holen Sie sich den Namen des Elements,Ambil nama item,local result = Item:getItemName(itemid)
100002,10,道具管理,getItemId,Item:getItemId(objid),objid:number,"ErrorCode.OK, itemid:number",获取物品ID,local result = Item:getItemId(objid),道具管理,Props,การจัดการไอเทม,Objetos,Props,Props,新增待译0717,道具管理,소품,Công Cụ,Реквизит,Props,puntelli,Requisiten,Alat,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,Get the ID of item,獲取物品ID,รับIDของไอเทม,Obtener ID de artículo,Obter o ID do item,Obtenez l'ID de l'article,新增待译0717,アイテムのIDを取得,항목의 ID를 가져옵니다,Nhận ID của vật phẩm,Получить идентификатор элемента,Öğenin kimliğini al,Ottenere l'ID della voce,Holen Sie sich das ID von Artikel,Ambil ID item,local result = Item:getItemId(objid)
100003,10,道具管理,getDropItemNum,Item:getDropItemNum(objid),objid:number,"ErrorCode.OK, itemnum:number",获取掉落物数量,local result = Item:getDropItemNum(objid),道具管理,Props,การจัดการไอเทม,Objetos,Props,Props,新增待译0717,道具管理,소품,Công Cụ,Реквизит,Props,puntelli,Requisiten,Alat,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,objid:number,Get the number of dorps,獲取掉落物數量,รับจำนวนของดรอป,Consigue el número de dorps,Obter o número de dorps,Obtenez le nombre de Dorps,新增待译0717,落とし物の数量を取得する,dorps의 수를 가져옵니다,Nhận số vật phẩm rơi,Получить количество dorps,Yurt sayısını al,Prendi il numero di dorps,Gibt die Anzahl der dorps,Ambil jumlah drops,local result = Item:getDropItemNum(objid)
110001,11,背包管理,getBackpackBarIDRange,Backpack:getBackpackBarIDRange(bartype),bartype:number背包类型，快捷栏、存储栏、装备栏,"ErrorCode.OK, begid:number道具格起始ID, endid:number道具格末尾ID",获取道具背包栏ID范围(起始ID~结束ID),local result = Backpack:getBackpackBarIDRange(bartype),背包管理,Backpack,การจัดการเป้,Mochila,Mochila,Sac à dos,新增待译0717,鞄管理,배낭,Túi,Рюкзак,Sırt çantası,Zaino,Rucksack,Tas,"bartype:number backpack type, shortcut bar, saving bar,equipment bar","bartype:number backpack type, shortcut bar, saving bar,equipment bar","bartype:number backpack type, shortcut bar, saving bar,equipment bar","bartype:number backpack type, shortcut bar, saving bar,equipment bar","bartype:number backpack type, shortcut bar, saving bar,equipment bar","bartype:number backpack type, shortcut bar, saving bar,equipment bar","bartype:number backpack type, shortcut bar, saving bar,equipment bar","bartype:number backpack type, shortcut bar, saving bar,equipment bar","bartype:number backpack type, shortcut bar, saving bar,equipment bar","bartype:number backpack type, shortcut bar, saving bar,equipment bar","bartype:number backpack type, shortcut bar, saving bar,equipment bar","bartype:number backpack type, shortcut bar, saving bar,equipment bar","bartype:number backpack type, shortcut bar, saving bar,equipment bar","bartype:number backpack type, shortcut bar, saving bar,equipment bar","bartype:number backpack type, shortcut bar, saving bar,equipment bar",Get the range of backpack bar's ID(Start ID~End ID),獲取道具背包欄ID範圍(起始ID~結束ID),รับช่วง ID ของแถบไอเทมของเป้(ID เริ่มต้น~ID สุดท้าย),Obtenga la gama de ID de mochila (ID de inicio ~ ID de finalización),Obter a gama de barra de mochila ID(Start ID~End ID),Obtenez la gamme de ID barre de sac à dos (Start ID ~ ID de fin),新增待译0717,バックパックバーのIDの範囲を取得します（開始ID〜終了ID）,배낭 바의 ID의 범위를 가져옵니다 (시작 ID ~ 종료 ID),Nhận ID của túi công cụ trong phạm vi ( ID bắt đầu - ID kết thúc),Получить диапазон ID рюкзак бара (Start ID ~ Конец ID),Sırt çantası çubuğunun kimliğinin aralığını elde et (ID başla ~  ID Bitiş),Ottenere la gamma di ID di bar zaino (Inizio ID ~ Fine ID),Holen Sie sich das Angebot an Rucksack Leiste des ID (Start-ID ~ Ende ID),Lihat ID dari berbagai tas (ID awal-ID akhir),local result = Backpack:getBackpackBarIDRange(bartype)
110002,11,背包管理,getBackpackBarSize,Backpack:getBackpackBarSize(bartype),bartype:number,"ErrorCode.OK, size:number",获取道具背包栏大小,local result = Backpack:getBackpackBarSize(bartype),背包管理,Backpack,การจัดการเป้,Mochila,Mochila,Sac à dos,新增待译0717,鞄管理,배낭,Túi,Рюкзак,Sırt çantası,Zaino,Rucksack,Tas,bartype:number,bartype:number,bartype:number,bartype:number,bartype:number,bartype:number,bartype:number,bartype:number,bartype:number,bartype:number,bartype:number,bartype:number,bartype:number,bartype:number,bartype:number,Get the size of backpack bar,獲取道具背包欄大小,รับขนาดแถบไอเทมของเป้,Consigue el tamaño de la barra de mochila.,Obter o tamanho da barra de mochila,Obtenir la taille de la barre de sac à dos,新增待译0717,バックパックバーのサイズを取得する,배낭 바의 크기를 가져옵니다,Nhận kích thước của túi,Получить размер рюкзака бар,Sırt çantası çubuğunun boyutunu al,Ottenere la dimensione della barra di zaino,Ermittelt die Größe des Rucksacks Bar,Lihat ukuran tas,local result = Backpack:getBackpackBarSize(bartype)
110003,11,背包管理,setGridItem,"Backpack:setGridItem(playerid, gridid, itemid, num, durability)","playerid:number, gridid:number道具格ID, itemid:number, num:number默认1, durability:number耐久度，默认满耐久",ErrorCode.OK,设置背包格道具,"local result = Backpack:setGridItem(playerid, gridid, itemid, num, durability)",背包管理,Backpack,การจัดการเป้,Mochila,Mochila,Sac à dos,新增待译0717,鞄管理,배낭,Túi,Рюкзак,Sırt çantası,Zaino,Rucksack,Tas,"playerid:number, gridid:number item slot ID, itemid:number, num:number default 1, durability:number durability, default durability max","playerid:number, gridid:number item slot ID, itemid:number, num:number default 1, durability:number durability, default durability max","playerid:number, gridid:number item slot ID, itemid:number, num:number default 1, durability:number durability, default durability max","playerid:number, gridid:number item slot ID, itemid:number, num:number default 1, durability:number durability, default durability max","playerid:number, gridid:number item slot ID, itemid:number, num:number default 1, durability:number durability, default durability max","playerid:number, gridid:number item slot ID, itemid:number, num:number default 1, durability:number durability, default durability max","playerid:number, gridid:number item slot ID, itemid:number, num:number default 1, durability:number durability, default durability max","playerid:number, gridid:number item slot ID, itemid:number, num:number default 1, durability:number durability, default durability max","playerid:number, gridid:number item slot ID, itemid:number, num:number default 1, durability:number durability, default durability max","playerid:number, gridid:number item slot ID, itemid:number, num:number default 1, durability:number durability, default durability max","playerid:number, gridid:number item slot ID, itemid:number, num:number default 1, durability:number durability, default durability max","playerid:number, gridid:number item slot ID, itemid:number, num:number default 1, durability:number durability, default durability max","playerid:number, gridid:number item slot ID, itemid:number, num:number default 1, durability:number durability, default durability max","playerid:number, gridid:number item slot ID, itemid:number, num:number default 1, durability:number durability, default durability max","playerid:number, gridid:number item slot ID, itemid:number, num:number default 1, durability:number durability, default durability max",Set the backpack,設置背包格道具,ตั้งค่าไอเทมในเป้,Pon la mochila,Coloque a mochila,Mettre le sac à dos,新增待译0717,バックパックをセットする,배낭을 설정,Cài đặt ô công cụ trong túi,Установите рюкзак,Sırt çantasını ayarla,Impostare lo zaino,Stellen Sie den Rucksack,Atur settingan tas,"local result = Backpack:setGridItem(playerid, gridid, itemid, num, durability)"
110004,11,背包管理,removeGridItem,"Backpack:removeGridItem(playerid, gridid, num)","playerid:number, gridid:number, num:number默认全部道具",ErrorCode.OK,移除背包格内一定数量道具，通过道具格移除，默认全部移除,"local result = Backpack:removeGridItem(playerid, gridid, num)",背包管理,Backpack,การจัดการเป้,Mochila,Mochila,Sac à dos,新增待译0717,鞄管理,배낭,Túi,Рюкзак,Sırt çantası,Zaino,Rucksack,Tas,"playerid:number, gridid:number, num:number default all items","playerid:number, gridid:number, num:number default all items","playerid:number, gridid:number, num:number default all items","playerid:number, gridid:number, num:number default all items","playerid:number, gridid:number, num:number default all items","playerid:number, gridid:number, num:number default all items","playerid:number, gridid:number, num:number default all items","playerid:number, gridid:number, num:number default all items","playerid:number, gridid:number, num:number default all items","playerid:number, gridid:number, num:number default all items","playerid:number, gridid:number, num:number default all items","playerid:number, gridid:number, num:number default all items","playerid:number, gridid:number, num:number default all items","playerid:number, gridid:number, num:number default all items","playerid:number, gridid:number, num:number default all items",Remove some items in backpack bar. Removed by item slots. All removed by default,移除背包格內一定數量道具，通過道具格移除，默認全部移除,ลบรายการจำนวนหนึ่งในเป้ออก ลบออกโดยช่องไอเทม ลบทั้งหมดโดยค่าเริ่มต้น,Retire algunos artículos de la barra de mochila. Eliminado por las ranuras de elementos. Todos eliminados por defecto,Remova alguns itens na barra de mochila. Removido por slots de itens. Tudo removido por padrão,Retirer certains éléments dans la barre de sac à dos. Enlevé par des fentes de l'élément. Tous éliminés par défaut,新增待译0717,バックパックバーの中のいくつかのアイテムを取り除き、アイテムスロットによって取り除くことで、デフォルトですべて削除する。,배낭 표시 줄에 일부 항목을 제거합니다. 아이템 슬롯에 의해 제거되었습니다. 모든 기본적으로 제거,"Xóa số lượng công cụ nhất định, thông qua xóa các công cụ, mặc định xóa toàn bộ",Удалите некоторые элементы в рюкзаке бар. Удаленные элементы слоты. Все удаляются по умолчанию,Sırt çubuğundaki bazı eşyaları çıkarın. Varsayılan olarak tümü kaldırıldı,Rimuovere alcuni elementi in bar zaino. Rimosso dalla slot per gli oggetti. Tutto rimosso per impostazione predefinita,Entfernen Sie einige Elemente in Rucksack bar. Entfernt von Element-Slots. Alle standardmäßig entfernt,Hapus beberapa item di tas. Beberapa item akan dihapus secara otomatis.,"local result = Backpack:removeGridItem(playerid, gridid, num)"
110005,11,背包管理,removeGridItemByItemID,"Backpack:removeGridItemByItemID(playerid, itemid, num)","playerid:number, itemid:number, num:number默认全部道具","ErrorCode.OK, num:number成功移除数量",移除背包内一定数量道具，通过道具ID移除，默认全部移除,"local result = Backpack:removeGridItemByItemID(playerid, itemid, num)",背包管理,Backpack,การจัดการเป้,Mochila,Mochila,Sac à dos,新增待译0717,鞄管理,배낭,Túi,Рюкзак,Sırt çantası,Zaino,Rucksack,Tas,"playerid:number, itemid:number, num:number default all items","playerid:number, itemid:number, num:number default all items","playerid:number, itemid:number, num:number default all items","playerid:number, itemid:number, num:number default all items","playerid:number, itemid:number, num:number default all items","playerid:number, itemid:number, num:number default all items","playerid:number, itemid:number, num:number default all items","playerid:number, itemid:number, num:number default all items","playerid:number, itemid:number, num:number default all items","playerid:number, itemid:number, num:number default all items","playerid:number, itemid:number, num:number default all items","playerid:number, itemid:number, num:number default all items","playerid:number, itemid:number, num:number default all items","playerid:number, itemid:number, num:number default all items","playerid:number, itemid:number, num:number default all items",Remove some items in backpack bar. Removed by item ID. All removed by default,移除背包內一定數量道具，通過道具ID移除，默認全部移除,ลบรายการจำนวนหนึ่งในเป้ออก ลบออกโดย ID ไอเทม ลบทั้งหมดโดยค่าเริ่มต้น,Retire algunos artículos de la barra de mochila. Eliminado por ID de artículo. Todos eliminados por defecto,Remova alguns itens na barra de mochila. Removido pelo ID do item. Tudo removido por padrão,Retirer certains éléments dans la barre de sac à dos. Retiré par l'ID de l'élément. Tous éliminés par défaut,新增待译0717,バックパックバーの中のいくつかのアイテムを取り除き、アイテムスロットによって取り除くことで、デフォルトですべて削除する。,배낭 표시 줄에 일부 항목을 제거합니다. 항목 ID에 의해 제거되었습니다. 모든 기본적으로 제거,"Xóa một số mục trong ngăn của túi, thông qua việc xóa ID công cụ. Tất cả bị xóa theo mặc định",Удалите некоторые элементы в рюкзаке бар. Удалены по идентификатору товара. Все удаляются по умолчанию,Sırt çubuğundaki bazı eşyaları çıkarın. Varsayılan olarak tümü kaldırıldı,Rimuovere alcuni elementi in bar zaino. Rimosso da ID elemento. Tutto rimosso per impostazione predefinita,Entfernen Sie einige Elemente in Rucksack bar. Entfernt von Artikel ID. Alle standardmäßig entfernt,Hapus beberapa item di tas. Beberapa item akan dihapus secara otomatis.,"local result = Backpack:removeGridItemByItemID(playerid, itemid, num)"
110006,11,背包管理,clearPack,"Backpack:clearPack(playerid, bartype)","playerid:number, bartype:number背包类型",ErrorCode.OK,清空指定背包栏,"local result = Backpack:clearPack(playerid, bartype)",背包管理,Backpack,การจัดการเป้,Mochila,Mochila,Sac à dos,新增待译0717,鞄管理,배낭,Túi,Рюкзак,Sırt çantası,Zaino,Rucksack,Tas,"playerid:number, bartype:number Backpack type","playerid:number, bartype:number Backpack type","playerid:number, bartype:number Backpack type","playerid:number, bartype:number Backpack type","playerid:number, bartype:number Backpack type","playerid:number, bartype:number Backpack type","playerid:number, bartype:number Backpack type","playerid:number, bartype:number Backpack type","playerid:number, bartype:number Backpack type","playerid:number, bartype:number Backpack type","playerid:number, bartype:number Backpack type","playerid:number, bartype:number Backpack type","playerid:number, bartype:number Backpack type","playerid:number, bartype:number Backpack type","playerid:number, bartype:number Backpack type",Clear designate backpack bar,清空指定背包欄,ล้างแถบไอเทมของเป้ที่กำหนด,Mochila designate clara,Limpar designar mochila,bar désigné clair sac à dos,新增待译0717,指定バックパックバーをクリアする,클리어 지정 배낭 바,Xóa ngăn chỉ định trong túi,Очистить Назначенный рюкзак бар,Atanmış sırt çantası çubuğunu temizleyin,Cancella designato bar zaino,Klar designierter Rucksack bar,Kosongkan tas,"local result = Backpack:clearPack(playerid, bartype)"
110007,11,背包管理,clearAllPack,Backpack:clearAllPack(playerid),playerid:number,,清空全部背包(包含背包栏、快捷栏、装备栏),local result = Backpack:clearAllPack(playerid),背包管理,Backpack,การจัดการเป้,Mochila,Mochila,Sac à dos,新增待译0717,鞄管理,배낭,Túi,Рюкзак,Sırt çantası,Zaino,Rucksack,Tas,playerid:number,playerid:number,playerid:number,playerid:number,playerid:number,playerid:number,playerid:number,playerid:number,playerid:number,playerid:number,playerid:number,playerid:number,playerid:number,playerid:number,playerid:number,"Clear all the backpack(including backpack bar, shortcut bar, equip bar)",清空全部背包(包含背包欄、快捷欄、裝備欄),ล้างแถบไอเทมของเป้ทั้งหมด(รวมถึง ช่องในเป้ แถบไอเทม แถบอุปกรณ์),"Borrar toda la mochila (incluida la barra de mochila, barra de acceso directo, barra de equipamiento)","Limpar toda a mochila (incluindo barra de mochila, barra de atalho, equipar bar)","Effacer tout le sac à dos (y compris la barre de sac à dos, barre de raccourcis, équipez bar)",新增待译0717,すべてのバックパックをクリアする（バックパックバー、ショートカットバー、装備バーを含む）,"(배낭 줄을 포함, 도구 모음, 바 장비) 모두 삭제 배낭","Xóa toàn bộ túi (bao gồm ngăn, thanh phím tắt, thanh trang bị)","Очистить все рюкзак (включая рюкзак бар, панель быстрого доступа, оборудуют бар)","Tüm sırt çantasını temizleyin (sırt çantası çubuğu, kısayol çubuğu, donanım çubuğu dahil)","Cancella tutto lo zaino (compresi bar zaino, barra di collegamento, dotare bar)","Alle löscht den Rucksack (einschließlich Rucksack Bar, Shortcut-Leiste ausrüsten bar)","Kosongkan isi tas (termasuk bar tas, shortcut bar dll)",local result = Backpack:clearAllPack(playerid)
110008,11,背包管理,moveGridItem,"Backpack:moveGridItem(playerid, gridsrc, griddst, num)","playerid:number, gridsrc:number, griddst:number, num:number默认全部转移",ErrorCode.OK,移动背包道具，默认全部转移,"local result = Backpack:moveGridItem(playerid, gridsrc, griddst, num)",背包管理,Backpack,การจัดการเป้,Mochila,Mochila,Sac à dos,新增待译0717,鞄管理,배낭,Túi,Рюкзак,Sırt çantası,Zaino,Rucksack,Tas,"playerid:number, gridsrc:number, griddst:number, num:number default transfer all ","playerid:number, gridsrc:number, griddst:number, num:number default transfer all ","playerid:number, gridsrc:number, griddst:number, num:number default transfer all ","playerid:number, gridsrc:number, griddst:number, num:number default transfer all ","playerid:number, gridsrc:number, griddst:number, num:number default transfer all ","playerid:number, gridsrc:number, griddst:number, num:number default transfer all ","playerid:number, gridsrc:number, griddst:number, num:number default transfer all ","playerid:number, gridsrc:number, griddst:number, num:number default transfer all ","playerid:number, gridsrc:number, griddst:number, num:number default transfer all ","playerid:number, gridsrc:number, griddst:number, num:number default transfer all ","playerid:number, gridsrc:number, griddst:number, num:number default transfer all ","playerid:number, gridsrc:number, griddst:number, num:number default transfer all ","playerid:number, gridsrc:number, griddst:number, num:number default transfer all ","playerid:number, gridsrc:number, griddst:number, num:number default transfer all ","playerid:number, gridsrc:number, griddst:number, num:number default transfer all ",Remove items in backpack. All transferred by default,移動背包道具，默認全部轉移,ย้ายไอเทมในเป้ ย้ายทั้งหมดโดยค่าเริ่มต้น,Retire los artículos de la mochila. Todos descargados por defecto,Remova itens na mochila. Tudo transferido por padrão,Supprimer des éléments à sac à dos. Tous transférés par défaut,新增待译0717,バックパックの中身を取り除きます。デフォルトですべて転送する。,배낭에서 항목을 제거합니다. 모든 기본적으로 전송,"Xóa công cụ trong túi, tất cả bị xóa theo mặc định",Удалить элементы в рюкзаке. Все переданы по умолчанию,Sırt çantasındaki eşyaları çıkarın. Tümü varsayılan olarak aktarıldı,Rimuovere gli elementi in zaino. Tutti trasferiti predefinita,Entfernen Sie Gegenstände im Rucksack. Alle standardmäßig übertragen,Hapus item didalam tas. Semua akan ditransfer secara otomatis.,"local result = Backpack:moveGridItem(playerid, gridsrc, griddst, num)"
110009,11,背包管理,swapGridItem,"Backpack:swapGridItem(playerid, gridsrc, griddst)","playerid:number, gridsrc:number, griddst:number",ErrorCode.OK,交换背包道具,"local result = Backpack:swapGridItem(playerid, gridsrc, griddst)",背包管理,Backpack,การจัดการเป้,Mochila,Mochila,Sac à dos,新增待译0717,鞄管理,배낭,Túi,Рюкзак,Sırt çantası,Zaino,Rucksack,Tas,"playerid:number, gridsrc:number, griddst:number","playerid:number, gridsrc:number, griddst:number","playerid:number, gridsrc:number, griddst:number","playerid:number, gridsrc:number, griddst:number","playerid:number, gridsrc:number, griddst:number","playerid:number, gridsrc:number, griddst:number","playerid:number, gridsrc:number, griddst:number","playerid:number, gridsrc:number, griddst:number","playerid:number, gridsrc:number, griddst:number","playerid:number, gridsrc:number, griddst:number","playerid:number, gridsrc:number, griddst:number","playerid:number, gridsrc:number, griddst:number","playerid:number, gridsrc:number, griddst:number","playerid:number, gridsrc:number, griddst:number","playerid:number, gridsrc:number, griddst:number",Exchange item backpack ,交換背包道具,แลกเปลี่ยนไอเทมในเป้,Intercambio de artículos de mochila,Mochila de itens de troca,Sac à dos de l'élément d'échange,新增待译0717,アイテムのバックパックを交換する,교환 항목 배낭,Trao đổi các vật phẩm trong Túi,Обмен вещь рюкзак,Değişim öğe sırt çantası,oggetto di scambio zaino,Exchange-Objekt Rucksack,Tukar tas item,"local result = Backpack:swapGridItem(playerid, gridsrc, griddst)"
110010,11,背包管理,enoughSpaceForItem,"Backpack:enoughSpaceForItem(playerid, itemid, num)","playerid:number, itemid:number, num:number默认1",ErrorCode.OK,背包(包含背包栏、快捷栏)是否有足够的空间存放一定数量的道具,"local result = Backpack:enoughSpaceForItem(playerid, itemid, num)",背包管理,Backpack,การจัดการเป้,Mochila,Mochila,Sac à dos,新增待译0717,鞄管理,배낭,Túi,Рюкзак,Sırt çantası,Zaino,Rucksack,Tas,"playerid:number, itemid:number, num:number default 1","playerid:number, itemid:number, num:number default 2","playerid:number, itemid:number, num:number default 3","playerid:number, itemid:number, num:number default 4","playerid:number, itemid:number, num:number default 5","playerid:number, itemid:number, num:number default 6","playerid:number, itemid:number, num:number default 7","playerid:number, itemid:number, num:number default 8","playerid:number, itemid:number, num:number default 9","playerid:number, itemid:number, num:number default 10","playerid:number, itemid:number, num:number default 11","playerid:number, itemid:number, num:number default 12","playerid:number, itemid:number, num:number default 13","playerid:number, itemid:number, num:number default 14","playerid:number, itemid:number, num:number default 15","If there is enough space in backpack(including backpack bar, shortcut bar) to store items",背包(包含背包欄、快捷欄)是否有足夠的空間存放一定數量的道具,เป้(รวมถึง ช่องในเป้ แถบไอเทม)มีพื้นที่เพียงพอสำหรับจัดเก็บรายการจำนวนหนึ่งหรือไม่,"Si hay suficiente espacio en la mochila (incluida la barra de mochila, barra de acceso directo) para almacenar artículos","Se houver espaço suficiente na mochila (incluindo barra de mochila, barra de atalhos) para armazenar itens","S'il y a assez d'espace dans le sac à dos (y compris la barre de sac à dos, barre de raccourcis) pour stocker des éléments",新增待译0717,アイテムを保管するためにバックパック（バックパックバー、ショートカットバーを含む）に十分なスペースがありますか。,"배낭에 충분한 공간이있는 경우 항목을 저장하기 (배낭 바, 도구 모음을 포함)","Nếu có đủ không gian trong Túi (bao gồm các ngăn Túi, thanh công cụ) để lưu trữ vật phẩm","Если есть достаточно места в рюкзаке (включая рюкзак бар, панель быстрого доступа) для хранения предметов","Eşyaları depolamak için sırt çantasında yeterli alan varsa (sırt çantası çubuğu, kısayol çubuğu dahil)","Se non c'è abbastanza spazio nello zaino (compresi bar zaino, barra di collegamento) per memorizzare gli oggetti","Wenn es genügend Platz im Rucksack ist (einschließlich Rucksack Bar, Shortcut-Leiste) zum Speichern von Elementen",Apakah terdapat cukup ruang di tas untuk menyimpan item?,"local result = Backpack:enoughSpaceForItem(playerid, itemid, num)"
110011,11,背包管理,calcSpaceNumForItem,"Backpack:calcSpaceNumForItem(playerid, itemid)","playerid:number, itemid:number","ErrorCode.OK, num:number",计算背包(包含背包栏、快捷栏)能存放的道具剩余总数量,"local result = Backpack:calcSpaceNumForItem(playerid, itemid)",背包管理,Backpack,การจัดการเป้,Mochila,Mochila,Sac à dos,新增待译0717,鞄管理,배낭,Túi,Рюкзак,Sırt çantası,Zaino,Rucksack,Tas,"playerid:number, itemid:number","playerid:number, itemid:number","playerid:number, itemid:number","playerid:number, itemid:number","playerid:number, itemid:number","playerid:number, itemid:number","playerid:number, itemid:number","playerid:number, itemid:number","playerid:number, itemid:number","playerid:number, itemid:number","playerid:number, itemid:number","playerid:number, itemid:number","playerid:number, itemid:number","playerid:number, itemid:number","playerid:number, itemid:number","Calculate the total number of remaining items that the backpack(including backpack bar, shortcut bar) can store",計算背包(包含背包欄、快捷欄)能存放的道具剩餘總數量,คำนวณจำนวนรายการทั้งหมดที่เหลืออยู่ในเป้(รวมถึง ช่องในเป้ แถบไอเทม),"Calcule el número total de elementos que puede almacenar la mochila (incluida la barra de mochila, la barra de acceso directo)","Calcular o número total de itens restantes que a mochila (incluindo barra de mochila, barra de atalhos) pode armazenar","Calculer le nombre total d'éléments restants que le sac à dos (y compris la barre de sac à dos, bar raccourci) peut stocker",新增待译0717,バックパック（バックパックバー、ショートカットバーを含む）に保存できる残りのアイテムの総数を計算する,배낭 (배낭 바를 포함한 단축 bar)의 나머지 항목의 총 수를 계산 저장할 수,"Tính tổng số vật phẩm còn lại mà Túi (bao gồm ngăn Túi, thanh công cụ) có thể lưu trữ","Вычислить общее количество оставшихся элементов, которые рюкзак (в том числе рюкзак бар, ярлык бар) может хранить","Sırt çantasının (sırt çantası çubuğu, kısayol çubuğu dahil) saklayabileceği toplam öğe sayısını hesaplayın","Calcolare il numero totale di elementi rimanenti che lo zaino (compresi bar zaino, barra di collegamento) può memorizzare","Berechnen der Gesamtzahl der verbleibenden Elemente, die den Rucksack (einschließlich Rucksack bar, Shortcutleiste) speichern kann","Hitung jumlah total item yang tersisa yang dapat disimpandi tas (termasuk bar backpack, shortcut bar)","local result = Backpack:calcSpaceNumForItem(playerid, itemid)"
110012,11,背包管理,getBackpackBarValidList,"Backpack:getBackpackBarValidList(playerid, bartype)","playerid:number, bartype:number背包类型","ErrorCode.OK, num:number数量, array:table背包格ID数组",获取道具背包栏有效格ID列表(道具已存在)，背包格ID数组,"local result = Backpack:getBackpackBarValidList(playerid, bartype)",背包管理,Backpack,การจัดการเป้,Mochila,Mochila,Sac à dos,新增待译0717,鞄管理,배낭,Túi,Рюкзак,Sırt çantası,Zaino,Rucksack,Tas,"playerid:number, bartype:number Backpack type","playerid:number, bartype:number Backpack type","playerid:number, bartype:number Backpack type","playerid:number, bartype:number Backpack type","playerid:number, bartype:number Backpack type","playerid:number, bartype:number Backpack type","playerid:number, bartype:number Backpack type","playerid:number, bartype:number Backpack type","playerid:number, bartype:number Backpack type","playerid:number, bartype:number Backpack type","playerid:number, bartype:number Backpack type","playerid:number, bartype:number Backpack type","playerid:number, bartype:number Backpack type","playerid:number, bartype:number Backpack type","playerid:number, bartype:number Backpack type",Get valid list of items' ID which are already in backpack bar. Item ID,獲取道具背包欄有效格ID列表(道具已存在)，背包格ID數組,รับรายการ ID ของช่องที่ว่างอยู่ในเป้(มีไอเทมอยู่แล้ว) กลุ่มตัวเลข ID ช่องของเป้,Obtenga una lista de identificación válida de los artículos que ya están en la barra de mochila. ID del artículo,Obter lista válida de ID de itens que já estão na barra de mochila. ID do item,Obtenir la liste valide de l'ID des éléments qui sont déjà dans la barre de sac à dos. ID de l'article,新增待译0717,すでにバックパックバーに入っているアイテムIDの有効リストを取得します。バックパックバーのアイテムID。,배낭 표시 줄에 이미 항목 'ID의 유효 목록을 가져옵니다. 항목 ID,Nhận danh sách ID hợp lệ của các vật phẩm đã có trong ngăn Túi. ID vật phẩm,"Получить действительный список ID предметов, которые уже находятся в рюкзаке баре. Item ID",Önceden sırt çantası çubuğunda bulunan öğelerin kimliğinin geçerli bir listesini alın. Öğe Kimliği,Ottenere elenco valido di ID elementi che sono già in bar zaino. Numero identificativo dell'oggetto,"Erhalten Sie gültige Liste der Elemente ID, die bereits im Rucksack bar sind. Artikel Identifikationsnummer",Dapatkan daftar ID barang yang valid dan yang sudah ada di dalam tas. ID barang.,"local result = Backpack:getBackpackBarValidList(playerid, bartype)"
110013,11,背包管理,getBackpackBarItemList,"Backpack:getBackpackBarItemList(playerid, bartype)","playerid:number, bartype:number背包类型","ErrorCode.OK, num:number数量, arr:table道具ID数组",获取道具背包栏已拥有道具，道具ID数组,"local result = Backpack:getBackpackBarItemList(playerid, bartype)",背包管理,Backpack,การจัดการเป้,Mochila,Mochila,Sac à dos,新增待译0717,鞄管理,배낭,Túi,Рюкзак,Sırt çantası,Zaino,Rucksack,Tas,"playerid:number, bartype:number Backpack type","playerid:number, bartype:number Backpack type","playerid:number, bartype:number Backpack type","playerid:number, bartype:number Backpack type","playerid:number, bartype:number Backpack type","playerid:number, bartype:number Backpack type","playerid:number, bartype:number Backpack type","playerid:number, bartype:number Backpack type","playerid:number, bartype:number Backpack type","playerid:number, bartype:number Backpack type","playerid:number, bartype:number Backpack type","playerid:number, bartype:number Backpack type","playerid:number, bartype:number Backpack type","playerid:number, bartype:number Backpack type","playerid:number, bartype:number Backpack type",Get items already in backpack bar. Item ID,獲取道具背包欄已擁有道具，道具ID數組,รับไอเทมที่มีอยู่ในเป้ กลุ่มตัวเลข ID ไอเทม,Consigue artículos ya en la barra de mochila. ID del artículo,Obter itens já na barra de mochila. ID do item,Obtenez des produits déjà dans la barre de sac à dos. ID de l'article,新增待译0717,バックパックバーに既にアイテムが入っています。アイテムID。,배낭 표시 줄에 이미 항목을 가져옵니다. 항목 ID,Nhận các vật phẩm đã có trong ngăn Túi. ID vật phẩm,Получить предметы уже в рюкзаке баре. Item ID,Zaten sırt çantası çubuğunda öğeleri alın. Öğe Kimliği,Ottenere gli elementi già in bar zaino. Numero identificativo dell'oggetto,Erhalten Sie Artikel bereits in Rucksack bar. Artikel Identifikationsnummer,Dapatkan barang yang sudah ada di bar tas. ID barang.,"local result = Backpack:getBackpackBarItemList(playerid, bartype)"
110014,11,背包管理,hasItemByBackpackBar,"Backpack:hasItemByBackpackBar(playerid, bartype, itemid)","playerid:number, bartype:number, itemid:number",ErrorCode.OK,检测背包是否持有某个道具,"local result = Backpack:hasItemByBackpackBar(playerid, bartype, itemid)",背包管理,Backpack,การจัดการเป้,Mochila,Mochila,Sac à dos,新增待译0717,鞄管理,배낭,Túi,Рюкзак,Sırt çantası,Zaino,Rucksack,Tas,"playerid:number, bartype:number, itemid:number","playerid:number, bartype:number, itemid:number","playerid:number, bartype:number, itemid:number","playerid:number, bartype:number, itemid:number","playerid:number, bartype:number, itemid:number","playerid:number, bartype:number, itemid:number","playerid:number, bartype:number, itemid:number","playerid:number, bartype:number, itemid:number","playerid:number, bartype:number, itemid:number","playerid:number, bartype:number, itemid:number","playerid:number, bartype:number, itemid:number","playerid:number, bartype:number, itemid:number","playerid:number, bartype:number, itemid:number","playerid:number, bartype:number, itemid:number","playerid:number, bartype:number, itemid:number",Detect if there is any designated item in backpack ,檢測背包是否持有某個道具,ตรวจสอบว่าในเป้มีไอเทมบางอยางหรือไม่,Detectar si hay algún artículo designado en la mochila.,Detectar se há algum item designado na mochila,Détecter s'il y a un élément désigné sac à dos,新增待译0717,バックパックに指定アイテムがあるかどうかをチェックする。,배낭에있는 지정된 항목이있는 경우에 감지,Phát hiện nếu có bất kỳ vật phẩm chỉ định trong Túi,"Обнаружить, если есть какой-либо Нужный элемент в рюкзаке",Sırt çantasında herhangi bir tasarım öğesi olup olmadığını tespit edin,Rilevare se c'è qualche elemento designato nello zaino,"Erkennen, ob es ein benannten Artikel in Rucksack",Pendeteksi jika ada barang yang ditunjuk di tas.,"local result = Backpack:hasItemByBackpackBar(playerid, bartype, itemid)"
110015,11,背包管理,getItemNumByBackpackBar,"Backpack:getItemNumByBackpackBar(playerid, bartype, itemid)","playerid:number, bartype:number, itemid:number","ErrorCode.OK, num:number道具总数量, arr:table道具格ID数组",获取背包持有某个道具总数量，同时返回装有道具的背包格数组,"local result = Backpack:getItemNumByBackpackBar(playerid, bartype, itemid)",背包管理,Backpack,การจัดการเป้,Mochila,Mochila,Sac à dos,新增待译0717,鞄管理,배낭,Túi,Рюкзак,Sırt çantası,Zaino,Rucksack,Tas,"playerid:number, bartype:number, itemid:number","playerid:number, bartype:number, itemid:number","playerid:number, bartype:number, itemid:number","playerid:number, bartype:number, itemid:number","playerid:number, bartype:number, itemid:number","playerid:number, bartype:number, itemid:number","playerid:number, bartype:number, itemid:number","playerid:number, bartype:number, itemid:number","playerid:number, bartype:number, itemid:number","playerid:number, bartype:number, itemid:number","playerid:number, bartype:number, itemid:number","playerid:number, bartype:number, itemid:number","playerid:number, bartype:number, itemid:number","playerid:number, bartype:number, itemid:number","playerid:number, bartype:number, itemid:number",Get the total number of items in backpack. Return to backpacks with items,獲取背包持有某個道具總數量，同時返回裝有道具的背包格數組,รับจำนวนของไอเทมบางอย่างในเป้ ในขณะเดียวกันส่งคืนกลุ่มตัวเลขช่องของเป้ที่มีไอเทมอยู่,Obtener el número total de artículos en la mochila. Volver a las mochilas con artículos.,Obter o número total de itens na mochila. Retornar para mochilas com itens,Obtenez le nombre total d'articles dans sac à dos. Retour à dos avec des articles,新增待译0717,バックパック内のアイテムの合計数を取得します。アイテムのバックパックを返す。,배낭에있는 항목의 총 수를 가져옵니다. 항목 배낭로 돌아 가기,Lấy tổng số vật phẩm trong Túi. Quay trở lại Túi với các vật phẩm,Получить общее количество элементов в рюкзаке. Вернуться к рюкзакам с позиций,Sırt çantasındaki toplam eşya sayısını al. Eşyalarla sırt çantalarına dön,Prendi il numero totale di elementi in zaino. Ritorno a zaini con articoli,Holen Sie sich die Gesamtzahl der Elemente im Rucksack. Zurück zu Rucksäcken mit Artikeln,Dapatkan jumlah total barang di tas. Kembali ke tas dengan barang-barang tersebut.,"local result = Backpack:getItemNumByBackpackBar(playerid, bartype, itemid)"
110016,11,背包管理,getGridItemID,"Backpack:getGridItemID(playerid, gridid)","playerid:number, gridid:number","ErrorCode.OK, itemid:number, num:number",获取背包格道具ID和数量,"local result = Backpack:getGridItemID(playerid, gridid)",背包管理,Backpack,การจัดการเป้,Mochila,Mochila,Sac à dos,新增待译0717,鞄管理,배낭,Túi,Рюкзак,Sırt çantası,Zaino,Rucksack,Tas,"playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number",Get item ID and number in backpack ,獲取背包格道具ID和數量,รับIDและจำนวนของไอเทมในเป้,Obtener ID y número de artículo en la mochila,Obter o ID e o número do item na mochila,Obtenir l'article ID et le numéro dans sac à dos,新增待译0717,バックパックでアイテムIDと数量を取得する,배낭에서 아이템 ID 및 번호를 가져옵니다,Lấy ID vật phẩm và số trong Túi,Получить идентификатор элемента и номер в рюкзаке,Sırt çantasında öğe kimliğini ve numarayı alın,Ottenere ID elemento e il numero nello zaino,Erhalten Sie Artikel-ID und Nummer in Rucksack,Lihat ID item dan jumlahnya di tas,"local result = Backpack:getGridItemID(playerid, gridid)"
110017,11,背包管理,getGridItemName,"Backpack:getGridItemName(playerid, gridid)","playerid:number, gridid:number","ErrorCode.OK, name:string",获取背包格道具名称,"local result = Backpack:getGridItemName(playerid, gridid)",背包管理,Backpack,การจัดการเป้,Mochila,Mochila,Sac à dos,新增待译0717,鞄管理,배낭,Túi,Рюкзак,Sırt çantası,Zaino,Rucksack,Tas,"playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number",Get item name in backpack,獲取背包格道具名稱,รับชื่อของไอเทมในเป้,Obtener el nombre del artículo en la mochila,Obter o nome do item na mochila,Obtenir le nom de l'élément dans sac à dos,新增待译0717,バックパックのアイテム名を取得する,배낭에서 아이템 이름을 가져옵니다,Lấy tên vật phẩm trong Túi,Получить имя элемента в рюкзаке,Sırt çantasında eşya adını al,Ottiene il nome oggetto nello zaino,Erhalten Sie Elementname in Rucksack,Dapat nama tas item,"local result = Backpack:getGridItemName(playerid, gridid)"
110018,11,背包管理,getGridStack,"Backpack:getGridStack(playerid, gridid)","playerid:number, gridid:number","ErrorCode.OK, num:number, maxstack:number",获取背包格道具数量和最大堆叠数,"local result = Backpack:getGridStack(playerid, gridid)",背包管理,Backpack,การจัดการเป้,Mochila,Mochila,Sac à dos,新增待译0717,鞄管理,배낭,Túi,Рюкзак,Sırt çantası,Zaino,Rucksack,Tas,"playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number",Get the number of items in backpack and max no. of stacks,獲取背包格道具數量和最大堆疊數,รับจำนวนไอเทมและจำนวนกองซ้อนกันสูงสุดในเป้,Obtenga el número de artículos en la mochila y máx. de baterias,Obter o número de itens na mochila e no máx. de pilhas,Obtenez le nombre d'articles dans sac à dos et max pas. des piles,新增待译0717,バックパックのアイテム数と最大数を取得します。,배낭과 최대 어떤 항목 수를 가져옵니다. 스택의,Lấy số lượng vật phẩm trong Túi và tối đa số lượng của ngăn Túi,Получить количество элементов в рюкзаке и макс нет. стеков,Sırt çantası içindeki öğelerin sayısını alın ve maks. yığınların,Prendi il numero di elementi in zaino e max n. di pile,Holen Sie die Anzahl der Elemente in Rucksack und max nicht. von Stapeln,Dapatkan jumlah barang di tas dan jumalh maksimal tumpukan,"local result = Backpack:getGridStack(playerid, gridid)"
110019,11,背包管理,getGridDurability,"Backpack:getGridDurability(playerid, gridid)","playerid:number, gridid:number","ErrorCode.OK, durcur:number, durmax:number",获取背包格道具耐久度和最大耐久度,"local result = Backpack:getGridDurability(playerid, gridid)",背包管理,Backpack,การจัดการเป้,Mochila,Mochila,Sac à dos,新增待译0717,鞄管理,배낭,Túi,Рюкзак,Sırt çantası,Zaino,Rucksack,Tas,"playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number",Get item durability in backpack and maximum durability,獲取背包格道具耐久度和最大耐久度,รับค่าความทนทานและค่าความทนทานสูงสุดของไอเทมในเป้,Consigue la durabilidad de la mochila y la máxima durabilidad.,Obtenha durabilidade de itens na mochila e máxima durabilidade,Obtenez la durabilité de l'article en sac à dos et une durabilité maximale,新增待译0717,バックパックにあるアイテムの耐久性と最大の耐久性を獲得する。,배낭 및 최대 내구성 항목의 내구성을 얻기,Nhận độ bền vật phẩm trong túi và độ bền tối đa,Получить долговечность элемента в рюкзаке и максимальной прочности,Sırt çantasında eşya dayanıklılığı ve maksimum dayanıklılık elde edin,Ottenere Resistenza nello zaino e la massima durata nel tempo,Erhalten Sie Artikel Haltbarkeit in Rucksack und maximale Haltbarkeit,Lihat daya tahan barang di tas dan daya tahan maksimumnya,"local result = Backpack:getGridDurability(playerid, gridid)"
110020,11,背包管理,getGridEnchantList,"Backpack:getGridEnchantList(playerid, gridid)","playerid:number, gridid:number","ErrorCode.OK, num:number数量, arr:table附魔ID数组",获取背包格道具附魔，返回附魔id数组,"local result = Backpack:getGridEnchantList(playerid, gridid)",背包管理,Backpack,การจัดการเป้,Mochila,Mochila,Sac à dos,新增待译0717,鞄管理,배낭,Túi,Рюкзак,Sırt çantası,Zaino,Rucksack,Tas,"playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number",Get enchantment of items in backpack. Return to enchantment ID.,獲取背包格道具附魔，返回附魔id數組,รับไอเทมร่ายมนตร์ในเป้ ส่งคืนดลุ่มตัวเลขidร่ายมนตร์,Compra el elemento de encantamiento en la mochila. Volver a la identificación de encantamiento.,Adquira encantamento de itens na mochila. Retornar para o ID do encantamento.,Obtenez l'enchantement des articles dans sac à dos. Retour à l'ID d'enchantement.,新增待译0717,バックパックの中のアイテムenchantmentを獲得する。enchantmentのIDを返す。,배낭에있는 항목의 마법을 가져옵니다. 마법의 ID로 돌아갑니다.,Nhận phụ ma của các vật phẩm trong Túi. Quay trở lại ID phụ ma.,Получить зачарование предметов в рюкзаке. Вернуться к очарованию ID.,Sırt çantasında eşyalarınızı büyütün. Büyü kimliğine geri dön.,Ottenere incanto di articoli in zaino. Ritorno a ID incanto.,Holen Zauber der Elemente im Rucksack. Zurück zur Verzauberung ID.,Dapatkan nilai pesona dari item di tas. Kembali ke ID pesona.,"local result = Backpack:getGridEnchantList(playerid, gridid)"
110021,11,背包管理,getGridToolType,"Backpack:getGridToolType(playerid, gridid)","playerid:number, gridid:number","ErrorCode.OK, type:number工具类型",获取背包道具工具类型(<0表示非工具),"local result = Backpack:getGridToolType(playerid, gridid)",背包管理,Backpack,การจัดการเป้,Mochila,Mochila,Sac à dos,新增待译0717,鞄管理,배낭,Túi,Рюкзак,Sırt çantası,Zaino,Rucksack,Tas,"playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number",Get the item tool type in backpack (<0 means non-tool),獲取背包道具工具類型(<0表示非工具),รับประเภทเครื่องมือของไอเทมในเป้(<0แสดงว่าไม่ใช่เครื่องมือ),Obtener el tipo de herramienta del elemento en la mochila (<0 significa que no es herramienta),Obter o tipo de ferramenta de item na mochila (<0 significa não-ferramenta),Obtenez le type d'outil de pièce sac à dos (<0 signifie non-outil),新增待译0717,バックパックのアイテムツールタイプを取得します（<0はツールではないことを意味します）,배낭에서 항목 도구의 형태를 취득 (<0이 아닌 도구를 의미합니다),Lấy loại công cụ vật phẩm trong Túi (<0 có nghĩa là không phải công cụ),Получить тип элемента инструмента в рюкзаке (<0 означает не инструмент),Eşya alet tipini sırt çantasında alın (<0 aletsiz demektir),Prendi il tipo di utensile oggetto nello zaino (<0 significa non-utensile),Holen Sie sich das Element Werkzeugtyp in Rucksack (<0 bedeutet nicht-Werkzeug),Dapatkan tipe item di tas (<0 berarti bukan sebuah alat),"local result = Backpack:getGridToolType(playerid, gridid)"
110022,11,背包管理,addItem,"Backpack:addItem(playerid, itemid, num)","playerid:number, itemid:number, num:number","ErrorCode.OK, successNum:number成功添加的数量",添加道具到背包,"local result = Backpack:addItem(playerid, itemid, num)",背包管理,Backpack,การจัดการเป้,Mochila,Mochila,Sac à dos,新增待译0717,鞄管理,배낭,Túi,Рюкзак,Sırt çantası,Zaino,Rucksack,Tas,"playerid:number, itemid:number, num:number","playerid:number, itemid:number, num:number","playerid:number, itemid:number, num:number","playerid:number, itemid:number, num:number","playerid:number, itemid:number, num:number","playerid:number, itemid:number, num:number","playerid:number, itemid:number, num:number","playerid:number, itemid:number, num:number","playerid:number, itemid:number, num:number","playerid:number, itemid:number, num:number","playerid:number, itemid:number, num:number","playerid:number, itemid:number, num:number","playerid:number, itemid:number, num:number","playerid:number, itemid:number, num:number","playerid:number, itemid:number, num:number",Add the item to backpack,添加道具到背包,เพิ่มไอเทมเข้าไปในเป้,Añadir artículo a la mochila.,Adicione o item à mochila,Ajouter l'élément à sac à dos,新增待译0717,アイテムをバックパックに入れる,배낭에 상품을 추가,Thêm vật phẩm vào Túi,Добавить товар в рюкзак,Eşyayı sırt çantasına ekle,Aggiungere la voce a zaino,Fügen Sie den Artikel Rucksack,Tambahkan item ke tas,"local result = Backpack:addItem(playerid, itemid, num)"
110023,11,背包管理,discardItem,"Backpack:discardItem(playerid, gridid, num)","playerid:number, gridid:number, num:number",ErrorCode.OK,丢弃背包道具,"local result = Backpack:discardItem(playerid, gridid, num)",背包管理,Backpack,การจัดการเป้,Mochila,Mochila,Sac à dos,新增待译0717,鞄管理,배낭,Túi,Рюкзак,Sırt çantası,Zaino,Rucksack,Tas,"playerid:number, gridid:number, num:number","playerid:number, gridid:number, num:number","playerid:number, gridid:number, num:number","playerid:number, gridid:number, num:number","playerid:number, gridid:number, num:number","playerid:number, gridid:number, num:number","playerid:number, gridid:number, num:number","playerid:number, gridid:number, num:number","playerid:number, gridid:number, num:number","playerid:number, gridid:number, num:number","playerid:number, gridid:number, num:number","playerid:number, gridid:number, num:number","playerid:number, gridid:number, num:number","playerid:number, gridid:number, num:number","playerid:number, gridid:number, num:number",Discard the item in backpack,丟棄背包道具,ทิ้งไอเทมในเป้,Deseche el artículo en la mochila.,Descarte o item na mochila,Jeter l'élément dans sac à dos,新增待译0717,バックパックのアイテムを捨てる,배낭에서 항목을 폐기,Bỏ vật phẩm ra khỏi Túi,Выбросьте элемент в рюкзаке,Sırt çantasındaki öğeyi atın,Eliminare la voce nello zaino,Entsorgen Sie den Artikel in Rucksack,Buang item dari tas,"local result = Backpack:discardItem(playerid, gridid, num)"
110024,11,背包管理,getGridNum,"Backpack:getGridNum(playerid, gridid)","playerid:number, gridid:number",ErrorCode.OK,获取背包某个格子的道具数量,"local result = Backpack:getGridNum(playerid, gridid)",背包管理,Backpack,การจัดการเป้,Mochila,Mochila,Sac à dos,新增待译0717,鞄管理,배낭,Túi,Рюкзак,Sırt çantası,Zaino,Rucksack,Tas,"playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number","playerid:number, gridid:number",Get the number of items in one slot of backpack ,獲取背包某個格子的道具數量,รับจำนวนไอเทมในบางช่องของเป้,Obtenga el número de artículos en una ranura de mochila,Obter o número de itens em um slot de mochila,Obtenez le nombre d'éléments dans un emplacement de sac à dos,新增待译0717,バックパックにあるスロットのアイテム数を取得する,배낭 하나 개의 슬롯의 항목 수를 가져옵니다,Lấy số lượng vật phẩm trong một ô của ba lô,Получить количество элементов в одном слоте рюкзака,Bir yuvadaki eşyaların sayısını al,Ottenere il numero di elementi in uno slot di zaino,Holen Sie die Anzahl der Elemente in einem Schlitz des Rucksacks,Dapatkan jumlah item di sebuah slot dalam tas,"local result = Backpack:getGridNum(playerid, gridid)"
120001,12,小地图管理,newShape,"MapMark:newShape(type, isshow, r, g, b)","type:number, isshow:boolean, r:number, g:number, b:number","ErrorCode.OK, shapeid:number",新增一个形状(线，矩形，圆形),"local result = MapMark:newShape(type, isshow, r, g, b)",小地圖管理,Minimap,การจัดการแมพเล็ก,Mini Mapa,Mini Mapa,minicarte,新增待译0717,ミニマップ,미니 맵,Minimap,Minimap,Küçük harita,minimap,Minimap,Minimap,"type:number, isshow:boolean, r:number, g:number, b:number","type:number, isshow:boolean, r:number, g:number, b:number","type:number, isshow:boolean, r:number, g:number, b:number","type:number, isshow:boolean, r:number, g:number, b:number","type:number, isshow:boolean, r:number, g:number, b:number","type:number, isshow:boolean, r:number, g:number, b:number","type:number, isshow:boolean, r:number, g:number, b:number","type:number, isshow:boolean, r:number, g:number, b:number","type:number, isshow:boolean, r:number, g:number, b:number","type:number, isshow:boolean, r:number, g:number, b:number","type:number, isshow:boolean, r:number, g:number, b:number","type:number, isshow:boolean, r:number, g:number, b:number","type:number, isshow:boolean, r:number, g:number, b:number","type:number, isshow:boolean, r:number, g:number, b:number","type:number, isshow:boolean, r:number, g:number, b:number","New shape(line, rectangle, circle)",新增一個形狀(線，矩形，圓形),เพิ่มรูปร่าง(เส้น สี่เหลี่ยมผืนผ้า วงกลม),"Nueva forma (línea, rectángulo, círculo)","Nova forma (linha, retângulo, círculo)","Nouvelle forme (ligne, rectangle, cercle)",新增待译0717,形（線、長方形、円形）を一つ追加する。,"새로운 모양 (선, 사각형, 원)","Hình dạng mới (đường thẳng, hình chữ nhật, hình tròn)","Новая форма (линия, прямоугольник, круг)","Yeni şekil (çizgi, dikdörtgen, daire)","Nuova forma (linea, rettangolo, cerchio)","Neue Form (Linie, Rechteck, Kreis)","Bentuk baru (garis, kotak atau bulat)","local result = MapMark:newShape(type, isshow, r, g, b)"
120002,12,小地图管理,deleteShape,MapMark:deleteShape(shapeid),shapeid:number,ErrorCode.OK,删除一个形状,local result = MapMark:deleteShape(shapeid),小地圖管理,Minimap,การจัดการแมพเล็ก,Mini Mapa,Mini Mapa,minicarte,新增待译0717,ミニマップ,미니 맵,Minimap,Minimap,Küçük harita,minimap,Minimap,Minimap,shapeid:number,shapeid:number,shapeid:number,shapeid:number,shapeid:number,shapeid:number,shapeid:number,shapeid:number,shapeid:number,shapeid:number,shapeid:number,shapeid:number,shapeid:number,shapeid:number,shapeid:number,Delete a shape,刪除一個形狀,ลบหนึ่งรูปร่าง,Eliminar una forma,Excluir uma forma,Supprimer une forme,新增待译0717,形を一つ削除する,모양 삭제,Xóa hình dạng,Удалить форму,Bir şekli sil,Eliminare una forma,Löschen einer Form,Hapus bentuk,local result = MapMark:deleteShape(shapeid)
120003,12,小地图管理,setShapeColor,"MapMark:setShapeColor(shapeid, r, g, b)","shapeid:number, r:number, g:number, b:number",ErrorCode.OK,设置形状颜色,"local result = MapMark:setShapeColor(shapeid, r, g, b)",小地圖管理,Minimap,การจัดการแมพเล็ก,Mini Mapa,Mini Mapa,minicarte,新增待译0717,ミニマップ,미니 맵,Minimap,Minimap,Küçük harita,minimap,Minimap,Minimap,"shapeid:number, r:number, g:number, b:number","shapeid:number, r:number, g:number, b:number","shapeid:number, r:number, g:number, b:number","shapeid:number, r:number, g:number, b:number","shapeid:number, r:number, g:number, b:number","shapeid:number, r:number, g:number, b:number","shapeid:number, r:number, g:number, b:number","shapeid:number, r:number, g:number, b:number","shapeid:number, r:number, g:number, b:number","shapeid:number, r:number, g:number, b:number","shapeid:number, r:number, g:number, b:number","shapeid:number, r:number, g:number, b:number","shapeid:number, r:number, g:number, b:number","shapeid:number, r:number, g:number, b:number","shapeid:number, r:number, g:number, b:number",Set the shape and color,設置形狀顏色,ตั้งค่าสีของรูปร่าง,Establecer forma y color,Definir a forma e cor,Définissez la forme et la couleur,新增待译0717,形と色を設定する,모양과 색상을 설정합니다,Đặt hình dạng và màu sắc,Установите форму и цвет,  Şekli ve rengi ayarla,Impostare la forma e il colore,Stellen Sie die Form und Farbe,Atur bentuk dan warna,"local result = MapMark:setShapeColor(shapeid, r, g, b)"
120004,12,小地图管理,showShape,"MapMark:showShape(shapeid, showflag)","shapeid:number, showflag:boolean",ErrorCode.OK,设置形状显示or隐藏,"local result = MapMark:showShape(shapeid, showflag)",小地圖管理,Minimap,การจัดการแมพเล็ก,Mini Mapa,Mini Mapa,minicarte,新增待译0717,ミニマップ,미니 맵,Minimap,Minimap,Küçük harita,minimap,Minimap,Minimap,"shapeid:number, showflag:boolean","shapeid:number, showflag:boolean","shapeid:number, showflag:boolean","shapeid:number, showflag:boolean","shapeid:number, showflag:boolean","shapeid:number, showflag:boolean","shapeid:number, showflag:boolean","shapeid:number, showflag:boolean","shapeid:number, showflag:boolean","shapeid:number, showflag:boolean","shapeid:number, showflag:boolean","shapeid:number, showflag:boolean","shapeid:number, showflag:boolean","shapeid:number, showflag:boolean","shapeid:number, showflag:boolean",Set shape displayed or hidden,設置形狀顯示or隱藏,ตั้งค่าแสดงหรือซ่อนรูปร่าง,Establecer la forma mostrada u oculta,Definir forma exibida ou ocultada,Changer la forme affichée ou masquée,新增待译0717,形状を表示或いは非表示を設定する。,세트 모양 표시 또는 숨김,Đặt hình dạng hiển thị hoặc ẩn,Установить форму показать или скрыть,Görüntülenen veya gizli şekli ayarla,Set forma visualizzata o nascosta,Set Form angezeigt oder verborgen,Atur ukuran tertampil atau tersembunyi,"local result = MapMark:showShape(shapeid, showflag)"
120005,12,小地图管理,updateLine,"MapMark:updateLine(shapeid, sx, sz, ex, ez)","shapeid:number, sx:number, sz:number, ex:number, ez:number",ErrorCode.OK,更新形状(线形)，传入起始坐标和末尾坐标,"local result = MapMark:updateLine(shapeid, sx, sz, ex, ez)",小地圖管理,Minimap,การจัดการแมพเล็ก,Mini Mapa,Mini Mapa,minicarte,新增待译0717,ミニマップ,미니 맵,Minimap,Minimap,Küçük harita,minimap,Minimap,Minimap,"shapeid:number, sx:number, sz:number, ex:number, ez:number","shapeid:number, sx:number, sz:number, ex:number, ez:number","shapeid:number, sx:number, sz:number, ex:number, ez:number","shapeid:number, sx:number, sz:number, ex:number, ez:number","shapeid:number, sx:number, sz:number, ex:number, ez:number","shapeid:number, sx:number, sz:number, ex:number, ez:number","shapeid:number, sx:number, sz:number, ex:number, ez:number","shapeid:number, sx:number, sz:number, ex:number, ez:number","shapeid:number, sx:number, sz:number, ex:number, ez:number","shapeid:number, sx:number, sz:number, ex:number, ez:number","shapeid:number, sx:number, sz:number, ex:number, ez:number","shapeid:number, sx:number, sz:number, ex:number, ez:number","shapeid:number, sx:number, sz:number, ex:number, ez:number","shapeid:number, sx:number, sz:number, ex:number, ez:number","shapeid:number, sx:number, sz:number, ex:number, ez:number",Update shape(line). Add start coordinate and end coordinate,更新形狀(線形)，傳入起始坐標和末尾坐標,อัปเดตรูปร่าง(เส้น) ส่งพิกัดเริ่มต้นและพิกัดสิ้นสุด,Actualizar la forma (línea). Agregue la coordenada inicial y la coordenada final,Atualize a forma (linha). Adicionar coordenada inicial e coordenada final,forme de mise à jour (ligne). Ajouter début coordonnées et coordonnée finale,新增待译0717,形を更新する（線）。開始座標と終了座標を追加する。,업데이트 모양 (선). 좌표와 끝 좌표 시작 추가,Cập nhật hình dạng (dòng). Thêm tọa độ bắt đầu và tọa độ kết thúc,Форма обновления (линия). Добавить начало координат и конец координат,Güncelleme şekli (satır). Başlangıç koordinatı ekle ve bitiş koordinatı,Aggiornamento forma (linea). Aggiungere inizio coordinare e coordinare fine,Update Form (Linie). In Startkoordinate und Endkoordinate,Update bentuk (garis). Tambahkan kordinasi mulai atau awal dan kordinasi akhir,"local result = MapMark:updateLine(shapeid, sx, sz, ex, ez)"
120006,12,小地图管理,updateRectangle,"MapMark:updateRectangle(shapeid, sx, sz, w, h)","shapeid:number, sx:number, sz:number, w:number, h:number",ErrorCode.OK,更新形状(矩形)，传入起始坐标和尺寸,"local result = MapMark:updateRectangle(shapeid, sx, sz, w, h)",小地圖管理,Minimap,การจัดการแมพเล็ก,Mini Mapa,Mini Mapa,minicarte,新增待译0717,ミニマップ,미니 맵,Minimap,Minimap,Küçük harita,minimap,Minimap,Minimap,"shapeid:number, sx:number, sz:number, w:number, h:number","shapeid:number, sx:number, sz:number, w:number, h:number","shapeid:number, sx:number, sz:number, w:number, h:number","shapeid:number, sx:number, sz:number, w:number, h:number","shapeid:number, sx:number, sz:number, w:number, h:number","shapeid:number, sx:number, sz:number, w:number, h:number","shapeid:number, sx:number, sz:number, w:number, h:number","shapeid:number, sx:number, sz:number, w:number, h:number","shapeid:number, sx:number, sz:number, w:number, h:number","shapeid:number, sx:number, sz:number, w:number, h:number","shapeid:number, sx:number, sz:number, w:number, h:number","shapeid:number, sx:number, sz:number, w:number, h:number","shapeid:number, sx:number, sz:number, w:number, h:number","shapeid:number, sx:number, sz:number, w:number, h:number","shapeid:number, sx:number, sz:number, w:number, h:number",Update shape(rectangle). Add start coordinate and end coordinate,更新形狀(矩形)，傳入起始坐標和尺寸,อัปเดตรูปร่าง(สี่เหลี่ยมผืนผ้า) ส่งพิกัดเริ่มต้นและขนาด,Actualizar la forma (rectángulo). Agregue la coordenada inicial y la coordenada final,Atualize a forma (retângulo). Adicionar coordenada inicial e coordenada final,forme de mise à jour (rectangle). Ajouter début coordonnées et coordonnée finale,新增待译0717,形を更新する（長方形）。開始座標と大きさを追加する。,업데이트 모양 (사각형). 좌표와 끝 좌표 시작 추가,Cập nhật hình dạng (hình chữ nhật). Thêm tọa độ bắt đầu và tọa độ kết thúc,Форма обновления (прямоугольник). Добавить начало координат и конец координат,Güncelleme şekli (dikdörtgen). Başlangıç koordinatı ekle ve bitiş koordinatı,Aggiornamento forma (rettangolo). Aggiungere inizio coordinare e coordinare fine,Update Form (rechteckig). In Startkoordinate und Endkoordinate,Update bentuk (kotak). Tambahkan kordinasi mulai atau awal dan kordinasi akhir,"local result = MapMark:updateRectangle(shapeid, sx, sz, w, h)"
120007,12,小地图管理,updateCircle,"MapMark:updateCircle(shapeid, cx, cz, r)","shapeid:number, cx:number, cz:number, r:number",ErrorCode.OK,更新形状(圆形)，传入圆心坐标和半径,"local result = MapMark:updateCircle(shapeid, cx, cz, r)",小地圖管理,Minimap,การจัดการแมพเล็ก,Mini Mapa,Mini Mapa,minicarte,新增待译0717,ミニマップ,미니 맵,Minimap,Minimap,Küçük harita,minimap,Minimap,Minimap,"shapeid:number, cx:number, cz:number, r:number","shapeid:number, cx:number, cz:number, r:number","shapeid:number, cx:number, cz:number, r:number","shapeid:number, cx:number, cz:number, r:number","shapeid:number, cx:number, cz:number, r:number","shapeid:number, cx:number, cz:number, r:number","shapeid:number, cx:number, cz:number, r:number","shapeid:number, cx:number, cz:number, r:number","shapeid:number, cx:number, cz:number, r:number","shapeid:number, cx:number, cz:number, r:number","shapeid:number, cx:number, cz:number, r:number","shapeid:number, cx:number, cz:number, r:number","shapeid:number, cx:number, cz:number, r:number","shapeid:number, cx:number, cz:number, r:number","shapeid:number, cx:number, cz:number, r:number",Update the shape(circle). Add center coordinates and radius,更新形狀(圓形)，傳入圓心坐標和半徑,อัปเดตรูปร่าง(วงกลม) ส่งพิกัดศูนย์กลางและรัศมีวงกลม,Actualizar la forma (círculo). Añadir coordenadas centro y radio,Atualize a forma (círculo). Adicionar coordenadas do centro e raio,Mettre à jour la forme (cercle). Ajouter les coordonnées du centre et le rayon,新增待译0717,形を更新する（円）。中心座標と半径を追加する。,모양 (원)을 업데이트합니다. 중심 좌표와 반경을 추가,Cập nhật hình dạng (hình tròn). Thêm tọa độ trung tâm và bán kính,Обновление формы (круг). Добавить координаты центра и радиус,Şekli güncelle (daire). Merkez koordinatlarını ve yarıçapı ekle,Aggiornare la forma (cerchio). Aggiungere le coordinate del centro e raggio,Aktualisieren Sie die Form (Kreis). In Zentrum Koordinaten und Radius,Update bentuk (bulat). Tambahkan kordinasi mulai atau awal dan kordinasi akhir,"local result = MapMark:updateCircle(shapeid, cx, cz, r)"
130001,13,出生点管理,getSpawnPoint,Spawnport:getSpawnPoint(),nil,"ErrorCode.OK, x:number, y:number, z:number",获取出生点位置,local result = Spawnport:getSpawnPoint(),出生點管理,Spawn Point,การจัดการจุดเกิด,Punto Resurreción,Ponto Renascimento,point de Spawn,新增待译0717,出生地管理,스폰 포인트,Điểm Hồi Sinh,Точка возрождения,Spawn Noktası,Punto di spawn,Spawnpunkt,Titik Bangkit,nil,nil,nil,nil,nil,nil,nil,nil,nil,nil,nil,nil,nil,nil,nil,Get the location of spawn point,獲取出生點位置,รับตำำแหน่งจุดเกิด,Obtener la ubicación del punto de respuesta,Obter a localização do ponto de respaw,Obtenez l'emplacement du point spawn,新增待译0717,出生地の位置を取得,산란 점의 위치를 ​​가져옵니다,Lấy vị trí của điểm hổi sinh,Получить расположение Спавнпоинт,Yumurtlama noktasının yerini al,Ottenere la posizione del punto di spawn,Holen Sie sich die Position Spawnpunkt,Lihat lokasi dari titik bangkit,local result = Spawnport:getSpawnPoint()
130002,13,出生点管理,getChunkValidSpawnPos,"Spawnport:getChunkValidSpawnPos(x, y, z)","x:number, y:number, z:number","ErrorCode.OK, x:number, y:number, z:number",获取区块可以作为出生点的位置，传入区块内任一方块位置,"local result = Spawnport:getChunkValidSpawnPos(x, y, z)",出生點管理,Spawn Point,การจัดการจุดเกิด,Punto Resurreción,Ponto Renascimento,point de Spawn,新增待译0717,出生地管理,스폰 포인트,Điểm Hồi Sinh,Точка возрождения,Spawn Noktası,Punto di spawn,Spawnpunkt,Titik Bangkit,"x:number, y:number, z:number","x:number, y:number, z:number","x:number, y:number, z:number","x:number, y:number, z:number","x:number, y:number, z:number","x:number, y:number, z:number","x:number, y:number, z:number","x:number, y:number, z:number","x:number, y:number, z:number","x:number, y:number, z:number","x:number, y:number, z:number","x:number, y:number, z:number","x:number, y:number, z:number","x:number, y:number, z:number","x:number, y:number, z:number",Get the spawn point in region. Add the position of random block in region,獲取區塊可以作為出生點的位置，傳入區塊內任一方塊位置,รับตำแหน่งที่สามารถตั้งเป็นจุดเกิดของขอบเขต ส่งไปยังตำแหน่งหนึ่งในขอบเขต,Obtener el punto de respuesta en la región. Agregue la posición del bloque aleatorio en la región.,Obtenha o ponto de respaw na região. Adicione a posição do bloco aleatório na região,Obtenez le point d'apparition dans la région. Ajouter la position du bloc aléatoire dans la région,新增待译0717,領域内に出生地としての場所を取得します。領域内のランダムのブロックの位置へ追加する。,지역의 스폰 포인트를 가져옵니다. 지역에서 무작위 블록의 위치를 ​​추가,Lấy điểm hồi sinh trong khu vực. Thêm vị trí của khối vuông ngẫu nhiên trong khu vực,Получить точку спавна в регионе. Добавить позицию случайного блока в регионе,Bölgede ortaya çıkış noktasını alın. Bölgedeki rastgele bloğun konumunu ekle,Prendi il punto di spawn nella regione. Aggiungere la posizione di blocco casuale nella regione,Holen Sie sich das Spawnpunkt in der Region. Fügen Sie die Position des statistischen Block in der Region,Lihat titik bangkit di wilayah. Tambahkan posisi dari blok wilayah secara acak.,"local result = Spawnport:getChunkValidSpawnPos(x, y, z)"
