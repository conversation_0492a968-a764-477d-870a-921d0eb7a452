任务ID,任务名称,任务内容[0=无，1=击败生物，2=收集道具],任务奖励[｛0=道具，道具ID，数量｝｛1=经验，数量｝],是否需要交付[0=不需要，1=需要],在冒险笔记显示[0=否，1=是],交任务目标ID,"剧情对话（接任务后）[FuncType，0=继续对话，1=跳转对话，2=终止对话]，例子：[""FuncType"": 1,""Val"": 1]","任务对话（未完成）[FuncType，0=继续对话，1=跳转对话，2=终止对话]，例子：[""FuncType"": 1,""Val"": 1]",任务对话（已完成）,是否可重复,是否启用任务交互界面
TaskID,TaskName,TaskContent,TaskReward,IsDeliver,ShowInNote,InteractID,PlotDialogue,UnCompletedDialogue,CompletedDialogue,IsRepeat,UseInteract
1,@10801,"[{""Type"":0}]","[{""Type"":0,""ItemId"":817,""Num"":3},{""Type"":1,""Num"":300}]",0,1,,"[
    {
        ""ID"": 1,
        ""Text"": ""@10804"",
        ""Answer"": [
            {
                ""Text"": ""@10805"",
                ""FuncType"": 2
            }
        ]
    }
]","[
    {
        ""ID"": 1,
        ""Text"": ""@10806"",
        ""Answer"": [
            {
                ""Text"": ""@10807"",
                ""FuncType"": 2
            }
        ]
    }
]","[
    {
        ""ID"": 1,
        ""Text"": ""@10808"",
        ""Answer"": [
            {
                ""Text"": ""@10809"",
                ""FuncType"": 4
            }
        ]
    }
]",,1
2,@10810,"[{""Type"":1, ""ID"":3400,""Num"":1}]","[{""Type"":0,""ItemId"":12502,""Num"":2},{""Type"":1,""Num"":500}]",0,1,,"[
    {
        ""ID"": 1,
        ""Text"": ""@10814"",
        ""Answer"": [
            {
                ""Text"": ""@10815"",
                ""FuncType"": 2
            }
        ]
    }
]","[
    {
        ""ID"": 1,
        ""Text"": ""@10816"",
        ""Answer"": [
            {
                ""Text"": ""@10817"",
                ""FuncType"": 2
            }
        ]
    }
]","[
    {
        ""ID"": 1,
        ""Text"": ""@10818"",
        ""Answer"": [
            {
                ""Text"": ""@10819"",
                ""FuncType"": 4
            }
        ]
    }
]",,1
3,@10820,"[{""Type"":2, ""ID"":200,""Num"":10}]","[{""Type"":0,""ItemId"":11307,""Num"":1},{""Type"":1,""Num"":100}]",0,1,,"[
    {
        ""ID"": 1,
        ""Text"": ""@10824"",
        ""Answer"": [
            {
                ""Text"": ""@10825"",
                ""FuncType"": 2
            }
        ]
    }
]","[
    {
        ""ID"": 1,
        ""Text"": ""@10826"",
        ""Answer"": [
            {
                ""Text"": ""@10827"",
                ""FuncType"": 2
            }
        ]
    }
]","[
    {
        ""ID"": 1,
        ""Text"": ""@10828"",
        ""Answer"": [
            {
                ""Text"": ""@10829"",
                ""FuncType"": 4
            }
        ]
    }
]",,1
4000,,"[{""Type"":1, ""ID"":3400,""Num"":1}]","[{""Type"":0,""ItemId"":12502,""Num"":2},{""Type"":1,""Num"":500}]",0,1,,"[
    {
        ""ID"": 1,
        ""Text"": ""@10830"",
        ""Answer"": [
            {
                ""Text"": ""@10831"",
                ""FuncType"": 0
            },
            {
                ""Text"": """",
                ""FuncType"": 0
            },
            {
                ""Text"": """",
                ""FuncType"": 0
            },
            {
                ""Text"": """",
                ""FuncType"": 0
            }
        ]
    }
]","[
    {
        ""ID"": 1,
        ""Text"": ""@10832"",
        ""Answer"": [
            {
                ""Text"": ""@10833"",
                ""FuncType"": 0
            },
            {
                ""Text"": """",
                ""FuncType"": 0
            },
            {
                ""Text"": """",
                ""FuncType"": 0
            },
            {
                ""Text"": """",
                ""FuncType"": 0
            }
        ]
    }
]","[
    {
        ""ID"": 1,
        ""Text"": ""@10834"",
        ""Answer"": [
            {
                ""Text"": ""@10835"",
                ""FuncType"": 4
            }
        ]
    }
]",,1
