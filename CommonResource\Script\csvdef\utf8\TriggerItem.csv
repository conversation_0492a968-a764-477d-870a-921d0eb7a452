控件ID【控件大类(1=事件|2=条件|3=动作|4=局部事件)_子类（01~17）_序号（0001~）】,名称,控件大类,子类型,子类名称,显示过滤【①全局（1=显示（默认）|0=隐藏），②局部（1=显示（默认）|0=隐藏），③道具类型（0=通用|1=方块|2=杂物|3=工具|4=一组物品|5=怪物|6=账号物品|7=弓|8=投掷物/弹药|9=装备|10=枪械|11=食物|12=石矛|15=包裹|16=机械）(子类型=6时才读)（兼用多种道具类型时，数字用‘|’隔开），④使用仅供学习的模板地图时禁用（0=不禁用，1=禁用）】,描述,描述备注,备注,事件传参备注,事件传参,参数1,参数默认值1,参数2,参数默认值2,参数3,参数默认值3,参数4,参数默认值4,参数5,参数默认值5,参数6,参数默认值6,参数7,参数默认值7,参数8,参数默认值8,参数9,参数默认值9,参数10,参数默认值10,触发器显示（0=默认全部可见 1=仅开发者可见 2=仅999渠道可见）,触发器提示版本号（填写版本号的第二位）,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
ID,Name,Type,ChildType,ChildName,DisplayFilter,Desc,,Remarks,,TriggerEventParams,Param1,DefaultParam1,Param2,DefaultParam2,Param3,DefaultParam3,Param4,DefaultParam4,Param5,DefaultParam5,Param6,DefaultParam6,Param7,DefaultParam7,Param8,DefaultParam8,Param9,DefaultParam9,Param10,DefaultParam10,Display,Version,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1010001,天气改变时,1,1,世界,"1,0,0,0",任意天气发生改变时,任意天气发生改变时,天气从晴天变为雨天，雨天变为晴天时,,,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1010002,地形组的天气改变时,1,1,世界,"1,0,0,0",指定|@1|的天气改变时,,常见组：平原/森林/峭壁/丛林/盆地/空岛/河流；海洋组：沙滩/深海/浅海/岛屿；沙漠组：沙漠/绿洲；普通寒带：针叶林/冰原；高峰寒带：冰山；火山组：火山；湿地组：雨林/沼泽；平坦、空岛,,,"1137,1",5|990001,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1020001,游戏创建时,1,2,游戏逻辑,"1,0,0,0",游戏创建时触发,游戏创建时触发,"进入游戏倒计时3,2,1结束后，开始游戏时",,,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1020002,游戏内时间,1,2,游戏逻辑,"1,0,0,0",游戏内时间处于|@1|点,游戏内时间处于【数值】点,游戏当前时刻处在指定时间点上时,,,"1005,1",1|5,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1020003,游戏运行时间,1,2,游戏逻辑,"1,0,0,0",游戏运行时间|@1|秒后,游戏运行时间【数值】秒后,进入游戏后运行指定时间后,,,"1005,1",1|5,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1020004,游戏胜利,1,2,游戏逻辑,"1,0,0,0",任意玩家触发胜利时,任意玩家触发胜利时,,触发事件的玩家|事件中的位置,250001|240001,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1020005,游戏失败,1,2,游戏逻辑,"1,0,0,0",任意玩家触发失败时,任意玩家触发失败时,,触发事件的玩家|事件中的位置,250001|240001,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1020007,玩家进入游戏,1,2,游戏逻辑,"1,0,0,0",任意玩家进入游戏时,任意玩家进入游戏时,,触发事件的玩家|事件中的位置,250001|240001,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1020008,玩家离开游戏,1,2,游戏逻辑,"1,0,0,0",任意玩家离开游戏时,任意玩家离开游戏时,,触发事件的玩家|事件中的位置,250001|240001,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1020009,计时器变化,1,2,游戏逻辑,"1,0,0,0",任意计时器发生变化时,任意计时器发生变化时,变量库中任意计时器变量发生变化时,,,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1020010,指定计时器变化,1,2,游戏逻辑,"1,0,0,0",@1|发生变化时,【计时器】发生变化时,变量库中指定计时器变量发生变化时,,,"1040,1",,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1020011,每当游戏运行时间（循环）,1,2,游戏逻辑,"1,0,0,0",游戏每运行|@1|秒,游戏每运行【数值】秒,"1.最小取值为0.05秒,0.01-0.09都会取0.05
2.游戏每运行指定时间后就会触发（循环）",,,"1005,1",1|2,,,,,,,,,,,,,,,,,,,,2,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1020012,地图接受到信息时,1,2,游戏逻辑,"1,0,0,0",地图接受到任意字符串信息时,地图接受到任意字符串信息时,,事件中的字符串,550001,,,,,,,,,,,,,,,,,,,,,0,5,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1030001,玩家被击败,1,3,玩家,"1,0,0,0",任意玩家被击败时,任意玩家死亡,,触发事件的玩家|事件中的位置,250001|240001,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1030002,复活,1,3,玩家,"1,0,0,0",任意玩家复活,任意玩家复活,,触发事件的玩家|事件中的位置,250001|240001,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1030003,属性改变,1,3,玩家,"1,0,0,0",任意玩家|@1|改变,任意玩家【玩家属性】变化,当玩家某个属性，如血量变化时,触发事件的玩家|事件中的位置,250001|240001,"1020,1",5|140001,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1030004,击败目标,1,3,玩家,"1,0,0,0",任意玩家击败其他玩家或任意生物,任意玩家打败其他玩家或生物,当玩家打败其他玩家、生物时。被打败的玩家、生物为事件中的目标玩家、生物,触发事件的玩家|事件中的位置|事件中的目标玩家|事件中的目标生物|事件中的目标生物类型,250001|240001|250002|300002|440002,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1030005,攻击,1,3,玩家,"1,0,0,0",任意玩家攻击时,任意玩家开始攻击,,触发事件的玩家|事件中的位置,250001|240001,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1030006,攻击命中,1,3,玩家,"1,0,0,0",任意玩家攻击命中任意玩家、生物 ,任意玩家攻击命中任意玩家、生物,当玩家攻击命中其他玩家、生物时。被命中的玩家、生物为事件中的目标玩家、生物,触发事件的玩家|事件中的位置|事件中的目标玩家|事件中的目标生物|事件中的目标生物类型,250001|240001|250002|300002|440002,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1030007,造成伤害,1,3,玩家,"1,0,0,0",任意玩家对任意玩家、生物造成伤害,任意玩家对任意玩家、生物造成伤害,当玩家对其他玩家、生物造成伤害时。被伤害的玩家、生物为事件中的目标玩家、生物,触发事件的玩家|事件中的位置|事件中的目标玩家|事件中的目标生物|事件中的目标生物类型,250001|240001|250002|300002|440002,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1030008,受到伤害,1,3,玩家,"1,0,0,0",任意玩家受到伤害,任意玩家受到伤害,,触发事件的玩家|事件中的位置,250001|240001,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1030009,移动,1,3,玩家,"1,0,0,0",任意玩家移动一格距离时,任意玩家移动一格,,触发事件的玩家|事件中的位置,250001|240001,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1030010,使用动画表情,1,3,玩家,"1,0,0,0",任意玩家使用动画表情,任意玩家使用动画表情,,触发事件的玩家|事件中的位置|事件中的动作表情,250001|240001|310001,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1030011,玩家使用道具,1,3,玩家,"1,0,0,0",任意玩家使用道具,任意玩家使用道具,,触发事件的玩家|事件中的位置|事件中的道具类型,250001|240001|270001,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1030012,玩家获得道具,1,3,玩家,"1,0,0,0",任意玩家获得道具,任意玩家获得道具,,触发事件的玩家|事件中的位置|事件中的道具类型,250001|240001|270001,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1030013,玩家消耗道具,1,3,玩家,"1,0,0,0",任意玩家消耗道具,任意玩家消耗道具,,触发事件的玩家|事件中的位置|事件中的道具类型,250001|240001|270001,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1030014,玩家点击生物,1,3,玩家,"1,0,0,0",任意玩家点击生物,任意玩家点击生物,,触发事件的玩家|事件中的位置|事件中的目标生物|事件中的目标生物类型,250001|240001|300002|440002,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1030015,玩家点击方块,1,3,玩家,"1,0,0,0",任意玩家点击方块,任意玩家点击方块,,触发事件的玩家|事件中的位置|事件中的方块类型,250001|240001|260002,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1030016,骑乘,1,3,玩家,"1,0,0,0",任意玩家骑乘任意坐骑、载具,任意玩家骑乘任意坐骑、载具,,触发事件的玩家|事件中的位置|事件中的目标生物|事件中的目标生物类型,250001|240001|300002|440002,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1030017,取消骑乘,1,3,玩家,"1,0,0,0",任意玩家取消骑乘,任意玩家取消骑乘,,触发事件的玩家|事件中的位置|事件中的目标生物|事件中的目标生物类型,250001|240001|300002|440002,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1030018,运动状态改变,1,3,玩家,"1,0,0,0",任意玩家进入|@1|状态时,任意玩家进入【运动状态】状态,,触发事件的玩家|事件中的位置,250001|240001,"1021,1",5|150002,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1030019,发生碰撞,1,3,玩家,"1,0,0,0",任意玩家与其他玩家/生物发生碰撞,任意玩家与其他玩家/生物发生碰撞,,触发事件的玩家|事件中的位置|事件中的目标玩家|事件中的目标生物,250001|240001|250002|300002,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1030020,拾取掉落物,1,3,玩家,"1,0,0,0",任意玩家拾取任意掉落物,任意玩家拾取任意掉落物,,触发事件的玩家|事件中的位置|事件中的道具类型|事件中的掉落物,250001|240001|270001|290001,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1030021,玩家进入区域,1,3,玩家,"1,0,0,0",任意玩家进入|@1|时,任意玩家进入【区域】,进入区域和离开区域事件无法触发开发者相关的动作，例如播放广告、显示商品购买窗口、打开开发者商店,触发事件的玩家|事件中的位置,250001|240001,"1001,1",,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1030022,玩家离开区域,1,3,玩家,"1,0,0,0",任意玩家离开|@1|时,任意玩家离开【区域】,进入区域和离开区域事件无法触发开发者相关的动作，例如播放广告、显示商品购买窗口、打开开发者商店,触发事件的玩家|事件中的位置,250001|240001,"1001,1",,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1030023,装备栏改变,1,3,玩家,"1,0,0,0",任意玩家装备栏发生改变,任意玩家装备栏发生改变,,触发事件的玩家|事件中的位置|事件中的道具类型|事件中的装备栏,250001|240001|270001|430001,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1030024,背包道具栏改变,1,3,玩家,"1,0,0,0",任意玩家背包道具栏发生改变,任意玩家背包道具栏发生改变,,触发事件的玩家|事件中的位置|事件中的道具类型,250001|240001|270001,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1030025,快捷道具栏改变,1,3,玩家,"1,0,0,0",任意玩家的快捷道具栏发生改变,任意玩家的快捷道具栏发生改变,,触发事件的玩家|事件中的位置|事件中的道具类型,250001|240001|270001,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1030026,选择快捷道具栏,1,3,玩家,"1,0,0,0",任意玩家选择任意序号的快捷栏,任意玩家选择任意序号的快捷栏,,触发事件的玩家|事件中的位置|事件中的道具类型|事件中的快捷栏,250001|240001|270001|400001,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1030027,丢弃道具,1,3,玩家,"1,0,0,0",任意玩家丢弃道具,任意玩家丢弃道具,,触发事件的玩家|事件中的位置|事件中的道具类型|事件中的掉落物,250001|240001|270001|290001,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1030028,穿上装备,1,3,玩家,"1,0,0,0",任意玩家穿上装备,任意玩家穿上装备,,触发事件的玩家|事件中的位置|事件中的道具类型|事件中的装备栏,250001|240001|270001|430001,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1030029,脱下装备,1,3,玩家,"1,0,0,0",任意玩家脱下装备,任意玩家脱下装备,,触发事件的玩家|事件中的位置|事件中的道具类型|事件中的装备栏,250001|240001|270001|430001,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1030030,聊天框显示字符串,1,3,玩家,"1,0,0,0",聊天框显示|@1,聊天框中显示|@1,,触发事件的玩家|事件中的位置|事件中的字符串,"250001|240001|550001
","1010,1",,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1030031,输入字符串,1,3,玩家,"1,0,0,0",任意玩家从聊天框输入|@1,任意玩家从聊天框输入【字符串】,,触发事件的玩家|事件中的位置|事件中的字符串,"250001|240001|550001
","1010,1",,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1030032,按下按键,1,3,玩家,"1,0,0,0",任意玩家按下|@1,任意玩家按下【按键】,,触发事件的玩家,250001,"1051,1",,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1030033,长按按键,1,3,玩家,"1,0,0,0",任意玩家长按|@1,任意玩家长按【按键】,,触发事件的玩家,250001,"1051,1",,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1030034,松开按键,1,3,玩家,"1,0,0,0",任意玩家松开|@1,任意玩家松开【按键】,,触发事件的玩家,250001,"1051,1",,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1030035,获得状态效果,1,3,玩家,"1,0,0,0",任意玩家获得状态效果,任意玩家获得状态效果,,触发事件的玩家|事件中的位置|事件中的状态效果,250001|240001|410001,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1030036,失去状态效果,1,3,玩家,"1,0,0,0",任意玩家失去状态效果,任意玩家失去状态效果,,触发事件的玩家|事件中的位置|事件中的状态效果,250001|240001|410001,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1030037,失去指定状态效果,1,3,玩家,"1,0,0,0",任意玩家失去|@1,任意玩家失去【状态】,,触发事件的玩家|事件中的位置|事件中的状态效果,250001|240001|410001,"1037,1",,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1030038,等级发生改变,1,3,玩家,"1,0,0,0",任意玩家等级发生改变,任意玩家等级发生改变,,触发事件的玩家|事件中的位置,250001|240001,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1030039,玩家输入聊天信息（包含字符串）,1,3,玩家,"1,0,0,0",任意玩家从聊天框输入包含|@1|的信息,任意玩家从聊天框输入包含【字符串】的信息,当玩家在聊天框中输入包含指定文本并发出时,触发事件的玩家|事件中的位置|事件中的字符串,"250001|240001|550001
","1010,1",,,,,,,,,,,,,,,,,,,,,2,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1040001,受到伤害,1,4,生物,"1,0,0,0",任意生物受到伤害时触发,任意生物受到伤害,,触发事件的生物|触发事件的生物类型|事件中的位置|事件中的目标玩家,300001|440001|240001|250002,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1040002,攻击,1,4,生物,"1,0,0,0",任意生物开始攻击,任意生物开始攻击,,触发事件的生物|事件中的位置|触发事件的生物类型|事件中的目标生物|事件中的目标生物类型|事件中的目标玩家,300001|240001|440001|300002|440002|250002,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1040003,攻击命中,1,4,生物,"1,0,0,0",任意生物攻击命中任意玩家、生物,任意生物攻击命中任意玩家、生物,,触发事件的生物|事件中的位置|触发事件的生物类型|事件中的目标生物|事件中的目标生物类型|事件中的目标玩家,300001|240001|440001|300002|440002|250002,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1040004,造成伤害,1,4,生物,"1,0,0,0",任意生物对任意玩家、生物造成伤害,任意生物对任意玩家、生物造成伤害,,触发事件的生物|事件中的位置|触发事件的生物类型|事件中的目标生物|事件中的目标生物类型|事件中的目标玩家,300001|240001|440001|300002|440002|250002,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1040005,生物被击败,1,4,生物,"1,0,0,0",任意生物被击败时,任意生物死亡,,触发事件的生物|触发事件的生物类型|事件中的位置,300001|440001|240001,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1040006,击败目标,1,4,生物,"1,0,0,0",任意生物击败玩家或其他生物,任意生物打败玩家或其他生物,,触发事件的生物|事件中的位置|触发事件的生物类型|事件中的目标生物|事件中的目标生物类型|事件中的目标玩家,300001|240001|440001|300002|440002|250002,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1040007,生物进入区域,1,4,生物,"1,0,0,0",任意生物进入|@1|时,任意生物进入【区域】,,触发事件的生物|触发事件的生物类型|事件中的位置,300001|440001|240001,"1001,1",,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1040008,生物离开区域,1,4,生物,"1,0,0,0",任意生物离开|@1|时,任意生物离开【区域】,,触发事件的生物|触发事件的生物类型|事件中的位置,300001|440001|240001,"1001,1",,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1040009,生物行动状态改变,1,4,生物,"1,0,0,0",任意生物进入|@1|状态时,任意生物进入【生物行动状态】,,触发事件的生物|触发事件的生物类型|事件中的位置,300001|440001|240001,"1034,1",5|200003,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1040010,碰撞时,1,4,生物,"1,0,0,0",任意生物碰撞玩家、其他生物时,任意生物碰撞玩家、其他生物时,,触发事件的生物|事件中的位置|触发事件的生物类型|事件中的目标生物|事件中的目标生物类型|事件中的目标玩家,300001|240001|440001|300002|440002|250002,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1040011,生物属性改变,1,4,生物,"1,0,0,0",任意生物|@1|变化时,任意生物【生物属性】变化,,触发事件的生物|触发事件的生物类型|事件中的位置,300001|440001|240001,"1035,1",5|210001,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1040012,被创建,1,4,生物,"1,0,0,0",任意生物被创建,任意生物被创建,,触发事件的生物|触发事件的生物类型|事件中的位置,300001|440001|240001,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1040013,获得状态效果,1,4,生物,"1,0,0,0",任意生物获得状态效果,任意生物获得状态效果,,触发事件的生物|触发事件的生物类型|事件中的位置|事件中的状态效果,300001|440001|240001|410001,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1040014,失去状态效果,1,4,生物,"1,0,0,0",任意生物失去状态效果,任意生物失去状态效果,,触发事件的生物|触发事件的生物类型|事件中的位置|事件中的状态效果,300001|440001|240001|410001,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1040015,失去指定状态效果,1,4,生物,"1,0,0,0",任意生物失去|@1,任意生物失去【状态】,,触发事件的生物|触发事件的生物类型|事件中的位置|事件中的状态效果,300001|440001|240001|410001,"1037,1",,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1040016,受到伤害时,1,4,生物,"0,1,0,0",此类生物受到伤害时,此类生物受到伤害时,,触发事件的生物|事件中的位置|触发事件的生物类型|事件中的目标玩家,300001|240001|440001|250002,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1040017,开始攻击时,1,4,生物,"0,1,0,0",此类生物开始攻击时,此类生物开始攻击时,,触发事件的生物|事件中的位置|触发事件的生物类型|事件中的目标生物|事件中的目标生物类型|事件中的目标玩家,300001|240001|440001|300002|440002|250002,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1040018,攻击命中时,1,4,生物,"0,1,0,0",此类生物攻击并命中目标时,此类生物攻击并命中目标时,,触发事件的生物|事件中的位置|触发事件的生物类型|事件中的目标生物|事件中的目标生物类型|事件中的目标玩家,300001|240001|440001|300002|440002|250002,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1040019,造成伤害时,1,4,生物,"0,1,0,0",此类生物对目标造成伤害时,此类生物对目标造成伤害时,,触发事件的生物|事件中的位置|触发事件的生物类型|事件中的目标生物|事件中的目标生物类型|事件中的目标玩家,300001|240001|440001|300002|440002|250002,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1040020,被击败时,1,4,生物,"0,1,0,0",此类生物被击败时,此类生物被击败时,,触发事件的生物|事件中的位置|触发事件的生物类型,300001|240001|440001,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1040021,击败目标时,1,4,生物,"0,1,0,0",此类生物击败目标时,此类生物击败目标时,,触发事件的生物|事件中的位置|触发事件的生物类型|事件中的目标生物|事件中的目标生物类型|事件中的目标玩家,300001|240001|440001|300002|440002|250002,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1040022,行动状态改变时,1,4,生物,"0,1,0,0",此类生物进入|@1,此类生物进入【生物行动状态】,,触发事件的生物|事件中的位置|触发事件的生物类型,300001|240001|440001,"1034,1",5|200003,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1040023,碰撞时,1,4,生物,"0,1,0,0",此类生物碰撞玩家或其他生物时,此类生物碰撞玩家或其他生物时,,触发事件的生物|事件中的位置|触发事件的生物类型|事件中的目标生物|事件中的目标生物类型|事件中的目标玩家,300001|240001|440001|300002|440002|250002,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1040024,属性改变时,1,4,生物,"0,1,0,0",此类生物|@1|变化时,此类生物【生物属性】变化时,,触发事件的生物|事件中的位置|触发事件的生物类型,300001|240001|440001,"1035,1",5|210001,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1040025,被创建时,1,4,生物,"0,1,0,0",此类生物被创建时,此类生物被创建时,,触发事件的生物|事件中的位置|触发事件的生物类型,300001|240001|440001,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1040026,获得状态时,1,4,生物,"0,1,0,0",此类生物获得任意状态时,此类生物获得任意状态时,,触发事件的生物|事件中的位置|触发事件的生物类型|事件中的状态效果,300001|240001|440001|410001,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1040027,失去状态时,1,4,生物,"0,1,0,0",此类生物失去任意状态时,此类生物失去任意状态时,,触发事件的生物|事件中的位置|触发事件的生物类型|事件中的状态效果,300001|240001|440001|410001,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1040028,被玩家点击时,1,4,生物,"0,1,0,0",此类生物被玩家点击时,此类生物被玩家点击时,,触发事件的生物|事件中的位置|触发事件的生物类型|事件中的目标玩家,300001|240001|440001|250002,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1050001,被挖掘完毕时,1,5,方块,"1,0,0,0",任意方块被挖掘完毕时,任意方块被挖掘完毕时,,事件中的位置|事件中的方块类型|触发事件的玩家|触发事件的生物|触发事件的生物类型,240001|260002|250001|300001|440001,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1050002,被破坏,1,5,方块,"1,0,0,0",任意方块被破坏时,任意方块被破坏时,,事件中的位置|事件中的方块类型,240001|260002,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1050003,方块开关状态,1,5,方块,"1,0,0,0",任意方块开关状态发生变化,任意方块充能状态发生变化,,事件中的位置|事件中的方块类型,240001|260002,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1050004,储存容器道具发生改变,1,5,方块,"1,0,0,0",任意储存容器内道具发生改变,任意储存容器内道具发生改变,,事件中的位置|事件中的方块类型|事件中的道具类型,240001|260002|270001,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1050005,被创建,1,5,方块,"1,0,0,0",任意方块被创建,任意方块被创建,,事件中的位置|事件中的方块类型,240001|260002,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1050006,储存容器道具被放入时,1,5,方块,"1,0,0,0",任意储存容器内道具被放入时,任意储存容器内道具被放入时,,事件中的位置|事件中的方块类型|事件中的道具类型,240001|260002|270001,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1050007,储存容器道具被取出时,1,5,方块,"1,0,0,0",任意储存容器内道具被取出时,任意储存容器内道具被取出时,,事件中的位置|事件中的方块类型|事件中的道具类型,240001|260002|270001,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1050008,被挖掘时,1,5,方块,"1,0,0,0",任意方块被挖掘时,任意方块被挖掘时,,事件中的位置|事件中的方块类型|触发事件的玩家|触发事件的生物|触发事件的生物类型,240001|260002|250001|300001|440001,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1050009,被挖掘中断时,1,5,方块,"1,0,0,0",任意方块被挖掘但中途取消时,任意方块被挖掘但中途取消时,,事件中的位置|事件中的方块类型|触发事件的玩家|触发事件的生物|触发事件的生物类型,240001|260002|250001|300001|440001,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1050010,被挖掘时,1,5,方块,"0,1,0,0",此类方块被挖掘时,此类方块被挖掘时,,触发事件的玩家|触发事件的生物|触发事件的生物类型|事件中的位置|事件中的方块类型,250001|300001|440001|240001|260002,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1050011,被挖掘完毕时,1,5,方块,"0,1,0,0",此类方块被挖掘完毕时,此类方块被挖掘完毕时,,触发事件的玩家|触发事件的生物|触发事件的生物类型|事件中的位置|事件中的方块类型,250001|300001|440001|240001|260002,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1050012,被挖掘中断时,1,5,方块,"0,1,0,0",此类方块被挖掘但中途取消时,此类方块被挖掘但中途取消时,,触发事件的玩家|触发事件的生物|触发事件的生物类型|事件中的位置|事件中的方块类型,250001|300001|440001|240001|260002,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1050013,被破坏时,1,5,方块,"0,1,0,0",此类方块被破坏时,此类方块被破坏时,,触发事件的玩家|触发事件的生物,250001|300001,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1050014,被创建时,1,5,方块,"0,1,0,0",此类方块被创建时,此类方块被创建时,,事件中的位置|事件中的方块类型,240001|260002,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1050015,开关状态变化时,1,5,方块,"0,0,0,0",此类方块的开关状态发生变化时,此类方块的开关状态发生变化时,,触发事件的玩家|触发事件的生物|触发事件的生物类型|事件中的位置|事件中的方块类型,250001|300001|440001|240001|260002,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1050016,被获取时,1,5,方块,"0,1,0,0",此类方块被玩家或生物获取时,此类方块被玩家或生物获取时,,触发事件的玩家|触发事件的生物|触发事件的生物类型|事件中的位置|事件中的方块类型,250001|300001|440001|240001|260002,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1050017,被玩家点击时,1,5,方块,"0,1,0,0",此类方块被玩家点击时,此类方块被玩家点击时,,触发事件的玩家|事件中的位置|事件中的方块类型,250001|240001|260002,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1060001,投掷物击中,1,6,道具,"1,0,0,0",任意投掷物击中任意玩家、生物、方块,任意投掷物命中任意玩家、生物、方块,,触发事件的投掷物|触发事件的玩家|触发事件的生物|事件中的道具类型|事件中的目标玩家|事件中的目标生物|事件中的目标生物类型|事件中的位置,280001|250001|300001|270001|250002|300002|440002|240001,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1060002,投掷物进入区域,1,6,道具,"1,0,0,0",任意投掷物进入|@1|时,任意投掷物进入【区域】,,触发事件的投掷物|触发事件的玩家|触发事件的生物|事件中的道具类型|事件中的位置,280001|250001|300001|270001|240001,"1001,1",,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1060003,投掷物离开区域,1,6,道具,"1,0,0,0",任意投掷物离开|@1|时,任意投掷物离开【区域】,,触发事件的投掷物|触发事件的玩家|触发事件的生物|事件中的道具类型|事件中的位置,280001|250001|300001|270001|240001,"1001,1",,,,·,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1060004,掉落物进入区域,1,6,道具,"1,0,0,0",任意掉落物进入|@1|时,任意掉落物进入【区域】,,事件中的掉落物|事件中的道具类型|事件中的位置,290001|270001|240001,"1001,1",,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1060005,掉落物离开区域,1,6,道具,"1,0,0,0",任意掉落物离开|@1|时,任意掉落物离开【区域】,,事件中的掉落物|事件中的道具类型|事件中的位置,290001|270001|240001,"1001,1",,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1060006,掉落物被拾取,1,6,道具,"1,0,0,0",任意掉落物被拾取,任意掉落物被拾取,,事件中的掉落物|事件中的道具类型|事件中的位置,290001|270001|240001,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1060007,掉落物消失,1,6,道具,"1,0,0,0",任意掉落物消失,任意掉落物消失,,事件中的掉落物|事件中的道具类型|事件中的位置,290001|270001|240001,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1060008,投掷物被创建,1,6,道具,"1,0,0,0",任意投掷物被创建,任意投掷物被创建,,触发事件的投掷物|事件中的道具类型|触发事件的玩家|触发事件的生物|触发事件的生物类型|事件中的位置,280001|270001|250001|300001|440001|240001,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1060009,被获取时,1,6,道具,"0,1,0,0",此类道具被玩家或生物获取时,此类道具被玩家或生物获取时,,触发事件的玩家|触发事件的生物|触发事件的生物类型|事件中的掉落物|事件中的位置|事件中的道具类型,250001|300001|440001|290001|240001|270001,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1060010,被丢弃时,1,6,道具,"0,1,0,0",此类道具被丢弃时,此类道具被丢弃时,,触发事件的玩家|事件中的掉落物|事件中的位置|事件中的道具类型,250001|290001|240001|270001,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1060011,消失时,1,6,道具,"0,1,0,0",此类道具掉落物消失时,此类道具掉落物消失时,,事件中的位置|事件中的道具类型,240001|270001,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1060012,损坏时,1,6,道具,"0,1,3|7|9|12,0",此类道具损坏时,此类道具损坏时,,事件中的位置|事件中的道具类型,240001|270001,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1060013,击中目标时,1,6,道具,"0,1,8|12,0",此类道具投掷物击中任意目标时,此类道具投掷物击中任意目标时,,触发事件的投掷物|触发事件的玩家|触发事件的生物|事件中的位置|事件中的道具类型|事件中的目标生物|事件中的目标生物类型|事件中的目标玩家|事件中的方块类型,280001|250001|300001|240001|270001|300002|440002|250002|260002,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1060014,被创建或消耗时,1,6,道具,"0,1,8|12,0",此类道具投掷物被创建或消耗时,此类道具投掷物被创建或消耗时,,触发事件的投掷物|事件中的位置|事件中的道具类型|触发事件的生物|触发事件的生物类型|触发事件的玩家,280001|240001|270001|300001|440001|250001,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1060015,被消耗时,1,6,道具,"0,1,11,0",此类道具被消耗时,此类道具被消耗时,,事件中的位置|事件中的道具类型|触发事件的生物|触发事件的生物类型|触发事件的玩家,240001|270001|300001|440001|250001,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1060016,被打开时,1,6,道具,"0,1,15,0",此类包裹道具被打开时,此类包裹道具被打开时,,事件中的位置|事件中的道具类型|触发事件的玩家,240001|270001|250001,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1060017,被穿上时,1,6,道具,"0,1,9,0",此类装备道具被穿上时,此类装备道具被穿上时,,事件中的位置|事件中的道具类型|触发事件的玩家,240001|270001|250001,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1060018,被脱下时,1,6,道具,"0,1,9,0",此类装备道具被脱下时,此类装备道具被脱下时,,事件中的位置|事件中的道具类型|触发事件的玩家,240001|270001|250001,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1060019,被破坏时,1,6,道具,"0,1,9,0",此类装备道具被玩家或生物破坏时,此类装备道具被玩家或生物破坏时,,事件中的位置|事件中的道具类型|触发事件的玩家,240001|270001|250001,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1060020,被使用时,1,6,道具,"0,1,2|3|7|8|10|11|12,0",此类道具被玩家使用时,此类道具被玩家使用时,,事件中的位置|事件中的道具类型|触发事件的玩家,240001|270001|250001,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1070001,任意位置上特效被创建,1,7,特效,"1,0,0,0",任意位置上特效被创建,任意位置上特效被创建,,事件中的位置|事件中的特效类型,240001|340001,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1070002,任意玩家身上特效被创建,1,7,特效,"1,0,0,0",任意玩家身上特效被创建,任意玩家身上特效被创建,,触发事件的玩家|事件中的位置|事件中的特效类型,250001|240001|340001,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1070003,任意生物身上特效被创建,1,7,特效,"1,0,0,0",任意生物身上特效被创建,任意生物身上特效被创建,,触发事件的生物|事件中的位置|事件中的特效类型,300001|240001|340001,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1070004,任意投掷物特效被创建,1,7,特效,"1,0,0,0",任意投掷物特效被创建,任意投掷物特效被创建,,触发事件的投掷物|事件中的位置|事件中的特效类型,280001|240001|340001,,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1080001,被生物获得时,1,8,状态,"0,1,0,0",此类状态被生物获得时,此类状态被生物获得时,,事件中的位置|事件中的状态效果|触发事件的生物|触发事件的生物类型,240001|410001|300001|440001,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1080002,从生物身上移除时,1,8,状态,"0,1,0,0",此类状态从生物身上移除时,此类状态从生物身上移除时,,事件中的位置|事件中的状态效果|触发事件的生物|触发事件的生物类型,240001|410001|300001|440001,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1080003,被玩家获得时,1,8,状态,"0,1,0,0",此类状态被玩家获得时,此类状态被玩家获得时,,事件中的位置|事件中的状态效果|触发事件的玩家,240001|410001|250001,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1080004,从玩家身上移除时,1,8,状态,"0,1,0,0",此类状态从玩家身上移除时,此类状态从玩家身上移除时,,事件中的位置|事件中的状态效果|触发事件的玩家,240001|410001|250001,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1090001,此配方合成完毕时,1,9,配方,"0,1,0,0",此类配方合成完毕时,此类配方合成完毕时,,事件中的配方|触发事件的玩家|触发事件的生物|事件中的道具类型|事件中的道具数量,450001|250001|300001|270001|480001,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1090002,配方合成完毕时,1,9,配方,"1,0,0,0",任意配方合成完毕时,任意配方合成完毕时,,事件中的配方|触发事件的玩家|触发事件的生物|触发事件的生物类型|事件中的道具类型|事件中的道具数量,450001|250001|300001|440001|270001|480001,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1100001,熔炼开始时,1,10,熔炼,"1,0,0,0",任意熔炼开始时,任意熔炼开始时,,事件中的位置|事件中的熔炼,240001|460001,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1100002,熔炼完成时,1,10,熔炼,"1,0,0,0",任意熔炼获得产出物时,任意熔炼获得产出物时,,事件中的位置|事件中的熔炼,240001|460001,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1100003,此熔炼开始时,1,10,熔炼,"0,1,0,0",此类熔炼开始时,此类熔炼开始时,,事件中的位置|事件中的熔炼,240001|460001,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1100004,此熔炼完成时,1,10,熔炼,"0,1,0,0",此类熔炼获得产出物时,此类熔炼获得产出物时,,事件中的位置|事件中的熔炼,240001|460001,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1110001,剧情开始时,1,11,剧情,"1,0,0,0",任意剧情开始时,任意剧情开始时,,事件中的剧情|触发事件的玩家|事件中的目标生物|事件中的目标生物类型,470001|250001|300002|440002,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1110002,剧情结束时,1,11,剧情,"1,0,0,0",任意剧情结束时,任意剧情结束时,,事件中的剧情|触发事件的玩家|事件中的目标生物|事件中的目标生物类型,470001|250001|300002|440002,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1110003,此剧情开始时,1,11,剧情,"0,1,0,0",此段剧情开始时,此段剧情开始时,,触发事件的玩家|事件中的目标生物|事件中的目标生物类型|事件中的剧情,250001|300002|440002|470001,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1110004,此剧情结束时,1,11,剧情,"0,1,0,0",此段剧情结束时,此段剧情结束时,,触发事件的玩家|事件中的位置,250001|300002|440002|470001,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1120001,当前界面任意按钮被松开,1,12,界面,"1,1,0,0",当前界面任意按钮被松开,当前界面任意按钮被松开,,触发事件的玩家|事件中的元件|事件中的位置,250001|510001|240001,,,,,,,,,,,,,,,,,,,,,2,1,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1120002,当前编辑界面被打开时,1,12,界面,"1,1,0,0",当前编辑界面被打开时,当前编辑界面被打开时,,事件中的界面|事件中的位置|触发事件的玩家,520002|240001|250001,,,,,,,,,,,,,,,,,,,,,,1,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1120003,当前编辑界面关闭时,1,12,界面,"1,1,0,0",当前编辑界面关闭时,当前编辑界面关闭时,,事件中的界面|事件中的位置|触发事件的玩家,520002|240001|250001,,,,,,,,,,,,,,,,,,,,,,1,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1120004,当前界面任意按钮被按下,1,12,界面,"1,1,0,0",当前界面任意按钮被按下,当前界面任意按钮被按下,,触发事件的玩家|事件中的元件|事件中的位置,250001|510001|240001,,,,,,,,,,,,,,,,,,,,,2,1,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1120005,当任意玩家输入任意字符串,1,12,界面,"1,1,0,0",当任意玩家输入任意字符串,当任意玩家输入任意字符串,,触发事件的玩家|事件中的字符串|事件中的元件,250001|550001|510001,,,,,,,,,,,,,,,,,,,,,2,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1120006,当前界面指定按钮被松开,1,12,界面,"1,1,0,0",当前界面的|@1|被松开,【按钮】被松开,,触发事件的玩家|事件中的元件|事件中的位置,250001|510001|240001,"1075,0,4",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1120007,当前界面指定按钮被按下,1,12,界面,"1,1,0,0",当前界面的|@1|被按下,【按钮】被按下,,触发事件的玩家|事件中的元件|事件中的位置,250001|510001|240001,"1075,0,4",,,,,,,,,,,,,,,,,,,,,1,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1120008,当玩家在当前界面的输入框输入任意内容,1,12,界面,"1,1,0,0",在玩家在当前界面的|@1|输入任意内容,在【输入框】输入任意字符串,,触发事件的玩家|事件中的字符串|事件中的元件,250001|550001|510001,"1075,0,7",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1130001,任意音乐开始播放,1,13,音效,"1,1,0,0",任意音乐开始播放时,任意音乐开始播放时,,触发事件的玩家|事件中的音乐|事件中的位置,250001|570001|240001,,,,,,,,,,,,,,,,,,,,,,3,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1140001,任意玩家在本地图购买1个月大会员,1,14,开发者,"1,1,0,0",任意玩家在本地图购买1个月大会员,任意玩家在本地图购买1个月大会员,,触发事件的玩家|事件中的位置,250001|240001,,,,,,,,,,,,,,,,,,,,,6,21,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1140002,任意玩家在本地图购买3个月大会员,1,14,开发者,"1,1,0,0",任意玩家在本地图购买3个月大会员,任意玩家在本地图购买3个月大会员,,触发事件的玩家|事件中的位置,250001|240001,,,,,,,,,,,,,,,,,,,,,6,21,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1140003,任意玩家购买或提取开发者商店道具,1,14,开发者,"1,1,0,0",任意玩家购买或提取开发者商店道具,任意玩家购买或提取开发者商店道具,,事件中的道具类型|触发事件的玩家|事件中的位置,270001|250001|240001,,,,,,,,,,,,,,,,,,,,,1,21,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2010001,数值对比,2,1,数学,"1,1,0,0",@1||@2||@3,【数值】【比较计算符】【数值】,,,,"1005,1",,"1016,1",5|120001,"1005,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2010002,随机数,2,1,数学,"1,1,0,0",@1||@2||@3,【数值】【比较计算符】【数值】,,,,"1005,1",3|20003,"1016,1",5|120001,"1005,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2010003,最大值或最小值,2,1,数学,"1,1,0,0",@1||@2||@3,【数值】【比较计算符】【数值】,,,,"1005,1",3|20004,"1016,1",5|120001,"1005,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2010004,整除,2,1,数学,"1,1,0,0",@1||@2||@3,【数值】【比较计算符】【数值】,,,,"1005,1",3|20005,"1016,1",5|120001,"1005,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2010005,位置的坐标值,2,1,数学,"1,1,0,0",@1||@2||@3,【数值】【比较计算符】【数值】,,,,"1005,1",3|20006,"1016,1",5|120001,"1005,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2010006,位置与位置的水平角度,2,1,数学,"1,1,0,0",@1||@2||@3,【数值】【比较计算符】【数值】,,,,"1005,1",3|20007,"1016,1",5|120001,"1005,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2010007,位置与位置的垂直角度,2,1,数学,"1,1,0,0",@1||@2||@3,【数值】【比较计算符】【数值】,,,,"1005,1",3|20008,"1016,1",5|120001,"1005,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2010008,幂运算,2,1,数学,"1,1,0,0",@1||@2||@3,【数值】【比较计算符】【数值】,,,,"1005,1",3|20009,"1016,1",5|120001,"1005,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2010009,开方,2,1,数学,"1,1,0,0",@1||@2||@3,【数值】【比较计算符】【数值】,,,,"1005,1",3|20010,"1016,1",5|120001,"1005,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2010010,绝对值,2,1,数学,"1,1,0,0",@1||@2||@3,【数值】【比较计算符】【数值】,,,,"1005,1",3|20011,"1016,1",5|120001,"1005,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2010011,三角函数,2,1,数学,"1,1,0,0",@1||@2||@3,【数值】【比较计算符】【数值】,,,,"1005,1",3|20012,"1016,1",5|120001,"1005,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2010012,取整,2,1,数学,"1,1,0,0",@1||@2||@3,【数值】【比较计算符】【数值】,,,,"1005,1",3|20015,"1016,1",5|120001,"1005,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2010013,角度与弧度转换,2,1,数学,"1,1,0,0",@1||@2||@3,【数值】【比较计算符】【数值】,,,,"1005,1",3|20016,"1016,1",5|120001,"1005,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2010014,逻辑与,2,1,数学,"1,1,0,0",@1||@2||@3,【布尔值】【是否】【布尔值】,,,,"1014,1",3|20017,"1012,1",5|90001,"1014,1",5|320002,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2010015,逻辑或,2,1,数学,"1,1,0,0",@1||@2||@3,【布尔值】【是否】【布尔值】,,,,"1014,1",3|20018,"1012,1",5|90001,"1014,1",5|320002,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2010016,逻辑异或,2,1,数学,"1,1,0,0",@1||@2||@3,【布尔值】【是否】【布尔值】,,,,"1014,1",3|20019,"1012,1",5|90001,"1014,1",5|320002,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2010017,逻辑非,2,1,数学,"1,1,0,0",@1||@2||@3,【布尔值】【是否】【布尔值】,,,,"1014,1",3|20020,"1012,1",5|90001,"1014,1",5|320002,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2010018,任意值比较,2,1,数学,"1,1,0,0",@1||@2||@3,【任意值】【是否】【任意值】,,,,"1053,1",,"1012,1",5|90001,"1053,1",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2010019,逻辑与（新）,2,1,数学,"1,1,0,0",@1|与|@2,【布尔值】与【布尔值】,,,,"1014,1",,"1014,1",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2010020,逻辑或（新）,2,1,数学,"1,1,0,0",@1|或|@2,【布尔值】或【布尔值】,,,,"1014,1",,"1014,1",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2010021,逻辑非（新）,2,1,数学,"1,1,0,0",非|@1,非【布尔值】,,,,"1014,1",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2020001,游戏内时间,2,2,游戏,"1,1,0,0",@1||@2||@3,【数值】【比较计算符】【数值】,,,,"1005,1",3|30001,"1016,1",5|120001,"1005,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2020002,游戏中玩家的数量,2,2,游戏,"1,1,0,0",@1||@2||@3,【数值】【比较计算符】【数值】,,,,"1005,1",3|60003,"1016,1",5|120001,"1005,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2020003,天气判断,2,2,游戏,"1,1,0,0",@1||@2||@3,【天气】【是否】【天气】,,,,"1000,1",3|100001,"1012,1",5|90001,"1000,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2020004,重力判断,2,2,游戏,"1,1,0,0",@1||@2||@3,【数值】【比较计算符】【数值】,,,,"1005,1",3|30003,"1016,1",5|120001,"1005,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2030001,队伍属性判断,2,3,队伍,"1,1,0,0",@1||@2||@3,【数值】【比较计算符】【数值】,,,,"1005,1",3|40001,"1016,1",5|120001,"1005,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2040001,玩家属性判断,2,4,玩家,"1,1,0,0",@1||@2||@3,【数值】【比较计算符】【数值】,,,,"1005,1",3|50003,"1016,1",5|120001,"1005,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2040002,拥有的道具数量,2,4,玩家,"1,1,0,0",@1||@2||@3,【数值】【比较计算符】【数值】,,,,"1005,1",3|50004,"1016,1",5|120001,"1005,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2040003,玩家编号判断,2,4,玩家,"1,1,0,0",@1||@2||@3,【数值】【比较计算符】【数值】,,,,"1005,1",3|60002,"1016,1",5|120001,"1005,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2040004,玩家队伍判断,2,4,玩家,"1,1,0,0",@1||@2||@3,【队伍】【是否】【队伍】,,,,"1023,1",3|50018,"1012,1",5|90001,"1023,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2040005,玩家个体判断,2,4,玩家,"1,1,0,0",@1||@2||@3,【玩家】【是否】【玩家】,,,,"1018,1",6|250001,"1012,1",5|90001,"1018,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2040006,玩家面向角度,2,4,玩家,"1,1,0,0",@1||@2||@3,【数值】【比较计算符】【数值】,,,,"1005,1",3|50006,"1016,1",5|120001,"1005,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2040007,处于玩家组的判断,2,4,玩家,"1,1,0,0",@1||@2|处于|@3|中,【玩家】【是否】处于【玩家组】中,,,,"1018,1",6|250001,"1012,1",5|90001,"1019,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2040008,处于区域的判断,2,4,玩家,"1,1,0,0",@1||@2|处于|@3|中,【玩家】【是否】处于【区域】中,,,,"1018,1",6|250001,"1012,1",5|90001,"1001,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2040009,玩家状态判断,2,4,玩家,"1,1,0,0",@1||@2|处于|@3|中,【玩家】【是否】处于【状态】中,,,,"1018,1",6|250001,"1012,1",5|90001,"1037,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2040010,动画表情判断,2,4,玩家,"1,1,0,0",@1||@2|为|@3,【动画表情】【是否】为【动画表情】,,,,"1041,1",6|310001,"1012,1",5|90001,"1041,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2040011,玩家设置判断,2,4,玩家,"1,1,0,0",@1|的|@2|为|@3,【玩家】的【玩家设置】为【布尔值】,,,,"1018,1",6|250001,"1022,1",,"1014,1",5|320002,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2040012,选择快捷栏键位,2,4,玩家,"1,0,0,0",@1||@2||@3,【快捷栏按键】【是否】【快捷栏按键】,,,,"1052,1",6|400001,"1012,1",5|90001,"1052,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2040013,手持道具类型判断,2,4,玩家,"1,1,0,0",@1||@2||@3,【道具类型】【是否】【道具类型】,,,,"1028,1",3|50034,"1012,1",5|90001,"1028,1",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2040014,装备栏判断,2,4,玩家,"1,0,0,0",@1||@2||@3,【装备栏】【是否】【装备栏】,,,,"1054,1",6|430001,"1012,1",5|90001,"1054,1",5|420001,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2040015,玩家穿有装备判断,2,4,玩家,"1,1,0,0",@1||@2|穿有|@3,【玩家】【是否】穿有【装备类型】,,,,"1018,1",6|250001,"1012,1",5|90001,"1055,1",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2040016,玩家外观判断,2,4,玩家,"1,0,0,0",@1||@2||@3,【外观类型】【是否】【外观类型】,,,,"1060,1",3|50037,"1012,1",5|90001,"1060,1",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2050001,玩家组中的玩家数量,2,5,玩家组,"1,1,0,0",@1||@2||@3,【数值】【比较计算符】【数值】,,,,"1005,1",3|60001,"1016,1",5|120001,"1005,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2050002,玩家组中的玩家的编号,2,5,玩家组,"1,1,0,0",@1||@2||@3,【数值】【比较计算符】【数值】,,,,"1005,1",3|60002,"1016,1",5|120001,"1005,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2050003,玩家组中指定编号的玩家,2,5,玩家组,"1,1,0,0",@1||@2||@3,【玩家】【是否】【玩家】,,,,"1018,1",3|60004,"1012,1",5|90001,"1018,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2060001,生物属性,2,6,生物,"1,1,0,0",@1||@2||@3,【数值】【比较计算符】【数值】,,,,"1005,1",3|70002,"1016,1",5|120001,"1005,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2060003,处于生物组的判断,2,6,生物,"1,1,0,0",@1||@2|处于|@3|中,【生物】【是否】处于【生物组】中,,,,"1031,1",6|300001,"1012,1",5|90001,"1033,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2060004,生物类型判断,2,6,生物,"1,1,0,0",@1||@2||@3,【生物】【是否】【生物类型】,,,,"1031,1",6|300001,"1012,1",5|90001,"1032,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2060005,生物个体判断,2,6,生物,"1,1,0,0",@1||@2||@3,【生物】【是否】【生物】,,,,"1031,1",6|300001,"1012,1",5|90001,"1031,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2060006,生物面向角度,2,6,生物,"1,1,0,0",@1||@2||@3,【数值】【比较计算符】【数值】,,,,"1005,1",3|70005,"1016,1",5|120001,"1005,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2060007,处于区域的判断,2,6,生物,"1,1,0,0",@1||@2|处于|@3|中,【生物】【是否】处于【区域】中,,,,"1031,1",6|300001,"1012,1",5|90001,"1001,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2060008,生物状态判断,2,6,生物,"1,1,0,0",@1||@2|处于|@3|中,【生物】【是否】处于【状态】中,,,,"1031,1",6|300001,"1012,1",5|90001,"1037,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2060009,生物队伍判断,2,6,生物,"1,1,0,0",@1||@2||@3,【队伍】【是否】【队伍】,,,,"1023,1",3|70012,"1012,1",5|90001,"1023,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2060010,生物组中生物编号判断,2,6,生物,"1,1,0,0",@1||@2||@3,【数值】【比较计算符】【数值】,,,,"1005,1",3|70004,"1016,1",5|120001,"1005,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2060011,生物设置判断,2,6,生物,"1,1,0,0",@1|的|@2|为|@3,【生物】的【生物设置】为【布尔值】,,,,"1031,1",6|300001,"1036,1",5|220001,"1014,1",5|320002,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2060012,生物外观判断,2,6,生物,"1,0,0,0",@1||@2||@3,【外观类型】【是否】【外观类型】,,,,"1060,1",3|70017,"1012,1",5|90001,"1060,1",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2070001,生物组中生物的数量,2,7,生物组,"1,1,0,0",@1||@2||@3,【数值】【比较计算符】【数值】,,,,"1005,1",3|80001,"1016,1",5|120001,"1005,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2070002,生物组中的指定编号的生物,2,7,生物组,"1,1,0,0",@1||@2||@3,【生物】【是否】【生物】,,,,"1031,1",3|80002,"1012,1",5|90001,"1031,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2080001,掉落物数量判断,2,8,道具,"1,1,0,0",@1||@2||@3,【数值】【比较计算符】【数值】,,,,"1005,1",3|90002,"1016,1",5|120001,"1005,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2080002,掉落物处于区域内,2,8,道具,"1,1,0,0",@1||@2|处于|@3|中,【掉落物】【是否】处于【区域】中,,,,"1030,1",6|290001,"1012,1",5|90001,"1001,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2080003,道具类型判断,2,8,道具,"1,1,0,0",@1||@2||@3,【道具类型】【是否】【道具类型】,,,,"1028,1",6|270001,"1012,1",5|90001,"1028,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2080004,投掷物处于区域内,2,8,道具,"1,1,0,0",@1||@2|处于|@3|中,【投掷物】【是否】处于【区域】中,,,,"1044,1",6|280001,"1012,1",5|90001,"1001,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2090001,位置与位置之间的距离,2,9,方块,"1,1,0,0",@1||@2||@3,【数值】【比较计算符】【数值】,,,,"1005,1",3|100002,"1016,1",5|120001,"1005,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2090002,方块类型判断,2,9,方块,"1,1,0,0",@1||@2||@3,【方块类型】【是否】【方块类型】,,,,"1025,1",,"1012,1",5|90001,"1025,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2090003,方块开关状态,2,9,方块,"1,1,0,0",@1||@2||@3,【布尔值】【是否】【布尔值】,,,,"1014,1",3|100005,"1012,1",5|90001,"1014,1",5|320002,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2090004,方块处于区域判断,2,9,方块,"1,1,0,0",@1||@2|处于|@3|中,【方块类型】【是否】处于【区域】中,,,,"1025,1",,"1012,1",5|90001,"1001,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2090005,方块的朝向,2,9,方块,"1,1,0,0",处于|@1|的方块朝向|@2|为|@3,处于【位置】的方块朝向【朝向】为【布尔值】,,,,"1002,1",6|240001,"1003,1",5|20001,"1014,1",5|320002,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2090006,指定位置的方块类型,2,9,方块,"1,1,0,0",处于|@1|的方块类型|@2||@3,处于【位置】的方块类型【是否】【方块类型】,,,,"1002,1",6|240001,"1012,1",5|90001,"1025,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2090007,方块设置判断,2,9,方块,"1,1,0,0",@1|的|@2|为|@3,【方块类型】的【方块设置】为【布尔值】,,,,"1025,1",,"1026,1",5|190001,"1014,1",5|320002,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2100001,计时器时间判断,2,10,工具,"1,1,0,0",@1||@2||@3,【数值】【比较计算符】【数值】,,,,"1005,1",3|10009,"1016,1",5|120001,"1005,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2100002,布尔值比较,2,10,工具,"1,1,0,0",@1||@2||@3,【布尔值】【是否】【布尔值】,,,,"1014,1",,"1012,1",5|90001,"1014,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2100003,字符串比较,2,10,工具,"1,1,0,0",@1||@2||@3,【字符串】【是否】【字符串】,,,,"1010,1",,"1012,1",5|90001,"1010,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2100004,音乐类型判断,2,10,工具,"1,1,0,0",@1||@2||@3,【音乐】【是否】【音乐】,,,,"1080,1",6|570001,"1012,1",5|90001,"1080,1",,,,,,,,,,,,,,,,,3,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2100005,从服务器表获取值完成判断,2,10,工具,"1,1,0,0",从服务器表或排行榜获取值完成判断为|@1,从服务器表或排行榜获取值完成判断为【布尔值】,"1.承接从表或排行榜获取值的动作
2.会将该触发器的所有参数传递过来（例如触发事件玩家，触发事件生物等等）",,,"1014,1",5|320001,,,,,,,,,,,,,,,,,,,,9,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2100006,判断玩家大会员身份,2,10,工具,"1,1,0,0",@1||@2|迷你世界大会员为|@3,【玩家】【是否】迷你世界大会员为【布尔值】,,,,"1018,1",6|250001,"1012,1",5|90001,"1014,1",5|320001,,,,,,,,,,,,,,,6,12,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2110001,特定玩家的载具,2,11,载具,"1,1,0,0",@1||@2||@3,【生物】【是否】【生物】,,,,"1031,1",3|110001,"1012,1",5|90001,"1031,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2120001,特效判断,2,12,特效,"1,1,0,0",@1||@2||@3,【特效】【是否】【特效】,,,,"1045,1",,"1012,1",5|90001,"1045,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2130001,播放广告完成判断,2,13,播放,"1,0,0,1",当前播放广告完成判断为|@1,当前播放广告完成判断为【布尔值】,,,,"1014,1",5|320001,,,,,,,,,,,,,,,,,,,1,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2140001,状态效果判断,2,14,状态,"1,1,0,0",@1||@2||@3,【状态】【是否】【状态】,,,,"1037,1",6|410001,"1012,1",5|90001,"1037,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2150001,元件判断,2,15,界面,"1,1,0,0,520002",@1||@2||@3|的|@4,【元件】【是否】【界面】的【元件】,,,,"1075,1,4|7",6|510001,"1012,1",5|90001,"1074,1",6|520001,"1075,1,4|7",,,,,,,,,,,,,,,1,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
2150002,页面判断,2,15,界面,"1,1,0,0",@1||@2||@3,【界面】【是否】【界面】,,,,"1074,1",6|520002,"1012,1",5|90001,"1074,1",,,,,,,,,,,,,,,,,1,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3010001,改变全局天气模式,3,1,世界,"1,1,0,0",改变全局天气模式为|@1,改变当前天气为【天气】,,,,"1000,1",5|10003,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3010002,改变时间,3,1,世界,"1,1,0,0",改变当前时间为|@1,改变当前时间为【数值】,,,,"1005,1",1|0,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3010003,改变重力,3,1,世界,"1,1,0,0",改变当前重力为|@1,改变当前重力为【数值】,,,,"1005,1",1|0,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3010004,允许/禁止游戏中途加入,3,1,世界,"1,1,0,0",设置游戏中途加入为|@1,设置游戏中途加入为【开关】,,,,"1013,1",6|100002,,,,,,,,,,,,,,,,,,,,9,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3010013,改变地形组天气,3,1,世界,"1,1,0,0",改变|@1|的天气为|@2,改变【地形组】的天气为【天气】,,,,"1137,1",5|990001,"1000,1",5|10003,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3020001,运行触发器,3,2,触发器,"1,1,0,0",运行|@1|，检查如果条件为|@2|，则执行动作,运行【触发器】，检查如果条件为【布尔值】，则执行动作,,,,"1038,1",,"1014,1",5|320002,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3020002,循环运行触发器,3,2,触发器,"1,1,0,0",从|@1|到|@2|，执行|@3,从【数值】到【数值】，执行【触发器】,,,,"1005,1",1|1,"1005,1",1|2,"1038,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3020003,改变触发器状态,3,2,触发器,"1,1,0,0",改变|@1|状态开启为|@2,改变【触发器】状态开启为【开关】,,,,"1038,1",,"1013,1",5|100001,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3020004,改变触发器组状态,3,2,触发器,"1,1,0,0",改变|@1|状态开启为|@2,改变【触发器组】状态开启为【开关】,,,,"1039,1",,"1013,1",5|100001,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3040001,创建方块,3,4,方块,"1,1,0,0",在|@1|上创建|@2|，朝向为|@3,在【位置】上创建【方块类型】，朝向为【朝向】,,,,"1002,1",6|240001,"1025,1",2|200,"1003,1",5|20001,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3040002,摧毁方块,3,4,方块,"1,1,0,0",摧毁在|@1|上的方块,摧毁在【位置】上的方块,,,,"1002,1",6|240001,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3040003,替换方块,3,4,方块,"1,1,0,0",替换在|@1|上的方块为|@2|，朝向为|@3,替换在【位置】上的方块为【方块类型】，朝向为【朝向】,,,,"1002,1",6|240001,"1025,1",,"1003,1",5|20001,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3040004,改变开关状态,3,4,方块,"1,1,0,0",改变|@1|的方块开关状态为|@2,改变【位置】的方块开关状态为【开关】,,,,"1002,1",6|240001,"1013,1",5|100001,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3040005,改变方块设置开关,3,4,方块,"1,1,0,0",改变|@1|的|@2|设置为|@3,改变【方块类型】的【方块设置】设置为【开关】,,,,"1025,1",,"1026,1",5|190001,"1013,1",5|100001,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3040006,以方块填充区域,3,4,方块,"1,1,0,0",以|@1|填充|@2|，方块朝向为|@3,以【方块类型】填充【区域】，方块朝向为【朝向】,,,,"1025,1",,"1001,1",,"1003,1",5|20001,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3040007,清空区域内方块,3,4,方块,"1,1,0,0",清空|@1|内的|@2,清空【区域】内的【方块类型】,,,,"1001,1",,"1025,1",,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3040008,复制区域内方块并放置在指定点,3,4,方块,"1,1,0,0",复制|@1|内的方块并放置在|@2|，朝向为|@3,复制【区域】内的方块并放置在【位置】，朝向为【朝向】,,,,"1001,1",,"1002,1",6|240001,"1003,1",5|20001,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3040009,区域替换方块,3,4,方块,"1,1,0,0",将|@1|中的|@2|替换为|@3|，方块朝向为|@4,将【区域】中的【方块类型】替换为【方块类型】，方块朝向为【朝向】,,,,"1001,1",,"1025,1",,"1025,1",,"1003,1",5|20001,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3040010,破坏方块,3,4,方块,"1,1,0,0",破坏在|@1|上的方块,破坏在【位置】上的方块,,,,"1002,1",,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3050001,击败玩家,3,5,玩家,"1,1,0,0",击败|@1,击败【玩家】,,,,"1018,1",6|250001,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3050002,复活到指定点,3,5,玩家,"1,1,0,0",@1|复活到|@2,【玩家】复活到【位置】,,,,"1018,1",6|250001,"1002,1",6|240001,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3050003,改变玩家复活点位置,3,5,玩家,"1,1,0,0",改变|@1|复活点位置至|@2,改变【玩家】复活点位置至【位置】,,,,"1018,1",6|250001,"1002,1",6|240001,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3050004,设置属性,3,5,玩家,"1,1,0,0",设置|@1|的|@2|为|@3,设置【玩家】的【玩家属性】为【数值】,,,,"1018,1",6|250001,"1020,1",5|140001,"1005,1",1|0,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3050005,获得状态效果,3,5,玩家,"1,1,0,0",@1|获得|@2|效果，永久持续为|@3|，持续|@4|秒,【玩家】获得【状态】效果，永久持续为【布尔值】，持续【数值】秒,,,,"1018,1",6|250001,"1037,1",,"1014,1",5|320002,"1005,1",1|0,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3050006,移除状态效果,3,5,玩家,"1,1,0,0",移除|@1|的|@2|效果,移除【玩家】的【状态】效果,,,,"1018,1",6|250001,"1037,1",,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3050007,改变玩家设置开关,3,5,玩家,"1,1,0,0",设置|@1|的|@2|设置为 |@3,设置【玩家】的【玩家设置】设置为 【开关】,,,,"1018,1",6|250001,"1022,1",,"1013,1",5|100001,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3050008,移除玩家,3,5,玩家,"1,1,0,0",移除|@1|出本局游戏,移除【玩家】出本局游戏,,,,"1018,1",6|250001,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3050009,加入游戏观战,3,5,玩家,"1,1,0,0",使|@1|加入游戏观战,使【玩家】加入游戏观战,,,,"1018,1",6|250001,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3050010,改变玩家队伍,3,5,玩家,"1,1,0,0",改变|@1|队伍为|@2,改变【玩家】队伍为【队伍】,,,,"1018,1",6|250001,"1023,1",5|170001,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3050011,改变玩家位置,3,5,玩家,"1,1,0,0",改变|@1|的当前位置至|@2,改变【玩家】的当前位置至【位置】,,,,"1018,1",6|250001,"1002,1",,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3050012,改变玩家朝向,3,5,玩家,"1,1,0,0",改变|@1|的面向为|@2,改变【玩家】的面向为【朝向】,,,,"1018,1",6|250001,"1003,1",5|20001,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3050013,改变玩家镜头偏移,3,5,玩家,"1,1,0,0",设置|@1|的镜头偏移为水平Yaw|@2|垂直Pitch|@3,设置【玩家】的镜头偏移为水平Yaw【数值】垂直Pitch【数值】,,,,"1018,1",6|250001,"1005,1",1|1,"1005,1",1|1,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3050014,添加道具至玩家背包,3,5,玩家,"1,1,0,0",添加道具|@1||@2|个至|@3|背包,添加道具【道具类型】【数值】个至【玩家】背包,,,,"1028,1",,"1005,1",1|1,"1018,1",6|250001,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3050016,丢弃玩家背包的道具,3,5,玩家,"1,1,0,0",丢弃|@1|身上的|@2|个|@3,丢弃【玩家】身上的【数值】个【道具类型】,,,,"1018,1",6|250001,"1005,1",,"1028,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3050017,销毁玩家背包的道具,3,5,玩家,"1,1,0,0",销毁|@1|身上的|@2|的|@3,销毁【玩家】身上的【数值】的【道具类型】,,,,"1018,1",6|250001,"1005,1",,"1028,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3050018,骑乘载具,3,5,玩家,"1,1,0,0",使得|@1|登上|@2|，位置编号为|@3,使得【玩家】登上【生物】，位置编号为【数值】,,,,"1018,1",6|250001,"1031,1",,"1005,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3050019,脱离骑乘的载具,3,5,玩家,"1,1,0,0",使得|@1|离开当前载具,使得【玩家】离开当前载具,,,,"1018,1",6|250001,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3050020,改变视角,3,5,玩家,"1,1,0,0",改变|@1|的视角为|@2|，并|@3|锁定,改变【玩家】的视角为【视角】，并【开关】锁定,,,,"1018,1",6|250001,"1004,1",,"1013,1",5|100001,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3050021,控制载具自动前进,3,5,玩家,"1,1,0,0",使|@1|驾驶的载具自动前进,使【玩家】驾驶的载具自动前进,,,,"1018,1",6|250001,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3050022,玩家自动寻路,3,5,玩家,"1,1,0,0",使|@1|从当前位置自动寻路到|@2|，玩家可操作为|@3,是【玩家】从当前位置自动寻路到【位置】，玩家可操作为【开关】,该触发器有距离限制，超过一定（约32个方块）范围会导致无法触发,,,"1018,1",6|250001,"1002,1",,"1013,1",5|100001,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3050023,使玩家胜利,3,5,玩家,"1,1,0,0",使|@1|获得游戏胜利,使【玩家】获得游戏胜利,,,,"1018,1",6|250001,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3050024,触发玩家播放动画表情,3,5,玩家,"1,1,0,0",播放|@1|对|@2,播放【动画表情】对【玩家】,动画表情参数输入值范围1-20 人物动作1-10 通用动作11-20,,,"1041,1",,"1018,1",6|250001,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3050025,对玩家显示飘窗文字,3,5,玩家,"1,1,0,0",对|@1|显示飘窗文字，文字内容为|@2,对【玩家】显示飘窗文字，文字内容为【字符串】,,,,"1018,1",6|250001,"1010,1",,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3050026,使用快捷栏道具,3,5,玩家,"1,1,0,0",使|@1|使用快捷栏选中道具,使【玩家】使用快捷栏选中道具,,,,"1018,1",6|250001,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3050027,对玩家显示聊天框信息,3,5,玩家,"1,1,0,0",对|@1|显示聊天框信息，内容为|@2,对【玩家】显示聊天框信息，内容为【字符串】,,,,"1018,1",6|250001,"1010,1",,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3050028,指定道具不可丢弃,3,5,玩家,"1,1,0,0",设置|@1|的|@2|不可丢弃为|@3,设置【玩家】的【道具类型】不可丢弃,,,,"1018,1",6|250001,"1028,1",,"1013,1",5|100001,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3050029,指定道具不可掉落,3,5,玩家,"1,1,0,0",设置|@1|的|@2|不可掉落为|@3,设置【玩家】的【道具类型】不可掉落,,,,"1018,1",6|250001,"1028,1",,"1013,1",5|100001,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3050030,使玩家朝方向移动,3,5,玩家,"1,1,0,0",使|@1|朝|@2|移动，速度为|@3,使【玩家】朝【方向】移动，速度为【数值】,,,,"1018,1",6|250001,"1047,1",,"1005,1",1|0,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,4,4,,2,5,7,10,17,20,27,30,0,0,0,0,玩家,方向,数值,数值,,,,,"1018,1","1047,1","1005,1","1005,1"
3050031,替换状态效果,3,5,玩家,"1,1,0,0",将|@1|身上的|@2|替换为|@3|，永久持续为|@4|，持续|@5|秒,将【玩家】身上的【状态】替换为【状态】，永久持续为【布尔值】，持续【数值】秒,,,,"1018,1",6|250001,"1037,1",,"1037,1",,"1014,1",5|320002,"1005,1",1|0,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3050032,使玩家穿上装备,3,5,玩家,"1,1,0,0",使|@1|穿上背包中的|@2,使【玩家】穿上背包中的【装备类型】,,,,"1018,1",6|250001,"1055,1",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3050033,使玩家脱下装备,3,5,玩家,"1,1,0,0",使|@1|脱下|@2|的装备至背包中,使【玩家】脱下【装备栏】的装备至背包中,,,,"1018,1",6|250001,"1054,1",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3050034,创建装备,3,5,玩家,"1,1,0,0",创建|@1|至|@2|装备栏中,创建【装备类型】至【玩家】装备栏中,,,,"1055,1",,"1018,1",6|250001,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3050035,销毁装备,3,5,玩家,"1,1,0,0",销毁|@1||@2|中的装备,销毁【玩家】【装备栏】中的装备,,,,"1018,1",6|250001,"1054,1",5|420001,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3050036,改变玩家外观,3,5,玩家,"1,0,0,0",改变|@1|的外观为|@2,改变【玩家】的外观为【外观类型】,,,,"1018,1",6|250001,"1060,1",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3050037,恢复玩家外观,3,5,玩家,"1,0,0,0",恢复|@1|的初始外观,恢复【玩家】的初始外观,,,,"1018,1",6|250001,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3050040,使玩家对玩家造成伤害,3,5,玩家,"1,1,0,0",使|@1|对|@2|造成|@3|点伤害，伤害类型为|@4,使【玩家】对【玩家】造成【数值】点伤害，伤害类型为【伤害类型】,"1.可以获取到伤害来源为玩家，会显示伤害飘字
2.近战伤害、远程伤害、爆炸伤害会默认加5点伤害值，会根据防御力计算最终的伤害值
3.会触发对应的事件（例如玩家造成伤害，玩家受到伤害），但不会触发玩家攻击、攻击命中等事件",,,"1018,1",6|250001,"1018,1",6|250002,"1005,1",1|10,"1011,1",5|80001,,,,,,,,,,,,,,2,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3050041,使玩家对生物造成伤害,3,5,玩家,"1,1,0,0",使|@1|对|@2|造成|@3|点伤害，伤害类型为|@4,使【玩家】对【生物】造成【数值】点伤害，伤害类型为【伤害类型】,"1.可以获取到伤害来源为玩家，会显示伤害飘字
2.近战伤害、远程伤害、爆炸伤害会默认加5点伤害值，会根据防御力计算最终的伤害值
3.会触发对应的事件（例如玩家造成伤害，玩家受到伤害），但不会触发玩家攻击、攻击命中等事件",,,"1018,1",6|250001,"1031,1",6|300002,"1005,1",1|10,"1011,1",5|80001,,,,,,,,,,,,,,2,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3050042,使玩家伤害区域内的目标,3,5,玩家,"1,1,0,0",使|@1|对|@2|内的所有目标造成|@3|点伤害，伤害类型为|@4,使【玩家】对【区域】内的所有目标造成【数值】点伤害，伤害类型为【伤害类型】,"1.对所有人造成伤害（包括自己），可以获取到伤害来源为玩家，会显示伤害飘字
2.近战伤害、远程伤害、爆炸伤害会默认加5点伤害值，会根据防御力计算最终的伤害值
3. 会触发对应的事件（例如玩家造成伤害、玩家、生物受到伤害等），但不会触发玩家攻击、攻击命中等事件
",,,"1018,1",6|250001,"1001,1",,"1005,1",1|10,"1011,1",5|80001,,,,,,,,,,,,,,2,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3050043,更改玩家名字,3,5,玩家,"1,1,0,0",更改|@1|的名字为|@2,更改【玩家】的名字为【字符串】,,,,"1018,1",6|250001,"1010,1",,,,,,,,,,,,,,,,,,2,2,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3050044,改变玩家移动方式,3,5,玩家,"1,1,0,0",设置|@1|的移动方式为|@2,设置【玩家】的移动方式为【移动方式】,角色落地后会取消飞行状态，需要再次触发该动作,,,"1018,1",6|250001,"1077,1",6|530001,,,,,,,,,,,,,,,,,,2,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3050045,使玩家退出观战模式,3,5,玩家,"1,1,0,0",使|@1|退出观战模式,使【玩家】退出观战模式,,,,"1018,1",6|250001,,,,,,,,,,,,,,,,,,,,2,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3050046,设置玩家免疫伤害类型,3,5,玩家,"1,1,0,0",设置|@1|免疫的伤害类型为|@2,设置【玩家】免疫的伤害类型为【伤害类型】,,,,"1018,1",6|250001,"1011,1",6|80001,,,,,,,,,,,,,,,,,,2,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3050047,移除玩家免疫伤害类型,3,5,玩家,"1,1,0,0",移除|@1|免疫的伤害类型为|@2,移除【玩家】免疫的伤害类型为【伤害类型】,,,,"1018,1",6|250001,"1011,1",6|80001,,,,,,,,,,,,,,,,,,2,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3050048,抖动玩家镜头,3,5,玩家,"1,1,0,0",对|@1|抖动镜头，持续|@2|秒，强度为|@3,对【玩家】抖动镜头，持续【数值】秒，强度为【数值】,,,,"1018,1",6|250001,"1005,1",1|2,"1005,1",1|100,,,,,,,,,,,,,,,,2,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3050049,停止玩家镜头抖动,3,5,玩家,"1,1,0,0",停止|@1|镜头抖动,停止【玩家】镜头抖动,,,,"1018,1",6|250001,,,,,,,,,,,,,,,,,,,,2,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3060001,添加玩家进玩家组,3,6,玩家组,"1,1,0,0",添加|@1|进指定|@2,添加【玩家】进指定【玩家组】,,,,"1018,1",6|250001,"1019,1",,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3060002,从玩家组中移除玩家,3,6,玩家组,"1,1,0,0",将|@1|从|@2|中移除,将【玩家】从【玩家组】中移除,,,,"1018,1",6|250001,"1019,1",,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3060003,清空玩家组,3,6,玩家组,"1,1,0,0",清空|@1,清空【玩家组】,,,,"1019,1",,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3060004,对玩家组执行触发器,3,6,玩家组,"1,1,0,0",对|@1|内的每一个玩家执行|@2|，并检查触发器条件为|@3,对【玩家组】内的每一个玩家执行【触发器】，并检查触发器条件为【布尔值】,,,,"1019,1",,"1038,1",,"1014,1",5|320002,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3060005,对玩家组显示聊天框信息,3,6,玩家组,"1,1,0,0",对|@1|内每一个玩家显示聊天框信息，内容为|@2,对【玩家组】内每一个玩家显示聊天框信息，内容为【字符串】,,,,"1019,1",,"1010,1",,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3060006,对玩家组显示飘窗文字,3,6,玩家组,"1,1,0,0",对|@1|内每一个玩家显示飘窗文字，内容为|@2,对【玩家组】内每一个玩家显示飘窗文字，内容为【字符串】,,,,"1019,1",,"1010,1",,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3070001,创建生物,3,7,生物,"1,1,0,0",在|@1|创建|@2|个|@3|，阵营属于|@4,在【位置】创建【数值】个【生物类型】，阵营属于【队伍】,,,,"1002,1",6|240001,"1005,1",1|1,"1032,1",,"1023,1",5|170001,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3070003,击败生物,3,7,生物,"1,1,0,0",将|@1|击败,将【生物】杀死,,,,"1031,1",6|300001,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3070004,移除生物,3,7,生物,"1,1,0,0",将|@1|移除,将【生物】移除,,,,"1031,1",6|300001,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3070005,击败生物类型,3,7,生物,"1,1,0,0",将|@1|击败,将【生物类型】杀死,,,,"1032,1",,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3070006,移除生物类型,3,7,生物,"1,1,0,0",将|@1|移除,将【生物类型】移除,,,,"1032,1",,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3070007,设置属性,3,7,生物,"1,1,0,0",设置|@1|的|@2|为|@3,设置【生物】的【生物属性】为【数值】,,,,"1031,1",6|300001,"1035,1",5|210001,"1005,1",1|0,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3070008,设置伤害类型,3,7,生物,"1,1,0,0",设置|@1|的伤害类型为|@2,设置【生物】的伤害类型为【伤害类型】,,,,"1031,1",6|300001,"1011,1",5|80001,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3070009,设置免疫伤害类型,3,7,生物,"1,1,0,0",设置|@1|免疫的伤害类型为|@2,设置【生物】免疫的伤害类型为【伤害类型】,,,,"1031,1",6|300001,"1011,1",5|80001,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3070010,改变生物设置开关,3,7,生物,"1,1,0,0",将|@1|的|@2|设置为 |@3,设置【生物】的【生物设置】设置为 【开关】,,,,"1031,1",6|300001,"1036,1",5|220001,"1013,1",5|100001,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3070011,使生物播放动画表情,3,7,生物,"1,1,0,0",使|@1|播放|@2,使【生物】播放【动画表情】,动画表情参数输入值范围1-20 人物动作1-10 通用动作11-20,,,"1031,1",6|300001,"1041,1",,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3070012,获得状态效果,3,7,生物,"1,1,0,0",使|@1|获得|@2|效果,使【生物】获得【状态】效果,,,,"1031,1",6|300001,"1037,1",,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3070013,移除状态效果,3,7,生物,"1,1,0,0",移除|@1|身上的|@2|效果,移除【生物】身上的【状态】效果,,,,"1031,1",6|300001,"1037,1",,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3070014,改变生物面向角度,3,7,生物,"1,1,0,0",设置触发事件中的|@1|面向|@2|度方向（面向Z轴方向为0度）,设置触发事件中的【生物】面向【数值】度方向（面向Z轴方向为0度）,,,,"1031,1",6|300001,"1005,1",,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3070015,改变生物位置,3,7,生物,"1,1,0,0",改变|@1|的当前位置至|@2,改变【生物】的当前位置至【位置】,,,,"1031,1",6|300001,"1002,1",,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3070016,生物自动寻路,3,7,生物,"1,1,0,0",使|@1|从当前位置自动寻路到|@2,使【生物】从当前位置自动寻路到【位置】,该触发器有距离限制，超过一定（约32个方块）范围会导致无法触发,,,"1031,1",6|300001,"1002,1",,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3070017,使生物朝方向移动,3,7,生物,"1,1,0,0",使|@1|朝|@2|移动，速度为|@3,使【生物】朝【方向】移动，速度为【数值】，衰减系数为【数值】,,,,"1031,1",6|300001,"1047,1",,"1005,1",1|0,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3070018,改变生物外观,3,7,生物,"1,0,0,0",改变|@1|的外观为|@2,改变【生物】的外观为【外观类型】,,,,"1031,1",6|300001,"1060,1",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3070019,恢复生物外观,3,7,生物,"1,0,0,0",恢复|@1|的初始外观,恢复【生物】的初始外观,,,,"1031,1",6|300001,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3070022,使生物对玩家造成伤害,3,7,生物,"1,1,0,0",使|@1|对|@2|造成|@3|点伤害，伤害类型为|@4,使【生物】对【玩家】造成【数值】点伤害，伤害类型为【伤害类型】,"1.可以获取到伤害来源为生物，会显示伤害飘字
2.会根据防御力计算最终的伤害值
3. 会触发对应的事件（例如生物造成伤害、生物受到伤害等），但不会触发生物攻击、攻击命中等事件",,,"1031,1",6|300001,"1018,1",6|250002,"1005,1",1|10,"1011,1",5|80001,,,,,,,,,,,,,,2,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3070023,使生物对生物造成伤害,3,7,生物,"1,1,0,0",使|@1|对|@2|造成|@3|点伤害，伤害类型为|@4,使【生物】对【生物】造成【数值】点伤害，伤害类型为【伤害类型】,"1.可以获取到伤害来源为生物，会显示伤害飘字
2.会根据防御力计算最终的伤害值
3. 会触发对应的事件（例如生物造成伤害、生物受到伤害等），但不会触发生物攻击、攻击命中等事件",,,"1031,1",6|300001,"1031,1",6|300002,"1005,1",1|10,"1011,1",5|80001,,,,,,,,,,,,,,2,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3070024,使生物伤害区域内的目标,3,7,生物,"1,1,0,0",使|@1|对|@2|内的目标造成|@3|点伤害，伤害类型为|@4,使【生物】对【区域】内的目标造成【数值】点伤害，伤害类型为【伤害类型】,"1.对所有人造成伤害（包括自己），可以获取到伤害来源为生物，会显示伤害飘字
2.会根据防御力计算最终的伤害值
3. 会触发对应的事件（例如生物造成伤害、生物受到伤害等），但不会触发生物攻击、攻击命中等事件",,,"1031,1",6|300001,"1001,1",,"1005,1",1|10,"1011,1",5|80001,,,,,,,,,,,,,,2,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3070025,替换生物,3,7,生物,"1,1,0,0",替换|@1|为|@2|，使用旧生物的生命值百分比|@3,替换【生物】为【生物类型】，使用旧生物的生命值百分比【真假】,"1.替换后保留生物的图文信息
2.替换后生物队伍不变
3.生物变量也会改变，例如：替换前变量存了A生物，替换成B生物后，变量存的值从A生物变为B生物（只影响生物变量，不影响生物类型变量）",,,"1031,1",6|300001,"1032,1",,"1014,1",5|320001,,,,,,,,,,,,,,,,2,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3070026,改变生物队伍,3,7,生物,"1,1,0,0",改变|@1|队伍为|@2,改变【生物】队伍为【队伍】,,,,"1031,1",6|300001,"1023,1",5|170001,,,,,,,,,,,,,,,,,,2,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3070027,移除生物免疫伤害类型,3,7,生物,"1,1,0,0",移除|@1|免疫的伤害类型为|@2,移除【玩家】免疫的伤害类型为【伤害类型】,,,,"1031,1",6|300001,"1011,1",5|80001,,,,,,,,,,,,,,,,,,2,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3080001,添加生物进生物组,3,8,生物组,"1,1,0,0",添加|@1|进入指定|@2,添加【生物】进入指定【生物组】,,,,"1031,1",6|300001,"1033,1",,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3080002,从生物组中移除生物,3,8,生物组,"1,1,0,0",将|@1|移除出|@2,将【生物】移除出【生物组】,,,,"1031,1",6|300001,"1033,1",,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3080003,清空生物组,3,8,生物组,"1,1,0,0",清空|@1,清空【生物组】,,,,"1033,1",,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3080004,对生物组执行触发器,3,8,生物组,"1,1,0,0",对|@1|内的每一个生物执行|@2|，并检查触发器条件为|@3,对【生物组】内的每一个生物执行【触发器】，并检查触发器条件为【布尔值】,,,,"1033,1",,"1038,1",,"1014,1",5|320002,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3100001,创建道具至储物箱,3,10,道具,"1,1,0,0",创建|@1||@2|个至指定|@3|储存箱,创建【道具类型】【数值】个至指定【位置】储存箱,,,,"1028,1",,"1005,1",,"1002,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3100002,销毁储物箱中的道具,3,10,道具,"1,1,0,0",销毁指定|@1|储存箱中的|@2|个|@3,销毁指定【位置】储存箱中的【数值】个【道具类型】,,,,"1002,1",,"1005,1",,"1028,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3100003,清空储物箱的道具,3,10,道具,"1,1,0,0",清空指定|@1|储存箱中的所有道具,清空指定【位置】储存箱中的所有道具,,,,"1002,1",,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3100004,创建投掷物,3,10,道具,"1,1,0,0",在指定|@1|创建|@2|，向|@3|发射,在指定【位置】创建【投掷物类型】，向【位置】发射,,,,"1002,1",6|240001,"1029,1",,"1002,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3100005,创建属于玩家的投掷物,3,10,道具,"1,1,0,0",在指定|@1|创建属于|@2|的|@3|，向|@4|发射,在指定【位置】创建属于【玩家】的【投掷物类型】，向【位置】发射,,,,"1002,1",6|240001,"1018,1",,"1029,1",,"1002,1",,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3100006,销毁投掷物,3,10,道具,"1,1,0,0",销毁|@1,销毁【投掷物】,,,,"1044,1",6|280001,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3100007,创建掉落物,3,10,道具,"1,1,0,0",在指定|@1|创建|@2|个|@3|的掉落物,在指定【位置】创建【数值】个【道具类型】的掉落物,,,,"1002,1",6|240001,"1005,1",1|1,"1028,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3100008,销毁掉落物,3,10,道具,"1,1,0,0",销毁|@1,销毁【掉落物】,,,,"1030,1",6|290001,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3100009,修改冷却时间,3,10,道具,"1,1,0,0",修改|@1|的|@2|使用的冷却时间为|@3|秒,修改【玩家】的【道具类型】使用的冷却时间为【数值】秒,,,,"1018,1",,"1028,1",,"1005,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3100010,进入冷却时间,3,10,道具,"1,1,0,0",使|@1|的|@2|进入冷却时间,使【玩家】的【道具类型】进入冷却时间,,,,"1018,1",,"1028,1",,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3100011,创建属于生物的投掷物,3,10,道具,"1,1,0,0",在指定|@1|创建属于|@2|的|@3|，向|@4|发射,在指定【位置】创建属于【生物】的【投掷物类型】，向【位置】发射,,,,"1002,1",6|240001,"1031,1",,"1029,1",,"1002,1",,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3100012,使玩家发射投掷物,3,10,道具,"1,1,0,0",使|@1|朝|@2|发射|@3,使【玩家】朝【方向】发射【投掷物类型】,,,,"1018,1",6|250001,"1047,1",,"1029,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3100013,使生物发射投掷物,3,10,道具,"1,1,0,0",使|@1|朝|@2|发射|@3,使【生物】朝【方向】发射【投掷物类型】,,,,"1031,1",6|300001,"1047,1",,"1029,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3100014,改变道具外观,3,10,道具,"0,0,0,0",改变|@1|的外观为|@2,改变【道具类型】的外观为【外观类型】,,,,"1028,1",,"1060,1",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3100015,改变投掷物外观,3,10,道具,"1,0,0,0",改变|@1|的外观为|@2,改变【投掷物】的外观为【外观类型】,,,,"1044,1",6|280001,"1060,1",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3110001,运行倒计时器,3,11,计时器,"1,1,0,0",启动|@1|，倒数|@2|秒，重复计时为|@3,启动【计时器】，倒数【数值】秒，重复计时为【布尔值】,,,,"1040,1",,"1005,1",1|5,"1014,1",5|320002,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3110002,运行正向计时器,3,11,计时器,"1,1,0,0",启动|@1,启动【计时器】,,,,"1040,1",,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3110003,暂停计时器,3,11,计时器,"1,1,0,0",暂停|@1,暂停【计时器】,,,,"1040,1",,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3110004,恢复计时器,3,11,计时器,"1,1,0,0",恢复|@1,恢复【计时器】,,,,"1040,1",,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3110005,停止计时器,3,11,计时器,"1,1,0,0",停止|@1,停止【计时器】,,,,"1040,1",,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3110006,等待时间,3,11,计时器,"1,1,0,0",等待|@1|秒后，继续执行下一条动作,等待【数值】秒后，继续执行下一条动作,,,,"1005,1",1|5,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3110007,改变计时器计时,3,11,计时器,"1,1,0,0",改变|@1|为|@2|秒,改变【计时器】为【数值】秒,,,,"1040,1",,"1005,1",1|5,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3110009,显示计时器窗口,3,11,计时器,"1,1,0,0",对|@1|显示|@2|窗口，标题为|@3,对【玩家组】显示【计时器】窗口，标题为【字符串】,,,,"1019,1",,"1040,1",,"1010,1",,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3110010,隐藏计时器窗口,3,11,计时器,"1,1,0,0",对|@1|隐藏|@2|窗口,对【玩家组】隐藏【计时器】窗口,,,,"1019,1",,"1040,1",,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3120001,设置位置,3,12,赋值,"1,1,0,0",设置|@1|为|@2,设置【位置】为【位置】,,,,"1002,0",,"1002,1",6|240001,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3120002,设置区域,3,12,赋值,"1,1,0,0",设置|@1|为|@2,设置【区域】为【区域】,,,,"1001,0",,"1001,1",,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3120003,设置数值,3,12,赋值,"1,1,0,0",设置|@1|为|@2,设置【数值】为【数值】,,,,"1005,0",,"1005,1",1|0,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3120004,设置布尔值,3,12,赋值,"1,1,0,0",设置|@1|为|@2,设置【布尔值】为【布尔值】,,,,"1014,0",,"1014,1",5|320002,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3120005,设置玩家,3,12,赋值,"1,1,0,0",设置|@1|为|@2,设置【玩家】为【玩家】,,,,"1018,0",,"1018,1",6|250001,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3120006,设置方块类型,3,12,赋值,"1,1,0,0",设置|@1|为|@2,设置【方块类型】为【方块类型】,,,,"1025,0",,"1025,1",,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3120007,设置道具类型,3,12,赋值,"1,1,0,0",设置|@1|为|@2,设置【道具类型】为【道具类型】,,,,"1028,0",,"1028,1",,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3120008,设置生物,3,12,赋值,"1,1,0,0",设置|@1|为|@2,设置【生物】为【生物】,,,,"1031,0",,"1031,1",6|300001,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3120009,设置生物类型,3,12,赋值,"1,1,0,0",设置|@1|为|@2,设置【生物类型】为【生物类型】,,,,"1032,0",,"1032,1",,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3120010,设置字符串,3,12,赋值,"1,1,0,0",设置|@1|为|@2,设置【字符串】为【字符串】,,,,"1010,0",,"1010,1",,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3120011,设置特效类型,3,12,赋值,"1,1,0,0",设置|@1|为|@2,设置【特效类型】为【特效类型】,,,,"1045,0",,"1045,1",6|340001,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3130001,在位置上播放特效,3,13,特效,"1,1,0,0",在|@1|上播放|@2|，大小为|@3,在【位置】上播放【特效类型】，大小为【数值】,,,,"1002,1",6|240001,"1045,1",,"1005,1",1|1,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3130002,对玩家播放特效,3,13,特效,"1,1,0,0",在|@1|上播放|@2|，大小为|@3,对【玩家】播放【特效类型】，大小为【数值】,,,,"1018,1",6|250001,"1045,1",,"1005,1",1|1,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3130003,对生物播放特效,3,13,特效,"1,1,0,0",在|@1|上播放|@2|，大小为|@3,对【生物】播放【特效类型】，大小为【数值】,,,,"1031,1",6|300001,"1045,1",,"1005,1",1|1,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3130004,对投掷物播放特效,3,13,特效,"1,1,0,0",在|@1|上播放|@2|，大小为|@3,在【投掷物】上播放【特效类型】，大小为【数值】,,,,"1044,1",6|280001,"1045,1",,"1005,1",1|1,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3130005,删除位置上的特效,3,13,特效,"1,1,0,0",删除|@1|上的|@2,删除【位置】上的【特效类型】,,,,"1002,1",6|240001,"1045,1",,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3130006,删除玩家身上的特效,3,13,特效,"1,1,0,0",删除|@1|身上的|@2,删除【玩家】身上的【特效类型】,,,,"1018,1",6|250001,"1045,1",,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3130007,删除生物身上的特效,3,13,特效,"1,1,0,0",删除|@1|身上的|@2,删除【生物】身上的【特效类型】,,,,"1031,1",6|300001,"1045,1",,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3130008,删除投掷物上的特效,3,13,特效,"1,1,0,0",删除|@1|上的|@2,删除【投掷物】上的【特效类型】,,,,"1044,1",6|280001,"1045,1",,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3130009,改变位置特效大小,3,13,特效,"1,1,0,0",改变|@1|上的|@2|大小为|@3,改变【位置】上的【特效类型】大小为【数值】,,,,"1002,1",6|240001,"1045,1",,"1005,1",1|1,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3130010,改变玩家特效大小,3,13,特效,"1,1,0,0",改变|@1|上的|@2|为大小|@3,改变【玩家】上的【特效类型】大小为【数值】,,,,"1018,1",6|250001,"1045,1",,"1005,1",1|1,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3130011,改变生物特效大小,3,13,特效,"1,1,0,0",改变|@1|上的|@2|大小为|@3,改变【生物】上的【特效类型】大小为【数值】,,,,"1031,1",6|300001,"1045,1",,"1005,1",1|1,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3130012,改变投掷物特效大小,3,13,特效,"1,1,0,0",改变|@1|上的|@2|大小为|@3,改变【投掷物】上的【特效类型】大小为【数值】,,,,"1044,1",6|280001,"1045,1",,"1005,1",1|1,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3140012,使玩家播放音效,3,14,音效,"1,1,0,0",使|@1|播放场景|@2|，音量为|@3|，音调为|@4|，循环播放为|@5,使【玩家】播放场景【音效】，音量为【数值】，音调为【数值】，循环播放为【布尔值】,,,,"1018,1",6|250001,"1046,1",,"1005,1",1|100,"1005,1",1|1,"1014,1",5|320002,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3140013,使生物播放音效,3,14,音效,"1,1,0,0",使|@1|播放场景|@2|，音量为|@3|，音调为|@4|，循环播放为|@5,使【生物】播放场景【音效】，音量为【数值】，音调为【数值】，循环播放为【布尔值】,,,,"1031,1",6|300001,"1046,1",,"1005,1",1|100,"1005,1",1|1,"1014,1",5|320002,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3140014,在位置上播放音效,3,14,音效,"1,1,0,0",在|@1|播放场景|@2|，音量为|@3|，音调为|@4|，循环播放为|@5,在【位置】播放场景【音效】，音量为【数值】，音调为【数值】，循环播放为【布尔值】,,,,"1002,1",6|240001,"1046,1",,"1005,1",1|100,"1005,1",1|1,"1014,1",5|320002,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3140015,关闭玩家音效,3,14,音效,"1,1,0,0",关闭|@1|身上正在播放的|@2,关闭【玩家】身上正在播放的【音效】,,,,"1018,1",6|250001,"1046,1",,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3140016,关闭生物音效,3,14,音效,"1,1,0,0",关闭|@1|身上正在播放的|@2,关闭【生物】身上正在播放的【音效】,,,,"1031,1",6|300001,"1046,1",,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3140017,关闭位置音效,3,14,音效,"1,1,0,0",关闭|@1|上正在播放的|@2,关闭【位置】上正在播放的【音效】,,,,"1002,1",6|240001,"1046,1",,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3140018,对玩家组播放界面音效,3,14,音效,"1,1,0,0",对|@1|播放界面|@2|，音量为|@3|，音调为|@4|，循环播放为|@5,对【玩家组】播放界面【音效】，音量为【数值】，音调为【数值】，循环播放为【布尔值】,,,,"1019,1",,"1046,1",,"1005,1",1|100,"1005,1",1|1,"1014,1",5|320002,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3140019,对玩家播放界面音效,3,14,音效,"1,1,0,0",对|@1|播放界面|@2|，音量为|@3|，音调为|@4|，循环播放为|@5,对【玩家】播放界面【音效】，音量为【数值】，音调为【数值】，循环播放为【布尔值】,,,,"1018,1",6|250001,"1046,1",,"1005,1",1|100,"1005,1",1|1,"1014,1",5|320002,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3140020,关闭玩家组界面音效,3,14,音效,"1,1,0,0",关闭|@1|正在播放的界面|@2,关闭【玩家组】正在播放的界面【音效】,,,,"1019,1",,"1046,1",,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3140021,关闭玩家界面音效,3,14,音效,"1,1,0,0",关闭|@1|正在播放的界面|@2,关闭【玩家】正在播放的界面【音效】,,,,"1018,1",6|250001,"1046,1",,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3140022,对玩家播放界面音乐,3,14,音效,"1,1,0,0",对|@1|播放界面|@2|，音量为|@3|，循环播放为|@4,对【玩家】播放界面【音乐】，音量为【数值】，循环播放为【布尔值】,该触发器播放的声音受到玩家设置的“音效”开关控制，关闭“音效”后声音无法听到,,,"1018,1",6|250001,"1080,1",,"1005,1",1|100,"1014,1",5|320002,,,,,,,,,,,,,,3,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3140023,暂停/恢复玩家界面音乐,3,14,音效,"1,1,0,0",@1||@2|正在播放的界面音乐,【暂停恢复】【玩家】正在播放的界面音乐,,,,"1081,1",5|580001,"1018,1",6|250001,,,,,,,,,,,,,,,,,,3,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3150001,执行脚本,3,15,脚本,"1,0,0,0",执行脚本|@1,执行脚本【脚本】,,,,"1050,1",,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3160001,播放广告(老),3,16,开发者,"1,0,0,1",对|@1|播放广告，设置广告位名字为|@2|，并且运行|@3,对【玩家】播放广告，设置广告位名字为【字符串】，并且运行【触发器】,,,,"1018,1",6|250001,"1010,1",,"1038,1",,,,,,,,,,,,,,,,2,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3160002,打开开发者商店（老）,3,16,开发者,"1,0,0,1",使|@1|打开开发者商店(仅按下按钮和松开按钮可触发),使【玩家】打开开发者商店(仅按下按钮和松开按钮可触发),,,,"1018,1",6|250001,,,,,,,,,,,,,,,,,,,1,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3160003,显示商品购买窗口,3,16,开发者,"1,0,0,1",对|@1|显示|@2|的购买窗口，推荐语显示|@3,对【玩家】显示【商品】的购买窗口，推荐语显示【字符串】(仅按下按钮和松开按钮可触发，其他事件直接发放道具),,,,"1018,1",6|250001,"1059,1",,"1010,1",,,,,,,,,,,,,,,,1,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3160004,播放视频,3,16,开发者,"1,0,0,1",对|@1|打开|@2|并自动播放视频,对【玩家】打开【视频链接】并自动播放视频,,,,"1018,1",6|250001,"1073,1",,,,,,,,,,,,,,,,,,2,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3160005,播放广告,3,16,开发者,"1,0,0,1",对|@1|播放广告，设置广告位名字为|@2|，并且运行|@3|(仅按下按钮和松开按钮可触发),对【玩家】播放广告，设置广告位名字为【字符串】，并且运行【触发器】(仅按下按钮和松开按钮可触发),,,,"1018,1",6|250001,"1010,1",,"1038,1",,,,,,,,,,,,,,,,1,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3160006,打开开发者商店,3,16,开发者,"1,0,0,1",对|@1|打开开发者商店,对【玩家】打开开发者商店,,,,"1018,1",6|250001,,,,,,,,,,,,,,,,,,,1,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3160007,打开开发者商店指定页面,3,16,开发者,"1,0,0,1",对|@1|打开开发者商店|@2|，商店名为|@3,对【玩家】打开开发者商店【指定页面】，商店名为【字符串】,,,,"1018,1",6|250001,"1101,1",,"1010,1",,,,,,,,,,,,,,,,1,23,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3160008,打开商品详情页,3,16,开发者,"1,0,0,1",对|@1|打开|@2|详情页（仅按下按钮和松开按钮可触发）,对【玩家】打开【商品】详情页（仅按下按钮和松开按钮可触发）,,,,"1018,1",6|250001,"1059,1",,,,,,,,,,,,,,,,,,1,27,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3170001,在位置上创建图文信息,3,17,图文信息,"1,1,0,0",在|@1|上创建|@2,在【位置】上创建【图文信息】,,,,"1002,1",6|240001,"1057,1",3|150001,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3170002,在玩家身上创建图文信息,3,17,图文信息,"1,1,0,0",在|@1|身上创建|@2|，向|@3|偏移距离|@4,在【玩家】身上创建【图文信息】，向【方向】偏移距离【数值】,,,,"1018,1",6|250001,"1057,1",3|150001,"1047,1",6|350001,"1005,1",1|100,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3170003,在生物身上创建图文信息,3,17,图文信息,"1,1,0,0",在|@1|身上创建|@2|，向|@3|偏移距离|@4,在【生物】身上创建【图文信息】，向【方向】偏移距离【数值】,,,,"1031,1",6|300001,"1057,1",3|150001,"1047,1",6|350001,"1005,1",1|100,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3170004,在投掷物上创建图文信息,3,17,图文信息,"1,1,0,0",在|@1|上创建|@2|，向|@3|偏移|@4,在【投掷物】上创建【图文信息】，向【方向】偏移【数值】,,,,"1044,1",6|280001,"1057,1",3|150001,"1047,1",6|350001,"1005,1",1|100,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3170005,删除位置上的图文信息类型,3,17,图文信息,"1,1,0,0",删除|@1|上编号为|@2|的|@3,删除【位置】上编号为【数值】的【图文信息类型】,,,,"1002,1",6|240001,"1005,1",1|1,"1058,1",6|490001,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3170006,删除玩家身上的图文信息类型,3,17,图文信息,"1,1,0,0",删除|@1|身上编号为|@2|的|@3,删除【玩家】身上编号为【数值】的【图文信息类型】,,,,"1018,1",6|250001,"1005,1",1|1,"1058,1",6|490001,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3170007,删除生物身上的图文信息类型,3,17,图文信息,"1,1,0,0",删除|@1|身上编号为|@2|的|@3,删除【生物】身上编号为【数值】的【图文信息类型】,,,,"1031,1",6|300001,"1005,1",1|1,"1058,1",6|490001,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3170008,删除投掷物上的图文信息类型,3,17,图文信息,"1,1,0,0",删除|@1|上编号为|@2|的|@3,删除【投掷物】上编号为【数值】的【图文信息类型】,,,,"1044,1",6|280001,"1005,1",1|1,"1058,1",6|490001,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3170009,在位置上创建平面图文信息,3,17,图文信息,"1,1,0,0",在|@1|上创建|@2|，向X轴偏移|@3|，Y轴偏移|@4,在【位置】上创建【图文信息】，向X轴偏移【数值】，Y轴偏移【数值】,,,,"1002,1",6|240001,"1057,1",3|150001,"1005,1",1|0,"1005,1",1|100,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3170010,在玩家身上创建平面图文信息,3,17,图文信息,"1,1,0,0",在|@1|身上创建|@2|，向X轴偏移|@3|，Y轴偏移|@4,在【玩家】身上创建【图文信息】，向X轴偏移【数值】，Y轴偏移【数值】,,,,"1018,1",6|250001,"1057,1",3|150001,"1005,1",1|0,"1005,1",1|100,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3170011,在生物身上创建平面图文信息,3,17,图文信息,"1,1,0,0",在|@1|身上创建|@2|，向X轴偏移|@3|，Y轴偏移|@4,在【生物】身上创建【图文信息】，向X轴偏移【数值】，Y轴偏移【数值】,,,,"1031,1",6|300001,"1057,1",3|150001,"1005,1",1|0,"1005,1",1|100,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3170012,在投掷物上创建平面图文信息,3,17,图文信息,"1,1,0,0",在|@1|身上创建|@2|，向X轴偏移|@3|，Y轴偏移|@4,在【投掷物】身上创建【图文信息】，向X轴偏移【数值】，Y轴偏移【数值】,,,,"1044,1",6|280001,"1057,1",3|150001,"1005,1",1|0,"1005,1",1|100,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3180001,在任意组中添加任意值,3,18,组,"1,1,0,0",在|@1|中添加|@2,在【任意组】中添加【任意值】,"1.添加位置：在末尾插入值
2. 使用该动作对位置组、区域组、生物组、玩家组、计时器组等变量类型添加值时，无法重复添加相同值
3. 数值组、字符串组、布尔值组、方块类型组、道具类型组、生物类型组、特效类型组等变量类型可以重复添加相同值",,,"1061,1",,"1053,1",,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3180002,在任意组中将指定编号的值设为空值,3,18,组,"1,1,0,0",在|@1|中将编号|@2|的值设为空值,在【任意组】中将编号【数值】的值设为空值,设为空值后还会占用位置，例如a = {1，2，3}，将1设为空值，则a = {nil，2，3},,,"1061,1",,"1005,1",1|1,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3180003,在任意组中将任意值设为空值,3,18,组,"1,1,0,0",在|@1|中将|@2|设为空值,在【任意组】中将【任意值】设为空值,"内容匹配清除，会将所有相同的值设为空值，例如a = {1,1,2,3} 将1设为空值，则a = {nil，nil，2,3}",,,"1061,1",,"1053,1",,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3180004,清空任意组,3,18,组,"1,1,0,0",清空|@1,清空【任意组】,删除组所有的值,,,"1061,1",,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3180005,在任意组插入任意值,3,18,组,"1,1,0,0",在|@1|的第|@2|位插入|@3,在【任意组】的第【数值】位插入【任意值】,向前插入，会影响排序。例如a = {1、2、3、4} 往第二位插入5，则a = {1、5、2、3、4},,,"1061,1",,"1005,1",1|1,"1053,1",,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3180006,在任意组删除指定编号的值,3,18,组,"1,1,0,0",删除|@1|中编号|@2|的值,删除【任意组】中编号【数值】的值,删除值会影响排序，后面的值往前进位，例如a = {1、2、3、4}，删除2后，a ={1、3、4},,,"1061,1",,"1005,1",1|1,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3180007,在任意组删除任意值,3,18,组,"1,1,0,0",在|@1|删除|@2,在【任意组】删除【任意值】,"内容匹配清除，例如a = {1,1,2,3} 删除1则删除所有1，则a = {2,3}",,,"1061,1",,"1053,1",,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3180008,清空任意组的空值,3,18,组,"1,1,0,0",清空|@1|的空值,清空【任意组】的空值,清空空值后自动进位，例如a = {1、nil、2、nil、nil、3}，清空后变为 a ={1、2、3},,,"1061,1",,,,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3180009,在任意组中添加一组值,3,18,组,"1,1,0,0",在|@1|中添加|@2|所有的值,在【任意组】中添加【任意组】所有的值,在a组的末尾按b组原顺序添加b组的所有值，包括空值。例如a = {1，2，3}， b = {4，5，6} 添加b到a，则 a = {1，2，3，4，5，6},,,"1061,1",,"1061,1",,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3180010,在任意组中将一组值设为空值,3,18,组,"1,1,0,0",在|@1|中，将与|@2|重复的值设为空值,在【任意组】中，将与【任意组】重复的值设为空值,"1.去重，将A组和B组重复的值设为空值，重复的空值不删除
2. 若有多个重复值，则优先将排在前面的值设为空值，例如a = {1，1，1，2，3} b = {1，1，2}，变为a = {nil，nil，1，nil，3}，优先ix = 1和ix = 2的1",,,"1061,1",,"1061,1",,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3180011,在任意组中删除一组值,3,18,组,"1,1,0,0",在|@1|中，删除与|@2|重复的值,在【任意组】中，删除与【任意组】重复的值,"1. 去重，删除A组和B组重复的值，重复的空值会被删除
2. 若有多个重复值，则优先删除排在前面的值，例如a = {1，1，1，2，3} b = {1，1，2}，删除后变为a = {1，3}，优先删除ix = 1和ix = 2的1",,,"1061,1",,"1061,1",,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3180012,在任意组插入一组值,3,18,组,"1,1,0,0",在|@1|的第|@2|位插入|@3|所有的值,在【任意组】的第【数值】位插入【任意组】所有的值,"1. 向前插入，影响排序。例如a = {1，2，3，4} 往第二位插入b = {5，6，7}，则a = {1，5，6，7，2，3，4}
2. 若插入的位置没有值，和赋值相同。例如a = {1，2，3}，往第四位插入b = {4，5，6} ，则a = {1，2，3，4，5，6}",,,"1061,1",,"1005,1",1|1,"1061,1",,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3180013,设置任意组中指定编号的值,3,18,组,"1,1,0,0",设置|@1|中编号|@2|的值为|@3,设置【任意组】中编号【数值】的值为【任意值】,赋值操作，和现有的赋值逻辑相同,,,"1061,1",,"1005,1",1|1,"1053,1",,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3180014,替换任意组中的任意值,3,18,组,"1,1,0,0",替换|@1|中的所有|@2|为|@3,替换【任意组】中的所有【任意值】为【任意值】,替换所有匹配内容，例如a = {1，1，1，2，3}，替换1为5，则a = {5，5，5，2，3},,,"1061,1",,"1053,1",,"1053,1",,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3180015,对任意组的值排序,3,18,组,"1,1,0,0",对|@1|的值|@2|排列,对【任意组】的值【排序】排列,"1.根据数字大小排序
2. 字符串组按照字符串长度排序，相同长度根据原顺序",,,"1061,1",,"1072,1",5|500001,,,,,,,,,,,,,,,,,,0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3190001,隐藏元件,3,19,界面,"1,0,0,0,520002",使|@1|修改|@2|的|@3|展示属性为隐藏,使【玩家】修改【界面】的【元件】展示属性为隐藏,,,,"1018,1",6|250001,"1074,1",6|520001,"1075,1",,,,,,,,,,,,,,,,,1,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3190002,设置文本内容,3,19,界面,"1,0,0,0,520002",使|@1|修改|@2|的|@3|内的文本为|@4,使【玩家】修改【界面】的【元件】内的文本为【字符串】,,,,"1018,1",6|250001,"1074,1",6|520001,"1075,1,3|7",,"1010,1",,,,,,,,,,,,,,,1,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3190003,设置图案\按钮内容,3,19,界面,"1,0,0,0,520002",使|@1|修改|@2|的|@3|内的图案为|@4,使【玩家】修改【界面】的【元件】内的图案为【图案模板】,,,,"1018,1",6|250001,"1074,1",6|520001,"1075,1,2|4",,"1076,1",,,,,,,,,,,,,,,1,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3190004,设置图案\按钮的大小,3,19,界面,"1,0,0,0,520002",使|@1|修改|@2|的|@3|长度为|@4|，宽度为|@5|。,使【玩家】修改【界面】的【元件】长度为【数值】，宽度为【数值】。,,,,"1018,1",6|250001,"1074,1",6|520001,"1075,1,2|4",,"1005,1",1|100,"1005,1",1|100,,,,,,,,,,,,1,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3190005,修改文本字体大小,3,19,界面,"1,0,0,0,520002",使|@1|修改|@2|的|@3|字体为|@4,使【玩家】修改【界面】的【元件】字体为【数值】,,,,"1018,1",6|250001,"1074,1",6|520001,"1075,1,3|7",,"1005,1",1|16,,,,,,,,,,,,,,1,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3190006,改变元件颜色,3,19,界面,"1,0,0,0,520002",使|@1|修改|@2|的|@3|颜色为|@4,使【玩家】修改【界面】的【元件】颜色为【颜色】,,,,"1018,1",6|250001,"1074,1",6|520001,"1075,1",,"1056,1",,,,,,,,,,,,,,,1,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3190007,显示元件,3,19,界面,"1,0,0,0,520002",使|@1|修改|@2|的|@3|展示属性为显示,使【玩家】修改【界面】的【元件】展示属性为显示,,,,"1018,1",6|250001,"1074,1",6|520001,"1075,1",,,,,,,,,,,,,,,,,1,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3190008,旋转元件,3,19,界面,"1,0,0,0,520002",使|@1|修改|@2|的|@3|按照顺时针旋转|@4,使【玩家】修改【界面】的【元件】按照顺时针旋转至【数值】,,,,"1018,1",6|250001,"1074,1",6|520001,"1075,1",,"1005,1",1|90,,,,,,,,,,,,,,1,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3190009,打开界面,3,19,界面,"1,0,0,0,520002",使|@1|打开|@2,使【玩家】打开【界面】,,,,"1018,1",6|250001,"1074,1",6|520001,,,,,,,,,,,,,,,,,,1,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3190010,关闭界面,3,19,界面,"1,0,0,0,520002",使|@1|关闭|@2,使【玩家】关闭【界面】,,,,"1018,1",6|250001,"1074,1",6|520001,,,,,,,,,,,,,,,,,,1,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3190011,设置元件透明度,3,19,界面,"1,0,0,0,520002",使|@1|修改|@2|的|@3|透明度为|@4,使【玩家】修改【界面】的【元件】透明度为【数值】,,,,"1018,1",6|250001,"1074,1",6|520001,"1075,1",,"1005,1",1|90,,,,,,,,,,,,,,2,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3190012,切换页面状态,3,19,界面,"1,0,0,0,520003",使|@1|的|@2|状态为|@3,使【玩家】的【界面】状态为【页面状态】,,,,"1018,1",6|250001,"1074,1",6|520001,"1079,1",,,,,,,,,,,,,,,,,2,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3190013,设置元件位置,3,19,界面,"1,0,0,0,520002",使|@1|修改|@2|的|@3|X轴坐标为|@4|，Y轴坐标为|@5|,使【玩家】修改【界面】的【元件】X轴坐标为【数值】，Y轴坐标为【数值】。,,,,"1018,1",6|250001,"1074,1",6|520001,"1075,1",,"1005,1",1|100,"1005,1",1|100,,,,,,,,,,,,2,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3200001,对玩家设置显示板的图片,3,20,显示板,"1,0,0,0",对|@1|设置|@2|显示图片为|@3,对【玩家】设置【显示板】显示图片为【图案模板】,"1.同个显示板可以对不同玩家显示不同的内容
2.需要对所有玩家都执行一次该触发器，才能让所有玩家看到相同的内容",,,"1018,1",6|250001,"1082,1",,"1076,1",,,,,,,,,,,,,,,,,5,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3200002,对玩家设置显示板的视频,3,20,显示板,"1,0,0,0",对|@1|设置|@2|显示视频链接为|@3,对【玩家】设置【显示板】显示视频链接为【字符串】,"1.同个显示板可以对不同玩家显示不同的内容
2.需要对所有玩家都执行一次该触发器，才能让所有玩家看到相同的内容",,,"1018,1",6|250001,"1082,1",,"1010,1",,,,,,,,,,,,,,,,,5,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3200003,对玩家设置显示板的界面,3,20,显示板,"1,0,0,0",对|@1|设置|@2|显示界面为|@3,对【玩家】设置【显示板】显示界面为【界面】,"1.同个显示板可以对不同玩家显示不同的内容
2.需要对所有玩家都执行一次该触发器，才能让所有玩家看到相同的内容",,,"1018,1",6|250001,"1082,1",,"1074,1",,,,,,,,,,,,,,,,,5,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3200004,对玩家显示显示板,3,20,显示板,"1,0,0,0",对|@1|设置|@2|为显示,对【玩家】设置【显示板】为显示,显示板可以对各个玩家存在不同的状态，可以对A玩家显示，B玩家隐藏,,,"1018,1",6|250001,"1082,1",,,,,,,,,,,,,,,,,,,5,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3200005,对玩家隐藏显示板,3,20,显示板,"1,0,0,0",对|@1|设置|@2|为隐藏,对【玩家】设置【显示板】为隐藏,显示板可以对各个玩家存在不同的状态，可以对A玩家显示，B玩家隐藏,,,"1018,1",6|250001,"1082,1",,,,,,,,,,,,,,,,,,,5,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3200006,对玩家设置显示板视频播放模式,3,20,显示板,"1,0,0,0",对|@1|设置|@2| 的视频播放模式为|@3,对【玩家】设置【显示板】 的视频播放模式为【播放模式】,,,,"1018,1",6|250001,"1082,1",,"1086,1",5|610001,,,,,,,,,,,,,,,,10,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3200007,对玩家设置显示板的网络视频,3,20,显示板,"1,0,0,0",对|@1|设置|@2|显示网络视频链接为|@3,对【玩家】设置【显示板】显示网络视频链接为【字符串】,,,,"1018,1",6|250001,"1082,1",,"1010,1",,,,,,,,,,,,,,,,,10,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3210001,在排行榜中设置玩家的数值,3,21,数据存储,"1,0,0,0",在|@1|中设置|@2|的值为|@3,在【排行榜】中设置【玩家】的值为【数值】,"1. 以玩家的迷你号为索引，在服务器排行榜设置玩家的数值
2. 将玩家数据存进服务器的排行榜，可以做出全图排行榜
#3. 相同分数谁先进入排行榜谁在前面
4. 排行榜需要地图上传后，在云服地图才可生效",,,"1084,1",,"1018,1",6|250001,"1005,1",1|1,,,,,,,,,,,,,,,,9,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3210002,在排行榜中设置指定名称的数值,3,21,数据存储,"1,0,0,0",在|@1|中设置名称为|@2|的值为|@3,在【排行榜】中设置名称为【字符串】的值为【数值】,"1.自定义索引名，只允许使用字母、数字、下划线_和星号*
2.排行榜可以使用输入框填写脚本引用名来索引",,,"1084,1",,"1010,1",,"1005,1",1|1,,,,,,,,,,,,,,,,9,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3210003,在排行榜中删除玩家的数值,3,21,数据存储,"1,0,0,0",在|@1|中删除|@2|的数值,在【排行榜】中删除【玩家】的数值,,,,"1084,1",,"1018,1",6|250001,,,,,,,,,,,,,,,,,,9,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3210004,在排行榜中删除指定名称的数值,3,21,数据存储,"1,0,0,0",在|@1|中删除名称为|@2|的数值,在【排行榜】中删除名称为【字符串】的数值,,,,"1084,1",,"1010,1",,,,,,,,,,,,,,,,,,,9,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3210005,使排行榜中指定玩家的值自运算,3,21,数据存储,"1,0,0,0",使|@1|中|@2|的值加|@3,使【排行榜】中【玩家】的值加【数值】,多个房间对同一个值操作时可以使用该动作，不会导致值相互覆盖,,,"1084,1",,"1018,1",6|250001,"1005,1",1|1,,,,,,,,,,,,,,,,9,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3210006,使排行榜中指定名称的值自运算,3,21,数据存储,"1,0,0,0",使|@1|中名称为|@2|的值加|@3,使【排行榜】中名称为【字符串】的值加【数值】,多个房间对同一个值操作时可以使用该动作，不会导致值相互覆盖,,,"1084,1",,"1010,1",,"1005,1",1|1,,,,,,,,,,,,,,,,9,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3210007,获取排行榜中指定排名的值,3,21,数据存储,"1,0,0,0",获取|@1|中以|@4|方式，排在第|@2|的值，并执行 |@3|将获取的值传递到触发器,获取【排行榜】中以【排序】方式，排在第【数值】的值，并执行 【触发器】将获取的值传递到触发器,"1.排行榜只允许取正整数，升序为从小到大，降序为从大到小
2. 本触发器的所有参数传递到执行的【触发器】，例如【触发事件玩家】、【事件中的目标玩家】、【触发事件的生物】等等
3. 可以通过服务器/排行榜获取的xx，拿到名称、玩家、数值、排名四个值",,,"1084,1",,"1005,1",1|1,"1038,1",,"1072,1",6|500001,,,,,,,,,,,,,,9,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3210008,获取排行榜中指定玩家的值,3,21,数据存储,"1,0,0,0",获取|@1|中|@2|的值，并执行 |@3|将获取的值传递到触发器,获取【排行榜】中【玩家】的值，并执行 【触发器】将获取的值传递到触发器,"1. 本触发器的所有参数传递到执行的【触发器】，例如【触发事件玩家】、【事件中的目标玩家】、【触发事件的生物】等等
2. 可以通过服务器/排行榜获取的xx，拿到名称、玩家、数值、排名四个值",,,"1084,1",,"1018,1",6|250001,"1038,1",,,,,,,,,,,,,,,,,9,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3210009,获取排行榜中指定名称的值,3,21,数据存储,"1,0,0,0",获取|@1|中名称为|@2|的值，并执行 |@3|将获取的值传递到触发,获取【排行榜】中名称为【字符串】的值，并执行 【触发器】将获取的值传递到触发,"1. 本触发器的所有参数传递到执行的【触发器】，例如【触发事件玩家】、【事件中的目标玩家】、【触发事件的生物】等等
2. 可以通过服务器/排行榜获取的xx，拿到名称、玩家、数值、排名四个值",,,"1084,1",,"1010,1",,"1038,1",,,,,,,,,,,,,,,,,9,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3210010,在表中设置指定名称的任意值,3,21,数据存储,"1,0,0,0",设置|@1|中名称为|@2|值为|@3,设置【表】中名称为【字符串】值为【任意值】,1. 使用英文的点.可以分割索引，对脚本使用有影响，只使用触发器可无视这条,,,"1085,1",,"1010,1",,"1053,1",,,,,,,,,,,,,,,,,9,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3210011,在表中删除指定名称的值,3,21,数据存储,"1,0,0,0",在|@1|中删除名称为|@2|的值,在【表】中删除名称为【字符串】的值,,,,"1085,1",,"1010,1",,,,,,,,,,,,,,,,,,,9,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3210012,获取表中指定名称的值,3,21,数据存储,"1,0,0,0",获取|@1|中名称为|@2|的值，并执行 |@3|将获取的值传递到触发器,获取【表】中名称为【字符串】的值，并执行 【触发器】将获取的值传递到触发器,,,,"1085,1",,"1010,1",,"1038,1",,,,,,,,,,,,,,,,,9,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3210013,清空表,3,21,数据存储,"1,0,0,0",清空|@1,清空【表】,删除后无法恢复，谨慎使用,,,"1085,1",,,,,,,,,,,,,,,,,,,,,9,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3210014,清空排行榜,3,21,数据存储,"1,0,0,0",清空|@1,清空【排行榜】,删除后无法恢复，谨慎使用,,,"1084,1",,,,,,,,,,,,,,,,,,,,,9,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3210015,上报玩家数据,3,21,数据存储,"1,1,0,0",上报|@1|数据，事件名为|@2,上报【玩家】数据，事件名为【字符串】,使用不同事件组合事件名，可以自定义上报玩家各种行为数据，上报后可以在创作者中心后台查看玩家的各项数据,,,"1018,1",6|250001,"1010,1",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3220001,对玩家显示评价弹窗,3,22,互动,"1,1,0,0",对|@1|显示评价弹窗,对【玩家】显示评价弹窗,"①玩家游玩一段时间后（当前为进入地图后3分钟）才可以触发
②10分钟只会弹一次，且玩家已评价过该地图不会触发
③重进地图或转换编辑地图可重新弹出一次，方便测试",,,"1018,1",6|250001,,,,,,,,,,,,,,,,,,,,12,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
3220002,对玩家显示收藏弹窗,3,22,互动,"1,1,0,0",对|@1|显示收藏弹窗,对【玩家】显示收藏弹窗,"①玩家游玩一段时间后（当前为进入地图后3分钟）才可以触发
②10分钟只会弹一次，且玩家已评价过该地图不会触发
③重进地图或转换编辑地图可重新弹出一次，方便测试",,,"1018,1",6|250001,,,,,,,,,,,,,,,,,,,,12,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
