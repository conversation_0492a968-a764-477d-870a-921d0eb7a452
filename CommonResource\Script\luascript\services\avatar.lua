-- *avatar.lua* 
-- avatar本地服务
if not class then return end 
local AvatarSkinDef = loadwwwcache('res.AvatarSkinDef')
return class.define('Avatar', {
    --{{{ attr

    --{{{

    -- cache for skin_info_list
    skin_info_cache = nil,
    --}}}

    --cache other_avatar_info
    other_cache = {},
    --{{{
    cache = {
        skin = {
            --[[
            [partid] = {
                hash = {
                    [seq1] = skin1,
                    [seq2] = skin2,
                    ...
                }
            },
            ...
            --]]
        },
        cfg = {
            --[[
            [author_uin . modelid] = cfg1,
            ...
            --]]
        },
        rpc = {},
    }, -- cache avatar skin 方便查询
    --}}}

    Uin = {
        get = function (self)
            local account = AccountManager.account
            return account and account.Uin or 0
        end,
    },
    --}}}

    OnInit = function (self)
        --{{{
        if AvatarSkinDef and AvatarSkinDef[1000] then
            AvatarSkinDef[1000] = self:private_get_valid_skin_cfg(AvatarSkinDef[1000]) 
        end 
        --}}}
    end,
    --{{{


    private_get_valid_skin_cfg = function (self, list)
        --{{{
        if not list then return {} end 
        local env = WorldCfg and GetIWorldConfig():getGameData("game_env") or 0
        -- 内网全部显示? 
        --if env ~= 1 then 
        --{{{
        local CltVersion  = LuaInterface:getCltVersion()
        local retlist = {}
        for k, v in pairs(list) do
            local c = v
            local ok = delegate(function ()
                --{{{
                if type(c) == 'table' then 
                    local CloseEnv = c.CloseEnv
                    if type(CloseEnv) == 'string' then 
                        local CloseEnvList = utils.split(CloseEnv, ',')
                        if CloseEnvList and CloseEnvList[1] then 
                            for _, Env in ipairs (CloseEnvList) do 
                                if tostring(env) == Env then 
                                    print('踢出 CloseEnv ', env, Env)
                                    return false
                                end 
                            end 
                        end 

                    end 



                    if CltVersion < 28 * 256 then 
                        if c.CostType and c.CostType == 0 then 
                            --不显示
                            print('踢出 CostType = ', c.CostType)
                            return false
                        end 
                    end
                end 

                if type(c) == 'table' and type(c.CltVersion) == 'string' and c.CltVersion ~= '' then 

                    local a, b, c = c.CltVersion:match('(%d+)%.(%d+)%.(%d+)')
                    c = c or '0'
                    if a and b and c then 
                        local ver = (tonumber(a) * 256 * 256) + tonumber(b) * 256 + tonumber(c)
                        if CltVersion < ver then print('踢出 版本号不符 要求版本号', ver, ' 当前版本号 ', CltVersion) end 
                        return CltVersion >= ver
                    end


                end 
                return true
                --}}}
            end )
            if ok == false then 
                -- print('XXXXXXXXX 踢出了 XXXXXXXXX', v.ModelID, ' cfg=', v)
            else
                table.insert(retlist, v) 
            end
        end 
        --}}}
        --end 
        return retlist
        --}}}
    end,


    private_skin_cache_get = function (self, part, seq)
        --{{{
        local account = AccountManager.account
        local avatar_cache = account and account.leveldb and account.leveldb.avatar and account.leveldb.avatar.cache

        if avatar_cache then 
            for i, v in ipairs (avatar_cache) do 
                if seq == (v.skin and v.skin.seq or '') then 
                    return ErrorCode.OK, self:private_valid_skin_get(v.skin)
                end 
            end 
        end 

        local c = self.cache.skin[part]
        if not c then return ErrorCode.FAILED end 
 
        local s = c.hash and c.hash[seq]
        if s then 
            if type(s) == 'table' then 
                return ErrorCode.OK, self:private_valid_skin_get(s)
            else
                return ErrorCode.OK, nil
            end 
        else
            return ErrorCode.FAILED
        end
        --}}}
    end,
    private_skin_cache_set = function (self, skin)
        --{{{
        if skin then 
            local c = self.cache.skin
            if type(skin) == 'table' then 
                local Part = skin.Part
                c[Part] = c[Part] or {}
                c[Part].hash = c[Part].hash or {}
                c[Part].hash[skin.seq] = skin
            else
                local Uin, AuthorUin, Part, ModelID = skin:match('(%d+)%.(%d+)%.(%d+)%.(%d+)')
                Part = tonumber(Part)
                c[Part] = c[Part] or {}
                c[Part].hash = c[Part].hash or {}
                c[Part].hash[skin] = skin
            end 
        end 
        --}}}
    end,

    private_skin_cache_check_dirty = function (self, skin)
        --{{{ 因为皮肤是作者的,不是我的, 所以,保存只是保存坑位信息
        return false
        --[[
        if type(skin) ~= 'table' then return true end 
        local code, cacheone = self:private_skin_cache_get(skin.Part, skin.seq)
        if code == ErrorCode.OK and type(cacheone) == 'table' then 
            if table.equal(cacheone, skin) then 
                return false --毛线, 啥也没有改, 不用保存
            end 
        end 
        return true
        ]]
        --}}}
    end,

    private_cfg_cache_get = function (self, author_uin, modelid)
        --{{{
        local cfg = self.cache.cfg
        local o = cfg[string.format('%s.%s', tostring(author_uin), tostring(modelid))]
        if o then 
            return ErrorCode.OK, o
        else

            local defaultcfg = account and account.leveldb and account.leveldb.avatar and account.leveldb.avatar.default
            if defaultcfg then 
                for i, v in ipairs (defaultcfg) do 
                    if v.Uin == author_uin and v.ModelID == modelid then 
                        return ErrorCode.OK, v
                    end 
                end 
            end

            local account = AccountManager.account
            local avatar_cache = account and account.leveldb and account.leveldb.avatar and account.leveldb.avatar.cache
            if avatar_cache then 
                for i, v in ipairs (avatar_cache) do 
                    if v and v.cfg and v.cfg.Uin == author_uin and v.cfg.ModelID == modelid then 
                        return ErrorCode.OK, v.cfg
                    end 
                end
            end

            --官方配置里面获取
            local official_cfg = AvatarSkinDef and AvatarSkinDef[author_uin] and AvatarSkinDef[author_uin][modelid]
            if official_cfg then return ErrorCode.OK, official_cfg end 

            return ErrorCode.FAILED
        end 
        --}}}
    end,
    private_cfg_cache_set = function (self, cfg)
        --{{{
        if cfg then 
            self.cache.cfg[string.format('%s.%s', tostring(cfg.Uin), tostring(cfg.ModelID))] = cfg 
            -- update cfg 
            local localcfglist = AvatarSkinDef and AvatarSkinDef[cfg.Uin]
            if localcfglist then 
                for i, v in ipairs (localcfglist) do 
                    if v.ModelID == cfg.ModelID then 
                        localcfglist[i] = cfg
                        break
                    end 
                end 
            end 
        end 
        --}}}
    end,

    private_rpc_cache_get = function (self, kind, key)
        --{{{
        local rpc = self.cache.rpc
        local Uin = self.Uin
        local value = rpc and rpc[Uin] and rpc[Uin][kind] and rpc[Uin][kind][key]
        if type(value) ~= 'nil' then return value end 

        --TODO 老版本还是用这个rpccache吧, 不然又会有大量的avatar请求过来. {{{
        --local cache {{{
        local cfg = container and container.cfg
        local avatar_local_cache = cfg and cfg.avatar_local_cache
        local CltVersion = LuaInterface:getCltVersion()
        if avatar_local_cache or CltVersion < 27 * 256 then 
            local data = container:load_from_file(string.format('avatar_%s', tostring(Uin)))
            if type(data) == 'table' then 
                rpc[Uin] = rpc[Uin] or {}
                for kind1, v in pairs(data) do
                    rpc[Uin][kind1] = rpc[Uin][kind1] or {}
                    for key1, vv in pairs(v) do
                        rpc[Uin][kind1][key1] = rpc[Uin][kind1][key1] or vv
                    end 
                end 
            end 
            return rpc and rpc[Uin] and rpc[Uin][kind] and rpc[Uin][kind][key]
        end 
        --}}}
        --}}}
        return nil

        --}}}
    end,
    private_rpc_cache_set = function (self, kind, key, value)
        --{{{
        local rpc = self.cache.rpc
        local Uin = self.Uin
        rpc[Uin] = rpc[Uin] or {}
        rpc[Uin][kind] = rpc[Uin][kind] or {}
        rpc[Uin][kind][key] = value

        --local cache {{{
        local cfg = container and container.cfg
        local avatar_local_cache = cfg and cfg.avatar_local_cache
        local CltVersion = LuaInterface:getCltVersion()
        if avatar_local_cache or CltVersion < 27 * 256 then 
            container:save_to_file(string.format('avatar_%s', tostring(Uin)), rpc[Uin])
        end 
        --}}}
        --}}}
    end,


    private_valid_skin_get = function (self, skin)
        --{{{
        if type(skin) == 'table' then 
            local ExpireTime = skin.ExpireTime
            local now = threadpool:now()
            if ExpireTime == -1 then return skin end 
            if ExpireTime > now then return skin end 
        end 
        return nil
        --}}}
    end,

    private_update_skin_info_in_cache = function (self, skin)
        --{{{
        if self.skin_info_cache and skin then self.skin_info_cache[skin.seq] = skin end 
        --}}}
    end,
    --}}}


    --{{{ 服务函数


    rpc_cache_clear = function (self, ...)
        --{{{
        print(' rpc clear ', ...)
        self.skin_info_cache = nil -- clear skin info cache

        local rpc = self.cache.rpc
        local Uin = self.Uin
        if rpc and rpc[Uin] then 
            for i = 1, select('#', ...) do
                local todo = select(i, ...)
                for kind, v in pairs(rpc[Uin]) do
                    if kind:match(todo) then 
                        local keys = {}
                        for key, vv in pairs(v) do table.insert(keys, key) end 
                        for iii, key in ipairs (keys) do v[key] = nil end 
                    end 
                end 
            end 
        end 

        --local cache {{{
        local cfg = container and container.cfg
        local avatar_local_cache = cfg and cfg.avatar_local_cache
        local CltVersion = LuaInterface:getCltVersion()
        if avatar_local_cache or CltVersion < 27 * 256 then 
            container:save_to_file(string.format('avatar_%s', tostring(Uin)), rpc[Uin])
        end 
        --}}}
        return ErrorCode.OK
        --}}}
    end,

    skin_query = function (self, seq)
    end,

    skin_cache = function (self, seq, info)
    end,

    -- Uin : 作者Uin, ModelID
    skin_cfg_query = function (self, Uin, ModelID)
    end,

    skin_buy = function (self, Uin, ModelID)
        --{{{
        local code, cfg, skin = AccountManager:remote_call('shopsvr', 'avatar_skin_buy', Uin, ModelID)
        if code ~= ErrorCode.OK then 
            if code == ErrorCode.PRECHECK_AVATAR_SKIN_ALREADY_HAS and UpdatePartServerInfo then UpdatePartServerInfo(ModelID) end 
            return code 
        end 
        self:private_skin_cache_set(skin)
        self:private_cfg_cache_set(cfg)
        if skin then self:rpc_cache_clear('skin.' .. skin.Part) end 
        if cfg then self:rpc_cache_clear('cfg.' .. cfg.Part) end 
        skin = self:private_valid_skin_get(skin)

        -- cache {{{
        if self.skin_info_cache then self:private_update_skin_info_in_cache(skin) end 

        -- }}}

        return ErrorCode.OK, cfg, skin
        --}}}
    end,
    skin_buy_list = function (self, list)
        --{{{
        local code, ret = AccountManager:remote_call('shopsvr', 'avatar_skin_buy_list', list)
        if code ~= ErrorCode.OK then return code end 
        if type(ret) then 
            for i, v in ipairs (ret) do 
                local code, cfg, skin = unpack(v)
                if code == ErrorCode.OK then 
                    self:private_skin_cache_set(skin)
                    self:private_cfg_cache_set(cfg)
                    if skin then self:rpc_cache_clear('skin.' .. skin.Part) end 
                    if cfg then self:rpc_cache_clear('cfg.' .. cfg.Part) end 
                    skin = self:private_valid_skin_get(skin)
                    self:private_update_skin_info_in_cache(skin)
                end 
            end 
        end 
        return ErrorCode.OK, ret
        --}}}
    end,

    skin_info_page = function (self, Part, Min, Max)
        --{{{
        print('======== avatar skin info by page ============')
        local kind = string.format('%s.%s', 'skin', tostring(Part))

        local key = string.format('%s.%s', tostring(Min), tostring(Max))
        local cache_result = self:private_rpc_cache_get(kind, key)
        if cache_result then return ErrorCode.OK, cache_result end 

        ShowNoTransparentLoadLoop()
        local code, list = self.cluster.shopsvr.avatar_skin_info_page(Part, Min, Max)
        HideNoTransparentLoadLoop()
        -- cache ????
        if code == ErrorCode.OK and list then 
            local n = #list
            for i = n, 1, -1 do
                local skin = list[i]
                self:private_skin_cache_set(skin)
                if not self:private_valid_skin_get(skin) then 
                    table.remove(list, i)
                end 
            end 
            self:private_rpc_cache_set(kind, key, list)
        end 

        return code, list
        --}}}
    end,

    skin_cfg_page = function (self, Part, Min, Max)
        --{{{
        print('======== avatar skin cfg by page ============')
        local code, list = delegate(function ()
            local kind = string.format('cfg.%s', tostring(Part))
            local key = string.format('%s.%s', tostring(Min), tostring(Max))
            local cache_result = self:private_rpc_cache_get(kind, key)
            if cache_result then 
                if type(cache_result) == 'table' then cache_result = self:private_get_valid_skin_cfg(cache_result) end 
                return ErrorCode.OK, cache_result 
            end 

            ShowNoTransparentLoadLoop()
            local code, list = self.cluster.shopsvr.avatar_skin_cfg_page(Part, Min, Max)
            HideNoTransparentLoadLoop()
            -- cache ????
            if code == ErrorCode.OK then 
                for i, v in ipairs (list) do 
                    if v.cfg then self:private_cfg_cache_set(v.cfg) end 
                    if v.skin then 
                        self:private_skin_cache_set(v.skin) 
                    end 
                    v.skin = self:private_valid_skin_get(v.skin)
                end 
                self:private_rpc_cache_set(kind, key, list)
            end 
            return code, list
        end )

        if code == ErrorCode.OK and type(list) == 'table' then 
            --{{{
            list = self:private_get_valid_skin_cfg(list)
            --}}}
        end 


        print('code:', code, ' list:', list)
        return code, list
        --}}}
    end,

    --坑位信息
    seat_info = function (self, seatid)
        --{{{
        local account = AccountManager.account
        local leveldb = account.leveldb

        local seat = account:avatar_seat_get(seatid)

        if not seat then 
            --没有这个坑
            return ErrorCode.FAILED, nil
        end 

        local clone = table.clone(seat)

        local Uin = account.Uin

        print("get_seat_info", seatid, clone)
        clone.skin = delegate(function ()
            local todo_remote = {} --需要远程处理的东东
            --{{{
            local ret = {}
            if clone.skin then 
                for k, v in pairs(clone.skin) do
                    local one = {}
                    local part = tonumber(k)
                    local author_uin = v.AuthorUin or 1000
                    local modelid = v.ModelID

                    local seq = string.format('%d.%d.%d.%d', Uin, author_uin, part, modelid)
                    local needremote = false

                    local code, skin = self:private_skin_cache_get(part, seq)
                    skin = self:private_valid_skin_get(skin)
                    if code == ErrorCode.OK and type(skin) == 'table' then 
                        one.skin = table.clone(skin)
                    else
                        needremote = true
                    end 
                    
                    local code, cfg = self:private_cfg_cache_get(author_uin, modelid)
                    if code == ErrorCode.OK then 
                        one.cfg = table.clone(cfg)
                        if cfg.CostType and cfg.CostType == 0 then 
                            --免费的 {{{
                            one.skin = table.clone(v)
                            needremote = false
                            --}}}
                        end 
                    else
                        needremote = true
                    end

                    if needremote then 
                        table.insert(todo_remote, seq) 
                    else
                        ret[part] = one
                    end 
                end 

            end 

            if todo_remote[1] then 
                --有东西取服务端取数据
                --ShowNoTransparentLoadLoop()
                local code, list = self.cluster.shopsvr.avatar_skin_cfg_list(todo_remote)
                --HideNoTransparentLoadLoop()
                if code == ErrorCode.OK then 
                    for i, v in ipairs (list) do 
                        local rskin = self:private_valid_skin_get(v.skin)
                        if rskin then
                            self:private_skin_cache_set(v.skin)
                            self:private_cfg_cache_set(v.cfg)
                            ret[v.Part] = {
                                cfg = v.cfg,
                                skin = rskin,
                            }
                        else
                            if leveldb.avatar_pvc and v.cfg and v.seq then
                                local now = getServerTime()
                                if leveldb.avatar_pvc > now and self:is_use_avatar_pvc(v.cfg.Tag, v.cfg.ModelID) then
                                    -- 特权卡有效
                                    local Uin, AuthorUin, Part, ModelID = v.seq:match('(%d+)%.(%d+)%.(%d+)%.(%d+)')
                                    ret[v.Part] = {
                                        cfg = v.cfg,
                                        skin = {
                                            seq = v.seq,
                                            Uin = tonumber(Uin),
                                            AuthorUin = tonumber(AuthorUin),
                                            ModelID = tonumber(ModelID),
                                            Part = tonumber(Part),
                                            ExpireTime = leveldb.avatar_pvc, -- -1表示不过期
                                            CreateTime = now,
                                            ModifyTime = now, 
                                            Tag = v.cfg.Tag
                                        }
                                    }
                                else
                                    if self:seat_reset(seatid) == ErrorCode.OK then
                                        account:avatar_seat_reset(seatid, now)
                                        return nil
                                    end
                                end
                            end
                        end
                        
                    end 
                end 
            end 

            for Part, v in pairs(ret) do
                if type(v.skin) == 'table' then 
                    v.skin.Data = clone.skin and clone.skin[tostring(Part)] and clone.skin[tostring(Part)].Data
                end 
            end 
            
            return ret
            --}}}
        end)

        clone.seatid = seatid

        return ErrorCode.OK, clone

        --}}}
    end,

    is_use_avatar_pvc = function(CostType, ModelID)
        if ns_shop_config2 and ns_shop_config2.avatar_vip_cfg then
            local avtcfg = ns_shop_config2.avatar_vip_cfg
            for _, v in ipairs(avtcfg.notAvailable_Avatar_type) do
                if v == CostType then
                    return false
                end
            end 
            for _, v in ipairs(avtcfg.notAvailable_Avatar_ID) do
                if v == ModelID then
                    return false
                end
            end
            return true
        end
        return false
    end,

    -- 查询其他玩家坑位信息, 如果seatid为空,那么就是当前
    other_seat_info = function (self, Uin, seatid)
        --{{{
        if type(Uin) ~= 'number' then return ErrorCode.PRECHECK_TYPE_ERROR end 
        if type(seatid) == 'string' and seatid == '' then return ErrorCode.FAILED, nil end 
        local now = os.time()
        seatid = seatid and tonumber(seatid) or 0
        MiniLog('other_seat_info',Uin, self.other_cache[Uin])
        if self.other_cache and self.other_cache[Uin] and self.other_cache[Uin][seatid] then
            local cacheinfo = self.other_cache[Uin][seatid]
            --if type(cacheinfo.rec_ts) == 'number' and cacheinfo.rec_ts < now + 1800  then)
            -- if type(cacheinfo.rec_ts) == 'number' and cacheinfo.rec_ts < now + 10  then --没太看懂，这么写的话永远都是取的缓存，不奇怪吗？
            if type(cacheinfo.rec_ts) == 'number' and now - cacheinfo.rec_ts <= 10  then 
                MiniLog('other_seat_info-2', cacheinfo, cacheinfo.info)
                return ErrorCode.OK, cacheinfo.info
            end
        end
        --ShowNoTransparentLoadLoop()
        local code, info = self.cluster.shopsvr.avatar_other_seat_info(Uin, seatid)
        --HideNoTransparentLoadLoop()
        MiniLog('other_cache0=',code, info)
        if code == ErrorCode.OK and info then 
            info.skin = delegate(function ()
                local ret = {}
                for i, v in ipairs (info.skin) do 
                    --if v.skin then v.skin.ExpireTime = v.skin.ExpireTime or -1 end 
                    if v.cfg and v.cfg.CostType and v.cfg.CostType == 0 then 
                        ret[v.cfg.Part] = {
                            cfg = v.cfg,
                            skin = v.skin,
                        }
                    else
                        if v.skin then
                            local bexpired = self:private_valid_skin_get(v.skin);
                            if bexpired then
                                ret[v.Part] = {
                                    cfg = v.cfg,
                                    skin = bexpired,
                                }
                            end
                        else
                            -- 是否特权卡期间
                            if v.cfg and v.seq and self:is_use_avatar_pvc(v.cfg.Tag, v.cfg.ModelID) then
                                 -- 特权卡有效
                                local now = getServerTime()
                                local Uin, AuthorUin, Part, ModelID = v.seq:match('(%d+)%.(%d+)%.(%d+)%.(%d+)')
                                ret[v.Part] = {
                                    cfg = v.cfg,
                                    skin = {
                                        seq = v.seq,
                                        Uin = tonumber(Uin),
                                        AuthorUin = tonumber(AuthorUin),
                                        ModelID = tonumber(ModelID),
                                        Part = tonumber(Part),
                                        ExpireTime = -1, -- -1表示不过期
                                        CreateTime = now,
                                        ModifyTime = now, 
                                        Tag = v.cfg.Tag
                                    }
                                }
                            end
                        end
                    end 
                end 
                return ret
            end )
        end 

        -- 做个缓存
        if self.other_cache then
            if not self.other_cache[Uin] then
                self.other_cache[Uin] = {}
            end
        end
        
        if code == ErrorCode.OK and info then
            self.other_cache[Uin][seatid] = {rec_ts = now, info = info}
        end

        MiniLog('other_cache=',self.other_cache[Uin][seatid], info)
        
        return code, info
        --}}}
    end,

    seat_unlock = function (self, seatid)
        --{{{
        return AccountManager:remote_call('shopsvr', 'avatar_seat_unlock', seatid)
        --}}}
    end,

    --保存坑位信息, 保存的坑位信息的数据结构和 seat_info给出的数据结构一致.
    seat_save = function (self, seatid, info)
        --{{{
        info = table.clone(info)
        print('seat save', seatid, info)
        --local skin_save_list = {}
        local data = {}
        info.skin = delegate(function ()
            --{{{
            local skin = info.skin
            local ret = {}
            for k, o in pairs(skin) do
                if o.skin then 
                    ret[tostring(o.skin.Part or o.cfg.Part)] = {
                        AuthorUin = o.skin.AuthorUin or o.cfg.Uin,
                        ModelID = o.skin.ModelID or o.cfg.ModelID,
                        Part = o.skin.Part or o.cfg.Part,
                        Data = o.skin.Data,
                    }
                else
                    if o.cfg then 
                        ret[tostring(o.cfg.Part)] =  {
                            AuthorUin = o.cfg.Uin,
                            ModelID = o.cfg.ModelID,
                            Part = o.cfg.Part,
                        }
                    end 
                end 
            end 
            return ret
            --}}}
        end )
        info.seatid = nil
        info.ts = nil
        -- 解决海外bug:https://www.tapd.cn/********/bugtrace/bugs/view?bug_id=11********001091123
        -- 【ID1091123】【统一代码】AVT—地图跳转至定制库，正在使用的栏位调整后状态异常
        if isAbroadEvn() then
            return AccountManager:remote_call('shopsvr', 'avatar_seat_save_new', seatid, info)
        end
        return AccountManager:remote_call('shopsvr', 'avatar_seat_save', seatid, info)
        --}}}
    end,

    seat_reset = function (self, seatid)
        return AccountManager:remote_call('shopsvr', 'avatar_seat_reset', seatid)
    end,

    skin_info_list = function (self, cfglist)
        --{{{
        local account = AccountManager.account
        local Uin = account.Uin
        local printflag = _G.printflag or false
        local log = function (...)
            if printflag then print('skin_info_list ', ...) end 
        end 

        if self.skin_info_cache  == nil then 
            print('skin info cache is nil')
            if self.skin_info_fetching == true then 
                print(' fetching skin ....')
                local gid = gen_gid()
                local code = threadpool:wait(gid, 10, function ()
                    if self.skin_info_fetching == false then 
                        threadpool:notify(gid, ErrorCode.OK)
                    end 
                end )
                if code ~= ErrorCode.OK then return code end 
                print('wait fetch skin ok ', self.skin_info_cache)
            else
                self.skin_info_fetching = true
                --当前也只有官方的, 暂时先这样吧 {{{
                local ok, code = pcall(function ()
                    local code, official_skin_cfg_list = self:official_skin_cfg_list()
                    if code == ErrorCode.OK then 
                        --{{{
                        local l = {}
                        for i, v in ipairs (official_skin_cfg_list) do 
                            local seq = string.format('%d.%d.%d.%d', Uin, v.Uin, v.Part, v.ModelID)
                            print('XXXXXXXXX', seq)
                            table.insert(l, seq)
                        end 

                        local code, ret = self:avatar_skin_info_list_ex(l)
                        if code ~= ErrorCode.OK then return code end 
                        self.skin_info_cache = {}
                        for k, v in pairs(ret) do
                            self.skin_info_cache[v.seq] = v
                        end
                        for i, v in ipairs (official_skin_cfg_list) do 
                            local seq = string.format('%d.%d.%d.%d', Uin, v.Uin, v.Part, v.ModelID)
                            self.skin_info_cache[seq] = self.skin_info_cache[seq] or nil
                        end 
                        return ErrorCode.OK,ret
                        --}}}
                    else
                        return code
                    end 
                end )
                --}}}
                print(' fetch skin finish ')
                self.skin_info_fetching = false
                if not ok then return ErrorCode.EXCEPTION, log('EXCEPTION ', code) end 
                if code ~= ErrorCode.OK then return code end 
                print(' fetch skin done')
            end 
        end 

        if self.skin_info_cache then 
            print(' get skin from skin info cache ', self.skin_info_cache)
            --{{{
            local list = {}
            local ret = {}
            for i, v in ipairs (cfglist) do 
                local seq = string.format('%d.%d.%d.%d', Uin, v.Uin, v.Part, v.ModelID)
                print(' seq ', seq)
                local incache = self.skin_info_cache[seq]
                if type(incache) == 'table' then 
                    print(' skin in cache ', seq)
                    table.insert(ret, table.clone(incache))
                elseif type(incache) == 'nil' then
                    print(' skin not in cache ', seq )
                    table.insert(list, seq)
                else
                    print(' skin must be nil', seq)
                end 
            end
            if list and list[1] then 
                local code, list2 = self:avatar_skin_info_list_ex(list)
                if code == ErrorCode.OK and list2 then 
                    for i, v in ipairs (list2) do 
                        table.insert(ret, v)
                    end 
                end 
            end 
            return ErrorCode.OK, ret
            --}}}
        else
            print( ' get skin from remote ' )
            --{{{
            local list = {}
            for i, v in ipairs (cfglist) do 
                local seq = string.format('%d.%d.%d.%d', Uin, v.Uin, v.Part, v.ModelID)
                table.insert(list, seq)
            end 
            local code,ret = self:avatar_skin_info_list_ex(list)
            return code, ret
            --}}}
        end 

        --}}}
    end,

    avatar_skin_info_list_ex = function(self, list)
        --分批次拉取
        local retlist = {}
        local count = 0
        local getlist = {}
        local code = ErrorCode.OK
        local ret = {}
        for k, v in ipairs(list) do
            count = count + 1
            table.insert(getlist, v)
            --一次拉50个
            if count >= 50 then
                -- 拉取
                code, ret = self.cluster.shopsvr.avatar_skin_info_list(getlist)
                if code == ErrorCode.OK and ret[1] then
                    for k,v in ipairs(ret) do 
                        table.insert(retlist, v)
                    end
                end

                count = 0
                getlist = {}
            end
        end

        code, ret = self.cluster.shopsvr.avatar_skin_info_list(getlist)
        if code == ErrorCode.OK and ret[1] then
            if code == ErrorCode.OK and ret[1] then
                for k,v in ipairs(ret) do 
                    table.insert(retlist, v)
                end
            end
        end

        print('retlist-------------------------------------')
        print('avatar_skin_info_list_ex =',code, retlist)
        print('retlist-------------------------------------')
        return code, retlist
    end,

    wish_add = function (self, id)
        return AccountManager:remote_call('shopsvr', 'avatar_wish_add', id)
    end,
    wish_rm = function (self, id)
        return AccountManager:remote_call('shopsvr', 'avatar_wish_rm', id)
    end,


    official_skin_cfg_list = function (self)
        local list = AvatarSkinDef and AvatarSkinDef[1000]
        if list then 
            return ErrorCode.OK, self:private_get_valid_skin_cfg(table.clone(list))
        else
            return ErrorCode.OK, {}
        end 
    end,

    avatar_skin_count = function (self)
        --{{{
        -- 先从本地数据取
        local account = AccountManager.account
        local avatarcount = account and account.leveldb and account.leveldb.avatar and account.leveldb.avatar.avatarcount
        if avatarcount then
            return ErrorCode.OK, avatarcount
        end
        --从服务器获取
        local code, count = AccountManager:remote_call('shopsvr', 'get_avatar_skin_count', Uin)
        if code ~= ErrorCode.OK then 
            return code, 0
        end 

        return ErrorCode.OK, count
        --}}}
    end,

    avatar_had_list = function (self)
        --{{{
        --从服务器获取
        local code, list = AccountManager:remote_call('shopsvr', 'get_avatar_had_list')
        if code ~= ErrorCode.OK then 
            return code, {}
        end 

        return ErrorCode.OK, list
        --}}}
    end,



    --}}}

}, class.ServiceBase)

