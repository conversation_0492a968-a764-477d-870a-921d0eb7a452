#include "BlockArcPlate.h"
#include "BlockMaterialMgr.h"
#include "section.h"
#include "SectionMesh.h"
#include "BlockGeom.h"
#include "world.h"
#include "DefManagerProxy.h"
#include "IPlayerControl.h"
#include "IClientPlayer.h"
#include "BlockMeshVert.h"
#include "WorldManager.h"
#include "WorldRender.h"
#include "CurveFace.h"
//#include "WeatherManager.h"
//#include "OgreMaterial.h"
#include "Gizmo/DebugUtility.h"
#include "Common/GameStatic.h"

IMPLEMENT_SCENEOBJECTCLASS(ArcPlateMaterial)
//IMPLEMENT_BLOCKINSTANCE(ArcPlateMaterial)
using namespace MINIW;


static MINIW::GameStatic<dynamic_array<dynamic_array<BlockGeomVert>>> s_mArcWholeFace;
dynamic_array<dynamic_array<BlockGeomVert>>& ArcPlateMaterial::m_mArcWholeFace()
{
	return *s_mArcWholeFace.EnsureInitialized();
}

dynamic_array<UInt16>* ArcPlateMaterial::m_dNegIndices/* = { 0, 3, 2, 0, 2, 1 }*/;
dynamic_array<UInt16>* ArcPlateMaterial::m_dPosIndices/* = { 0, 1, 2, 0, 2, 3 }*/;
dynamic_array<UInt16>* ArcPlateMaterial::m_NegTrIndices/* = { 0, 2, 1 }*/;
dynamic_array<UInt16>* ArcPlateMaterial::m_PosTrIndices/* = { 0, 1, 2 }*/;
dynamic_array<UInt16>* ArcPlateMaterial::m_PosDiamondIndices/* = { 2,3,1,3,4,1,4,0,1 }*/;
dynamic_array<UInt16>* ArcPlateMaterial::m_PosBigArcIndices/* = {  0,1,2,0,2,3,3,2,5,3,5,4,4,5,6,4,6,7 }*/;
dynamic_array<UInt16>* ArcPlateMaterial::m_TurnArcIndices/* = {0,2,1,1,2,4,1,4,3,3,4,6,3,6,5 }*/;
dynamic_array<UInt16>* ArcPlateMaterial::m_PosBigDiamondIndices/* = { 2,3,1,3,4,1,4,5,1,5,0,1 }*/;
Rainbow::Vector3f* ArcPlateMaterial::ms_LightVec;
void initAllArcPlateIndex(void* data)
{
	ArcPlateMaterial::m_dNegIndices = new dynamic_array<UInt16>();
	*ArcPlateMaterial::m_dNegIndices = { 0, 3, 2, 0, 2, 1 };
	ArcPlateMaterial::m_dPosIndices = new dynamic_array<UInt16>();
	*ArcPlateMaterial::m_dPosIndices = { 0, 1, 2, 0, 2, 3 };
	ArcPlateMaterial::m_NegTrIndices = new dynamic_array<UInt16>();
	*ArcPlateMaterial::m_NegTrIndices = { 0, 2, 1 };
	ArcPlateMaterial::m_PosTrIndices = new dynamic_array<UInt16>();
	*ArcPlateMaterial::m_PosTrIndices = { 0, 1, 2 };
	ArcPlateMaterial::m_PosDiamondIndices = new dynamic_array<UInt16>();
	*ArcPlateMaterial::m_PosDiamondIndices = { 2,3,1,3,4,1,4,0,1 };
	ArcPlateMaterial::m_PosBigArcIndices = new dynamic_array<UInt16>();
	*ArcPlateMaterial::m_PosBigArcIndices = { 0,1,2,0,2,3,3,2,5,3,5,4,4,5,6,4,6,7 };
	ArcPlateMaterial::m_TurnArcIndices = new dynamic_array<UInt16>();
	*ArcPlateMaterial::m_TurnArcIndices = {0,2,1,1,2,4,1,4,3,3,4,6,3,6,5 };
	ArcPlateMaterial::m_PosBigDiamondIndices = new dynamic_array<UInt16>();
	*ArcPlateMaterial::m_PosBigDiamondIndices = { 2,3,1,3,4,1,4,5,1,5,0,1 };
	ArcPlateMaterial::ms_LightVec = new Rainbow::Vector3f(0.f, 0.8944f, 0.4472f);
}
void uninitAllArcPlateIndex(void* data)
{
	ArcPlateMaterial::m_dNegIndices->clear_dealloc();
	delete(ArcPlateMaterial::m_dNegIndices);
	ArcPlateMaterial::m_dPosIndices->clear_dealloc();
	delete(ArcPlateMaterial::m_dPosIndices);
	ArcPlateMaterial::m_NegTrIndices->clear_dealloc();
	delete(ArcPlateMaterial::m_NegTrIndices);
	ArcPlateMaterial::m_PosTrIndices->clear_dealloc();
	delete(ArcPlateMaterial::m_PosTrIndices);
	ArcPlateMaterial::m_PosDiamondIndices->clear_dealloc();
	delete(ArcPlateMaterial::m_PosDiamondIndices);
	ArcPlateMaterial::m_PosBigArcIndices->clear_dealloc();
	delete(ArcPlateMaterial::m_PosBigArcIndices);
	ArcPlateMaterial::m_TurnArcIndices->clear_dealloc();
	delete(ArcPlateMaterial::m_TurnArcIndices);
	ArcPlateMaterial::m_PosBigDiamondIndices->clear_dealloc();
	delete(ArcPlateMaterial::m_PosBigDiamondIndices);
	delete(ArcPlateMaterial::ms_LightVec);
}
GameRuntimeInitializeAndCleanup initArcPlateIdx(initAllArcPlateIndex, uninitAllArcPlateIndex);

// dynamic_array<dynamic_array<BlockGeomVert>> ArcPlateMaterial::m_mArcWholeFace;


ArcPlateMaterial::ArcPlateMaterial() 
{

}

ArcPlateMaterial::~ArcPlateMaterial()
{


}

void ArcPlateMaterial::init(int resid)
{
	CubeBlockMaterial::init(resid);
	SetToggle(BlockToggle_IsOpaqueCube, false);
	SetAttrRenderType(BLOCKRENDER_CUBE_MODEL);

	if (m_LoadOnlyLogic) return;

	char texname[256];
	RenderBlockMaterial* topmtl = nullptr , * sidemtl = nullptr, * bottommtl = nullptr;

	sprintf(texname, "%s_top", GetBlockDef()->Texture1.c_str());
	topmtl = g_BlockMtlMgr.createRenderMaterial(texname, GetBlockDef());
	if (topmtl == NULL)
	{
		topmtl = g_BlockMtlMgr.createRenderMaterial(GetBlockDef()->Texture1.c_str(), GetBlockDef(), GETTEX_WITHDEFAULT);

		sidemtl = topmtl;
		sidemtl->AddRef();
		bottommtl = topmtl;
		bottommtl->AddRef();
	}
	else
	{
		if (!GetBlockDef()->Texture2.empty()) sidemtl = g_BlockMtlMgr.createRenderMaterial(GetBlockDef()->Texture2.c_str(), GetBlockDef(), GETTEX_WITHDEFAULT);
		else
		{
			sprintf(texname, "%s_side", GetBlockDef()->Texture1.c_str());
			sidemtl = g_BlockMtlMgr.createRenderMaterial(texname, GetBlockDef(), GETTEX_WITHDEFAULT);
		}

		sprintf(texname, "%s_bottom", GetBlockDef()->Texture1.c_str());
		bottommtl = g_BlockMtlMgr.createRenderMaterial(texname, GetBlockDef());
		if (bottommtl == NULL)
		{
			bottommtl = topmtl;
			bottommtl->AddRef();
		}
	}


	setFaceMtl(DIR_POS_X, sidemtl);
	setFaceMtl(DIR_NEG_Z, sidemtl);
	setFaceMtl(DIR_NEG_X, sidemtl);
	setFaceMtl(DIR_POS_Z, sidemtl);

	setFaceMtl(DIR_POS_Y, topmtl);
	setFaceMtl(DIR_NEG_Y, bottommtl);


	ENG_RELEASE(sidemtl);
	ENG_RELEASE(topmtl);
	ENG_RELEASE(bottommtl);

	m_nSpecialLogicType[0] |= RotateMechaStopNoChangePos;
	initVertData();
}

bool ArcPlateMaterial::hasSolidTopSurface(int blockdata)
{
	return true;
}

float ArcPlateMaterial::getBlockHeight(int blockdata)
{
	if (blockdata & 8)
	{
		return 1.f;
	}
	else if (blockdata & 4)
	{
		return -0.5f;
	}
	return 0.5f;
}
void  ArcPlateMaterial::drawLine(const Rainbow::Vector3f& point1, const Rainbow::Vector3f& point2, World* pworld)
{
	//我们先求出2点之间的向量
	//我们认为这个是x向量
	Rainbow::Vector3f localX = point2 - point1;
	Rainbow::Vector3f normalX = localX;
	normalX.NormalizeSafe();
	//我们整个y向量
	Rainbow::Vector3f normalY(-normalX.z, 0, normalX.x);
	//整个z向量
	Rainbow::Vector3f normalZ = Rainbow::Vector3f::Cross(normalX, normalY);
	normalZ.NormalizeSafe();
	//我们只求周围点,所以不用管轴方向
	////我们需要一个世界矩阵
	//Rainbow::Matrix4x4f worldTrans;
	//worldTrans.SetIdentity();
	//worldTrans.SetPosition(-point);
	////模型矩阵
	//Rainbow::Matrix3x3f model;
	//model.SetIdentity();
	//model.SetBasisTransposed(normalX, normalY, normalZ);;
	//
	//Rainbow::Vector3f localPoint2 = model.MultiplyVector3(worldTrans.MultiplyVector3(point2));

	//我们找到要绘制的8个点
	//0: x0y0z0, //1: x1y0z0, //2: x1y0z1  //3: x0y0z1//4: x0y1z0//5: x1y1z0//6: x1y1z1//7: x0y1z1
	float range = 1;
	Rainbow::Vector3f renderPoint[8];
	//renderPoint[0] = point1 - normalY * range - normalZ * range;
	//renderPoint[1] = point2 - normalY * range - normalZ * range;
	//renderPoint[2] = point2 - normalY * range + normalZ * range;
	//renderPoint[3] = point1 - normalY * range + normalZ * range;
	//renderPoint[4] = point1 + normalY * range - normalZ * range;
	//renderPoint[5] = point2 + normalY * range - normalZ * range;
	//renderPoint[6] = point2 + normalY * range + normalZ * range;
	//renderPoint[7] = point1 + normalY * range + normalZ * range;
	renderPoint[0] = point1;
	renderPoint[1] = Rainbow::Vector3f(point2.x, point1.y, point1.z);
	renderPoint[2] = Rainbow::Vector3f(point2.x, point1.y, point2.z);
	renderPoint[3] = Rainbow::Vector3f(point1.x, point1.y, point2.z);
	renderPoint[4] = Rainbow::Vector3f(point1.x, point2.y, point1.z);
	renderPoint[5] = Rainbow::Vector3f(point2.x, point2.y, point1.z);
	renderPoint[6] = Rainbow::Vector3f(point2.x, point2.y, point2.z);
	renderPoint[7] = Rainbow::Vector3f(point1.x, point2.y, point2.z);
	std::vector<BlockGeomVert> vert1;
	vert1.resize(8);
	for (int j = 0; j < 8; j++)
	{
		auto& vert = vert1[j];
		vert.pos.w = (0x7f << 8) | 0x7f;
		vert.color = BlockColor(255, 255, 255, 255);
		vert.normal = BlockVector(127, 127, 127, 127);// Vector3f::zero;
		vert.uv.x = 0;
		vert.uv.y = 0;
		vert.pos.x = renderPoint[j].x;
		vert.pos.y = renderPoint[j].y;
		vert.pos.z = renderPoint[j].z;
	}
	std::vector<unsigned short>indices;
	indices.reserve(36);
	indices.push_back(0);
	indices.push_back(1);
	indices.push_back(2);
	indices.push_back(1);
	indices.push_back(2);
	indices.push_back(3);

	indices.push_back(4);
	indices.push_back(5);
	indices.push_back(7);
	indices.push_back(5);
	indices.push_back(6);
	indices.push_back(7);

	indices.push_back(0);
	indices.push_back(4);
	indices.push_back(1);
	indices.push_back(4);
	indices.push_back(5);
	indices.push_back(1);

	indices.push_back(1);
	indices.push_back(5);
	indices.push_back(2);
	indices.push_back(5);
	indices.push_back(6);
	indices.push_back(2);

	indices.push_back(3);
	indices.push_back(7);
	indices.push_back(2);
	indices.push_back(7);
	indices.push_back(6);
	indices.push_back(2);

	indices.push_back(0);
	indices.push_back(4);
	indices.push_back(7);
	indices.push_back(4);
	indices.push_back(7);
	indices.push_back(3);
	pworld->getRender()->getCurveRender()->addRect(CURVEFACEMTL_1YELLOW, Rainbow::Vector3f(0, 0, 0), vert1, indices);
}


void ArcPlateMaterial::drawBox(WCoord min, WCoord max)
{
	//WCoord down[4] = { WCoord(min.x, min.y, min.z), WCoord(max.x, min.y, min.z), WCoord(max.x, min.y, max.z), WCoord(min.x, min.y, max.z) };
	//WCoord up[4] = { WCoord(min.x, max.y, min.z), WCoord(max.x, max.y, min.z), WCoord(max.x, max.y, max.z), WCoord(min.x, max.y, max.z) };
	//World* pWorld = GetWorldManagerPtr()->getWorld(GetWorldManagerPtr()->getWoldPwid());
}

unsigned char ArcPlateMaterial::TriangleNormal2LightColor(const Rainbow::Vector3f& normal)
{
	Rainbow::Vector3f tmp;
	tmp.x = Rainbow::Abs(normal.x);
	tmp.y = normal.y;
	tmp.z = Rainbow::Abs(normal.z);

	//Vector3 v = s_LightVec;
	//Normalize(v);
	float t = DotProduct(tmp, *ms_LightVec);
	t = t * 0.2f + 0.8f;

	return (unsigned char)(t * 255);
}

bool ArcPlateMaterial::canSprayPaint(World* pworld, const WCoord& blockpos)
{
	return (pworld->getBlockData(blockpos) & 8) > 0;
}

int ArcPlateMaterial::convertDataByRotate(int blockdata, int rotatetype)
{
	int dir = commonConvertDataByRotate(blockdata & 3, rotatetype);
	return ((blockdata >> 2) << 2) | dir;
}
void ArcPlateMaterial::createCollideData(CollisionDetect* coldetect, World* pworld, const WCoord& blockpos)
{
	float blockheight = getBlockHeight(pworld->getBlockData(blockpos));
	WCoord pos = blockpos * BLOCK_SIZE;
	
	if (blockheight == 1)
	{
		coldetect->addObstacle(pos, pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
	}
	else
	{
		
		short step = 10;
		float movesize = 1.f / float(step);
		float heightOffset = 1.f / (float(step));
		int size = BLOCK_SIZE ;
		int dir = pworld->getBlockData(blockpos) & 3;

		auto pblock = pworld->getBlock(blockpos);
		int warp = -1;
		int turnDir = -1;

		auto frontBlock = pworld->getBlock(blockpos + g_DirectionCoord[dir]);
		if (pblock.getResID() == frontBlock.getResID() && frontBlock.getData() < 8 && ((pblock.getData() & 4) == (frontBlock.getData() & 4)))
		{
			int frontDir = frontBlock.getData() & 3;
			if (frontDir != dir && frontDir != ReverseDirection(dir))
			{
				warp = 0;
				turnDir = frontDir;
			}
		}
		if (warp == -1)
		{
			auto backBlock = pworld->getBlock(blockpos + g_DirectionCoord[ReverseDirection(dir)]);
			if (pblock.getResID() == backBlock.getResID() && backBlock.getData() < 8 && ((pblock.getData() & 4) == (backBlock.getData() & 4)))
			{
				int backDir = backBlock.getData() & 3;
				if (backDir != dir && backDir != ReverseDirection(dir))
				{
					warp = 1;
					turnDir = backDir;
				}
			}
		}
		int count = 30;
		if (warp == -1)
		{
			if (blockheight > 0)
			{
				int countBox[10] = { 10,25,35,40,40,35,30,20,15,5 };
				if (dir == 0)
				{
					for (int i = 0; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(int(movesize * i * BLOCK_SIZE), 0, 0), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * size +countBox[i]), BLOCK_SIZE));
						//drawBox(pos + WCoord(int(movesize * i * BLOCK_SIZE), 0, 0), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * size), BLOCK_SIZE));
					}
				}
				else if (dir == 1)
				{
					for (int i = 0; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(0, 0, 0), pos + WCoord(BLOCK_SIZE - int(movesize * i * BLOCK_SIZE), int(heightOffset * i * size +countBox[i]), BLOCK_SIZE));
						//drawBox(pos + WCoord(0, 0, 0), pos + WCoord(BLOCK_SIZE - int(movesize * i * BLOCK_SIZE), int(heightOffset * i * size), BLOCK_SIZE));
					}
				}
				else if (dir == 2)
				{
					for (int i = 0; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(0, 0, int(movesize * i * BLOCK_SIZE)), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * size+countBox[i]), BLOCK_SIZE));
						//drawBox(pos + WCoord(0, 0, int(movesize * i * BLOCK_SIZE)), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * size), BLOCK_SIZE));
					}
				}
				else if (dir == 3)
				{
					for (int i = 0; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(0, 0, 0), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * size + countBox[i]), BLOCK_SIZE - int(movesize * i * BLOCK_SIZE)));
						//drawBox(pos + WCoord(0, 0, 0), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * size), BLOCK_SIZE - int(movesize * i * BLOCK_SIZE)));
					}
				}
			}
			else
			{
				int countBox[10] = { -2,5,12,20,25,25,25,25,12,8 };
				if (dir == 0)
				{
					for (int i = 0; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(BLOCK_SIZE - int(movesize * (i + 1) * size), int(heightOffset * i * BLOCK_SIZE- countBox[i]), 0), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
						//drawBox(pos + WCoord(BLOCK_SIZE - int(movesize * i * size), int(heightOffset * i * BLOCK_SIZE), 0), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
					}
				}
				else if (dir == 1)
				{
					for (int i = 0; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(0, int(heightOffset * i * BLOCK_SIZE- countBox[i]), 0), pos + WCoord(int(movesize * (i + 1) * size), BLOCK_SIZE, BLOCK_SIZE));
						//drawBox(pos + WCoord(0, int(heightOffset * i * BLOCK_SIZE), 0), pos + WCoord(int(movesize * i * size), BLOCK_SIZE, BLOCK_SIZE));
					}
				}
				else if (dir == 2)
				{
					for (int i = 0; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(0, int(heightOffset * i * BLOCK_SIZE- countBox[i]), BLOCK_SIZE - int(movesize * (i + 1) * size)), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
						//drawBox(pos + WCoord(0, int(heightOffset * i * BLOCK_SIZE), BLOCK_SIZE - int(movesize * i * size)), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
					}
				}
				else if (dir == 3)
				{
					for (int i = 0; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(0, int(heightOffset * i * BLOCK_SIZE- countBox[i]), 0), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, int(movesize * (i + 1) * size)));
						//drawBox(pos + WCoord(0, int(heightOffset * i * BLOCK_SIZE), 0), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, int(movesize * i * size)));
					}
				}
			}
		}
		else if (1 == warp)
		{
			if (blockheight > 0)
			{
				int countBox[10] = { 10,25,35,40,40,35,30,20,15,5 };
				if (dir == 0 && turnDir == 2 || dir == 2 && turnDir == 0)
				{
					for (int i = 0; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(int(movesize * i * BLOCK_SIZE), 0, int(movesize * i * BLOCK_SIZE)), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * size+ countBox[i]), BLOCK_SIZE));
					}
				}
				else if (dir == 0 && turnDir == 3 || dir == 3 && turnDir == 0)
				{
					for (int i = 0; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(int(movesize * i * BLOCK_SIZE), 0, 0), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * size+ countBox[i]), BLOCK_SIZE - int(movesize * i * BLOCK_SIZE)));
					}
				}
				else if (dir == 1 && turnDir == 2 || dir == 2 && turnDir == 1)
				{
					for (int i = 0; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(0, 0, int(movesize * i * BLOCK_SIZE)), pos + WCoord(BLOCK_SIZE - int(movesize * i * BLOCK_SIZE), int(heightOffset * i * size + countBox[i]), BLOCK_SIZE));
					}
				}
				else if (dir == 1 && turnDir == 3 || dir == 3 && turnDir == 1)
				{
					for (int i = 0; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(0, 0, 0), pos + WCoord(BLOCK_SIZE - int(movesize * i * BLOCK_SIZE), int(heightOffset * i * size+ countBox[i]), BLOCK_SIZE - int(movesize * i * BLOCK_SIZE)));
					}
				}
			}
			else
			{
				int countBox[10] = { -2,5,12,20,25,25,25,25,12,8 };
				if (dir == 0 && turnDir == 2 || dir == 2 && turnDir == 0)
				{
					for (int i = 0; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(BLOCK_SIZE - int(movesize * (i + 1) * size), int(heightOffset * i * BLOCK_SIZE- countBox[i]), BLOCK_SIZE - int(movesize * (i + 1) * size)), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
						//drawBox(pos + WCoord(BLOCK_SIZE - int(movesize * i * size), int(heightOffset * i * BLOCK_SIZE), BLOCK_SIZE - int(movesize * i * size)), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
					}
				}
				else if (dir == 0 && turnDir == 3 || dir == 3 && turnDir == 0)
				{
					for (int i = 0; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(BLOCK_SIZE - int(movesize * (i + 1) * size), int(heightOffset * i * BLOCK_SIZE- countBox[i]), 0), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, int(movesize * (i + 1) * size)));
						//drawBox(pos + WCoord(BLOCK_SIZE - int(movesize * i * size), int(heightOffset * i * BLOCK_SIZE), 0), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, int(movesize * i * size)));
					}
				}
				else if (dir == 1 && turnDir == 2 || dir == 2 && turnDir == 1)
				{
					for (int i = 0; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(0, int(heightOffset * i * BLOCK_SIZE- countBox[i]), BLOCK_SIZE - int(movesize * (i + 1) * size)), pos + WCoord(int(movesize * (i + 1) * size), BLOCK_SIZE, BLOCK_SIZE));
						//drawBox(pos + WCoord(0, int(heightOffset * i * BLOCK_SIZE), BLOCK_SIZE - int(movesize * i * size)), pos + WCoord(int(movesize * i * size), BLOCK_SIZE, BLOCK_SIZE));
					}
				}
				else if (dir == 1 && turnDir == 3 || dir == 3 && turnDir == 1)
				{
					for (int i = 0; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(0, int(heightOffset * i * BLOCK_SIZE- countBox[i]), 0), pos + WCoord(int(movesize * (i + 1) * size), BLOCK_SIZE, int(movesize * (i + 1) * size)));
						//drawBox(pos + WCoord(0, int(heightOffset * float(i) * BLOCK_SIZE), 0), pos + WCoord(int(movesize * (i)*size), BLOCK_SIZE, int(movesize * i * size)));
					}
				}
			}
		}
		else
		{
			if (blockheight > 0)
			{
				int countBox[10] = { 10,25,35,40,40,35,30,20,15,5 };
				if (dir == 0 && turnDir == 2 || dir == 2 || turnDir == 0)
				{
					for (int i = 0; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(int(movesize * i * BLOCK_SIZE), 0, 0), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * size+ countBox[i]), BLOCK_SIZE));
						//drawBox(pos + WCoord(int(movesize * i * BLOCK_SIZE), 0, 0), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * size), BLOCK_SIZE));
						coldetect->addObstacle(pos + WCoord(0, 0, int(movesize * i * BLOCK_SIZE)), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * size + countBox[i]), BLOCK_SIZE));
						//drawBox(pos + WCoord(0, 0, int(movesize * i * BLOCK_SIZE)), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * size), BLOCK_SIZE));
					}
				}
				else if (dir == 0 && turnDir == 3 || dir == 3 && turnDir == 0)
				{
					for (int i = 0; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(int(movesize * i * BLOCK_SIZE), 0, 0), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * size + countBox[i]), BLOCK_SIZE));
						//drawBox(pos + WCoord(int(movesize * i * BLOCK_SIZE), 0, 0), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * size), BLOCK_SIZE));
						coldetect->addObstacle(pos + WCoord(0, 0, 0), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * size + countBox[i]), BLOCK_SIZE - int(movesize * i * BLOCK_SIZE)));
						//drawBox(pos + WCoord(0, 0, 0), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * size), BLOCK_SIZE - int(movesize * i * BLOCK_SIZE)));
					}
				}
				else if (dir == 1 && turnDir == 2 || dir == 2 || turnDir == 1)
				{
					for (int i = 0; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(0, 0, 0), pos + WCoord(BLOCK_SIZE - int(movesize * i * BLOCK_SIZE), int(heightOffset * i * size + countBox[i]), BLOCK_SIZE));
						//drawBox(pos + WCoord(0, 0, 0), pos + WCoord(BLOCK_SIZE - int(movesize * i * BLOCK_SIZE), int(heightOffset * i * size), BLOCK_SIZE));
						coldetect->addObstacle(pos + WCoord(0, 0, int(movesize * i * BLOCK_SIZE)), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * size + countBox[i]), BLOCK_SIZE));
						//drawBox(pos + WCoord(0, 0, int(movesize * i * BLOCK_SIZE)), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * size), BLOCK_SIZE));
					}
				}
				else if (dir == 1 && turnDir == 3 || dir == 3 && turnDir == 1)
				{
					for (int i = 0; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(0, 0, 0), pos + WCoord(BLOCK_SIZE - int(movesize * i * BLOCK_SIZE), int(heightOffset * i * size + countBox[i]), BLOCK_SIZE));
						//drawBox(pos + WCoord(0, 0, 0), pos + WCoord(BLOCK_SIZE - int(movesize * i * BLOCK_SIZE), int(heightOffset * i * size), BLOCK_SIZE));
						coldetect->addObstacle(pos + WCoord(0, 0, 0), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * size + countBox[i]), BLOCK_SIZE - int(movesize * i * BLOCK_SIZE)));
						//drawBox(pos + WCoord(0, 0, 0), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * size), BLOCK_SIZE - int(movesize * i * BLOCK_SIZE)));
					}
				}

			}
			else
			{
				int countBox[10] = { -2,5,12,20,25,25,25,25,12,8 };
				if (dir == 0 && turnDir == 2 || dir == 2 || turnDir == 0)
				{
					for (int i = 1; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(BLOCK_SIZE - int(movesize * i * size), int(heightOffset * i * BLOCK_SIZE - countBox[i]), 0), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
						//drawBox(pos + WCoord(BLOCK_SIZE - int(movesize * i * size), int(heightOffset * i * BLOCK_SIZE), 0), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
						coldetect->addObstacle(pos + WCoord(0, int(heightOffset * i * BLOCK_SIZE - countBox[i]), BLOCK_SIZE - int(movesize * i * size)), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
						//drawBox(pos + WCoord(0, int(heightOffset * i * BLOCK_SIZE), BLOCK_SIZE - int(movesize * i * size)), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
					}
				}
				else if (dir == 0 && turnDir == 3 || dir == 3 && turnDir == 0)
				{
					for (int i = 1; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(BLOCK_SIZE - int(movesize * i * size), int(heightOffset * i * BLOCK_SIZE - countBox[i]), 0), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
						//drawBox(pos + WCoord(BLOCK_SIZE - int(movesize * i * size), int(heightOffset * i * BLOCK_SIZE), 0), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
						coldetect->addObstacle(pos + WCoord(0, int(heightOffset * i * BLOCK_SIZE - countBox[i]), 0), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, int(movesize * i * size)));
						//drawBox(pos + WCoord(0, int(heightOffset * i * BLOCK_SIZE), 0), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, int(movesize * i * size)));
					}
				}
				else if (dir == 1 && turnDir == 2 || dir == 2 || turnDir == 1)
				{
					for (int i = 1; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(0, int(heightOffset * i * BLOCK_SIZE - countBox[i]), 0), pos + WCoord(int(movesize * i * size), BLOCK_SIZE, BLOCK_SIZE));
						//drawBox(pos + WCoord(0, int(heightOffset * i * BLOCK_SIZE), 0), pos + WCoord(int(movesize * i * size), BLOCK_SIZE, BLOCK_SIZE));
						coldetect->addObstacle(pos + WCoord(0, int(heightOffset * i * BLOCK_SIZE - countBox[i]), BLOCK_SIZE - int(movesize * i * size)), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
						//drawBox(pos + WCoord(0, int(heightOffset * i * BLOCK_SIZE), BLOCK_SIZE - int(movesize * i * size)), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
					}
				}
				else if (dir == 1 && turnDir == 3 || dir == 3 && turnDir == 1)
				{
					for (int i = 1; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(0, int(heightOffset * i * BLOCK_SIZE - countBox[i]), 0), pos + WCoord(int(movesize * i * size), BLOCK_SIZE, BLOCK_SIZE));
						//drawBox(pos + WCoord(0, int(heightOffset * i * BLOCK_SIZE), 0), pos + WCoord(int(movesize * i * size), BLOCK_SIZE, BLOCK_SIZE));
						coldetect->addObstacle(pos + WCoord(0, int(heightOffset * i * BLOCK_SIZE - countBox[i]), 0), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, int(movesize * i * size)));
						//drawBox(pos + WCoord(0, int(heightOffset * i * BLOCK_SIZE), 0), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, int(movesize * i * size)));
					}
				}
			}
		}
	}
}

bool ArcPlateMaterial::coverNeighbor(int curblockdata, SolidBlockMaterial* neighbor, int neighbor_data, DirectionType dir)
{
	if (curblockdata & 8)
	{
		if (dir == DIR_NEG_Y)
		{
			if (neighbor && neighbor->GetAttrRenderType() == BLOCKRENDER_FLUID && neighbor_data) return false;
		}
		return true;
	}
	else
	{
		int curDir = curblockdata & 3;
		int updown = curblockdata & 4;
		if (ReverseDirection(curDir) == dir || (updown + 4) == dir)
		{
			int updown = (curblockdata & 4) >> 2;
			if ((updown + 4) == dir)
			{
				if (dir == DIR_NEG_Y)
				{
					if (neighbor && neighbor->GetAttrRenderType() == BLOCKRENDER_FLUID && neighbor_data) return false;
				}
				return true;
			}
		}
	}
	return false;
}

bool ArcPlateMaterial::canAttachedToMecha(int curblockdata, DirectionType dir, int neighbor_id, int neighbor_data)
{
	/*	if(dir == DIR_NEG_Y)
		{
			return curblockdata != 1;
		}
		else if(dir == DIR_POS_Y)
		{
			return curblockdata != 0;
		}
		else */return true;
}

void ArcPlateMaterial::dropBlockAsItem(World* pworld, const WCoord& blockpos, int blockdata, BLOCK_MINE_TYPE droptype, float chance, int uin/* = -1 */)
{
	CubeBlockMaterial::dropBlockAsItem(pworld, blockpos, blockdata, droptype, chance, uin);

	if (blockdata & 8)
	{
		CubeBlockMaterial::dropBlockAsItem(pworld, blockpos, blockdata, droptype, chance, uin);
	}
}

int ArcPlateMaterial::getPhisicMeshVerts(BaseSection* psection, const WCoord& blockpos, dynamic_array<Rainbow::Vector3f>& verts, dynamic_array<UInt16>& idxs)
{
#ifdef IWORLD_SERVER_BUILD
	if (!m_mPhyModel.size())
	{
		initVertData();
	}
#endif	
	int blockdata = psection->getBlock(blockpos).getData();
	if (blockdata < 8)
	{
		auto pblock = psection->getBlock(blockpos);
		int warp = -1;
		int turnDir = -1;
		DirectionType curDir = DirectionType(blockdata & 3);
		auto frontBlock = psection->getNeighborBlock(blockpos, g_DirectionCoord[curDir]);
		if (pblock.getResID() == frontBlock.getResID() && frontBlock.getData() < 8 && ((pblock.getData() & 4) == (frontBlock.getData() & 4)))
		{
			int frontDir = frontBlock.getData() & 3;
			if (frontDir != curDir && frontDir != ReverseDirection(curDir))
			{
				warp = 0;
				turnDir = frontDir;
			}
		}
		if (warp == -1)
		{
			auto backBlock = psection->getNeighborBlock(blockpos, g_DirectionCoord[ReverseDirection(curDir)]);
			if (pblock.getResID() == backBlock.getResID() && backBlock.getData() < 8 && ((pblock.getData() & 4) == (backBlock.getData() & 4)))
			{
				int backDir = backBlock.getData() & 3;
				if (backDir != curDir && backDir != ReverseDirection(curDir))
				{
					warp = 1;
					turnDir = backDir;
				}
			}
		}
		if (warp == -1)
		{
			if (m_mPhyModel.find(blockdata) != m_mPhyModel.end())
			{
				ArcPhyModel* pTag = &m_mPhyModel[blockdata];
				verts = pTag->verts;
				idxs = pTag->idxs;
				return  pTag->ArcCount;
			}
		}
		else
		{
			int downUp = (blockdata & 4) ? 1 : 0;
			if (m_mPhyModel.find(10000 + downUp * 1000 + warp * 100 + curDir * 10 + turnDir) != m_mPhyModel.end())
			{
				ArcPhyModel* pTag = &m_mPhyModel[10000 + downUp * 1000 + warp * 100 + curDir * 10 + turnDir];
				verts = pTag->verts;
				idxs = pTag->idxs;
				return  pTag->ArcCount;
			}
		}
	}
	return 0;
}

BLOCK_RENDERTYPE_T ArcPlateMaterial::GetAttrRenderType() const
{
	return BLOCKRENDER_MODEL;
}

void ArcPlateMaterial::onBlockPlacedBy(World* pworld, const WCoord& blockpos, IClientPlayer* player)
{
	int dir = player->GetPlayerCurPlaceDir();
	float x, y, z;
	player->GetPlayerFaceDir(x, y, z);
	int data = dir;
	if (y > 0)
	{
		data += 4;
	}
	pworld->setBlockData(blockpos, data);
}



void ArcPlateMaterial::createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh)
{
	auto psection = data.m_SharedSectionData;
#ifndef IWORLD_SERVER_BUILD
	FaceVertexLight faceVertexLight;
	//float block_light[16] = { 0 };
	Block pblock = psection->getBlock(blockpos);
	int curblockdata = pblock.getData();
	DirectionType curDir = DirectionType(curblockdata & 3);

	float blockheight = getBlockHeight(curblockdata);
	DirectionType specialdir = DIR_NOT_INIT;
	if (blockheight > 0 && blockheight < 1.0f) specialdir = DIR_POS_Y;
	else if (blockheight<0 && blockheight>-1.0f) specialdir = DIR_NEG_Y;

	
	const BiomeDef* biome = GetDefManagerProxy()->getBiomeDef(1);
	std::vector<int> wholeFace;
	std::vector<int> triangleFace;
	std::vector<int> slantFace;
	// 	dynamic_array<Vector4f> turnslantFace;
	dynamic_array<int> turnslantFace;
	if (curblockdata & 8)
	{
		CubeBlockMaterial::createBlockMesh(data, blockpos, poutmesh);
		return;
		// 		for (int ii = 0; ii < 6; ii++)
		// 		{
		// 			wholeFace.push_back(ii);
		// 		}
	}
	else
	{
		int warp = -1;
		int turnDir = -1;
		auto frontBlock = psection->getNeighborBlock(blockpos, g_DirectionCoord[curDir]);
		if (pblock.getResID() == frontBlock.getResID() && frontBlock.getData() < 8 && ((pblock.getData() & 4) == (frontBlock.getData() & 4)))
		{
			int frontDir = frontBlock.getData() & 3;
			if (frontDir != curDir && frontDir != ReverseDirection(curDir)/* && psection->getNeighborBlock(blockpos, g_DirectionCoord[frontDir]) != pblock*/)
			{
				warp = 0;
				turnDir = frontDir;
			}
		}

		if (warp == -1)
		{
			auto backBlock = psection->getNeighborBlock(blockpos, g_DirectionCoord[ReverseDirection(curDir)]);
			if (pblock.getResID() == backBlock.getResID() && backBlock.getData() < 8 && ((pblock.getData() & 4) == (backBlock.getData() & 4)))
			{
				int backDir = backBlock.getData() & 3;
				if (backDir != curDir && backDir != ReverseDirection(curDir)/* && psection->getNeighborBlock(blockpos, g_DirectionCoord[backDir]) != pblock*/)
				{
					warp = 1;
					turnDir = backDir;
				}
			}
		}

		if (specialdir == DIR_POS_Y)
		{
			wholeFace.push_back(4);
		}
		else
		{
			wholeFace.push_back(5);
		}
		if (warp == -1)
		{
			wholeFace.push_back(ReverseDirection(curDir));
		}

		if (curDir == DIR_NEG_X)
		{
			if (specialdir == DIR_POS_Y)
			{
				if (warp == -1)
				{
					triangleFace.push_back(6);//4+2
					triangleFace.push_back(7);//4+3
					slantFace.push_back(0);
				}
				else
				{
					if (1 == warp)
					{
						triangleFace.push_back(ReverseDirection(turnDir) + 4);//4+3
						triangleFace.push_back(ReverseDirection(curDir) + (1 - (turnDir % 2)) * 4);
					}
					else
					{
						// 						triangleFace.push_back(turnDir + 4);//4+3
						triangleFace.push_back(turnDir + (curDir % 2 ? 0 : 4));
						triangleFace.push_back(curDir + (turnDir % 2 ? 0 : 4));
						wholeFace.push_back(ReverseDirection(curDir));
						wholeFace.push_back(ReverseDirection(turnDir));
					}
					turnslantFace.push_back(warp * 100 + curDir * 10 + turnDir);
					turnslantFace.push_back(warp * 100 + turnDir * 10 + curDir);
				}
			}
			else
			{
				if (warp == -1)
				{
					triangleFace.push_back(14);//3*4+2
					triangleFace.push_back(15);//3*4+3
					slantFace.push_back(4);
				}
				else
				{
					if (1 == warp)
					{
						triangleFace.push_back(ReverseDirection(turnDir) + 12);//3*4+3
						triangleFace.push_back(ReverseDirection(curDir) + (1 - (turnDir % 2)) * 4 + 8);
					}
					else
					{
						triangleFace.push_back(turnDir + 8 + (curDir % 2 ? 0 : 4));
						triangleFace.push_back(curDir + 8 + (turnDir % 2 ? 0 : 4));
						// 						triangleFace.push_back(turnDir + 12);//3*4+3
						wholeFace.push_back(ReverseDirection(curDir));
						wholeFace.push_back(ReverseDirection(turnDir));
					}
					// 					triangleFace.push_back(ReverseDirection(curDir) + 12);
					turnslantFace.push_back(1000 + warp * 100 + curDir * 10 + turnDir);
					turnslantFace.push_back(1000 + warp * 100 + turnDir * 10 + curDir);
				}
			}
		}
		else if (curDir == DIR_POS_X)
		{
			if (specialdir == DIR_POS_Y)
			{
				if (warp == -1)
				{
					triangleFace.push_back(2);
					triangleFace.push_back(3);
					slantFace.push_back(1);
				}
				else
				{
					if (1 == warp)
					{
						triangleFace.push_back(ReverseDirection(turnDir));
						triangleFace.push_back(ReverseDirection(curDir) + (1 - (turnDir % 2)) * 4);
					}
					else
					{
						// 						triangleFace.push_back(turnDir);
						triangleFace.push_back(turnDir + (curDir % 2 ? 0 : 4));
						triangleFace.push_back(curDir + (turnDir % 2 ? 0 : 4));
						wholeFace.push_back(ReverseDirection(curDir));
						wholeFace.push_back(ReverseDirection(turnDir));
					}
					// 					triangleFace.push_back(ReverseDirection(curDir));
					turnslantFace.push_back(warp * 100 + curDir * 10 + turnDir);
					turnslantFace.push_back(warp * 100 + turnDir * 10 + curDir);
				}
			}
			else
			{
				if (warp == -1)
				{
					triangleFace.push_back(10);//2*4+2
					triangleFace.push_back(11);//2*4+3
					slantFace.push_back(5);
				}
				else
				{
					if (1 == warp)
					{
						triangleFace.push_back(ReverseDirection(turnDir) + 8);//2*4+3
						triangleFace.push_back(ReverseDirection(curDir) + (1 - (turnDir % 2)) * 4 + 8);
					}
					else
					{
						// 						triangleFace.push_back(turnDir + 8);//2*4+3
						triangleFace.push_back(turnDir + 8 + (curDir % 2 ? 0 : 4));
						triangleFace.push_back(curDir + 8 + (turnDir % 2 ? 0 : 4));
						wholeFace.push_back(ReverseDirection(curDir));
						wholeFace.push_back(ReverseDirection(turnDir));
					}
					// 					triangleFace.push_back(ReverseDirection(curDir) + 8);
					turnslantFace.push_back(1000 + warp * 100 + curDir * 10 + turnDir);
					turnslantFace.push_back(1000 + warp * 100 + turnDir * 10 + curDir);
				}
			}
		}
		else if (curDir == DIR_NEG_Z)
		{
			if (specialdir == DIR_POS_Y)
			{
				if (warp == -1)
				{
					triangleFace.push_back(4);
					triangleFace.push_back(5);
					slantFace.push_back(2);
				}
				else
				{
					if (1 == warp)
					{
						triangleFace.push_back(ReverseDirection(turnDir) + 4);
						triangleFace.push_back(ReverseDirection(curDir) + (1 - (turnDir % 2)) * 4);
					}
					else
					{
						// 						triangleFace.push_back(turnDir + 4);
						triangleFace.push_back(turnDir + (curDir % 2 ? 0 : 4));
						triangleFace.push_back(curDir + (turnDir % 2 ? 0 : 4));
						wholeFace.push_back(ReverseDirection(curDir));
						wholeFace.push_back(ReverseDirection(turnDir));
					}
					// 					triangleFace.push_back(ReverseDirection(curDir));
					turnslantFace.push_back(warp * 100 + curDir * 10 + turnDir);
					turnslantFace.push_back(warp * 100 + turnDir * 10 + curDir);
				}
			}
			else
			{
				if (warp == -1)
				{
					triangleFace.push_back(12);//3*4+0
					triangleFace.push_back(13);//3*4+1
					slantFace.push_back(6);
				}
				else
				{
					if (1 == warp)
					{
						triangleFace.push_back(ReverseDirection(turnDir) + 12);//3*4+1
						triangleFace.push_back(ReverseDirection(curDir) + (1 - (turnDir % 2)) * 4 + 8);
					}
					else
					{
						// 						triangleFace.push_back(turnDir + 12);
						triangleFace.push_back(turnDir + 8 + (curDir % 2 ? 0 : 4));
						triangleFace.push_back(curDir + 8 + (turnDir % 2 ? 0 : 4));
						wholeFace.push_back(ReverseDirection(curDir));
						wholeFace.push_back(ReverseDirection(turnDir));
					}
					// 					triangleFace.push_back(ReverseDirection(curDir) + 12);
					turnslantFace.push_back(1000 + warp * 100 + curDir * 10 + turnDir);
					turnslantFace.push_back(1000 + warp * 100 + turnDir * 10 + curDir);
				}
			}
		}
		else if (curDir == DIR_POS_Z)
		{
			if (specialdir == DIR_POS_Y)
			{
				if (warp == -1)
				{
					triangleFace.push_back(0);
					triangleFace.push_back(1);
					slantFace.push_back(3);
				}
				else
				{
					if (1 == warp)
					{
						triangleFace.push_back(ReverseDirection(turnDir));
						triangleFace.push_back(ReverseDirection(curDir) + (1 - (turnDir % 2)) * 4);
					}
					else
					{
						// 						triangleFace.push_back(turnDir);
						triangleFace.push_back(turnDir + (curDir % 2 ? 0 : 4));
						triangleFace.push_back(curDir + (turnDir % 2 ? 0 : 4));
						wholeFace.push_back(ReverseDirection(curDir));
						wholeFace.push_back(ReverseDirection(turnDir));
					}
					// 					triangleFace.push_back(ReverseDirection(curDir));
					turnslantFace.push_back(warp * 100 + curDir * 10 + turnDir);
					turnslantFace.push_back(warp * 100 + turnDir * 10 + curDir);
				}
			}
			else
			{
				if (warp == -1)
				{
					triangleFace.push_back(8);//2*4+0
					triangleFace.push_back(9);//2*4+1
					slantFace.push_back(7);
				}
				else
				{
					if (1 == warp)
					{
						triangleFace.push_back(ReverseDirection(turnDir) + 8);//2*4+1
						triangleFace.push_back(ReverseDirection(curDir) + (1 - (turnDir % 2)) * 4 + 8);
					}
					else
					{
						// 						triangleFace.push_back(turnDir + 8);
						triangleFace.push_back(turnDir + 8 + (curDir % 2 ? 0 : 4));
						triangleFace.push_back(curDir + 8 + (turnDir % 2 ? 0 : 4));
						wholeFace.push_back(ReverseDirection(curDir));
						wholeFace.push_back(ReverseDirection(turnDir));
					}
					// 					triangleFace.push_back(ReverseDirection(curDir) + 8);
					turnslantFace.push_back(1000 + warp * 100 + curDir * 10 + turnDir);
					turnslantFace.push_back(1000 + warp * 100 + turnDir * 10 + curDir);
				}
			}
		}
	}
	BlockColor facecolor(255, 255, 255, 0);
	for (auto& d : wholeFace)
	{
		DirectionType dir = (DirectionType)d;
		// 		if (m_DisableCoverFaceOpt || dir == specialdir || !psection->getNeighborCover(blockpos, this, curblockdata, dir))
		{
			bool flipQuad = psection->getCubeFaceVertexLight(blockpos, dir, faceVertexLight);
			dynamic_array<UInt16>* indices = /*flipQuad ? &m_dNegIndices : */m_dPosIndices;
			RenderBlockMaterial* pmtl = getFaceMtl(biome, dir, pblock.getData(), facecolor);
			if (pmtl == NULL)
				continue;
			SectionSubMesh* psubmesh = poutmesh->getSubMesh(pmtl);
			const float* uvtile = nullptr;
			if (psubmesh && !psubmesh->IsIgnoreTileUV())
				uvtile = pmtl->getUVTile();


			BlockGeomMeshInfo mesh;

			// 			mesh.vertices = vertices;
			mesh.vertices = m_mArcWholeFace()[d];
			mesh.indices = *indices;
			unsigned int avelt[4];
			getAvelt(data, blockpos, dir, avelt);
			for (int n = 0; n < m_mArcWholeFace()[d].size(); n++)
			{
				auto& vert = mesh.vertices[n];
				InitBlockVertLight(vert, avelt[n], uvtile);
			}
			if (psubmesh)
				psubmesh->addGeomFaceLight(mesh, &blockpos, faceVertexLight, &facecolor, pmtl->getUVTile());
		}
	}
	for (auto& d : triangleFace)
	{
		DirectionType dir = (DirectionType)(d % 4);
		bool flipQuad = psection->getCubeFaceVertexLight(blockpos, dir, faceVertexLight);
		dynamic_array<UInt16>* indices = /*flipQuad ? &m_NegTrIndices : */m_PosDiamondIndices;
		RenderBlockMaterial* pmtl = getFaceMtl(biome, dir, pblock.getData(), facecolor);
		if (pmtl == NULL)
			continue;
		SectionSubMesh* psubmesh = poutmesh->getSubMesh(pmtl);
		const float* uvtile = nullptr;
		if (psubmesh && !psubmesh->IsIgnoreTileUV())
			uvtile = pmtl->getUVTile();

		BlockGeomMeshInfo mesh;

		mesh.vertices = m_mDiamondFace[d];

		mesh.indices = *indices;
		for (int n = 0; n < mesh.vertices.size(); n++)
		{
			auto& vert = mesh.vertices[n];
			int aveltMe = 0;
			int aveltNeight[3] = { 0 };
			int avelt = 0;
			if (vert.pos.y > 0 && specialdir == DIR_POS_Y || vert.pos.y == 0 && specialdir == DIR_NEG_Y)
			{
				auto selfPos = blockpos + g_DirectionCoord[specialdir] + g_DirectionCoord[dir];
				bool isXdir = dir < 2;
				int xdir = isXdir ? dir : vert.pos.x == 0 ? 0 : 1;
				int zdir = !isXdir ? dir : vert.pos.z == 0 ? 2 : 3;
				aveltMe = psection->getLight2(selfPos, true);
				aveltNeight[0] = psection->getLight2(selfPos + g_DirectionCoord[xdir], true);
				aveltNeight[1] = psection->getLight2(selfPos + g_DirectionCoord[zdir], true);
				aveltNeight[2] = psection->getLight2(selfPos + g_DirectionCoord[xdir] + g_DirectionCoord[zdir], true);
				avelt = ((aveltMe + aveltNeight[0] + aveltNeight[1] + aveltNeight[2]) >> 2) & 0xff00ff;
			}
			else
			{
				auto selfPos = blockpos + g_DirectionCoord[dir];
				int sideDir = specialdir == DIR_NEG_Y ? DIR_POS_Y : DIR_NEG_Y;
				aveltMe = psection->getLight2(selfPos, true);
				aveltNeight[0] = psection->getLight2(selfPos + g_DirectionCoord[sideDir], true);
				avelt = ((aveltMe + aveltNeight[0]) >> 1) & 0xff00ff;
			}
			InitBlockVertLight(vert, avelt, uvtile);
		}
		//if (psubmesh)
			//psubmesh->addGeomFaceLight(mesh, &blockpos, faceVertexLight, &facecolor, pmtl->getUVTile());
		if (psubmesh) psubmesh->addGeomFace(mesh, &blockpos);// addGeomFaceLight(mesh, &blockpos, faceVertexLight, &facecolor, pmtl->getUVTile());
	}
	for (auto& d : turnslantFace)
	{
		// 		DirectionType dir = (DirectionType)d.z;
		DirectionType dir = (DirectionType)((d / 10) % 10);
		bool flipQuad = psection->getFaceVertexLight(blockpos, dir, faceVertexLight);
		FaceVertexLight faceUpDownVertexLight;
		psection->getFaceVertexLight(blockpos, specialdir, faceUpDownVertexLight);
		for (int i = 0; i < 4; i++)
		{
			if (specialdir == DIR_NEG_Y)
			{
				faceVertexLight.m_Light[i] = (3 * faceVertexLight.m_Light[i] + (faceUpDownVertexLight.m_Light[i])) / 4;
				faceVertexLight.m_AmbientOcclusion[i] = (3 * faceVertexLight.m_AmbientOcclusion[i] + faceUpDownVertexLight.m_AmbientOcclusion[i]) / 4;
			}
			else
			{
				faceVertexLight.m_Light[i] = (faceVertexLight.m_Light[i] + (2 * faceUpDownVertexLight.m_Light[i])) / 3;
				faceVertexLight.m_AmbientOcclusion[i] = (faceVertexLight.m_AmbientOcclusion[i] + 2 * faceUpDownVertexLight.m_AmbientOcclusion[i]) / 3;
			}
		}

		dynamic_array<UInt16>* indices = /*flipQuad ? &m_NegTrIndices : */m_TurnArcIndices;
		RenderBlockMaterial* pmtl = getFaceMtl(biome, dir, pblock.getData(), facecolor);
		if (pmtl == NULL)
			continue;
		SectionSubMesh* psubmesh = poutmesh->getSubMesh(pmtl);
		const float* uvtile = nullptr;
		if (psubmesh && !psubmesh->IsIgnoreTileUV())
			uvtile = pmtl->getUVTile();

		BlockGeomMeshInfo mesh;

		mesh.vertices = m_mTurnArcFace[d];
		mesh.indices = *indices;
		//unsigned short dir_color1 = TriangleNormal2LightColor(g_DirectionCoord[dir].toVector3());
		//unsigned short dir_color2 = TriangleNormal2LightColor(g_DirectionCoord[specialdir].toVector3());
		BlockColor vertcolor = Rainbow::ColorRGBA32::white;
		//vertcolor.v = 0xffffffff;
		vertcolor.a = 0;// (dir_color1 + 2 * dir_color2) / 3;
		//if (specialdir == DIR_NEG_Y)
		//{
		//	vertcolor.a = (3 * dir_color1 + dir_color2) / 4;
		//}
		for (int n = 0; n < m_mTurnArcFace[d].size(); n++)
		{
			auto& vert = mesh.vertices[n];
			int aveltMe = 0;
			int aveltNeight[3] = { 0 };
			int avelt = 0;
			if (vert.pos.y > 0 && specialdir == DIR_POS_Y || vert.pos.y == 0 && specialdir == DIR_NEG_Y)
			{
				auto selfPos = blockpos + g_DirectionCoord[specialdir];
				bool isXdir = dir < 2;
				int xdir = isXdir ? dir : vert.pos.x == 0 ? 0 : 1;
				int zdir = !isXdir ? dir : vert.pos.z == 0 ? 2 : 3;
				aveltMe = psection->getLight2(selfPos, true);
				aveltNeight[0] = psection->getLight2(selfPos + g_DirectionCoord[xdir], true);
				aveltNeight[1] = psection->getLight2(selfPos + g_DirectionCoord[zdir], true);
				aveltNeight[2] = psection->getLight2(selfPos + g_DirectionCoord[xdir] + g_DirectionCoord[zdir], true);
				avelt = ((aveltMe + aveltNeight[0] + aveltNeight[1] + aveltNeight[2]) >> 2) & 0xff00ff;
			}
			else
			{
				auto selfPos = blockpos + g_DirectionCoord[dir];
				int xdir = vert.pos.x == 0 ? 0 : 1;
				int zdir = vert.pos.z == 0 ? 2 : 3;
				int sideDir = dir > 1 ? xdir : zdir;
				aveltMe = psection->getLight2(selfPos, true);
				aveltNeight[0] = psection->getLight2(selfPos + g_DirectionCoord[sideDir], true);
				avelt = ((aveltMe + aveltNeight[0]) >> 1) & 0xff00ff;
			}
			vert.color = vertcolor;
			InitBlockVertLight(vert, avelt, uvtile);
			//int lt1 = (((avelt >> 4) & 0xf) * vertcolor.a) >> 5;
			//int lt2 = (((avelt >> 20) & 0xf) * vertcolor.a) >> 5;
			//vert.pos.w = (lt1 << 8) | lt2;
			//vert.color = vertcolor;
		}
		if (psubmesh) psubmesh->addGeomFace(mesh, &blockpos);// addGeomFaceLight(mesh, &blockpos, faceVertexLight, &facecolor, pmtl->getUVTile());
	}
	for (auto& d : slantFace)
	{
		//down
		DirectionType dir = (DirectionType)curDir;
		FaceVertexLight faceUpDownVertexLight;
		bool flipQuad = psection->getFaceVertexLight(blockpos, dir, faceVertexLight);
		psection->getFaceVertexLight(blockpos, specialdir, faceUpDownVertexLight);
		for (int i = 0; i < 4; i++)
		{
			if (specialdir == DIR_NEG_Y)
			{
				faceVertexLight.m_Light[i] = (3*faceVertexLight.m_Light[i] + ( faceUpDownVertexLight.m_Light[i])) / 4;
				faceVertexLight.m_AmbientOcclusion[i] = (3*faceVertexLight.m_AmbientOcclusion[i] + faceUpDownVertexLight.m_AmbientOcclusion[i]) / 4;
			}
			else
			{
				faceVertexLight.m_Light[i] = (faceVertexLight.m_Light[i] + (2 * faceUpDownVertexLight.m_Light[i])) / 3;
				faceVertexLight.m_AmbientOcclusion[i] = (faceVertexLight.m_AmbientOcclusion[i] + 2*faceUpDownVertexLight.m_AmbientOcclusion[i]) / 3;
			}	
		}

		dynamic_array<UInt16>* indices = /*flipQuad ? &m_dNegIndices : */m_PosBigArcIndices;

		RenderBlockMaterial* pmtl = getFaceMtl(biome, dir, pblock.getData(), facecolor);
		if (pmtl == NULL)
			continue;
		SectionSubMesh* psubmesh = poutmesh->getSubMesh(pmtl);

		const float* uvtile = nullptr;
		if (psubmesh && !psubmesh->IsIgnoreTileUV())
			uvtile = pmtl->getUVTile();

		BlockGeomMeshInfo mesh;

		// 		mesh.vertices = vertices;
		mesh.vertices = m_mBigArcFace[d];
		mesh.indices = *indices;
		//unsigned short dir_color1 = TriangleNormal2LightColor(g_DirectionCoord[dir].toVector3());
		//unsigned short dir_color2 = TriangleNormal2LightColor(g_DirectionCoord[specialdir].toVector3());
		BlockColor vertcolor = Rainbow::ColorRGBA32::white;
		vertcolor.a = 0;// (dir_color1 + 2 * dir_color2) / 3;
		//vertcolor.v = 0xffffffff;
		//if (specialdir == DIR_NEG_Y)
		//{
		//	vertcolor.a = (3 * dir_color1 + dir_color2) / 4;
		//}
		for (int n = 0; n < m_mBigArcFace[d].size(); n++)
		{
			auto& vert = mesh.vertices[n];
			int aveltMe = 0;
			int aveltNeight[3] = { 0 };
			int avelt = 0;
			if (vert.pos.y > 0 && specialdir == DIR_POS_Y || vert.pos.y == 0 && specialdir == DIR_NEG_Y)
			{
				auto selfPos = blockpos + g_DirectionCoord[specialdir];
				aveltMe = psection->getLight2(selfPos, true);
				bool isXdir = dir < 2;
				int xdir = isXdir ? dir : vert.pos.x == 0 ? 0 : 1;
				int zdir = !isXdir ? dir : vert.pos.z == 0 ? 2 : 3;
				aveltNeight[0] = psection->getLight2(selfPos + g_DirectionCoord[xdir], true);
				aveltNeight[1] = psection->getLight2(selfPos + g_DirectionCoord[zdir], true);
				aveltNeight[2] = psection->getLight2(selfPos + g_DirectionCoord[xdir] + g_DirectionCoord[zdir], true);
				avelt = ((aveltMe + aveltNeight[0] + aveltNeight[1] + aveltNeight[2]) >> 2) & 0xff00ff;
			}
			else
			{
				auto selfPos = blockpos + g_DirectionCoord[dir];
				aveltMe = psection->getLight2(selfPos, true);
				int xdir = vert.pos.x == 0 ? 0 : 1;
				int zdir = vert.pos.z == 0 ? 2 : 3;
				int sideDir = dir > 1 ? xdir : zdir;
				aveltNeight[0] = psection->getLight2(selfPos + g_DirectionCoord[sideDir], true);
				avelt = ((aveltMe + aveltNeight[0]) >> 1) & 0xff00ff;
			}
			//int lt1 = (((avelt >> 4) & 0xf) * vertcolor.a) >> 5;
			//int lt2 = (((avelt >> 20) & 0xf) * vertcolor.a) >> 5;
			//vert.pos.w = (lt1 << 8) | lt2;
			//vert.color = vertcolor;
			vert.color = vertcolor;
			InitBlockVertLight(vert, avelt, uvtile);
		}
		if (psubmesh) psubmesh->addGeomFace(mesh, &blockpos);// addGeomFaceLight(mesh, &blockpos, faceVertexLight, &facecolor, pmtl->getUVTile());
	}

#endif
}

void ArcPlateMaterial::initVertData()
{
	initWholeFaceVertData();
	initBigArcFaceVertData();
	initDiamondFaceVertData();
	initTurnArcFaceVertData();
	initPhyModelData();
	
}

void ArcPlateMaterial::initWholeFaceVertData()
{
	if (m_mArcWholeFace().size() != 0)
	{
		return;
	}
	for (int d = 0; d < 6; d++)
	{
		DirectionType dir = (DirectionType)d;
		dynamic_array<UInt16>* indices = m_dPosIndices;

		dynamic_array<BlockGeomVert> vertices;
		Rainbow::Vector3f normalVec = g_DirectionCoord[d].toVector3();
		Rainbow::Normalize(normalVec);
		BlockVector normal_dir = PackVertNormal(normalVec);
		BlockGeomVert vert[4];
		//unsigned short dir_color = Normal2LightColor(g_DirectionCoord[dir].toVector3());
		//unsigned short dir_color = TriangleNormal2LightColor(g_DirectionCoord[dir].toVector3());
		BlockVector vertcolor;
		vertcolor.v = 0xffffffff;
		vertcolor.w = 0;
		if (0 == d)
		{
			vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
			vert[1].pos = Rainbow::Vector4f(0, 0, 100, 0);
			vert[2].pos = Rainbow::Vector4f(0, 100, 100, 0);
			vert[3].pos = Rainbow::Vector4f(0, 100, 0, 0);

			vert[0].uv = { 1, 1 };
			vert[1].uv = { 0, 1 };
			vert[2].uv = { 0, 0 };
			vert[3].uv = { 1, 0 };
		}
		else if (1 == d)
		{
			vert[0].pos = Rainbow::Vector4f(100, 0, 0, 0);
			vert[1].pos = Rainbow::Vector4f(100, 100, 0, 0);
			vert[2].pos = Rainbow::Vector4f(100, 100, 100, 0);
			vert[3].pos = Rainbow::Vector4f(100, 0, 100, 0);

			vert[0].uv = { 0, 1 };
			vert[1].uv = { 0, 0 };
			vert[2].uv = { 1, 0 };
			vert[3].uv = { 1, 1 };
		}
		else if (2 == d)
		{
			vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
			vert[1].pos = Rainbow::Vector4f(0, 100, 0, 0);
			vert[2].pos = Rainbow::Vector4f(100, 100, 0, 0);
			vert[3].pos = Rainbow::Vector4f(100, 0, 0, 0);

			vert[0].uv = { 0, 1 };
			vert[1].uv = { 0, 0 };
			vert[2].uv = { 1, 0 };
			vert[3].uv = { 1, 1 };
		}
		else if (3 == d)
		{
			vert[0].pos = Rainbow::Vector4f(0, 0, 100, 0);
			vert[1].pos = Rainbow::Vector4f(100, 0, 100, 0);
			vert[2].pos = Rainbow::Vector4f(100, 100, 100, 0);
			vert[3].pos = Rainbow::Vector4f(0, 100, 100, 0);

			vert[0].uv = { 1, 1 };
			vert[1].uv = { 0, 1 };
			vert[2].uv = { 0, 0 };
			vert[3].uv = { 1, 0 };
		}
		else if (4 == d)
		{
			vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
			vert[1].pos = Rainbow::Vector4f(100, 0, 0, 0);
			vert[2].pos = Rainbow::Vector4f(100, 0, 100, 0);
			vert[3].pos = Rainbow::Vector4f(0, 0, 100, 0);

			vert[0].uv = { 1, 0 };
			vert[1].uv = { 0, 0 };
			vert[2].uv = { 0, 1 };
			vert[3].uv = { 1, 1 };
		}
		else if (5 == d)
		{
			vert[0].pos = Rainbow::Vector4f(0, 100, 0, 0);
			vert[1].pos = Rainbow::Vector4f(0, 100, 100, 0);
			vert[2].pos = Rainbow::Vector4f(100, 100, 100, 0);
			vert[3].pos = Rainbow::Vector4f(100, 100, 0, 0);

			vert[0].uv = { 0, 0 };
			vert[1].uv = { 0, 1 };
			vert[2].uv = { 1, 1 };
			vert[3].uv = { 1, 0 };
		}

		for (int oo = 0; oo < 4; oo++)
		{
			vert[oo].uv = { (short)(vert[oo].uv.x * BLOCKUV_SCALE), (short)(vert[oo].uv.y * BLOCKUV_SCALE) };
			vert[oo].normal = normal_dir;
			vert[oo].color.SetUInt32(vertcolor.v);
			vert[oo].pos.w = 0;
			vertices.push_back(vert[oo]);
		}
		m_mArcWholeFace().push_back(vertices);
		// 		m_mArcWholeFace()[d] = vertices;
	}
}


void ArcPlateMaterial::initBigArcFaceVertData()
{
	for (int i = 0; i < 2; i++)
	{
		for (int j = 0; j < 4; j++)
		{
			DirectionType dir = (DirectionType)j;
			dynamic_array<UInt16>* indices = m_PosBigArcIndices;
			int d = i * 4 + j;
			dynamic_array<BlockGeomVert> vertices;
			Rainbow::Vector3f normalVec = (g_DirectionCoord[j] + g_DirectionCoord[i ? 4 : 5]).toVector3();
			//unsigned short dir_color = Normal2LightColor(normalVec);
			BlockVector vertcolor;
			vertcolor.v = 0xffffffff;
			vertcolor.w = 0;
			Rainbow::Normalize(normalVec);
			BlockVector normal_dir = PackVertNormal(normalVec);
			BlockGeomVert vert[8];
			Rainbow::Vector2f uv[8];
			if (0 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
				vert[1].pos = Rainbow::Vector4f(0, 0, 100, 0);
				vert[2].pos = Rainbow::Vector4f(13.397f, 50, 100, 0);
				vert[3].pos = Rainbow::Vector4f(13.397f, 50, 0, 0);
				vert[4].pos = Rainbow::Vector4f(50, 86.603f, 0, 0);
				vert[5].pos = Rainbow::Vector4f(50, 86.603f, 100, 0);
				vert[6].pos = Rainbow::Vector4f(100, 100, 100, 0);
				vert[7].pos = Rainbow::Vector4f(100, 100, 0, 0);

				uv[0] = { 1, 1 };
				uv[1] = { 0, 1 };
				uv[2] = { 0, 0.6667f };
				uv[3] = { 1, 0.6667f };
				uv[4] = { 1, 0.3333f };
				uv[5] = { 0, 0.3333f };
				uv[6] = { 0, 0 };
				uv[7] = { 1, 0 };
			}
			else if (1 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 100, 0, 0);
				vert[1].pos = Rainbow::Vector4f(0, 100, 100, 0);
				vert[2].pos = Rainbow::Vector4f(50, 86.603f, 100, 0);
				vert[3].pos = Rainbow::Vector4f(50, 86.603f, 0, 0);
				vert[4].pos = Rainbow::Vector4f(86.603f, 50, 0, 0);
				vert[5].pos = Rainbow::Vector4f(86.603f, 50, 100, 0);
				vert[6].pos = Rainbow::Vector4f(100, 0, 100, 0);
				vert[7].pos = Rainbow::Vector4f(100, 0, 0, 0);

				uv[0] = { 0, 0 };
				uv[1] = { 1, 0 };
				uv[2] = { 1, 0.3333f };
				uv[3] = { 0, 0.3333f };
				uv[4] = { 0, 0.6667f };
				uv[5] = { 1, 0.6667f };
				uv[6] = { 1, 1 };
				uv[7] = { 0, 1 };
			}
			else if (2 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 100, 100, 0);
				vert[1].pos = Rainbow::Vector4f(100, 100, 100, 0);
				vert[2].pos = Rainbow::Vector4f(100, 86.603f, 50, 0);
				vert[3].pos = Rainbow::Vector4f(0, 86.603f, 50, 0);
				vert[4].pos = Rainbow::Vector4f(0, 50, 13.397f, 0);
				vert[5].pos = Rainbow::Vector4f(100, 50, 13.397f, 0);
				vert[6].pos = Rainbow::Vector4f(100, 0, 0, 0);
				vert[7].pos = Rainbow::Vector4f(0, 0, 0, 0);

				uv[0] = { 0, 0 };
				uv[1] = { 1, 0 };
				uv[2] = { 1, 0.3333f };
				uv[3] = { 0, 0.3333f };
				uv[4] = { 0, 0.6667f };
				uv[5] = { 1, 0.6667f };
				uv[6] = { 1, 1 };
				uv[7] = { 0, 1 };
			}
			else if (3 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 0, 100, 0);
				vert[1].pos = Rainbow::Vector4f(100, 0, 100, 0);
				vert[2].pos = Rainbow::Vector4f(100, 50, 86.603f, 0);
				vert[3].pos = Rainbow::Vector4f(0, 50, 86.603f, 0);
				vert[4].pos = Rainbow::Vector4f(0, 86.603f, 50, 0);
				vert[5].pos = Rainbow::Vector4f(100, 86.603f, 50, 0);
				vert[6].pos = Rainbow::Vector4f(100, 100, 0, 0);
				vert[7].pos = Rainbow::Vector4f(0, 100, 0, 0);

				uv[0] = { 1, 1 };
				uv[1] = { 0, 1 };
				uv[2] = { 0, 0.6667f };
				uv[3] = { 1, 0.6667f };
				uv[4] = { 1, 0.3333f };
				uv[5] = { 0, 0.3333f };
				uv[6] = { 0, 0 };
				uv[7] = { 1, 0 };
			}
			else if (4 == d)
			{
				vert[0].pos = Rainbow::Vector4f(100, 0, 0, 0);
				vert[1].pos = Rainbow::Vector4f(100, 0, 100, 0);
				vert[2].pos = Rainbow::Vector4f(50, 13.397f, 100, 0);
				vert[3].pos = Rainbow::Vector4f(50, 13.397f, 0, 0);
				vert[4].pos = Rainbow::Vector4f(13.397f, 50, 0, 0);
				vert[5].pos = Rainbow::Vector4f(13.397f, 50, 100, 0);
				vert[6].pos = Rainbow::Vector4f(0, 100, 100, 0);
				vert[7].pos = Rainbow::Vector4f(0, 100, 0, 0);

				uv[0] = { 1, 1 };
				uv[1] = { 0, 1 };
				uv[2] = { 0, 0.6667f };
				uv[3] = { 1, 0.6667f };
				uv[4] = { 1, 0.3333f };
				uv[5] = { 0, 0.3333f };
				uv[6] = { 0, 0 };
				uv[7] = { 1, 0 };
			}
			else if (5 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 0, 100, 0);
				vert[1].pos = Rainbow::Vector4f(0, 0, 0, 0);
				vert[2].pos = Rainbow::Vector4f(50, 13.397f, 0, 0);
				vert[3].pos = Rainbow::Vector4f(50, 13.397f, 100, 0);
				vert[4].pos = Rainbow::Vector4f(86.603f, 50, 100, 0);
				vert[5].pos = Rainbow::Vector4f(86.603f, 50, 0, 0);
				vert[6].pos = Rainbow::Vector4f(100, 100, 0, 0);
				vert[7].pos = Rainbow::Vector4f(100, 100, 100, 0);

				uv[0] = { 1, 1 };
				uv[1] = { 0, 1 };
				uv[2] = { 0, 0.6667f };
				uv[3] = { 1, 0.6667f };
				uv[4] = { 1, 0.3333f };
				uv[5] = { 0, 0.3333f };
				uv[6] = { 0, 0 };
				uv[7] = { 1, 0 };
			}
			else if (6 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 100, 0, 0);
				vert[1].pos = Rainbow::Vector4f(100, 100, 0, 0);
				vert[2].pos = Rainbow::Vector4f(100, 50, 13.397f, 0);
				vert[3].pos = Rainbow::Vector4f(0, 50, 13.397f, 0);
				vert[4].pos = Rainbow::Vector4f(0, 13.397f, 50, 0);
				vert[5].pos = Rainbow::Vector4f(100, 13.397f, 50, 0);
				vert[6].pos = Rainbow::Vector4f(100, 0, 100, 0);
				vert[7].pos = Rainbow::Vector4f(0, 0, 100, 0);


				uv[0] = { 0, 0 };
				uv[1] = { 1, 0 };
				uv[2] = { 1, 0.3333f };
				uv[3] = { 0, 0.3333f };
				uv[4] = { 0, 0.6667f };
				uv[5] = { 1, 0.6667f };
				uv[6] = { 1, 1 };
				uv[7] = { 0, 1 };
			}
			else if (7 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
				vert[1].pos = Rainbow::Vector4f(100, 0, 0, 0);
				vert[2].pos = Rainbow::Vector4f(100, 13.397f, 50, 0);
				vert[3].pos = Rainbow::Vector4f(0, 13.397f, 50, 0);
				vert[4].pos = Rainbow::Vector4f(0, 50, 86.603f, 0);
				vert[5].pos = Rainbow::Vector4f(100, 50, 86.603f, 0);
				vert[6].pos = Rainbow::Vector4f(100, 100, 100, 0);
				vert[7].pos = Rainbow::Vector4f(0, 100, 100, 0);

				uv[0] = { 1, 1 };
				uv[1] = { 0, 1 };
				uv[2] = { 0, 0.6667f };
				uv[3] = { 1, 0.6667f };
				uv[4] = { 1, 0.3333f };
				uv[5] = { 0, 0.3333f };
				uv[6] = { 0, 0 };
				uv[7] = { 1, 0 };
			}
			for (int oo = 0; oo < 8; oo++)
			{
				vert[oo].uv = { short(uv[oo].x * BLOCKUV_SCALE), short(uv[oo].y * BLOCKUV_SCALE) };
				vert[oo].normal = normal_dir;
				vert[oo].color.SetUInt32(vertcolor.v);
				// 				vert[oo].color = 0xffffffff;
				vertices.push_back(vert[oo]);
			}
			m_mBigArcFace.push_back(vertices);
		}
	}
}



void ArcPlateMaterial::initDiamondFaceVertData()
{
	for (int i = 0; i < 4; i++)
	{
		for (int j = 0; j < 4; j++)
		{
			DirectionType dir = (DirectionType)j;
			dynamic_array<UInt16>* indices = m_PosDiamondIndices;
			int d = i * 4 + j;
			//unsigned short dir_color = TriangleNormal2LightColor(g_DirectionCoord[dir].toVector3());
			dynamic_array<BlockGeomVert> vertices;
			Rainbow::Vector3f normalVec = g_DirectionCoord[j].toVector3();
			////unsigned short dir_color = Normal2LightColor(normalVec);
			Rainbow::Normalize(normalVec);
			BlockVector normal_dir = PackVertNormal(normalVec);
			BlockVector vertcolor;
			vertcolor.v = 0xffffffff;
			vertcolor.w = 0;

			Rainbow::Vector2f uv[5];
			BlockGeomVert vert[5];
			if (0 == d)//斜线朝向3 down
			{
				vert[0].pos = Rainbow::Vector4f(0, 100, 0, 0);
				vert[1].pos = Rainbow::Vector4f(0, 0, 0, 0);
				vert[2].pos = Rainbow::Vector4f(0, 0, 100, 0);
				vert[3].pos = Rainbow::Vector4f(0, 50, 86.603f, 0);
				vert[4].pos = Rainbow::Vector4f(0, 86.603f, 50, 0);

				uv[0] = { 1, 0 };
				uv[1] = { 1, 1 };
				uv[2] = { 0, 1 };
				uv[3] = { 0.13397f, 0.5f };
				uv[4] = { 0.5f, 0.13397f };
			}
			else if (1 == d)
			{
				vert[0].pos = Rainbow::Vector4f(100, 0, 100, 0);
				vert[1].pos = Rainbow::Vector4f(100, 0, 0, 0);
				vert[2].pos = Rainbow::Vector4f(100, 100, 0, 0);
				vert[3].pos = Rainbow::Vector4f(100, 86.603f, 50, 0);
				vert[4].pos = Rainbow::Vector4f(100, 50, 86.603f, 0);

				uv[0] = { 1, 1 };
				uv[1] = { 0, 1 };
				uv[2] = { 0, 0 };
				uv[3] = { 0.5f, 0.13397f };
				uv[4] = { 0.86603f, 0.5f };
			}
			else if (2 == d)
			{
				vert[0].pos = Rainbow::Vector4f(100, 0, 0, 0);
				vert[1].pos = Rainbow::Vector4f(0, 0, 0, 0);
				vert[2].pos = Rainbow::Vector4f(0, 100, 0, 0);
				vert[3].pos = Rainbow::Vector4f(50, 86.603f, 0, 0);
				vert[4].pos = Rainbow::Vector4f(86.603f, 50, 0, 0);

				uv[0] = { 1, 1 };
				uv[1] = { 0, 1 };
				uv[2] = { 0, 0 };
				uv[3] = { 0.5f, 0.13397f };
				uv[4] = { 0.86603f, 0.5f };
			}
			else if (3 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 100, 100, 0);
				vert[1].pos = Rainbow::Vector4f(0, 0, 100, 0);
				vert[2].pos = Rainbow::Vector4f(100, 0, 100, 0);
				vert[3].pos = Rainbow::Vector4f(86.603f, 50, 100, 0);
				vert[4].pos = Rainbow::Vector4f(50, 86.603f, 100, 0);

				uv[0] = { 1, 0 };
				uv[1] = { 1, 1 };
				uv[2] = { 0, 1 };
				uv[3] = { 0.13397f, 0.5f };
				uv[4] = { 0.5f, 0.13397f };
			}
			else if (4 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
				vert[1].pos = Rainbow::Vector4f(0, 0, 100, 0);
				vert[2].pos = Rainbow::Vector4f(0, 100, 100, 0);
				vert[3].pos = Rainbow::Vector4f(0, 86.603f, 50, 0);
				vert[4].pos = Rainbow::Vector4f(0, 50, 13.397f, 0);

				uv[0] = { 1, 1 };
				uv[1] = { 0, 1 };
				uv[2] = { 0, 0 };
				uv[3] = { 0.5f, 0.13397f };
				uv[4] = { 0.86603f, 0.5f };
			}
			else if (5 == d)
			{
				vert[0].pos = Rainbow::Vector4f(100, 100, 100, 0);
				vert[1].pos = Rainbow::Vector4f(100, 0, 100, 0);
				vert[2].pos = Rainbow::Vector4f(100, 0, 0, 0);
				vert[3].pos = Rainbow::Vector4f(100, 50, 13.397f, 0);
				vert[4].pos = Rainbow::Vector4f(100, 86.603f, 50, 0);

				uv[0] = { 1, 0 };
				uv[1] = { 1, 1 };
				uv[2] = { 0, 1 };
				uv[3] = { 0.13397f, 0.5f };
				uv[4] = { 0.5f, 0.13397f };
			}
			else if (6 == d)
			{
				vert[0].pos = Rainbow::Vector4f(100, 100, 0, 0);
				vert[1].pos = Rainbow::Vector4f(100, 0, 0, 0);
				vert[2].pos = Rainbow::Vector4f(0, 0, 0, 0);
				vert[3].pos = Rainbow::Vector4f(13.397f, 50, 0, 0);
				vert[4].pos = Rainbow::Vector4f(50, 86.603f, 0, 0);

				uv[0] = { 1, 0 };
				uv[1] = { 1, 1 };
				uv[2] = { 0, 1 };
				uv[3] = { 0.13397f, 0.5f };
				uv[4] = { 0.5f, 0.13397f };
			}
			else if (7 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 0, 100, 0);
				vert[1].pos =  Rainbow::Vector4f(100, 0, 100, 0);
				vert[2].pos =  Rainbow::Vector4f(100, 100, 100, 0);
				vert[3].pos = Rainbow::Vector4f(50, 86.603f, 100, 0);
				vert[4].pos =  Rainbow::Vector4f(13.397f, 50, 100, 0);

				uv[0] = { 1, 1 };
				uv[1] = { 0, 1 };
				uv[2] = { 0, 0 };
				uv[3] = { 0.5f, 0.13397f };
				uv[4] = { 0.86603f, 0.5f };
			}
			else if (8 == d)
			{
			vert[0].pos = Rainbow::Vector4f(0, 100, 100, 0);
			vert[1].pos = Rainbow::Vector4f(0, 100, 0, 0);
			vert[2].pos = Rainbow::Vector4f(0, 0, 0, 0);
			vert[3].pos = Rainbow::Vector4f(0, 13.397f, 50, 0);
			vert[4].pos = Rainbow::Vector4f(0, 50, 86.603f, 0);

			uv[0] = { 0, 0 };
			uv[1] = { 1, 0 };
			uv[2] = { 1, 1 };
			uv[3] = { 0.5f, 0.86603f };
			uv[4] = { 0.13397f, 0.5f };
			}
			else if (9 == d)
			{
			vert[0].pos = Rainbow::Vector4f(100, 0, 0, 0);
			vert[1].pos = Rainbow::Vector4f(100, 100, 0, 0);
			vert[2].pos = Rainbow::Vector4f(100, 100, 100, 0);
			vert[3].pos = Rainbow::Vector4f(100, 50, 86.603f, 0);
			vert[4].pos = Rainbow::Vector4f(100, 13.397f, 50, 0);

			uv[0] = { 0, 1 };
			uv[1] = { 0, 0 };
			uv[2] = { 1, 0 };
			uv[3] = { 0.86603f, 0.5f };
			uv[4] = { 0.5f, 0.86603f };
			}
			else if (10 == d)
			{
			vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
			vert[1].pos = Rainbow::Vector4f(0, 100, 0, 0);
			vert[2].pos = Rainbow::Vector4f(100, 100, 0, 0);
			vert[3].pos = Rainbow::Vector4f(86.603f, 50, 0, 0);
			vert[4].pos = Rainbow::Vector4f(50, 13.397f, 0, 0);

			uv[0] = { 0, 1 };
			uv[1] = { 0, 0 };
			uv[2] = { 1, 0 };
			uv[3] = { 0.86603f, 0.5f };
			uv[4] = { 0.5f, 0.86603f };
			}
			else if (11 == d)
			{
			vert[0].pos = Rainbow::Vector4f(100, 100, 100, 0);
			vert[1].pos = Rainbow::Vector4f(0, 100, 100, 0);
			vert[2].pos = Rainbow::Vector4f(0, 0, 100, 0);
			vert[3].pos = Rainbow::Vector4f(50, 13.397f, 100, 0);
			vert[4].pos = Rainbow::Vector4f(86.603f, 50, 100, 0);

			uv[0] = { 0, 0 };
			uv[1] = { 1, 0 };
			uv[2] = { 1, 1 };
			uv[3] = { 0.5f, 0.86603f };
			uv[4] = { 0.13397f, 0.5 };
			}
			else if (12 == d)
			{
			vert[0].pos = Rainbow::Vector4f(0, 0, 100, 0);
			vert[1].pos = Rainbow::Vector4f(0, 100, 100, 0);
			vert[2].pos = Rainbow::Vector4f(0, 100, 0, 0);
			vert[3].pos = Rainbow::Vector4f(0, 50, 13.397f, 0);
			vert[4].pos = Rainbow::Vector4f(0, 13.397f, 50, 0);

			uv[0] = { 0, 1 };
			uv[1] = { 0, 0 };
			uv[2] = { 1, 0 };
			uv[3] = { 0.86603f, 0.5f };
			uv[4] = { 0.5f, 0.86603f };
			}
			else if (13 == d)
			{
			vert[0].pos = Rainbow::Vector4f(100, 100, 0, 0);
			vert[1].pos = Rainbow::Vector4f(100, 100, 100, 0);
			vert[2].pos = Rainbow::Vector4f(100, 0, 100, 0);
			vert[3].pos = Rainbow::Vector4f(100, 13.397f, 50, 0);
			vert[4].pos = Rainbow::Vector4f(100, 50, 13.397f, 0);

			uv[0] = { 0, 0 };
			uv[1] = { 1, 0 };
			uv[2] = { 1, 1 };
			uv[3] = { 0.5f, 0.86603f };
			uv[4] = { 0.13397f, 0.5f };
			}
			else if (14 == d)
			{
			vert[0].pos = Rainbow::Vector4f(0, 100, 0, 0);
			vert[1].pos = Rainbow::Vector4f(100, 100, 0, 0);
			vert[2].pos = Rainbow::Vector4f(100, 0, 0, 0);
			vert[3].pos = Rainbow::Vector4f(50, 13.397f, 0, 0);
			vert[4].pos = Rainbow::Vector4f(13.397f, 50, 0, 0);

			uv[0] = { 0, 0 };
			uv[1] = { 1, 0 };
			uv[2] = { 1, 1 };
			uv[3] = { 0.5f, 0.86603f };
			uv[4] = { 0.13397f, 0.5f };
			}
			else if (15 == d)
			{
			vert[0].pos = Rainbow::Vector4f(100, 0, 100, 0);
			vert[1].pos = Rainbow::Vector4f(100, 100, 100, 0);
			vert[2].pos = Rainbow::Vector4f(0, 100, 100, 0);
			vert[3].pos = Rainbow::Vector4f(13.397f, 50, 100, 0);
			vert[4].pos = Rainbow::Vector4f(50, 13.397f, 100, 0);

			uv[0] = { 0, 1 };
			uv[1] = { 0, 0 };
			uv[2] = { 1, 0 };
			uv[3] = { 0.86603f, 0.5f };
			uv[4] = { 0.5f, 0.86603f };
			}
			for (int oo = 0; oo < 5; oo++)
			{
				vert[oo].uv = { short(uv[oo].x * BLOCKUV_SCALE), short(uv[oo].y * BLOCKUV_SCALE) };
				vert[oo].normal = normal_dir;
				vert[oo].color.SetUInt32(vertcolor.v);
				vert[oo].pos.w = 0;
				vertices.push_back(vert[oo]);
			}
			m_mDiamondFace.push_back(vertices);
		}
	}
}


void ArcPlateMaterial::initTurnArcFaceVertData()
{
	for (int i = 0; i < 2; i++)//updown
	{
		for (int j = 0; j < 2; j++) //0是凹1是凸
		{
			for (int k = 0; k < 4; k++)//4个主方向
			{
				if (0 == k)
				{
					if (0 == i)
					{
						if (1 == j)
						{
							for (int o = 2; o <= 3; o++)
							{
								dynamic_array<BlockGeomVert> vertices;
								BlockGeomVert vert[7];
								Rainbow::Vector2f uv[7];
								if (o == 2)
								{
									vert[0].pos = Rainbow::Vector4f(100, 100, 100, 0);
									vert[1].pos = Rainbow::Vector4f(50, 86.603f, 100, 0);
									vert[2].pos = Rainbow::Vector4f(50, 86.603f, 50, 0);
									vert[3].pos = Rainbow::Vector4f(13.397f, 50, 100, 0);
									vert[4].pos = Rainbow::Vector4f(13.397f, 50, 13.397f, 0);
									vert[5].pos = Rainbow::Vector4f(0, 0, 100, 0);
									vert[6].pos = Rainbow::Vector4f(0, 0, 0, 0);
									uv[0] = { 0, 0 };
									uv[1] = { 0, 0.3333f };
									uv[2] = { 0.5f, 0.3333f };
									uv[3] = { 0, 0.6667f };
									uv[4] = { 0.86607f, 0.6667f };
									uv[5] = { 0, 1 };
									uv[6] = { 1, 1 };
								}
								else
								{
									vert[0].pos = Rainbow::Vector4f(100, 100, 0, 0);
									vert[1].pos = Rainbow::Vector4f(50, 86.603f, 50, 0);
									vert[2].pos = Rainbow::Vector4f(50, 86.603f, 0, 0);
									vert[3].pos = Rainbow::Vector4f(13.397f, 50, 86.603f, 0);
									vert[4].pos = Rainbow::Vector4f(13.397f, 50, 0, 0);
									vert[5].pos = Rainbow::Vector4f(0, 0, 100, 0);
									vert[6].pos = Rainbow::Vector4f(0, 0, 0, 0);
									uv[0] = { 1, 0 };
									uv[1] = { 0.5f, 0.3333f };
									uv[2] = { 1, 0.3333f };
									uv[3] = { 0.13397f, 0.6667f };
									uv[4] = { 1, 0.6667f };
									uv[5] = { 0, 1 };
									uv[6] = { 1, 1 };
								}

								Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5]).toVector3();
								//unsigned short dir_color = Normal2LightColor(normalVec);
								Rainbow::Normalize(normalVec);
								BlockVector normal_dir = PackVertNormal(normalVec);
								BlockVector vertcolor;
								vertcolor.v = 0xffffffff;
								vertcolor.w = 0;

								for (int oo = 0; oo < 7; oo++)
								{
									vert[oo].uv = { short(uv[oo].x * BLOCKUV_SCALE), short(uv[oo].y * BLOCKUV_SCALE) };
									vert[oo].normal = normal_dir;
									vert[oo].color.SetUInt32(vertcolor.v);
									vertices.push_back(vert[oo]);
								}

								m_mTurnArcFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							}
						}
						else
						{
							for (int o = 2; o <= 3; o++)
							{
								dynamic_array<BlockGeomVert> vertices;
								BlockGeomVert vert[7];
								Rainbow::Vector2f uv[7];
								if (o == 2)//0002
								{
									vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
									vert[1].pos = Rainbow::Vector4f(13.397f, 50, 0, 0);
									vert[2].pos = Rainbow::Vector4f(13.397f, 50, 13.397f, 0);
									vert[3].pos = Rainbow::Vector4f(50, 86.603f, 0, 0);
									vert[4].pos = Rainbow::Vector4f(50, 86.603f, 50, 0);
									vert[5].pos = Rainbow::Vector4f(100, 100, 0, 0);
									vert[6].pos = Rainbow::Vector4f(100, 100, 100, 0);
									uv[0] = { 1, 1 };
									uv[1] = { 1, 0.6667f };
									uv[2] = { 0.86603f, 0.6667f };
									uv[3] = { 1, 0.3333f };
									uv[4] = { 0.5, 0.3333f };
									uv[5] = { 1, 0 };
									uv[6] = { 0, 0 };
								}
								else//0003
								{
									vert[0].pos = Rainbow::Vector4f(0, 0, 100, 0);
									vert[1].pos = Rainbow::Vector4f(13.397f, 50, 86.603f, 0);
									vert[2].pos = Rainbow::Vector4f(13.397f, 50, 100, 0);
									vert[3].pos = Rainbow::Vector4f(50, 86.603f, 50, 0);
									vert[4].pos = Rainbow::Vector4f(50, 86.603f, 100, 0);
									vert[5].pos = Rainbow::Vector4f(100, 100, 0, 0);
									vert[6].pos = Rainbow::Vector4f(100, 100, 100, 0);
									uv[0] = { 0, 1 };
									uv[1] = { 0.13397f, 0.6667f };
									uv[2] = { 0, 0.6667f };
									uv[3] = { 0.5f, 0.3333f };
									uv[4] = { 0, 0.3333f };
									uv[5] = { 1, 0 };
									uv[6] = { 0, 0 };
								}

								Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5]).toVector3();
								//unsigned short dir_color = Normal2LightColor(normalVec);
								Rainbow::Normalize(normalVec);
								BlockVector normal_dir = PackVertNormal(normalVec);
								BlockVector vertcolor;
								vertcolor.v = 0xffffffff;
								vertcolor.w = 0;
								for (int oo = 0; oo < 7; oo++)
								{
									vert[oo].uv = { short(uv[oo].x * BLOCKUV_SCALE), short(uv[oo].y * BLOCKUV_SCALE) };
									vert[oo].normal = normal_dir;
									vert[oo].color.SetUInt32(vertcolor.v);
									vertices.push_back(vert[oo]);
								}
								m_mTurnArcFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							}
						}
					}
					else
					{
						if (1 == j)
						{
							for (int o = 2; o <= 3; o++)
							{
								dynamic_array<BlockGeomVert> vertices;
								BlockGeomVert vert[7];
								Rainbow::Vector2f uv[7];
								if (o == 2)//1102
								{
									vert[0].pos = Rainbow::Vector4f(100, 0, 100, 0);
									vert[1].pos = Rainbow::Vector4f(50, 13.397f, 50, 0);
									vert[2].pos = Rainbow::Vector4f(50, 13.397f, 100, 0);
									vert[3].pos = Rainbow::Vector4f(13.397f, 50, 13.397f, 0);
									vert[4].pos = Rainbow::Vector4f(13.397f, 50, 100, 0);
									vert[5].pos = Rainbow::Vector4f(0, 100, 0, 0);
									vert[6].pos = Rainbow::Vector4f(0, 100, 100, 0);
									uv[0] = { 0, 1 };
									uv[1] = { 0.5f, 0.6667f };
									uv[2] = { 0, 0.6667f };
									uv[3] = { 0.86607f, 0.3333f };
									uv[4] = { 0, 0.3333f };
									uv[5] = { 1, 0 };
									uv[6] = { 0, 0 };
								}
								else//1103
								{
									vert[0].pos = Rainbow::Vector4f(100, 0, 0, 0);
									vert[1].pos = Rainbow::Vector4f(50, 13.397f, 0, 0);
									vert[2].pos = Rainbow::Vector4f(50, 13.397f, 50, 0);
									vert[3].pos = Rainbow::Vector4f(13.397f, 50, 0, 0);
									vert[4].pos = Rainbow::Vector4f(13.397f, 50, 86.603f, 0);
									vert[5].pos = Rainbow::Vector4f(0, 100, 0, 0);
									vert[6].pos = Rainbow::Vector4f(0, 100, 100, 0);
									uv[0] = { 1, 1 };
									uv[1] = { 1, 0.6667f };
									uv[2] = { 0.5, 0.6667f };
									uv[3] = { 1, 0.3333f };
									uv[4] = { 0.13397f, 0.3333f };
									uv[5] = { 1, 0 };
									uv[6] = { 0, 0 };
								}

								Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5]).toVector3();
								//unsigned short dir_color = Normal2LightColor(normalVec);
								Rainbow::Normalize(normalVec);
								BlockVector normal_dir = PackVertNormal(normalVec);
								BlockVector vertcolor;
								vertcolor.v = 0xffffffff;
								vertcolor.w = 0;
								for (int oo = 0; oo < 7; oo++)
								{
									vert[oo].uv = { short(uv[oo].x * BLOCKUV_SCALE), short(uv[oo].y * BLOCKUV_SCALE) };
									vert[oo].normal = normal_dir;
									vert[oo].color.SetUInt32(vertcolor.v);
									vertices.push_back(vert[oo]);
								}
								m_mTurnArcFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							}
						}
						else
						{
							for (int o = 2; o <= 3; o++)
							{
								dynamic_array<BlockGeomVert> vertices;
								BlockGeomVert vert[7];
								Rainbow::Vector2f uv[7];
								if (o == 2)//1002
								{
									vert[0].pos = Rainbow::Vector4f(0, 100, 0, 0);
									vert[1].pos = Rainbow::Vector4f(13.397f, 50, 13.397f, 0);
									vert[2].pos = Rainbow::Vector4f(13.397f, 50, 0, 0);
									vert[3].pos = Rainbow::Vector4f(50, 13.397f, 50, 0);
									vert[4].pos = Rainbow::Vector4f(50, 13.397f, 0, 0);
									vert[5].pos = Rainbow::Vector4f(100, 0, 100, 0);
									vert[6].pos = Rainbow::Vector4f(100, 0, 0, 0);
									uv[0] = { 0, 1 };
									uv[1] = { 0.86603f, 0.3333f };
									uv[2] = { 1, 0.3333f };
									uv[3] = { 0.5f, 0.6667f };
									uv[4] = { 1, 0.6667f };
									uv[5] = { 0, 1 };
									uv[6] = { 1, 1 };
								}
								else//1003
								{
									vert[0].pos = Rainbow::Vector4f(0, 100, 100, 0);
									vert[1].pos = Rainbow::Vector4f(13.397f, 50, 100, 0);
									vert[2].pos = Rainbow::Vector4f(13.397f, 50, 86.603f, 0);
									vert[3].pos = Rainbow::Vector4f(50, 13.397f, 100, 0);
									vert[4].pos = Rainbow::Vector4f(50, 13.397f, 50, 0);
									vert[5].pos = Rainbow::Vector4f(100, 0, 100, 0);
									vert[6].pos = Rainbow::Vector4f(100, 0, 0, 0);
									uv[0] = { 0, 0 };
									uv[1] = { 0, 0.3333f };
									uv[2] = { 0.13397f, 0.3333f };
									uv[3] = { 0, 0.6667f };
									uv[4] = { 0.5f, 0.6667f };
									uv[5] = { 0, 1 };
									uv[6] = { 1, 1 };
								}

								Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5]).toVector3();
								//unsigned short dir_color = Normal2LightColor(normalVec);
								Rainbow::Normalize(normalVec);
								BlockVector normal_dir = PackVertNormal(normalVec);
								BlockVector vertcolor;
								vertcolor.v = 0xffffffff;
								vertcolor.w = 0;
								for (int oo = 0; oo < 7; oo++)
								{
									vert[oo].uv = { short(uv[oo].x * BLOCKUV_SCALE), short(uv[oo].y * BLOCKUV_SCALE) };
									vert[oo].normal = normal_dir;
									vert[oo].color.SetUInt32(vertcolor.v);
									vertices.push_back(vert[oo]);
								}
								m_mTurnArcFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							}
						}
					}
				}
				else if (1 == k)
				{
					if (0 == i)
					{
						if (1 == j)
						{
							for (int o = 2; o <= 3; o++)
							{
								dynamic_array<BlockGeomVert> vertices;
								BlockGeomVert vert[7];
								Rainbow::Vector2f uv[7];
								if (o == 2)
								{
									vert[0].pos = Rainbow::Vector4f(0, 100, 100, 0);
									vert[1].pos = Rainbow::Vector4f(50, 86.603f, 50, 0);
									vert[2].pos = Rainbow::Vector4f(50, 86.603f, 100, 0);
									vert[3].pos = Rainbow::Vector4f(86.603f, 50, 13.397f, 0);
									vert[4].pos = Rainbow::Vector4f(86.603f, 50, 100, 0);
									vert[5].pos = Rainbow::Vector4f(100, 0, 0, 0);
									vert[6].pos = Rainbow::Vector4f(100, 0, 100, 0);
									uv[0] = { 1, 0 };
									uv[1] = { 0.5f, 0.3333f };
									uv[2] = { 1, 0.3333f };
									uv[3] = { 0.13397f, 0.6667f };
									uv[4] = { 1, 0.6667f };
									uv[5] = { 0, 1 };
									uv[6] = { 1, 1 };
								}
								else
								{
									vert[0].pos = Rainbow::Vector4f(0, 100, 0, 0);
									vert[1].pos = Rainbow::Vector4f(50, 86.603f, 0, 0);
									vert[2].pos = Rainbow::Vector4f(50, 86.603f, 50, 0);
									vert[3].pos = Rainbow::Vector4f(86.603f, 50, 0, 0);
									vert[4].pos = Rainbow::Vector4f(86.603f, 50, 86.603f, 0);
									vert[5].pos = Rainbow::Vector4f(100, 0, 0, 0);
									vert[6].pos = Rainbow::Vector4f(100, 0, 100, 0);
									uv[0] = { 0, 0 };
									uv[1] = { 0, 0.3333f };
									uv[2] = { 0.5f, 0.3333f };
									uv[3] = { 0, 0.6667f };
									uv[4] = { 0.86603f, 0.6667f };
									uv[5] = { 0, 1 };
									uv[6] = { 1, 1 };
								}

								Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5]).toVector3();
								//unsigned short dir_color = Normal2LightColor(normalVec);
								Rainbow::Normalize(normalVec);
								BlockVector normal_dir = PackVertNormal(normalVec);
								BlockVector vertcolor;
								vertcolor.v = 0xffffffff;
								vertcolor.w = 0;
								for (int oo = 0; oo < 7; oo++)
								{
									vert[oo].uv = { short(uv[oo].x * BLOCKUV_SCALE), short(uv[oo].y * BLOCKUV_SCALE) };
									vert[oo].normal = normal_dir;
									vert[oo].color.SetUInt32(vertcolor.v);
									vertices.push_back(vert[oo]);
								}
								m_mTurnArcFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							}
						}
						else
						{
							for (int o = 2; o <= 3; o++)
							{
								dynamic_array<BlockGeomVert> vertices;
								BlockGeomVert vert[7];
								Rainbow::Vector2f uv[7];
								if (o == 2)//0012
								{
									vert[0].pos = Rainbow::Vector4f(100, 0, 0, 0);
									vert[1].pos = Rainbow::Vector4f(86.603f, 50, 13.397f, 0);
									vert[2].pos = Rainbow::Vector4f(86.603f, 50, 0, 0);
									vert[3].pos = Rainbow::Vector4f(50, 86.603f, 50, 0);
									vert[4].pos = Rainbow::Vector4f(50, 86.603f, 0, 0);
									vert[5].pos = Rainbow::Vector4f(0, 100, 100, 0);
									vert[6].pos = Rainbow::Vector4f(0, 100, 0, 0);
									uv[0] = { 0, 1 };
									uv[1] = { 0.13397f, 0.6667f };
									uv[2] = { 0, 0.6667f };
									uv[3] = { 0.5f, 0.3333f };
									uv[4] = { 0, 0.3333f };
									uv[5] = { 1, 0 };
									uv[6] = { 0, 0 };
								}
								else//0013
								{
									vert[0].pos = Rainbow::Vector4f(100, 0, 100, 0);
									vert[1].pos = Rainbow::Vector4f(86.603f, 50, 100, 0);
									vert[2].pos = Rainbow::Vector4f(86.603f, 50, 86.603f, 0);
									vert[3].pos = Rainbow::Vector4f(50, 86.603f, 100, 0);
									vert[4].pos = Rainbow::Vector4f(50, 86.603f, 50, 0);
									vert[5].pos = Rainbow::Vector4f(0, 100, 100, 0);
									vert[6].pos = Rainbow::Vector4f(0, 100, 0, 0);
									uv[0] = { 1, 1 };
									uv[1] = { 1, 0.6667f };
									uv[2] = { 0.86603f, 0.6667f };
									uv[3] = { 1, 0.3333f };
									uv[4] = { 0.5f, 0.3333f };
									uv[5] = { 1, 0 };
									uv[6] = { 0, 0 };
								}
								Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5]).toVector3();
								//unsigned short dir_color = Normal2LightColor(normalVec);
								Rainbow::Normalize(normalVec);
								BlockVector normal_dir = PackVertNormal(normalVec);
								BlockVector vertcolor;
								vertcolor.v = 0xffffffff;
								vertcolor.w = 0;
								for (int oo = 0; oo < 7; oo++)
								{
									vert[oo].uv = { short(uv[oo].x * BLOCKUV_SCALE), short(uv[oo].y * BLOCKUV_SCALE) };
									vert[oo].normal = normal_dir;
									vert[oo].color.SetUInt32(vertcolor.v);
									vertices.push_back(vert[oo]);
								}
								m_mTurnArcFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							}
						}
					}
					else
					{
						if (1 == j)
						{
							for (int o = 2; o <= 3; o++)
							{
								dynamic_array<BlockGeomVert> vertices;
								BlockGeomVert vert[7];
								Rainbow::Vector2f uv[7];
								if (o == 2)//1112
								{
									vert[0].pos = Rainbow::Vector4f(0, 0, 100, 0);
									vert[1].pos = Rainbow::Vector4f(50, 13.397f, 100, 0);
									vert[2].pos = Rainbow::Vector4f(50, 13.397f, 50, 0);
									vert[3].pos = Rainbow::Vector4f(86.603f, 50, 100, 0);
									vert[4].pos = Rainbow::Vector4f(86.603f, 50, 13.397f, 0);
									vert[5].pos = Rainbow::Vector4f(100, 100, 100, 0);
									vert[6].pos = Rainbow::Vector4f(100, 100, 0, 0);

									uv[0] = { 1, 1 };
									uv[1] = { 1, 0.6667f };
									uv[2] = { 0.5f, 0.6667f };
									uv[3] = { 1, 0.3333f };
									uv[4] = { 0.13397f, 0.3333f };
									uv[5] = { 1, 0 };
									uv[6] = { 0, 0 };
								}
								else//1113
								{
									vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
									vert[1].pos = Rainbow::Vector4f(50, 13.397f, 50, 0);
									vert[2].pos = Rainbow::Vector4f(50, 13.397f, 0, 0);
									vert[3].pos = Rainbow::Vector4f(86.603f, 50, 86.603f, 0);
									vert[4].pos = Rainbow::Vector4f(86.603f, 50, 0, 0);
									vert[5].pos = Rainbow::Vector4f(100, 100, 100, 0);
									vert[6].pos = Rainbow::Vector4f(100, 100, 0, 0);

									uv[0] = { 0, 1 };
									uv[1] = { 0.5f, 0.6667f };
									uv[2] = { 0, 0.6667f };
									uv[3] = { 0.86603f, 0.3333f };
									uv[4] = { 0, 0.3333f };
									uv[5] = { 1, 0 };
									uv[6] = { 0, 0 };
								}

								Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5]).toVector3();
								//unsigned short dir_color = Normal2LightColor(normalVec);
								BlockVector vertcolor;
								vertcolor.v = 0xffffffff;
								vertcolor.w = 0;
								Rainbow::Normalize(normalVec);
								BlockVector normal_dir = PackVertNormal(normalVec);
								for (int oo = 0; oo < 7; oo++)
								{
									vert[oo].uv = { short(uv[oo].x * BLOCKUV_SCALE), short(uv[oo].y * BLOCKUV_SCALE) };
									vert[oo].normal = normal_dir;
									vert[oo].color.SetUInt32(vertcolor.v);
									vertices.push_back(vert[oo]);
								}
								m_mTurnArcFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							}
						}
						else
						{
							for (int o = 2; o <= 3; o++)
							{
								dynamic_array<BlockGeomVert> vertices;
								BlockGeomVert vert[7];
								Rainbow::Vector2f uv[7];
								if (o == 2)//1012
								{
									vert[0].pos = Rainbow::Vector4f(100, 100, 0, 0);
									vert[1].pos = Rainbow::Vector4f(86.603f, 50, 0, 0);
									vert[2].pos = Rainbow::Vector4f(86.603f, 50, 13.397f, 0);
									vert[3].pos = Rainbow::Vector4f(50, 13.397f, 0, 0);
									vert[4].pos = Rainbow::Vector4f(50, 13.397f, 50, 0);
									vert[5].pos = Rainbow::Vector4f(0, 0, 0, 0);
									vert[6].pos = Rainbow::Vector4f(0, 0, 100, 0);

									uv[0] = { 0, 0 };
									uv[1] = { 0, 0.3333f };
									uv[2] = { 0.13397f, 0.3333f };
									uv[3] = { 0, 0.6667f };
									uv[4] = { 0.5f, 0.6667f };
									uv[5] = { 0, 1 };
									uv[6] = { 1, 1 };
								}
								else//1013
								{
									vert[0].pos = Rainbow::Vector4f(100, 100, 100, 0);
									vert[1].pos = Rainbow::Vector4f(86.603f, 50, 86.603f, 0);
									vert[2].pos = Rainbow::Vector4f(86.603f, 50, 100, 0);
									vert[3].pos = Rainbow::Vector4f(50, 13.397f, 50, 0);
									vert[4].pos = Rainbow::Vector4f(50, 13.397f, 100, 0);
									vert[5].pos = Rainbow::Vector4f(0, 0, 0, 0);
									vert[6].pos = Rainbow::Vector4f(0, 0, 100, 0);

									uv[0] = { 1, 0 };
									uv[1] = { 0.86603f, 0.3333f };
									uv[2] = { 1, 0.3333f };
									uv[3] = { 0.5f, 0.6667f };
									uv[4] = { 1, 0.6667f };
									uv[5] = { 0, 1 };
									uv[6] = { 1, 1 };
								}

								Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5]).toVector3();
								//unsigned short dir_color = Normal2LightColor(normalVec);
								BlockVector vertcolor;
								vertcolor.v = 0xffffffff;
								vertcolor.w = 0;
								Rainbow::Normalize(normalVec);
								BlockVector normal_dir = PackVertNormal(normalVec);
								for (int oo = 0; oo < 7; oo++)
								{
									vert[oo].uv = { short(uv[oo].x * BLOCKUV_SCALE), short(uv[oo].y * BLOCKUV_SCALE) };
									vert[oo].normal = normal_dir;
									vert[oo].color.SetUInt32(vertcolor.v);
									vertices.push_back(vert[oo]);
								}
								m_mTurnArcFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							}
						}
					}
				}
				else if (2 == k)
				{
					if (0 == i)
					{
						if (1 == j)
						{
							for (int o = 0; o <= 1; o++)
							{
								dynamic_array<BlockGeomVert> vertices;
								BlockGeomVert vert[7];
								Rainbow::Vector2f uv[7];
								if (o == 0)//0120
								{
									vert[0].pos = Rainbow::Vector4f(100, 100, 100, 0);
									vert[1].pos = Rainbow::Vector4f(50, 86.603f, 50, 0);
									vert[2].pos = Rainbow::Vector4f(100, 86.603f, 50, 0);
									vert[3].pos = Rainbow::Vector4f(13.397f, 50, 13.397f, 0);
									vert[4].pos = Rainbow::Vector4f(100, 50, 13.397f, 0);
									vert[5].pos = Rainbow::Vector4f(0, 0, 0, 0);
									vert[6].pos = Rainbow::Vector4f(100, 0, 0, 0);

									uv[0] = { 1, 0 };
									uv[1] = { 0.5f, 0.3333f };
									uv[2] = { 1, 0.3333f };
									uv[3] = { 0.13397f, 0.6667f };
									uv[4] = { 1, 0.6667f };
									uv[5] = { 0, 1 };
									uv[6] = { 1, 1 };
								}
								else//0121
								{
									vert[0].pos = Rainbow::Vector4f(0, 100, 100, 0);
									vert[1].pos = Rainbow::Vector4f(0, 86.603f, 50, 0);
									vert[2].pos = Rainbow::Vector4f(50, 86.603f, 50, 0);
									vert[3].pos = Rainbow::Vector4f(0, 50, 13.397f, 0);
									vert[4].pos = Rainbow::Vector4f(86.603f, 50, 13.397f, 0);
									vert[5].pos = Rainbow::Vector4f(0, 0, 0, 0);
									vert[6].pos = Rainbow::Vector4f(100, 0, 0, 0);

									uv[0] = { 0, 0 };
									uv[1] = { 0, 0.3333f };
									uv[2] = { 0.5f, 0.3333f };
									uv[3] = { 0, 0.6667f };
									uv[4] = { 0.86603f, 0.6667f };
									uv[5] = { 0, 1 };
									uv[6] = { 1, 1 };
								}

								Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5]).toVector3();
								//unsigned short dir_color = Normal2LightColor(normalVec);
								BlockVector vertcolor;
								vertcolor.v = 0xffffffff;
								vertcolor.w = 0;
								Rainbow::Normalize(normalVec);
								BlockVector normal_dir = PackVertNormal(normalVec);
								for (int oo = 0; oo < 7; oo++)
								{
									vert[oo].uv = { short(uv[oo].x * BLOCKUV_SCALE), short(uv[oo].y * BLOCKUV_SCALE) };
									vert[oo].normal = normal_dir;
									vert[oo].color.SetUInt32(vertcolor.v);
									vertices.push_back(vert[oo]);
								}
								m_mTurnArcFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							}
						}
						else
						{
							for (int o = 0; o <= 1; o++)
							{
								dynamic_array<BlockGeomVert> vertices;
								BlockGeomVert vert[7];
								Rainbow::Vector2f uv[7];
								if (o == 0)//0020
								{
									vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
									vert[1].pos = Rainbow::Vector4f(13.397f, 50, 13.397f, 0);
									vert[2].pos = Rainbow::Vector4f(0, 50, 13.397f, 0);
									vert[3].pos = Rainbow::Vector4f(50, 86.603f, 50, 0);
									vert[4].pos = Rainbow::Vector4f(0, 86.603f, 50, 0);
									vert[5].pos = Rainbow::Vector4f(100, 100, 100, 0);
									vert[6].pos = Rainbow::Vector4f(0, 100, 100, 0);

									uv[0] = { 0, 1 };
									uv[1] = { 0.13397f, 0.6667f };
									uv[2] = { 0, 0.6667f };
									uv[3] = { 0.5f, 0.3333f };
									uv[4] = { 0, 0.3333f };
									uv[5] = { 1, 0 };
									uv[6] = { 0, 0 };
								}
								else//0021
								{
									vert[0].pos = Rainbow::Vector4f(100, 0, 0, 0);
									vert[1].pos = Rainbow::Vector4f(100, 50, 13.397f, 0);
									vert[2].pos = Rainbow::Vector4f(86.603f, 50, 13.397f, 0);
									vert[3].pos = Rainbow::Vector4f(100, 86.603f, 50, 0);
									vert[4].pos = Rainbow::Vector4f(50, 86.603f, 50, 0);
									vert[5].pos = Rainbow::Vector4f(100, 100, 100, 0);
									vert[6].pos = Rainbow::Vector4f(0, 100, 100, 0);

									uv[0] = { 1, 1 };
									uv[1] = { 1, 0.6667f };
									uv[2] = { 0.86603f, 0.6667f };
									uv[3] = { 1, 0.3333f };
									uv[4] = { 0.5, 0.3333f };
									uv[5] = { 1, 0 };
									uv[6] = { 0, 0 };
								}

								Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5]).toVector3();
								//unsigned short dir_color = Normal2LightColor(normalVec);
								BlockVector vertcolor;
								vertcolor.v = 0xffffffff;
								vertcolor.w = 0;
								Rainbow::Normalize(normalVec);
								BlockVector normal_dir = PackVertNormal(normalVec);
								for (int oo = 0; oo < 7; oo++)
								{
									vert[oo].uv = { short(uv[oo].x * BLOCKUV_SCALE), short(uv[oo].y * BLOCKUV_SCALE) };
									vert[oo].normal = normal_dir;
									vert[oo].color.SetUInt32(vertcolor.v);
									vertices.push_back(vert[oo]);
								}
								m_mTurnArcFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							}
						}
					}
					else
					{
						if (1 == j)
						{
							for (int o = 0; o <= 1; o++)
							{
								dynamic_array<BlockGeomVert> vertices;
								BlockGeomVert vert[7];
								Rainbow::Vector2f uv[7];
								if (o == 0)//1120
								{
									vert[0].pos = Rainbow::Vector4f(100, 0, 100, 0);
									vert[1].pos = Rainbow::Vector4f(100, 13.397f, 50, 0);
									vert[2].pos = Rainbow::Vector4f(50, 13.397f, 50, 0);
									vert[3].pos = Rainbow::Vector4f(100, 50, 13.397f, 0);
									vert[4].pos = Rainbow::Vector4f(13.397f, 50, 13.397f, 0);
									vert[5].pos = Rainbow::Vector4f(100, 100, 0, 0);
									vert[6].pos = Rainbow::Vector4f(0, 100, 0, 0);

									uv[0] = { 1, 1 };
									uv[1] = { 1, 0.6667f };
									uv[2] = { 0.5f, 0.6667f };
									uv[3] = { 1, 0.3333f };
									uv[4] = { 0.13397f, 0.3333f };
									uv[5] = { 1, 0 };
									uv[6] = { 0, 0 };
								}
								else//1121
								{
									vert[0].pos = Rainbow::Vector4f(0, 0, 100, 0);
									vert[1].pos = Rainbow::Vector4f(50, 13.397f, 50, 0);
									vert[2].pos = Rainbow::Vector4f(0, 13.397f, 50, 0);
									vert[3].pos = Rainbow::Vector4f(86.603f, 50, 13.397f, 0);
									vert[4].pos = Rainbow::Vector4f(0, 50, 13.397f, 0);
									vert[5].pos = Rainbow::Vector4f(100, 100, 0, 0);
									vert[6].pos = Rainbow::Vector4f(0, 100, 0, 0);

									uv[0] = { 0, 1 };
									uv[1] = { 0.5f, 0.6667f };
									uv[2] = { 0, 0.6667f };
									uv[3] = { 0.86603f, 0.3333f };
									uv[4] = { 0, 0.3333f };
									uv[5] = { 1, 0 };
									uv[6] = { 0, 0 };
								}

								Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5]).toVector3();
								//unsigned short dir_color = Normal2LightColor(normalVec);
								BlockVector vertcolor;
								vertcolor.v = 0xffffffff;
								vertcolor.w = 0;
								Rainbow::Normalize(normalVec);
								BlockVector normal_dir = PackVertNormal(normalVec);
								for (int oo = 0; oo < 7; oo++)
								{
									vert[oo].uv = { short(uv[oo].x * BLOCKUV_SCALE), short(uv[oo].y * BLOCKUV_SCALE) };
									vert[oo].normal = normal_dir;
									vert[oo].color.SetUInt32(vertcolor.v);
									vertices.push_back(vert[oo]);
								}
								m_mTurnArcFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							}
						}
						else
						{
							for (int o = 0; o <= 1; o++)
							{
								dynamic_array<BlockGeomVert> vertices;
								BlockGeomVert vert[7];
								Rainbow::Vector2f uv[7];
								if (o == 0)//1020
								{
									vert[0].pos = Rainbow::Vector4f(0, 100, 0, 0);
									vert[1].pos = Rainbow::Vector4f(0, 50, 13.397f, 0);
									vert[2].pos = Rainbow::Vector4f(13.397f, 50, 13.397f, 0);
									vert[3].pos = Rainbow::Vector4f(0, 13.397f, 50, 0);
									vert[4].pos = Rainbow::Vector4f(50, 13.397f, 50, 0);
									vert[5].pos = Rainbow::Vector4f(0, 0, 100, 0);
									vert[6].pos = Rainbow::Vector4f(100, 0, 100, 0);

									uv[0] = { 0, 0 };
									uv[1] = { 0, 0.3333f };
									uv[2] = { 0.13397f, 0.3333f };
									uv[3] = { 0, 0.6667f };
									uv[4] = { 0.5f, 0.6667f };
									uv[5] = { 0, 1 };
									uv[6] = { 1, 1 };
								}

								else//1021
								{
									vert[0].pos = Rainbow::Vector4f(100, 100, 0, 0);
									vert[1].pos = Rainbow::Vector4f(86.603f, 50, 13.397f, 0);
									vert[2].pos = Rainbow::Vector4f(100, 50, 13.397f, 0);
									vert[3].pos = Rainbow::Vector4f(50, 13.397f, 50, 0);
									vert[4].pos = Rainbow::Vector4f(100, 13.397f, 50, 0);
									vert[5].pos = Rainbow::Vector4f(0, 0, 100, 0);
									vert[6].pos = Rainbow::Vector4f(100, 0, 100, 0);

									uv[0] = { 1, 0 };
									uv[1] = { 0.86603f, 0.3333f };
									uv[2] = { 1, 0.3333f };
									uv[3] = { 0.5f, 0.6667f };
									uv[4] = { 1, 0.6667f };
									uv[5] = { 0, 1 };
									uv[6] = { 1, 1 };
								}

								Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5]).toVector3();
								//unsigned short dir_color = Normal2LightColor(normalVec);
								BlockVector vertcolor;
								vertcolor.v = 0xffffffff;
								vertcolor.w = 0;
								Rainbow::Normalize(normalVec);
								BlockVector normal_dir = PackVertNormal(normalVec);
								for (int oo = 0; oo < 7; oo++)
								{
									vert[oo].uv = { short(uv[oo].x * BLOCKUV_SCALE), short(uv[oo].y * BLOCKUV_SCALE) };
									vert[oo].normal = normal_dir;
									vert[oo].color.SetUInt32(vertcolor.v);
									vertices.push_back(vert[oo]);
								}
								m_mTurnArcFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							}
						}
					}
				}
				else if (3 == k)
				{
					if (0 == i)
					{
						if (1 == j)
						{
							for (int o = 0; o <= 1; o++)
							{
								dynamic_array<BlockGeomVert> vertices;
								BlockGeomVert vert[7];
								Rainbow::Vector2f uv[7];
								if (o == 0)
								{
									vert[0].pos = Rainbow::Vector4f(100, 100, 0, 0);
									vert[1].pos = Rainbow::Vector4f(100, 86.603f, 50, 0);
									vert[2].pos = Rainbow::Vector4f(50, 86.603f, 50, 0);
									vert[3].pos = Rainbow::Vector4f(100, 50, 86.603f, 0);
									vert[4].pos = Rainbow::Vector4f(13.397f, 50, 86.603f, 0);
									vert[5].pos = Rainbow::Vector4f(100, 0, 100, 0);
									vert[6].pos = Rainbow::Vector4f(0, 0, 100, 0);

									uv[0] = { 0, 0 };
									uv[1] = { 0, 0.3333f };
									uv[2] = { 0.5f, 0.3333f };
									uv[3] = { 0, 0.6667f };
									uv[4] = { 0.86607f, 0.6667f };
									uv[5] = { 0, 1 };
									uv[6] = { 1, 1 };
								}
								else
								{
									vert[0].pos = Rainbow::Vector4f(0, 100, 0, 0);
									vert[1].pos = Rainbow::Vector4f(50, 86.603f, 50, 0);
									vert[2].pos = Rainbow::Vector4f(0, 86.603f, 50, 0);
									vert[3].pos = Rainbow::Vector4f(86.603f, 50, 86.603f, 0);
									vert[4].pos = Rainbow::Vector4f(0, 50, 86.603f, 0);
									vert[5].pos = Rainbow::Vector4f(100, 0, 100, 0);
									vert[6].pos = Rainbow::Vector4f(0, 0, 100, 0);

									uv[0] = { 1, 0 };
									uv[1] = { 0.5f, 0.3333f };
									uv[2] = { 1, 0.3333f };
									uv[3] = { 0.13397f, 0.6667f };
									uv[4] = { 1, 0.6667f };
									uv[5] = { 0, 1 };
									uv[6] = { 1, 1 };

								}

								Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5]).toVector3();
								//unsigned short dir_color = Normal2LightColor(normalVec);
								BlockVector vertcolor;
								vertcolor.v = 0xffffffff;
								vertcolor.w = 0;
								Rainbow::Normalize(normalVec);
								BlockVector normal_dir = PackVertNormal(normalVec);
								for (int oo = 0; oo < 7; oo++)
								{
									vert[oo].uv = { short(uv[oo].x * BLOCKUV_SCALE), short(uv[oo].y * BLOCKUV_SCALE) };
									vert[oo].normal = normal_dir;
									vert[oo].color.SetUInt32(vertcolor.v);
									vertices.push_back(vert[oo]);
								}
								m_mTurnArcFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							}
						}
						else
						{
							for (int o = 0; o <= 1; o++)
							{
								dynamic_array<BlockGeomVert> vertices;
								BlockGeomVert vert[7];
								Rainbow::Vector2f uv[7];
								if (o == 0)//0030
								{
									vert[0].pos = Rainbow::Vector4f(0, 0, 100, 0);
									vert[1].pos = Rainbow::Vector4f(0, 50, 86.603f, 0);
									vert[2].pos = Rainbow::Vector4f(13.397f, 50, 86.603f, 0);
									vert[3].pos = Rainbow::Vector4f(0, 86.603f, 50, 0);
									vert[4].pos = Rainbow::Vector4f(50, 86.603f, 50, 0);
									vert[5].pos = Rainbow::Vector4f(0, 100, 0, 0);
									vert[6].pos = Rainbow::Vector4f(100, 100, 0, 0);

									uv[0] = { 1, 1 };
									uv[1] = { 1, 0.6667f };
									uv[2] = { 0.86603f, 0.6667f };
									uv[3] = { 1, 0.3333f };
									uv[4] = { 0.5f, 0.3333f };
									uv[5] = { 1, 0 };
									uv[6] = { 0, 0 };
								}
								else//0031
								{
									vert[0].pos = Rainbow::Vector4f(100, 0, 100, 0);
									vert[1].pos = Rainbow::Vector4f(86.603f, 50, 86.603f, 0);
									vert[2].pos = Rainbow::Vector4f(100, 50, 86.603f, 0);
									vert[3].pos = Rainbow::Vector4f(50, 86.603f, 50, 0);
									vert[4].pos = Rainbow::Vector4f(100, 86.603f, 50, 0);
									vert[5].pos = Rainbow::Vector4f(0, 100, 0, 0);
									vert[6].pos = Rainbow::Vector4f(100, 100, 0, 0);

									uv[0] = { 0, 1 };
									uv[1] = { 0.13397f, 0.6667f };
									uv[2] = { 0, 0.6667f };
									uv[3] = { 0.5f, 0.3333f };
									uv[4] = { 0, 0.3333f };
									uv[5] = { 1, 0 };
									uv[6] = { 0, 0 };
								}

								Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5]).toVector3();
								//unsigned short dir_color = Normal2LightColor(normalVec);
								BlockVector vertcolor;
								vertcolor.v = 0xffffffff;
								vertcolor.w = 0;
								Rainbow::Normalize(normalVec);
								BlockVector normal_dir = PackVertNormal(normalVec);
								for (int oo = 0; oo < 7; oo++)
								{
									vert[oo].uv = { short(uv[oo].x * BLOCKUV_SCALE), short(uv[oo].y * BLOCKUV_SCALE) };
									vert[oo].normal = normal_dir;
									vert[oo].color.SetUInt32(vertcolor.v);
									vertices.push_back(vert[oo]);
								}
								m_mTurnArcFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							}
						}
					}
					else
					{
						if (1 == j)
						{
							for (int o = 0; o <= 1; o++)
							{
								dynamic_array<BlockGeomVert> vertices;
								BlockGeomVert vert[7];
								Rainbow::Vector2f uv[7];
								if (o == 0)//1130
								{
									vert[0].pos = Rainbow::Vector4f(100, 0, 0, 0);
									vert[1].pos = Rainbow::Vector4f(50, 13.397f, 50, 0);
									vert[2].pos = Rainbow::Vector4f(100, 13.397f, 50, 0);
									vert[3].pos = Rainbow::Vector4f(13.397f, 50, 86.603f, 0);
									vert[4].pos = Rainbow::Vector4f(100, 50, 86.603f, 0);
									vert[5].pos = Rainbow::Vector4f(0, 100, 100, 0);
									vert[6].pos = Rainbow::Vector4f(100, 100, 100, 0);

									uv[0] = { 0, 1 };
									uv[1] = { 0.5, 0.6667f };
									uv[2] = { 0, 0.6667f };
									uv[3] = { 0.86607f, 0.3333f };
									uv[4] = { 0, 0.3333f };
									uv[5] = { 1, 0 };
									uv[6] = { 0, 0 };
								}
								else//1131
								{
									vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
									vert[1].pos = Rainbow::Vector4f(0, 13.397f, 50, 0);
									vert[2].pos = Rainbow::Vector4f(50, 13.397f, 50, 0);
									vert[3].pos = Rainbow::Vector4f(0, 50, 86.603f, 0);
									vert[4].pos = Rainbow::Vector4f(86.603f, 50, 86.603f, 0);
									vert[5].pos = Rainbow::Vector4f(0, 100, 100, 0);
									vert[6].pos = Rainbow::Vector4f(100, 100, 100, 0);

									uv[0] = { 1, 1 };
									uv[1] = { 1, 0.6667f };
									uv[2] = { 0.5, 0.6667f };
									uv[3] = { 1, 0.3333f };
									uv[4] = { 0.13397f, 0.3333f };
									uv[5] = { 1, 0 };
									uv[6] = { 0, 0 };
								}

								Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5]).toVector3();
								//unsigned short dir_color = Normal2LightColor(normalVec);
								BlockVector vertcolor;
								vertcolor.v = 0xffffffff;
								vertcolor.w = 0;
								Rainbow::Normalize(normalVec);
								BlockVector normal_dir = PackVertNormal(normalVec);
								for (int oo = 0; oo < 7; oo++)
								{
									vert[oo].uv = { short(uv[oo].x * BLOCKUV_SCALE), short(uv[oo].y * BLOCKUV_SCALE) };
									vert[oo].normal = normal_dir;
									vert[oo].color.SetUInt32(vertcolor.v);
									vertices.push_back(vert[oo]);
								}
								m_mTurnArcFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							}
						}
						else
						{
							for (int o = 0; o <= 1; o++)
							{
								dynamic_array<BlockGeomVert> vertices;
								BlockGeomVert vert[7];
								Rainbow::Vector2f uv[7];
								if (o == 0)//1030
								{
									vert[0].pos = Rainbow::Vector4f(0, 100, 100, 0);
									vert[1].pos = Rainbow::Vector4f(13.397f, 50, 86.603f, 0);
									vert[2].pos = Rainbow::Vector4f(0, 50, 86.603f, 0);
									vert[3].pos = Rainbow::Vector4f(50, 13.397f, 50, 0);
									vert[4].pos = Rainbow::Vector4f(0, 13.397f, 50, 0);
									vert[5].pos = Rainbow::Vector4f(100, 0, 0, 0);
									vert[6].pos = Rainbow::Vector4f(0, 0, 0, 0);

									uv[0] = { 1, 0 };
									uv[1] = { 0.86607f, 0.3333f };
									uv[2] = { 1, 0.3333f };
									uv[3] = { 0.5f, 0.6667f };
									uv[4] = { 1, 0.6667f };
									uv[5] = { 0, 1 };
									uv[6] = { 1, 1 };
								}
								else//1031
								{
									vert[0].pos = Rainbow::Vector4f(100, 100, 100, 0);
									vert[1].pos = Rainbow::Vector4f(100, 50, 86.603f, 0);
									vert[2].pos = Rainbow::Vector4f(86.603f, 50, 86.603f, 0);
									vert[3].pos = Rainbow::Vector4f(100, 13.397f, 50, 0);
									vert[4].pos = Rainbow::Vector4f(50, 13.397f, 50, 0);
									vert[5].pos = Rainbow::Vector4f(100, 0, 0, 0);
									vert[6].pos = Rainbow::Vector4f(0, 0, 0, 0);

									uv[0] = { 0, 0 };
									uv[1] = { 0, 0.3333f };
									uv[2] = { 0.13397f, 0.3333f };
									uv[3] = { 0, 0.6667f };
									uv[4] = { 0.5f, 0.6667f };
									uv[5] = { 0, 1 }; 
									uv[6] = { 1, 1 };
								}

								Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5]).toVector3();
								//unsigned short dir_color = Normal2LightColor(normalVec);
								BlockVector vertcolor;
								vertcolor.v = 0xffffffff;
								vertcolor.w = 0;
								Rainbow::Normalize(normalVec);
								BlockVector normal_dir = PackVertNormal(normalVec);
								for (int oo = 0; oo < 7; oo++)
								{
									vert[oo].uv = { short(uv[oo].x * BLOCKUV_SCALE), short(uv[oo].y * BLOCKUV_SCALE) };
									vert[oo].normal = normal_dir;
									vert[oo].color.SetUInt32(vertcolor.v);
									vertices.push_back(vert[oo]);
								}
								m_mTurnArcFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							}
						}
					}
				}
			}
		}
	}
}

void ArcPlateMaterial::initPhyModelData()
{
	if (m_mArcWholeFace().size() == 0 || m_mDiamondFace.size() == 0 || m_mBigArcFace.size() == 0)
	{
		return;
	}
	int triangleFaceId[4] = { 6,2,4,0 };
	for (int i = 0; i < 2; i++)
	{
		for (int j = 0; j < 4; j++)
		{
			ArcPhyModel model;
			dynamic_array<dynamic_array<BlockGeomVert>*> faceList;
			//4 vert
			faceList.push_back(0 == i ? &m_mArcWholeFace()[4] : &m_mArcWholeFace()[5]);
			faceList.push_back(&m_mArcWholeFace()[ReverseDirection(j)]);
			faceList.push_back(&m_mDiamondFace[8 * i + triangleFaceId[j]]);
			faceList.push_back(&m_mBigArcFace[4 * i + j]);
			faceList.push_back(&m_mDiamondFace[8 * i + triangleFaceId[j] + 1]);

			int ArcCount = 0;
			for (auto face : faceList)
			{
				dynamic_array<short> indexlist;
				for (auto& vertdata : (*face))
				{
					Rainbow::Vector3f pos(vertdata.pos.x, vertdata.pos.y, vertdata.pos.z);
					short index = -1;
					for (int o = 0; o < model.verts.size(); o++)
					{
						if (model.verts[o] == pos)
						{
							index = o;
							break;
						}
					}
					if (index == -1)
					{
						index = (short)model.verts.size();
						model.verts.push_back(pos);
					}
					indexlist.push_back(index);
				}
				dynamic_array<UInt16>* posIndeices = m_dPosIndices;
				if (indexlist.size() == 5)
				{
					posIndeices = m_PosDiamondIndices;
				}
				else if (indexlist.size() == 8)
				{
					posIndeices = m_PosBigArcIndices;
				}
				ArcCount += indexlist.size() - 2;
				for (auto& idxex : (*posIndeices))
				{
					model.idxs.push_back(indexlist[idxex]);
				}
			}
			model.ArcCount = ArcCount;
			m_mPhyModel.insert(make_pair(i * 4 + j, model));
		}
	}

	int speicalFaceId[4] = { 4,0,4,0 };
	for (int i = 0; i < 2; i++)//0 down 1 up
	{
		for (int j = 0; j < 2; j++)//0 凹 1 凸
		{
			for (int k = 0; k < 4; k++)
			{
				int dirlist[2] = { RotateDirPos90(k), RotateDir90(k) };
				for (int q = 0; q < 2; q++)
				{
					auto iter = m_mPhyModel.find(10000 + i * 1000 + j * 100 + k * 10 + dirlist[q]);
					if (iter == m_mPhyModel.end())
					{
						ArcPhyModel model;
						dynamic_array<dynamic_array<BlockGeomVert>*> faceList;
						faceList.push_back(0 == i ? &m_mArcWholeFace()[4] : &m_mArcWholeFace()[5]);
						if (1 == j)
						{
							faceList.push_back(&m_mDiamondFace[8 * i + speicalFaceId[k] + ReverseDirection(dirlist[q])]);
						}
						else
						{
							faceList.push_back(&m_mDiamondFace[8 * i + speicalFaceId[k] + dirlist[q]]);
							faceList.push_back(&m_mArcWholeFace()[ReverseDirection(k)]);
							faceList.push_back(&m_mArcWholeFace()[ReverseDirection(dirlist[q])]);
						}
						faceList.push_back(&m_mTurnArcFace[i * 1000 + j * 100 + k * 10 + dirlist[q]]);
						faceList.push_back(&m_mTurnArcFace[i * 1000 + j * 100 + dirlist[q] * 10 + k]);

						int ArcCount = 0;
						for (auto face : faceList)
						{
							dynamic_array<short> indexlist;
							for (auto& vertdata : (*face))
							{
								Rainbow::Vector3f pos(vertdata.pos.x, vertdata.pos.y, vertdata.pos.z);
								short index = -1;
								for (int o = 0; o < model.verts.size(); o++)
								{
									if (model.verts[o] == pos)
									{
										index = o;
										break;
									}
								}
								if (index == -1)
								{
									index = (short)model.verts.size();
									model.verts.push_back(pos);
								}
								indexlist.push_back(index);
							}
							dynamic_array<UInt16>* posIndeices = m_dPosIndices;
							if (indexlist.size() == 5)
							{
								posIndeices = m_PosDiamondIndices;
							}
							else if (indexlist.size() == 7)
							{
								posIndeices = m_TurnArcIndices;
							}
							ArcCount += indexlist.size() - 2;
							for (auto& idxex : (*posIndeices))
							{
								model.idxs.push_back(indexlist[idxex]);
							}
						}
						model.ArcCount = ArcCount;
						m_mPhyModel.insert(make_pair(10000 + i * 1000 + j * 100 + k * 10 + dirlist[q], model));
					}
				}
			}
		}
	}
}

SectionMesh* ArcPlateMaterial::createBlockProtoMesh(int protodata)
{
	SectionMesh* pmesh = ENG_NEW(SectionMesh)();
	int blockdata = protodata;

	const BiomeDef* biome = GetDefManagerProxy()->getBiomeDef(0);
	//  	dynamic_array<UInt16>* indices = &m_dPosIndices;

	char list[5] = { 3,4,4,5,2 };
	BlockColor facecolor(255, 255, 255, 0);
	for (int i = 0; i < 5; i++)
	{
		DirectionType dir = (DirectionType)(list[i] % 4);
		RenderBlockMaterial* pmtl = getFaceMtl(biome, dir, blockdata, facecolor);
		SectionSubMesh* psubmesh = pmesh->getSubMesh(pmtl, true);

		BlockGeomMeshInfo meshinfo;
		if (i < 2)
		{
			meshinfo.vertices = m_mArcWholeFace()[list[i]];
			meshinfo.indices = *m_dPosIndices;
		}
		else if (i < 4)
		{
			meshinfo.vertices = m_mDiamondFace[list[i]];
			meshinfo.indices = *m_PosDiamondIndices;
		}
		else
		{
			meshinfo.vertices = m_mBigArcFace[list[i]];
			meshinfo.indices = *m_PosBigArcIndices;
		}
		if (psubmesh)
			psubmesh->addGeomFaceLight(meshinfo, NULL, s_DefaultFaceVertexLights, &facecolor);
	}
	return pmesh;
}

typedef ArcPlateMaterial::BlockInstance ArcPlateMaterialInstance;
IMPLEMENT_SCENEOBJECTCLASS(ArcPlateMaterialInstance)
MNSandbox::ReflexClassParam<ArcPlateMaterial::BlockInstance, int> ArcPlateMaterialInstance::R_Dir(0, "Dir", "Block", &ArcPlateMaterial::BlockInstance::GetBlockData, &ArcPlateMaterial::BlockInstance::SetBlockData);