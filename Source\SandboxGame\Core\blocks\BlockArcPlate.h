
#ifndef __BLOCK_ARC_PLATE_H__
#define __BLOCK_ARC_PLATE_H__

#include "BlockMaterial.h"

struct ArcPhyModel
{
	dynamic_array<Rainbow::Vector3f> verts;
	dynamic_array<UInt16> idxs;
	int ArcCount;
	ArcPhyModel() :
		ArcCount(0)
	{
	}
};
// struct mapless
// {
// 	bool  operator()(const Rainbow::Vector4f& _Left, const Rainbow::Vector4f& _Right) const {
// 		return _Left == _Right;
// 	}
// };
// bool mapcompare(const Rainbow::Vector4f& _Left, const Rainbow::Vector4f& _Right)
// {
// 	return _Left == _Right;
// }

class ArcPlateMaterial : public CubeBlockMaterial //tolua_exports
{ //tolua_exports
	//typedef CubeBlockMaterial Super;
	DECLARE_SCENEOBJECTCLASS(ArcPlateMaterial)
	//DECLARE_BLOCKINSTANCE(ArcPlateMaterial)
public:
	ArcPlateMaterial();
	virtual ~ArcPlateMaterial();
	//tolua_begin
	virtual void init(int resid) override;

	void initWholeFaceVertData();
	virtual void initVertData();
	virtual void initBigArcFaceVertData();
	virtual void initTurnArcFaceVertData();
	virtual void initDiamondFaceVertData();
	virtual void initPhyModelData();
	//virtual bool isOpaqueCube()
	//{
	//	return false;
	//}
	virtual bool hasSolidTopSurface(int blockdata);
	virtual bool canAttachedToMecha(int curblockdata, DirectionType dir, int neighbor_id, int neighbor_data) override;
	virtual void dropBlockAsItem(World* pworld, const WCoord& blockpos, int blockdata = 0, BLOCK_MINE_TYPE droptype = BLOCK_MINE_NOTOOL, float chance = 1.0f, int uin = -1) override;
	virtual int getPhisicMeshVerts(BaseSection* psection, const WCoord& blockpos, dynamic_array<Rainbow::Vector3f>& verts, dynamic_array<UInt16>& idxs);
	BLOCK_RENDERTYPE_T GetAttrRenderType() const;/* 渲染类型 */
	virtual void onBlockPlacedBy(World* pworld, const WCoord& blockpos, IClientPlayer* player);
	virtual void createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh) override;
	virtual SectionMesh* createBlockProtoMesh(int protodata = 0);
	virtual void createCollideData(CollisionDetect* coldetect, World* pworld, const WCoord& blockpos);
	virtual bool coverNeighbor(int curblockdata, SolidBlockMaterial* neighbor, int neighbor_data, DirectionType dir) override;
	void drawLine(const Rainbow::Vector3f& point1, const Rainbow::Vector3f& point2, World* pworld);
	void drawBox(WCoord min, WCoord max);
	unsigned char TriangleNormal2LightColor(const Rainbow::Vector3f& normal);
	virtual int convertDataByRotate(int blockdata, int rotatetype);
	virtual BlockMaterial::BlockType BlockTypeId() { return BlockMaterial::BlockType::BlockType_SlantBlock; }
	// 	static void initAllWholeTriangleIndex(void* data);
	// 	static void uninitAllWholeTriangleIndex(void* data);
	virtual bool canSprayPaint(World* pworld, const WCoord& blockpos);
public:
	static dynamic_array<UInt16>* m_dNegIndices;
	static dynamic_array<UInt16>* m_dPosIndices;
	static dynamic_array<UInt16>* m_NegTrIndices;
	static dynamic_array<UInt16>* m_PosTrIndices;
	static dynamic_array<UInt16>* m_PosDiamondIndices;
	static dynamic_array<UInt16>* m_PosBigArcIndices; 
	static dynamic_array<UInt16>* m_PosBigDiamondIndices;
	static dynamic_array<UInt16>* m_TurnArcIndices;
	static Rainbow::Vector3f* ms_LightVec;
private:
	virtual float getBlockHeight(int blockdata);

protected:

	static dynamic_array<dynamic_array<BlockGeomVert>>& m_mArcWholeFace();

	dynamic_array<dynamic_array<BlockGeomVert>> m_mTriangleFace;
	dynamic_array<dynamic_array<BlockGeomVert>> m_mSlantFace;
	dynamic_array<dynamic_array<BlockGeomVert>> m_mBigArcFace;
	std::map<int, dynamic_array<BlockGeomVert>>  m_mTurnArcFace;
	dynamic_array<dynamic_array<BlockGeomVert>> m_mDiamondFace;
	// 	std::map<Rainbow::Vector4f, dynamic_array<BlockGeomVert>, std::less_equal<Rainbow::Vector4f>> m_mTurnSlantFace;
	std::map<int, dynamic_array<BlockGeomVert>> m_mTurnSlantFace;

	std::map<unsigned short, ArcPhyModel> m_mPhyModel;
public:
	class BlockInstance : public Super::BlockInstance
	{
		DECLARE_SCENEOBJECTCLASS(BlockInstance)
	public:
		static MNSandbox::ReflexClassParam<BlockInstance, int> R_Dir;
	};
	virtual MNSandbox::AutoRef<BlockMaterial::BlockInstance> GetBlockInstance() override
	{
		return ArcPlateMaterial::BlockInstance::NewInstance();
	}
}; //tolua_exports

#endif