
#include "BlockCanvas.h"
#include "BlockMaterialMgr.h"
#include "Collision.h"
#include "section.h"
#include "SectionMesh.h"
#include "BlockGeom.h"
#include "Collision.h"
#include "world.h"
#include "Environment.h"
#include "ActorManagerInterface.h"
#include "IClientPlayer.h"
#include "IClientActor.h"
#include "ChunkGenerator.h"
//#include "GameEvent.h"
#include "DefManagerProxy.h"
#include "VehicleWorld.h"
#include "ActorVehicleAssemble.h"
//#include "ActorVillager.h"
#include "SandboxIdDef.h"
#include "ClientPlayer.h"
#include "ActorVillager.h"
using namespace MINIW;

const static int sCanvasExtendPos[4][4][2] = {
		{
			{0, 0}, {1, 0}, {0, -1}, {1, -1},
		},
		{
			{0, 0}, {-1, 0}, {0, 1}, {-1, 1},
		},
		{
			{0, 0}, {1, 0}, {0, 1}, {1, 1},
		},
		{
			{0, 0}, {-1, 0}, {0, -1}, {-1, -1},
		},

};

//相对核心方块的位置偏移，，从门口向其他位置搜索
const static int sCanvasStandPos[4][12][2] = {
		{
			{-1, 0},{-1, -1},{-1, 1}, {-1, -2},{0, 1}, {0, -2}, {1, 1}, {1, -2},{2, 1},{2, -2},{2, 0}, {2, -1}
		},
		{
			{1, 0},{1, 1},{1, -1}, {1, 2},{0, -1}, {0, 2}, {1, -1}, {1, 2},{2, -1},{2, 0},{2, 1}, {2, 2}
		},
		{
			{0, -1},{1, -1},{-1, -1}, {2, -1},{-1, 0}, {2, 0}, {-1, 1}, {2, 1},{-1, 2},{0, 2},{1, 2}, {2, 2}
		},
		{
			{0, 1},{-1, 1},{1, 1}, {-2, 1},{1, 0}, {-2, 0}, {1, -1}, {-2, -1},{1, -2},{0, -2},{-1, -2}, {-2, -2}
		},
};

const static int sSleepPosOffset[4][2] = {
		{
			50,0
		},
		{
			50,100
		},
		{
			100,50
		},
		{
			0,50
		},
};

const static int sEffectPosOffset[4][2] = {
		{
			0,0
		},
		{
			100,100
		},
		{
			100,0
		},
		{
			0,100
		},
};


IMPLEMENT_BLOCKMATERIAL(BlockCanvas)

BlockCanvas::BlockCanvas()
{

}

int BlockCanvas::getBlockGeomID(int* idbuf, int* dirbuf, const SectionDataHandler* psection, const WCoord& blockpos, World* world)
{
	if (psection == NULL || idbuf == NULL || dirbuf == NULL)
	{
		return 0;
	}

	if (world == NULL || world->getContainerMgr() == NULL)
	{
		return 0;
	}

	WorldCanvas* container = dynamic_cast<WorldCanvas*>(world->getContainerMgr()->getContainer(psection->getOrigin() + blockpos)); //sureContainer会造成线程冲突
	if (container == NULL)
	{
		return 0;
	}

	char stage = container->getStage();
	bool isSleep = haveActorSleep(world, psection->getOrigin() + blockpos);
	
	if (stage == 0)
	{
		int blockdata = psection->getBlock(blockpos).getData();
		int dir = blockdata & 3;

		dirbuf[0] = dir;
		idbuf[0] = isSleep ? 1: 0;

		container->needPlaySleepEffect(isSleep);

		return 1;
	}
	else if(stage == 1)
	{
		int blockdata = psection->getBlock(blockpos).getData();
		int dir = blockdata & 3;

		dirbuf[0] = dir;
		idbuf[0] = isSleep ? 3 : 2;;

		container->needPlaySleepEffect(isSleep);

		return 1;
	}
	else {
		int blockdata = psection->getBlock(blockpos).getData();
		int dir = blockdata & 3;

		dirbuf[0] = dir;
		idbuf[0] = 4;
	
		container->needPlaySleepEffect(false);
		return 1;
	}
}

//const char *BlockCanvas::getGeomName()
//{
//	return m_Def->Texture2.c_str();
//}
void BlockCanvas::initGeomName()
{
	m_geomName = m_Def->Texture2.c_str();
}

bool BlockCanvas::onTrigger(World* pworld, const WCoord& blockpos, DirectionType face, IClientPlayer* player, const Rainbow::Vector3f& colpoint)
{
	if (!player || !pworld)
	{
		return false;
	}

	if (pworld->isRemoteMode())
	{
		return true;
	}

	int blockData = pworld->getBlockData(blockpos);
	if (!isCoreBlock(pworld, blockpos))
	{
		WCoord basePos = getCoreBlockPos(pworld, blockpos);
		if (basePos.y < 0)
		{
			return false;
		}

		return onTrigger(pworld, basePos, face, player, colpoint);
	}

	WorldCanvas* container = sureContainer(pworld, blockpos);
	if (container == NULL)
	{
		return 0;
	}

	char stage = container->getStage();
	if (stage == 2)
	{
		return true;
	}

	if (haveActorSleep(pworld, blockpos))
	{
		return true;
	}

	int ret = doSleep(pworld, player, blockpos);

	if (ret == 0)
	{
		setBedOccupied(pworld, blockpos, true);
	}
	else
	{
		player->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, ret);
	}

	return true;
}


void  BlockCanvas::init(int resid)
{
	ModelBlockMaterial::init(resid);
	if (BlockMaterial::m_LoadOnlyLogic) return;

	getDefaultMtl()->setItemMtlOpaque(true);

	SetToggle(BlockToggle_HasContainer, true);
}

void BlockCanvas::createCollideData(CollisionDetect *coldetect, World *pworld, const WCoord &blockpos)
{
	WCoord origin = blockpos * BLOCK_SIZE;
	coldetect->addObstacle(origin, origin + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
}

WorldCanvas* BlockCanvas::createContainer(World* pworld, const WCoord& blockpos)
{
	if (isCoreBlock(pworld, blockpos))
	{
		return SANDBOX_NEW(WorldCanvas, blockpos);
	}
	else
	{
		return nullptr;
	}
}

void BlockCanvas::createBlockMesh(const BuildSectionMeshData& psection, const WCoord& blockpos, SectionMesh* poutmesh)
{
	
	auto data = psection.m_SharedSectionData->getBlock(blockpos).getData();
	
	if (data & 4)
		return;

	Super::createBlockMesh(psection,blockpos,poutmesh);
}

void BlockCanvas::onBlockPlacedBy(World* pworld, const WCoord& blockpos, IClientPlayer* player)
{
	if (pworld == NULL || player == NULL)
	{
		return;
	}

	ModelBlockMaterial::onBlockPlacedBy(pworld, blockpos, player);
	int placeDir = player->GetPlayerCurPlaceDir();
	if (placeDir == DIR_NEG_Y || placeDir == DIR_POS_Y)
	{
		placeDir = DIR_NEG_X;
	}
	pworld->setBlockData(blockpos, placeDir);
}

int BlockCanvas::getPlaceBlockDataByPlayer(World* pworld, IClientPlayer* player)
{
	if (player)
		return player->GetPlayerCurPlaceDir();

	return 0;
}

int BlockCanvas::getPlaceBlockData(World *pworld, const WCoord &blockpos, DirectionType face, float hitptx, float hitpty, float hitptz, int def_blockdata)
{
	int placeDir = face;
	if (placeDir == DIR_NEG_Y || placeDir == DIR_POS_Y)
	{
		placeDir = DIR_NEG_X;
	}
	return placeDir;
}

void BlockCanvas::onBlockAdded(World* pworld, const WCoord& blockpos)
{
	if (pworld == NULL)
	{
		return;
	}

	// 这里只有主机会执行
	int blockData = pworld->getBlockData(blockpos);
	if (isCoreBlock(pworld, blockpos))
	{
		ModelBlockMaterial::onBlockAdded(pworld, blockpos);

		int placeDir = blockData & 3;
		for (int i = 0; i < 4; i++)
		{
			for (int y = 0; y < 2; y++)
			{
				auto curPos = blockpos + WCoord(sCanvasExtendPos[placeDir][i][0], y, sCanvasExtendPos[placeDir][i][1]);
				if (curPos == blockpos)
					continue;

				pworld->setBlockAll(curPos, m_BlockResID, 4 | placeDir);
			}
		}
	}
}

void BlockCanvas::onBlockRemoved(World *pworld, const WCoord &blockpos, int blockid, int blockdata)
{
	if (pworld == NULL)
	{
		return;
	}
	if (blockdata == 12)  //表示不用再处理remove事件
		return;

	int placeDir = blockdata & 3;
	if (blockdata & 4)  //普通的方块
	{
		WCoord basePos = getCoreBlockPos(pworld, blockpos, blockdata);
		if (basePos.y >= 0)
		{
			pworld->setBlockAir(basePos);
		}
	}
	else
	{
		WorldCanvas* canvasContainer = dynamic_cast<WorldCanvas*>(pworld->getContainerMgr()->getContainer(blockpos));
		if (canvasContainer)
		{
			canvasContainer->stopBedEffectByBlockdata(blockdata);

			//同时清理世界数据记录
			if (g_WorldMgr && canvasContainer->getBindActor())
			{
				if (g_WorldMgr->getWorldInfoManager()->getVillageBedStatus(blockpos, canvasContainer->getBindActor()) != ENUM_BED_STATUS_NO_BIND_ACOTR)
				{
					g_WorldMgr->getWorldInfoManager()->removeVillageBedRelationship(canvasContainer->getBindActor());
				}
			}
			//玩家
			if (BedLogicHandle::IsBedOccupied(pworld, blockpos, blockdata))
			{
				WCoord sleepPos = BedLogicHandle::getSleepPosition(pworld, blockpos);
				auto actorMgr = dynamic_cast<ActorManager*>(pworld->getActorMgr());
				if (actorMgr)
				{
					auto player = actorMgr->getOccupiedPlayer(CoordDivBlock(sleepPos), ACTORFLAG_SLEEP);
					if (player)
					{
						player->wakeUp(true, false, false);
						WCoord newpos = TopCoord(blockpos);
						player->getLocoMotion()->gotoPosition(BlockCenterCoord(newpos), player->getLocoMotion()->m_RotateYaw, player->getLocoMotion()->m_RotationPitch);
					}
				}
				
			}
		}

		pworld->getContainerMgr()->destroyContainer(blockpos);
		ModelBlockMaterial::onBlockRemoved(pworld, blockpos, blockid, blockdata);
		clearNormalBlock(pworld, blockpos, blockdata);
	}

}

//bool BlockCanvas::hasContainer() const
//{
//	return true;
//}

void BlockCanvas::clearNormalBlock(World* pworld, const WCoord& blockpos, int blockdata /* = -1 */)
{
	if (blockdata == -1)
		blockdata = pworld->getBlockData(blockpos);
	int placeDir = blockdata & 3;

	for (int i = 0; i < 4; i++)
	{
		for (int y = 0; y < 2; y++)
		{
			auto curPos = blockpos + WCoord(sCanvasExtendPos[placeDir][i][0], y, sCanvasExtendPos[placeDir][i][1]);
			if (curPos == blockpos)
				continue;

			int blockid = pworld->getBlockID(curPos);
			if (blockid == m_BlockResID && !isCoreBlock(pworld, curPos))
			{
				pworld->setBlockData(curPos, 12, 0);
				pworld->setBlockAir(curPos);
			}
		}
	}
}

bool BlockCanvas::IsBedOccupied(World* pworld, const WCoord& blockpos, int blockdata)
{
	return (getCoreBlockData(pworld,blockpos) & 8) != 0;
}

void BlockCanvas::setBedOccupied(World* pworld, const WCoord& blockpos, bool occupied)
{
	int oldData = getCoreBlockData(pworld, blockpos);
	int newData = occupied ? oldData | 8 : oldData&(~8);
	setCoreBlockData(pworld, blockpos, newData);
}

inline bool CanStandOnBlock(World* pworld, int x, int y, int z)
{
	int blockid = pworld->getBlockID(x, y, z);
	BlockDef* def = GetDefManagerProxy()->getBlockDef(blockid);
	if (def) {
		return def->MoveCollide != 1;
	}
	else {
		return false;
	}
}

bool BlockCanvas::getNearestEmptyChunkCoordinates(WCoord& ret, World* pworld, const WCoord& blockpos, int loopcount)
{
	int blockdata = pworld->getBlockData(blockpos);
	int dir = blockdata & 3;

	WCoord corePos = getCoreBlockPos(pworld, blockpos);

	for (int i = 0; i < 12; i++)
	{
		int x = corePos.x + sCanvasStandPos[dir][i][0];
		int z = corePos.z + sCanvasStandPos[dir][i][1];
		int y = corePos.y;
		if (pworld->doesBlockHaveSolidTopSurface(WCoord(x, y - 1, z))
			&& CanStandOnBlock(pworld, x, y, z) && CanStandOnBlock(pworld, x, y + 1, z))
		{
			ret = WCoord(x, blockpos.y, z);
			return true;
		}
	}

	return false;
}

void BlockCanvas::getEyePosInBed(World* pworld, const WCoord& sleeppos, WCoord& eyepos, Rainbow::Vector3f& lookdir)
{
	WCoord blockpos = CoordDivBlock(sleeppos);
	int dir = pworld->getBlockData(blockpos) & 3;
	WCoord dirCoor = g_DirectionCoord[dir];
	WCoord corePos = getCoreBlockPos(pworld, blockpos);

	WCoord rootPos = getSleepPosition(pworld, blockpos);
	//eyepos = rootPos - dirCoor * 120;
	eyepos = rootPos + dirCoor * 200;
	eyepos.y += 50;

	lookdir.x = dirCoor.x;
	lookdir.y = dirCoor.y;
	lookdir.z = dirCoor.z;
	lookdir *= -1;
}

WORLD_ID BlockCanvas::getBindActor(World* pworld, const WCoord& blockpos)
{
	WorldCanvas* container = dynamic_cast<WorldCanvas*>(pworld->getContainerMgr()->getContainer(blockpos));
	if (container)
	{
		return container->getBindActor();
	}
	return 0;
}

void BlockCanvas::setBindActor(World* pworld, const WCoord& blockpos, WORLD_ID bindactor)
{
	WorldCanvas* container = sureContainer(pworld, blockpos);
	container->setBindActor(bindactor);
	container->setBedStatus(ENUM_BED_STATUS_NORMAL);

	//世界也记一份
	if (g_WorldMgr)
	{
		auto actorMgr = dynamic_cast<ActorManager*>(pworld->getActorMgr());
		if (actorMgr)
		{
			ActorVillager* mob = dynamic_cast<ActorVillager*>(actorMgr->findActorByWID(bindactor));
			if (mob && mob->getTamedOwnerID())
			{
				g_WorldMgr->getWorldInfoManager()->changeVillageBedRelationship(mob->getTamedOwnerID(), bindactor, blockpos, ENUM_BED_STATUS_NORMAL);
			}
		}
		
	}
}

WCoord BlockCanvas::getSleepPosition(World* pworld, const WCoord& blockpos)
{
	WCoord corePos = getCoreBlockPos(pworld, blockpos);
	int dir = pworld->getBlockData(blockpos) & 3;
	WCoord sleepPos(corePos.x * 100 + sSleepPosOffset[dir][0], corePos.y * 100, corePos.z * 100 + sSleepPosOffset[dir][1]);
	return sleepPos;
}

WorldContainer* BlockCanvas::getCoreContainer(World* pworld, const WCoord& blockpos)
{
	int blockid = pworld->getBlockID(blockpos);
	if (blockid != BLOCK_CANVAS)
	{
		return NULL;
	}
	WCoord corePos = getCoreBlockPos(pworld, blockpos);
	WorldCanvas* container = dynamic_cast<WorldCanvas*>(pworld->getContainerMgr()->getContainer(corePos));
	return container;
}

WCoord BlockCanvas::getCoreBlockPos(World* pworld, const WCoord& blockpos, int blockdata/* =-1 */)
{
	const int searchDir[4][2] = {
			{-1, 1},
			{1, -1},
			{-1, -1},
			{1, 1},
	};
	if (blockdata == -1)
		blockdata = pworld->getBlockData(blockpos);
	int placeDir = blockdata & 3;
	for (int x = 0; x < 2; x++)
	{
		for (int z = 0; z < 2; z++)
		{
			for (int y = 0; y < 2; y++)
			{
				auto curPos = blockpos + WCoord(searchDir[placeDir][0] * x, -1 * y, searchDir[placeDir][1] * z);
				int blockid = pworld->getBlockID(curPos);
				if (blockid == m_BlockResID && (pworld->getBlockData(curPos) & 4) == 0) //显示模型的方块
				{
					//pworld->setBlockAir(curPos);
					return curPos;
				}
			}
		}
	}

	return WCoord(0, -1, 0);
}

WCoord BlockCanvas::getSleepEffectPos(World* pworld, const WCoord& blockpos)
{
	WCoord corePos = getCoreBlockPos(pworld, blockpos);
	int dir = pworld->getBlockData(blockpos) & 3;
	WCoord effectPos(corePos.x * 100 + sEffectPosOffset[dir][0], corePos.y * 100, corePos.z * 100 + sEffectPosOffset[dir][1]);
	return effectPos;
}

bool BlockCanvas::haveActorSleep(World* pworld, const WCoord& blockpos)
{
	bool isSleep = false;
	if (IsBedOccupied(pworld, blockpos, 0))
	{
		isSleep = true;
	}

	WORLD_ID actorId = getBindActor(pworld, blockpos);
	IClientActor* pActor = pworld->getActorMgr()->iFindActorByWID(actorId);
	if (pActor && pActor->isSleeping())
	{
		isSleep = true;
	}
	return isSleep;
}

bool BlockCanvas::canPutOntoPlayer(World* pworld, const WCoord& blockpos, IClientPlayer* player)
{
	if (!player || !pworld)
		return false;

	int placeDir = player->GetPlayerCurPlaceDir();
	if (placeDir == DIR_NEG_Y || placeDir == DIR_POS_Y || placeDir > 4 || placeDir < 0)
	{
		placeDir = DIR_NEG_X;
	}

	for (int i = 0; i < 4; i++)
	{
		for (int y = 0; y < 2; y++)
		{
			auto curPos = blockpos + WCoord(sCanvasExtendPos[placeDir][i][0], y, sCanvasExtendPos[placeDir][i][1]);
			auto* mtl = pworld->getBlockMaterial(curPos);
			if (mtl && !mtl->canPutOntoPos(pworld->getWorldProxy(), curPos))
				return false;
		}

	}

	return true;
}

void BlockCanvas::dropBlockAsItem(World* pworld, const WCoord& blockpos, int blockdata, BLOCK_MINE_TYPE droptype, float chance, int uin/* = -1 */)
{
	WCoord corePos = getCoreBlockPos(pworld, blockpos);
	if(droptype != BLOCK_MINE_NONE && pworld->CheckBlockSettingEnable(this, ENABLE_DROPITEM) > 0)
	{
		WorldCanvas* container = sureContainer(pworld, corePos);
		if (container == NULL)
		{
			return;
		}
		char stage = container->getStage();
		if (stage > 2)
		{
			return;
		}

		BackPackGrid grid;
		
		int createItemId = BLOCK_CANVAS;
		if (stage == 1)
		{
			createItemId = 11912;
		}
		else if (stage == 2)
		{
			createItemId = 11913;
		}
		
		SetBackPackGrid(grid, createItemId, 1,-1,-1,0,1,0);
		auto actorMgr = dynamic_cast<ActorManager*>(pworld->getActorMgr());
		if (actorMgr)
		{
			actorMgr->spawnItem(BlockCenterCoord(corePos), grid, 0);
		}
		//pworld->getActorMgr()->SpawnIClientItem(BlockCenterCoord(corePos), grid, 0);
	}
}

bool BlockCanvas::isCoreBlock(World* pworld, const WCoord& blockpos)
{
	if (!pworld)
		return false;

	int blockData = pworld->getBlockData(blockpos);
	return (pworld->getBlockData(blockpos) & 4) == 0;
}

void BlockCanvas::setCoreBlockData(World* pworld, const WCoord& blockpos,int blockData)
{
	if (!pworld)
		return ;
	WCoord corePos = getCoreBlockPos(pworld, blockpos);
	pworld->setBlockData(corePos,blockData,2);
}

int BlockCanvas::getCoreBlockData(World* pworld, const WCoord& blockpos)
{
	if (!pworld)
		return 0;
	WCoord corePos = getCoreBlockPos(pworld, blockpos);
	return pworld->getBlockData(corePos);
}

WorldCanvas* BlockCanvas::sureContainer(World* pworld, const WCoord& blockpos)
{
	WorldCanvas* container = dynamic_cast<WorldCanvas*>(pworld->getContainerMgr()->getContainer(blockpos));
	if (!container)
	{
		container = SANDBOX_NEW(WorldCanvas, blockpos);
		pworld->getContainerMgr()->spawnContainer(container);
	}
	return container;
}

int BlockCanvas::doSleep(World* pworld, IClientPlayer* player, const WCoord& blockpos) const
{
if (g_WorldMgr && !g_WorldMgr->canSleepToSkipNight())
	{
		return STRDEF_SLEEP_NOTIPS;
	}
	int result = player->sleepInVehicleBed(blockpos, pworld);

	return result < 0 ? player->sleep(blockpos) : result;
}