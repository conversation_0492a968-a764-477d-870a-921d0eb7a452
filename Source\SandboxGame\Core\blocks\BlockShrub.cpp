#include "BlockShrub.h"
#include "BlockMaterialMgr.h"
#include "BlockGeom.h"

#include "world.h"
#include "SectionMesh.h"
#include "special_blockid.h"
#include "WorldProxy.h"
#include "TaskData.h"
#include "SandboxCoreDriver.h"
#include "WorldManager.h"
#include "TemperatureManager.h"
#include "LuaInterfaceProxy.h"
#include "worldData/coreMisc.h"

IMPLEMENT_BLOCKMATERIAL(BlockShrub)

BlockShrub::BlockShrub()
{
	m_materialMap.clear();
}

BlockShrub::~BlockShrub()
{
	for (std::map<int, RenderBlockMaterial*>::iterator it = m_materialMap.begin(); it != m_materialMap.end(); it++)
	{
		ENG_RELEASE(it->second);
	}
	m_materialMap.clear();
}

void BlockShrub::init(int resid)
{
	m_materialMap.clear();
	m_faceMeshMap.clear();
	ModelBlockMaterial::init(resid);

	//属性初始化
	SetToggle(BlockToggle_IsSolid, false);
	SetToggle(BlockToggle_RandomTick, true);

	m_nSpecialLogicType[0] |= BlockMaterial::BlockSpceialLogicTeam0::BlockFaceToPlane;
	m_nSpecialLogicType[0] |= BlockMaterial::BlockSpceialLogicTeam0::BranchNotLink;
	//纹理
	m_materialMap.insert(std::make_pair(0, createMaterial("homeshrub_saplings_small")));
	m_materialMap.insert(std::make_pair(1, createMaterial("homeshrub_saplings_big")));
	m_materialMap.insert(std::make_pair(2, createMaterial("homeshrub")));
	//m_materialMap.insert(std::make_pair(BLOCK_HOMELAND_COFFEE, createMaterial("coffee")));
	//m_materialMap.insert(std::make_pair(BLOCK_HOMELAND_TEA, createMaterial("tea")));
	//m_materialMap.insert(std::make_pair(BLOCK_HOMELAND_ORANGE, createMaterial("orange")));
	m_materialMap.insert(std::make_pair(BLOCK_COTTON, createMaterial("cotton")));
	//m_materialMap.insert(std::make_pair(BLOCK_HOMELAND_CHILI, createMaterial("chili")));

	//果树子模型位置
	//m_faceMeshMap.insert(std::make_pair(BLOCK_HOMELAND_COFFEE, 3));
	//m_faceMeshMap.insert(std::make_pair(BLOCK_HOMELAND_TEA, 4));
	//m_faceMeshMap.insert(std::make_pair(BLOCK_HOMELAND_ORANGE, 5));
	m_faceMeshMap.insert(std::make_pair(BLOCK_COTTON, 6));
	//m_faceMeshMap.insert(std::make_pair(BLOCK_HOMELAND_CHILI, 7));
}

void BlockShrub::initDrawType()
{
	m_blockDrawType = BLOCKDRAW_GRASS;
}

void BlockShrub::initGeomName()
{
	m_geomName = m_Def->Texture1.c_str();
}
RenderBlockMaterial* BlockShrub::createMaterial(const char *material)
{
	return g_BlockMtlMgr.createRenderMaterial(material, GetBlockDef(), GETTEX_WITHDEFAULT, getDrawType(), getMipmapMethod());
}

RenderBlockMaterial* BlockShrub::getBlockMaterial(int blockdata)
{
	auto iter = m_materialMap.find(blockdata);
	if (iter != m_materialMap.end())
	{
		return iter->second;
	}
	return NULL;
}

int BlockShrub::getBlockFaceMeshIndex(int key)
{
	auto iter = m_faceMeshMap.find(key);
	if (iter != m_faceMeshMap.end())
	{
		return iter->second;
	}
	return 0;
}

void BlockShrub::checkChange(World *pworld, const WCoord &blockpos)
{
	TriggerBlockAddRemoveDisable Tmp(pworld);

	if (!canStayOnPos(pworld->getWorldProxy(), blockpos))
	{
		dropBlockAsItem(pworld, blockpos, pworld->getBlockData(blockpos));
		pworld->setBlockAll(blockpos, 0, 0, 3);
	}
}

void BlockShrub::createBlockMesh(const BuildSectionMeshData& data, const WCoord &blockpos, SectionMesh *poutmesh)
{
	BlockGeomTemplate* geom = getGeom(data.m_LODLevel);
	if (!geom)
		return;

	auto psection = data.m_SharedSectionData;
	Block pblock = psection->getBlock(blockpos);
	int blockdata = pblock.getData();
	Rainbow::ColorRGBAf verts_light[1] = { Rainbow::ColorRGBAf::black };
	RenderBlockMaterial *mtl = NULL;
	BlockGeomMeshInfo meshinfo;
	psection->getBlockVertexLight(blockpos, verts_light);


	float angle = 1;
	float tx = 0;
	float tz = 0;
	float max_scale = 1.2f;
	float min_scale = 0.9f;
	float scale = 1.0f;
	bool isCotton = getBlockResID() == BLOCK_COTTON;
	if(isCotton)
	{
		ChunkRandGen chunkrand;
		WCoord wpos = psection->getOrigin() + blockpos;
		WCoordHashCoder coder;
		chunkrand.setSeed(coder(wpos));
		angle = (chunkrand.getFloat()) * 360;
		tx = (chunkrand.getFloat() - chunkrand.getFloat()) * 0.4f;
		tz = (chunkrand.getFloat() - chunkrand.getFloat()) * 0.4f;
		scale = min_scale + (chunkrand.getFloat()) * (max_scale - min_scale);
	}

	// 幼苗
	if (blockdata == SHRUB_STAGE_SEED) 
	{
		mtl = getBlockMaterial(0);
		if (mtl)
		{
			SectionSubMesh *psubmesh = poutmesh->getSubMesh(mtl);
			geom->getFaceVerts(meshinfo, 0);
			psubmesh->addGeomBlockLight(meshinfo, &blockpos, verts_light, NULL, mtl->getUVTile());
		}
	}
	// 成长中
	else if (blockdata == SHRUB_STAGE_GROWTH)
	{
		mtl = getBlockMaterial(1);
		if (mtl)
		{
			SectionSubMesh *psubmesh = poutmesh->getSubMesh(mtl);
			geom->getFaceVerts(meshinfo, 1);
			psubmesh->addGeomBlockLight(meshinfo, &blockpos, verts_light, NULL, mtl->getUVTile());
		}
	}
	// 成熟阶段
	else
	{
		//大树，棉花有自己的大树
		mtl = getBlockMaterial(getBlockResID() == BLOCK_COTTON ? BLOCK_COTTON : 2);
		if (mtl)
		{
			SectionSubMesh* psubmesh = poutmesh->getSubMesh(mtl);
			if(isCotton)
			{
				geom->getFaceScaleVerts(meshinfo, 8, scale, scale, 0, angle > 7 ? angle : 7, 0, NULL, tx, 0, tz, 0);
			}else
			{
				geom->getFaceVerts(meshinfo, 2);
			}
			psubmesh->addGeomBlockLight(meshinfo, &blockpos, verts_light, NULL, mtl->getUVTile());
		}

		// 果实
		mtl = getBlockMaterial(getBlockResID());
		if (mtl)
		{
			SectionSubMesh *psubmesh = poutmesh->getSubMesh(mtl);
			if (isCotton)
			{
				geom->getFaceScaleVerts(meshinfo, getBlockFaceMeshIndex(getBlockResID()), scale, scale, 0, angle > 7 ? angle : 7, 0, NULL, tx, 0, tz, 0);
			}else
			{
				geom->getFaceVerts(meshinfo, getBlockFaceMeshIndex(getBlockResID()));
			}

			psubmesh->addGeomBlockLight(meshinfo, &blockpos, verts_light, NULL, mtl->getUVTile());
		}
	}
}

void BlockShrub::onBlockRemoved(World *pworld, const WCoord &blockpos, int blockid, int blockdata)
{
	ModelBlockMaterial::onBlockRemoved(pworld, blockpos, blockid, blockdata);

}

void BlockShrub::blockTick(World *pworld, const WCoord &blockpos)
{
	if (pworld->onServer())
	{
		ModelBlockMaterial::blockTick(pworld, blockpos);

		checkChange(pworld, blockpos);

		int blockdata = pworld->getBlockData(blockpos);

		// 上方有阳光且没有成熟
	/*	bool cangrow = (pworld->getBlockLightValue(TopCoord(blockpos)) >= 9 && blockdata < 2);*/
		bool cangrow = false;
		const BlockDef* def = GetBlockDef();
		if (def && def->CropsSign > 0 && def->GrowthTimeNum > 0)
		{
			cangrow = true;
		}
		else
		{
			cangrow = pworld->getBlockLightValue(blockpos + WCoord(0, 1, 0)) >= 9;
		}

		if (cangrow)
		{

			
			if (def->CropsSign == 0|| def->GrowthTimeNum == 0)
			{
				float rate = getGrowRate(pworld, blockpos);

				bool isTemperatureActive = pworld->GetWorldMgr()->getTemperatureMgr()->GetTemperatureActive();

				int defLevel = GetBlockDef()->GrowthTempRange;
				if (defLevel != TEMPERATURE_LEVEL_NONE && isTemperatureActive)
				{
					float temp = 0.0f;
					int posLevel = TEMPERATURE_LEVEL_NONE;
					pworld->GetWorldMgr()->getTemperatureMgr()->GetBlockTemperatureAndLevel(pworld, blockpos, temp, posLevel);
					float factorRateMin = GetLuaInterfaceProxy().get_lua_const()->growth_rate_min / 100;
					float factor = (1 - (Rainbow::Abs(posLevel - defLevel) / 6)) * (1 - factorRateMin) + factorRateMin;
					if (factor == 0)
						factor = 1;
					rate *= factor;
				}
				if (GenRandomInt(0, int(25.0f / rate)) == 0)
				{
					//红土 不能生成
					if (pworld->getBlockID(DownCoord(blockpos)) == BLOCK_FARMLAND_RED)
					{
						return;
					}
					markOrGrowMarked(pworld, blockpos);
				}
			}
			else
			{
				if (pworld->getBlockID(DownCoord(blockpos)) == BLOCK_FARMLAND_RED)
				{
					return;
				}
				
				if (pworld->getBlockData(blockpos) < getMaxBlockdata())
				{
					dealNewGrow(pworld, blockpos, kBlockUpdateFlagNeedUpdate, getMaxBlockdata());
					WCoord bottomPos(blockpos.x, blockpos.y - 1, blockpos.z);
					int id = pworld->getBlockID(bottomPos);
					if (id == BLOCK_FARMLAND)//把耕地变为土块
					{
						pworld->setBlockAll(bottomPos, BLOCK_GRASS, 0);
					}
					/*
					if (TaskSubSystem::GetTaskSubSystem())
					{
						TaskSubSystem::GetTaskSubSystem()->CheckCommonSyncTask(TASKSYS_GROW, blockpos * BLOCK_SIZE, m_BlockResID);
					}*/

					if (pworld->getBlockData(blockpos) >= getMaxBlockdata())
					{
						WCoord pos = blockpos * BLOCK_SIZE;
						if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
							MNSandbox::SandboxContext sContext = MNSandbox::SandboxContext(nullptr).
								SetData_Number("type", TASKSYS_GROW).
								SetData_Userdata("WCoord", "trackPos", &pos).
								SetData_Number("target1", m_BlockResID).
								SetData_Number("target2", 0).
								SetData_Number("goalnum", 1);
							MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("TaskSubSystem_CheckCommonSyncTask", sContext);
						}
					}
				}
			}	
		}
	}
}
void  BlockShrub::forceResh(World* pworld, const WCoord& blockpos)
{
	if (!pworld || !pworld->onServer())
	{
		return;
	}
	const BlockDef* def = GetBlockDef();
	if (!def || def->CropsSign == 0 || def->GrowthTimeNum == 0)
	{
		return;
	}
	if (pworld->getBlockID(DownCoord(blockpos)) == BLOCK_FARMLAND_RED)
	{
		return;
	}

	if (pworld->getBlockData(blockpos) < getMaxBlockdata())
	{
		dealNewGrow(pworld, blockpos, kBlockUpdateFlagNeedUpdate, getMaxBlockdata());
		WCoord bottomPos(blockpos.x, blockpos.y - 1, blockpos.z);
		int id = pworld->getBlockID(bottomPos);
		if (id == BLOCK_FARMLAND)//把耕地变为土块
		{
			pworld->setBlockAll(bottomPos, BLOCK_GRASS, 0);
		}
		/*
		if (TaskSubSystem::GetTaskSubSystem())
		{
			TaskSubSystem::GetTaskSubSystem()->CheckCommonSyncTask(TASKSYS_GROW, blockpos * BLOCK_SIZE, m_BlockResID);
		}*/
		WCoord pos = blockpos * BLOCK_SIZE;
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
			MNSandbox::SandboxContext sContext = MNSandbox::SandboxContext(nullptr).
				SetData_Number("type", TASKSYS_GROW).
				SetData_Userdata("WCoord", "trackPos", &pos).
				SetData_Number("target1", m_BlockResID).
				SetData_Number("target2", 0).
				SetData_Number("goalnum", 1);
			MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("TaskSubSystem_CheckCommonSyncTask", sContext);
		}
	}
}

bool BlockShrub::onFertilized(World *pworld, const WCoord &blockpos, int fertiliser)
{

	const BlockDef* def = GetBlockDef();
	if (def && def->CropsSign == 0)
	{
		if (GenRandomFloat() < 0.45f)
		{
			//红土 不能生成
			FertilizedPlayEffect(pworld, blockpos);
			if (pworld->getBlockID(DownCoord(blockpos)) == BLOCK_FARMLAND_RED)
			{
				return false;
			}
			markOrGrowMarked(pworld, blockpos);
		}
	}
	else
	{
		if (pworld->getBlockID(DownCoord(blockpos)) == BLOCK_FARMLAND_RED)
		{
			return false;
		}
		return dealFertilized(pworld, blockpos, fertiliser);
	}
	return true;
}

bool BlockShrub::canStayOnPos(WorldProxy *pworld, const WCoord &blockpos)
{
	int downBlockID = pworld->getBlockID(DownCoord(blockpos));

	// 可以在耕地、草木灰耕地、土块和草块上存在
	return canThisPlantGrowOnThisBlockID(downBlockID) || downBlockID == BLOCK_DIRT || downBlockID == BLOCK_GRASS;
}

bool BlockShrub::canPutOntoPos(WorldProxy *pworld, const WCoord &blockpos)
{
	//if (!BlockMaterial::canPutOntoPos(pworld, blockpos)) return false;
	if (!canThisPlantGrowOnThisBlockID(pworld->getBlockID(DownCoord(blockpos)))) return false;

	return true;
}

bool BlockShrub::canThisPlantGrowOnThisBlockID(int blockid)
{
	// 耕地上
	return blockid == BLOCK_FARMLAND || blockid == BLOCK_FARMLAND_RED || blockid == BLOCK_GRASS_WOOD_GRAY_FARMLAND;
}

void BlockShrub::onNotify(World *pworld, const WCoord &blockpos, int blockid)
{
	int blockdata = pworld->getBlockData(blockpos);

	if (!canStayOnPos(pworld->getWorldProxy(), blockpos))
	{
		dropBlockAsItem(pworld, blockpos, blockdata);
		pworld->setBlockAir(blockpos);
	}
}

void BlockShrub::onBlockDestroyedBy(World *pworld, const WCoord &blockpos, int blockdata, BLOCK_DESTROY_REASON_T destroytype, IClientActor *bywho)
{
	//获取下面块
	WCoord downpos = DownCoord(blockpos);
	int blockid = pworld->getBlockID(downpos);
	if (blockid == BLOCK_GRASS_WOOD_GRAY_FARMLAND)//如果是种在草木灰耕地上
	{//消耗次数
		//return false;
		int blockdata = pworld->getBlockData(downpos);
		if (blockdata == 0)
		{
			pworld->setBlockAll(downpos, BLOCK_DIRT, 0);//变土块
		}
		else {
			//消耗放HerbMaterial::onBlockPlacedBy会有问题,因为使用种子的时候,不会触发onBlockPlacedBy
			pworld->setBlockData(downpos, blockdata - 1, 0);//草木灰耕地上 栽了植物后,会消耗次数
		}
	}
}

void BlockShrub::dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata /*= 0*/, BLOCK_MINE_TYPE droptype /*= BLOCK_MINE_NOTOOL*/, float chance /*= 1.0f*/, int uin)
{
	//int blockdata = pworld->getBlockData(blockpos);
	// 生长阶段掉落种子
	if (blockdata < 2)
	{
		if (droptype == BLOCK_MINE_NONE) return;
		if (GenRandomFloat() > chance) return;

		int blockid = getBlockResID();
		const BlockDef *def = GetBlockDef();
		if (def == NULL) return;
		if (pworld->CheckBlockSettingEnable(this, ENABLE_DROPITEM) == 0) { return; }
		int itemid = 0;
		int itemnum = 0;
		int r = rand() % 10000;
		if (def->MineTool == 0 && droptype == BLOCK_MINE_NOTOOL) droptype = BLOCK_MINE_TOOLFIT;

		if (droptype == BLOCK_MINE_NOTOOL)
		{
			itemnum = 1;
			if (itemnum > 0) itemid = 11419;
		}
		else if (droptype == BLOCK_MINE_PRECISE)
		{
			itemid = 11419;
			itemnum = 1;
		}
		else
		{
			for (int i = 0; i < MAX_TOOLMINE_DROP; i++)
			{
				itemid = 11419;
				if (itemid > 0)
				{
					itemnum = 1;
					if (itemnum > 0) break;
				}
			}
		}

		//这里有可能会掉落道具，彩色方块掉落道具会使g_BlockMtlMgr.getMaterial(itemid)触发断言
		//int protodata = (isColorableBlock() &&  g_BlockMtlMgr.getMaterial(itemid) && g_BlockMtlMgr.getMaterial(itemid)->isColorableBlock()) ? blockdata : 0;
		int protodata = 0;
		if (itemid <= SOC_BLOCKID_MAX/* || (itemid < SOC_BLOCKID_MAX || (itemid >= EX_BLOCKID_MIN && itemid < EX_BLOCKID_MAX))*/)//SOC 方块ID 范围 0-4095 不支持扩展id
		{
			protodata = (isColorableBlock() && g_BlockMtlMgr.getMaterial(itemid) && g_BlockMtlMgr.getMaterial(itemid)->isColorableBlock()) ? blockdata : 0;
		}

		if (itemid > 0 && itemnum > 0) {
			if (BlockMaterial::m_DigLuckBuff > 1) {
				itemnum *= BlockMaterial::m_DigLuckBuff;
			}
			doDropItem(pworld, blockpos, itemid, itemnum, protodata);
		}

		if (BlockMaterial::m_DigLuckEnchant > 0 && droptype == BLOCK_MINE_TOOLFIT)
		{
			int itemid = 11419;
			if (itemid > 0)
			{
				float addprob[3];
				memset(addprob, 0, sizeof(addprob));
				int n = 1;
				for (int i = 0; i < n; i++)
				{
					if (GenRandomInt(10000) < GetBlockDef()->ToolMineDrops[0].odds)
					{
						doDropItem(pworld, blockpos, itemid, 1, protodata);
					}
				}
			}
		}
	}
	// 成熟阶段掉落配置掉落物
	else
	{
		ModelBlockMaterial::dropBlockAsItem(pworld, blockpos, blockdata, droptype, chance);
	}
}

void BlockShrub::markOrGrowMarked(World *pworld, const WCoord &blockpos)
{
	int blockdata = pworld->getBlockData(blockpos);
	if (blockdata < 2)
	{
		pworld->setBlockData(blockpos, blockdata + 1, 6);
		if (pworld->getBlockData(blockpos) == 2)
		{
			WCoord bottomPos(blockpos.x, blockpos.y - 1, blockpos.z);
			int id = pworld->getBlockID(bottomPos);
			if (id == BLOCK_FARMLAND)//把耕地变为土块
			{
				pworld->setBlockAll(bottomPos, BLOCK_GRASS, 0);
			}
			/*if (TaskSubSystem::GetTaskSubSystem())
			{
				TaskSubSystem::GetTaskSubSystem()->CheckCommonSyncTask(TASKSYS_GROW, blockpos * BLOCK_SIZE, m_BlockResID);
			}*/
			WCoord pos = blockpos * BLOCK_SIZE;
			if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
				MNSandbox::SandboxContext sContext = MNSandbox::SandboxContext(nullptr).
					SetData_Number("type", TASKSYS_GROW).
					SetData_Userdata("WCoord", "trackPos", &pos).
					SetData_Number("target1", m_BlockResID).
					SetData_Number("target2", 0).
					SetData_Number("goalnum", 1);
				MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("TaskSubSystem_CheckCommonSyncTask", sContext);
			}
		}
	}
}

float BlockShrub::getGrowRate(World *pworld, const WCoord &blockpos)
{
	float rate = 1.0f;
	int x = blockpos.x;
	int y = blockpos.y;
	int z = blockpos.z;

	int blockid1 = pworld->getBlockID(x, y, z - 1);
	int blockid2 = pworld->getBlockID(x, y, z + 1);
	int blockid3 = pworld->getBlockID(x - 1, y, z);
	int blockid4 = pworld->getBlockID(x + 1, y, z);
	int blockid5 = pworld->getBlockID(x - 1, y, z - 1);
	int blockid6 = pworld->getBlockID(x + 1, y, z - 1);
	int blockid7 = pworld->getBlockID(x + 1, y, z + 1);
	int blockid8 = pworld->getBlockID(x - 1, y, z + 1);
	bool adj_x = blockid3 == getBlockResID() || blockid4 == getBlockResID();
	bool adj_z = blockid1 == getBlockResID() || blockid2 == getBlockResID();
	bool corner = blockid5 == getBlockResID() || blockid6 == getBlockResID() || blockid7 == getBlockResID() || blockid8 == getBlockResID();

	for (int i = x - 1; i <= x + 1; ++i)
	{
		for (int j = z - 1; j <= z + 1; ++j)
		{
			int downid = pworld->getBlockID(i, y - 1, j);
			float tmprate = 0.0f;

			if (downid == BLOCK_FARMLAND ||downid == BLOCK_FARMLAND_RED || downid == BLOCK_BURYLAND || downid == BLOCK_FARMLAND_PIT)
			{
				tmprate = 1.0f;

				if (pworld->getBlockData(i, y - 1, j) > 0) //wet
				{
					tmprate = 3.0f;
				}
			}
			else if (downid == BLOCK_GRASS_WOOD_GRAY_FARMLAND) {//草木灰耕地土块 
				tmprate = 5.0f;
			}

			if (i != x || j != z)
			{
				tmprate /= 4.0f;
			}

			rate += tmprate;
		}
	}

	if (corner || adj_x && adj_z)
	{
		rate /= 2.0f;
	}

	return rate;
}

int  BlockShrub::getMaxBlockdata()
{
	return 2;
}