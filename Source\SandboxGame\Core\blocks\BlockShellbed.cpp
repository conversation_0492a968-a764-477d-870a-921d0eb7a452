
#include "BlockShellbed.h"
#include "BlockMaterialMgr.h"
#include "Collision.h"
#include "section.h"
#include "SectionMesh.h"
#include "BlockGeom.h"
#include "Collision.h"
#include "world.h"
#include "Environment.h"
#include "ClientActorManager.h"
#include "IClientPlayer.h"
#include "ChunkGenerator.h"
//#include "GameEvent.h"
#include "DefManagerProxy.h"
#include "VehicleWorld.h"
#include "ActorVehicleAssemble.h"
#include "ActorVillager.h"
#include "SandboxIdDef.h"
#include "ClientPlayer.h"
using namespace MINIW;

const static int sShellbedExtendPos[4][9][2] = {
		{
			{0, 0}, {1, 0},{2,0}, {0, -1}, {1, -1},{2, -1},{0, -2},{1, -2},{2, -2},
		},
		{
			{0, 0}, {-1, 0},{-2, 0}, {0, 1}, {-1, 1},{-2, 1},{0, 2},{-1, 2},{-2, 2},
		},
		{
			{0, 0}, {1, 0},{2, 0}, {0, 1}, {1, 1},{2, 1},{0, 2},{1, 2},{2, 2},
		},
		{
			{0, 0}, {-1, 0},{-2, 0}, {0, -1}, {-1, -1},{-2, -1},{0,-2},{-1, -2},{-2, -2},
		},

};

//相对核心方块的位置偏移，，从门口向其他位置搜索
const static int sShellbedStandPos[4][12][2] = {
		{
			{-1, 0},{-1, -1},{-1, 1}, {-1, -2},{0, 1}, {0, -2}, {1, 1}, {1, -2},{2, 1},{2, -2},{2, 0}, {2, -1}
		},
		{
			{1, 0},{1, 1},{1, -1}, {1, 2},{0, -1}, {0, 2}, {1, -1}, {1, 2},{2, -1},{2, 0},{2, 1}, {2, 2}
		},
		{
			{0, -1},{1, -1},{-1, -1}, {2, -1},{-1, 0}, {2, 0}, {-1, 1}, {2, 1},{-1, 2},{0, 2},{1, 2}, {2, 2}
		},
		{
			{0, 1},{-1, 1},{1, 1}, {-2, 1},{1, 0}, {-2, 0}, {1, -1}, {-2, -1},{1, -2},{0, -2},{-1, -2}, {-2, -2}
		},
};

const static int sSleepPosOffset[4][2] = {
		{
			50,0
		},
		{
			50,100
		},
		{
			100,50
		},
		{
			0,50
		},
};
IMPLEMENT_BLOCKMATERIAL(BlockShellbed)
BlockShellbed::BlockShellbed()
{

}

int BlockShellbed::getBlockGeomID(int* idbuf, int* dirbuf, const SectionDataHandler* psection, const WCoord& blockpos, World* pworld)
{
	if (psection == NULL || idbuf == NULL || dirbuf == NULL)
	{
		return 0;
	}
	if (pworld == NULL || pworld->getContainerMgr() == NULL)
	{
		return 0;
	}
	WorldBed* container = dynamic_cast<WorldBed*>(pworld->getContainerMgr()->getContainer(psection->getOrigin() + blockpos));
	if (container == NULL) 
	{
		return 0;
	}
	int blockdata = psection->getBlock(blockpos).getData();
	idbuf[0] = 0;
	dirbuf[0] = blockdata & 3;
	return 1;
}

void BlockShellbed::initGeomName()
{
	m_geomName = m_Def->Texture2.c_str();
}

bool BlockShellbed::onTrigger(World* pworld, const WCoord& blockpos, DirectionType face, IClientPlayer* player, const Rainbow::Vector3f& colpoint)
{
	if (!player || !pworld)
	{
		return false;
	}

	if (pworld->isRemoteMode())
	{
		return true;
	}

	int blockData = pworld->getBlockData(blockpos);
	if (!isCoreBlock(pworld, blockpos))
	{
		WCoord basePos = getCoreBlockPos(pworld, blockpos);
		if (basePos.y < 0)
		{
			return false;
		}

		return onTrigger(pworld, basePos, face, player, colpoint);
	}

	WorldBed* container = sureContainer(pworld, blockpos);
	if (container == NULL)
	{
		return 0;
	}
	if (IsBedOccupied(pworld, blockpos, blockData))
	{
		WCoord sleepPos = getSleepPosition(pworld, blockpos);
		ActorManager* actorMgr = dynamic_cast<ActorManager*>(pworld->getActorMgr());
		if (actorMgr && actorMgr->getOccupiedPlayer(CoordDivBlock(sleepPos), ACTORFLAG_SLEEP))  //不能用核心方块的位置，这里需要传实际睡觉的位置
		{
			return true;
		}
		//有野人在睡觉的床也不能躺
		if (container && container->getBindActor() > 0)
		{
			return true;
		}

		setBedOccupied(pworld, blockpos, false);
	}
	int ret = doSleep(pworld, player, blockpos);

	if (ret == 0)
	{
		setBedOccupied(pworld, blockpos, true);
	}
	else
	{
		player->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, ret);
	}

	return true;
}


void  BlockShellbed::init(int resid)
{
	ModelBlockMaterial::init(resid);
	SetToggle(BlockToggle_HasContainer, true);
	if (BlockMaterial::m_LoadOnlyLogic) return;

	getDefaultMtl()->setItemMtlOpaque(true);

	
}

void BlockShellbed::createCollideData(CollisionDetect* coldetect, World* pworld, const WCoord& blockpos)
{
	WCoord origin = blockpos * BLOCK_SIZE;
	coldetect->addObstacle(origin, origin + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
}

WorldBed* BlockShellbed::createContainer(World* pworld, const WCoord& blockpos)
{
	if (pworld->isRemoteMode())
	{
		return nullptr;
	}
	if (isCoreBlock(pworld, blockpos))
	{
		return SANDBOX_NEW(WorldBed, blockpos);
	}
	else
	{
		return nullptr;
	}
}

void BlockShellbed::createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh)
{
	if (data.m_SharedSectionData->getBlock(blockpos).getData() & 4)
		return;

	Super::createBlockMesh(data, blockpos, poutmesh);
}

void BlockShellbed::onBlockPlacedBy(World* pworld, const WCoord& blockpos, IClientPlayer* player)
{
	if (pworld == NULL || player == NULL)
	{
		return;
	}
	ClientPlayer* playerTmp = player->GetPlayer();
	if (!playerTmp) return;
	ModelBlockMaterial::onBlockPlacedBy(pworld, blockpos, player);
	int placeDir = playerTmp->getCurPlaceDir();
	if (placeDir == DIR_NEG_Y || placeDir == DIR_POS_Y)
	{
		placeDir = DIR_NEG_X;
	}
	pworld->setBlockData(blockpos, placeDir);
}

int BlockShellbed::getPlaceBlockDataByPlayer(World* pworld, IClientPlayer* player)
{
	ClientPlayer* playerTmp = player->GetPlayer();
	if (playerTmp) 
		return playerTmp->getCurPlaceDir();

	return 0;
}

int BlockShellbed::getPlaceBlockData(World* pworld, const WCoord& blockpos, DirectionType face, float hitptx, float hitpty, float hitptz, int def_blockdata)
{
	int placeDir = face;
	if (placeDir == DIR_NEG_Y || placeDir == DIR_POS_Y)
	{
		placeDir = DIR_NEG_X;
	}
	return placeDir;
}

void BlockShellbed::onBlockAdded(World* pworld, const WCoord& blockpos)
{
	if (pworld == NULL)
	{
		return;
	}

	// 这里只有主机会执行
	int blockData = pworld->getBlockData(blockpos);
	if (isCoreBlock(pworld, blockpos))
	{
		ModelBlockMaterial::onBlockAdded(pworld, blockpos);

		int placeDir = blockData & 3;
		for (int i = 0; i < 9; i++)
		{
			for (int y = 0; y < 3; y++)
			{
				auto curPos = blockpos + WCoord(sShellbedExtendPos[placeDir][i][0], y, sShellbedExtendPos[placeDir][i][1]);
				if (curPos == blockpos)
					continue;

				pworld->setBlockAll(curPos, m_BlockResID, 4 | placeDir);
			}
		}
	}
}

void BlockShellbed::onBlockRemoved(World* pworld, const WCoord& blockpos, int blockid, int blockdata)
{
	if (pworld == NULL)
	{
		return;
	}
	if (blockdata == 12)  //表示不用再处理remove事件
		return;

	int placeDir = blockdata & 3;
	if (blockdata & 4)  //普通的方块
	{
		WCoord basePos = getCoreBlockPos(pworld, blockpos, blockdata);
		if (basePos.y >= 0)
		{
			pworld->setBlockAir(basePos);
		}
	}
	else
	{
		ModelBlockMaterial::onBlockRemoved(pworld, blockpos, blockid, blockdata);

		WorldBed* canvasContainer = dynamic_cast<WorldBed*>(pworld->getContainerMgr()->getContainer(blockpos));
		if (canvasContainer)
		{
			canvasContainer->stopBedEffectByBlockdata(blockdata);

			//同时清理世界数据记录
			if (g_WorldMgr && canvasContainer->getBindActor())
			{
				if (g_WorldMgr->getWorldInfoManager()->getVillageBedStatus(blockpos, canvasContainer->getBindActor()) != ENUM_BED_STATUS_NO_BIND_ACOTR)
				{
					g_WorldMgr->getWorldInfoManager()->removeVillageBedRelationship(canvasContainer->getBindActor());
				}
			}
		}

		pworld->getContainerMgr()->destroyContainer(blockpos);
		clearNormalBlock(pworld, blockpos, blockdata);
	}

}


void BlockShellbed::clearNormalBlock(World* pworld, const WCoord& blockpos, int blockdata /* = -1 */)
{
	if (blockdata == -1)
		blockdata = pworld->getBlockData(blockpos);
	int placeDir = blockdata & 3;

	for (int i = 0; i < 9; i++)
	{
		for (int y = 0; y < 3; y++)
		{
			auto curPos = blockpos + WCoord(sShellbedExtendPos[placeDir][i][0], y, sShellbedExtendPos[placeDir][i][1]);
			if (curPos == blockpos)
				continue;

			int blockid = pworld->getBlockID(curPos);
			if (blockid == m_BlockResID && !isCoreBlock(pworld, curPos))
			{
				pworld->setBlockData(curPos, 12, 0);
				pworld->setBlockAir(curPos);
			}
		}
	}
}

bool BlockShellbed::IsBedOccupied(World* pworld, const WCoord& blockpos, int blockdata)
{
	return (getCoreBlockData(pworld, blockpos) & 8) != 0;
}

void BlockShellbed::setBedOccupied(World* pworld, const WCoord& blockpos, bool occupied)
{
	int oldData = getCoreBlockData(pworld, blockpos);
	int newData = occupied ? oldData | 8 : oldData & (~8);
	setCoreBlockData(pworld, blockpos, newData);
}

inline bool CanStandOnBlock(World* pworld, int x, int y, int z)
{
	int blockid = pworld->getBlockID(x, y, z);
	BlockDef* def = GetDefManagerProxy()->getBlockDef(blockid);
	if (def) {
		return def->MoveCollide != 1;
	}
	else {
		return false;
	}
}

bool BlockShellbed::getNearestEmptyChunkCoordinates(WCoord& ret, World* pworld, const WCoord& blockpos, int loopcount)
{
	int blockdata = pworld->getBlockData(blockpos);
	int dir = blockdata & 3;

	WCoord corePos = getCoreBlockPos(pworld, blockpos);

	for (int i = 0; i < 12; i++)
	{
		int x = corePos.x + sShellbedStandPos[dir][i][0];
		int z = corePos.z + sShellbedStandPos[dir][i][1];
		int y = corePos.y;
		if (pworld->doesBlockHaveSolidTopSurface(WCoord(x, y - 1, z))
			&& CanStandOnBlock(pworld, x, y, z) && CanStandOnBlock(pworld, x, y + 1, z) && CanStandOnBlock(pworld, x, y + 2, z))
		{
			ret = WCoord(x, blockpos.y, z);
			return true;
		}
	}

	return false;
}

void BlockShellbed::getEyePosInBed(World* pworld, const WCoord& sleeppos, WCoord& eyepos, Rainbow::Vector3f& lookdir)
{
	WCoord blockpos = CoordDivBlock(sleeppos);
	int dir = pworld->getBlockData(blockpos) & 3;
	WCoord dirCoor = g_DirectionCoord[dir];
	WCoord corePos = getCoreBlockPos(pworld, blockpos);

	WCoord rootPos = getSleepPosition(pworld, blockpos);
	//eyepos = rootPos - dirCoor * 120;
	eyepos = rootPos + dirCoor * 200;
	eyepos.y += 50;

	lookdir.x = dirCoor.x;
	lookdir.y = dirCoor.y;
	lookdir.z = dirCoor.z;
	lookdir *= -1;
}

WORLD_ID BlockShellbed::getBindActor(World* pworld, const WCoord& blockpos)
{
	WorldBed* container = dynamic_cast<WorldBed*>(pworld->getContainerMgr()->getContainer(blockpos));
	if (container)
	{
		return container->getBindActor();
	}
	return 0;
}

void BlockShellbed::setBindActor(World* pworld, const WCoord& blockpos, WORLD_ID bindactor)
{
	WorldBed* container = sureContainer(pworld, blockpos);
	container->setBindActor(bindactor);
	container->setBedStatus(ENUM_BED_STATUS_NORMAL);

	//世界也记一份
	ActorManager* actorMgr = dynamic_cast<ActorManager*>(pworld->getActorMgr());
	if (g_WorldMgr && actorMgr)
	{
		ActorVillager* mob = dynamic_cast<ActorVillager*>(actorMgr->findActorByWID(bindactor));
		if (mob && mob->getTamedOwnerID())
		{
			g_WorldMgr->getWorldInfoManager()->changeVillageBedRelationship(mob->getTamedOwnerID(), bindactor, blockpos, ENUM_BED_STATUS_NORMAL);
		}
	}
}

WCoord BlockShellbed::getSleepPosition(World* pworld, const WCoord& blockpos)
{
	WCoord corePos = getCoreBlockPos(pworld, blockpos);
	int dir = pworld->getBlockData(blockpos) & 3;
	WCoord sleepPos(corePos.x * 100 + sSleepPosOffset[dir][0], corePos.y * 100 + 25, corePos.z * 100 + sSleepPosOffset[dir][1]);
	return sleepPos;
}

WorldContainer* BlockShellbed::getCoreContainer(World* pworld, const WCoord& blockpos)
{
	int blockid = pworld->getBlockID(blockpos);
	if (blockid != BLOCK_SHELLBED)
	{
		return NULL;
	}
	WCoord corePos = getCoreBlockPos(pworld, blockpos);
	WorldBed* container = dynamic_cast<WorldBed*>(pworld->getContainerMgr()->getContainer(corePos));
	return container;
}

WCoord BlockShellbed::getCoreBlockPos(World* pworld, const WCoord& blockpos, int blockdata/* =-1 */)
{
	const int searchDir[4][2] = {
			{-1, 1},
			{1, -1},
			{-1, -1},
			{1, 1},
	};
	if (blockdata == -1)
		blockdata = pworld->getBlockData(blockpos);
	int placeDir = blockdata & 3;
	for (int x = 0; x < 3; x++)
	{
		for (int z = 0; z < 3; z++)
		{
			for (int y = 0; y < 3; y++)
			{
				auto curPos = blockpos + WCoord(searchDir[placeDir][0] * x, -1 * y, searchDir[placeDir][1] * z);
				int blockid = pworld->getBlockID(curPos);
				if (blockid == m_BlockResID && (pworld->getBlockData(curPos) & 4) == 0) //显示模型的方块
				{
					//pworld->setBlockAir(curPos);
					return curPos;
				}
			}
		}
	}

	return WCoord(0, -1, 0);
}

bool BlockShellbed::canPutOntoPlayer(World* pworld, const WCoord& blockpos, IClientPlayer* player)
{
	if (!player || !pworld)
		return false;
	ClientPlayer* playerTmp = player->GetPlayer();
	if (!playerTmp) return false;
	int placeDir = playerTmp->getCurPlaceDir();
	if (placeDir == DIR_NEG_Y || placeDir == DIR_POS_Y || placeDir > 4 || placeDir < 0)
	{
		placeDir = DIR_NEG_X;
	}

	for (int i = 0; i < 9; i++)
	{
		for (int y = 0; y < 3; y++)
		{
			auto curPos = blockpos + WCoord(sShellbedExtendPos[placeDir][i][0], y, sShellbedExtendPos[placeDir][i][1]);
			auto* mtl = pworld->getBlockMaterial(curPos);
			if (mtl && !mtl->canPutOntoPos(pworld->getWorldProxy(), curPos))
				return false;
		}

	}

	return true;
}

void BlockShellbed::dropBlockAsItem(World* pworld, const WCoord& blockpos, int blockdata, BLOCK_MINE_TYPE droptype/* =BLOCK_MINE_NOTOOL */, float chance/* =1.0f */, int uin)
{
	//int ishead = blockdata & 4;
	//if (!ishead)
		ModelBlockMaterial::dropBlockAsItem(pworld, blockpos, blockdata, droptype, chance, uin);
}

bool BlockShellbed::isCoreBlock(World* pworld, const WCoord& blockpos)
{
	if (!pworld)
		return false;

	int blockData = pworld->getBlockData(blockpos);
	return (pworld->getBlockData(blockpos) & 4) == 0;
}

void BlockShellbed::setCoreBlockData(World* pworld, const WCoord& blockpos, int blockData)
{
	if (!pworld)
		return;
	WCoord corePos = getCoreBlockPos(pworld, blockpos);
	pworld->setBlockData(corePos, blockData, 2);
}

int BlockShellbed::getCoreBlockData(World* pworld, const WCoord& blockpos)
{
	if (!pworld)
		return 0;
	WCoord corePos = getCoreBlockPos(pworld, blockpos);
	return pworld->getBlockData(corePos);
}

WorldBed* BlockShellbed::sureContainer(World* pworld, const WCoord& blockpos)
{
	WorldBed* container = dynamic_cast<WorldBed*>(pworld->getContainerMgr()->getContainer(blockpos));
	if (!container)
	{
		container = SANDBOX_NEW(WorldBed, blockpos);
		pworld->getContainerMgr()->spawnContainer(container);
	}
	return container;
}

int BlockShellbed::doSleep(World* pworld, IClientPlayer* player, const WCoord& blockpos) const
{
	if (g_WorldMgr && !g_WorldMgr->canSleepToSkipNight())
	{
		return STRDEF_SLEEP_NOTIPS;
	}

	VehicleWorld* vworld = dynamic_cast<VehicleWorld*>(pworld);
	ClientPlayer* playerTmp = player->GetPlayer();
	if (vworld && playerTmp)
	{
		ActorVehicleAssemble* pAssemble = vworld->getActorVehicleAssemble();

		return playerTmp->sleepInVehicleBed(blockpos, pAssemble);
	}

	return player->sleep(blockpos,true);
}