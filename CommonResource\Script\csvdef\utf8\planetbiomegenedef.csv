,,,注意：该值=地表最低高度 -空岛最低的高度/2 + 30,,,,,,,,,
地表最低高度,地表地形的最大高度,空岛最低的高度,空岛的基础高度,空岛生成的噪声值,地表生成的噪声值,空岛底部生成的噪声值,图腾生成的CHUNK间隔,图腾生成的随机数范围（越大概率越小，1为50%，2为33%）,每棵太空树的氧气果生成概率,宇宙商人生成的CHUNK间隔,宇宙商人生成的随机数范围,宇宙商人出现的最低高度
BOTTOM_BASE_HEIGHT_Y,BOTTOM_BASE_MAX_HEIGHT_Y,AIRLAND_MIN_HEIGHT,AIRLAND_BASE_HEIGHT_Y,AIRLAND_NOISE_DATA,BOTTOM_NOISE_DATA,AIRLAND_BOTTOM_NOISE_DATA,TOTEMGEN_CHUNK_MOD_NUM,TOTEMGEN_MAX_LOOP_NUM,TOTEMGEN_OXYGEN_FRUIT_PROB,COMMICSSHOP_CHUNK_MOD_NUM,COMMICSHOP_MAX_LOOP_NUM,COMMICSHOP_MIN_Y
30,65,6,80,10,80,32,10,2,1,10,1,47
