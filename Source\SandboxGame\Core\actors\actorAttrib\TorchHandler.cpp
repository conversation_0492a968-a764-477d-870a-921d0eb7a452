#include "TorchHandler.h"
#include "PlayerAttrib.h"
#include "ClientPlayer.h"
#include "backpack.h"
#include "special_blockid.h"
#include "SoundComponent.h"

TorchHandler::TorchHandler(PlayerAttrib* playerAttrib)
	: m_PlayerAttrib(playerAttrib)
	, m_ClientPlayer(nullptr)
	, m_Backpack(nullptr)
	, m_LastHeldTorchItemId(0)
	, m_IsHoldingTorch(false)
	, m_IsInitialized(false)
	, m_TickCounter(0)
{
}

TorchHandler::~TorchHandler()
{
}

void TorchHandler::initialize(ClientPlayer* player)
{
	if (m_IsInitialized || !m_PlayerAttrib)
		return;

	// 获取玩家和背包引用
	m_ClientPlayer = player;
	m_Backpack = m_PlayerAttrib->m_Backpack;

	// 重置状态
	m_LastHeldTorchItemId = 0;
	m_IsHoldingTorch = false;
	m_TickCounter = 0;
	m_IsInitialized = true;
}

void TorchHandler::tick()
{
	if (!m_IsInitialized)
		return;

	int torchItemId = 0;
	if (isPlayerHoldingTorch(torchItemId))
	{
		m_IsHoldingTorch = true;
		m_LastHeldTorchItemId = torchItemId;
		
		// 增加tick计数器
		m_TickCounter++;
		
		// 获取火把的最大耐久度
		int maxDurability = getTorchMaxDurability(torchItemId);
		if (maxDurability > 0)
		{
			// 根据最大耐久度计算扣除间隔和数量
			int reductionInterval = calculateDurabilityReductionInterval(maxDurability);
			int reductionAmount = calculateDurabilityReductionAmount(maxDurability);
			
			// 检查是否到了扣除耐久度的时间
			if (m_TickCounter >= reductionInterval)
			{
				m_TickCounter -= reductionInterval; // 保留多余的tick数，而不是直接归零
				
				// 一次性扣除指定数量的耐久度
				reduceTorchDurability(torchItemId, reductionAmount);
			}
		}
	}
	else
	{
		m_IsHoldingTorch = false;
		m_LastHeldTorchItemId = 0;
		m_TickCounter = 0; // 没有手持火把时重置计数器
	}
}

bool TorchHandler::isPlayerHoldingTorch(int& torchItemId)
{
	if (!m_PlayerAttrib)
		return false;

	// 获取当前手持的物品ID
	int currentItemId = m_PlayerAttrib->getEquipItem(EQUIP_WEAPON);
	
	// 检查是否是火把物品
	if (isTorchItem(currentItemId))
	{
		torchItemId = currentItemId;
		return true;
	}

	return false;
}

void TorchHandler::reduceTorchDurability(int torchItemId, int amount)
{
	if (!m_Backpack || !m_PlayerAttrib || amount <= 0)
		return;

	// 获取当前武器槽的格子索引
	int weaponIndex = m_PlayerAttrib->equipSlot2Index(EQUIP_WEAPON);
	BackPackGrid* grid = m_Backpack->index2Grid(weaponIndex);
	
	if (!grid || !grid->def || grid->def->ID != torchItemId)
		return;

	// 减少指定数量的耐久度
	int newDurability = grid->addDuration(-amount, true);
	
	// 如果耐久度降到0或以下，清空火把
	if (newDurability <= 0)
	{
		clearPlayerTorch(torchItemId);
	}
	else
	{
		// 通知背包更新
		m_Backpack->afterChangeGrid(weaponIndex);
	}
}

void TorchHandler::clearPlayerTorch(int torchItemId)
{
	if (!m_Backpack || !m_PlayerAttrib || !m_ClientPlayer)
		return;

	// 获取当前武器槽的格子索引
	int weaponIndex = m_PlayerAttrib->equipSlot2Index(EQUIP_WEAPON);
	BackPackGrid* grid = m_Backpack->index2Grid(weaponIndex);
	
	if (!grid || !grid->def || grid->def->ID != torchItemId)
		return;

	// 播放火把熄灭声音
	auto soundComp = m_ClientPlayer->getSoundComponent();
	if (soundComp)
	{
		soundComp->playSound("misc.break", 1.0f, 1.0f);
	}

	// 清空格子
	m_Backpack->removeItem(weaponIndex, 1);
	
	// 重置手持火把状态
	m_IsHoldingTorch = false;
	m_LastHeldTorchItemId = 0;
	m_TickCounter = 0;
}

bool TorchHandler::isTorchItem(int itemId) const
{
	return itemId == ITEM_SOCTORCH;
	// 检查是否是火把物品
	//return (itemId == BLOCK_TORCH ||      // 普通火把
	//		itemId == ITEM_SOCTORCH ||    // SOC火把
	//		itemId == BLOCK_ICE_TORCH ||  // 冰火把
	//		itemId == BLOCK_SMALL_TORCH); // 小火把
}

int TorchHandler::getTorchMaxDurability(int torchItemId)
{
	if (!m_Backpack || !m_PlayerAttrib)
		return 0;

	// 获取当前武器槽的格子索引
	int weaponIndex = m_PlayerAttrib->equipSlot2Index(EQUIP_WEAPON);
	BackPackGrid* grid = m_Backpack->index2Grid(weaponIndex);
	
	if (!grid || !grid->def || grid->def->ID != torchItemId)
		return 0;

	// 返回最大耐久度
	return grid->getMaxDuration();
}

int TorchHandler::calculateDurabilityReductionInterval(int maxDurability)
{
	// 根据你的需求：
	// 如果总耐久度 < 60：每20tick扣一耐久度
	// 如果总耐久度 ≥ 300（5*60）：每1200tick（1分钟）扣60耐久
	// 两种情况下的消耗速度应该是相同的：都是每分钟消耗60点耐久度
	
	if (maxDurability < 60)
	{
		return 20; // 每20tick扣一次，每次扣1点 = 每分钟60点
	}
	else if (maxDurability >= 300)
	{
		return 1200; // 每1200tick扣一次，每次扣60点 = 每分钟60点
	}
	else
	{
		// 中间值：保持和低耐久度相同的消耗速度
		return 20;
	}
}

int TorchHandler::calculateDurabilityReductionAmount(int maxDurability)
{
	// 根据你的需求：
	// 如果总耐久度 < 60：每次扣1耐久度
	// 如果总耐久度 ≥ 300（5*60）：每次扣60耐久度
	// 中间值每次扣1耐久度
	
	if (maxDurability < 60)
	{
		return 1; // 每次扣1点耐久度
	}
	else if (maxDurability >= 300)
	{
		return 60; // 每次扣60点耐久度
	}
	else
	{
		// 中间值每次扣1点耐久度
		return 1;
	}
}

