#ifndef __CLIENTMOB_H__
#define __CLIENTMOB_H__

#include "ClientActorLiving.h"
#include "SandboxCoreDriver.h"
#include "ai/AITask.h"
#include "VacantComponent.h"
#include "GrowComponent.h"
#include "QuiteComponent.h"
#include "BreedComponent.h"
#include "ClientFlyComponent.h"
#include "IClientMob.h"

using namespace game::hc;

typedef std::multimap<int, int> BreedingItemMap;

typedef bool (*FilterActorFunc)(ClientActor* actor, void* userdata);
namespace game
{
	namespace common
	{
		class PB_ActorMob;
	}
}

namespace FBSave
{
	struct ActorMob;
}

class ClientItem;
class BTreeIns;
class BTBlackboard;
class GridRuneItemData;
class AttackingTargetComponent;
class HPProgressComponent;
class TeamComponent;
class GrowComponent;
class BreedComponent;
class QuiteComponent;
class AngryComponent;
class TameComponent;
class EquipGridContainer;
class GridContainer;
class MobAttrib;
class SandboxTLManager;
class SandboxTimeline;
struct ScriptCmpData;
struct MonsterDef;
class VacantComponent;
class AIUpdateFrequency;
class ClientFlyComponent;
//tolua_begin
struct InteractFuncDef
{
	short InteractionType;
	int ID;
	bool Show;
};
//tolua_end

//tolua_begin
enum
{
	STRDEF_SLEEP_STATUS = 1,  //此状态不能睡觉
	STRDEF_SLEEP_NOTHERE = 2,     //这个地方不能睡觉
	STRDEF_SLEEP_DAYTIME = 3,
	STRDEF_SLEEP_TOOFAR = 4,
	STRDEF_SLEEP_MOBATTACK = 80301, //周围有敌意的生物
	STRDEF_SLEEP_NOTIPS = -1,   //规则限定 需自己添加提示
};

//breed item type
enum BREED_ITEM_T
{
	BREED_ITEM_NOFOOD = 0,  //不是食物
	BREED_ITEM_FOOD, //加血食物
	BREED_ITEM_LOVE, //成长繁殖食物（成长 > 繁殖）
	BREED_ITEM_QUITE, //让它安静，可以挤奶等(副产也是这个类型食物);
};

enum
{
	MOB_SCRIPTNOTIFY_INTERACT = 1,		//交互
	MOB_SCRIPTNOTIFY_ATTACKED = 2,		//受击
	MOB_SCRIPTNOTIFY_DIE = 3,			//死亡
	MOB_SCRIPTNOTIFY_COLLIDE = 4,		//碰撞
	MOB_SCRIPTNOTIFY_ENTERWORLD = 5,	//进入世界
	MOB_SCRIPTNOTIFY_LEAVEWORLD = 6,	//离开世界
};

//tolua_end

//  CREATUREATTR //对应 services/mob.lua 里面 CREATUREATTR 如果需要存档就加在里面
enum CREATUREATTR
{
	VIEW_DISTANCE = 26, //视野距离
	BODY_LERP_SPEED = 27, //转身速度
	ATK_PHYSICAL = 28, //物理攻击
	ATK_MAGIC = 29, //元素攻击
	DEF_PHYSICAL = 30, //物理防御
	DEF_MAGIC = 31, //元素防御
	EXTRA_HP = 32, //临时生命值
	TOUGHNESS = 33, //韧性值
};

class EXPORT_SANDBOXGAME ClientMob;
class ClientMob	: public ActorLiving, public IClientMob //tolua_exports
{ //tolua_exports
	H_USEMODULELONELY_NOPARAM(isInteracting, bool, false)
	H_USEMODULELONELY_ONEPARAM_INT(addOpenDialogueUIN, int)
	H_USEMODULELONELY_ONEPARAM_INT(removeOpenDialogueUIN, int)
	H_USEMODULELONELY_NOPARAM_INT(closeDialogue)
	// 这里会添加proto的include链
	//H_USEMODULELONELY_ONEPARAM_INT(getInteractData, google::protobuf::RepeatedPtrField<game::common::PB_IntertactData>*)
	//H_USEMODULELONELY_ONEPARAM_INT(resetInteractData, const google::protobuf::RepeatedPtrField<game::common::PB_IntertactData>*)
	H_USEMODULELONELY_NOPARAM_INT(getInteractNum)
	H_USEMODULELONELY_ONEPARAM(getInteractDef, const InteractFuncDef*, nullptr, int)
	H_USEMODULELONELY_ONEPARAM_INT(setInitInteractFunc, bool)
	H_USEMODULELONELY_ONEPARAM_INT(setShopDisplay, bool)
	H_USEMODULELONELY_NOPARAM(getShopDisplay, bool, false)
	H_USEMODULELONELY_ONEPARAM_INT(setOpenStoreConfig, Rainbow::FixedString)
	H_USEMODULELONELY_NOPARAM(getOpenStoreConfig, Rainbow::FixedString, "")
	DECLARE_SCENEOBJECTCLASS(ClientMob)
public:
	//tolua_begin
	ClientMob();

	void createEvent();//事件注册
	void deleteEvent();//事件注销

	virtual flatbuffers::Offset<FBSave::SectionActor> save(SAVE_BUFFER_BUILDER &builder) override;
	virtual bool load(const void *srcdata, int version) override;
	virtual bool todoLoad(const void* srcdata, int version);
	virtual ACTOR_MOVEMODE_T getMoveMode() override;
	virtual float getRunWalkFactor() override;
	virtual void setRiddenByActor(ClientActor *p, int i=0);
	virtual bool interact(ClientActor*player, bool onshift=false, bool isMobile=false) override;
	virtual float getOxygenUseRate() override;
	virtual PackContainer *getBags() override;
	//tolua_end
	//初始化一个空的mob
	virtual void initDefault();

	// 实例化一个生物
	static ClientMob* Instantiate(int id, const jsonxx::Object& obj, int mapid=-1);


#pragma region IClientMob
	virtual ClientMob* CastToMob() override;
	virtual void SetMobDieInDay(bool dieInDay, int deathAnimId = -1) override
	{
		setDieInDay(dieInDay, deathAnimId);
	}
#pragma endregion

public:
	//tolua_begin
	/*
		v2:添加参数mobtype， mobtype=1（家园养殖场生物）  mobtype=2（宠物）
	*/
  //20210812: 新增是否触发触发器参数 codeby:wangshuai
	static ClientMob *createFromDef(int monsterid, int mobtype=0, bool trigger = true, bool init = true, bool bodyAsyncLoad=true, bool needInit = true);
	static void initMobBody(ActorBody *body, const MonsterDef *def, const bool initModel = false, bool playEffect = true);
	static ClientMob *createFromDef(jsonxx::Object mobJson);


	virtual bool init(int monsterid);

	bool init(int monsterid, const jsonxx::Object& obj);
	bool mobInit(int monsterid);
	
	void InitModify();
	void InitModify(const jsonxx::Object& obj,bool isCreat = true);
	void LoadScriptComponent();

	bool changeBtree(const char* uuid, const char* treepath, const char* bbpath);
	void reloadAI();
	void loadAI();
	void clearAI();

	void ParseAI(jsonxx::Array aiJson);

	void ParseAIByMonsterModConfig(const MonsterDef* def, const jsonxx::Object& jsonObj);
	void ParseAIByFightModConfig(const MonsterDef* def, const jsonxx::Object& jsonObj);
	
	void ParseAvatarInfo(jsonxx::Object avatarInfoJson);
	bool isLoadAiEditNode();
	virtual void enterWorld(World *pworld);
	virtual void leaveWorld(bool keep_inchunk);
	virtual void tick();
	virtual void aiTick();
	virtual bool  haveBtree();
	virtual void update(float dtime);
	virtual void setObjType(int objType)
	{
		m_ObjType = objType;
	}
	virtual int getObjType() const override;
	virtual bool attackedFrom(OneAttackData &atkdata, ClientActor *attacker);
	virtual bool todoAttackedFrom(OneAttackData& atkdata, ClientActor* attacker);
	virtual bool canSpawnHere(World *pworld, const WCoord &pos);
	virtual ATTACK_TARGET_TYPE getAttackTargetType();
	virtual void onClear();
	virtual void onDie();
	virtual ActorBody *newActorBody();

	bool ProcessDie() override
	{
		onDie();
		return true;
	}

	//added by dongjianan 
	//desc: 补丁一个创建MobAttrib的工厂方法
	virtual MobAttrib* CreateAttrib();
	

	virtual int getMass();

	float getBlockPathWeight(World *pworld, const WCoord &pos);
	float getBlockPathWeight(const WCoord &pos);

	virtual int getViewDist() override;
	virtual void setViewDist(int dist) override { m_viewDistance = dist; }

	virtual float getAttackDist();

	virtual float getVerticalFaceSpeed();

	//virtual bool interactPlot(ClientPlayer *player);
	//virtual bool checkInteractPlot(ClientPlayer *player);
	virtual bool IsPotionApplicable(int buffid);
	virtual void onCollideWithPlayer(ClientActor *player) override;

	virtual bool canNavigation();

	virtual bool isDead();

	/*
		进行Actorbody裁剪
		Mob类单独 设置为视野范围
	*/
	virtual void setBodyCull();
	/*
		设置怪物可视范围，用于Actorbody的裁剪
	*/
	float SetMobVisibleDistance();
	//void getInteractData(RepeatedPtrField<PB_IntertactData>* pdatas);
	//void resetInteractData(const RepeatedPtrField<PB_IntertactData>* pdatas);

	bool attackActorRanged(ClientActor *target);
	//可以传投射物的id，buffid，投射的数量。 多个的时候以第一个为中心，其他的随机分布
	virtual void doProjectileAttack(ClientActor *target, int projectileId, float power, int buffId, int count = 1, int dropType = 0);
	//枪械发射
	virtual void doGunAttack(ClientActor* target, int projectileId, int count = 1,int attackType = 1);
	virtual void doGunAttacks(ClientActor* target, int projectileId,  const std::vector<int> & attackTypes );
	//生物拾取逻辑
	void pickItem(ClientItem *item, int puttype=0); //puttype 0装备栏 1背包
	
	ClientMob *getOccupyMob(const WCoord &blockpos);
	bool findNearRandomBlock(int blockid, WCoord &blockpos, int radius_xz, int radius_y, int trynum=1);

	/*void addOpenDialogueUIN(int uin);
	void removeOpenDialogueUIN(int uin);*/

	bool isSleeping()
	{
		return getFlagBit(ACTORFLAG_SLEEP);
	}
	void setSleeping(bool b);

	virtual void setBedBindPos(WCoord pos) { }

	virtual WCoord getBedBindPos() { return WCoord::zero; }

	virtual int GetJumpHighest();
	virtual int GetJumpLongest();

	virtual bool needSaveInChunk();
	
	virtual void SetNeedSaveInChunk(bool needSaveInChunk) override;

	void dropBagsAll();
	virtual int sleepInBed(const WCoord &blockpos);
	virtual void wakeUp();

	void setOxygenNeed(bool b)
	{
		m_NeedOxygen = b;
	}
	MobAttrib* getMobAttrib();
	
	virtual int onFeed(int itemid, int feedType = 0); //进食效果(主要是加血) feedType 0手持交互 1 饲料槽进食
	virtual void onFeedCallback(int breedType, int feed_ret, ClientPlayer* player = NULL); //breedItemType:食物类型
	void onFeedHeldFodderCallback(int breedType, int feed_ret, ClientPlayer* player = NULL);
	void setSheared(bool flag);
	bool getSheared()
	{
		return getFlagBit(ACTORFLAG_SHEARED);
	}
	void setColor(int color);
	void setColorEx(unsigned int color);
	int getColor()
	{
		return m_Color;
	}
	//模型颜色 
	void setBodyColorAndNotify(unsigned int color);
	void setScale(float scale);
	void setInfuse(bool flag)
	{
		setFlagBit(ACTORFLAG_INFUSE, flag);
	}
	bool getInfuse()
	{
		return getFlagBit(ACTORFLAG_INFUSE);
	}
	//IClientMob
	virtual void setPersistance(bool flag) override
	{
		setFlagBit(ACTORFLAG_PERSISTENCE, flag);
	}
	bool getPersistance()
	{
		return getFlagBit(ACTORFLAG_PERSISTENCE);
	}
	void setImmuneToFire(int i);
	void setImmuneAttackType(int i, bool isAdd = true);
	int getTimeSinceIgnited()
	{
		return m_TimeSinceIgnited;
	}
	void setTimeSinceIgnited(int iTimeSinceIgnited)
	{
		m_TimeSinceIgnited = iTimeSinceIgnited;
	}

	int getEquipItem(int slot);
	void addInitEquip(int slot, int itemid, int enchantid=-1,int userDataInt = 1);
	void addInitEquip_byRune(int slot, int itemid, const GridRuneItemData& runeItem, int userDataInt = 1);
	void setCanPickUpLoot(bool flag)
	{
		m_CanPickUpLoot = flag;
	}
	int getCollarColor()
	{
		return m_CollarColor;
	}
	void setCollarColor(int collarColor);
	void playTameEffect(bool flag);
	void playInteractionEffect();

	
	const MonsterDef *getDef()
	{
		return m_Def;
	}
	
	//void mobAdult();
	virtual bool managedByChunk()
	{
		return false;
	}

	void getFeedInfo(int &feedId,int &feedCount);//获取该生物对应的饲养槽id和消耗次数

	void setAISitting(bool b)
	{
		setFlagBit(ACTORFLAG_AI_SIT, b);
	}
	bool getAISitting()
	{
		return getFlagBit(ACTORFLAG_AI_SIT);
	}

	void setAIToppleOver(bool b)
	{
		setFlagBit(ACTORFLAG_AI_TOPPLEOVER, b);
	}

	bool getAIToppleOver()
	{
		return getFlagBit(ACTORFLAG_AI_TOPPLEOVER);
	}
		
	void setHome(int iHomeDist, int x, int y, int z);
	void setHomeDist(int iHomeDist)
	{
		m_HomeDist = iHomeDist;
	}
	bool isInHomeDist(int x, int y, int z);
	int getHomeDist() {return m_HomeDist;}

	virtual void setTraceDist(int iTraceDist) override
	{
		m_TraceDist = iTraceDist;
	}
	int getTraceDist()
	{
		return m_TraceDist;
	}

	WORLD_ID getCombineID()
	{
		return m_combineID;
	}


	void setCombineID(WORLD_ID id)
	{
		m_combineID = id;
	}

	void setPanic(bool panic)
	{
		m_Panic = panic;
	}
	void setAIActive(bool active)
	{
		m_NeedUpdateAI = active;
	}
	virtual bool needUpdateAI();
	bool getPanic()
	{
		return m_Panic;
	}
	void ClearMob(int stayTick = 0)
	{
		setNeedClear(stayTick);
	}

	bool getCanRideByPlayer()
	{
		return m_bcanRideByPlayer;
	}

	void setCanRideByPlayer(bool canRideByPlayer)
	{
		m_bcanRideByPlayer = canRideByPlayer;
	}

	void setAifire(bool setfire)
	{
		m_bAiFire = setfire;
	}

	bool getAifire()
	{
		return m_bAiFire;
	}

	int getMilkingTimes()
	{
		return m_MilkingTimes;
	}

	void setMilkingTimes(int n)
	{
		m_MilkingTimes = n;
	}

	virtual int getDefID();

	void setBonFirePos(WCoord pos)
	{
		m_BonFirePos = pos;
	}
	WCoord getBonFirePos()
	{
		return m_BonFirePos;
	}

	virtual std::string getFullyCustomModelKey();

	virtual void updateBodyByFullyCustomModel();
	virtual void updateBodyByImportModel();


	void addAiTaskTempt(int iPriority, float speed, int iSeedID, bool bScared);
	void removeAiTaskTempt(AIBase *aiTempt);
	void addAiTaskLayEggs(int iPriority, int layprob, const char *laysound, int item1, int prob1 = 100, int item2 = 0, int prob2 = 0, int item3 = 0, int prob3 = 0);
	void addAIPickupItemExItem(int itemID);

	bool followOwnerAttack(ClientActor *pTarget, ClientActor *pOwner);
	bool interactBlock(const WCoord &blockpos);
	void destroyBlock(const WCoord &pos, bool destroy_effect, int type = BLOCK_MINE_NOTOOL, bool dropItem = true);

	
	ClientMob *getNearbyItem(const std::vector<int>& ids, int range = 3*BLOCK_SIZE);
	ClientMob *getNearbyMobForCombine(const std::vector<int>& ids, int range = 8*BLOCK_SIZE);
	ClientMob *getNearbyMob();
	ClientMob *getNearbyMate();
	ClientMob *selectNearMob(int iMobDefID, int grouptype, int range, void* pfunc = NULL, void* userdata = NULL);
	//monsterType : OBJ_TYPE_MONSTER
	std::vector<ClientActor *> selectAllMobs(int iMobDefID, int grouptype, int range,int monsterType=0);
	std::vector<ClientActor*> selectAllMobs(int range = 8 * BLOCK_SIZE);

	virtual void playHurtSound();
	virtual void playDeathSound();
	virtual void playStepSound();
	virtual void playSaySound();
	virtual void playAttackSound();
	//IClientMob
	virtual int GetDanceState() override { return m_nDanceState;};
	virtual void SetDanceState(int state) override { m_nDanceState = state;}
	bool tryMoveToXYZ(int x, int y, int z, float speed);
	virtual bool getDirectSwitchAni() override { return m_directSwitchAni; }
	virtual void setDirectSwitchAni(bool b) override { m_directSwitchAni = b; }
	bool mountActor_mob(ClientActor *actor);
	bool IsTickForever();
	bool SetTickForever(bool bForever);
	//tolua_end
	virtual bool isStopBodyRotation() { return IsStopBodyRotation; }
	virtual int getLastHeadIconItemID() override { return m_LastHeadIconItemID; }
	virtual void setLastHeadIconItemID(int itemID) override { m_LastHeadIconItemID = itemID; }
	virtual void setClimbing(bool b) override { m_Climbing = b; }
	virtual bool getClimbing() override { return m_Climbing; }
	//tolua_begin
	void calUnmountPos(ClientActor *actor);
	void showSkin(const char* skinname, bool show,bool isNeedSync = false);
	//是所有的模型隐藏和显示 code lihuimiao
	void showAllSkins(bool show);

	int addBagsItem(int itemid, int num);
	int getCurToolID();
	void playBlockPlaceSound(int blockid, int x, int y, int z);
	void playBlockDigSound(int blockid, int x, int y, int z);
	void addCurToolDuration(int num);
	int removeItemInNormalPack(int itemid, int num);
	void shortcutItemUsed(bool ignoreDurable = false);

	//DialogueComponent函数（供lua使用）
	void closeDialogue() { EXEC_USEMODULE(closeDialogue); }
	void addOpenDialogueUIN(int uin) { EXEC_USEMODULE(addOpenDialogueUIN,uin); }
	void removeOpenDialogueUIN(int uin) { EXEC_USEMODULE(removeOpenDialogueUIN, uin); }
	bool isInteracting() { return EXEC_USEMODULE(isInteracting); }

	int getInteractNum();
	const InteractFuncDef *getInteractDef(int index);
	const MonsterDef *getMonsterDef() const;
	bool getCanOpenDevStore() { return EXEC_USEMODULE(getShopDisplay); }
	//设置显示名称
	void setDisplayName(std::string name);
	//宠物自定义昵称
	void setmodName(std::string mobName);
	//设置随机野人名字
	void setRandWildmanName(std::string name);
	//获取显示名称
	virtual std::string getDisplayName() override;
	// 重置行为树
	void ResetBTree();
	// 黑板数据获取、设置(通过json)
	int/*BTInstanceID*/ GetBlackboardID() const;

	AI_MOTION_TYPE GetMotionType() { return m_MotionType; };

	/*
		家园生物--设置服务器id
	*/
	void setHomeLandServerId(std::string serverid);


	void dropEgg(int itemid, int num); //生物掉落对应的蛋并保存信息 code-by:lizb

	
	bool placeBlock(int blockid, int x, int y, int z, int face, float facept_x = 0, float facept_y = 0, float facept_z = 0);

	virtual bool isInvulnerable(ClientActor *attacker);
	bool isDodge();
	void setMale(bool set){ m_bMale = set;}
	bool getMale(){ return m_bMale;}
	bool getIsPet();
	int getItemSkillTargetCamp(int skillId);
	bool canAttackByItemSkill(int skillId, ClientPlayer* player);
	void setDieTick(int dieTicks) { m_DieTicks = dieTicks;}


	// 加载、释放行动者事件监听器
	virtual void LoadActorEventListen() override;
	//void setLocoMotion(ActorLocoMotion *locoMotion) { m_LocoMotion = locoMotion;}

	//void closeDialogue();
	//bool isInteracting();
	//转换mob 根据monsterid	
	bool transformation(int monsterid);
	void SetMotionType(AI_MOTION_TYPE motp) { m_MotionType = motp; };

	void SetTriggerActorAttrib(ActorAttribut* attribut) { m_ActorAttribTrigger = attribut; };
	ActorAttribut* GetTriggerActorAttrib() { return m_ActorAttribTrigger; };
	virtual bool checkIfSavageSleeping() override;
	bool checkIfSavageAttracked();	// 野人是否被烤肉吸引
	void transToNewSavage();	//变身成为新野人
	bool OpenBTreeForTest();

	void setEvadeCD(int num) { m_iEvadeCDTick = num; }
	bool canEvade() { return m_iEvadeCDTick == 0; }
	void setNeedRunAway(bool state) { m_bNeedRunAway = state; }
	bool isNeedRunAway() { return m_bNeedRunAway; }

	virtual bool canAttack() { return true; }
	virtual void handleTouch(ClientPlayer* player);

	void setRunCurAICD(AI_MOTION_TYPE aiType, int num) { m_RunAICDList[aiType] = num; }
	bool canRunCurAI(AI_MOTION_TYPE aiType) 
	{
		if (m_RunAICDList.find(aiType) == m_RunAICDList.end()) return true;
		return m_RunAICDList[aiType] == 0; 
	}

	void setStayBlockTime(int num) { m_iStayBlockTime = num; }
	int getStayBlockTime() { return m_iStayBlockTime; }
	void setIsUseAILua(bool isUseAILua) { m_isUseAILua = isUseAILua; }
	bool IsUseAILua() { return m_isUseAILua; }
	virtual std::string getActorType() {return "ClientMob";}
	virtual bool isSupportSaveExt() {return false;}
	virtual void save(jsonxx::Object &obj);
	virtual bool load(const jsonxx::Object &obj, int version);
	virtual ClientActor* clone();

	virtual bool supportSaveToPB();
	virtual int saveToPB(game::hc::PB_GeneralEnterAOIHC *pb);
	virtual int LoadFromPB(const game::hc::PB_GeneralEnterAOIHC &pb);
	int saveToMobPB(game::common::PB_ActorMob* actorMob);
	int LoadFromMobPB(const game::common::PB_ActorMob& actorMob);

	//--GrowComponent
	bool isAdult();
	int getGrowingAge();
	int getGrowingDValue();
	int getGrowingTime();
	void setGrowingAge(int growingAge);
	void setGrowingDValue(int dValue);
	void setGrowingTime(int growingTime);
	bool canFeed();
	bool isEaten();
	bool isNeedFeed();
	bool hadFeedFood();
	//static void initMobGrowTime();
	int getMobGrowTime(int mobID);
	int getMobGrowDValue(int mobID);
	int getLoveCD(int mobID);
	void setLoveCD(int cd);
	void setEaten(bool eaten);
	void setCurNeedFeedNum(int num);
	int getCurNeedFeedNum();

	//--BreedComponent
	bool isInLove();
	bool isInHate();
	void setInLove(int inLove);
	int getInLove();
	//static void initBreedItem();
	int isBreedItem(int itemID); //0: 不是食物,   1: 食物,  2: 食物+繁殖

	//-- QuiteComponent
	bool isInQuite();
	void setQuiteTick(int qTick);
	void setReadyToQuit(int num);
	void setRealQuit(int num);
	int getInQuite();
	int getReadyToQuit();
	int getRealQuit();

	//-- AngryComponent
	void setAngry(bool angry);
	bool getAngry();
	void setAIAngry(bool angry);
	bool getAIAngry();

	//-- TameComponent
	int getTamedOwnerID();
	void setTamedOwnerID(int uin);
	bool getTamed();
	void mobTamed(int owneruin, int tamed_target);
	void setTamedOwnerUin(int iUin);
	ClientPlayer *getTamedOwner();
	int getSpawnSkillID();
	bool pushToTamedOwnerTamedList();//code-by dengpeng 进入玩家的驯养列表
	void mobToWild(int tamed_target); //驯服生物转野生 code-by:lizb
	bool getPlayClearFx();
	void SetPlayClearFx(bool clearFx);
	void setPandaRunAwayTick(int tick) {
		if (m_pandaRunAwayTick > 0) return;
		m_pandaRunAwayTick = tick;
	}
	//tolua_end

	bool mountActor(ClientActor *actor);
	template<typename T, typename ...Args>
	void addAiTask(int iPriority, Args... args)
	{
		if (nullptr == m_AITask)
		{
			m_AITask = ENG_NEW(AITask)( this);
		}
		T* NewAI = ENG_NEW(T)( this, args...);
		m_AITask->addTask(iPriority, NewAI);

		if (IsUseAILua())
		{
			NewAI->m_bPriority = iPriority;
			AIBase * NewAIBase = NewAI;
			MNSandbox::GetCoreLuaDirector().CallFunctionM("AILuaManager", "AILuaCurTaskAdd", "u[AIBase]", NewAIBase);
		}
	}

	template<typename T, typename ...Args>
	void addAiTaskTarget(int iPriority, Args... args)
	{
		if (nullptr == m_AITaskTarget)
		{
			m_AITaskTarget = ENG_NEW(AITask)( this);
		}
		T* NewAI = ENG_NEW(T)( this, args...);
		m_AITaskTarget->addTask(iPriority, NewAI);

		if (IsUseAILua())
		{
			NewAI->m_bPriority=iPriority;
			AIBase * NewAIBase = NewAI;
			MNSandbox::GetCoreLuaDirector().CallFunctionM("AILuaManager", "AILuaCurTaskTargetAdd", "u[AIBase]", NewAIBase);
		}
	}
	WORLD_ID getMobAttackTarget();
	void PlayModBeHit(bool explode);
	virtual bool NeedTickForever()override;

#pragma region ICientMob
	virtual IActorLocoMotion* GetMobLocoMotion() override;

	virtual WCoord& GetMobPosition() override
	{
		return getPosition();
	}

	virtual int GetMobType() override;

	virtual int GetMobID() override
	{
		return getDefID();
	}
	virtual void MobWakeUp() override
	{
		wakeUp();
	}
	virtual bool IsInvulnerable() override
	{
		return isInvulnerable(nullptr);
	}
#pragma endregion

public:
	/* 描述 */
	const std::string& GetDescription() const { return m_description; }
	void SetDescription(const std::string& desc);
	
	// 针对接入了模型组件的怪物，初始化ActorBody后,需要单独设置ActorBody的某些属性
	void SetModelActorBody(int iModelType, const std::string& sModelPath, int iExtraData = -1, bool saveMesh = false);

	int getMonsterId() { return m_monsterId; }
	static MNSandbox::ReflexClassParam<ClientMob, int> R_MonsterId;
//	static MNSandbox::ReflexClassParam<ClientMob, std::string> R_DisplayName;
//	static MNSandbox::ReflexClassMember<ClientMob, std::string> R_Desc;

	TameComponent* getTameComponent();
	TameComponent* sureTameComponent();

	bool SetLocoMotionType(int locotype, bool isNo);

public:
    // 添加剥皮相关属性和方法
    bool m_isSkinned; // 是否已被剥皮
    bool m_isSkinning;                // 是否正在剥皮
    float m_skinningTime;             // 剥皮总时长
	float m_corpseRemainTime;         // 尸体剩余时间
    float m_skinningElapsedTime;      // 已经过的剥皮时间
    long m_skinningPlayerID;     // 正在剥皮的玩家ID
	int m_skinningToolID;         // 剥皮工具ID

    void setSkinned(bool skinned) { m_isSkinned = skinned; }
    bool getSkinned() const { return m_isSkinned; }
	bool isSkinning() const { return m_isSkinning; }

    // 开始剥皮过程
    bool startSkinning(ClientPlayer* player);
	bool startSkinningClient(ClientPlayer* player);
	bool startSkinningServer(ClientPlayer* player);
    
    // 更新剥皮进度
    void updateSkinning(float dtime, ClientPlayer* player);
    
    // 取消剥皮过程
    void cancelSkinning(ClientPlayer* player);
	void cancelSkinningClient(ClientPlayer* player);
	void cancelSkinningServer(ClientPlayer* player);
    
    // 完成剥皮过程
    bool finishSkinning(ClientPlayer* player);
	bool finishSkinningClient(ClientPlayer* player);
	bool finishSkinningServer(ClientPlayer* player);
    
    // 获取剥皮进度百分比
    float getSkinningPercentage() const;
    
    // 更新剥皮进度UI
    void updateSkinningUI(ClientPlayer* player, bool forceUpdate = false);
    
    // 更新尸体外观为已剥皮状态
    void updateSkinnedAppearance();

	// 计算与另一个Actor之间的距离
	float getDistanceTo(ClientActor* actor);

	// 检查玩家是否在剥皮有效距离内
	bool isPlayerInSkinningRange(ClientPlayer* player);	

private:
	//void LocomotionPosGet(Rainbow::Vector3f& value) const;
	//void LocomotionPosSet(const Rainbow::Vector3f& value);
	void ReleaseMobEventListen();
	int MonsterIdGet() const;
	void MonsterIdSet(const int& value);
	const std::string& DisplayNameGet() const;
	void DisplayNameSet(const std::string& value);
	int m_monsterId = 0;
	/*描述*/
	std::string m_description;

	//拆分一些init的函数 方便后期重复调用
	void initAi();

	void initActorAttribTrigger();
	void initAcotrBody(ActorBody* pAcotrBody);
	void initLocomotion();
	void initMobAttrib();
	bool loadAIEditTree(bool islocal);
	bool ModloadAI(const jsonxx::Object& obj);
	void LoadBTree();

	bool isVacantFox(int monsterId);
	void ModBeHitTick();
protected:
	virtual void setMonsterId(const int id);
	//virtual HPProgressComponent* getHPProgressComponent() override;
	//virtual TeamComponent* getTeamComponent() override;
	//virtual GrowComponent* getGrowComponent();
	//virtual BreedComponent* getBreedComponent();
	//virtual QuiteComponent* getQuiteComponent();
	//virtual AngryComponent* getAngryComponent();
	//GrowComponent* m_pGrowComponent;
	//BreedComponent* m_pBreedComponent;
	//QuiteComponent* m_pQuiteComponent;
	//AngryComponent* m_pAngryComponent;
	TameComponent* m_pTameComponent = nullptr;
public:
	//tolua_begin
	static int m_DropItemCallCount; //由于附魔, 天赋的幸运, 导致掉落物品次数改变
	const MonsterDef *m_Def;
	bool m_directSwitchAni;
	bool IsStopBodyRotation;

 	virtual void doActualAttack(ClientActor *target, int targetIndex = 1);
	void checkActorDisplayName(int id,bool isBlick);
	void updateActorDisplayName(int id,int itemId,int itemCount,int animId);
	void syscActorDisplayNameToClient(bool isBlick);
	void syscActorDisplayNameFromHost();

	void onBuffAppend(int buffid, int bufflvl); // 效果附加
	void onBuffRemove(int buffid, int bufflvl); // 效果移除

	void setMobPresetPosKey(unsigned int key) { m_MobPresetPosKey = key; }
	unsigned int getMobPresetPosKey() const { return m_MobPresetPosKey; }

	void generateKeepsakePackage(bool isDefect)
	{
	}
	// 野人躺下，主动或者被动起来的时候需要刷新碰撞盒子
	void updateBound();

	/*
		家园生物--获取服务器id（服务器专用，跟客户端的objid不同）
	*/
	std::string getHomeLandServerId() { return m_ServerWid; }
	/*
		是否是家园宠物
	*/
	bool getIsHomeLandPet();

	void updateModelName(); 
	void updateActorBodySkin();

	void setRangeSameTypeMobRunAway(ClientMob* mob);

	bool getIsStandSleeping()const{
		return m_isStandSleeping;
	}

	void setIsStandSleeping(bool val){
		m_isStandSleeping = val;
	}
	void applyDisplayHomeBillBoard();

	void setRandomScale(float scale)
	{
		m_RandomScale = scale;
	}
	float getRandomScale( )
	{
		return m_RandomScale;
	}
	/*void setInitInteractFunc(bool isInit)
	{
		m_bInitInteractFunc = isInit;
	}*/
	void applyDisplayName();
	bool getIsAttackStatus() {
		return m_isAttackStatus;
	}
	void setIsAttackStatus(bool val) {
		m_isAttackStatus = val;
	}
	virtual bool getIsHideStatus() {
		return m_isHideStatus;
	}
	void setIsHideStatus(bool val);
	bool getIsHurtDropItem() {
		return m_isHurtDropItem;
	}
	void setIsHurtDropItem(bool val) {
		m_isHurtDropItem = val;
	}
	bool getIsBrokenTail() {
		return m_isBrokenTail;
	}
	void setIsBrokenTail(bool val) {
		m_isBrokenTail = val;
	}
	void setBeEvicted(bool value)
	{
		m_Evicted = value;
	}
	bool getBeEvicted()
	{
		return m_Evicted;
	}

	int getAILuaKey()
	{
		return m_isUseAILua ? m_AILuaKey : -1;
	}
	virtual bool isFirstCreate() {
		return m_isFirstCreate;
	}

	virtual void setSpawnPoint(const WCoord& blockpos);

	virtual WCoord& getSpawnPoint() { return m_SpawnPoint; }

	//使用 TimeLine 模拟注册动画事件 (因为云服没有动画)
	void RegisterTLAnimFrameEvent(int seqId, int keyFrame, const std::string& eventName);
	void ActiveTLAnimFrameEvent();
	void ClearTLAnimFrameEvent();
	
	//tolua_end

	void setScorpionHideTick(int val)
	{
		m_scorpionHideTick = val;
	}

	AITask* getAITask()
	{
		return m_AITask;
	}

	BTreeIns* getBTree()
	{
		return m_btree;
	}

	void setData(int data) { m_Data = data; };
	int getData() { return m_Data; }

	virtual ~ClientMob();

	bool getHasAttackTarget();
	void MobTick();
	int m_ObjType;
	std::string createComponentSaveData();
	void loadComponentSaveData(const std::string& componentDataStr);
	flatbuffers::Offset<FBSave::ActorMob> saveMob(SAVE_BUFFER_BUILDER& builder);

	virtual void applyActorCollision(ClientActor* actor);
	virtual void InitDefaultChildren();  // 初始化子节点，自会在首次创建的时候执行，之后读档、克隆、同步等是不会执行

    void setSyncLoadModelWhileNewActorBody(bool b) { m_defaultSyncLoadMonsterModel = b; }
	bool isSyncLoadModelWhileNewActorBody();
	bool checkIfActorCanAttack();

protected:

	virtual void doActualRangeAttack(ClientActor *target);
	virtual bool canDespawn();
	virtual void pickUpLoot();
	

	void AITick();//AI相关Tick

	virtual ActorLocoMotion *newLocoMotion();
	virtual int getBufferId();

	void saveMob(jsonxx::Object &obj);
	void notifyBodyChange();
	void notifyVillagerChange(int changeType, unsigned int changeValue, std::string otherValue = "");
	void notifyVillagerClothChange(std::string name, bool bshow);
	void triggerActorAttribChunk();	//开发者属性变化触发检测


	//怪物被击杀时, 击杀的人获得等级经验
	void AddLevelExp2Killer(ClientPlayer* pPlayer);
	//彼岸菠萝 突发恶疾buff 主动发起攻击
	void suddenIllnessBuffAttack();

	// 野人战士和野人投矛手可穿戴装备
	bool canEquipByPickup(int itemid);

	// 攻击流程
	void attackProcess(OneAttackData& atkdata, ClientActor* target);
	void attackProcessNew(OneAttackData& atkdata, ClientActor* target);
	void SaveMobTriggerAttr(SAVE_BUFFER_BUILDER& builder, flatbuffers::Offset<flatbuffers::Vector<float>>& vec);
	void LoadMobTriggerAttr(const flatbuffers::Vector<float>* vec);
public:
	// 监听
	//MNSandbox::ListenerClass<ClientMob> m_listenBoundBoxChanged;

	// 回调
	void OnUpdateBoundBoxChanged();
public:
	DECLARE_CACHE_COMPONENT(VacantComponent)
	DECLARE_CACHE_COMPONENT(GrowComponent)
	DECLARE_CACHE_COMPONENT(BreedComponent)
	DECLARE_CACHE_COMPONENT(QuiteComponent)

public:
	virtual VacantVortexComponent* getVacantVortexComponent() override {
		return m_pVacantVortexComponent;
	}
	virtual ClientFlyComponent* getClientFlyComponent() override {
		return m_pClientFlyComponent;
	}
	virtual void cacheClientFlyComponent(ClientFlyComponent* pClientFlyComponent) {
		m_pClientFlyComponent = pClientFlyComponent;
	}
	virtual ClientAquaticComponent* getClientAquaticComponent() override {
		return m_pClientAquaticComponent;
	}
	virtual void cacheClientAquaticComponent(ClientAquaticComponent* pClientAquaticComponent) {
		m_pClientAquaticComponent = pClientAquaticComponent;
	}

protected:
	//int m_GrowingAge; 
	int m_CollarColor;
	int m_Color;
	int m_TimeSinceIgnited;
	int m_pandaBeenAttacked;
	int m_pandaBeenAttackedTick;
	int m_pandaRunAwayTick;
	int m_DespawnTicks;
	int m_DieTicks;
	int m_SaySoundTimer;

	int m_TraceDist;
	int m_HomeDist;
	//int m_InLove; //爱心, <0:暴躁状态, 会争斗,  0: 正常,   >0:爱慕状态, 会繁殖
	//int m_InQuite; //安静状态
	//int m_TamedOwnerUin;
	bool m_CanPickUpLoot;
	bool m_AISitting;
	//bool m_Angry;
	bool m_Panic;
	//bool m_PlayClearFX;
	bool m_NeedOxygen;
	bool m_needSaveInChunk;
	float m_RandomScale;
	bool m_NeedUpdateAI;
	AITask *m_AITask;
	AITask *m_AITaskTarget;
	int m_nDanceState;
	bool m_bcanRideByPlayer;
	bool m_bAiFire;
	bool m_bMale;
	int m_iEvadeCDTick; //躲避CD
	bool m_bNeedRunAway; //逃跑，部分生物被攻击后，附近生物都会逃跑
	//bool m_bEaten;  //是否已进食（原有自动成长改为进食后再成长）
	//int m_iReadyToQuit; //准备进入Quit状态的等待tick
	//int m_iRealQuitTick; //等待完成后m_InQuit的值
	//int m_iCurNeedFeedNum;	// 触发成长和繁殖还需要进食的次数（目前只有饲料槽需要）
	std::map<AI_MOTION_TYPE, int> m_RunAICDList;	// 执行ai的CD
	int m_iStayBlockTime;	// 停留在方块上时间
	//bool m_bWaitToAdult;	// 待成年状态 时间上已满足成长需求
	ActorEventListen* m_EventListen;

	int m_MilkingTimes;
	WORLD_ID m_combineID;
	//bool m_bDShopDisplay;

	//std::vector<int>m_OpenDialogueUINs;
	//std::vector<InteractFuncDef> m_InteractFuncs;
	//bool m_bInitInteractFunc;
	AI_MOTION_TYPE m_MotionType;
	AIBase *m_aiTempt;
	ActorAttribut *m_ActorAttribTrigger;	//角色属性
	VacantVortexComponent* m_pVacantVortexComponent = nullptr;
	ClientFlyComponent* m_pClientFlyComponent = nullptr;
	ClientAquaticComponent* m_pClientAquaticComponent = nullptr;
	AIUpdateFrequency* m_pAIUpdateFrequency = nullptr;
	int m_iTickTriggerCount;	//开发者tick触发计数 10个tick触发一次

	//int m_iEatFoodTime;
	WCoord m_BonFirePos;		//被吸引的篝火位置
	short m_iTickAnimAwake;		//播放被叫醒动画的tick
	std::string m_displayName;	//显示名称
	std::string m_modName;		//宠物自定义昵称

	int m_suddenIllnessBuffTraceTick;
	bool m_isSuddenIllnessFindMob;

	// 行为树AI
	BTreeIns* m_btree;
	BTBlackboard* m_bb;

	BTreeIns* m_needClearBtree;
	BTBlackboard* m_needClearBb;

	/*
		家园生成怪对应的服务器id（服务器用来校验）
		（服务器专用，跟客户端的objid不同）
	*/
	std::string  m_ServerWid;
	bool m_isStandSleeping;

	bool m_Climbing;

	//用于标识AI管理方式
	bool m_isUseAILua;
	//保留AILua协程索引
	int m_AILuaKey;

	int m_addBuffCount;
    int m_scorpionHideTick;
    int m_scorpionSyncTick;
	bool m_isAttackStatus;//战斗状态
	bool m_isHideStatus;//躲藏状态
	bool m_isHurtDropItem;//受伤掉落物品
	bool m_isBrokenTail;//断尾
    bool m_defaultSyncLoadMonsterModel;//默认是否同步加载怪物模型

	bool m_Evicted;//是否被驱赶
	bool m_isFirstCreate; //是否第一次创建 ，被序列化不算第一次，有时需要做些属性随机。
	WCoord m_SpawnPoint;  //出生点

	MNSandbox::ListenerFunction<ClientActor*,int>* m_listenerSetRiddenByActor;
	int m_viewDistance{ 0 };

public:
		std::string m_SaySound;		//保存说话声音路径
private:
	int m_Data; //通用数据存储
	int m_LastHeadIconItemID;
	int m_mobtick;
	int m_noAttackedTicks;		// 未被攻击的tick数
	int m_recoverToughnessTicks;// 韧性恢复的tick数
	int m_lastTickTime{ 0 };
	SandboxTLManager* m_tlMgr{ nullptr };
	SandboxTimeline* m_timeline{ nullptr };
	ScriptCmpData* m_stData{ nullptr };
	bool m_bLoadBTree = false;
	bool m_bInitChildrenNode;    //初始化子节点;
	char m_ModExplodeBeHitState = 0; //0 初始，1 倒下，2 站起
	int m_ModExplodeBeHitActTick;
	unsigned int m_MobPresetPosKey = 0;
}; //tolua_exports



#define MOB_DESPAWN_MINDIST 24   //消失最近距离
#define MOB_DESPAWN_MAXDIST 96   //消失最远距离

#endif