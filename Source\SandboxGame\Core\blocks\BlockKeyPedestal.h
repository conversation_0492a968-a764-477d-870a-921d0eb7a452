#ifndef BLOCK_KEY_PEDESTAL_H
#define BLOCK_KEY_PEDESTAL_H
#include "BlockMaterial.h"

class BlockKeyPedestal : public ModelBlockMaterial //tolua_exports
{ //tolua_exports
	//typedef ModelBlockMaterial Super;
	DECLARE_BLOCKMATERIAL(BlockKeyPedestal)
public:
	virtual void initDefaultMtl() override;
	//tolua_begin
	BlockKeyPedestal();
	virtual ~BlockKeyPedestal();
	virtual WorldContainer* createContainer(World* pworld, const WCoord& blockpos) override;
	//virtual bool hasContainer() override
	//{
	//	return true;
	//}

	virtual void init(int resid) override;
	virtual void onBlockPlacedBy(World* pworld, const WCoord& blockpos, IClientPlayer* player) override;
// 	virtual int  getPlaceBlockData(World* pworld, const WCoord& blockpos, DirectionType face, float hitptx, float hitpty, float hitptz, int def_blockdata) override;
// 	virtual void update(unsigned int dtick) override;
	virtual void onNotify(World* pworld, const WCoord& blockpos, int blockid) override;
	virtual bool onTrigger(World* pworld, const WCoord& blockpos, DirectionType face, IClientPlayer* player, const Rainbow::Vector3f& colpoint = Rainbow::Vector3f(0, 0, 0)) override;

	virtual void onBlockRemoved(World* pworld, const WCoord& blockpos, int blockid, int blockdata) override;
// 	virtual int  convertDataByRotate(int blockdata, int rotatetype) override;
	virtual void dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata = 0, BLOCK_MINE_TYPE droptype = BLOCK_MINE_NOTOOL, float chance = 1.0f, int uin = -1);
	virtual RenderBlockMaterial *getGeomMtl(const SectionDataHandler* sectionData, const WCoord &blockpos, World* world);
	//tolua_end
private:	
	virtual int getBlockGeomID(int* idbuf, int* dirbuf, const SectionDataHandler* sectionData, const WCoord& blockpos, World* world) override;
	virtual int getProtoBlockGeomID(int* idbuf, int* dirbuf) override;

	void playBossEnterEffect(World* pworld, const std::map<int, WCoord>& posmap, WCoord& centerpos, int dir);

	void onPlayRandEffect(World *pworld, const WCoord &blockpos);
private:
	//RenderBlockMaterial *m_StateMtl[2];
	const static int Mtl_Num = 2;
	unsigned int m_stateMtlsIndex[Mtl_Num];

public:
	DECLARE_BLOCKMATERIAL_INSTANCE_BEGIN(BlockKeyPedestal)
		DECLARE_BLOCKMATERIAL_INSTANCE_R_PARAM(R_Dir, int)
	DECLARE_BLOCKMATERIAL_INSTANCE_END(BlockKeyPedestal)

}; //tolua_exports
#endif // BLOCK_KEY_PEDESTAL_H

