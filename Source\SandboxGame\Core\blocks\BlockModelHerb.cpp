
#include "BlockModelHerb.h"
#include "Collision.h"
#include "section.h"
#include "WorldProxy.h"
#include "world.h"
#include "BlockMaterialMgr.h"
#include "EffectManager.h"
#include "special_blockid.h"
//#include "OgreMaterial.h"
#include "SectionMesh.h"
#include "BlockGeom.h"


IMPLEMENT_BLOCKMATERIAL(BlockModelHerb)

void BlockModelHerb::init(int resid)
{
	ModelBlockMaterial::init(resid);
	//属性初始化
	SetToggle(BlockToggle_IsSolid, false);
	
}

void BlockModelHerb::initDrawType()
{
	m_blockDrawType = BLOCKDRAW_GRASS;
}
bool BlockModelHerb::canStayOnPos(WorldProxy *pworld, const WCoord &blockpos)
{
	int downid = pworld->getBlockID(DownCoord(blockpos));
	if(downid == getBlockResID()) return true;

	if(pworld->getBlockID(TopCoord(blockpos)) != getBlockResID()) return false;
	if(canOnlyOnDirt())
	{
		if(downid!=BLOCK_GRASS && downid!=BLOCK_DIRT && downid!=BLOCK_BURYLAND && downid != BLOCK_FARMLAND_PIT) return false;
	}
	else if(!pworld->doesBlockHaveSolidTopSurface(DownCoord(blockpos))) return false;

	return true;
}

void BlockModelHerb::dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata/* =0 */, BLOCK_MINE_TYPE droptype/* =BLOCK_MINE_NOTOOL */, float chance/* =1.0f */, int uin)
{
	if ((blockdata & 8) != 0) return;

	ModelBlockMaterial::dropBlockAsItem(pworld, blockpos, blockdata, droptype, chance, uin);
}


void BlockModelHerb::generateFlowerPotMesh(const SectionDataHandler* sectionData, const WCoord &blockpos, SectionMesh *poutmesh, int meshId, Rainbow::Vector3f offset)
{
	Rainbow::ColorRGBAf verts_light[1] = { Rainbow::ColorRGBAf::black };
	sectionData->getBlockVertexLight(blockpos, verts_light);

	BlockGeomMeshInfo meshinfo;
	SectionSubMesh *psubmesh = poutmesh->getSubMesh(getDefaultMtl());
	if (psubmesh == nullptr) return;
	//getGeom()->getFaceVertsWithOffset(meshinfo, meshId, 1.0f, 0, 0, offset.x, offset.y, offset.z);
	
	BlockGeomTemplate* geom = getGeom(0);
	if(geom)
		geom->getModelFaceVerts(meshinfo, meshId, DIR_NEG_X, offset.x, offset.y, offset.z);
	psubmesh->addGeomBlockLight(meshinfo, &blockpos, verts_light, NULL, getDefaultMtl()->getUVTile());
}

void BlockModelHerb::createBlockMesh(const BuildSectionMeshData& data, const WCoord &blockpos, SectionMesh *poutmesh)
{
	BlockGeomTemplate* geom = getGeom(data.m_LODLevel);
	if (!geom)
		return;

	auto psection = data.m_SharedSectionData;
	Rainbow::ColorRGBAf verts_light[1] = { Rainbow::ColorRGBAf::black };
	psection->getBlockVertexLight(blockpos, verts_light);

	int idbuf[32];
	int dirbuf[32];
	int ngeom = getBlockGeomID(idbuf, dirbuf, psection, blockpos, data.m_World);
	BlockGeomMeshInfo meshinfo;
	SectionSubMesh *psubmesh = poutmesh->getSubMesh(getDefaultMtl());

	SolidBlockMaterial * m_BlockMtl =  NULL;
	float height = 1.0f;
	if(idbuf[0])
	{
		Block baseblock = psection->getNeighborBlock(blockpos, WCoord(0,-2,0));
		m_BlockMtl =  dynamic_cast<SolidBlockMaterial *>(g_BlockMtlMgr.getMaterial(baseblock.getResID()));
		if(m_BlockMtl)
		 height = m_BlockMtl->getBlockHeight(0);	
	}
	else
	{
		Block baseblock = psection->getNeighborBlock(blockpos, WCoord(0,-1,0));
		m_BlockMtl =  dynamic_cast<SolidBlockMaterial *>(g_BlockMtlMgr.getMaterial(baseblock.getResID()));
		if(m_BlockMtl)
		 height = m_BlockMtl->getBlockHeight(0);
	}
	for (int i = 0; i < ngeom; i++)
	{
		geom->getFaceVerts(meshinfo, idbuf[i], 1.0f, 0, dirbuf[i], 0, NULL, (int)((height - 1.0f)*BLOCK_SIZE));
		psubmesh->addGeomBlockLight(meshinfo, &blockpos, verts_light, NULL, getDefaultMtl()->getUVTile());
	}
}

void BlockModelHerb::onNotify(World *pworld, const WCoord &blockpos, int blockid)
{
	ModelBlockMaterial::onNotify(pworld, blockpos, blockid);

	if (!canStayOnPos(pworld->getWorldProxy(), blockpos))
	{
		dropBlockAsItem(pworld, blockpos, pworld->getBlockData(blockpos));
		pworld->setBlockAll(blockpos, 0, 0, 3);
	}
}



bool BlockModelHerb::canPutOntoPos(WorldProxy *pworld, const WCoord &blockpos)
{
	WCoord downpos = DownCoord(blockpos);
	if(canOnlyOnDirt())
	{
		int downid = pworld->getBlockID(downpos);
		if(downid!=BLOCK_GRASS && downid!=BLOCK_DIRT && downid!=BLOCK_BURYLAND && downid != BLOCK_FARMLAND_PIT) return false;
	}
	else if(!pworld->doesBlockHaveSolidTopSurface(downpos)) return false;

	for(int y=0; y<GetBlockDef()->Height; y++)
	{
		if(!BlockMaterial::canPutOntoPos(pworld, blockpos+WCoord(0,y,0))) return false;
	}

	return true;
}

void BlockModelHerb::createCollideData(CollisionDetect *coldetect, World *pworld, const WCoord &blockpos)
{
	coldetect->addObstacle(blockpos*BLOCK_SIZE, blockpos*BLOCK_SIZE + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
}

int BlockModelHerb::getBlockGeomID(int *idbuf, int *dirbuf, const SectionDataHandler* sectionData, const WCoord &blockpos, World* world)
{
	int blockdata = sectionData->getBlock(blockpos).getData();

	//根据blockdata来获取model中的id
	if (blockdata & 8) idbuf[0] = 1;
	else idbuf[0] = 0;
	//前两位表示朝向
	dirbuf[0] = blockdata & 3;
	return 1;
}
