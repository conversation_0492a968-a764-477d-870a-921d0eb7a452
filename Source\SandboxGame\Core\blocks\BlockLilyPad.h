
#ifndef __BLOCK_LILYPAD_H__
#define __BLOCK_LILYPAD_H__

#include "BlockMaterial.h"

class LilyPadMaterial : public BlockMaterial //tolua_exports
{ //tolua_exports
	DECLARE_BLOCKMATERIAL(LilyPadMaterial)
public:
	//tolua_begin
	virtual bool canPutOntoPos(WorldProxy *pworld, const WCoord &blockpos);
	bool canPutOntoFace(WorldProxy *pworld, const WCoord &blockpos, int face);
	void onNotify(World *pworld, const WCoord &blockpos, int blockid);
	virtual int getBlockGeomID(int* idbuf, int* dirbuf, const SectionDataHandler* sectionData, const WCoord& blockpos, World* world);
	int getProtoBlockGeomID(int *idbuf, int *dirbuf);
	virtual void createCollideData(CollisionDetect *coldetect, World *pworld, const WCoord &blockpos);

	virtual bool isDoubleSide()
	{
		return true;
	}
	virtual void createBlockMesh(const BuildSectionMeshData& data, const WCoord &blockpos, SectionMesh *poutmesh) override;

	virtual bool canAttachedToMecha(int curblockdata, DirectionType dir, int neighbor_id, int neighbor_data) override
	{
		return false;
	}
	virtual bool onTrigger(World *pworld, const WCoord &blockpos, DirectionType face, IClientPlayer *player, const Rainbow::Vector3f &colpoint=Rainbow::Vector3f(0, 0, 0));
	virtual void dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata=0, BLOCK_MINE_TYPE droptype=BLOCK_MINE_NOTOOL, float chance=1.0f, int uin = -1) override;
	//tolua_end

	//survive 2.0 modified by navy
	LilyPadMaterial();
	virtual ~LilyPadMaterial();
	//virtual const char* getGeomName() override;
	virtual void initGeomName() override;
	virtual void init(int resid) override;
	virtual SectionMesh* createBlockProtoMesh(int protodata = 0);

	virtual int getPlacedFace(int blockdata)
	{
		return blockdata & 7;
	}

	//virtual BlockDrawType getDrawType() override
	//{
	//	//return BLOCKDRAW_XPARENT;
	//	return BLOCKDRAW_GRASS;
	//}
	virtual void initDrawType() override;
	virtual void initDefaultMtl() override;

	/*virtual bool isOpaqueCube() override
	{
		return false;
	}*/
	int blockdata2Item(int blockdata);
private:	
	int item2Blockdata(int itemid, int blockdata);

}; //tolua_exports

#endif