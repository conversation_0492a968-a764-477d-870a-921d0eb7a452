#include "TerritoryBuffComponent.h"
#include "ClientPlayer.h"
#include "TerritoryQueryInterface.h"
#include "PlayerAttrib.h"
#include "LivingAttrib.h"
#include "container_territory.h"
#include "TerritoryManager.h"
#include "container_erosion.h"
//#include "MiniLogger.h"

using namespace MINIW;

IMPLEMENT_COMPONENTCLASS(TerritoryBuffComponent)

// 静态常量定义（已移除POSITION_TOLERANCE，因为方块位置是整数坐标）

TerritoryBuffComponent::TerritoryBuffComponent()
	: m_player(nullptr)
	, m_isInTerritory(false)
	, m_isInAuthorizedTerritory(false)
	, m_hasMaintenanceMaterials(false)
	, m_currentTerritoryBuffId(0)
	, m_lastCheckedBlockPosition(0, 0, 0)
{
}

TerritoryBuffComponent::~TerritoryBuffComponent()
{
}

void TerritoryBuffComponent::OnEnterOwner(NS_SANDBOX::SandboxNode* owner)
{
	Super::OnEnterOwner(owner);
	
	// 获取玩家对象
	m_player = dynamic_cast<ClientPlayer*>(owner);
	if (!m_player)
	{
		//LOG_WARN("TerritoryBuffComponent: Owner is not a ClientPlayer");
		return;
	}
	
	// 绑定tick事件
	BindOnTick();
	
	// 初始检查 - 设置一个无效方块位置确保第一次会触发检查
	m_lastCheckedBlockPosition = WCoord(-999999, -999999, -999999);
}

void TerritoryBuffComponent::OnLeaveOwner(NS_SANDBOX::SandboxNode* owner)
{
	// 清除所有领地buff
	ClearAllTerritoryBuffs();
	
	// 解绑tick事件
	UnBindOnTick();
	
	m_player = nullptr;
	
	Super::OnLeaveOwner(owner);
}

void TerritoryBuffComponent::OnTick()
{
	if (m_player->GetWorld()->isRemoteMode())//客机不跑
		return;
	if (!m_player)
		return;
		
	// 获取玩家当前方块位置
	WCoord currentBlockPos = CoordDivBlock(m_player->getPosition());
	
	// 只有方块位置变化时才检查
	if (currentBlockPos != m_lastCheckedBlockPosition)
	{
		CheckTerritoryAndUpdateBuff();
	}
}

void TerritoryBuffComponent::CheckCurrentTerritoryStatus()
{
	CheckTerritoryAndUpdateBuff();
}

void TerritoryBuffComponent::CheckTerritoryAndUpdateBuff()
{
	if (!m_player)
		return;
		
	// 获取玩家当前方块位置
	WCoord currentBlockPos = CoordDivBlock(m_player->getPosition());
	
	// 更新记录的方块位置
	m_lastCheckedBlockPosition = currentBlockPos;
	
	// 直接使用方块坐标进行领地查询（TerritoryQuery内部会处理坐标转换）
	// 检查是否在任意领地内
	bool isInAnyTerritory = TerritoryQuery::IsInAnyTerritory(currentBlockPos);
	
	// 检查是否在授权领地内
	bool isInAuthorizedTerritory = false;
	bool hasMaintenanceMaterials = false;
	if (isInAnyTerritory)
	{
		isInAuthorizedTerritory = TerritoryQuery::IsPlayerInOwnTerritory(currentBlockPos, m_player->getUin());
		
		// 如果在授权领地内，检查维护材料状态
		if (isInAuthorizedTerritory)
		{
			TerritoryContainer* territoryContainer = TerritoryQuery::GetTerritoryAt(currentBlockPos);
			if (territoryContainer)
			{
				hasMaintenanceMaterials = HasSufficientMaintenanceMaterials(territoryContainer);
			}
		}
	}
	
	// 状态发生变化时更新buff
	if (m_isInTerritory != isInAnyTerritory || m_isInAuthorizedTerritory != isInAuthorizedTerritory || m_hasMaintenanceMaterials != hasMaintenanceMaterials)
	{
		// 先清除当前的领地buff
		ClearAllTerritoryBuffs();
		
		// 更新状态
		m_isInTerritory = isInAnyTerritory;
		m_isInAuthorizedTerritory = isInAuthorizedTerritory;
		m_hasMaintenanceMaterials = hasMaintenanceMaterials;
		
		// 根据新状态添加对应的buff
		if (m_isInTerritory)
		{
			if (m_isInAuthorizedTerritory)
			{
				// 在授权领地内，根据维护材料状态添加不同的buff
				if (m_hasMaintenanceMaterials)
				{
					// 维护材料充足，添加正常授权领地buff
					AddTerritoryBuff(AUTHORIZED_TERRITORY_BUFF_ID);
					LOG_INFO("Player %u entered authorized territory with sufficient maintenance materials, added buff %d", m_player->getUin(), AUTHORIZED_TERRITORY_BUFF_ID);
				}
				else
				{
					// 维护材料不足，建筑腐蚀中，添加腐蚀buff
					AddTerritoryBuff(TERRITORY_EROSION_BUFF_ID);
					LOG_INFO("Player %u entered authorized territory with insufficient maintenance materials (erosion), added buff %d", m_player->getUin(), TERRITORY_EROSION_BUFF_ID);
				}
			}
			else
			{
				// 在非授权领地内，添加非授权领地buff
				AddTerritoryBuff(UNAUTHORIZED_TERRITORY_BUFF_ID);
				LOG_INFO("Player %u entered unauthorized territory, added buff %d", m_player->getUin(), UNAUTHORIZED_TERRITORY_BUFF_ID);
			}
		}
		else
		{
			// 不在任何领地内，清除所有领地buff
			LOG_INFO("Player %u left territory, cleared all territory buffs", m_player->getUin());
		}
	}
}

void TerritoryBuffComponent::AddTerritoryBuff(int buffId)
{
	if (!m_player || buffId <= 0)
		return;
		
	PlayerAttrib* playerAttrib = m_player->getPlayerAttrib();
	if (!playerAttrib)
		return;
		
	LivingAttrib* livingAttrib = playerAttrib;
	if (!livingAttrib)
		return;
	
	// 检查是否已经有这个buff
	if (!livingAttrib->hasBuff(buffId))
	{
		// 添加buff (buffid, bufflv, customticks, buffInstanceId, objid)
		livingAttrib->addBuff(buffId, 1, -1, 0, 0);
		m_currentTerritoryBuffId = buffId;
	}
}

void TerritoryBuffComponent::RemoveTerritoryBuff(int buffId)
{
	if (!m_player || buffId <= 0)
		return;
		
	PlayerAttrib* playerAttrib = m_player->getPlayerAttrib();
	if (!playerAttrib)
		return;
		
	LivingAttrib* livingAttrib = playerAttrib;
	if (!livingAttrib)
		return;
	
	// 移除buff
	if (livingAttrib->hasBuff(buffId))
	{
		livingAttrib->removeBuff(buffId);
		if (m_currentTerritoryBuffId == buffId)
		{
			m_currentTerritoryBuffId = 0;
		}
	}
}

void TerritoryBuffComponent::ClearAllTerritoryBuffs()
{
	// 移除所有可能的领地buff
	RemoveTerritoryBuff(AUTHORIZED_TERRITORY_BUFF_ID);
	RemoveTerritoryBuff(TERRITORY_EROSION_BUFF_ID);
	RemoveTerritoryBuff(UNAUTHORIZED_TERRITORY_BUFF_ID);
	m_currentTerritoryBuffId = 0;
}

bool TerritoryBuffComponent::HasSufficientMaintenanceMaterials(TerritoryContainer* territoryContainer)
{
	if (!territoryContainer) return false;
	
	// 检查是否有任何管理的腐蚀方块
	const auto& managedBlocks = territoryContainer->GetManagedErosionBlocks();
	if (managedBlocks.empty()) return true; // 没有需要维护的方块，认为材料充足
	
	// 检查每个腐蚀方块是否至少可以维护一次
	for (const auto* erosionBlock : managedBlocks)
	{
		if (!erosionBlock) continue;
		
		// 获取该方块所需的维护材料数量
		int availableMaintenanceCount = territoryContainer->GetAvailableMaterialCountByBlockId(erosionBlock->getBlockID());
		
		// 如果任何一个方块无法维护，返回false
		if (availableMaintenanceCount <= 0)
		{
			return false;
		}
	}
	
	return true; // 所有方块都可以至少维护一次
}


