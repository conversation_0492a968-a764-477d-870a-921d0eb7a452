
#ifndef __BLOCKONEQUARTER_H__
#define __BLOCKONEQUARTER_H__

#include "BlockBasic.h"

//#define MAX_WEATHER_NUM 4 //天气预报组合数

class BlockOneQuarter : public ModelBlockMaterial //tolua_exports
{ //tolua_exports
	DECLARE_BLOCKMATERIAL(BlockOneQuarter)
public:

	virtual void init(int resid) override;

	//tolua_begin
	BlockOneQuarter();
	~BlockOneQuarter();
	virtual bool canPlacedAgain(World *pworld, int placeblockid, const Rainbow::Vector3f &colpoint, const WCoord &blockpos, bool placeinto, int face);
	virtual void onBlockPlacedBy(World *pworld, const WCoord &blockpos, IClientPlayer *player, const Rainbow::Vector3f &colpoint, bool placeinto, int face);
    virtual void onBlockAdded(World *pworld, const WCoord &blockpos)override;
	virtual void onBlockRemoved(World *pworld, const WCoord &blockpos, int blockid, int blockdata) override;
	virtual void createCollideData(CollisionDetect *coldetect, World *pworld, const WCoord &blockpos);
	virtual bool canPutOntoPos(WorldProxy *pworld, const WCoord &blockpos) 
	{
		return true;
	}
	//virtual bool hasContainer()
	//{
	//	return true;
	//}
	virtual int getBlockGeomID(int *idbuf, int *dirbuf, const SectionDataHandler* sectionData, const WCoord &blockpos, World* world);

	virtual void dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata = 0, BLOCK_MINE_TYPE droptype = BLOCK_MINE_NOTOOL, float chance = 1.0f, int uin = -1);

	virtual void createBlockMeshWithContainer(const BuildSectionMeshData& data, const WCoord &blockpos, SectionMesh *poutmesh, WorldContainer *pContainer) override;
	//tolua_end
private:
	int getBlockGeomIDWithContainer(int *idbuf, int *dirbuf, const SectionDataHandler* sectionData, const WCoord &blockpos, World* world, WorldContainer *pContainer);
}; //tolua_exports

#endif