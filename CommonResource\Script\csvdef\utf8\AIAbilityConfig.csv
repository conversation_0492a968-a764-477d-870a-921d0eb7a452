ID[整型],名称[string],界面大类[整型](      1.AI事件 2.AI装饰器 3.AI组合 4.AI条件 5.AI动作 6.AI取值 7.AI变量),"界面子类[整型]【1通用，2游戏，3玩家，4生物，5方块，6道具，7变量，8控制，9自定义，10数学，11组，12计时器，13界面，14特效，15音频，16图文信息，17开发者，18赋值，19数据存储，21区域，22位置，23数值，24字符串，25布尔值，26生物类型，27排行榜，28表,29世界,30对象,101搜索对象 , 102移动, 103攻击 ， 150 内部节点】",形状类型[数值](      1.AI事件 2.AI装饰器 3.AI组合 4.AI条件 5.AI动作 6.AI取值 7.AI变量 8.全局变量  9.AI根节点),简略显示[数值]【在库里是否缩略显示，1=是，0=否】,描述[string],图标[string],使用场景过滤[string],·,左右值标记[整型],返回值[整型],参数1类型[string],参数1默认值[string],参数2类型,参数2默认值,参数3类型,参数3默认值,参数4类型,参数4默认值,参数5类型,参数5默认值,参数6类型,参数6默认值,参数7类型,参数7默认值,参数8类型,参数8默认值,显示控制（0=默认全部可见 1=仅开发者可见 2=仅999渠道可见 3=4位数迷你号可见  4 = 仅国内可见  5 = 仅海外可见 6 = 仅国内开发者可见 7 = 仅海外开发者可见 8 = 不显示）,积木间距值（间距值默认为60，超过60会开始变高）,说明,"生成规则[ 0 =lua ,1=c++]",,"内部节点[0,外部开放节点 ；  1,内部使用节点]",[],,,,,,,,
ID,Name,Type,Sort,BlockType,SimpleDisplay,Desc,Icon,SceneFilter,TranParam,LeftRightSign,ReturnValue,ParamType1,ParamDefault1,ParamType2,ParamDefault2,ParamType3,ParamDefault3,ParamType4,ParamDefault4,ParamType5,ParamDefault5,ParamType6,ParamDefault6,ParamType7,ParamDefault7,ParamType8,ParamDefault8,Display,HeightPx,Notes,GenerateType,真排序,,,,,,,,,,
100001,当 受到攻击,1,2,1,1,"当 受到攻击时,将攻击者存储在%1",20,"1,1,0,1,1,0",,,47,1048|0|0,,,,,,,,,,,,,,,,,,9401001,,,,,,,,,,,,
100002,当 受到道具交互,1,2,1,1,"当 受到其他对象持有的%1交互时,将对象存储在%2",2,"1,1,0,1,1,0",,,,1028|1|0,2|200,1048|0|0,,,,,,,,,,,,,,,,9401002,,,,,,,,,,,,
100003,当 接收广播,1,2,1,1,当 收到广播%1,5,"1,99,0,1,1,0",,,,1090|1|0,,,,,,,,,,,,,,,,,,9401003,1,,,,,,,,,,,
100004,当 听到周边音频,1,2,1,1,"当 听到以%1为中心周围%2格内%3时,将音频位置存储在%4",17,"1,1,0,1,1,0",,,,1048|1|0,3|600101,1005|1|0,1|10,1046|1|0,,1002|0|0,,,,,,,,,,,,9401004,,,,,,,,,,,,
100005,当 被玩家点击,1,2,1,1,"当 受到玩家点击时,将玩家存储在%1",20,"1,1,0,1,1,0",,,,1018|1|0,,,,,,,,,,,,,,,,,,9401058,,,,,,,,,,,,
100006,当 友方被攻击,1,2,1,1,"当 周边%1格内友方被攻击时,将攻击者存储在%2",20,"1,1,0,1,1,0",,,47,1005|1|0,1|10,1048|0|0,,,,,,,,,,,,,,,,9401059,,,,,,,,,,,,
200001,取反,2,1,2,1,以下节点返回值取反,,"1,1,0,1,1,0",,,,,,,,,,,,,,,,,,,,,,9401005,1,,,,,,,,,,,
200002,仅执行一次,2,1,2,1,以下节点仅执行一次,,"1,1,0,1,1,0",,,,,,,,,,,,,,,,,,,,,,9401006,1,,,,,,,,,,,
200003,循环,2,1,2,1,循环执行%1次,,"1,1,0,1,1,0",,,,1005|1|0,1|2,,,,,,,,,,,,,,,,,9401007,1,,,,,,,,,,,
200004,总是返回成功,2,1,2,1,返回成功,,"1,1,0,1,1,0",,,,,,,,,,,,,,,,,,,,,,9401008,1,,,,,,,,,,,
200005,总是返回失败,2,1,2,1,返回失败,,"1,1,0,1,1,0",,,,,,,,,,,,,,,,,,,,,,9401009,1,,,,,,,,,,,
200006,每若干秒执行一次,2,1,2,2,每%1秒执行一次,,"1,1,0,1,1,0",,,,1005|1|0,1|1,,,,,,,,,,,,,,,,,9401060,1,,,,,,,,,,,
300001,顺序执行,3,1,3,1,顺序执行%1,,"1,1,0,1,1,0",,,,1203|1|0,5|2030001,,,,,,,,,,,,,,,,,9401010,1,,,,,,,,,,,
300002,选择执行,3,1,3,1,选择执行%1,,"1,1,0,1,1,0",,,,1203|1|0,5|2030001,,,,,,,,,,,,,,,,,9401011,1,,,,,,,,,,,
300003,同时执行,3,1,3,1,同时执行%1%2,,"1,1,0,1,1,0",,,,1204|1|0,5|2040002,1205|1|0,5|2050002,,,,,,,,,,,,,,,9401012,1,,,,,,,,,,,
400201,游戏时间,4,2,4,1,%1%2%3,12,"1,1,0,1,1,0",,,,1005|1|0,3|600205,1016|1|2,5|120001,1005|1|0,1|10,,,,,,,,,,,,,9401013,,,,,,,,,,,,
400203,位置处于区域判断,4,2,4,1,%1%2处于%3中,12,"1,1,0,1,1,0",,,,1002|1|0,,1012|1|2,5|90001,1001|1|0,,,,,,,,,,,,,,9401014,,,,,,,,,,,,
400204,位置与位置之间的距离,4,2,4,1,%1%2%3,12,"1,1,0,1,1,0",,,,1005|1|0,3|600104,1016|1|2,5|120001,1005|1|0,1|1,,,,,,,,,,,,,9401015,,,,,,,,,,,,
400205,对象属性判断,0,0,0,1,%1%2%3,12,"1,1,0,1,1,0",,,,1005|1|0,3|600201,1016|1|2,5|120001,1005|1|0,1|10,,,,,,,,,,,,,,,,,,,,,,,,,
400206,对象间队伍关系判断,4,2,4,1,%1与%2的队伍关系为%3,12,"1,1,0,1,1,0",,,,1048|1|0,,1048|1|0,,1206|1|2,5|2060002,,,,,,,,,,,,,9401017,,,,,,,,,,,,
400207,对象是否存活,4,2,4,1,%1存活,12,"1,1,0,1,1,0",,,,1048|1|0,,,,,,,,,,,,,,,,,,9401016,,,,,,,,,,,,
400208,对象类型判断,4,2,4,1,%1的类型%2%3,12,"1,1,0,1,1,0",,,,1048|1|0,3|600101,1016|1|2,5|120001,1106|1|0,,,,,,,,,,,,,,9401018,,,,,,,,,,,,
400209,对象手持道具类型判断,4,6,4,1,%1手持道具类型%2%3,1,"1,1,0,1,1,0",,,,1048|1|0,3|600101,1016|1|2,5|120001,1028|1|0,2|200,,,,,,,,,,,,,,,,,,,,,,,,,
400210,内部-是否为虚空之夜,4,2,4,1,当前是特殊夜晚,12,"1,1,0,1,1,0",,,,,,,,,,,,,,,,,,,,2,,,,,,,,,,,,,,
400211,对象状态效果判断,4,2,4,1,%1%2处于%3中,12,"1,1,0,1,1,0",,,,1048|1|0,3|600101,1012|1|2,5|90001,1037|1|0,2|4001,,,,,,,,,,,,,9401061,,,,,,,,,,,,
400212,对象队伍判断,4,2,4,1,%1队伍为%2,12,"1,1,0,1,1,0",,,,1048|1|0,3|600101,1207|1|0,,,,,,,,,,,,,,,,9401062,,,,,,,,,,,,
400213,对象与对象之间的距离,4,2,4,1,%1%2%3,12,"1,1,0,1,1,0",,,,1005|1|0,3|600113,1016|1|2,5|120001,1005|1|0,1|1,,,,,,,,,,,,,9401015,,,,,,,,,,,,
400311,玩家属性判断,4,3,4,1,%1%2%3,8,"1,1,0,1,1,0",,,,1005|1|0,3|600311,1016|1|2,5|120001,1005|1|0,1|10,,,,,,,,,,,,,9401019,,,,,,,,,,,,
400312,周边是否有玩家存在,4,3,4,1,%1存在,8,"1,1,0,1,1,0",,,,1018|1|0,3|600312,,,,,,,,,,,,,,,,,9401020,,,,,,,,,,,,
400411,生物属性判断,4,4,4,1,%1%2%3,5,"1,1,0,1,1,0",,,,1005|1|0,3|600411,1016|1|2,5|120001,1005|1|0,1|10,,,,,,,,,,,,,9401021,,,,,,,,,,,,
400412,周边是否有生物存在,4,4,4,1,%1存在,5,"1,1,0,1,1,0",,,,1031|1|0,3|600415,,,,,,,,,,,,,,,,,9401020,,,,,,,,,,,,
400421,生物是否处于持盾状态,4,4,4,1,%1处于持盾状态,5,"1,1,0,1,1,0",,,,1031|1|0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
400422,生物是否处于抱起状态,4,4,4,1,%1处于抱起状态,5,"1,1,0,1,1,0",,,,1031|1|0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
400501,周边是否存在特定方块,4,5,4,1,%1上有方块存在,2,"1,1,0,1,1,0",,,,1002|1|0,,,,,,,,,,,,,,,,,,9401022,,,,,,,,,,,,
400502,位置处的方块类型判断,4,5,4,1,%1的方块类型为%2,2,"1,1,0,1,1,0",,,,1002|1|0,,1025|1|0,2|200,,,,,,,,,,,,,,,9401023,,,,,,,,,,,,
400580,内部-周边若干个是否有建筑,4,5,4,1,周边%1格内有建筑,2,"1,1,0,1,1,0",,,,1005|1|0,1|20,,,,,,,,,,,,,,,2,,,,,,,,,,,,,,
400581,内部-僵尸条件打断优化,4,5,4,1,%1%2%3,2,"1,1,0,1,1,0",,,,1048|1|0,3|600101,1048|1|0,3|600101,1005|1|0,,,,,,,,,,,,2,,,1,,,,,,,,,,,
400601,道具属性判断,0,6,0,1,%1%2%3,1,"1,1,0,1,1,0",,,,1005|1|0,3|600601,1016|1|2,5|120001,1005|1|0,,,,,,,,,,,,,,9401024,,,,,,,,,,,,
400611,道具属性判断,0,6,0,1,%1%2%3,1,"1,1,0,1,1,0",,,,1005|1|0,3|600611,1016|1|2,5|120001,1005|1|0,,,,,,,,,,,,,,9401024,,,,,,,,,,,,
401001,数值比较,4,10,4,1,%1%2%3,6,"1,1,0,1,1,0",,,,1005|1|0,1|0,1016|1|2,5|120001,1005|1|0,1|10,,,,,,,,,,,,,,,,,,,,,,,,,
401002,布尔值比较,4,10,4,1,%1%2%3,6,"1,1,0,1,1,0",,,,1014|1|0,5|320001,1012|1|2,5|90001,1014|1|0,5|320001,,,,,,,,,,,,,,,,,,,,,,,,,
401003,字符串比较,4,10,4,1,%1%2%3,6,"1,1,0,1,1,0",,,,1010|1|0,1|300198,1012|1|2,5|90001,1010|1|0,1|300198,,,,,,,,,,,,,,,,,,,,,,,,,
401004,位置比较,4,10,4,1,%1%2%3,6,"1,1,0,1,1,0",,,,1002|1|0,,1016|1|2,5|120001,1002|1|0,,,,,,,,,,,,,,,,,,,,,,,,,,
401005,对象比较,4,10,4,1,%1%2%3,6,"1,1,0,1,1,0",,,,1048|1|0,,1012|1|2,5|90001,1048|1|0,,,,,,,,,,,,,,,,,,,,,,,,,,
401010,随机数,4,10,4,1,%1%2%3,6,"1,1,0,1,1,0",,,,1005|1|0,3|601001,1016|1|2,5|120001,1005|1|0,1|10,,,,,,,,,,,,,,,,,,,,,,,,,
401201,计时器时间判断,4,12,4,1,%1%2%3,6,"1,1,0,1,1,0",,1,,1005|1|0,3|601201,1016|1|2,5|120001,1005|1|0,,,,,,,,,,,,,,,,,,,,,,,,,,
401601,存在特定编号的图文信息,4,16,4,1,%1身上存在编号为%2的%3,3,"1,1,0,1,1,0",,1,,1048|1|0,,1005|1|0,1|1,1058|1|2,5|490001,,,,,,,,,,,,,,,,,,,,,,,,,
500101,设置数值,5,1,5,1,设置%1为%2,6,"1,1,0,1,1,0",,,,1005|0|0,,1005|1|0,1|0,,,,,,,,,,,,,,,,,,,,,,,,,,,
500102,设置字符串,5,1,5,1,设置%1为%2,6,"1,1,0,1,1,0",,,,1010|0|0,,1010|1|0,1|300198,,,,,,,,,,,,,,,,,,,,,,,,,,,
500103,设置布尔值,5,1,5,1,设置%1为%2,6,"1,1,0,1,1,0",,,,1014|0|0,,1014|1|0,5|320002,,,,,,,,,,,,,,,,,,,,,,,,,,,
500104,设置对象,5,1,5,1,设置%1为%2,6,"1,1,0,1,1,0",,,,1048|0|0,,1048|1|0,,,,,,,,,,,,,,,,,,,,,,,,,,,,
500105,设置位置,5,1,5,1,设置%1为%2,6,"1,1,0,1,1,0",,,,1002|0|0,,1002|1|0,,,,,,,,,,,,,,,,,,,,,,,,,,,,
500106,设置区域,5,1,5,1,设置%1为%2,6,"1,1,0,1,1,0",,,,1001|0|0,,1001|1|0,,,,,,,,,,,,,,,,,,,,,,,,,,,,
500107,设置玩家,5,1,5,1,设置%1为%2,6,"1,1,0,1,1,0",,,,1018|0|0,,1018|1|0,,,,,,,,,,,,,,,,,,,,,,,,,,,,
500108,设置生物,5,1,5,1,设置%1为%2,6,"1,1,0,1,1,0",,,,1031|0|0,,1031|1|0,,,,,,,,,,,,,,,,,,,,,,,,,,,,
500109,设置属性（字符串）,5,1,5,1,（字符串）设置%1的%2的%3为%4,,"1,1,0,1,1,0",,,,1048|1|0,,1010|1|0,1|300198,1010|1|0,1|300198,1005|1|0,1|5,,,,,,,,,2,,,,,,,,,,,,,,
500110,设置方块类型,5,1,5,1,设置%1为%2,6,"1,1,0,1,1,0",,,,1025|0|0,,1025|1|0,2|200,,,,,,,,,,,,,,,,,,,,,,,,,,,
500111,设置道具类型,5,1,5,1,设置%1为%2,6,"1,1,0,1,1,0",,,,1028|0|0,,1028|1|0,2|11002,,,,,,,,,,,,,,,,,,,,,,,,,,,
500112,设置生物类型,5,1,5,1,设置%1为%2,6,"1,1,0,1,1,0",,,,1032|0|0,,1032|1|0,2|3400,,,,,,,,,,,,,,,,,,,,,,,,,,,
500113,设置特效类型,5,1,5,1,设置%1为%2,6,"1,1,0,1,1,0",,,,1045|0|0,,1045|1|0,2|1024,,,,,,,,,,,,,,,,,,,,,,,,,,,
500150,切换行为树,5,1,5,1,切换%1,6,"1,1,0,1,1,0",,,,1210|0|0,,,,,,,,,,,,,,,,2,,,,,,,,,,,,,,
500151,引用行为树,5,1,5,1,引用%1,6,"1,1,0,1,1,0",,,,1210|0|0,,,,,,,,,,,,,,,,2,,,1,,,,,,,,,,,
500201,改变对象属性,0,0,0,1,使%1的%2%3%4,8,"1,1,0,1,1,0",,,,1048|1|0,,1020|1|2,5|140001,1120|1|2,5|840001,1005|1|0,1|0,,,,,,,,,2,,,,,,,,,,,,,,
500202,等待时间,5,2,5,1,等待%1秒后，执行下一个节点,12,"1,1,0,1,1,0",,,,1005|1|0,1|1,,,,,,,,,,,,,,,,,9401027,1,,,,,,,,,,,
500203,发送广播,5,2,5,1,发送广播%1,12,"1,1,0,1,1,0",,,,1090|1|0,,,,,,,,,,,,,,,,,,9401026,,,,,,,,,,,,
500204,设置对象朝向,5,2,5,1,使%1朝向%2缓动时间%3秒,12,"1,1,0,1,1,0",,,,1048|1|0,3|600101,1002|1|0,,1005|1|0,1|1,,,,,,,,,,,,,9401025,,,,,,,,,,,,
500205,设置对象队伍,5,2,5,1,设置%1的队伍为%2,12,"1,1,0,1,1,0",,,,1048|1|0,,1207|1|0,,,,,,,,,,,,,,,,9401028,,,,,,,,,,,,
500206,改变外观,0,2,0,1,改变%1的外观为%2,12,"1,1,0,1,1,0",,,,1048|1|0,3|600101,1060|1|0,2|163,,,,,,,,,,,,,,,9401029,,,,,,,,,,,,
500207,替换生物,0,2,0,1,"替换%1为%2,%3保留血量百分比",12,"1,1,0,1,1,0",,,,1048|1|0,,1032|1|2,2|3400,1012|1|2,5|90001,,,,,,,,,,,,,9401030,,,,,,,,,,,,
500208,骑乘生物,0,2,0,1,骑乘%1,12,"1,1,0,1,1,0",,,,1048|1|0,,,,,,,,,,,,,,,,,,9401031,,,,,,,,,,,,
500209,播放动画,5,2,5,1,"使此生物播放ID为%1的动画,持续%2秒,模式%3",12,"1,1,0,1,1,0",,,,1005|1|0,,1005|1|0,2|0,1102|0|0,700001,,,,,,,,,,,2,,9401032,,,,,,,,,,,,
500210,搜寻时受到视线阻挡,5,2,5,1,%1搜寻目标时，%2受到视线阻挡,12,"1,1,0,1,1,0",,,,1048|1|0,,1012|1|2,5|90001,,,,,,,,,,,,,8,,9401032,,,,,,,,,,,,
500211,停止动画,5,2,5,1,使此生物停止播放ID为%1的动画,12,"1,1,0,1,1,0",,,,1005|1|0,,,,,,,,,,,,,,,,2,,9401032,,,,,,,,,,,,
500301,改变玩家属性,0,3,0,1,使玩家%1的%2%3%4,8,"1,1,0,1,1,0",,,,1048|1|0,,1020|1|2,5|140001,1120|1|2,5|840001,1005|1|0,1|1,,,,,,,,,,,9401033,,,,,,,,,,,,
500311,改变玩家属性,5,3,5,1,使%1的%2%3%4,8,"1,1,0,1,1,0",,,,1018|1|0,,1020|1|2,5|140001,1120|1|2,5|840001,1005|1|0,1|1,,,,,,,,,,,9401033,,,,,,,,,,,,
500312,改变组内玩家的属性,5,3,5,1,使%1的%2%3%4,8,"1,1,0,1,1,0",,,,1019|1|0,,1020|1|2,5|140001,1120|1|2,5|840001,1005|1|0,1|1,,,,,,,,,,,9401033,,,,,,,,,,,,
500401,改变生物属性,0,4,0,1,使生物%1的%2%3%4,5,"1,1,0,1,1,0",,,,1048|1|0,,1035|1|2,5|140001,1120|1|2,5|840001,1005|1|0,1|1,,,,,,,,,,,9401034,,,,,,,,,,,,
500410,设置生物位置,5,4,5,1,传送%1至%2,5,"1,1,0,1,1,0",,,,1031|1|0,3|600400,1002|1|0,,,,,,,,,,,,,,,,,,,,,,,,,,,,
500411,改变生物属性,5,4,5,1,使%1的%2%3%4,5,"1,1,0,1,1,0",,,,1031|1|0,3|600400,1035|1|2,5|140001,1120|1|2,5|840001,1005|1|0,1|1,,,,,,,,,,,9401034,,,,,,,,,,,,
500412,改变生物模型,5,4,5,1,改变%1的模型外观为%2,5,"1,1,0,1,1,0",,,,1031|1|0,3|600400,1060|1|0,2|163,,,,,,,,,,,,,,,9401029,,,,,,,,,,,,
500413,替换生物,5,4,5,1,"替换%1为%2,%3保留血量百分比",5,"1,1,0,1,1,0",,,,1031|1|0,,1032|1|0,2|3400,1012|1|2,5|90001,,,,,,,,,,,,,9401030,,,,,,,,,,,,
500414,骑乘生物,5,4,5,1,使此生物骑乘%1,5,"1,1,0,1,1,0",,,,1031|1|0,,,,,,,,,,,,,,,,,,9401031,,,,,,,,,,,,
500415,创建生物,5,4,5,1,在%1创建%2生物,5,"1,1,0,1,1,0",,,,1002|1|0,,1032|1|0,2|3400,,,,,,,,,,,,,,,,,,,,,,,,,,,
500416,击败生物,5,4,5,1,击败%1,5,"1,1,0,1,1,0",,,,1031|1|0,,,,,,,,,,,,,,,,,,9401055,,,,,,,,,,,,
500417,删除生物,5,4,5,1,删除%1,5,"1,1,0,1,1,0",,,,1031|1|0,,,,,,,,,,,,,,,,,,9401056,,,,,,,,,,,,
500418,抱起生物,5,4,5,1,使此生物抱起%1,5,"1,1,0,1,1,0",,,,1031|1|0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
500419,放下抱起的生物,5,4,5,1,使此生物放下正在抱着的生物,5,"1,1,0,1,1,0",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
500420,设置生物死亡掉落,5,4,5,1,"设置此生物死亡时,%1%的概率,掉落%2个%3",5,"1,1,0,1,1,0",,,,1005|1|0,100,1005|1|0,100,1028|1|0,2|200,,,,,,,,,,,,,,,,,,,,,,,,,
500421,使生物播放动画表情,5,4,5,1,使%1播放%2,5,"1,1,0,1,1,0",,,,1031|1|0,3|600400,1041|1|0,2|1,,,,,,,,,,,,,,,,,,,,,,,,,,,
500422,改变组内生物的属性,5,4,5,1,使%1的%2%3%4,5,"1,1,0,1,1,0",,,,1033|1|0,,1020|1|2,5|140001,1120|1|2,5|840001,1005|1|0,1|1,,,,,,,,,,,9401033,,,,,,,,,,,,
500430,设置移动方式,5,4,5,1,设置%1移动方式为%2,5,"1,1,0,1,1,0",,,,1031|1|0,3|600400,1211|1|0,5|2110001,,,,,,,,,,,,,,,,,,,,,,,,,,,
500431,设置血条显示与隐藏,5,4,5,1,设置%1的血条显示为%2,5,"1,1,0,1,1,0",,,,1031|1|0,3|600400,1013|1|2,5|100002,,,,,,,,,,,,,,,,,,,,,,,,,,,
500432,设置搜寻时受到视线阻挡,5,4,5,1,%1搜寻目标时，%2受到视线阻挡,5,"1,1,0,1,1,0",,,,1031|1|0,3|600400,1012|1|2,5|90001,,,,,,,,,,,,,8,,9401032,,,,,,,,,,,,
500433,设置生物飞行高度,5,4,5,1,"当%1可飞行时,飞行高度为%2",5,"1,1,0,1,1,0",,,,1031|1|0,3|600400,1005|1|0,1|10,,,,,,,,,,,,,,,,,,,,,,,,,,,
500434,设置允许生物开门,5,4,5,1,设置允许%1开门,5,"1,1,0,1,1,0",,,,1031|1|0,3|600400,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
500440,使生物向前跳跃,5,4,5,1,使%1向前跳跃,5,"1,1,0,1,1,0",,,,1031|1|0,3|600400,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
500441,踢开生物,5,4,5,1,"距离%1%2格子时,以%3的力量踢开",5,"1,1,0,1,1,0",,,,1031|1|0,,1005|1|0,,1005|1|0,,,,,,,,,,,,,,,,,,,,,,,,,,
500442,睡觉,5,4,5,1,使%1睡%2秒,5,"1,1,0,1,1,0",,,,1031|1|0,3|600400,1005|1|0,,,,,,,,,,,,,,,,,,,,,,,,,,,,
500501,挖方块,5,5,5,1,"靠近%1并挖掘,%2方块掉落",2,"1,1,0,1,1,0",,,,1002|1|0,,1013|1|2,5|100002,,,,,,,,,,,,,,,,1,,,,,,,,,,,
500502,与方块交互,0,5,0,1,与%1的方块进行交互,,"1,1,0,1,1,0",,,,1002|1|0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
500503,破坏方块,5,5,5,1,"破坏%1位置的方块,%2方块掉落",2,"1,1,0,1,1,0",,,,1002|1|0,,1013|1|2,5|100002,,,,,,,,,,,,,,,9401035,,,,,,,,,,,,
500504,放置方块,5,5,5,1,在%1放置%2，朝向为%3,2,"1,1,0,1,1,0",,,,1002|1|0,,1025|1|0,2|200,1003|1|2,5|20001,,,,,,,,,,,,,9401036,,,,,,,,,,,,
500505,替换方块,5,5,5,1,替换在%1上的方块为%2，朝向%3,2,"1,1,0,1,1,0",,,,1002|1|0,,1025|1|0,2|200,1003|1|2,5|20001,,,,,,,,,,,,,9401037,,,,,,,,,,,,
500506,破坏区域内的方块,5,5,5,1,"破坏%1内的方块,间隔%2秒,%3方块掉落",2,"1,1,0,1,1,0",,,,1001|1|0,,1005|1|0,1|0,1013|1|2,5|100002,,,,,,,,,,,0,,9401037,,,,,,,,,,,,
500507,挖掘区域内的方块,5,5,5,1,"挖掘%1内的方块,%2方块掉落,速度%3倍",2,"1,1,0,1,1,0",,,,1001|1|0,,1013|1|2,5|100002,1005|1|0,1|1,,,,,,,,,,,,,,1,,,,,,,,,,,
500508,内部-搜寻并攻击建筑,5,5,5,1,"搜寻%1格内的建筑并靠近破坏,攻击间隔%2,伤害值%3,攻击距离%4",2,"1,1,0,1,1,0",,,,1005|1|0,,1005|1|0,1|1,1005|1|0,1|10,1005|1|0,1|2,,,,,,,,,2,,,,,,,,,,,,,,
500602,存储物品,5,6,5,1,将%1个%2存储到%3,1,"1,1,0,1,1,0",,,,1005|1|0,1|1,1028|1|0,2|200,1002|1|0,,,,,,,,,,,,,,9401038,,,,,,,,,,,,
500603,拿出背包道具并放在手上,5,6,5,1,把背包内%1放在手上,1,"1,1,0,1,1,0",,,,1028|1|0,2|200,,,,,,,,,,,,,,,,,9401039,,,,,,,,,,,,
500604,生成道具并直接放入背包,5,6,5,1,生成%1并直接放入背包,1,"1,1,0,1,1,0",,,,1028|1|0,2|200,,,,,,,,,,,,,,,,,9401040,,,,,,,,,,,,
500605,拾取周边道具并放入背包,5,6,5,1,捡起周边%1格内的%2放入背包,1,"1,1,0,1,1,0",,,,1005|1|0,1|10,1028|1|0,2|200,,,,,,,,,,,,,,,9401041,,,,,,,,,,,,
500606,生成道具并直接放在手上,5,6,5,1,生成%1并直接放在手上,1,"1,1,0,1,1,1",,,,1028|1|0,2|200,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
500607,使用道具,5,6,5,1,"使用%1,%2需要有此物品",1,"1,1,0,1,1,1",,,,1028|1|0,2|200,1012|1|2,5|90002,,,,,,,,,,,,,,,,,,,,,,,,,,,
500608,内部-幸存者拾道具,5,6,5,1,自动拾周边%1格道具,1,"1,1,0,1,1,0",,,,1005|1|0,,,,,,,,,,,,,,,,2,,,,,,,,,,,,,,
501201,运行计时器,5,12,5,1,启动%1,12,"1,1,0,1,1,0",,,,1040|1|0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
501202,暂停计时器,5,12,5,1,暂停%1,12,"1,1,0,1,1,0",,,,1040|1|0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
501203,恢复计时器,5,12,5,1,恢复%1,12,"1,1,0,1,1,0",,,,1040|1|0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
501204,重置计时器,5,12,5,1,重置%1,12,"1,1,0,1,1,0",,,,1040|1|0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
501401,在指定位置播放特效,5,14,5,1,"在%1播放%2,大小%3",7,"1,1,0,1,1,0",,,,1002|1|0,,1045|1|0,,1005|1|0,1|1,,,,,,,,,,,,,,,,,,,,,,,,,
501402,在对象身上播放特效,5,14,5,1,"在%1身上播放%2,大小%3",7,"1,1,0,1,1,0",,,,1048|1|0,,1045|1|0,,1005|1|0,1|1,,,,,,,,,,,,,,,,,,,,,,,,,
501403,删除指定位置的特效,5,14,5,1,删除%1的%2特效,7,"1,1,0,1,1,0",,,,1002|1|0,,1045|1|0,,,,,,,,,,,,,,,,,,,,,,,,,,,,
501404,删除对象身上的特效,5,14,5,1,删除%1身上的%2特效,7,"1,1,0,1,1,0",,,,1048|1|0,,1045|1|0,,,,,,,,,,,,,,,,,,,,,,,,,,,,
501501,在位置处播放音频,5,15,5,1,"在%1播放%2,音量%3,循环%4",11,"1,1,0,1,1,0",,,,1002|1|0,,1046|1|0,,1005|1|0,1|100,1012|1|2,5|90002,,,,,,,,,,,,,,,,,,,,,,,
501502,使对象播放音频,5,15,5,1,"使%1播放%2,音量%3,循环%4",11,"1,1,0,1,1,0",,,,1048|1|0,,1046|1|0,,1005|1|0,1|100,1012|1|2,5|90002,,,,,,,,,,,,,,,,,,,,,,,
501511,关闭位置处音频,5,15,5,1,关闭%1正在播放的%2,11,"1,1,0,1,1,0",,,,1002|1|0,,1046|1|0,,,,,,,,,,,,,,,,,,,,,,,,,,,,
501512,关闭对象身上音频,5,15,5,1,关闭%1正在播放的%2,11,"1,1,0,1,1,0",,,,1048|1|0,,1046|1|0,,,,,,,,,,,,,,,,,,,,,,,,,,,,
501601,创建平面图文信息,5,16,5,1,在%1身上创建%2，向X轴偏移%3，Y轴偏移%4,3,"1,1,0,1,1,0",,,,1048|1|0,,1057|1|0,,1005|1|0,1|0,1005|1|0,1|100,,,,,,,,,,,,,,,,,,,,,,,
501602,删除平面图文信息,5,16,5,1,删除%1身上编号为%2的%3,3,"1,1,0,1,1,0",,,,1048|1|0,,1005|1|0,1|1,1058|1|2,5|490001,,,,,,,,,,,,,,,,,,,,,,,,,
501610,使玩家打开界面,5,16,5,1,使%1打开%2,3,"1,0,0,1,1,0",,,,1018|1|0,,1074|1|0,,,,,,,,,,,,,,,100,9401042,,,,,,,,,,,,
510201,移动到指定位置,5,102,5,1,"移动到%1,速度%2倍,%3允许改变朝向",15,"1,1,0,1,1,0",,,,1002|1|0,,1005|1|0,1|1,1012|1|2,5|90001,,,,,,,,,,,,,9401042,,,,,,,,,,,,
510202,随机位置移动,5,102,5,1,"以%1为中心在%2格内随机移动,速度%3倍,%4允许改变朝向。",15,"1,1,0,1,1,0",,,,1002|1|0,,1005|1|0,1|10,1005|1|0,1|2,1012|1|2,5|90001,,,,,,,,,,,9401043,,,,,,,,,,,,
510203,跟随对象移动,5,102,5,1,"跟随%1移动,并保持%2格距离,速度%3倍,%4允许改变朝向",15,"1,1,0,1,1,0",,,,1048|1|0,,1005|1|0,1|2,1005|1|0,1|2,1012|1|2,5|90001,,,,,,,,,,,9401044,,,,,,,,,,,,
510204,指定路线移动,5,102,5,1,"按照%1移动,速度%2倍,%3往返, 移动重启时%4从头开始",15,"1,1,0,1,1,0",,,,1062|1|0,,1005|1|0,1|2,1012|1|2,5|90001,1012|1|2,5|90001,,,,,,,,,,,9401045,,,,,,,,,,,,
510205,远离位置移动,5,102,5,1,"远离%1移动,速度%2倍,远离时间%3秒,%4允许改变朝向",15,"1,1,0,1,1,0",,,,1002|1|0,,1005|1|0,1|2,1005|1|0,1|2,1012|1|2,5|90001,,,,,,,,,,,9401046,,,,,,,,,,,,
510206,在区域中随机移动,5,102,5,1,"在%1内随机移动,速度%2倍,%3允许改变朝向",15,"1,1,0,1,1,0",,,,1001|1|0,,1005|1|0,1|2,1012|1|2,5|90001,1012|1|2,5|90001,,,,,,,,,,,9401047,,,,,,,,,,,,
510220,停止移动,5,102,5,1,使%1停止移动,15,"1,1,0,1,1,0",,,,1048|1|0,3|600101,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
510300,使用指定武器近战攻击,5,103,5,1,"使用%1近战攻击%2,攻击间隔%3秒,攻击距离%4格",16,"1,1,0,1,1,0",,,,1028|1|0,,1048|1|0,,1005|1|0,1|1,1005|1|0,1|2,,,,,,,,,,,9401050,,,,,,,,,,,,
510301,使用手持道具近战攻击,5,103,5,1,"近战攻击%1,攻击间隔%2秒,攻击距离%3格",16,"1,1,0,1,1,0",,,,1048|1|0,,1005|1|0,1|1,1005|1|0,1|2,,,,,,,,,,,,,9401049,,,,,,,,,,,,
510302,使用投掷物攻击,5,103,5,1,"远程攻击%1,攻击间隔%2秒,攻击距离%3格，投掷%4",16,"1,1,0,1,1,0",,,,1048|1|0,,1005|1|0,1|1,1005|1|0,1|2,1029|1|0,2|12052,,,,,,,,,,,9401050,,,,,,,,,,,,
510303,使用武器发射投掷物,5,103,5,1,"使用%1装填%2攻击%3,攻击间隔%4秒,攻击距离%5格",16,"1,1,0,1,1,0",,,,1028|1|0,,1029|1|0,2|12052,1048|1|0,,1005|1|0,1|2,1005|1|0,,,,,,,,,,9401051,,,,,,,,,,,,
510304,使用武器进行蓄力攻击,5,103,5,1,"使用%1装填%2蓄力攻击%3,蓄力时间%4秒,攻击间隔%5秒,攻击距离%6格",16,"1,1,0,1,1,0",,,,1028|1|0,,1029|1|0,2|12052,1048|1|0,,1005|1|0,1|2,1005|1|0,1|2,1005|1|0,1|2,,,,,,,,,,,,,,,,,,,
510305,内部-使用武器进行N次连续攻击,5,103,5,1,"使用%1进行%2段攻击,攻击%3,攻击距离%4格",16,"1,1,0,1,1,0",,,,1028|1|0,,1005|1|0,1|1,1048|1|0,,1005|1|0,1|1,,,,,,,,,2,,,,,,,,,,,,,,
510306,内部-使用武器进行第N段攻击 ,5,103,5,1,"使用%1进行第%2段攻击,攻击%3,攻击距离%4格,结束时%5重置动画",16,"1,1,0,1,1,0",,,,1028|1|0,,1005|1|0,1|1,1048|1|0,,1005|1|0,1|1,1012|1|2,5|90001,,,,,,,2,,,,,,,,,,,,,,
510307,内部 - 瞄准目标使用爆炸技能,5,103,5,1,瞄准%1使用爆炸技能,16,"1,1,0,1,1,0",,,,1048|1|0,,,,,,,,,,,,,,,,2,,,,,,,,,,,,,,
510308,举起盾牌,5,103,5,1,使此生物举起%1,16,"1,1,0,1,1,0",,,,1028|1|0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
510309,放下盾牌,5,103,5,1,使此生物放下盾牌,16,"1,1,0,1,1,0",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
510310,给予对象状态效果,5,103,5,1,"给予%1%2效果,永久持续%3,持续%4秒",16,"1,1,0,1,1,0",,,,1048|1|0,,1037|1|0,2|4001,1014|1|0,5|320002,1005|1|0,1|0,,,,,,,,,,,9401052,,,,,,,,,,,,
510311,移除对象状态效果,5,103,5,1,移除%1的%2效果,16,"1,1,0,1,1,0",,,,1048|1|0,,1037|1|0,2|4001,,,,,,,,,,,,,,,9401053,,,,,,,,,,,,
510312,替换对象状态效果,5,103,5,1,"将%1身上的%2替换为%3,永久持续%4,持续%5秒",16,"1,1,0,1,1,0",,,,1048|1|0,,1037|1|0,2|4001,1037|1|0,2|8001,1014|1|0,5|320002,1005|1|0,1|0,,,,,,,,,9401054,,,,,,,,,,,,
510313,使用枪械攻击,5,103,5,1,"使用%1攻击%2,攻击距离%3",16,"1,1,0,1,1,0",,,,1028|1|0,,1048|1|0,,1005|1|0,1|5,,,,,,,,,,,2,,,,,,,,,,,,,,
510320,击败对象,0,103,0,1,击败%1,16,"1,1,0,1,1,0",,,,1048|1|0,,,,,,,,,,,,,,,,,,9401055,,,,,,,,,,,,
510321,删除对象,0,103,0,1,删除%1,1,"1,1,0,1,1,0",,,,1048|1|0,,,,,,,,,,,,,,,,,,9401056,,,,,,,,,,,,
510330,抱起对象,0,103,0,1,使此生物抱起%1,1,"1,1,0,1,1,0",,,,1048|1|0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
510331,放下抱起的对象,0,103,0,1,使此生物放下抱起的对象,1,"1,1,0,1,1,0",,,,1048|1|0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
510340,使用指定目标类型技能,5,103,5,1,"对%1使用%2技能,%3自动转向",16,"1,1,0,1,1,0",,,,1048|1|0,,1010|1|0,,1013|1|2,5|100001,,,,,,,,,,,2,,,,,,,,,,,,,,
510341,使用指定位置类型技能,5,103,5,1,"对%1使用%2技能,%3自动转向",16,"1,1,0,1,1,0",,,,1002|1|0,,1010|1|0,,1013|1|2,5|100001,,,,,,,,,,,2,,,,,,,,,,,,,,
600101,此对象,6,1,6,1,此对象,5,"1,1,0,1,1,0",,1,47,,,,,,,,,,,,,,,,,,100,9401057,,,,,,,,,,,,
600102,队伍关系,6,1,6,1,%1关系,12,"1,1,0,1,1,0",,1,208,1206|1|2,5|2060001,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
600103,实际队伍,6,1,6,1,%1队伍,12,"1,1,0,1,1,0",,1,208,1207|1|2,5|2070001,,,,,,,,,,,,,,,,100,,,,,,,,,,,,,
600104,位置与位置的距离,6,1,6,1,%1与%2的距离,12,"1,1,0,1,1,0",,1,6,1002|1|0,,1002|1|0,,,,,,,,,,,,,,,,,,,,,,,,,,,,
600105,指定坐标位置,6,1,6,1,坐标值 X%1Y%2Z%3的位置,12,"1,1,0,1,1,0",,1,3,1005|1|0,1|0,1005|1|0,1|1,1005|1|0,1|1,,,,,,,,,,,,,,,,,,,,,,,,,
600106,位置的坐标值,6,1,6,0,%1的%2坐标值,12,"1,1,0,1,1,0",,1,6,1002|1|0,,1006|1|2,5|40001,,,,,,,,,,,,,,,,,,,,,,,,,,,
600107,内部-月亮坐标,6,1,6,1,月亮坐标,,"1,1,0,1,1,0",,1,3,,,,,,,,,,,,,,,,,2,,,,,,,,,,,,,,
600108,位置偏移（不改变原位置）,6,1,6,1,%1沿坐标轴偏移（%2，%3，%4）,,"1,1,0,1,1,0",,1,3,1002|1|0,,1005|1|0,1|1,1005|1|0,1|1,1005|1|0,1|1,,,,,,,,,,,,,,,,,,,,,,,
600109,对象生成位置,6,2,6,1,%1生成位置,12,"1,1,0,1,1,0",,1,3,1048|1|0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
600110,对象当前位置,6,2,6,1,%1当前位置,,"1,1,0,1,1,0",,1,3,1048|1|0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
600111,区域内空闲位置（像素坐标）——弃用,6,1,6,1,%1内空闲位置(像素坐标),,"1,1,0,1,1,0",,1,3,1001|1|0,,,,,,,,,,,,,,,,8,100,,,,,,,,,,,,,
600112,区域内空闲位置,6,1,6,1,%1内空闲位置,,"1,1,0,1,1,0",,1,3,1001|1|0,,,,,,,,,,,,,,,,,100,,,,,,,,,,,,,
600113,对象与对象的距离,6,1,6,1,%1与%2的距离,12,"1,1,0,1,1,0",,1,6,1048|1|0,,1048|1|0,,,,,,,,,,,,,,,,,1,,,,,,,,,,,
600130,位置转化为区域,6,1,6,1,以%1为中心点，尺寸为（%2，%3，%4）的区域,,"1,1,0,1,1,0",,1,2,1002|1|0,,1005|1|0,1|5,1005|1|0,1|5,1005|1|0,1|5,,,,,,,,,,,,,,,,,,,,,,,
600131,像素坐标转化方块坐标,6,1,6,1,%1(像素坐标)转化为%2(方块坐标）,,"1,1,0,1,1,0",,1,3,1002|1|0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
600132,方块坐标转化像素坐标,6,1,6,1,%1(方块坐标)转化为%2(像素坐标）,,"1,1,0,1,1,0",,1,3,1002|1|0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
600201,对象属性,0,0,0,1,%1的%2,,"1,1,0,1,1,0",,1,6,1048|1|0,,1020|1|2,,,,,,,,,,,,,,,,,,,,,,,,,,,,
600202,对象的队伍,6,2,6,1,%1的队伍,12,"1,1,0,1,1,0",,1,207,1048|1|0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
600203,对象的ID,6,2,6,1,%1的ID,12,"1,1,0,1,1,0",,1,11,1048|1|0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
600204,指定ID的对象,6,2,6,1,%1ID的对象,12,"1,1,0,1,1,0",,1,47,1010|1|0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
600205,当前游戏时间,6,2,6,1,当前游戏时间,12,"1,1,0,1,1,0",,1,6,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
600206,当前武器攻击连段攻击次数,6,2,6,1,%1武器的连段攻击次数,1,"1,1,0,1,1,0",,1,6,1028|1|0,,,,,,,,,,,,,,,,2,,,,,,,,,,,,,,
600207,对象身上的光照强度,6,2,6,1,%1身上的光照强度,12,"1,1,0,1,1,0",,1,6,1048|1|0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
600208,获取属性（字符串）,6,2,6,1,（字符串）获取%1的%2的%3,,"1,1,0,1,1,0",,,0,1048|1|0,,1010|1|0,1|300198,1010|1|0,1|300198,,,,,,,,,,,2,,,,,,,,,,,,,,
600300,玩家对象,6,3,6,1,%1对象,,"1,1,0,1,1,0",,1,47,1018|1|0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
600303,内部-获取幸存者主人,6,3,6,1,获取%1的主人,,"1,1,0,1,1,0",,1,17,1048|1|0,3|600101,,,,,,,,,,,,,,,2,,,,,,,,,,,,,,
600304,内部-获取饱食度,6,3,6,1,获取%1的饱食度,,"1,1,0,1,1,0",,1,6,1048|1|0,3|600101,,,,,,,,,,,,,,,2,,,,,,,,,,,,,,
600305,内部-获取濒死状态,6,3,6,1,获取%1的是否濒死,,"1,1,0,1,1,0",,1,13,1048|1|0,3|600101,,,,,,,,,,,,,,,2,,,,,,,,,,,,,,
600306,对象转玩家,6,3,6,1,%1玩家,8,"1,1,0,1,1,0",,1,17,1048|1|0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
600309,玩家的名字,6,3,6,1,%1名字,8,"1,1,0,1,1,0",,1,11,1018|1|0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
600310,玩家的迷你号,6,3,6,1,%1迷你号,8,"1,1,0,1,1,0",,1,11,1018|1|0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
600311,玩家属性,6,3,6,1,%1的%2,8,"1,1,0,1,1,0",,1,6,1018|1|0,,1020|1|2,5|140001,,,,,,,,,,,,,,100,,,,,,,,,,,,,
600312,寻找最近玩家,6,101,6,1,以%1为参照物，寻找%2%3格内%4最近的玩家。视线阻挡：%5，路径阻挡：%6,,"1,1,0,1,1,0",,1,17,1048|1|0,3|600101,1209|1|2,5|2090001,1005|1|0,1|5,1208|1|0,3|600102,1013|1|2,5|100001,1013|1|2,5|100001,,,,,,,,,,,,,,,,,,,
600313,寻找周边所有玩家,6,101,6,1,以%1为参照物，寻找%2%3格内%4玩家。视线阻挡：%5，路径阻挡：%6,,"1,1,0,1,1,0",,1,18,1048|1|0,3|600101,1209|1|2,5|2090001,1005|1|0,1|10,1208|1|0,3|600102,1013|1|2,5|100001,1013|1|2,5|100001,,,,,,,,,,,,,,,,,,,
600314,获取周边玩家数量,6,101,6,1,以%1为参照物，寻找%2%3格内%4玩家数量。视线阻挡：%5，路径阻挡：%6,,"1,1,0,1,1,0",,1,6,1048|1|0,3|600101,1209|1|2,5|2090001,1005|1|0,1|10,1208|1|0,3|600102,1013|1|2,5|100001,1013|1|2,5|100001,,,,,,,,,,,,,,,,,,,
600315,寻找区域内所有玩家,6,101,6,1,以%1为参照物，寻找%2内%3的所有玩家,,"1,1,0,1,1,0",,1,18,1048|1|0,3|600101,1001|1|0,,1208|1|0,3|600102,1106|1|0,5|730001,,,,,,,,,8,,,,,,,,,,,,,,
600316,寻找最近特定状态玩家,6,101,6,1,"以%1为参照物，寻找%2%3格内%4最近处于%5的玩家,视线阻挡：%6，路径阻挡：%7",,"1,1,0,1,1,0",,1,17,1048|1|0,3|600101,1209|1|2,5|2090001,1005|1|0,1|5,1208|1|0,3|600102,1037|1|0,2|4001,1013|1|2,5|100001,1013|1|2,5|100001,,,,100,,,,,,,,,,,,,
600400,此生物,6,4,6,1,此生物,5,"1,1,0,1,1,0",,1,30,,,,,,,,,,,,,,,,,,100,9401057,,,,,,,,,,,,
600406,生物对象,6,4,6,1,%1对象,,"1,1,0,1,1,0",,1,47,1031|1|0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
600407,对象转生物,6,4,6,1,%1生物,,"1,1,0,1,1,0",,1,30,1048|1|0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
600411,生物属性,6,4,6,1,%1的%2,5,"1,1,0,1,1,0",,1,6,1031|1|0,3|600400,1035|1|2,5|210001,,,,,,,,,,,,,,,,,,,,,,,,,,,
600412,特定生物类型,6,4,6,1,%1类型生物,5,"1,1,0,1,1,0",,1,105,1032|1|2,2|3400,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
600413,获取生物类型,6,4,6,1,%1的生物类型,5,"1,1,0,1,1,0",,1,31,1031|1|0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
600414,寻找生物,6,101,6,1,以%1为参照物，寻找%2%3格内%4最近的生物。视线阻挡：%5，路径阻挡：%6,5,"1,1,0,1,1,0",,1,30,1048|1|0,3|600101,1209|1|2,5|2090001,1005|1|0,1|10,1208|1|0,3|600102,1013|1|2,5|100001,1013|1|2,5|100001,,,,,,,,,,,,,,,,,,,
600415,寻找特定生物,6,101,6,1,以%1为参照物，寻找%2%3格内%4最近的%5。视线阻挡：%6，路径阻挡：%7,5,"1,1,0,1,1,0",,1,30,1048|1|0,3|600101,1209|1|2,5|2090001,1005|1|0,1|10,1208|1|0,3|600102,1032|1|0,2|3400,1013|1|2,5|100001,1013|1|2,5|100001,,,,,,,,,,,,,,,,,
600416,寻找周边所有特定生物,6,101,6,1,以%1为参照物，寻找%2%3格内%4%5。视线阻挡：%6，路径阻挡：%7,,"1,1,0,1,1,0",,1,32,1048|1|0,3|600101,1209|1|2,5|2090001,1005|1|0,1|10,1208|1|0,3|600102,1032|1|0,2|3400,1013|1|2,5|100001,1013|1|2,5|100001,,,,,,,,,,,,,,,,,
600417,获取周边生物数量,6,101,6,1,以%1为参照物，寻找%2%3格内%4%5数量。视线阻挡：%6，路径阻挡：%7,,"1,1,0,1,1,0",,1,6,1048|1|0,3|600101,1209|1|2,5|2090001,1005|1|0,1|10,1208|1|0,3|600102,1032|1|0,2|3400,1013|1|2,5|100001,1013|1|2,5|100001,,,,,,,,,,,,,,,,,
600418,寻找区域内生物,6,101,6,1,以%1为参照物，寻找区域%2内%3所有的%4,,"1,1,0,1,1,0",,1,32,1048|1|0,3|600101,1001|1|0,,1208|1|0,3|600102,1032|1|0,2|3400,,,,,,,,,8,,,,,,,,,,,,,,
600419,寻找指定状态的生物,6,101,6,1,以%1为参照物，寻找%2%3格内%4最近%5的生物。视线阻挡：%6，路径阻挡：%7,5,"1,1,0,1,1,0",,1,30,1048|1|0,3|600101,1209|1|2,5|2090001,1005|1|0,1|10,1208|1|0,3|600102,1037|1|0,2|4001,1013|1|2,5|100001,1013|1|2,5|100001,,,,,,,,,,,,,,,,,
600420,寻找指定状态的特定生物,6,101,6,1,以%1为参照物，寻找%2%3格内%4最近%5的%6。视线阻挡：%7，路径阻挡：%8,5,"1,1,0,1,1,0",,1,30,1048|1|0,3|600101,1209|1|2,5|2090001,1005|1|0,1|10,1208|1|0,3|600102,1037|1|0,2|4001,1032|1|0,2|3400,1013|1|2,5|100001,1013|1|2,5|100001,,100,,,,,,,,,,,,,
600501,寻找最近方块位置,6,101,6,1,"以%1为参照物,寻找最近的%2格内%3的位置。视线阻挡：%4，路径阻挡：%5",2,"1,1,0,1,1,0",,1,3,1048|1|0,3|600101,1005|1|0,1|10,1025|1|0,,1013|1|2,5|100001,1013|1|2,5|100001,,,,,,,,100,,,,,,,,,,,,,
600510,位置的光照强度,6,5,6,1,%1的光照强度,2,"1,1,0,1,1,0",,1,6,1002|1|0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
600599,投掷物对象,6,6,6,1,%1对象,,"1,1,0,1,1,0",,1,47,1044|1|0,,,,,,,,,,,,,,,,8,,,,,,,,,,,,,,
600600,掉落物对象,6,6,6,1,%1对象,,"1,1,0,1,1,0",,1,47,1030|1|0,,,,,,,,,,,,,,,,8,100,,,,,,,,,,,,,
600601,道具属性,6,6,6,1,道具:%1的%2,1,"1,1,0,1,1,0",,1,6,1048|1|0,,1099|1|0,5|680001,,,,,,,,,,,,,8,,,,,,,,,,,,,,
600602,特定道具类型,6,6,6,1,%1类型道具,1,"1,1,0,1,1,0",,1,105,1028|1|2,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
600603,拥有的道具数量,6,6,6,1,%1拥有的%2的数量,1,"1,1,0,1,1,0",,1,6,1048|1|0,3|600101,1028|1|0,2|200,,,,,,,,,,,,,,,,,,,,,,,,,,,
600604,获得对象道具类型,0,6,0,1,%1的道具类型,1,"1,1,0,1,1,0",,1,27,1048|1|0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
600606,寻找最近的特定道具,6,101,6,1,以%1为参照物，寻找%2%3格内最近的%4。视线阻挡：%5，路径阻挡：%6,1,"1,1,0,1,1,0",,1,47,1048|1|0,3|600101,1209|1|2,5|2090001,1005|1|0,1|10,1028|1|0,2|200,1013|1|2,5|100001,1013|1|2,5|100001,,,,,,,,,,,,,,,,,,,
600611,道具属性,0,6,0,1,道具:%1的%2,1,"1,1,0,1,1,0",,1,6,1048|1|0,,1099|1|0,5|680001,,,,,,,,,,,,,,,,,,,,,,,,,,,
600612,手持的道具类型,6,6,6,1,%1手持的道具类型,1,"1,1,0,1,1,0",,1,27,1048|1|0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
601001,随机数,6,10,6,0,生成随机整数:%1至%2,6,"1,1,0,1,1,0",,1,6,1005|1|0,1|1,1005|1|0,1|10,,,,,,,,,,,,,,,,,,,,,,,,,,,
601002,高级四则运算,6,10,6,1,%1%2%3,6,"1,1,0,1,1,0",,1,6,1005|1|0,1|0,1017|1|2,5|130001,1005|1|0,1|0,,,,,,,,,,,,,,,,,,,,,,,,,
601101,位置组中指定编号的位置,6,11,6,1,%1中编号为%2的位置,6,"1,1,0,1,1,0",,0,3,1062|0|0,,1005|1|0,1|1,,,,,,,,,,,,,,,,,,,,,,,,,,,
601102,位置组中的随机位置,6,11,6,1,%1中的随机位置,6,"1,1,0,1,1,0",,1,3,1062|0|0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
601103,数值组中指定编号的数值,6,11,6,1,%1中编号为%2的数值,6,"1,1,0,1,1,0",,0,6,1064|0|0,,1005|1|0,1|1,,,,,,,,,,,,,,,,,,,,,,,,,,,
601104,数值组中的随机数值,6,11,6,1,%1中的随机数值,6,"1,1,0,1,1,0",,1,6,1064|0|0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
601105,区域组中指定编号的区域,6,11,6,1,%1中编号为%2的区域,6,"1,1,0,1,1,0",,0,2,1063|0|0,,1005|1|0,1|1,,,,,,,,,,,,,,,,,,,,,,,,,,,
601106,区域组中的随机区域,6,11,6,1,%1中的随机区域,6,"1,1,0,1,1,0",,1,2,1063|0|0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
601107,对象组中指定编号的对象,6,11,6,1,%1中编号为%2的对象,6,"1,1,0,1,1,0",,0,47,1201|0|0,,1005|1|0,1|1,,,,,,,,,,,,,,,,,,,,,,,,,,,
601108,对象组中的随机对象,6,11,6,1,%1中的随机对象,6,"1,1,0,1,1,0",,1,47,1201|0|0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
601109,玩家组中指定编号的玩家,6,11,6,1,%1中编号为%2的玩家,6,"1,1,0,1,1,0",,0,17,1019|1|0,,1005|1|0,1|1,,,,,,,,,,,,,,,,,,,,,,,,,,,
601110,玩家组中的随机玩家,6,11,6,1,%1中的随机玩家,6,"1,1,0,1,1,0",,1,17,1019|1|0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
601111,生物组中指定编号的生物,6,11,6,1,%1中编号为%2的生物,6,"1,1,0,1,1,0",,0,30,1033|1|0,,1005|1|0,1|1,,,,,,,,,,,,,,,,,,,,,,,,,,,
601112,生物组中的随机生物,6,11,6,1,%1中的随机生物,6,"1,1,0,1,1,0",,1,30,1033|1|0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
601113,字符串组中指定编号的字符串,6,11,6,1,%1中编号为%2的字符串,6,"1,1,0,1,1,0",,0,11,1065|0|0,,1005|1|0,1|1,,,,,,,,,,,,,,,,,,,,,,,,,,,
601114,字符串组中的随机字符串,6,11,6,1,%1中的随机字符串,6,"1,1,0,1,1,0",,1,11,1065|0|0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
601115,获取对象组件属性,6,11,6,1,%1%2内的%3,6,"1,1,0,1,1,0",,1,,,,,,,,,,,,,,,,,,8,,,,,,,,,,,,,,
601201,计时器时间,6,12,6,1,%1的时间,,"1,1,0,1,1,0",,1,6,1040|1|0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
601401,文字板,6,16,6,1,内容为%1的文字板，字体大小%2，不透明度%3%，编号%4,,"1,1,0,1,1,0",,1,56,1010|1|0,1|300142,1005|1|0,1|16,1005|1|0,1|1,1005|1|0,1|1,,,,,,,,,,,,,,,,,,,,,,,806
601402,漂浮文字,6,16,6,1,内容为%1的漂浮文字，字体大小%2，编号%3,,"1,1,0,1,1,0",,1,56,1010|1|0,1|300142,1005|1|0,1|100,1005|1|0,1|1, ,,,,,,,,,,,,,,,,,,,,,95,,,807
601403,进度条,6,16,6,1,当前值%1、最大值%2的进度条，颜色%3，编号%4,,"1,1,0,1,1,0",,1,56,1005|1|0,1|100,1005|1|0,1|100,1056|1|0,1|0xF44336,1005|1|0,1|1,,,,,,,,,,,,,,,,,,,,,,,
601404,图片,6,16,6,1,图片为%1，大小为%2倍，不透明度为%3%，编号为%4,,"1,1,0,1,1,0",,1,56,1076|1|0,,1005|1|0,1|1,1005|1|0,1|100,1005|1|0,1|1,,,,,,,,,,,,,,,,,,,0,,,,808
601410,道具类型的图标,6,16,6,0,%1的图标,,"1,1,0,1,1,0",,1,75,1028|1|0,2|12005,,,,,,,,,,,,,,,,,,,,,,,,,,,,,815
601411,生物的图标,6,16,6,0,%1的图标,,"1,1,0,1,1,0",,1,75,1031|1|0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,816
601412,生物类型的图标,6,16,6,0,%1的图标,,"1,1,0,1,1,0",,1,75,1032|1|0,2|3400,,,,,,,,,,,,,,,,,,,,,,,,,,,,,817
601413,状态的图标,6,16,6,0,%1的图标,,"1,1,0,1,1,0",,1,75,1037|1|0,2|4001,,,,,,,,,,,,,,,,,,,,,,,,,,,,,818
601414,方块类型的图标,6,16,6,0,%1的图标,,"1,1,0,1,1,0",,1,75,1025|1|0,2|200,,,,,,,,,,,,,,,,,,,,,,,,,,,,,819
601415,玩家当前角色图标,6,16,6,0,当前%1角色的图标,,"1,1,0,1,1,0",,1,75,1018|1|0,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,820
601416,获取玩家快捷栏图标,6,16,6,0,%1快捷栏为%2的图标,,"1,1,0,1,1,0",,1,75,1018|1|0,,1005|1|0,1|1,,,,,,,,,,,,,,,,,,,,,,,,95,,,821
610101,获取对象位置,6,2,6,1,获取%1位置,,"1,1,0,1,1,0",,1,3,1048|1|0,,,,,,,,,,,,,,,,8,,,,,,,,,,,,,,
610102,寻找最近对象,6,101,6,1,以%1为参照物，寻找%2%3格内%4最近的%5,,"1,1,0,1,1,0",,1,47,1048|1|0,3|600101,1209|1|2,5|2090001,1005|1|0,1|10,1208|1|0,3|600102,1106|1|0,5|730001,,,,,,,8,,,,,,,,,,,,,,
610103,寻找周边对象,6,101,6,1,以%1为参照物，寻找%2%3格内%4%5,,"1,1,0,1,1,0",,1,201,1048|1|0,3|600101,1209|1|2,5|2090001,1005|1|0,1|10,1208|1|0,3|600102,1106|1|0,5|730001,,,,,,,8,,,,,,,,,,,,,,
610104,获取周边对象数量,6,101,6,1,以%1为参照物，寻找%2%3格内%4%5数量,,"1,1,0,1,1,0",,1,6,1048|1|0,3|600101,1209|1|2,5|2090001,1005|1|0,1|10,1208|1|0,3|600102,1106|1|0,5|730001,,,,,,,8,,,,,,,,,,,,,,
610105,寻找区域内所有对象,6,101,6,1,以%1为参照物，寻找区域%2内%3的所有%4,,"1,1,0,1,1,0",,1,201,1048|1|0,3|600101,1001|1|0,,1208|1|0,3|600102,1106|1|0,5|730001,,,,,,,,,8,,,,,,,,,,,,,,
615001,内部-获取主基地位置,6,101,6,1,获取主基地位置,12,"1,1,0,1,1,0",,1,3,,,,,,,,,,,,,,,,,2,,,,,,,,,,,,,,
700001,行为树变量（区域）,7,21,7,0,,,"1,1,0,1,1,0",,,2,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
700002,行为树变量（位置）,7,22,7,0,,,"1,1,0,1,1,0",,,3,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
700003,行为树变量（数值）,7,23,7,0,,,"1,1,0,1,1,0",,,6,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
700004,行为树变量（字符串）,7,24,7,0,,,"1,1,0,1,1,0",,,11,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
700005,行为树变量（布尔值）,7,25,7,0,,,"1,1,0,1,1,0",,,13,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
700006,行为树变量（对象）,7,30,7,0,,,"1,1,0,1,1,0",,,47,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
700007,行为树变量（对象类型）,0,31,0,0,,,"1,1,0,1,1,0",,,105,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
700008,行为树变量（对象组）,7,30,7,0,,,"1,1,0,1,1,0",,,201,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
700009,行为树变量（玩家）,7,3,7,0,,,"1,1,0,1,1,0",,,17,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
700010,行为树变量（玩家组）,7,3,7,0,,,"1,1,0,1,1,0",,,18,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
700011,行为树变量（方块类型）,7,5,7,0,,,"1,1,0,1,1,0",,,24,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
700012,行为树变量（道具类型）,7,6,7,0,,,"1,1,0,1,1,0",,,27,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
700013,行为树变量（生物）,7,4,7,0,,,"1,1,0,1,1,0",,,30,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
700014,行为树变量（生物类型）,7,26,7,0,,,"1,1,0,1,1,0",,,31,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
700015,行为树变量（生物组）,7,4,7,0,,,"1,1,0,1,1,0",,,32,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
700016,行为树变量（计时器）,7,12,7,0,,,"1,1,0,1,1,0",,,39,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
700017,行为树变量（特效类型）,7,14,7,0,,,"1,1,0,1,1,0",,,44,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
700018,行为树变量（位置组）,7,22,7,0,,,"1,1,0,1,1,0",,,61,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
700019,行为树变量（区域组）,7,21,7,0,,,"1,1,0,1,1,0",,,62,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
700020,行为树变量（数值组）,7,23,7,0,,,"1,1,0,1,1,0",,,63,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
700021,行为树变量（字符串组）,7,24,7,0,,,"1,1,0,1,1,0",,,64,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
700022,行为树变量（布尔值组）,7,25,7,0,,,"1,1,0,1,1,0",,,65,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
700023,行为树变量（方块类型组）,0,5,0,0,,,"1,1,0,1,1,0",,,66,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
700024,行为树变量（道具类型组）,0,6,0,0,,,"1,1,0,1,1,0",,,67,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
700025,行为树变量（生物类型组）,0,26,0,0,,,"1,1,0,1,1,0",,,68,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
700026,行为树变量（计时器组）,7,12,7,0,,,"1,1,0,1,1,0",,,69,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
700027,行为树变量（特效类型组）,0,14,0,0,,,"1,1,0,1,1,0",,,70,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
708001,全局变量模板（区域）,7,27,8,0,,,"1,1,0,1,1,0",,,2,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
708002,全局变量模板（位置）,7,28,8,0,,,"1,1,0,1,1,0",,,3,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
708003,全局变量模板（数值）,7,23,8,0,,,"1,1,0,1,1,0",,,6,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
708004,全局变量模板（字符串）,7,24,8,0,,,"1,1,0,1,1,0",,,11,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
708005,全局变量模板（布尔值）,7,25,8,0,,,"1,1,0,1,1,0",,,13,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
708006,全局变量模板（玩家）,7,3,8,0,,,"1,1,0,1,1,0",,,17,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
708007,全局变量模板（玩家组）,7,3,8,0,,,"1,1,0,1,1,0",,,18,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
708008,全局变量模板（方块类型）,7,5,8,0,,,"1,1,0,1,1,0",,,24,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
708009,全局变量模板（道具类型）,7,6,8,0,,,"1,1,0,1,1,0",,,27,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
708010,全局变量模板（生物）,7,4,8,0,,,"1,1,0,1,1,0",,,30,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
708011,全局变量模板（生物类型）,7,26,8,0,,,"1,1,0,1,1,0",,,31,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
708012,全局变量模板（生物组）,7,4,8,0,,,"1,1,0,1,1,0",,,32,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
708013,全局变量模板（计时器）,7,12,8,0,,,"1,1,0,1,1,0",,,39,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
708014,全局变量模板（特效类型）,7,14,8,0,,,"1,1,0,1,1,0",,,44,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
708015,全局变量模板（位置组）,7,22,8,0,,,"1,1,0,1,1,0",,,61,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
708016,全局变量模板（区域组）,7,21,8,0,,,"1,1,0,1,1,0",,,62,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
708017,全局变量模板（数值组）,7,23,8,0,,,"1,1,0,1,1,0",,,63,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
708018,全局变量模板（字符串组）,7,24,8,0,,,"1,1,0,1,1,0",,,64,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
708019,全局变量模板（布尔值组）,7,25,8,0,,,"1,1,0,1,1,0",,,65,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
708020,全局变量模板（方块类型组）,7,5,8,0,,,"1,1,0,1,1,0",,,66,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
708021,全局变量模板（道具类型组）,7,6,8,0,,,"1,1,0,1,1,0",,,67,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
708022,全局变量模板（生物类型组）,7,26,8,0,,,"1,1,0,1,1,0",,,68,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
708023,全局变量模板（计时器组）,7,12,8,0,,,"1,1,0,1,1,0",,,69,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
708024,全局变量模板（特效类型组）,7,14,8,0,,,"1,1,0,1,1,0",,,70,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
