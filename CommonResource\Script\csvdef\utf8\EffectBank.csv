id,效果名,描述,分类（1=一般效果 2=特殊效果 3=自定义的效果组件）,显示开关（不填=全显示 1=只在BUFF编辑显示 2=只在装备编辑显示 3=不显示）,隐藏Tips显示（0=不隐藏，1=在bufftips中隐藏）,属性类型（该类型与EffectBank表相通，10开头表示在EffectBankEnum表中，20开头表示在EffectBank表中 该参数需要程序来定义）,触发类型（1=计算公式内生效 2=直接生效 3=间隔时间内持续生效）,图标名字,参数类型1(100xxx=枚举类型  200xxx=滑动条数值  300001=开关  400001=模型库  400002=状态库  400003=道具库  400004=音效库  400006=状态类型 400007=特效库 400008=UI库 400009=方块库）,参数1默认值（枚举=填枚举ID 滑动条数值=填默认数值  开关=填0关，填1开   素材库类型=填对应库中的ID  ）,参数1描述ID（填StringID）,参数类型2,参数2默认值,参数2描述ID,参数类型3,参数3默认值,参数3描述ID,参数类型4,参数4默认值,参数4描述ID,参数类型5,参数5默认值,参数5描述ID,备注
ID,Name,Desc,Itemize,Display,TipsDisplay,AttType,TriggerType,IconName,ParaType1,Default1,DescriptionID1,ParaType2,Default2,DescriptionID2,ParaType3,Default3,DescriptionID3,ParaType4,Default4,DescriptionID4,ParaType5,Default5,DescriptionID5,
1001,改变移动速度,使角色的@1@2,1,,,,1,move_speed_add,100101,100101,25429,200012,20,25430,,,,,,,,,,
1002,改变挖掘速度,使角色的挖掘速度@1,1,,,2001,1,dig_speed_add,200012,20,25431,,,,,,,,,,,,,
1003,改变伤害倍率,使角色造成的@1@2,1,,,,1,attack_punch_add,100301,100301,25432,200012,20,25430,,,,,,,,,,
1004,跳跃,使角色的跳跃力增加@1,1,,,2002,1,move_speed_add,200004,20,25433,,,,,,,,,,,,,
1005,改变最大生命值/饥饿度,使角色的最大@1@2,1,,,,2,MODEL_ADD,100201,100201,25434,200003,100,25433,,,,,,,,,,
1006,持续改变饥饿度,每隔@1秒使角色的饥饿度@2点,1,,,2003,3,damagebuff_hunger,200005,10,25435,200003,10,25433,,,,,,,,,,
1007,持续恢复生命值,每隔@1秒持续恢复角色的生命值@2点最大生命值,1,,,2004,3,addhpbuff_add,200005,10,25435,200011,10,25433,,,,,,,,,,
1008,持续受到伤害,每隔@1秒持续使角色受到@2点@3,1,,,2020,3,NO-OXY-Dying,200005,10,25435,200011,10,25433,101004,101004,25432,,,,,,,
1009,视野亮度,使角色的视野亮度@1,1,,,2005,2,view_bright,200001,10,25433,,,,,,,,,,,,,
1010,受伤倍率,使角色受到的@1@2,1,,,,1,luck_killmob,100401,100401,25436,200012,-20,25433,,,,,,,,,,
1011,饥饿流失速度,使角色的饥饿度流失速度@1,1,,,2006,2,damagebuff_hunger,200012,10,25430,,,,,,,,,,,,,
1012,幸运,使角色@1时有@2的几率获得@3倍掉落,1,,,,1,move_speed_add,100601,100601,25437,200006,10,25447,200007,2,25457,,,,,,,
1013,击退强化,使角色击退时的距离增加@1格,1,,,2007,2,knock,200008,2,25438,,,,,,,,,,,,,
1014,击退抵抗,使角色被击退时的距离减少@1格,1,,,2008,2,knock_resist,200008,2,25438,,,,,,,,,,,,,
1015,模型体积,使角色的@1的体积@2,2,,,,2,model_ghost,100701,100701,25439,200012,100,25456,,,,,,,,,,
1016,改变模型,使角色的模型改变为@1,2,,,2014,2,OXY-satable,400001,3400,25440,300001,0,25441,300001,0,25442,,,,,,,
1017,晕头转向,使角色的移动操作，变为反向,2,,,2015,2,damagebuff_wither,,,,,,,,,,,,,,,,
1018,无敌,使角色无法被攻击,2,,,2016,2,damaged_all_add,,,,,,,,,,,,,,,,
1019,泡泡,使角色被泡泡包裹,2,,,2017,2,Hamster_Ball,,,,,,,,,,,,,,,,
1020,无法移动,使角色无法进行移动并原地旋转,2,,,2018,2,move_speed_reduce,,,,,,,,,,,,,,,,
1021,氧气,使角色在@1环境中每隔@2秒氧气值@3点,1,,,,3,swim_speed_add,100801,100801,25443,200005,10,25435,200010,5,25433,,,,,,,
1022,吸收伤害,使角色获得一个可以吸收@1伤害的护盾,1,,,2009,2,damage_absorb,200011,100,25444,,,,,,,,,,,,,
1023,改变攻击力,使角色的@1@2,1,,,,1,attack_all_add,100901,100901,25445,200003,50,25433,,,,,,,,,,
1024,攻击击飞,使角色的近战攻击会将目标击飞@1格,1,,,2010,2,DAMAGED_FALLING_ADD,200008,3,25446,,,,,,,,,,,,,
1025,攻击附加状态,使角色攻击命中时有@1的几率对目标附加持续时间为@2秒的@3状态,1,,,2011,1,attack_fire_add,200006,50,25447,200005,1,25448,400002,8001,25455,,,,,,,
1026,改变防御值,使角色的@1@2,1,,,,1,armor_add,100501,100501,25449,200003,50,25433,,,,,,,,,,
1027,反伤,使角色受到近战攻击的时有@1的几率对攻击者造成@2点伤害,1,,,2012,1,dameged_wither_add,200006,50,25447,200011,50,25450,,,,,,,,,,
1028,爬墙,使角色在任意侧面都可以像爬梯那样移动,2,,,2019,2,move_speed_add,,,,,,,,,,,,,,,,
1029,生命值恢复速度,使角色的生命值恢复速度@1,1,,,2026,1,hp_recoveryrate,200012,50,25430,,,,,,,,,,,,,
1030,经验获取速度,使角色的经验获取速度@1,1,,,2025,1,exp_gainrate,200012,50,25430,,,,,,,,,,,,,
1031,体力值上限百分比,使角色的@1上限@2,1,,,,1,sta_perc_breakthrough,101101,101101,25459,200012,50,25430,,,,,,,,,,
1032,体力值上限值,使角色的@1上限@2,1,,,,1,sta_breakthrough,101101,101101,25459,200003,50,25433,,,,,,,,,,
1033,持续恢复体力值,每隔@1秒恢复@2点体力值,1,,,2027,3,sta_recovery,200005,10,25435,200011,10,25433,,,,,,,,,,
1034,体力值消耗速率,使角色进行@1时，体力消耗@2,1,,,,1,sta_reducerate,101201,101201,25460,200012,10,25430,,,,,,,,,,
1035,体力值恢复速率,使角色处于@1时，体力恢复速度@2,1,,,,1,sta_growthrate,101301,101301,25461,200012,10,25430,,,,,,,,,,
1036,疲劳解除,角色体力值恢复至@1以上时，解除疲劳状态,2,,,2029,2,tired,200004,15,25462,,,,,,,,,,,,,
1037,禁食,无法使用食物道具,2,,,2021,2,fast,,,,,,,,,,,,,,,,
1038,状态开始时产出掉落物,@1状态开始时，使角色产生出@2,2,3,,2022,2,produce,400002,1001,25464,400003,206,25463,400004,25465,25465,,,,,,,
1039,持续产出掉落物,每隔@1秒，使角色产生出@2,2,,,2023,3,produce_continue,200005,10,25435,400003,206,25463,400004,25465,25465,,,,,,,
1040,无法奔跑,使角色无法进行奔跑,2,,,2024,2,run_no,,,,,,,,,,,,,,,,
1041,状态结束时产出掉落物,@1状态结束时，使角色产生出@2,2,3,,2028,2,produce,400002,1001,25464,400003,206,25463,400004,25465,25465,,,,,,,
1042,改变模型（皮肤）,使角色的模型改变为@1,2,3,,2030,2,OXY-satable,400005,,,,,,,,,,,,,,,只用于巴啦啦变身
1043,持续恢复生命值,每@1秒恢复生命值@2点,1,,,2004,3,addhpbuff_add,200005,10,25435,200011,10,25433,,,,,,,,,,
1044,持续受到伤害,每@1秒受到@2点伤害,1,,,2020,3,NO-OXY-Dying,200005,10,25435,200011,10,25433,101004,101004,25432,,,,,,,
1045,消耗体力受到伤害,角色消耗体力时受到等量的@1,2,,,2031,2,NO-OXY-Dying,100312,100312,25432,,,,,,,,,,,,,
1046,执行动作时受到伤害,角色进行@1时受到@2点@3,1,,,,2,NO-OXY-Dying,101401,101401,25460,200011,10,25433,100312,100312,25432,,,,,,,
1047,状态免疫,角色免疫@1,2,3,,2032,2,buff_defence,400002,77001,25461,,,,,,,,,,,,,
1048,附增状态,每隔@1秒使角色获得@2,2,3,,2033,2,buff_additional,200005,10,25435,400002,4001,25461,,,,,,,,,,
1049,禁止操作,禁止角色所有行为,2,3,,2034,2,DeepFreeze,,,,,,,,,,,,,,,,
1050,状态类型免疫,角色免疫@1类的所有状态,2,3,,2035,2,bufftype_defence,400006,3,25461,,,,,,,,,,,,,
1051,坠落,使飞行生物跌落到地上,2,3,,2036,2,damagebuff_wither,,,,,,,,,,,,,,,,
1052,禁止移动,禁止角色移动,2,3,,2037,2,damagebuff_wither,,,,,,,,,,,,,,,,
1053,发疯攻击任何生物,,2,3,,2038,2,,,,,,,,,,,,,,,,,
1054,彻底变身,使角色的模型以及属性和攻击方式改变为@1,2,3,,2039,2,OXY-satable,400001,3400,25440,300001,0,25441,300001,0,25442,,,,,,,
1055,右键点击,右键点击触发,2,3,,2040,2,,,,,,,,,,,,,,,,,
1056,屏幕贴花,使角色屏幕显示贴花,2,,1,2041,2,ScreenDecal,400008,24001,25474,300001,1,25475,,,,,,,,,,
1057,停止动作,使角色动作停在当前帧,2,,1,2042,2,StopAction,,,,,,,,,,,,,,,,
1058,改变贴图,使角色贴图变为冰块材质,2,,1,2043,2,freeze,,0,,200014,-3,,200014,-7,,,,,,,,
1059,添加特效,在角色身上添加特效,2,,1,2044,2,AddModel,400007,3035,25410,200016,10,25476,300001,1,25477,,,,,,,
1060,结束改变温度,状态结束时将角色的温度设置为@1,1,,1,2045,2,EndAttribute,200014,5,25433,,,,,,,,,,,,,
1061,结束播放特效音效,状态结束时播放特效和音效,2,,1,2046,2,EndEffect,400007,1009,25410,400004,10982,25411,,,,,,,,,,
1062,温度影响持续时间,状态时长会因为周围温度改变,2,,1,2047,2,melt,101501,101501,25478,200011,100,25479,200011,10,25480,200011,5,25481,200011,500,25482,
1063,累计受到伤害解除状态,累计受到@1伤害达到@2解除状态,1,,1,2048,2,HurtEnd,100412,100412,25432,200011,50,25483,,,,,,,,,,
1064,持续改变温度,每隔@1秒使角色温度改变@2,1,,,2049,3,ChangeTemp,200004,1,25435,200015,10,25433,300001,1,25484,,,,,,,
1065,体感温度改变,使角色受到的环境温度改变@1,1,,,2050,1,ColdDef,200015,50,25433,300001,1,25484,,,,,,,,,,
1066,改变周围温度,使角色周围@1格内的环境温度增加@2,2,3,,2051,3,SwitchTemperature,200013,1,25485,200014,1,25433,,,,,,,,,,
1067,方块凝结与融化,使角色周围@1格，上下高度@2格内的液体凝结,2,,1,2052,3,BlockCongeal,200013,1,25485,200013,1,25485,,,,,,,,,,
1068,周围随机生成方块,在玩家周围半径@1高度@2的圆柱范围内，每隔@3秒随机一个方块顶面生成@4,2,,1,2053,3,Generate,200013,5,25485,200013,5,25485,200004,1,25435,400009,115,25486,,,,
1069,使攻击者获得状态,受到@1次攻击时，有@2%概率使攻击者获得@3,2,,,2054,2,attack_fire_add,200004,1,25485,200004,100,25485,400002,1001,25464,,,,,,,
1070,食物恢复量倍率改变,@1食物的@2恢复量提高@3,1,3,,,2,buff_appetite,101701,101701,25487,101801,101801,25488,200002,30,25430,,,,,,,
1071,使受击者获得状态,受到@1次攻击时，有@2%概率获得@3,2,,,2055,2,attack_fire_add,200004,1,25485,200004,100,25485,400002,1001,25464,,,,,,,
1072,修改枪械属性,枪械@1 @2,1,3,,,1,,102001,102001,,200001,0,25433,,,,,,,,,,
1073,修改枪械属性，按百分比,枪械@1 @2,1,3,,,1,,102101,102101,,200002,0,,,,,,,,,,,
1074,修改buff/枪skill/投掷物,,1,3,,,1,,102201,102201,,,,,,,,,,,,,,
1075,获得护盾,,1,3,,,1,,101801,101804,,200001,10,,,,,,,,,,,
1076,改变最大生命值/饥饿度（百分比）,使角色的最大@1@2,1,,,,2,MODEL_ADD,100201,100201,25434,200002,10,,,,,,,,,,,
1077,持续恢复生命值百分比,"每隔@1秒持续恢复角色@2的生命值,@3立即生效",1,,,2057,3,addhpbuff_add,200004,1,25435,200002,10,25433,300001,0,25489,,,,,,,
1078,降低防御值百分比,使角色的@1@2,1,,,,1,armor_add,100501,100501,25449,200002,10,,,,,,,,,,,
1079,消耗口渴受到伤害,角色消耗体力时受到等量的@1,2,,,2031,2,NO-OXY-Dying,100312,100312,25432,,,,,,,,,,,,,
9999,,,3,3,1,,,buff_appetite,,,,,,,,,,,,,,,,自定义效果模板
