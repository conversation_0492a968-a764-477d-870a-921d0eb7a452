
#include "BlockVaryVerticalSlab.h"
#include "BlockMaterialMgr.h"
#include "section.h"
#include "SectionMesh.h"
#include "BlockGeom.h"
#include "world.h"
#include "IPlayerControl.h"
#include "DefManagerProxy.h"
#include "WorldManager.h"
#include "WeatherManager.h"
//#include "OgreMaterial.h"

IMPLEMENT_SCENEOBJECTCLASS(VerticalVarySlabMaterial)
//IMPLEMENT_BLOCKINSTANCE(SlabMaterial)
using namespace MINIW;

void VerticalVarySlabMaterial::initExVertData()
{
	if (m_mHalfWholeFace().size() == 4)
	{
		for (int d = 0; d < 4; d++)
		{
			dynamic_array<BlockGeomVert> lvertices(kMemVertexData);
			dynamic_array<BlockGeomVert> bvertices(kMemVertexData);
			for (const auto& subvert : m_mHalfWholeFace()[d])
			{
				BlockGeomVert littleVert = subvert;
				BlockGeomVert bigVert = subvert;
				if (subvert.pos.x == 50)
				{
					littleVert.pos.x -= 25 * g_DirectionCoord[d].x;
					bigVert.pos.x += 25 * g_DirectionCoord[d].x;
				}
				else if (subvert.pos.z == 50)
				{
					littleVert.pos.z -= 25 * g_DirectionCoord[d].z;
					bigVert.pos.z += 25 * g_DirectionCoord[d].z;
				}
				lvertices.push_back(littleVert);
				bvertices.push_back(bigVert);
			}
			m_mLittleHalfWholeFace.push_back(lvertices);
			m_mBigHalfWholeFace.push_back(bvertices);
		}
	}
	if (m_mHalfFace().size() == 16)
	{
		for (int d = 0; d < 16; d++)
		{
			dynamic_array<BlockGeomVert> lvertices(kMemVertexData);
			dynamic_array<BlockGeomVert> bvertices(kMemVertexData);
			DirectionType dir = (DirectionType)(d / 4);
			for (const auto& subvert : m_mHalfFace()[d])
			{
				BlockGeomVert littleVert = subvert;
				BlockGeomVert bigVert = subvert;
				if (subvert.pos.x == 50)
				{
					littleVert.pos.x += 25 * g_DirectionCoord[dir].x;
					bigVert.pos.x -= 25 * g_DirectionCoord[dir].x;
				}
				else if (subvert.pos.z == 50)
				{
					littleVert.pos.z += 25 * g_DirectionCoord[dir].z;
					bigVert.pos.z -= 25 * g_DirectionCoord[dir].z;
				}
				lvertices.push_back(littleVert);
				bvertices.push_back(bigVert);
			}
			m_mLittleHalfFace.push_back(lvertices);
			m_mBigHalfFace.push_back(bvertices);
		}
	}
}

void VerticalVarySlabMaterial::initVertData()
{
	VerticalSlabMaterial::initVertData();
	initExVertData();
}

bool VerticalVarySlabMaterial::hasSolidTopSurface(int blockdata)
{
	if(blockdata == 0) return false;
	else return true;
}

float VerticalVarySlabMaterial::getBlockHeight(int blockdata)
{
	if(blockdata & 8)
	{
		if (blockdata & 4)
		{
			return 1.0f;
		}
		return 0.75f;
	}
	else if (blockdata & 4)
	{
		return 0.5f;
	}
	return 0.25f;
}

bool VerticalVarySlabMaterial::canAttachedToMecha(int curblockdata, DirectionType dir, int neighbor_id, int neighbor_data)
{
	if(dir == DIR_NEG_Y)
	{
		return curblockdata != 1;
	}
	else if(dir == DIR_POS_Y)
	{
		return curblockdata != 0;
	}
	else return true;
}

void VerticalVarySlabMaterial::dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata, BLOCK_MINE_TYPE droptype, float chance, int uin/* = -1 */)
{
	CubeBlockMaterial::dropBlockAsItem(pworld, blockpos, blockdata, droptype, chance, uin);
	float height = getBlockHeight(blockdata);

	int exCount = height == 1.f ? 3 : height == 0.75f ? 2 : height == 0.5f ? 1 : 0;
	for (int i = 0; i < exCount; i++)
	{
		CubeBlockMaterial::dropBlockAsItem(pworld, blockpos, blockdata, droptype, chance, uin);
	}
}

char* VerticalVarySlabMaterial::getPhisicMeshBit(BaseSection* psection, const WCoord& blockpos)
{
#ifdef IWORLD_SERVER_BUILD
	if (!m_mBigHalfFace.size())
	{
		initVertData();
	}
	if (!m_mLittleHalfFace.size())
	{
		initExVertData();
	}
#endif	
	auto pblock = psection->getBlock(blockpos);
	int curblockdata = pblock.getData();
	int curDir = curblockdata & 3;

	float blockheight = getBlockHeight(curblockdata);

	char key[32];
	sprintf(key, "%f_%d", blockheight, curDir);
    if (getGeom())
    {
        char* cache = getGeom()->getPhisicMeshBitBaseCache(key);
        if (cache)
            return cache;
    }

	std::vector<BlockGeomMeshInfo*> infos;
	const BiomeDef* biome = GetDefManagerProxy()->getBiomeDef(1);
	dynamic_array<UInt16>* indices = m_dPosIndices;
	if (1.f == blockheight)
	{
		for (int i = 0; i < 6; i++)
		{
			BlockGeomMeshInfo* meshinfo = ENG_NEW_LABEL(BlockGeomMeshInfo, kMemTempAlloc);
			meshinfo->vertices = m_mWholeFace()[i];
			meshinfo->indices = *indices;
			infos.push_back(meshinfo);
		}
	}
	else
	{
		for (int i = 0; i < 6; i++)
		{
			BlockGeomMeshInfo* meshinfo = ENG_NEW_LABEL(BlockGeomMeshInfo, kMemTempAlloc);
			if (4 == i)
			{
				meshinfo->vertices = m_mWholeFace()[ReverseDirection(curDir)];
				meshinfo->indices = *indices;
			}
			else if (5 == i)
			{
				dynamic_array<dynamic_array<BlockGeomVert>>* pvc = &m_mHalfWholeFace();
				if (blockheight == 0.25f)
				{
					pvc = &m_mLittleHalfWholeFace;
				}
				else if (blockheight == 0.75f)
				{
					pvc = &m_mBigHalfWholeFace;
				}
				meshinfo->vertices = (*pvc)[curDir];
				meshinfo->indices = *indices;
			}
			else
			{
				dynamic_array<dynamic_array<BlockGeomVert>>* pvc = &m_mHalfFace();
				if (blockheight == 0.25f)
				{
					pvc = &m_mLittleHalfFace;
				}
				else if (blockheight == 0.75f)
				{
					pvc = &m_mBigHalfFace;
				}
				meshinfo->vertices = (*pvc)[ReverseDirection(curDir) * 4 + i]; 
				meshinfo->indices = *indices;
			}
			infos.push_back(meshinfo);
		}
	}

    if (!getGeom())
        return NULL;
	char* ret = getGeom()->getPhisicMeshBitBase(key, infos);
	for (int i = 0; i < (int)infos.size(); i++)
	{
		ENG_DELETE_LABEL(infos[i], kMemTempAlloc);
	}
	return ret;
}

void VerticalVarySlabMaterial::createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh)
{
	auto psection = data.m_SharedSectionData;
#ifndef IWORLD_SERVER_BUILD
	if (1 == BlockMaterialMgr::m_BlockShape || poutmesh->isSquareSectionMesh())
	{
		FaceVertexLight faceVertexLight;
		auto pblock = psection->getBlock(blockpos);

		int curblockdata = pblock.getData();
		int curDir = curblockdata & 3;

		const BiomeDef* biome = GetDefManagerProxy()->getBiomeDef(1);

		float blockheight = getBlockHeight(curblockdata);

		std::vector<int> wholeFace;
		std::vector<int> halfWholeFace;
		std::vector<int> halfFace;

		if (blockheight == 1.f)
		{
			for (int i = 0; i < 6; i++)
			{
				wholeFace.push_back(i);
			}
		}
		else
		{
			wholeFace.push_back(ReverseDirection(curDir));
			halfWholeFace.push_back(curDir);//0+4
			for (int ii = ReverseDirection(curDir) * 4; ii < ReverseDirection(curDir) * 4 + 4; ii++)
			{
				halfFace.push_back(ii);
			}
		}
		BlockColor facecolor(255, 255, 255, 0);
		for (auto& d : wholeFace)
		{
			DirectionType dir = (DirectionType)d;
//			if (m_DisableCoverFaceOpt || dir == specialdir || !psection->getNeighborCover(blockpos, this, curblockdata, dir))
			{
				bool flipQuad = psection->getCubeFaceVertexLight(blockpos, dir, faceVertexLight);

				dynamic_array<UInt16>* indices = /*flipQuad ? &m_dNegIndices : */m_dPosIndices;

				RenderBlockMaterial* pmtl = getFaceMtl(biome, dir, pblock.getData(), facecolor);
				if (pmtl == NULL)
					continue;
				SectionSubMesh* psubmesh = poutmesh->getSubMesh(pmtl);
				const float* uvtile = nullptr;
				if (psubmesh && !psubmesh->IsIgnoreTileUV())
					uvtile = pmtl->getUVTile();
				BlockGeomMeshInfo mesh;

				mesh.vertices = m_mWholeFace()[d];
				mesh.indices = *indices;
				unsigned int avelt[4];
				getAvelt(data, blockpos, dir, avelt);
				for (int n = 0; n < m_mWholeFace()[d].size(); n++)
				{
					auto& vert = mesh.vertices[n];
					InitBlockVertLight(vert, avelt[n], uvtile);
				}
				if (psubmesh) psubmesh->addGeomFaceLight(mesh, &blockpos, faceVertexLight, &facecolor, pmtl->getUVTile());
			}
		}

		for (auto& d : halfWholeFace)
		{
			DirectionType dir = (DirectionType)d;
			// 		if (m_DisableCoverFaceOpt || dir == specialdir || !psection->getNeighborCover(blockpos, this, curblockdata, dir))
			{
				bool flipQuad = psection->getCubeFaceVertexLight(blockpos, dir, faceVertexLight);

				dynamic_array<UInt16>* indices = m_dPosIndices;

				RenderBlockMaterial* pmtl = getFaceMtl(biome, dir, pblock.getData(), facecolor);
				if (pmtl == NULL)
					continue;
				SectionSubMesh* psubmesh = poutmesh->getSubMesh(pmtl);
				const float* uvtile = nullptr;
				if (psubmesh && !psubmesh->IsIgnoreTileUV())
					uvtile = pmtl->getUVTile();
				BlockGeomMeshInfo mesh;

				if (blockheight == 0.25f)
				{
					mesh.vertices = m_mLittleHalfWholeFace[d];
				}
				else if (blockheight == 0.5f)
				{
					mesh.vertices = m_mHalfWholeFace()[d];
				}
				else if (blockheight == 0.75f)
				{
					mesh.vertices = m_mBigHalfWholeFace[d];
				}
				mesh.indices = *indices;
				unsigned int avelt[4];
				getAvelt(data, blockpos, dir, avelt);
				for (int n = 0; n < mesh.vertices.size(); n++)
				{
					auto& vert = mesh.vertices[n];
					InitBlockVertLight(vert, avelt[n], uvtile);
				}
				if (psubmesh) psubmesh->addGeomFaceLight(mesh, &blockpos, faceVertexLight, &facecolor, pmtl->getUVTile());
			}
		}

		for (auto& d : halfFace)
		{
			DirectionType dir = DIR_NOT_INIT;
			int mainDir = d / 4;
			int side = d % 4;
			if (side < 2)
			{
				dir = side == 1 ? DIR_POS_Y : DIR_NEG_Y;
			}
			else
			{
				dir = side == 2 ? RotateDir90(mainDir) : RotateDirPos90(mainDir);
			}
			// 		if (m_DisableCoverFaceOpt || dir == specialdir || !psection->getNeighborCover(blockpos, this, curblockdata, dir))
			{
				bool flipQuad = psection->getCubeFaceVertexLight(blockpos, dir, faceVertexLight);

				dynamic_array<UInt16>* indices = m_dPosIndices;

				RenderBlockMaterial* pmtl = getFaceMtl(biome, dir, pblock.getData(), facecolor);
				if (pmtl == NULL)
					continue;
				SectionSubMesh* psubmesh = poutmesh->getSubMesh(pmtl);
				const float* uvtile = nullptr;
				if (psubmesh && !psubmesh->IsIgnoreTileUV())
					uvtile = pmtl->getUVTile();
				BlockGeomMeshInfo mesh;
				if (blockheight == 0.25f)
				{
					mesh.vertices = m_mLittleHalfFace[d];
				}
				else if (blockheight == 0.5f)
				{
					mesh.vertices = m_mHalfFace()[d];
				}
				else if (blockheight == 0.75f)
				{
					mesh.vertices = m_mBigHalfFace[d];
				}
				mesh.indices = *indices;
				unsigned int avelt[4];
				getAvelt(data, blockpos, dir, avelt);
				for (int n = 0; n < mesh.vertices.size(); n++)
				{
					auto& vert = mesh.vertices[n];
					InitBlockVertLight(vert, avelt[n], uvtile);
				}
				if (psubmesh) psubmesh->addGeomFaceLight(mesh, &blockpos, faceVertexLight, &facecolor, pmtl->getUVTile());
			}
		}
	}
	else
	{
		CubeBlockMaterial::createBlockMesh(data, blockpos, poutmesh);
	}
#endif
}


SectionMesh* VerticalVarySlabMaterial::createBlockProtoMesh(int protodata)
{
	if (1 == BlockMaterialMgr::m_BlockShape)
	{
		SectionMesh* pmesh = ENG_NEW(SectionMesh)();
		int blockdata = protodata;
		const BiomeDef* biome = GetDefManagerProxy()->getBiomeDef(0);
		//  std::vector<unsigned short>* indices = &m_dPosIndices;

		char list[6] = { 0,1,2,3,0,1 };
		for (int i = 0; i < 6; i++)
		{
			DirectionType dir = (DirectionType)i/*(list[i] % 4)*/;
			BlockColor facecolor(255, 255, 255, 0);
			RenderBlockMaterial* pmtl = getFaceMtl(biome, dir, blockdata, facecolor);
			SectionSubMesh* psubmesh = pmesh->getSubMesh(pmtl, true);

			BlockGeomMeshInfo meshinfo;
			if (0 == i)
			{
				meshinfo.vertices = m_mWholeFace()[list[i]];
				meshinfo.indices = *m_dPosIndices;
			}
			else if (1 == i)
			{
				meshinfo.vertices = m_mLittleHalfWholeFace[list[i]];
				meshinfo.indices = *m_dPosIndices;
			}
			else
			{
				meshinfo.vertices = m_mLittleHalfFace[list[i]];
				meshinfo.indices = *m_dPosIndices;
			}
			if (psubmesh)
				psubmesh->addGeomFaceLight(meshinfo, NULL, s_DefaultFaceVertexLights, &facecolor);
		}
		return pmesh;
	}
	return CubeBlockMaterial::createBlockProtoMeshAngle(protodata);
}

void VerticalVarySlabMaterial::createCollideData(CollisionDetect* coldetect, World* pworld, const WCoord& blockpos)
{
	float blockheight = getBlockHeight(pworld->getBlockData(blockpos));
	WCoord pos = blockpos * BLOCK_SIZE;
	if (blockheight >= 1.f)
	{
		coldetect->addObstacle(pos, pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
	}
	else
	{
		int dir = pworld->getBlockData(blockpos) & 3;
		int x1 = int(g_DirectionCoord[ReverseDirection(dir)].x * BLOCK_SIZE * (1.f - blockheight) * (ReverseDirection(dir) % 2));
		int z1 = int(g_DirectionCoord[ReverseDirection(dir)].z * BLOCK_SIZE * (1.f - blockheight) * (ReverseDirection(dir) % 2));

		int x2 = BLOCK_SIZE + int(g_DirectionCoord[ReverseDirection(dir)].x * BLOCK_SIZE * (blockheight) * (1 - (ReverseDirection(dir) % 2)));
		int z2 = BLOCK_SIZE + int(g_DirectionCoord[ReverseDirection(dir)].z * BLOCK_SIZE * (blockheight) * (1 - (ReverseDirection(dir) % 2)));
		if (dir == DIR_POS_X)
		{
			x2 = BLOCK_SIZE - x2;
		}
		else if (dir == DIR_POS_Z)
		{
			z2 = BLOCK_SIZE - z2;
		}

		coldetect->addObstacle(pos + WCoord(x1,0, z1),pos + WCoord(x2,BLOCK_SIZE,z2));
	}
}

bool VerticalVarySlabMaterial::coverNeighbor(int curblockdata, SolidBlockMaterial* neighbor, int neighbor_data, DirectionType dir)
{
	if ((curblockdata & 12) == 12)
	{
		return true;
	}
	else
	{
		int curDir  = curblockdata & 3;
		if (curDir == ReverseDirection(dir))
		{
			return true;
		}
	}
	return false;
}

typedef VerticalVarySlabMaterial::BlockInstance VerticalVarySlabMaterialInstance;
IMPLEMENT_SCENEOBJECTCLASS(VerticalVarySlabMaterialInstance)
MNSandbox::ReflexClassParam<VerticalVarySlabMaterial::BlockInstance, int> VerticalVarySlabMaterialInstance::R_Dir(0, "Dir", "Block", &VerticalVarySlabMaterial::BlockInstance::GetBlockData, &VerticalVarySlabMaterial::BlockInstance::SetBlockData);


