--声明
local furnacemainCtrl = Class("furnacemainCtrl",ClassList["UIBaseCtrl"])

local FuelCount = {
	[0] = 1,
	[1] = 1,
	[2] = 1,
	[3] = 2,
	[4] = 1,
}

local MatCount = {
	[0] = 2,
	[1] = 2,
	[2] = 5,
	[3] = 1,
	[4] = 1,
}

local RetCount = {
	[0] = 3,
	[1] = 3,
	[2] = 3,
	[3] = 10,
	[4] = 3,
}

--创建
function furnacemainCtrl:Create(param)
	return ClassList["furnacemainCtrl"].new(param)
end

--初始化
function furnacemainCtrl:Init(param)
	self.super:Init(param)

	self.ge = param.ge
end

--启动
function furnacemainCtrl:Start()
	self.super:Start()
	self.view:InitView()

	self.paneltype = OpenContainer:getAttrib(23)
	self.matcount = OpenContainer:getAttrib(24)
	self.fuelcount = OpenContainer:getAttrib(25)
	self.retcount = OpenContainer:getAttrib(26)
	self.view:UpdateType(self.model.blockid, self.paneltype, self.fuelcount, self.matcount, self.retcount)
	self:UpdateFuelState()

	local eventdispatcher = GetInst("MiniUIEventDispatcher")
	local dragctrl = GetInst("MiniUIManager"):GetDragCtrl()
	if dragctrl then
		dragctrl:RegisterDragEndListener("furnacemainCtrl:OnDragEnd", function(endpos, start_index, start_count)
			return self:OnDragEnd(endpos, start_index, start_count)
		end, function()
			self.view:UpdateAllItems()
		end)

		for i, fuelitem in ipairs(self.view.widgets.fuelitems) do
			eventdispatcher:addEventListener(fuelitem.bg, UIEventType_TouchBegin, function(obj, context)
				dragctrl:DragStart(FURNACE_START_INDEX + i + 9, obj, context)
			end)
			eventdispatcher:addEventListener(fuelitem.bg, UIEventType_Click, function(obj, context)
				dragctrl:DragEnd(obj, context)
			end)
		end

		for j, matitem in ipairs(self.view.widgets.matitems) do
			eventdispatcher:addEventListener(matitem.bg, UIEventType_TouchBegin, function(obj, context)
				dragctrl:DragStart(FURNACE_START_INDEX + j - 1, obj, context)
			end)
			eventdispatcher:addEventListener(matitem.bg, UIEventType_Click, function(obj, context)
				dragctrl:DragEnd(obj, context)
			end)
		end

		for k, retitem in ipairs(self.view.widgets.retitems) do
			eventdispatcher:addEventListener(retitem.bg, UIEventType_TouchBegin, function(obj, context)
				dragctrl:DragStart(FURNACE_START_INDEX + k + 19, obj, context)
			end)
			eventdispatcher:addEventListener(retitem.bg, UIEventType_Click, function(obj, context)
				dragctrl:DragEnd(obj, context)
			end)
		end
	end

	self.backpack_change_listener = SubscribeGameEvent(nil, "GE_BACKPACK_CHANGE", function (context)
		self:OnBackpackChange(context)
    end)
	self.backpack_attr_listener = SubscribeGameEvent(nil, "GE_BACKPACK_ATTRIB_CHANGE", function (context)
		self:OnBackpackAttribChange(context)
    end)
end

function furnacemainCtrl:OnBackpackAttribChange(context)
	MiniLog("furnacemainCtrl:OnBackpackAttribChange")
	self:UpdateFuelState()
end

-- 背包变化可能会连续变化多次，增加异步避免重复刷新
function furnacemainCtrl:OnBackpackChange(context)
	local paramData = context:GetParamData()
	local grid_index = paramData.grid_index
	if grid_index >= FURNACE_START_INDEX and grid_index <= FURNACE_START_INDEX + 29 then
		if grid_index >= FURNACE_START_INDEX + 10 and grid_index <= FURNACE_START_INDEX + 19 then
			self.view:UpdateFuelItem()
		end
		if grid_index >= FURNACE_START_INDEX and grid_index <= FURNACE_START_INDEX + 9 then
			self.view:UpdateMatItem()
		end
		if grid_index >= FURNACE_START_INDEX + 20 and grid_index <= FURNACE_START_INDEX + 29 then
			self.view:UpdateRetItem()
		end
		self:UpdateFuelState()
	end
end

function furnacemainCtrl:IsOnFire()
	return OpenContainer:getAttrib(20) == 1  
end

function furnacemainCtrl:CanFire()
	return OpenContainer:getAttrib(22) == 1  
end

function furnacemainCtrl:UpdateFuelState()
	self.switching_fire = false
	local state = 0
	if self:IsOnFire() then
		state = 2
	elseif self:CanFire() then
		state = 1
	end
	MiniLog("furnacemainCtrl:UpdateFuelState", state)
	self.view:UpdateFireState(state)
end

function furnacemainCtrl:IsMatItem(furnaceDef)
	if furnaceDef and furnaceDef.Result > 0 and furnaceDef.Heat == 0 then
		return true
	end
end

function furnacemainCtrl:IsFuelItem(furnaceDef)
	if furnaceDef and furnaceDef.Heat > 0 then
		return true
	end
end

-- 燃料0-9 材料10-19 产品 20-29
function furnacemainCtrl:OnDragEnd(endpos, start_index, start_count)
	local handgridindex = MOUSE_PICKITEM_INDEX + 1
	local itemid = ClientBackpack:getGridItem(handgridindex)
	if itemid == 0 then
		MiniLog("furnacemainCtrl:OnDragEnd 无道具")
		return
	end

	if not UIUtils:PosInObj(endpos, self.view.widgets.mainpanel) then
		MiniLog("furnacemainCtrl:OnDragEnd 位置错误")
		return
	end
	
	local furnaceDef = DefMgr:getFurnaceDefByMaterialIDWithType(itemid, false, self.paneltype)
	if not furnaceDef then
		ShowGameTips(GetS(700103))
		return -1
	end

	for j, matitem in ipairs(self.view.widgets.matitems) do
		if UIUtils:PosInObj(endpos, matitem.bg) then
			if not self:IsMatItem(furnaceDef) then
				ShowGameTips(GetS(700101))
				return -1
			end
			return FURNACE_START_INDEX + j - 1
		end
	end

	for i, fuelitem in ipairs(self.view.widgets.fuelitems) do
		if UIUtils:PosInObj(endpos, fuelitem.bg) then
			if not self:IsFuelItem(furnaceDef) then
				ShowGameTips(GetS(700102))
				return -1
			end
			return FURNACE_START_INDEX + i + 9
		end
	end
	
	MiniLog("furnacemainCtrl:OnDragEnd 未找到合适的位置")
	ShowGameTips(GetS(700102))
	return -1
end

--刷新
function furnacemainCtrl:Refresh()
	self.super:Refresh()

	local playermain_ctrl = GetInst("MiniUIManager"):GetCtrl("playermain")
	if playermain_ctrl then
		playermain_ctrl:AddOpenContainerTask("furnacemainAutoGen", true, 
			{x = self.ge.body.opencontainer.posx * 100, y = self.ge.body.opencontainer.posy * 100, z = self.ge.body.opencontainer.posz * 100}
		)
	end
end

--隐藏
function furnacemainCtrl:Reset()
	self.super:Reset()

	if CurMainPlayer then
		CurMainPlayer:closeContainer()
	end

	local playermain_ctrl = GetInst("MiniUIManager"):GetCtrl("playermain")
	if playermain_ctrl then
		playermain_ctrl:HidePackOnly()
	end
end

--关闭
function furnacemainCtrl:Remove()
	self.super:Reset()

	local dragctrl = GetInst("MiniUIManager"):GetDragCtrl()
	if dragctrl then
		dragctrl:UnRegisterDragEndListener("furnacemainCtrl:OnDragEnd")
	end

	if CurMainPlayer then
		CurMainPlayer:closeContainer()
	end

	local playermain_ctrl = GetInst("MiniUIManager"):GetCtrl("playermain")
	if playermain_ctrl then
		playermain_ctrl:HidePackOnly()
	end

	UnsubscribeGameEvent(nil, "GE_BACKPACK_CHANGE", self.backpack_change_listener)
	UnsubscribeGameEvent(nil, "GE_BACKPACK_ATTRIB_CHANGE", self.backpack_attr_listener)
end

--消息处理
function furnacemainCtrl:FGUIHandleEvent(eventName)

end

function furnacemainCtrl:btnfireClick(obj, context)
	if self:IsOnFire() then
		if not self.switching_fire then
			-- 使用道具移动来触发点火，没有做专门的协议
			CurMainPlayer:moveItem(FURNACE_START_INDEX + 30, FURNACE_START_INDEX + 31, 1)
			self.switching_fire = true
		end
	elseif not self:CanFire() then
		ShowGameTips(GetS(700105))
	else
		if not self.switching_fire then
			-- 使用道具移动来触发点火，没有做专门的协议
			CurMainPlayer:moveItem(FURNACE_START_INDEX + 31, FURNACE_START_INDEX + 30, 1)
			self.switching_fire = true
		end
	end
end

