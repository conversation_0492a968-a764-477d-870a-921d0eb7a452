﻿id,分类,名称,<PERSON>指令,说明,参数1,参数2,参数3,参数4,
ID,type,name,command,desc,arg1,arg2,arg3,arg4,
1000,基础,物品,ITEM,获取物品，itemdef表id,11001 1,,,,
1001,基础,npc,MOB,获取npc，monster表,6050101,,,,
1002,基础,传送,TP,"1个参数是传送地图,3个是本地图坐标",0 80 0,,,,
1003,基础,血量,HP,血量,100,,,,
1004,基础,科技模型位置,TreeModelViewPos,模型位置（默认0/50/0）,0 50 0,,,,
1005,基础,手模位置,cam pos,第一人称手部模型位置,0 0 0,,,,
1006,基础,手模大小,cam scale,没用,0.5,,,,
1007,基础,手和枪绑定,cam bind,1表示绑定，0表示解绑  默认是绑定的，解绑后可以观察手部模型,1,,,,
1008,基础,手模角度,cam yaw,设置手部模型的水平角度垂直角度,10 20,,,,
1009,基础,穿墙,wall,开启后面朝方块穿墙，1开启0关闭,1,,,,
1010,基础,GOGOGO,GOGOGO,快速传送+获得道具,,,,,soc-res\Script\miniui\module\GMU
1011,基础,FPS缩放,itemscale fps,设置当前道具第一人称缩放,1,,,,
1012,基础,TPS缩放,itemscale tps,设置当前道具第三人称缩放,1,,,,
1013,基础,手中道具位置旋转FPS,handitemdef fpsr,设置当前手持道具的第一人称位置、旋转,0|0|0|0|0|0,,,,
1014,基础,手中道具位置旋转TPS,handitemdef tpsr,设置当前手持道具的第三人称位置、旋转,0|0|0|-90|0|-90,,,,
1015,基础,手位置,handitemdef hand,设置当前手第一人称位置,0|0|0,,,,
101501,基础,建材礼包,Building,布*100；木头*3000；石头*3000；铁*3000；高金*1000,,,,,
101502,基础,进阶礼包,Advance,晶体*5000；木头*2000；石头*2000；铁*2000；高金*1000；金属矿石*1000；硫磺矿石*1000；布*1000；针线包*20；金属管*100；金属弹簧*20；半自动枪体*20；火药*2000；炸药*100,,,,,
101503,基础,防守方物资,Def,AK+防爆套,,,,,
101504,基础,进攻方物资,Atk,火箭筒+路标套,,,,,
101505,基础,进攻方炮楼,Atkbuilding,,,,,,
101506,基础,完成任务,task t,完成任务ID,1001001,,,,
1016,场景,噪点,terrgen,"噪点类型[0,2]  噪点频数  噪点基础频率  地形最低高度[0,255]  地形最高高度[0,255] 地形扭曲",,,,,
1017,场景,去除方块,dispore,去除非矿石方块,,,,,
1018,场景,去除附近,clrblock,表示周围100格 人站立以下的方块删除,100,,,,
1019,场景,游戏时间,time,设置时间（小时）,13,,,,
1020,场景,植物更新,btick,调整植物更新速度（倍）,10,,,,
1021,场景,切换下雨,rain,切换是否下雨,,,,,
1022,场景,沙滩暴风雨,tempest,参数：强度 持续时间,1,10,,,
1023,场景,沙尘暴,duststorm,参数：强度 持续时间,1,10,,,
1024,场景,暴雪,blizzard,参数：强度 持续时间,1,10,,,
1025,场景,极光,aurora,参数：是否开启 持续时间,1,10,,,
1026,场景,关闭遗迹保护,CLOSEBUILDPROTECT,,,,,,
1027,场景,开启遗迹保护,OPENBUILDPROTECT,,,,,,
1028,场景,加载蓝图,loadbp,原始蓝图,4001_lubianfeixu,,,,
1029,场景,生成蓝图,genbuild cityBuild,模拟生成后蓝图,3001_chaoshi,,,,
1030,场景,生成箱子,chest,模拟生成后箱子,2320,,,,
1031,场景,地图传送,maptp,开启后，点击大地图直接传送,,,,,
1032,场景,空投,airdrop,召唤玩家空投,110000,,,,
1033,战斗,怪物,mob,召唤怪物，怪物id,999999,,,,
1034,战斗,NPC受击盒子,mobcollide,修改受击盒子,52101,"[{""pos"":[-5,0,-5],""dim"":[10,3,10],""part"":""downbody""},{""pos"":[-5,3,-5],""dim"":[10,3,10],""part"":""upbody""},{""pos"":[-5,6,-5],""dim"":[4,4,4],""part"":""head""}]",,,
1035,战斗,清除怪物,clearmob,清除所有怪物,,,,,
1036,战斗,播放动作,anim,动作id 是否第一人称,101 1,,,,
1037,战斗,获得buff,buff,buffid,101,,,,
1038,战斗,碰撞盒,enableboxdebug,显示碰撞核，只能999,1,,,,
1039,战斗,耐久度修改,gm adddur,增加/减少耐久度,-300,,,,
1040,玩法,刷新车,veh,"9380大车,9379飞机,9378小车",9378,,,,
1041,玩法,飞行,gm,飞行,fly 1,,,,
1042,玩法,飞行速度,flyspeed,飞行速度，建议不超过0.5,0.2,,,,
1043,其他,横截面,terrimg,"是否显示横截面[0,1]",,,,,
1044,其他,时间速度,timespeed,"表示设置时间进度为两倍速度, 一倍速度是真实世界一分钟等于游戏世界一小时",2,,,,
1045,其他,科技树模型大小,TreeModelViewScale,模型大小调节（默认不动模型大小填1）,3,,,,
1046,程序,AICHAT,AICHAT,,,,,,
1047,程序,AICHATME,AICHATME,,,,,,
1048,程序,AICHATYOU,AICHATYOU,,,,,,
1049,程序,ReLoadMapUI,ReLoadMapUI,,,,,,
1050,程序,ReLoadPlayerUI,ReLoadPlayerUI,,,,,,
1051,程序,ReLoadAIChatUI,ReLoadAIChatUI,,,,,,
1052,程序,Techtree,Techtree,,,,,,
1053,程序,LookAT,LookAT,LookAT,LookAT,,,,
1054,程序,AddAvt,AddAvt,AddAvt,AddAvt,,,,
1055,程序,RemoveAvt,RemoveAvt,RemoveAvt,RemoveAvt,,,,
1056,程序,RoundedCorners,RoundedCorners,,,,,,
