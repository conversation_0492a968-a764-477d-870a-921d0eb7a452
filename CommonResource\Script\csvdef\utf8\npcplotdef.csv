剧情ID,备注,剧情名称,剧情图标[生物图标：mob_ID，道具图标：item_ID],触发类型[1=生物，2=方块],触发目标ID,"前置条件[0=无，1=前置任务，2=时间，3=拥有道具]，例子：[{""Type"":0},{""Type"":1,""TaskID1"":1,""TaskID2"":2,""TaskID3"":3},{""Type"":2,""StartTime"":8,""EndTime"":10},{""Type"":3,""ItemID"":200,""Num"":10}]","内容[FuncType，0=继续对话，1=跳转对话，2=终止对话，3=接受任务，5=执行脚本]，例子：[""FuncType"": 3,""Val"": 6]，[""FuncType"": 1,""Val"": 1]",插件编辑类型,插件编辑模板[0=不是模板，1=作为模板，2=功能直接生效],额外标记，用于针对同一个目标，拥有两套不同对话的情况
PlotID,,PlotName,PlotIcon,InteractType,InteractID,Condition,Content,EditType,IsTemplate,ExtraType
1,跟牛聊聊-插件对话,@10801,mob_3401,,,,"[
    {
        ""ID"": 1,
        ""Text"": ""@10802"",
        ""Answer"": [
            {
                ""Text"": ""@10803"",
                ""FuncType"": 3,
                ""Val"": 1
            }
        ]
    }
]",1,1,
2,狩猎鸡-插件对话,@10810,mob_3400,,,,"[
    {
        ""ID"": 1,
        ""Text"": ""@10811"",
        ""Answer"": [
            {
                ""Text"": ""@10812"",
                ""FuncType"": 3,
                ""Val"": 2
            },
            {
                ""Text"": ""@10813"",
                ""FuncType"": 2
            }
        ]
    }
]",1,1,
3,收集樱桃木-插件对话,@10820,item_200,,,,"[
    {
        ""ID"": 1,
        ""Text"": ""@10821"",
        ""Answer"": [
            {
                ""Text"": ""@10822"",
                ""FuncType"": 3,
                ""Val"": 3
            },
            {
                ""Text"": ""@10823"",
                ""FuncType"": 2
            }
        ]
    }
]",1,1,
4,复活雕像,@85030,,2,596,,"[
   {
    ""ID"": 0,
    ""Text"": ""@85031"",
    ""Answer"": [
     {
      ""FuncType"": 5,
      ""Text"": ""@85036"",
      ""ScriptName"": ""ResetRevivePoint""
     },
     {
      ""FuncType"": 2,
      ""Text"": ""@85037"",
      ""Val"": 1
     }
    ]
   },
   {
    ""ID"": 1,
    ""Text"": ""@85038"",
    ""Answer"": [
     {
      ""FuncType"": 0,
      ""Text"": ""@85039""
     }
    ]
   }
]",,2,
5,复活雕像,@85030,,2,732,,"[
   {
    ""ID"": 0,
    ""Text"": ""@85032"",
    ""Answer"": [
     {
      ""FuncType"": 5,
      ""Text"": ""@85036"",
      ""ScriptName"": ""ResetRevivePoint""
     },
     {
      ""FuncType"": 2,
      ""Text"": ""@85037"",
      ""Val"": 1
     }
    ]
   },
   {
    ""ID"": 1,
    ""Text"": ""@85038"",
    ""Answer"": [
     {
      ""FuncType"": 0,
      ""Text"": ""@85039""
     }
    ]
   }
]",,2,
6,复活雕像,@85030,,2,742,,"[
   {
    ""ID"": 0,
    ""Text"": ""@85033"",
    ""Answer"": [
     {
      ""FuncType"": 5,
      ""Text"": ""@85036"",
      ""ScriptName"": ""ResetRevivePoint""
     },
     {
      ""FuncType"": 2,
      ""Text"": ""@85037"",
      ""Val"": 1
     }
    ]
   },
   {
    ""ID"": 1,
    ""Text"": ""@85038"",
    ""Answer"": [
     {
      ""FuncType"": 0,
      ""Text"": ""@85039""
     }
    ]
   }
]",,2,
7,复活雕像,@85030,,2,746,,"[
   {
    ""ID"": 0,
    ""Text"": ""@85034"",
    ""Answer"": [
     {
      ""FuncType"": 5,
      ""Text"": ""@85036"",
      ""ScriptName"": ""ResetRevivePoint""
     },
     {
      ""FuncType"": 2,
      ""Text"": ""@85037"",
      ""Val"": 1
     }
    ]
   },
   {
    ""ID"": 1,
    ""Text"": ""@85038"",
    ""Answer"": [
     {
      ""FuncType"": 0,
      ""Text"": ""@85039""
     }
    ]
   }
]",,2,
8,复活雕像,@85030,,2,1058,,"[
   {
    ""ID"": 0,
    ""Text"": ""@85035"",
    ""Answer"": [
     {
      ""FuncType"": 5,
      ""Text"": ""@85036"",
      ""ScriptName"": ""ResetRevivePoint""
     },
     {
      ""FuncType"": 2,
      ""Text"": ""@85037"",
      ""Val"": 1
     }
    ]
   },
   {
    ""ID"": 1,
    ""Text"": ""@85038"",
    ""Answer"": [
     {
      ""FuncType"": 0,
      ""Text"": ""@85039""
     }
    ]
   }
]",,2,
9,星站控制台,@85010,,2,594,,"[
   {
    ""ID"": 0,
    ""Text"": ""@85011"" ,
    ""Answer"": [
     {
      ""FuncType"": 1,
      ""Text"": ""@85012"",
      ""Val"": 1 
     },
     {
      ""FuncType"": 1,
      ""Text"": ""@85013"",
      ""Val"": 3
     },
     {
      ""FuncType"": 5,
      ""Text"": ""@85014"",
      ""ScriptName"": ""openStarDescView""
     },
     {
      ""FuncType"": 2,
      ""Text"": ""@85003""
     }
    ]
   },
   {
    ""ID"": 1,
    ""Text"": ""@85015"" ,
    ""Answer"": [
     {
      ""FuncType"": 5,
      ""ScriptName"": ""switchStarstationSignPoint"",
      ""Text"": ""@85020""
     },
     {
      ""FuncType"": 5,
      ""ScriptName"": ""starStationSendChatMsg"",
      ""Text"": ""@85026""
     },
    {
      ""FuncType"": 1,
      ""Text"": ""@85021"",
      ""Val"": 0 
     }
    ]
   },
   {
    ""ID"": 2,
    ""Text"": ""@85022"" ,
    ""Answer"": [
     {
      ""FuncType"": 1,
      ""Text"": ""@85021"",
      ""Val"": 0 
     },
     {
      ""FuncType"": 2,
      ""Text"": ""@85003""
     }
    ]
   },
   {
    ""ID"": 3,
    ""Text"": ""@85023"" ,
    ""Answer"": [
     {
      ""FuncType"": 5,
      ""Text"": ""@85024"",
      ""ScriptName"": ""ResetRevivePoint""
     },
     {
      ""FuncType"": 1,
      ""Text"": ""@85021"",
      ""Val"": 0 
     }
    ]
   },
   {
    ""ID"": 4,
    ""Text"": ""@85025"" ,
    ""Answer"": [
     {
      ""FuncType"": 1,
      ""Text"": ""@85021"",
      ""Val"": 0 
     },
     {
      ""FuncType"": 2,
      ""Text"": ""@85003""
     }
    ]
   } 
  ]",,2,1
10,农场商人,,,1,39206,,"[
   {
    ""ID"": 0,
    ""Text"": ""@43001"" ,
    ""Answer"": [
     {
      ""FuncType"": 5,
      ""Text"": ""@43002"",
      ""ScriptName"": ""openFarmStore""
     },
     {
      ""FuncType"": 5,
      ""Text"": ""@43004"",
      ""ScriptName"": ""openExpandFarm""
     }
    ]
   }
  ]",,2,
11,牧场商人,,,1,39218,," [
   {
    ""ID"": 0,
    ""Text"": ""@43010"" ,
    ""Answer"": [
     {
      ""FuncType"": 5,
      ""Text"": ""@43011"",
      ""ScriptName"": ""openRanchStore""
     },
     {
      ""FuncType"": 5,
      ""Text"": ""@43013"",
      ""ScriptName"": ""openExpandRanch""
     }
    ]
   }
  ]",,2,
12,家园厨师,,,1,39220,,"   [
   {
    ""ID"": 0,
    ""Text"": ""@43019"" ,
    ""Answer"": [
     {
      ""FuncType"": 5,
      ""Text"": ""@43020"",
      ""ScriptName"": ""openCookStore""
     }
    ]
   }
  ]",,2,
13,家园工匠,,,1,39219,," [
   {
    ""ID"": 0,
    ""Text"": ""@43027"" ,
    ""Answer"": [
     {
      ""FuncType"": 5,
      ""Text"": ""@43028"",
      ""ScriptName"": ""openMaterialStore""
     },
     {
      ""FuncType"": 5,
      ""Text"": ""@43029"",
      ""ScriptName"": ""openDismantle""
     }
    ]
   }
  ]",,2,
14,星站控制台,@85010,,2,594,,"[
   {
    ""ID"": 0,
    ""Text"": ""@85011"" ,
    ""Answer"": [
     {
      ""FuncType"": 1,
      ""Text"": ""@85012"",
      ""Val"": 1 
     },
     {
      ""FuncType"": 1,
      ""Text"": ""@85013"",
      ""Val"": 3
     },
     {
      ""FuncType"": 5,
      ""Text"": ""@85014"",
      ""ScriptName"": ""openStarDescView""
     },
     {
      ""FuncType"": 2,
      ""Text"": ""@85003""
     }
    ]
   },
   {
    ""ID"": 1,
    ""Text"": ""@85015"" ,
    ""Answer"": [
     {
      ""FuncType"": 5,
      ""ScriptName"": ""openSpaceTravelSetupPanel"",
      ""Text"": ""@141014""
     },
  {
      ""FuncType"": 5,
      ""ScriptName"": ""switchStarstationSignPoint"",
      ""Text"": ""@85020""
     },
     {
      ""FuncType"": 5,
      ""ScriptName"": ""starStationSendChatMsg"",
      ""Text"": ""@85026""
     },
    {
      ""FuncType"": 1,
      ""Text"": ""@85021"",
      ""Val"": 0 
     }
    ]
   },
   {
    ""ID"": 2,
    ""Text"": ""@85022"" ,
    ""Answer"": [
     {
      ""FuncType"": 1,
      ""Text"": ""@85021"",
      ""Val"": 0 
     },
     {
      ""FuncType"": 2,
      ""Text"": ""@85003""
     }
    ]
   },
   {
    ""ID"": 3,
    ""Text"": ""@85023"" ,
    ""Answer"": [
     {
      ""FuncType"": 5,
      ""Text"": ""@85024"",
      ""ScriptName"": ""ResetRevivePoint""
     },
     {
      ""FuncType"": 1,
      ""Text"": ""@85021"",
      ""Val"": 0 
     }
    ]
   },
   {
    ""ID"": 4,
    ""Text"": ""@85025"" ,
    ""Answer"": [
     {
      ""FuncType"": 1,
      ""Text"": ""@85021"",
      ""Val"": 0 
     },
     {
      ""FuncType"": 2,
      ""Text"": ""@85003""
     }
    ]
   } 
  ]",,2,2
4000,,,mob_3400,,,,"[
    {
        ""ID"": 1,
        ""Text"": """",
        ""Answer"": [
            {
                ""Text"": """",
                ""FuncType"": 0
            },
            {
                ""Text"": """",
                ""FuncType"": 0
            },
            {
                ""Text"": """",
                ""FuncType"": 0
            },
            {
                ""Text"": """",
                ""FuncType"": 0
            }
        ]
    }
]",,,
