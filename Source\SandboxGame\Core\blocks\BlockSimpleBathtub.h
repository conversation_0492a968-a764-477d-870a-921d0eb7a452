
#ifndef __BLOCK_SIMPLE_BATHTUB_H__
#define __BLOCK_SIMPLE_BATHTUB_H__

#include "BlockMaterial.h"

class BlockSimpleBathtub : public ModelBlockMaterial //tolua_exports
{ //tolua_exports
	DECLARE_BLOCKMATERIAL(BlockSimpleBathtub)
public:
	//tolua_begin
	//virtual const char *getGeomName() override;
	virtual bool onTrigger(World *pworld, const WCoord &blockpos, DirectionType face, IClientPlayer *player, const Rainbow::Vector3f &colpoint=Rainbow::Vector3f(0, 0, 0));
	virtual void onNotify(World *pworld, const WCoord &blockpos, int blockid);
	virtual int getProtoBlockGeomID(int *idbuf, int *dirbuf) override;
	virtual void dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata = 0, BLOCK_MINE_TYPE droptype = BLOCK_MINE_NOTOOL, float chance = 1.0f, int uin = -1);
	virtual void createCollideData(CollisionDetect *coldetect, World *pworld, const WCoord &blockpos);
	virtual int convertDataByRotate(int blockdata, int rotatetype) override;
	virtual SectionMesh *createBlockProtoMesh(int protodata = 0);

// 	virtual bool hasContainer();
	//tolua_end
private:
	virtual int getBlockGeomID(int *idbuf, int *dirbuf, const SectionDataHandler* sectionData, const WCoord &blockpos, World* world);
	virtual void initGeomName() override;
}; //tolua_exports

//长2格宽1格高1格
//画
class BlockPicture : public BlockSimpleBathtub //tolua_exports
{ //tolua_exports
	DECLARE_BLOCKMATERIAL(BlockPicture)
public:
	//tolua_begin
	virtual void createCollideData(CollisionDetect *coldetect, World *pworld, const WCoord &blockpos);
	//tolua_end
}; //tolua_exports

// 长2格宽1格高2格（正方形）
// 画
class BlockFourSizeSquarePicture : public ModelBlockMaterial //tolua_exports
{ //tolua_exports
	DECLARE_BLOCKMATERIAL(BlockFourSizeSquarePicture)
public:
	//tolua_begin

	virtual void createBlockMesh(const BuildSectionMeshData& data, const WCoord &blockpos, SectionMesh *poutmesh) override;
	virtual void onNotify(World *pworld, const WCoord &blockpos, int blockid);
	virtual void createCollideData(CollisionDetect *coldetect, World *pworld, const WCoord &blockpos);
	//tolua_end
}; //tolua_exports

// 长1格宽1格高1格 双面显示
// 画
class BlockSimplePicture : public ModelBlockMaterial //tolua_exports
{ //tolua_exports
	DECLARE_BLOCKMATERIAL(BlockSimplePicture)
public:
	//tolua_begin
	virtual BlockDrawType getDrawType()
	{
		return BLOCKDRAW_ALPHTEST;
	}

	//tolua_end
}; //tolua_exports
#endif