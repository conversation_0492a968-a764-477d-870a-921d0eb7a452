
#ifndef __BLOCKHYDARM_MOVING_H__
#define __BLOCKHYDARM_MOVING_H__

#include "BlockHydarmBase.h"

class WorldContainer;

class BlockHydarmMoving : public BlockMaterial //tolua_exports
{ //tolua_exports
	DECLARE_BLOCKMATERIAL(BlockHydarmMoving)
public:
	//tolua_begin
	virtual void onBlockRemoved(World *pworld, const WCoord &blockpos, int blockid, int blockdata);
	virtual bool canPutOntoPos(WorldProxy *pworld, const WCoord &blockpos)
	{
		return false;
	}
	virtual bool canPutOntoFace(WorldProxy *pworld, const WCoord &blockpos, int face)
	{
		return false;
	}
	virtual bool onTrigger(World *pworld, const WCoord &blockpos, DirectionType face, IClientPlayer *player, const Rainbow::Vector3f &colpoint=Rainbow::Vector3f(0, 0, 0));
	virtual void onNotify(World *pworld, const WCoord &blockpos, int blockid);
	virtual void dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata/* =0 */, BLOCK_MINE_TYPE droptype/* =BLOCK_MINE_NOTOOL */, float chance/* =1.0f */, int uin = -1);
	virtual bool canAttachedToMecha(int curblockdata, DirectionType dir, int neighbor_id, int neighbor_data) override
	{
		return false;
	}

	bool getAABB(CollideAABB &box, World *pworld, const WCoord &blockpos, int blockid, float t, int dir);
	//tolua_end
}; //tolua_exports

#endif