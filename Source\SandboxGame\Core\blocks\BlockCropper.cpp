
#include "BlockCropper.h"
#include "BlockMaterialMgr.h"
#include "section.h"
#include "SectionMesh.h"
#include "chunk.h"
#include "BlockGeom.h"
#include "world.h"
#include "special_blockid.h"
#include "DefManagerProxy.h"
#include "Collision.h"
#include "Ecosystem.h"
#include "ActorLocoMotion.h"
#include "ClientMob.h"
#include "TaskData.h"

IMPLEMENT_BLOCKMATERIAL(CropperMaterial)
IMPLEMENT_BLOCKMATERIAL(BlockRice)

using namespace MINIW;

CropperMaterial::CropperMaterial(): m_MaxStages(0)
{
	for (int i = 0; i < MAX_STAGES; i++)
	{
		m_Mtls[i] = NULL;
	}
}

CropperMaterial::~CropperMaterial()
{
	for(int i=0; i<MAX_STAGES; i++)
	{
		ENG_RELEASE(m_Mtls[i]);
	}
}

void CropperMaterial::init(int resid)
{
	HerbMaterial::init(resid);
	SetToggle(BlockToggle_RandomTick, true);

	if(m_LoadOnlyLogic) return;

	m_MaxStages = 0;
	for(int i=0; i<MAX_STAGES; i++)
	{
		char texname[256];
		sprintf(texname, "%s_s%d", GetBlockDef()->Texture1.c_str(), i);
		m_Mtls[i] = g_BlockMtlMgr.createRenderMaterial(texname, GetBlockDef(), GETTEX_NORMAL, BLOCKDRAW_GRASS);
		if(m_Mtls[i] == NULL) break;

		if (g_BlockMtlMgr.IsUseVertexAnimationEffect(resid))
		{
			AppendBlockEffect(BLOCKEFFECT_VERTEX_ANIMATION);
			if (m_Mtls[i])
				m_Mtls[i]->SetBlockEffectFlag(BLOCKEFFECT_VERTEX_ANIMATION);
		}

		m_MaxStages++;
	}
}

void CropperMaterial::initGeomName()
{
	m_geomName = "wheat";
}
BlockTexElement *CropperMaterial::getDestroyTexture(Block pblock, BlockTexDesc &desc)
{
	desc.gray = false;
	desc.blendmode = BLEND_ALPHATEST;

	return m_Mtls[m_MaxStages-1]->getTexElement();
}

int CropperMaterial::getStage(int blockdata)
{
	float s = float(m_MaxStages)*(blockdata+1)/(getMaxGrowStage()+1);
	int stage = int(s+0.5f) - 1;
	
	if(stage >= m_MaxStages) stage = m_MaxStages-1;
	if(blockdata<getMaxGrowStage() && stage==m_MaxStages-1) stage = m_MaxStages-2;

	if(stage < 0) stage = 0;

	return stage;
}

void CropperMaterial::createBlockMesh(const BuildSectionMeshData& data, const WCoord &blockpos, SectionMesh *poutmesh)
{
	BlockGeomTemplate* geom = getGeom(data.m_LODLevel);

	if (!geom)
		return;
	auto psection = data.m_SharedSectionData;
auto downBlockID = psection->getNeighborBlock(blockpos, DIR_NEG_Y).getResID();
	Block pblock = psection->getBlock(blockpos);
	Rainbow::ColorRGBAf verts_light[1] = { Rainbow::ColorRGBAf::black };
	psection->getBlockVertexLight(blockpos, verts_light);

	ChunkRandGen chunkrand;
	WCoord wpos = psection->getOrigin() + blockpos;
	WCoordHashCoder coder;
	chunkrand.setSeed(coder(wpos));

	//const BiomeDef *biome = psection->getBiomeGen(blockpos)->getDef();

	BlockGeomMeshInfo meshinfo;
	int stage = getStage(pblock.getData());

	SectionSubMesh *psubmesh = poutmesh->getSubMesh(m_Mtls[stage], false, data.m_LODLevel);

	float max_scale = 1.0f;
	float min_scale = 1.2f;
	float angle = 0;
	float tx = 0;
	float tz = 0;
	float ty = 0;
	float scale =0;

	if (downBlockID != 102 && downBlockID != 150 && downBlockID != 399)
	{
		angle = (chunkrand.getFloat()) * 360;
		tx = (chunkrand.getFloat() - chunkrand.getFloat()) * 0.4f;
		tz = (chunkrand.getFloat() - chunkrand.getFloat()) * 0.4f;
		scale = min_scale + (chunkrand.getFloat()) * (max_scale - min_scale);
	}
	geom->getGrassModelFaceVerts(meshinfo, 0, angle > 7 ? angle : 7, scale, 0, tx, ty, tz);

	//getGeom()->getFaceVerts(meshinfo, 0);
	psubmesh->addGeomBlockLight(meshinfo, &blockpos, verts_light, NULL, m_Mtls[stage]->getUVTile());
}

void CropperMaterial::blockTick(World *pworld, const WCoord &blockpos)
{
	HerbMaterial::blockTick(pworld, blockpos);
	bool cangrow = false;
	const BlockDef* def = GetBlockDef();
	if(pworld->getCurMapID() >= MAPID_MENGYANSTAR)
	{
		if(pworld->getBlockID(TopCoord(blockpos)) == BLOCK_PLANTSPACE_OXYGEN) 
			cangrow = true;
	}
	else if (def&&def->CropsSign > 0 && def->GrowthTimeNum > 0)
	{
		cangrow = true;
	}
	else
	{
		int blockLight = pworld->getBlockLightValue(blockpos);
		if(!isVoidPlant())
			cangrow = (blockLight >= 9);
		else
			cangrow = (blockLight <= 9);
	}

	if(cangrow)
	{
		//红土 不能生成
		if (pworld->getBlockID(DownCoord(blockpos)) == BLOCK_FARMLAND_RED)
		{
			return ;
		}
		int blockdata = pworld->getBlockData(blockpos);
		if(blockdata < 7)
		{
			float canSpeedUpGrow = false;

			float rangexz = 4;
			CollideAABB box;
			box.pos = blockpos * BLOCK_SIZE;
			box.dim = WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE);
			box.expand((int)(rangexz*BLOCK_SIZE), (int)(2*BLOCK_SIZE), (int)(rangexz*BLOCK_SIZE));

			std::vector<IClientActor *>actors;
			pworld->getActorsOfTypeInBox(actors, box, OBJ_TYPE_MONSTER);
			int speedUpMonsterID = 3121;
			WCoord pos = BlockBottomCenter(blockpos);

			for (size_t i = 0; i < actors.size(); i++)
			{
				ClientMob *mob = static_cast<ClientMob *>(actors[i]);
				if (mob->getDef()->ID == speedUpMonsterID)
				{
					WCoord vec = mob->getLocoMotion()->getPosition() - pos;
					float dist = vec.length();
					if (dist < rangexz * BLOCK_SIZE)
					{
						canSpeedUpGrow = true;
						break;
					}
				}
			}

			const BlockDef* def = GetBlockDef();
			if (def->CropsSign == 0|| def->GrowthTimeNum == 0)
			{
				//生物生长
				float growrate = getGrowRate(pworld, blockpos);

				if (canSpeedUpGrow)
				{
					growrate = Rainbow::Min(growrate * 2, 25.0f);
				}

				if (GenRandomInt(0, int(25.0f / growrate)) == 0)
				{
					pworld->setBlockData(blockpos, blockdata + 1, 2);
				}

			}
			else
			{
				dealNewGrow(pworld, blockpos,2);
			}
			if (pworld->getBlockData(blockpos) == 7)
			{
				/*if (TaskSubSystem::GetTaskSubSystem())
				{
					TaskSubSystem::GetTaskSubSystem()->CheckCommonSyncTask(TASKSYS_GROW, blockpos * BLOCK_SIZE, m_BlockResID);
				}*/
				WCoord pos = blockpos * BLOCK_SIZE;
				if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
					MNSandbox::SandboxContext sContext = MNSandbox::SandboxContext(nullptr).
						SetData_Number("type", TASKSYS_GROW).
						SetData_Userdata("WCoord", "trackPos", &pos).
						SetData_Number("target1", m_BlockResID).
						SetData_Number("target2", 0).
						SetData_Number("goalnum", 1);
					MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("TaskSubSystem_CheckCommonSyncTask", sContext);
				}
			}
		}
	}
}
void CropperMaterial::forceResh(World* pworld, const WCoord& blockpos)
{
	if (!pworld || !pworld->onServer())
	{
		return;
	}
	const BlockDef* def = GetBlockDef();
	if (!def || def->CropsSign == 0 || def->GrowthTimeNum == 0)
	{
		return;
	}
	int blockdata = pworld->getBlockData(blockpos);
	if (blockdata < 7)
	{
		float canSpeedUpGrow = false;

		float rangexz = 4;
		CollideAABB box;
		box.pos = blockpos * BLOCK_SIZE;
		box.dim = WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE);
		box.expand((int)(rangexz * BLOCK_SIZE), (int)(2 * BLOCK_SIZE), (int)(rangexz * BLOCK_SIZE));

		std::vector<IClientActor*>actors;
		pworld->getActorsOfTypeInBox(actors, box, OBJ_TYPE_MONSTER);
		int speedUpMonsterID = 3121;
		WCoord pos = BlockBottomCenter(blockpos);

		for (size_t i = 0; i < actors.size(); i++)
		{
			ClientMob* mob = static_cast<ClientMob*>(actors[i]);
			if (mob->getDef()->ID == speedUpMonsterID)
			{
				WCoord vec = mob->getLocoMotion()->getPosition() - pos;
				float dist = vec.length();
				if (dist < rangexz * BLOCK_SIZE)
				{
					canSpeedUpGrow = true;
					break;
				}
			}
		}

		const BlockDef* def = GetBlockDef();
		if (def->CropsSign > 0 && def->GrowthTimeNum > 0)
		{

			dealNewGrow(pworld, blockpos, 2);
		}
		if (pworld->getBlockData(blockpos) == 7)
		{
			/*if (TaskSubSystem::GetTaskSubSystem())
			{
				TaskSubSystem::GetTaskSubSystem()->CheckCommonSyncTask(TASKSYS_GROW, blockpos * BLOCK_SIZE, m_BlockResID);
			}*/
			WCoord pos = blockpos * BLOCK_SIZE;
			if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
				MNSandbox::SandboxContext sContext = MNSandbox::SandboxContext(nullptr).
					SetData_Number("type", TASKSYS_GROW).
					SetData_Userdata("WCoord", "trackPos", &pos).
					SetData_Number("target1", m_BlockResID).
					SetData_Number("target2", 0).
					SetData_Number("goalnum", 1);
				MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("TaskSubSystem_CheckCommonSyncTask", sContext);
			}
		}
	}
}

bool CropperMaterial::canThisPlantGrowOnThisBlockID(int blockid)
{
	return blockid == BLOCK_FARMLAND || blockid == BLOCK_FARMLAND_RED||blockid == BLOCK_GRASS_WOOD_GRAY_FARMLAND;
}

void CropperMaterial::dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata, BLOCK_MINE_TYPE droptype, float chance, int uin/* = -1 */)
{
	if(GenRandomFloat() > chance) return;
	if(getBlockResID() == BLOCK_WHEAT)  //小麦
	{
		// 如果是上部方块， 需要根据下方方块blockdata判断掉落物
		bool isTop = (blockdata & 8) > 0;
		if (isTop)
		{
			WCoord downPos = DownCoord(blockpos);
			if (pworld->getBlockID(downPos) == BLOCK_WHEAT)
			{
				blockdata = pworld->getBlockData(downPos);
			}
		}

		blockdata &= 7;
		if(blockdata >= 7)
		{
			doDropItem(pworld, blockpos, GetBlockDef()->ToolMineDrops[0].item);

			int num = GenRandomInt(1, 2);
			for(int i=0; i<num; i++)
			{
				doDropItem(pworld, blockpos, GetBlockDef()->ToolMineDrops[1].item);
			}
		}
		else doDropItem(pworld, blockpos, GetBlockDef()->ToolMineDrops[1].item);
	}
	else
	{
		doDropItem(pworld, blockpos, GetBlockDef()->ToolMineDrops[1].item);

		if(blockdata >= 7)
		{
			int num = GenRandomInt(0, 2);
			for(int i=0; i<num; i++)
			{
				doDropItem(pworld, blockpos, GetBlockDef()->ToolMineDrops[0].item);
			}
		}
	}
}

bool CropperMaterial::hasDestroyScore(int blockdata)
{
	return blockdata >= 7;
}

bool CropperMaterial::onFertilized(World *pworld, const WCoord &blockpos, int fertiliser)
{
	if(pworld->getCurMapID() >= MAPID_MENGYANSTAR)
	{
		if(pworld->getBlockID(TopCoord(blockpos)) != BLOCK_PLANTSPACE_OXYGEN) return false;
	}
	//红土 不能生成
	if (pworld->getBlockID(DownCoord(blockpos)) == BLOCK_FARMLAND_RED)
	{
		return false;
	}
	int blockdata = pworld->getBlockData(blockpos);
	if (blockdata < 7)
	{
		const BlockDef* def = GetBlockDef();
		if (def && def->CropsSign == 0 || def->GrowthTimeNum == 0)
		{
			FertilizedPlayEffect(pworld, blockpos);
			blockdata += GenRandomInt(2, 4);
			if (blockdata > 7)blockdata = 7;
			if (blockdata == 7)
			{
				/*if (TaskSubSystem::GetTaskSubSystem())
				{
					TaskSubSystem::GetTaskSubSystem()->CheckCommonSyncTask(TASKSYS_GROW, blockpos * BLOCK_SIZE, m_BlockResID);
				}*/
				WCoord pos = blockpos * BLOCK_SIZE;
				if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
					MNSandbox::SandboxContext sContext = MNSandbox::SandboxContext(nullptr).
						SetData_Number("type", TASKSYS_GROW).
						SetData_Userdata("WCoord", "trackPos", &pos).
						SetData_Number("target1", m_BlockResID).
						SetData_Number("target2", 0).
						SetData_Number("goalnum", 1);
					MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("TaskSubSystem_CheckCommonSyncTask", sContext);
				}
			}
			pworld->setBlockData(blockpos, blockdata);
		}
		else
		{
			return dealFertilized(pworld, blockpos, fertiliser);
		}
		
	}

	return true;
}

void CropperMaterial::createCollideData(CollisionDetect *coldetect, World *pworld, const WCoord &blockpos)
{
	WCoord pos = blockpos*BLOCK_SIZE;
	int step = BLOCK_SIZE/8;

	int h = getStage(pworld->getBlockData(blockpos));
	h = (h+1)*BLOCK_SIZE/getMaxGrowStage();

	coldetect->addObstacle(pos+WCoord(step,0,step), pos+WCoord(BLOCK_SIZE-step, h, BLOCK_SIZE-step));
}

void CropperMaterial::onBlockAdded(World *pworld, const WCoord &blockpos)
{
	/*Chunk *pchunk = pworld->getChunk(blockpos);
	WCoord offset = blockpos - pchunk->m_Origin;
	if(pchunk) pchunk->addSearchBlock(offset.x, offset.y, offset.z, getBlockResID()); */
}

void CropperMaterial::onBlockRemoved(World *pworld, const WCoord &blockpos, int blockid, int blockdata)
{
	/*Chunk *pchunk = pworld->getChunk(blockpos);
	WCoord offset = blockpos - pchunk->m_Origin;
	if(pchunk) pchunk->removeSearchBlock(offset.x, offset.y, offset.z, getBlockResID()); */
}


//------------------------------------------------------------------------------------------------------------------------------------------

void BlockRice::initGeomName()
{
	m_geomName = "block_reed";
}

bool BlockRice::canPutOntoPos(WorldProxy *pworld, const WCoord &blockpos)
{
	if(!BlockMaterial::canPutOntoPos(pworld, blockpos)) return false;

	return canStayOnPos(pworld, blockpos);
}

bool BlockRice::canStayOnPos(WorldProxy *pworld, const WCoord &blockpos)
{
	WCoord downpos = DownCoord(blockpos);
	int waterid = pworld->getBlockID(downpos);
	int waterdata = pworld->getBlockData(downpos);
	if(IsWaterBlockID(waterid) && (waterdata==0 || waterdata>=8))
	{
		int id = pworld->getBlockID(DownCoord(downpos));
		if(id==BLOCK_DIRT || id==BLOCK_GRASS || id==BLOCK_FARMLAND || id == BLOCK_FARMLAND_RED || id==BLOCK_SAND || id==BLOCK_REDSOIL || id==BLOCK_GRASS_WOOD_GRAY_FARMLAND || id == BLOCK_SOLIDSAND) return true;
	}

	return false;
}

void BlockRice::onBlockRemoved(World *pworld, const WCoord &blockpos, int blockid, int blockdata)
{
	CropperMaterial::onBlockRemoved(pworld, blockpos, blockid, blockdata);
}
