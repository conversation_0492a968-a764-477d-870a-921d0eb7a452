
#ifndef __BLOCKSCREEN_H__
#define __BLOCKSCREEN_H__

#include "BlockMaterial.h"

class BlockScreen : public ModelBlockMaterial //tolua_exports
{ //tolua_exports
	DECLARE_BLOCKMATERIAL(BlockScreen)
public:
	//tolua_begin
	BlockScreen();
	virtual ~BlockScreen();

	virtual void init(int resid);
	//virtual const char *getGeomName();
	virtual const char *getBaseTexName(char *texname, const BlockDef *def, int &gettextype);

	virtual void createBlockMesh(const BuildSectionMeshData& data, const WCoord &blockpos, SectionMesh *poutmesh) override;
	virtual SectionMesh *createBlockProtoMesh(int protodata = 0);
	virtual void createCollideData(CollisionDetect *coldetect, World *pworld, const WCoord &blockpos);

	virtual bool canPutOntoPos(WorldProxy *pworld, const WCoord &blockpos);
	//virtual bool onTrigger(World *pworld, const WCoord &blockpos, DirectionType face, IClientPlayer *player, const Rainbow::Vector3f &colpoint=Rainbow::Vector3f(0, 0, 0));
	virtual void onNotify(World *pworld, const WCoord &blockpos, int blockid);
	virtual void dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata = 0, BLOCK_MINE_TYPE droptype = BLOCK_MINE_NOTOOL, float chance = 1.0f, int uin = -1);
	bool canBlocksMovement(World *pworld, const WCoord &blockpos);
	static bool isOpen(int blockdata);
	//virtual BlockDrawType getDrawType() override
	//{
	//	return BLOCKDRAW_XPARENT;
	//}
	//virtual bool baseTexNeed4BitAlpha() override
	//{
	//	return true;
	//}

	virtual int convertDataByRotate(int blockdata, int rotatetype) override;
	virtual void ignoreCheckUpBlockWhenNotify(bool ignore)
	{
		m_ignoreCheckUpBlock = ignore;
	}
	//tolua_end
private:
	//void onPoweredBlockChange(World *pworld, const WCoord &blockpos, bool open);
	virtual int ParseDoorData(const SectionDataHandler* sectionData, const WCoord &blockpos, bool &isupper, bool &isopen, bool &mirror);
	int ParseDoorData(World *pworld, const WCoord &blockpos, bool &isupper, bool &isopen, bool &mirror);
	virtual int ParseDoorDataInVehicle(VehicleWorld* pworld, const WCoord &blockpos, bool &isupper, bool &isopen, bool &mirror);
	virtual void initGeomName() override;
	virtual void initBaseTexNeed4BitAlpha() override { m_baseTexNeed4BitAlpha = true; }
	virtual void initDrawType() override;
protected:
	//RenderBlockMaterial *m_UpperMtl;
	//RenderBlockMaterial *m_UpperWoodMtl;
	//RenderBlockMaterial *m_LowerWoodMtl;
	unsigned int m_upperMtlIndex;
	unsigned int m_upperWoodMtlIndex;
	unsigned int m_lowerMtlIndex;

	bool m_ignoreCheckUpBlock;
}; //tolua_exports


class BlockInkScreen : public ModelBlockMaterial
{
	DECLARE_BLOCKMATERIAL(BlockInkScreen)
public:
	virtual void createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh) override;
	virtual SectionMesh* createBlockProtoMesh(int protodata = 0) override;
	virtual bool canPutOntoPos(WorldProxy* pworld, const WCoord& blockpos)override;
	virtual void onNotify(World* pworld, const WCoord& blockpos, int blockid) override;
	virtual void createCollideData(CollisionDetect* coldetect, World* pworld, const WCoord& blockpos);
};

#endif
