﻿
#ifndef __BLOCKTORCH_H__
#define __BLOCKTORCH_H__

#include "BlockMaterial.h"

class TorchMaterial : public ModelBlockMaterial //tolua_exports
{ //tolua_exports
	DECLARE_BLOCKMATERIAL(TorchMaterial)
	//DECLARE_BLOCKINSTANCE(TorchMaterial)
public:
	//tolua_begin
	TorchMaterial(){}
	virtual ~TorchMaterial(){}

	virtual bool hasContainer() override { return true; }
	virtual WorldContainer* createContainer(World* pworld, const WCoord& blockpos) override;

	//virtual const char *getGeomName()
	//{
	//	return "torch";
	//}

	virtual void createCollideData(CollisionDetect *coldetect, World *pworld, const WCoord &blockpos);
	virtual int getPlaceBlockData(World *pworld, const WCoord &blockpos, DirectionType face, float hitptx, float hitpty, float hitptz, int def_blockdata);
	virtual bool canPutOntoPos(WorldProxy *pworld, const WCoord &blockpos);
	virtual void onNotify(World *pworld, const WCoord &blockpos, int blockid);
	virtual void onPlayRandEffect(World *pworld, const WCoord &blockpos);
	virtual void onPlayEffect(World* pworld, const WCoord& blockpos)override;
	virtual void onStopEffect(World* pworld, const WCoord& blockpos)override;
	virtual bool canAttachedToMecha(int curblockdata, DirectionType dir, int neighbor_id, int neighbor_data) override
	{
		return (curblockdata & 7) == dir;
	}
	virtual void onBlockRemoved(World *pworld, const WCoord &blockpos, int blockid, int blockdata);
	virtual void onBlockAdded(World *pworld, const WCoord &blockpos);
	virtual void onBlockPlacedBy(World *pworld, const WCoord &blockpos, IClientPlayer *player);
	virtual int convertDataByRotate(int blockdata, int rotatetype);
	virtual void dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata = 0, BLOCK_MINE_TYPE droptype = BLOCK_MINE_NOTOOL, float chance = 1.0f, int uin = -1) override;
	//tolua_end
protected:
	bool checkDrop(World *pworld, const WCoord &blockpos);
	bool canPlaceTorchOn(WorldProxy *pworld, const WCoord &blockpos);
	virtual int getBlockGeomID(int *idbuf, int *dirbuf, const SectionDataHandler* sectionData, const WCoord &blockpos, World* world);
	virtual int getProtoBlockGeomID(int *idbuf, int *dirbuf);
	int getPlayerCurToolDurability(IClientPlayer* player); // 获取玩家当前工具耐久度
private:
	virtual void initGeomName() override;
}; //tolua_exports

#endif