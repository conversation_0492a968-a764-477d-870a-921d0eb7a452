--声明
local CoordAxisFrameView = Class("CoordAxisFrameView",ClassList["UIBaseView"])

--创建
function CoordAxisFrameView:Create(param)
	return ClassList["CoordAxisFrameView"].new(param)
end

--初始化
function CoordAxisFrameView:Init()
    self.coordFrame = getglobal("CoordAxisFrame") 
    self.modelView = getglobal("CoordAxisFrameModelView")
end

function CoordAxisFrameView:Refresh()
    self.modelView:AttachCoordAxis()
    self.modelView:setCoordAxisScale(1.0, 1.0, 1.0)
    self.modelView:setCoordAxisRotation(0, 0, 0, 1)
end

function CoordAxisFrameView:Reset()
    self.modelView:DetachCoordAxis()
end