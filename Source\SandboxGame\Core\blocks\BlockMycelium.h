
#ifndef __BLOCKMYCELIUM_H__
#define __BLOCKMYCELIUM_H__

#include "BlockGrayHerbs.h"

class BlockMycelium : public HerbMaterial //tolua_exports  //这个菌丝类需要设置种子，不能作为插件，和策划商量以后需要区分不同的菌丝时再分开写
{ //tolua_exports										  //blockdata 高两位表示蘑菇种类，低两位记录成长数据 
	//typedef HerbMaterial Super;
	DECLARE_BLOCKMATERIAL(BlockMycelium)
	//DECLARE_BLOCKINSTANCE(BlockMycelium)
public:
	//tolua_begin
	BlockMycelium();
	~BlockMycelium();

	//virtual const char *getGeomName();
	virtual void init(int resid) override;
	virtual void createBlockMesh(const BuildSectionMeshData& data, const WCoord &blockpos, SectionMesh *poutmesh) override;
	virtual BlockTexElement *getDestroyTexture(Block pblock, BlockTexDesc &desc) override;
	virtual int getPlaceBlockData(World *pworld, const WCoord &blockpos, DirectionType face, float hitptx, float hitpty, float hitptz, int def_blockdata);
	virtual bool onFertilized(World *pworld, const WCoord &blockpos, int fertiliser);
	virtual bool canPutOntoPos(WorldProxy *pworld, const WCoord &blockpos);
	virtual bool canStayOnPos(WorldProxy *pworld, const WCoord &blockpos);
	virtual void blockTick(World *pworld, const WCoord &blockpos);
	int getIdByBlockData(int pBlockData);
	int getBlockDataById(int pId, int pOldData);
	//tolua_end
protected:
	virtual bool canThisPlantGrowOnThisBlockID(int blockid);
	virtual void initGeomName() override;
protected:
	RenderBlockMaterial *m_MtlMycelium;
	int m_MaxLightValue;
}; //tolua_exports


//模型类型
class BlockMyceliumModel : public BlockMycelium	//tolua_exports
{//tolua_exports
	DECLARE_BLOCKMATERIAL(BlockMyceliumModel)

public:
	//tolua_begin
	BlockMyceliumModel();
	~BlockMyceliumModel();
	virtual void dropBlockAsItem(World* pworld, const WCoord& blockpos, int blockdata = 0, BLOCK_MINE_TYPE droptype = BLOCK_MINE_NOTOOL, float chance = 1.0f, int uin = -1);
	virtual void init(int resid) override;
	virtual bool canPutOntoPos(WorldProxy* pworld, const WCoord& blockpos);
	virtual bool canStayOnPos(WorldProxy* pworld, const WCoord& blockpos);
	void setMushRoomChange(World* pworld, const WCoord& blockpos);
	//virtual std::string getGeomName() override;
	virtual void initGeomName() override;
	virtual bool onFertilized(World* pworld, const WCoord& blockpos, int fertiliser);
	virtual BlockTexElement* getDestroyTexture(Block pblock, BlockTexDesc& desc) override;
	virtual void createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh) override;
	virtual void blockTick(World* pworld, const WCoord& blockpos) override;
	virtual void onBlockPlacedBy(World* pworld, const WCoord& blockpos, IClientPlayer* player) override;
	//tolua_end
protected:
	virtual const char* getBaseTexName(char* texname, const BlockDef* def, int& gettextype);
	virtual bool canThisPlantGrowOnThisBlockID(int blockid);
	virtual int getMipmapMethod()
	{
		return 0;
	}
	virtual int getBlockGeomID(int* idbuf, int* dirbuf, const SectionDataHandler* sectionData, const WCoord& blockpos, World* world);
	/*virtual RenderBlockMaterial* getGeomMtl(const SectionDataHandler* sectionData, const WCoord& blockpos)
	{
		return m_Mtl;
	}

	RenderBlockMaterial* m_Mtl;*/
	virtual void initDefaultMtl() override;
};


#endif