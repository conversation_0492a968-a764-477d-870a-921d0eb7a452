
#include "BlockPiano.h"
#include "BlockMaterialMgr.h"
#include "section.h"
#include "SectionMesh.h"
#include "BlockGeom.h"
#include "Collision.h"
#include "world.h"
#include "ClientPlayer.h"
#include "ClientActorManager.h"
#include "VehicleWorld.h"
#include "ActorVehicleAssemble.h"
#include "ClientPlayer.h"
#include "OgreScriptLuaVM.h"
IMPLEMENT_BLOCKMATERIAL(BlockPiano)
//IMPLEMENT_BLOCKINSTANCE(BlockPiano)

using namespace MINIW;

const static int BlockPianoExtendPos[4][6][2] = {
		{
			{0, 0}, {-1, 0}, {0, 1}, {-1, 1},{-2, 0}, {-2, 1},
		},
		{
			{0, 0}, {1, 0}, {0, -1}, {1, -1},{2, 0}, {2, -1},
		},
		{
			{0, 0}, {-1, 0}, {0, -1}, {-1, -1},{0, -2}, {-1, -2},
		},
		{
			{0, 0}, {1, 0}, {0, 1}, {1, 1},{0, 2}, {1, 2},
		},
};

static int ChairBlockToPianoBlockPos[4][2] = {
	{-180, 50}, {180, -50}, {-50, -180}, {50, 180}
};

BlockPiano::BlockPiano()
{

}


void BlockPiano::initGeomName()
{
	m_geomName = m_Def->Texture2.c_str();
}

//const char *BlockPiano::getGeomName()
//{
//	return GetBlockDef()->Texture2.c_str();
//}

bool BlockPiano::isPianoOccupied(int blockdata)
{
	return (blockdata & 8) != 0;
}

void BlockPiano::setPianoOccupied(World *pworld, const WCoord &blockpos, bool occupied)
{
	int blockdata = pworld->getBlockData(blockpos);

	if (occupied) blockdata |= 8;
	else blockdata &= ~8;

	pworld->setBlockData(blockpos, blockdata, 2);
}

bool BlockPiano::onTrigger(World *pworld, const WCoord &blockpos, DirectionType face, IClientPlayer *player, const Rainbow::Vector3f &colpoint)
{
	if (pworld->isRemoteMode())
	{
		return true;
	}

	//判断是否有人在弹奏中（座位上有人）
	if (!isCoreBlock(pworld, blockpos))
	{
		WCoord basePos = getCoreBlockPos(pworld, blockpos);
		if (basePos.y < 0)
		{
			return false;
		}

		return onTrigger(pworld, basePos, face, player, colpoint);
	}

	int blockdata = pworld->getBlockData(blockpos);
	if (isPianoOccupied(blockdata))
	{
		ActorManager* actorMgr = dynamic_cast<ActorManager*>(pworld->getActorMgr());
		if (actorMgr&& actorMgr->getOccupiedPlayer(blockpos, ACTORFLAG_SIT))
		{
			return true;
		}
		setPianoOccupied(pworld, blockpos, false);
	}

	int ret = 0;
	/**
	//当椅子被方块埋住得时候不能座到椅子上
	int blockData = pworld->getBlockData(blockpos);
	int placeDir = blockData & 3;
	for (int index = 4; index < 6; index++)
	{
		auto curPos = blockpos + WCoord(BlockPianoExtendPos[placeDir][index][0], 0, BlockPianoExtendPos[placeDir][index][1]);
		auto *mtl = pworld->getBlockMaterial(curPos);
		if (mtl && !mtl->canPutOntoPos(pworld, curPos))
		{
			ret = 110116;
			break;
		}
	}
	*/
	ClientPlayer* playerTmp = player->GetPlayer();
	if (playerTmp) {
		int blockData = pworld->getBlockData(blockpos);
		int placeDir = blockData & 3;
		auto chairPos = blockpos * BLOCK_SIZE + WCoord(ChairBlockToPianoBlockPos[placeDir][0], 0, ChairBlockToPianoBlockPos[placeDir][1]);
		ret = playerTmp->sitInChairEX(chairPos, blockpos);
	}

	if (ret == 0)
	{
		setPianoOccupied(pworld, blockpos, true);
	}
	else
	{
		if (player) player->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, ret);
	}


	return true;
}

void BlockPiano::createBlockMesh(const BuildSectionMeshData& data, const WCoord &blockpos, SectionMesh *poutmesh)
{
	if (data.m_SharedSectionData->getBlock(blockpos).getData() & 4)
		return;

	ModelBlockMaterial::createBlockMesh(data, blockpos, poutmesh);
}

void BlockPiano::dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata, BLOCK_MINE_TYPE droptype/* =BLOCK_MINE_NOTOOL */, float chance/* =1.0f */, int uin)
{
	ModelBlockMaterial::dropBlockAsItem(pworld, blockpos, blockdata, droptype, chance, uin);
}

void BlockPiano::createCollideData(CollisionDetect *coldetect, World *pworld, const WCoord &blockpos)
{
	coldetect->addObstacle(blockpos*BLOCK_SIZE, blockpos*BLOCK_SIZE + WCoord(BLOCK_SIZE, BLOCK_SIZE * 2, BLOCK_SIZE));
}

void BlockPiano::onBlockAdded(World *pworld, const WCoord &blockpos)
{
	if (pworld == NULL)
	{
		return;
	}

	// 这里只有主机会执行
	int blockData = pworld->getBlockData(blockpos);
	if (isCoreBlock(pworld, blockpos))
	{
		ModelBlockMaterial::onBlockAdded(pworld, blockpos);
		int placeDir = blockData & 3;
		for (int i = 0; i < 4; i++)
		{
			
			auto curPos = blockpos + WCoord(BlockPianoExtendPos[placeDir][i][0], 0, BlockPianoExtendPos[placeDir][i][1]);
			if (curPos.x == blockpos.x && curPos.z == blockpos.z)
				continue;
			pworld->setBlockAll(curPos, getBlockResID(), 4 | placeDir);
		}
	}
}

void BlockPiano::onBlockRemoved(World *pworld, const WCoord &blockpos, int blockid, int blockdata)
{
	if (pworld == NULL)
	{
		return;
	}
	if (blockdata == 12)  //表示不用再处理remove事件
		return;

	int placeDir = blockdata & 3;
	if (blockdata & 4)  //普通的方块
	{
		WCoord basePos = getCoreBlockPos(pworld, blockpos, blockdata);
		if (basePos.y >= 0)
		{
			pworld->setBlockAir(basePos);
		}
	}
	else
	{
		// 这里只有主机会执行
		ModelBlockMaterial::onBlockRemoved(pworld, blockpos, blockid, blockdata);
		clearNormalBlock(pworld, blockpos, blockdata);
	}
}

void BlockPiano::onBlockPlacedBy(World *pworld, const WCoord &blockpos, IClientPlayer *player)
{
	if (pworld == NULL || player == NULL)
	{
		return;
	}
	ClientPlayer* playerTmp = player->GetPlayer();
	if (!playerTmp) return;
	ModelBlockMaterial::onBlockPlacedBy(pworld, blockpos, player);

	int placeDir = playerTmp->getCurPlaceDir();
	if (placeDir == DIR_NEG_Y || placeDir == DIR_POS_Y)
	{
		placeDir = DIR_NEG_X;
	}
	pworld->setBlockData(blockpos, placeDir);
}

bool BlockPiano::canPutOntoPlayer(World *pworld, const WCoord &blockpos, IClientPlayer *player)
{
	if (!player || !pworld)
		return false;
	ClientPlayer* playerTmp = player->GetPlayer();
	if (!playerTmp) return false;
	int placeDir = playerTmp->getCurPlaceDir();
	if (placeDir == DIR_NEG_Y || placeDir == DIR_POS_Y || placeDir > 4 || placeDir < 0)
	{
		placeDir = DIR_NEG_X;
	}

	for (int i = 0; i < 6; i++)
	{
		for (int y = 0; y < 2; y++)
		{
			auto curPos = blockpos + WCoord(BlockPianoExtendPos[placeDir][i][0], y, BlockPianoExtendPos[placeDir][i][1]);
			auto *mtl = pworld->getBlockMaterial(curPos);
			
			if (mtl && !mtl->canPutOntoPos(pworld->getWorldProxy(), curPos))
				return false;
		}

	}

	return true;
}

WCoord BlockPiano::getCoreBlockPos(World *pworld, const WCoord &blockpos, int blockdata/* =-1 */)
{
	const int searchDir[4][2] = { {1, -1}, {-1, 1}, {1, 1}, {-1, -1}};
	if (blockdata == -1)
		blockdata = pworld->getBlockData(blockpos);
	int placeDir = blockdata & 3;
	for (int x = 0; x < 2; x++)
	{
		for (int z = 0; z < 2; z++)
		{
			for (int y = 0; y < 2; y++)
			{
				auto curPos = blockpos + WCoord(searchDir[placeDir][0] * x, -1 * y, searchDir[placeDir][1] * z);
				int blockid = pworld->getBlockID(curPos);
				if (blockid == getBlockResID() && (pworld->getBlockData(curPos) & 4) == 0) //显示模型的方块
				{
					return curPos;
				}
			}
		}
	}

	return WCoord(0, -1, 0);
}

bool BlockPiano::isCoreBlock(World *pworld, const WCoord &blockpos)
{
	//第三位代表当前方块是否核心方块（0 核心方块钢琴-显示模型， 普通方块-不显示模型）
	if (!pworld)
		return false;

	int blockData = pworld->getBlockData(blockpos);
	return (pworld->getBlockData(blockpos) & 4) == 0;
}

void BlockPiano::clearNormalBlock(World *pworld, const WCoord &blockpos, int blockdata /* = -1 */)
{
	if (blockdata == -1)
		blockdata = pworld->getBlockData(blockpos);
	int placeDir = blockdata & 3;

	for (int i = 0; i < 4; i++)
	{
		for (int y = 0; y < 2; y++)
		{
			auto curPos = blockpos + WCoord(BlockPianoExtendPos[placeDir][i][0], y, BlockPianoExtendPos[placeDir][i][1]);
			if (curPos == blockpos)
				continue;

			int blockid = pworld->getBlockID(curPos);
			if (blockid == getBlockResID() && !isCoreBlock(pworld, curPos))
			{
				pworld->setBlockData(curPos, 12, 0);
				pworld->setBlockAir(curPos);
			}
		}
	}
}

int BlockPiano::getPlaceBlockDataByPlayer(World *pworld, IClientPlayer *player)
{
	if (player)
	{
		ClientPlayer* playerTmp = player->GetPlayer();
		if (!playerTmp) return 0;
		return playerTmp->getCurPlaceDir();
	}

	return 0;
}