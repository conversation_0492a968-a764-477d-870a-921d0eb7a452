
#include "BlockBuildBluePrint.h"
#include "BlockMaterialMgr.h"
#include "section.h"
#include "SectionMesh.h"
#include "container_world.h"
#include "container_buildblueprint.h"
#include "special_blockid.h"
#include "IBackpack.h"
#include "IClientPlayer.h"
#include "actors/ClientActorManager.h"
#include "IClientItem.h"
#include "WorldManager.h"

using namespace MINIW;

IMPLEMENT_BLOCKMATERIAL(BlockBuildBluePrint)

BlockBuildBluePrint::BlockBuildBluePrint() //: m_SideLineMtl(NULL)
{

}

BlockBuildBluePrint::~BlockBuildBluePrint()
{
	//OGRE_RELEASE(m_SideLineMtl);
	//OGRE_RELEASE(m_WorkLineMtl);
}

void BlockBuildBluePrint::init(int resid)
{
	ModelBlockMaterial::init(resid);

	//m_SideLineMtl = g_BlockMtlMgr.createRenderMaterial("copy_range_line", m_Def, GETTEX_WITHDEFAULT, BLOCKDRAW_GRASS);
	//m_WorkLineMtl = g_BlockMtlMgr.createRenderMaterial("construction_line", m_Def, GETTEX_WITHDEFAULT, BLOCKDRAW_GRASS);
}

void BlockBuildBluePrint::onBlockPlacedBy(World *pworld, const WCoord &blockpos, IClientPlayer *player)
{
	if (!pworld || !player)
	{
		return;
	}

	ContainerBuildBluePrint *container = dynamic_cast<ContainerBuildBluePrint *>(pworld->getContainerMgr()->getContainer(blockpos));
	if (container == NULL)
	{
		container = SANDBOX_NEW(ContainerBuildBluePrint, blockpos);
		pworld->getContainerMgr()->spawnContainer(container);
	}

	std::string userdataStr = "";
	int realItemId = 0; //蓝图真实的id
	BackPackGrid *grid = player->getIBackPack()->index2Grid(player->getCurShortcut() + player->getIBackPack()->getShortcutStartIndex());
	if (grid)
	{
		userdataStr = grid->getUserdataStr();
		realItemId = grid->getUserDataInt();
	}

	container->setBuildPrintData(userdataStr, realItemId);

	int dir = player->GetPlayerCurPlaceDir();
	container->changeDim(dir);
	pworld->setBlockData(blockpos, dir, 3);

	/*if (g_pPlayerCtrl)
	{
		g_pPlayerCtrl->statisticToWorld(g_pPlayerCtrl->getUin(), 30002, "", g_pPlayerCtrl->getCurWorldType(), "1065");

	}*/
}

void BlockBuildBluePrint::onBlockAdded(World *pworld, const WCoord &blockpos)
{
	ModelBlockMaterial::onBlockAdded(pworld, blockpos);

	ContainerBuildBluePrint *container = dynamic_cast<ContainerBuildBluePrint *>(pworld->getContainerMgr()->getContainer(blockpos));
	if (container == NULL)
	{
		container = SANDBOX_NEW(ContainerBuildBluePrint, blockpos);
		pworld->getContainerMgr()->spawnContainer(container);
	}
}

void BlockBuildBluePrint::onBlockRemoved(World *pworld, const WCoord &blockpos, int blockid, int blockdata)
{
	ModelBlockMaterial::onBlockRemoved(pworld, blockpos, blockid, blockdata);

	ContainerBuildBluePrint *container = dynamic_cast<ContainerBuildBluePrint *>(pworld->getContainerMgr()->getContainer(blockpos));
	if (container)
	{
		pworld->getContainerMgr()->destroyContainer(blockpos);
	}
}

extern void AddVertToArray(std::vector<BlockGeomVert>&vertarray, const Rainbow::Vector3f &pos, float u, float v);
extern bool GetVertXCuboidArray(std::vector<BlockGeomVert>&array1, std::vector<BlockGeomVert>&array2, std::vector<BlockGeomVert>&array3, std::vector<BlockGeomVert>&array4, std::vector<unsigned short>&indices, WCoord startPos, WCoord dim);
extern bool GetVertZCuboidArray(std::vector<BlockGeomVert>&array1, std::vector<BlockGeomVert>&array2, std::vector<BlockGeomVert>&array3, std::vector<BlockGeomVert>&array4, std::vector<unsigned short>&indices, WCoord startPos, WCoord dim);
extern bool GetVertYCuboidArray(std::vector<BlockGeomVert>&array1, std::vector<BlockGeomVert>&array2, std::vector<BlockGeomVert>&array3, std::vector<BlockGeomVert>&array4, std::vector<unsigned short>&indices, WCoord startPos, WCoord dim);

void BlockBuildBluePrint::createBlockMesh(const BuildSectionMeshData& data, const WCoord &blockpos, SectionMesh *poutmesh)
{
#ifndef IWORLD_SERVER_BUILD
	ModelBlockMaterial::createBlockMesh(data, blockpos, poutmesh);
#endif
}

bool BlockBuildBluePrint::onTrigger(World *pworld, const WCoord &blockpos, DirectionType face, IClientPlayer *player, const Rainbow::Vector3f &colpoint)
{
	if (!player) return false;

	if(pworld->getContainerMgr() == NULL) return true;

	if (!pworld->isRemoteMode())
	{
		ContainerBuildBluePrint *container = dynamic_cast<ContainerBuildBluePrint *>(pworld->getContainerMgr()->getContainer(blockpos));
		if (container && (container->getBuildIndex() == FRIST_BUILD || !container->isMaterialEnough()))
		{
			if (player->getCurToolID() == ITEM_WRENCH)
			{
				int dir = pworld->getBlockData(blockpos);
				if (dir == NEG_X || dir == POS_X)
					container->changeDim(MIRROR_X);
				else if (dir == NEG_Z || dir == POS_Z)
					container->changeDim(MIRROR_Z);
				else if (dir == NEG_Y || dir == POS_Y)
					container->changeDim(MIRROR_Y);

				container->markRelativeBlocksForUpdate();

				return true;
			}
		}

		if (!GetWorldManagerPtr())
			return false;

		if (container && container->getBuildIndex() <= 0 && container->getBuildIndex() != FINISH_BUILD)
		{
			int type = pworld->getMapSpecialType();
			bool isGodMode = GetWorldManagerPtr()->isGodMode();

			//家园地图只有家园主在编辑模式才能打开 普通地图在非编辑模式才能打开
			if ((isGodMode && type == HOME_GARDEN_WORLD && player->hasUIControl()) || (!isGodMode && type != HOME_GARDEN_WORLD))
			{
				player->openContainer(container);
			}
		}
			
	}

	return true;
}

void BlockBuildBluePrint::onNotify(World *pworld, const WCoord &blockpos, int blockid)
{
	if(pworld->getContainerMgr() == NULL) return;
	int power = pworld->getStrongestIndirectPower(blockpos);
	ContainerBuildBluePrint *container = dynamic_cast<ContainerBuildBluePrint *>(pworld->getContainerMgr()->getContainer(blockpos));
	if (container)
	{
		if(HOME_GARDEN_WORLD != pworld->getMapSpecialType() || power > 0)  //家园蓝图不通电也能用
			container->setRateByPower(power);

		container->markRelativeBlocksForUpdate();

	}
}

void BlockBuildBluePrint::dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata /* = 0 */, BLOCK_MINE_TYPE droptype /* = BLOCK_MINE_NOTOOL */, float chance /* = 1.0f */, int uin /* = -1 */)
{
	if(pworld->getContainerMgr() == NULL) return;
	ContainerBuildBluePrint *container = dynamic_cast<ContainerBuildBluePrint *>(pworld->getContainerMgr()->getContainer(blockpos));
	if (container)
	{
		IClientItem *item = pworld->getActorMgr()->SpawnIClientItem(BlockCenterCoord(blockpos), 1064, 1);
		if (item)
			item->getItemData()->setUserdataStr(container->getBPDataStr().c_str());
	}
}

bool BlockBuildBluePrint::canDestroy(World *pworld, const WCoord &blockpos)
{
	if (!pworld || !GetWorldManagerPtr())
		return false;

	ContainerBuildBluePrint *container = dynamic_cast<ContainerBuildBluePrint *>(pworld->getContainerMgr()->getContainer(blockpos));
	if (container)
	{
		//家园蓝图建到一半的时候不允许破坏蓝图工作台方块
		if (HOME_GARDEN_WORLD == pworld->getMapSpecialType() && container->getBuildIndex() > 0)
			return false;
	}

	return true;
}

