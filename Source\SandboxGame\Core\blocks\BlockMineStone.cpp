/*
*	file: BlockMineStone
*/
#include "BlockMineStone.h"
#include "special_blockid.h"
#include "world.h"
#include "WorldProxy.h"
#include "BlockGeom.h"
#include "LuaInterfaceProxy.h"
#include "WorldManager.h"
#include "BlockMaterialMgr.h"
#include "BlockMaterial.h"
#include "RenderSection.h"
#include "Common/GameStatic.h"
#include "DefManagerProxy.h"
#include "section.h"
#include "coreMisc.h"
#include "RuneDef.h"
#include "ActorManager.h"

IMPLEMENT_BLOCKMATERIAL(BlockMineStone)
IMPLEMENT_BLOCKMATERIAL(BlockMineStoneDiffMtl)
IMPLEMENT_BLOCKMATERIAL(BlockMineStoneRune)
static const WCoord NeighborBlockPos[3][8] =
{
		{WCoord( 0, 1, 1), <PERSON>oord( 0, 1,-1), <PERSON><PERSON><PERSON>( 0,-1, 1), <PERSON>oord( 0,-1,-1), <PERSON>oord( 0, 1, 0), <PERSON>oord( 0,-1, 0), <PERSON>oord( 0, 0, 1), WCoord( 0, 0,-1)},
		{WCoord( 1, 1, 0), WCoord(-1, 1, 0), WCoord( 1,-1, 0), WCoord(-1,-1, 0), WCoord( 0, 1, 0), WCoord( 0,-1, 0), WCoord( 1, 0, 0), WCoord(-1, 0, 0)},
		{WCoord( 1, 0, 1), WCoord( 1, 0,-1), WCoord(-1, 0, 1), WCoord(-1, 0,-1), WCoord( 1, 0, 0), WCoord(-1, 0, 0), WCoord( 0, 0, 1), WCoord( 0, 0,-1)}
};
BlockMineStone::BlockMineStone() :m_StoneMtl(NULL), m_Mtl_x(NULL), m_Mtl_y(NULL), m_Mtl_z(NULL), m_Mtl_Proto(NULL)
{

}
BlockMineStone::~BlockMineStone()
{
	ENG_RELEASE(m_StoneMtl);
	ENG_RELEASE(m_Mtl_x);
	ENG_RELEASE(m_Mtl_y);
	ENG_RELEASE(m_Mtl_z);
	ENG_RELEASE(m_Mtl_Proto);
}

const dynamic_array<UInt16> noRoundBlock = { 400,536,453,454,455 };//不显示圆角的矿石 炽炎矿 曙光 攻击符文矿石 防御符文矿石 效率符文矿石
const dynamic_array<UInt16> inSideBlock = { 536,453,454,455 };//凹陷的矿石 曙光 攻击符文矿石 防御符文矿石 效率符文矿石
//const dynamic_array<UInt16> isProtoBlock = { 452,401,451,445,446,406,404,403,24,34,407,405 };//需要单独渲染手持和icon贴图的矿石 星瞳 紫荧
void BlockMineStone::init(int resid)
{
	ModelBlockMaterial::init(resid);
	ModelBlockMaterial::SetAttrRenderType(BLOCKRENDER_CUBE_MODEL_MIX);
	bool isNoRoundBlock = false;
	for (int i = 0; i < noRoundBlock.size(); i++)
	{
		if (resid == noRoundBlock[i])
		{
			isNoRoundBlock = true;
			break;
		}
	}
	if (isNoRoundBlock == false)
	{
		m_blockDrawType = BLOCKDRAW_OPAQUE;
		int getTex = GETTEX_NORMAL;
		if (resid == 34)//霜火石矿
		{
			getTex = GETTEX_WITHDEFAULT;
			m_blockDrawType = BLOCKDRAW_ICE;//冰需要绘制法线
		}
		char name[256];
		//侧面x
		sprintf(name, "%s_x", m_Def->Texture1.c_str());
		m_Mtl_x = g_BlockMtlMgr.createRenderMaterial(name, ModelBlockMaterial::m_Def, getTex, m_blockDrawType, getMipmapMethod());
			
		//侧面z
		sprintf(name, "%s_z", m_Def->Texture1.c_str());
		m_Mtl_z = g_BlockMtlMgr.createRenderMaterial(name, ModelBlockMaterial::m_Def, getTex, m_blockDrawType, getMipmapMethod());
			
		//顶面底面y
		sprintf(name, "%s_y", m_Def->Texture1.c_str());
		m_Mtl_y = g_BlockMtlMgr.createRenderMaterial(name, ModelBlockMaterial::m_Def, getTex, m_blockDrawType, getMipmapMethod());
	}
	else
	{
		m_Mtl_x = g_BlockMtlMgr.createRenderMaterial(m_Def->Texture1.c_str(), ModelBlockMaterial::m_Def, GETTEX_NORMAL, BLOCKDRAW_OPAQUE, getMipmapMethod());
		m_Mtl_z = NULL;
		m_Mtl_y = NULL;
	}

	m_nSpecialLogicType[0] |= RotateMechaStopNoChangePos;
	m_StoneMtl = g_BlockMtlMgr.createRenderMaterial(GetBlockDef()->Texture2.c_str(), ModelBlockMaterial::m_Def, GETTEX_WITHDEFAULT, BLOCKDRAW_STATUE, getMipmapMethod());

#ifndef DEDICATED_SERVER //云服主机调用这里会宕机，云服没有必要执行
	//m_StoneMtl->SetEmissiveTex(GetBlockDef()->Texture2.c_str());
	if (m_StoneMtl)
	{
		if (m_StoneMtl->isEmissiveTexload())//检查是否有发光贴图，有发光贴图的打开EMISSIVE效果
		{
			m_StoneMtl->getMaterial()->EnableKeyword("EMISSIVE");
		}
		else
		{
			m_StoneMtl->getMaterial();
		}
		m_StoneMtl->getItemMaterial();
	}
#endif

	setMaterialStatus(m_StoneMtl, resid);
	char nametmp[256];
	sprintf(nametmp, "%s_proto", m_Def->Texture2.c_str());
	m_Mtl_Proto = g_BlockMtlMgr.createRenderMaterial(nametmp, ModelBlockMaterial::m_Def, GETTEX_NORMAL, BLOCKDRAW_OPAQUE, getMipmapMethod());

#ifndef DEDICATED_SERVER //云服主机调用这里会宕机，云服没有必要执行
	if (m_Mtl_Proto)
	{
		//m_Mtl_Proto->SetEmissiveTex(nametmp);
		if (m_Mtl_Proto->isEmissiveTexload())//检查是否有发光贴图，有发光贴图的打开EMISSIVE效果
		{
			m_Mtl_Proto->getMaterial()->EnableKeyword("EMISSIVE");
		}
		else
		{
			m_Mtl_Proto->getMaterial();
		}
		m_Mtl_Proto->getItemMaterial();
	}
#endif

}
 
void BlockMineStone::setMaterialStatus(RenderBlockMaterial* Mtl, int resid)
{
	if (Mtl == NULL) return;
	switch (resid) {
	case 406://琥珀1
		Mtl->setMatcapMonsterStatusParam(1.6f, 0.8f, 1.6f, 0.8f, "crystal010.png");
		break;
	case 445://星瞳2
		Mtl->setMatcapMonsterStatusParam(6.0f, 0.9f, 6.0f, 0.9f, "colorful2.png");
		break;
	case 446://紫荧3
		Mtl->setMatcapMonsterStatusParam(6.0f, 0.9f, 6.0f, 0.9f, "colorful2.png");
		break;
	case 407://云母4
		Mtl->setMatcapMonsterStatusParam(8.0f, 0.8f, 8.0f, 0.8f, "crystal010.png");
		break;
	case 536://曙光5
		Mtl->setMatcapMonsterStatusParam(2.0f, 0.6f, 2.0f, 0.6f, "crystal06.png");
		break;
	case 33://平凡6
		Mtl->setMatcapMonsterStatusParam(1.8f, 0.8f, 1.8f, 0.8f, "crystal014.png");
		break;
	case 404://钨金7
		Mtl->setMatcapMonsterStatusParam(60.0f, 0.7f, 60.0f, 0.7f, "crystal11.png");
		break;
	case 34://霜火石矿8
		Mtl->setMatcapMonsterStatusParam(5.0f, 0.8f, 5.0f, 0.8f, "spacecamp.png");
		break;
	case 400://炽炎矿9
		Mtl->setMatcapMonsterStatusParam(5.0f, 0.6f, 5.0f, 0.6f, "crystal06.png");
		break;
	case 403://缠丝玛瑙原石10
		Mtl->setMatcapMonsterStatusParam(1.0f, 0.9f, 1.0f, 0.9f, "crystal09.png");
		break;
	case 401://秘银矿11
		Mtl->setMatcapMonsterStatusParam(2.6f, 1.2f, 2.6f, 1.2f, "goldenTestZmt_2.png");
		break;
	case 451://铜合金矿12
		Mtl->setMatcapMonsterStatusParam(4.0f, 1.2f, 4.0f, 1.2f, "goldenTestZmt4.png");
		break;
	case 452://钛合金矿13
		Mtl->setMatcapMonsterStatusParam(2.0f, 1.2f, 2.0f, 1.2f, "Metal02.png");
		break;
	case 24://锰结核矿14
		Mtl->setMatcapMonsterStatusParam(3.5f, 1.2f, 3.5f, 1.2f, "glass02.png");
		break;
	case 405://星能矿15
		Mtl->setMatcapMonsterStatusParam(6.0f, 1.3f, 6.0f, 1.3f, "metal2.png");
		break;
	case 453://攻击16
		Mtl->setMatcapMonsterStatusParam(2.0f, 0.4f, 2.0f, 0.4f, "crystal03.png");
		break;
	case 454://防御17
		Mtl->setMatcapMonsterStatusParam(2.0f, 0.4f, 2.0f, 0.4f, "crystal03.png");
		break;
	case 455://效率18
		Mtl->setMatcapMonsterStatusParam(2.0f, 0.4f, 2.0f, 0.4f, "crystal03.png");
		break;
	default://默认
		Mtl->setMatcapMonsterStatusParam(1.6f, 0.8f, 1.6f, 0.8f, "crystal010.png");
	}
}

BlockTexElement* BlockMineStone::getDestroyTexture(Block pblock, BlockTexDesc& desc)
{
	return m_StoneMtl->getTexElement();//m_Mtl->getTexElement();
}

RenderBlockMaterial* BlockMineStone::getFaceMtlForMineStone(int dir)
{
	if (m_Mtl_z == NULL || m_Mtl_y == NULL)return m_Mtl_x;
	if (dir <= DIR_POS_X)
	{
		return m_Mtl_x;
	}
	else if (dir <= DIR_POS_Z)
	{
		return m_Mtl_z;
	}
	return m_Mtl_y;
}

void BlockMineStone::createBlockMeshAngle(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh)
{
	bool isNoRoundBlock = false;
	for (int i = 0; i < noRoundBlock.size(); i++)
	{
		if (m_BlockResID == noRoundBlock[i])
		{
			isNoRoundBlock = true;
			break;
		}
	}
	auto psection = data.m_SharedSectionData;
	if (isNoRoundBlock == false)
	{
		bool noRound = true;
		if (0 == BlockMaterialMgr::m_BlockShape)//生成圆角
		{
			bool isRound = !poutmesh->isSquareSectionMesh();
			if (isRound)
			{
				FaceVertexLight faceVertexLight;
				auto pblock = psection->getBlock(blockpos);
				for (int d = 0; d < 6; d++)
				{
					DirectionType dir = (DirectionType)d;
					bool isNoClip = false;
					if (checkBlockMeshIsBuild(data, blockpos, d, isNoClip))
					{
						bool flipQuad = psection->getCubeFaceVertexLight(blockpos, dir, faceVertexLight);
						BlockColor facecolor(255, 255, 255, 0);
						RenderBlockMaterial* pmtl = getFaceMtlForMineStone(d);
						if (pmtl == NULL) continue;
						SectionSubMesh* psubmesh = poutmesh->getSubMesh(pmtl);
						BlockGeomMeshInfo mesh;
						getBlockMeshAngleData(data, blockpos, mesh, d, psubmesh && !psubmesh->IsIgnoreTileUV(), pmtl, isNoClip);
						if (!checkRenderForFace(data, blockpos, dir)) continue;
						if (psubmesh)
						{
							psubmesh->addGeomFace(mesh, &blockpos, &facecolor);
						}
					}
				}
				noRound = false;
			}
		}
		if (noRound == true)//生成直角
		{
			FaceVertexLight faceVertexLight;
			//float block_light[16] = { 0 };
			Block pblock = psection->getBlock(blockpos);
			int curblockdata = pblock.getData();

			const BiomeDef* biome = GetDefManagerProxy()->getBiomeDef(BIOME_TYPE::BIOME_PLAINS);

			for (int d = 0; d < 6; d++)
			{
				DirectionType dir = (DirectionType)d;
				if (!checkRender(data, blockpos, dir)) continue;
				bool flipQuad = psection->getFaceVertexLight(blockpos, dir, faceVertexLight);
				BlockColor facecolor(255, 255, 255, 0);
				RenderBlockMaterial* pmtl = getFaceMtlForMineStone(d);
				if (pmtl == NULL) continue;
				SectionSubMesh* psubmesh = poutmesh->getSubMesh(pmtl);
				BlockGeomMeshInfo mesh;
				BlockMaterial* mtltmp = g_BlockMtlMgr.getMaterial(104);
				if (!mtltmp || mtltmp->getGeom() == NULL) continue;
				mtltmp->getGeom()->getFaceVerts(mesh, dir, 1.0f, 1, DIR_NEG_Z, 0, nullptr, 0, flipQuad);
				if (psubmesh) psubmesh->addGeomFaceLight(mesh, &blockpos, faceVertexLight, &facecolor, pmtl->getUVTile());
			}
		}
	}
}

BLOCK_RENDERTYPE_T BlockMineStone::GetAttrRenderType() const
{
	return BLOCKRENDER_CUBE_MODEL_MIX;
}

bool BlockMineStone::checkRenderForFace(const BuildSectionMeshData& data, const WCoord& blockpos, DirectionType dir)
{
	bool showFaceBuf[9];
	showFaceBuf[0] = !checkRender(data, blockpos + NeighborBlockPos[dir / 2][0], dir);
	showFaceBuf[1] = !checkRender(data, blockpos + NeighborBlockPos[dir / 2][1], dir);
	showFaceBuf[2] = !checkRender(data, blockpos + NeighborBlockPos[dir / 2][2], dir);
	showFaceBuf[3] = !checkRender(data, blockpos + NeighborBlockPos[dir / 2][3], dir);
	showFaceBuf[4] = !checkRender(data, blockpos + NeighborBlockPos[dir / 2][4], dir);
	showFaceBuf[5] = !checkRender(data, blockpos + NeighborBlockPos[dir / 2][5], dir);
	showFaceBuf[6] = !checkRender(data, blockpos + NeighborBlockPos[dir / 2][6], dir);
	showFaceBuf[7] = !checkRender(data, blockpos + NeighborBlockPos[dir / 2][7], dir);
	showFaceBuf[8] = !checkRender(data, blockpos, dir);
	if (showFaceBuf[8])
	{
		if (showFaceBuf[4] && showFaceBuf[5]) return false;
		else if (showFaceBuf[6] && showFaceBuf[7]) return false;
		else if (showFaceBuf[0] && showFaceBuf[1] && showFaceBuf[2] && showFaceBuf[3]) return false;
		else if (showFaceBuf[2] && showFaceBuf[3] && showFaceBuf[4]) return false;
		else if (showFaceBuf[0] && showFaceBuf[1] && showFaceBuf[5]) return false;
		else if (showFaceBuf[1] && showFaceBuf[3] && showFaceBuf[6]) return false;
		else if (showFaceBuf[0] && showFaceBuf[2] && showFaceBuf[7]) return false;
	}
	return true;
}


bool BlockMineStone::checkRender(const BuildSectionMeshData& data, const WCoord& blockpos, DirectionType dir)
{
	auto psection = data.m_SharedSectionData;
	if (!psection) return false;
	Block sideblocktmp = psection->getNeighborBlock(blockpos, dir);
	int blockIdtmp = sideblocktmp.getResID();
	BLOCK_RENDERTYPE_T rendertype_tmp = (BLOCK_RENDERTYPE_T)BlockMaterial::getRenderTypes(blockIdtmp);
	BlockMaterial* blockMatTmp = g_BlockMtlMgr.getMaterial(blockIdtmp);
	if ((rendertype_tmp != BLOCKRENDER_CUBE && rendertype_tmp != BLOCKRENDER_CUBE_MODEL_MIX) || (blockMatTmp && blockMatTmp->BlockTypeId() != BlockMaterial::BlockType::NON))
	{
		return true;
	}
	return false;
}

void BlockMineStone::checkShowFace(const BuildSectionMeshData& data, const WCoord& blockpos, bool* showBuf)
{
	auto psection = data.m_SharedSectionData;
	if (!psection) return;
	for (int i = 0; i < 6; i++)
	{
		showBuf[i] = checkRender(data, blockpos, DirectionType(i));
	}
}

void BlockMineStone::createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh)
{
	BlockGeomTemplate* geom = getGeom(data.m_LODLevel);
	if (!geom) return;
	auto psection = data.m_SharedSectionData;
	bool showBuf[6];
	checkShowFace(data, blockpos, showBuf);
	createBlockMeshAngle(data, blockpos, poutmesh);
	Rainbow::ColorRGBAf verts_light[1] = { Rainbow::ColorRGBAf::white };
	bool isInSideBlock = false;
	for (int i = 0; i < inSideBlock.size(); i++)
	{
		if (m_BlockResID == inSideBlock[i])
		{
			isInSideBlock = true;
			break;
		}
	}
	psection->getBlockVertexLight(blockpos, verts_light);
	Block pblock = psection->getBlock(blockpos);
	int blockdata = pblock.getData();
	int idbuf[32];
	int dirbuf[32];
	int ngeom = getBlockGeomID(idbuf, dirbuf, psection, blockpos, data.m_World);
	BlockGeomMeshInfo meshinfo;
	RenderBlockMaterial* pmtl = m_StoneMtl; //getGeomMtl(psection, blockpos);
	if (pmtl == NULL) return;
	SectionSubMesh* psubmesh = poutmesh->getSubMesh(pmtl);
	int dir = dirbuf[0] & 0xffff;
	int mirrortype = (dirbuf[0] >> 16) & 3;
	for (int i = 0; i < 6; i++)
	{
		if (!showBuf[i]) continue;
		geom->getFaceVerts(meshinfo, idbuf[i], 1.0f, 0, dir, mirrortype);
		psubmesh->addGeomBlockLight(meshinfo, &blockpos, verts_light, NULL, pmtl->getUVTile());	
	}
	
	if (isInSideBlock == true)//凹陷矿石
	{
		BlockGeomMeshInfo meshinfo_t;
		RenderBlockMaterial* pmtl_t = m_Mtl_x; //getGeomMtl(psection, blockpos);
		SectionSubMesh* psubmesh_t = poutmesh->getSubMesh(pmtl_t);
		int dir_t = dirbuf[1] & 0xffff;
		int mirrortype_t = (dirbuf[1] >> 16) & 3;
		for (int i = 0; i < 6; i++)
		{
			if (!showBuf[i]) continue;
			geom->getFaceVerts(meshinfo_t, idbuf[i + 6], 1.0f, 0, dir_t, mirrortype_t);
			psubmesh_t->addGeomBlockLight(meshinfo_t, &blockpos, verts_light, NULL, pmtl_t->getUVTile());
			
		}
	}
}

SectionMesh* BlockMineStone::createBlockProtoMesh(int protodata)
{
	SectionMesh* pmesh = ENG_NEW(SectionMesh)();
	bool isinSideBlock = false;
	for (int i = 0; i < inSideBlock.size(); i++)
	{
		if (m_BlockResID == inSideBlock[i])
		{
			isinSideBlock = true;
		}
	}
	int blockModelId = 0;
	int blockdata = protodata;
	
	if (isinSideBlock == true)
	{
		blockModelId = 1;
		RenderBlockMaterial* imtl = m_Mtl_x;
		SectionSubMesh* imesh = pmesh->getSubMesh(imtl, true);
		BlockGeomMeshInfo imeshinfo;
		for (int i = 0; i < 6; i++)
		{
			getGeom(0)->getFaceVerts(imeshinfo, 6+i, 1.0f, 0, DIR_NEG_X);
			if (imesh)
				imesh->addGeomFaceLight(imeshinfo, NULL, s_DefaultFaceVertexLights, NULL, imtl->getUVTile());
		}
		//Rainbow::ColorRGBAf verts_light[1] = { Rainbow::ColorRGBAf::white };
	}
	RenderBlockMaterial* smtl = NULL;
	if (m_Mtl_Proto)
	{
		smtl = m_Mtl_Proto;
	}
	else
	{
		smtl = m_StoneMtl;
	}
	SectionSubMesh* stonemesh = pmesh->getSubMesh(smtl, true);
	BlockGeomMeshInfo meshinfo;
	for (int i = 0; i < 6; i++)
	{
		getGeom(0)->getFaceVerts(meshinfo, i, 1.0f, 0, DIR_NEG_X);
		if (stonemesh)
			stonemesh->addGeomFaceLight(meshinfo, NULL, s_DefaultFaceVertexLights, NULL, smtl->getUVTile());
	}
	//Rainbow::ColorRGBAf verts_light[1] = { Rainbow::ColorRGBAf::white };
	// 	int anglesize = RoundAngleSize;
	

	for (int i = 0; i < noRoundBlock.size(); i++)
	{
		if (m_BlockResID == noRoundBlock[i])
		{
			return pmesh;
		}
	}
	if (1 == BlockMaterialMgr::m_BlockShape)//直角
	{
		for (int d = 0; d < 6; d++)
		{
			DirectionType dir = (DirectionType)d;

			BlockColor facecolor(255, 255, 255, 0);
			RenderBlockMaterial* rbmtl = getFaceMtlForMineStone(d);
			if (!rbmtl)
			{
				continue;
			}
			SectionSubMesh* psubmesh = pmesh->getSubMesh(rbmtl, true);
			BlockGeomMeshInfo meshinfo;
			BlockMaterial* mtltmp = g_BlockMtlMgr.getMaterial(104);
			if (!mtltmp || mtltmp->getGeom(0) == NULL) continue;
			mtltmp->getGeom(0)->getFaceVerts(meshinfo, dir, 1.0f, 1);
			if (psubmesh)
				psubmesh->addGeomFaceLight(meshinfo, NULL, s_DefaultFaceVertexLights, &facecolor);
		}
	}
	else//圆角
	{
		for (int d = 0; d < 6; d++)
		{
			DirectionType dir = (DirectionType)d;
			RenderBlockMaterial* rbmtl = getFaceMtlForMineStone(d);
			if (rbmtl == NULL) continue;
			SectionSubMesh* psubmesh = pmesh->getSubMesh(rbmtl, true);
			setSubMesh(psubmesh, rbmtl, dir);
		}
	}
	return pmesh;
}

void BlockMineStone::setSubMesh(SectionSubMesh* psubmesh, RenderBlockMaterial* rbmtl, DirectionType dir)
{
	BlockGeomMeshInfo mesh;
	BlockColor facecolor(255, 255, 255, 0);
	float blockheight = 1.0f;
	dynamic_array<BlockGeomVert> verts;
	verts.resize_initialized(16, kIncreaseToExactSize);
	const char* origin_c/*[3]*/ = Rainbow::RoundBlockOriginC[dir];
	WCoord ndir = dir >= DIR_NEG_Z ? g_DirectionCoord[dir] : g_DirectionCoord[dir] + WCoord(0, 1, 0);
	unsigned short dir_color = Normal2LightColor(ndir.toVector3());

	Rainbow::Vector3f normalVec = ndir.toVector3();
	normalVec.Normalize();
	BlockVector normal_dir = PackVertNormal(normalVec, 1);
	BlockColor vertcolor1;
	BlockVector vertcolor;
	vertcolor.v = 0xffffffff;
	vertcolor.w = dir_color;


	const char* du = Rainbow::DU[dir];
	const char* dv = Rainbow::DV[dir];
	const float* texuv = Rainbow::BLOCKUV[dir];

	do
	{
		float uvtile[4];
		uvtile[0] = 0;
		uvtile[1] = 0;
		uvtile[2] = 1.0f;
		uvtile[3] = 1.0f;
		InitVert(verts[0], origin_c, du, dv, 0, 0, texuv[0 * 2], texuv[0 * 2 + 1], vertcolor, NULL, 255, normal_dir);
		InitVert(verts[1], origin_c, du, dv, 1, 0, texuv[1 * 2], texuv[1 * 2 + 1], vertcolor, NULL, 255, normal_dir);
		InitVert(verts[2], origin_c, du, dv, 1, 1, texuv[2 * 2], texuv[2 * 2 + 1], vertcolor, NULL, 255, normal_dir);
		InitVert(verts[3], origin_c, du, dv, 0, 1, texuv[3 * 2], texuv[3 * 2 + 1], vertcolor, NULL, 255, normal_dir);
		int blockdata_ = 0;

		unsigned char hor_ver = 6;

		const vertex_cache* cache = Section::getVertexCacheEx(blockheight, NULL, dir, blockdata_, 0, hor_ver);
		// 			const vertex_cache* cache = Section::getVertexCache(NULL, dir, blockdata_);
		if (cache)
		{
			const auto& find = *cache;
			int vertex_size = find.vertex_size;
			BlockVertUV uv_[16];
			uv_[0].x = verts[0].uv.x, uv_[0].y = verts[0].uv.y;
			uv_[1].x = verts[1].uv.x, uv_[1].y = verts[1].uv.y;
			uv_[2].x = verts[2].uv.x, uv_[2].y = verts[2].uv.y;
			uv_[3].x = verts[3].uv.x, uv_[3].y = verts[3].uv.y;
			uv_[4].x = uv_[0].x + (uv_[1].x - uv_[0].x) * ROUND_DIFF; uv_[4].y = uv_[0].y + (uv_[1].y - uv_[0].y) * ROUND_DIFF;
			uv_[5].x = uv_[0].x + (uv_[1].x - uv_[0].x) * (1 - ROUND_DIFF); uv_[5].y = uv_[0].y + (uv_[1].y - uv_[0].y) * (1 - ROUND_DIFF);
			uv_[6].x = uv_[1].x + (uv_[2].x - uv_[1].x) * ROUND_DIFF; uv_[6].y = uv_[1].y + (uv_[2].y - uv_[1].y) * ROUND_DIFF;
			uv_[7].x = uv_[1].x + (uv_[2].x - uv_[1].x) * (1 - ROUND_DIFF); uv_[7].y = uv_[1].y + (uv_[2].y - uv_[1].y) * (1 - ROUND_DIFF);
			uv_[8].x = uv_[2].x + (uv_[3].x - uv_[2].x) * ROUND_DIFF; uv_[8].y = uv_[2].y + (uv_[3].y - uv_[2].y) * ROUND_DIFF;
			uv_[9].x = uv_[2].x + (uv_[3].x - uv_[2].x) * (1 - ROUND_DIFF); uv_[9].y = uv_[2].y + (uv_[3].y - uv_[2].y) * (1 - ROUND_DIFF);
			uv_[10].x = uv_[3].x + (uv_[0].x - uv_[3].x) * ROUND_DIFF; uv_[10].y = uv_[3].y + (uv_[0].y - uv_[3].y) * ROUND_DIFF;
			uv_[11].x = uv_[3].x + (uv_[0].x - uv_[3].x) * (1 - ROUND_DIFF); uv_[11].y = uv_[3].y + (uv_[0].y - uv_[3].y) * (1 - ROUND_DIFF);
			uv_[12].x = uv_[11].x + (uv_[6].x - uv_[11].x) * ROUND_DIFF; uv_[12].y = uv_[11].y + (uv_[6].y - uv_[11].y) * ROUND_DIFF;
			uv_[13].x = uv_[11].x + (uv_[6].x - uv_[11].x) * (1 - ROUND_DIFF); uv_[13].y = uv_[11].y + (uv_[6].y - uv_[11].y) * (1 - ROUND_DIFF);
			uv_[14].x = uv_[10].x + (uv_[7].x - uv_[10].x) * (1 - ROUND_DIFF); uv_[14].y = uv_[10].y + (uv_[7].y - uv_[10].y) * (1 - ROUND_DIFF);
			uv_[15].x = uv_[10].x + (uv_[7].x - uv_[10].x) * ROUND_DIFF; uv_[15].y = uv_[10].y + (uv_[7].y - uv_[10].y) * ROUND_DIFF;
			for (int i = 0; i < vertex_size; i++)
			{
				auto& vertex_temp = find.base[i];
				verts[i].color = vertex_temp.color;
				verts[i].color.r = vertcolor.x;
				verts[i].color.g = vertcolor.y;
				verts[i].color.b = vertcolor.z;
				verts[i].normal = vertex_temp.normal;
				verts[i].uv.x = uv_[vertex_temp.uv_index].x;
				verts[i].uv.y = uv_[vertex_temp.uv_index].y;
				verts[i].pos.x = vertex_temp.pos.x + (origin_c[0] - origin_zero[0]) * BLOCK_SIZE;
				verts[i].pos.y = vertex_temp.pos.y + (origin_c[1] - origin_zero[1]) * BLOCK_SIZE;
				verts[i].pos.z = vertex_temp.pos.z + (origin_c[2] - origin_zero[2]) * BLOCK_SIZE;
				InitBlockVertLight(verts[i], 255, uvtile);
			}

			mesh.vertices = verts;
			mesh.indices = find.index;
		}
	} while (0);
	if (psubmesh) psubmesh->addGeomFaceLight(mesh, NULL, s_DefaultFaceVertexLights, &facecolor);

}

bool BlockMineStone::coverNeighbor(int curblockdata, SolidBlockMaterial* neighbor, int neighbor_data, DirectionType dir)
{
	if (dir == DIR_POS_Y)
	{
		if (neighbor && (neighbor->getBlockHeight(neighbor_data)) < 0) return false;
		return true;
	}
	else if (dir == DIR_NEG_Y)
	{
		if (neighbor && neighbor->GetAttrRenderType() == BLOCKRENDER_FLUID) return false;
		float h = 0.f;
		if (neighbor) {
			h = neighbor->getBlockHeight(neighbor_data);
		}
		if (h >= 0 && h < 1.0f) return false;
		return true;
	}
	return true;
}

int BlockMineStone::getBlockGeomID(int* idbuf, int* dirbuf, const SectionDataHandler* sectionData, const WCoord& blockpos, World* world)
{
	//分成6个子模型
	for (int i = 0; i < 6; i++)
	{
		idbuf[i] = i;
		dirbuf[i] = DIR_NEG_X;
	}
	bool isInSideBlock = false;
	for (int i = 0; i < inSideBlock.size(); i++)
	{
		if (m_BlockResID == inSideBlock[i])
		{
			isInSideBlock = true;
			break;
		}
	}
	if (isInSideBlock == true)
	{
		for (int i = 5; i < 12; i++)
		{
			idbuf[i] = i;
			dirbuf[i] = DIR_NEG_X;
		}
		return 12;
	}
	return 6;
}

void BlockMineStone::createCollideData(CollisionDetect* coldetect, World* pworld, const WCoord& blockpos)
{
	WCoord pos = blockpos * BLOCK_SIZE;
	coldetect->addObstacle(pos, pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
}



BlockTexElement* BlockMineStoneDiffMtl::getDestroyTexture(Block pblock, BlockTexDesc& desc)
{
	return m_StoneMtls[0]->getTexElement();//m_Mtl->getTexElement();
}

void BlockMineStoneDiffMtl::init(int resid)
{
	ModelBlockMaterial::init(resid);
	ModelBlockMaterial::SetAttrRenderType(BLOCKRENDER_CUBE_MODEL_MIX);
	for (int i = 0; i < WORLDTYPENUM; i++)
	{
		char name[256];
		//侧面x
		sprintf(name, "%s%d_x",m_Def->Texture1.c_str(), i + 1);
		if (!RenderBlockMaterial::checkBlockTextureExistByPath(name, ModelBlockMaterial::m_Def->gamemod))
		{
			m_Mtl_xs[i] = NULL;
			m_Mtl_ys[i] = NULL;
			m_Mtl_zs[i] = NULL;
			m_StoneMtls[i] = NULL;
			continue;
		}
		m_Mtl_xs[i] = g_BlockMtlMgr.createRenderMaterial(name, ModelBlockMaterial::m_Def, GETTEX_NORMAL, BLOCKDRAW_OPAQUE, getMipmapMethod());
		//侧面z
		sprintf(name, "%s%d_z", m_Def->Texture1.c_str(), i + 1);
		m_Mtl_zs[i] = g_BlockMtlMgr.createRenderMaterial(name, ModelBlockMaterial::m_Def, GETTEX_NORMAL, BLOCKDRAW_OPAQUE, getMipmapMethod());
		//顶面底面y
		sprintf(name, "%s%d_y", m_Def->Texture1.c_str(), i + 1);
		m_Mtl_ys[i] = g_BlockMtlMgr.createRenderMaterial(name, ModelBlockMaterial::m_Def, GETTEX_NORMAL, BLOCKDRAW_OPAQUE, getMipmapMethod());
		m_nSpecialLogicType[0] |= RotateMechaStopNoChangePos;
		sprintf(name, "%s%d", m_Def->Texture2.c_str(), i + 1);
		m_StoneMtls[i] = g_BlockMtlMgr.createRenderMaterial(name, ModelBlockMaterial::m_Def, GETTEX_WITHDEFAULT, BLOCKDRAW_STATUE, getMipmapMethod());

#ifndef DEDICATED_SERVER //云服主机调用这里会宕机，云服没有必要执行
		//m_StoneMtls[i]->SetEmissiveTex(name);
		if (m_StoneMtls[i])
		{
			if (m_StoneMtls[i]->isEmissiveTexload())//检查是否有发光贴图，有发光贴图的打开EMISSIVE效果
			{
				m_StoneMtls[i]->getMaterial()->EnableKeyword("EMISSIVE");
			}
			else
			{
				m_StoneMtls[i]->getMaterial();
			}
			m_StoneMtls[i]->getItemMaterial();
		}
#endif

		setMaterialStatus(m_StoneMtls[i], resid);
	}
	char nametmp[256];
	sprintf(nametmp, "%s_proto", m_Def->Texture2.c_str());
	m_Mtl_Proto = g_BlockMtlMgr.createRenderMaterial(nametmp, ModelBlockMaterial::m_Def, GETTEX_NORMAL, BLOCKDRAW_OPAQUE, getMipmapMethod());

#ifndef DEDICATED_SERVER //云服主机调用这里会宕机，云服没有必要执行
	if (m_Mtl_Proto)
	{
		//m_Mtl_Proto->SetEmissiveTex(nametmp);
		if (m_Mtl_Proto->isEmissiveTexload())//检查是否有发光贴图，有发光贴图的打开EMISSIVE效果
		{
			m_Mtl_Proto->getMaterial()->EnableKeyword("EMISSIVE");
		}
		else
		{
			m_Mtl_Proto->getMaterial();
		}
		m_Mtl_Proto->getItemMaterial();
	}
#endif

}

BlockMineStoneDiffMtl::BlockMineStoneDiffMtl()
{
	for (int i = 0; i < WORLDTYPENUM; i++)
	{
		m_Mtl_xs[i] = NULL;
		m_Mtl_ys[i] = NULL;
		m_Mtl_zs[i] = NULL;
		m_StoneMtls[i] = NULL;
	}
}

BlockMineStoneDiffMtl::~BlockMineStoneDiffMtl()
{
	for (int i = 0; i < WORLDTYPENUM; i++)
	{
		ENG_RELEASE(m_Mtl_xs[i]);
		ENG_RELEASE(m_Mtl_ys[i]);
		ENG_RELEASE(m_Mtl_zs[i]);
		ENG_RELEASE(m_StoneMtls[i]);
	}
}

RenderBlockMaterial* BlockMineStoneDiffMtl::getFaceMtlForDiffWorld(int id,int dir)
{
	if (id > 0 && m_Mtl_xs[id] == NULL) id = 0;
	if (dir <= DIR_POS_X)
	{
		return m_Mtl_xs[id];
	}
	else if (dir <= DIR_POS_Z)
	{
		return m_Mtl_zs[id];
	}
	return m_Mtl_ys[id];
}

void BlockMineStoneDiffMtl::createBlockMeshAngle(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh)
{
	auto psection = data.m_SharedSectionData;

	FaceVertexLight faceVertexLight;
	auto pblock = psection->getBlock(blockpos);
	int worldid = 0;
	worldid=data.m_World->getCurMapID();
	bool isNoRoundBlock = false;
	for (int i = 0; i < noRoundBlock.size(); i++)
	{
		if (m_BlockResID == noRoundBlock[i])
		{
			isNoRoundBlock = true;
			break;
		}
	}
	if (isNoRoundBlock == false)
	{
		bool noRound = true;
		if (0 == BlockMaterialMgr::m_BlockShape)
		{
			bool isRound = !poutmesh->isSquareSectionMesh();
			if (isRound)
			{
				for (int d = 0; d < 6; d++)
				{
					DirectionType dir = (DirectionType)d;
					bool isNoClip = false;
					if (checkBlockMeshIsBuild(data, blockpos, d, isNoClip))
					{
						bool flipQuad = psection->getCubeFaceVertexLight(blockpos, dir, faceVertexLight);
						BlockColor facecolor(255, 255, 255, 0);
						RenderBlockMaterial* pmtl = getFaceMtlForDiffWorld(worldid, d);
						if (pmtl == NULL) continue;
						SectionSubMesh* psubmesh = poutmesh->getSubMesh(pmtl);
						BlockGeomMeshInfo mesh;
						getBlockMeshAngleData(data, blockpos, mesh, d, psubmesh && !psubmesh->IsIgnoreTileUV(), pmtl, isNoClip);
						if (!checkRenderForFace(data, blockpos, dir)) continue;
						if (psubmesh)
						{
							psubmesh->addGeomFace(mesh, &blockpos, &facecolor);
						}
					}
				}
				noRound = false;
			}
		}
		if (noRound == true)
		{
			FaceVertexLight faceVertexLight;
			//float block_light[16] = { 0 };
			Block pblock = psection->getBlock(blockpos);
			int curblockdata = pblock.getData();
			for (int d = 0; d < 6; d++)
			{
				DirectionType dir = (DirectionType)d;
				if (!checkRender(data, blockpos, dir)) continue;
				bool flipQuad = psection->getFaceVertexLight(blockpos, dir, faceVertexLight);
				BlockColor facecolor(255, 255, 255, 0);
				RenderBlockMaterial* pmtl = getFaceMtlForDiffWorld(worldid, d);
				if (pmtl == NULL) continue;
				SectionSubMesh* psubmesh = poutmesh->getSubMesh(pmtl);
				BlockGeomMeshInfo mesh;
				BlockMaterial* mtltmp = g_BlockMtlMgr.getMaterial(104);
				if (!mtltmp || mtltmp->getGeom() == NULL) continue;
				mtltmp->getGeom()->getFaceVerts(mesh, dir, 1.0f, 1, DIR_NEG_Z, 0, nullptr, 0, flipQuad);
				if (psubmesh) psubmesh->addGeomFaceLight(mesh, &blockpos, faceVertexLight, &facecolor, pmtl->getUVTile());
			}
		}
	}
	
}

void BlockMineStoneDiffMtl::createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh)
{
	BlockGeomTemplate* geom = getGeom(data.m_LODLevel);
	if (!geom) return;
	auto psection = data.m_SharedSectionData;
	int worldid = 0;
	worldid = data.m_World->getCurMapID();
	if (m_StoneMtls[worldid] == NULL)
	{
		worldid = 0;
	}
	bool showBuf[6];
	checkShowFace(data, blockpos, showBuf);
	createBlockMeshAngle(data, blockpos, poutmesh);
	Rainbow::ColorRGBAf verts_light[1] = { Rainbow::ColorRGBAf::white };
	psection->getBlockVertexLight(blockpos, verts_light);
	Block pblock = psection->getBlock(blockpos);
	int blockdata = pblock.getData();
	int idbuf[32];
	int dirbuf[32];
	int ngeom = getBlockGeomID(idbuf, dirbuf, psection, blockpos, data.m_World);
	BlockGeomMeshInfo meshinfo;
	RenderBlockMaterial* pmtl = m_StoneMtls[worldid]; //getGeomMtl(psection, blockpos);
	if (pmtl == NULL)
		return;
	SectionSubMesh* psubmesh = poutmesh->getSubMesh(pmtl);
	int dir = dirbuf[0] & 0xffff;
	int mirrortype = (dirbuf[0] >> 16) & 3;
	//ModelBlockMaterial::getGeom()->getFaceVerts(meshinfo, idbuf[0], 1.0f, 0, dir, mirrortype);
	for (int i = 0; i < 6; i++)
	{
		if (!showBuf[i]) continue;
		geom->getFaceVerts(meshinfo, idbuf[i], 1.0f, 0, dir, mirrortype);
		psubmesh->addGeomBlockLight(meshinfo, &blockpos, verts_light, NULL, pmtl->getUVTile());
		
	}
	
}

SectionMesh* BlockMineStoneDiffMtl::createBlockProtoMesh(int protodata)
{
	SectionMesh* pmesh = ENG_NEW(SectionMesh)();
	int blockdata = protodata;
	float blockheight = 1.0f;
	RenderBlockMaterial* smtl = NULL;
	if (m_Mtl_Proto)
	{
		smtl = m_Mtl_Proto;
	}
	else
	{
		smtl = m_StoneMtls[0];
	}
	if (!smtl) return pmesh;
	SectionSubMesh* stonemesh = pmesh->getSubMesh(smtl, true);
	BlockGeomMeshInfo meshinfo;
	for (int i = 0; i < 6; i++)
	{
		getGeom(0)->getFaceVerts(meshinfo, i, 1.0f, 0, DIR_NEG_X);
		if (stonemesh)
			stonemesh->addGeomFaceLight(meshinfo, NULL, s_DefaultFaceVertexLights, NULL, smtl->getUVTile());
	}

	if (1 == BlockMaterialMgr::m_BlockShape)//直角
	{
		for (int d = 0; d < 6; d++)
		{
			DirectionType dir = (DirectionType)d;

			BlockColor facecolor(255, 255, 255, 0);
			RenderBlockMaterial* rbmtl = getFaceMtlForDiffWorld(0, d);
			if (!rbmtl)
			{
				continue;
			}
			SectionSubMesh* psubmesh = pmesh->getSubMesh(rbmtl, true);
			BlockGeomMeshInfo meshinfo;
			BlockMaterial* mtltmp = g_BlockMtlMgr.getMaterial(104);
			if (!mtltmp || mtltmp->getGeom(0) == NULL) continue;
			mtltmp->getGeom(0)->getFaceVerts(meshinfo, dir, 1.0f, 1);
			if (psubmesh)
				psubmesh->addGeomFaceLight(meshinfo, NULL, s_DefaultFaceVertexLights, &facecolor);
		}
	}
	else
	{
		for (int d = 0; d < 6; d++)
		{
			DirectionType dir = (DirectionType)d;

			BlockColor facecolor(255, 255, 255, 0);
			RenderBlockMaterial* rbmtl = getFaceMtlForDiffWorld(0, d);
			if (rbmtl == NULL) continue;
			SectionSubMesh* psubmesh = pmesh->getSubMesh(rbmtl, true);
			setSubMesh(psubmesh, rbmtl, dir);
		}
	}
	return pmesh;
}

void BlockMineStoneRune::spawnMonster(World* pworld, const WCoord& basepos)
{
	if (!pworld)
		return;

	int monsterid = 3892;//3892:符文怪
	const MonsterDef* def = GetDefManagerProxy()->getMonsterDef(monsterid);
	if (!def)
		return;
	ActorManager* actormgr = dynamic_cast<ActorManager*>(pworld->getActorMgr());
	if (!actormgr)
		return;
	//40%概率产生
	int pro = GenRandomInt(0, 100);
	if (pro > 40)
		return;
	int num = 3;//GenRandomInt(3, 5);
	WCoord createPos = BlockBottomCenter(basepos);
	for (int i = 0; i < num; i++)
		actormgr->spawnMob(createPos, monsterid, false, false);
	//ClientMob *mob = 
	//if(mob) mob->playSaySound();
}
/*
根据镐的材料不同，掉落各个等级的符文石概率也不同
- 铜镐：低级75%，中级20%，高级5%   100
- 铁镐：低级40%，中级45%，高级15%  100
- 钛镐：低级30%，中级40%，高级30%  100
- 钻头：低级15%，中级30%，高级55%  100
- 符文怪：产生概率40%，数量3-5只
*/

void BlockMineStoneRune::dropBlockAsItemWithToolId(World* pworld, const WCoord& blockpos, int blockdata, BLOCK_MINE_TYPE droptype, float chance, int useToolId, int uin)
{
	const ToolDef* tooldef = GetDefManagerProxy()->getToolDef(useToolId);
	if (tooldef && tooldef->Type == 2 && tooldef->Level >= 2) {//铜镐以上 才可以掉符文石
		int proArray[] = { 75,20,5,  40,45,15,  30,40,30,  15,30,55 };
		int startIndex = (tooldef->Level - 2) * 3;
		if (startIndex > 9) startIndex = 9;
		int* base = ((int*)proArray) + startIndex;
		int index = SelectFromOddsArray(base, 3);
		if (index >= 0 && index <= 2) {//0,1,2
			int category = getRuneStoneCategory(getBlockResID());
			if (category != RuneStoneCategoryNone) {
				int itemid = getUnAuthItemIDByRuneLevelAndCategory(index + 1, category);
				doDropItem(pworld, blockpos, itemid); //
			}
		}
	}
	spawnMonster(pworld, blockpos);
}