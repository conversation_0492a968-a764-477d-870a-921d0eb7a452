#pragma once
#include "BlockMaterial.h"

class BlockModelHerb : public ModelBlockMaterial //tolua_exports
{ //tolua_exports
	DECLARE_BLOCKMATERIAL(BlockModelHerb)
public:
	virtual void init(int resid) override;
	//tolua_begin
	BlockModelHerb() : m_RenderInPot(0), m_RenderPotOffsetY(0.0f) {}
	virtual bool canStayOnPos(WorldProxy *pworld, const WCoord &blockpos);
	virtual void onNotify(World *pworld, const WCoord &blockpos, int blockid);
	virtual bool canPutOntoPos(WorldProxy *pworld, const WCoord &blockpos);
	virtual void dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata = 0, BLOCK_MINE_TYPE droptype = BLOCK_MINE_NOTOOL, float chance = 1.0f, int uin = -1);
	void generateFlowerPotMesh(const SectionDataHandler* sectionData, const WCoord &blockpos, SectionMesh *poutmesh, int meshId, Rainbow::Vector3f offset);
	virtual void createBlockMesh(const BuildSectionMeshData& data, const WCoord &blockpos, SectionMesh *poutmesh) override;
	virtual bool canAttachedToMecha(int curblockdata, DirectionType dir, int neighbor_id, int neighbor_data) override
	{
		return dir == DIR_NEG_Y;
	}

	virtual bool canOnlyOnDirt()
	{
		return true;
	}

	void setRenderInPot(int n, float offsety = 0) //0: 正常渲染, 1: 第一格, 2: 第二格
	{
		m_RenderInPot = n;
		m_RenderPotOffsetY = offsety;
	}
	//virtual bool isSolid()
	//{
	//	return false;
	//}
	//virtual BlockDrawType getDrawType() override
	//{
	//	return BLOCKDRAW_GRASS;
	//}
	//tolua_end
private:
	virtual int getBlockGeomID(int *idbuf, int *dirbuf, const SectionDataHandler* sectionData, const WCoord &blockpos, World* world);
	virtual void createCollideData(CollisionDetect *coldetect, World *pworld, const WCoord &blockpos);
	virtual void initDrawType() override;

	int m_RenderInPot;
	float m_RenderPotOffsetY;
}; //tolua_exports
