
#include "BlockOneQuarter.h"
#include "IClientPlayer.h"
#include "BlockGeom.h"
#include "ShareRenderMaterial.h"
#include "container_onequarterblock.h"
#include "Collision.h"
#include "SectionMesh.h"
#include "SandboxEventDispatcherManager.h"
#include "world.h"

using namespace MNSandbox;

IMPLEMENT_BLOCKMATERIAL(BlockOneQuarter)
BlockOneQuarter::BlockOneQuarter()
{

}

BlockOneQuarter::~BlockOneQuarter()
{

}

void BlockOneQuarter::init(int resid)
{
	ModelBlockMaterial::init(resid);
	//属性初始化
	SetToggle(BlockToggle_HasContainer, true);
}
bool BlockOneQuarter::canPlacedAgain(World *pworld, int placeblockid, const Rainbow::Vector3f &colpoint, const WCoord &blockpos, bool placeinto, int face)
{
	if (m_Def && m_Def->ID != placeblockid)
		return false;

	if (!pworld || !pworld->getContainerMgr())
		return false;

	ContainerOneQuarterBlock *container = dynamic_cast<ContainerOneQuarterBlock *>(pworld->getContainerMgr()->getContainer(blockpos));
	if (container == NULL)
		return false;

	return container->canPlacedAgain(colpoint, placeinto, face);
}

void BlockOneQuarter::onBlockPlacedBy(World *pworld, const WCoord &blockpos, IClientPlayer *player, const Rainbow::Vector3f &colpoint, bool placeinto, int face)
{
	if (!player)
		return;

	if (!pworld || !pworld->getContainerMgr())
		return;

	ContainerOneQuarterBlock *container = dynamic_cast<ContainerOneQuarterBlock *>(pworld->getContainerMgr()->getContainer(blockpos));
	if (container == NULL)
	{
		container = SANDBOX_NEW(ContainerOneQuarterBlock, blockpos);
		int blockDir = player->getPlaceDirToBlock(blockpos);
		container->addBlockDataByPlace(blockDir, colpoint, placeinto, face);
		pworld->getContainerMgr()->spawnContainer(container);
	}
	else
	{
		container->addBlockDataByPlace(0, colpoint, placeinto, face);
		pworld->markBlockForUpdate(blockpos, true);
		SandboxEventDispatcherManager::GetGlobalInstance().Emit("StatisticRainforest_onCondition_AddBlock",SandboxContext(NULL)
			.SetData_Number("blockid", m_BlockResID));
	}

	//int blockdata = player->getCurPlaceDir();

	//pworld->setBlockData(blockpos, blockdata, 3);
}

void BlockOneQuarter::onBlockAdded(World *pworld, const WCoord &blockpos)
{
    ModelBlockMaterial::onBlockAdded(pworld, blockpos);
	SandboxEventDispatcherManager::GetGlobalInstance().Emit("StatisticRainforest_onCondition_AddBlock", SandboxContext(NULL)
		.SetData_Number("blockid", m_BlockResID));
}

void BlockOneQuarter::onBlockRemoved(World *pworld, const WCoord &blockpos, int blockid, int blockdata)
{
	ModelBlockMaterial::onBlockRemoved(pworld, blockpos, blockid, blockdata);

	if (!pworld || !pworld->getContainerMgr())
		return;

	ContainerOneQuarterBlock *container = dynamic_cast<ContainerOneQuarterBlock *>(pworld->getContainerMgr()->getContainer(blockpos));
	if (container)
	{
		SandboxEventDispatcherManager::GetGlobalInstance().Emit("StatisticRainforest_onCondition_RemoveBlock",SandboxContext(NULL)
			.SetData_Number("blockid", m_BlockResID)
			.SetData_Number("num", container->getDropNum()));
		pworld->getContainerMgr()->destroyContainer(blockpos);
	}
	else
	{
		SandboxEventDispatcherManager::GetGlobalInstance().Emit("StatisticRainforest_onCondition_RemoveBlock", SandboxContext(NULL)
			.SetData_Number("blockid", m_BlockResID));
	}
}

void BlockOneQuarter::createCollideData(CollisionDetect *coldetect, World *pworld, const WCoord &blockpos)
{
	if (!pworld || !pworld->getContainerMgr())
		return;

	WCoord pos = blockpos * BLOCK_SIZE;
	ContainerOneQuarterBlock *container = dynamic_cast<ContainerOneQuarterBlock *>(pworld->getContainerMgr()->getContainer(blockpos));
	if (!container || !container->createCollideData(coldetect, pos))
	{
		coldetect->addObstacle(pos, pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
	}
}


void BlockOneQuarter::createBlockMeshWithContainer(const BuildSectionMeshData& data, const WCoord &blockpos, SectionMesh *poutmesh, WorldContainer *pContainer){
	//createBlockMesh(psection,blockpos,poutmesh);
	BlockGeomTemplate* geom = getGeom(data.m_LODLevel);
	if (!geom)
		return;

	auto psection = data.m_SharedSectionData;
	bool hastbn = false;
	Rainbow::ColorRGBAf verts_light[1] = { Rainbow::ColorRGBAf::black };
	psection->getBlockVertexLight(blockpos, verts_light);
	int idbuf[32];
	int dirbuf[32];
	int ngeom = getBlockGeomIDWithContainer(idbuf, dirbuf, psection, blockpos, data.m_World, pContainer);
	BlockGeomMeshInfo meshinfo;
	RenderBlockMaterial *pmtl = getGeomMtl(psection, blockpos, data.m_World);
	SectionSubMesh *psubmesh = poutmesh->getSubMesh(pmtl);
	Block pblock = psection->getBlock(blockpos);

	for(int i=0; i<ngeom; i++)
	{

		int dir = dirbuf[i] & 0xffff;
		int mirrortype = (dirbuf[i] >> 16) & 3;

		if(mirrortype > 0) geom->getFaceVerts(meshinfo, idbuf[i], 1.0f, 0, dir, mirrortype,NULL,0, hastbn);
		else geom->getFaceVerts(meshinfo, idbuf[i], 1.0f, 0, dir, 0, NULL, 0, hastbn);

		if (isColorableBlock() && !isUseCustomModel())
		{
			BlockColor bv = getBlockColor(pblock.getData());
			bv.a = 0;
			psubmesh->addGeomBlockLight(meshinfo, &blockpos, verts_light, &bv, pmtl->getUVTile());
		}
		else
		{
			psubmesh->addGeomBlockLight(meshinfo, &blockpos, verts_light, NULL, pmtl->getUVTile());
		}
	}
}


int BlockOneQuarter::getBlockGeomIDWithContainer(int *idbuf, int *dirbuf, const SectionDataHandler* sectionData, const WCoord &blockpos, World* world, WorldContainer *pContainer)
{
	Block pblock = sectionData->getBlock(blockpos);

	idbuf[0] = 0;
	dirbuf[0] = 0;

	WorldContainer*  con = pContainer;
	if(!con){
		if (world && world->getContainerMgr())
			con = world->getContainerMgr()->getContainer(sectionData->getOrigin()+blockpos);
	}
	ContainerOneQuarterBlock *container = dynamic_cast<ContainerOneQuarterBlock *>(con);
	if (container){
		return container->getBlockGeomID(idbuf, dirbuf);
	}
	return 1;
}

int BlockOneQuarter::getBlockGeomID(int *idbuf, int *dirbuf, const SectionDataHandler* sectionData, const WCoord &blockpos, World* world)
{
	return getBlockGeomIDWithContainer(idbuf,dirbuf, sectionData,blockpos, world, NULL);
}

void BlockOneQuarter::dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata, BLOCK_MINE_TYPE droptype, float chance, int uin/* = -1 */)
{
	if (!pworld || !pworld->getContainerMgr() || !m_Def)
		return;
	
	ContainerOneQuarterBlock *container = dynamic_cast<ContainerOneQuarterBlock *>(pworld->getContainerMgr()->getContainer(blockpos));
	if (container)
	{
		//container->getDropItems(m_dropItems);
		int num = container->getDropNum();
		doDropItem(pworld, blockpos, GetBlockDef()->ID, num);
	}
}