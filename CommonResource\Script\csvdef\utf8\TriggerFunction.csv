函数ID,名称,返回类型【类型（parm表可查），区分左右值（0=左右值|1=仅右值），局部触发器中是否显示（1=显示（默认）|0=隐藏）】,返回类型名称,UI分类,UI显示类型名称,描述,描述备注,参数1,参数1默认值,参数2,参数2默认值,参数3,参数3默认值,参数4,参数4默认值,参数5,参数5默认值,参数6,参数6默认值,参数7,参数7默认值,参数8,参数8默认值,参数9,参数9默认值,参数10,参数10默认值,触发器显示（0=默认全部可见 1=仅开发者可见 2=仅999渠道可见）,触发器提示版本号（填写版本号的第二位）
ID,Name,ReturnType,,Type,TypeName,Desc,,Param1,DefaultParam1,Param2,DefaultParam2,Param3,DefaultParam3,Param4,<PERSON>faultParam4,Param5,<PERSON><PERSON>ultParam5,<PERSON><PERSON>6,<PERSON><PERSON>ultParam6,Para<PERSON>7,<PERSON><PERSON>ultParam7,Para<PERSON>8,<PERSON><PERSON>ultParam8,Param9,DefaultParam9,Param10,DefaultParam10,Display,Version
10001,位置转化为区域,"1001,1",区域,1,通用,以|@1|为中心点，尺寸为（|@2|，|@3|，|@4|）的区域,以【位置】为中心点，尺寸为（长【数值】，高【数值】，宽【数值】）的区域,"1002,1",,"1005,1",1|0,"1005,1",1|0,"1005,1",1|0,,,,,,,,,,,,,0,
10002,区域偏移,"1001,1",区域,1,通用,@1|沿坐标轴偏移（|@2|，|@3|，|@4|）,【区域】沿坐标轴偏移（【数值】，【数值】，【数值】）,"1001,1",,"1005,1",1|0,"1005,1",1|0,"1005,1",1|0,,,,,,,,,,,,,0,
10003,扩大区域,"1001,1",区域,1,通用,将|@1|三轴大小分别扩大X|@2|，Y|@3|，Z|@4|格,将【区域】三轴大小分别扩大【数值】，【数值】，【数值】格,"1001,1",,"1005,1",1|0,"1005,1",1|0,"1005,1",1|0,,,,,,,,,,,,,0,
10005,指定坐标位置,"1002,1",位置,1,通用,坐标值 X|@1|Y|@2|Z|@3|的位置,坐标值 X【数值】Y【数值】Z【数值】的位置,"1005,1",1|0,"1005,1",1|0,"1005,1",1|0,,,,,,,,,,,,,,,0,
10006,位置偏移（改变原位置）,"1002,1",位置,1,通用,@1|沿坐标轴偏移（|@2|，|@3|，|@4|）,【位置】沿坐标轴偏移（【数值】，【数值】，【数值】）,"1002,1",,"1005,1",1|0,"1005,1",1|0,"1005,1",1|0,,,,,,,,,,,,,0,
10007,区域的中央位置,"1002,1",位置,1,通用,@1|的中央位置,【区域】的中央位置,"1001,1",,,,,,,,,,,,,,,,,,,,0,
10008,区域中的随机位置,"1002,1",位置,1,通用,@1|的随机位置,【区域】的随机位置,"1001,1",,,,,,,,,,,,,,,,,,,,0,
10009,计时器时间,"1005,1",数值,1,通用,@1|的时间,【计时器】的时间,"1040,1",,,,,,,,,,,,,,,,,,,,0,
10010,指定方向射线的长度,"1005,1",数值,1,通用,以|@1|起，向|@2|投射|@3|格的射线，返回阻挡方块到起始位置之间的射线长度,以【位置】起，向【位置】投射【数值】格的射线，返回阻挡方块到起始位置之间的射线长度,"1002,1",,"1002,1",,"1005,1",1|0,,,,,,,,,,,,,,,0,
10011,字符串拼接,"1010,1",字符串,1,通用,@1|和|@2,【字符串】和【字符串】,"1010,1",,"1010,1",,,,,,,,,,,,,,,,,,0,
10012,数值转化为字符串,"1010,1",字符串,1,通用,@1|转化为字符串,【数值】转化为字符串,"1005,1",,,,,,,,,,,,,,,,,,,,0,
10013,布尔值比较,"1014,1",布尔值,1,通用,@1||@2||@3,【布尔值】【是否】【布尔值】,"1014,1",,"1012,1",5|90001,"1014,1",,,,,,,,,,,,,,,,0,
10014,数值比较,"1014,1",布尔值,1,通用,@1||@2||@3,【数值】【比较计算符】【数值】,"1005,1",,"1016,1",5|120001,"1005,1",,,,,,,,,,,,,,,,0,
10015,射线打到的方块类型,"1025,1",方块类型,1,通用,从|@1|，向|@2|发射|@3|格，阻挡射线的方块类型,从【位置】，向【方向】发射【数值】格，阻挡射线的方块类型,"1002,1",,"1003,1",,"1005,1",1|0,,,,,,,,,,,,,,,0,
10016,相对三轴坐标系方向,"1047,1",方向,1,通用,@1|面向的X|@2|，Y|@3|，Z|@4|的方向,【对象】面向的X【数值】，Y【数值】，Z【数值】的方向。,"1048,1",,"1005,1",1|0,"1005,1",1|0,"1005,1",1|0,,,,,,,,,,,,,0,
10017,相对球坐标系方向,"1047,1",方向,1,通用,@1|面向的方位角|@2|，仰角|@3|的方向,【对象】面向的方位角【数值】，仰角【数值】的方向。,"1048,1",,"1005,1",1|0,"1005,1",1|0,,,,,,,,,,,,,,,0,
10018,选择玩家对象,"1048,1",对象,1,通用,选择|@1|对象,选择【玩家】为对象,"1018,1",,,,,,,,,,,,,,,,,,,,0,
10019,选择生物对象,"1048,1",对象,1,通用,选择|@1|对象,选择【生物】为对象,"1031,1",,,,,,,,,,,,,,,,,,,,0,
10020,选择投掷物对象,"1048,1",对象,1,通用,选择|@1|对象,选择【投掷物】为对象,"1044,1",,,,,,,,,,,,,,,,,,,,0,
10021,选择掉落物对象,"1048,1",对象,1,通用,选择|@1|对象,选择【掉落物】为对象,"1030,1",,,,,,,,,,,,,,,,,,,,0,
10022,音调转化为数值,"1005,1",数值,1,通用,@1|转化为数值,【音调】转化为数值,"1049,1",,,,,,,,,,,,,,,,,,,,0,
10023,字符串比较,"1014,1",布尔值,1,通用,@1||@2||@3,【字符串】【是否】【字符串】,"1010,1",,"1012,1",5|90001,"1010,1",,,,,,,,,,,,,,,,0,
10024,位置偏移（不改变原位置）,"1002,1",位置,1,通用,@1|沿坐标轴偏移（|@2|，|@3|，|@4|）,【位置】沿坐标轴偏移（【数值】，【数值】，【数值】）,"1002,1",,"1005,1",1|0,"1005,1",1|0,"1005,1",1|0,,,,,,,,,,,,,0,
10025,布尔值,"1053,1",任意值,1,通用,|@1,【布尔值】,"1014,1",,,,,,,,,,,,,,,,,,,,,
10026,数值,"1053,1",任意值,1,通用,|@1,【数值】,"1005,1",,,,,,,,,,,,,,,,,,,,,
10027,字符串,"1053,1",任意值,1,通用,|@1,【字符串】,"1010,1",,,,,,,,,,,,,,,,,,,,,
10028,外观类型,"1053,1",任意值,1,通用,|@1,【外观类型】,"1060,1",,,,,,,,,,,,,,,,,,,,,
10029,布尔值组,"1061,1",任意组,1,通用,|@1,【布尔值组】,"1066,1",,,,,,,,,,,,,,,,,,,,,0
10030,数值组,"1061,1",任意组,1,通用,|@1,【数值组】,"1064,1",,,,,,,,,,,,,,,,,,,,,0
10031,字符串组,"1061,1",任意组,1,通用,|@1,【字符串组】,"1065,1",,,,,,,,,,,,,,,,,,,,,0
10032,获取任意组中值的数量,"1005,1",数值,1,通用,获取|@1|中值的数量,获取【任意组】中值的数量,"1061,1",,,,,,,,,,,,,,,,,,,,,0
10033,获取任意组中任意值的数量,"1005,1",数值,1,通用,获取|@1|中|@2|的数量,获取【任意组】中【任意值】的数量,"1061,1",,"1053,1",,,,,,,,,,,,,,,,,,,0
10034,获取任意组中任意值的编号,"1005,1",数值,1,通用,获取|@1|中|@2|的编号,获取【任意组】中【任意值】的编号,"1061,1",,"1053,1",,,,,,,,,,,,,,,,,,,0
10035,任意组中存在任意值判断,"1014,1",布尔值,1,通用,@1|中存在|@2,【任意组】中存在【任意值】,"1061,1",,"1053,1",,,,,,,,,,,,,,,,,,,0
10036,任意组指定编号存在值判断,"1014,1",布尔值,1,通用,@1|中编号为|@2|的位置存在值,【任意组】中编号为【数值】的位置存在值,"1061,1",,"1005,1",1|1,,,,,,,,,,,,,,,,,,
10037,截取字符串,"1010,1",字符串,1,通用,截取|@1|的|@2|- |@3|位文本,截取【字符串】的【数值】- 【数值】位文本,"1010,1",6|550001,"1005,1",1|1,"1005,1",1|5,,,,,,,,,,,,,,,,2
10038,字符串转化为数值,"1005,1",数值,1,通用,@1|转化为数值,【字符串】转化为数值,"1010,1",,,,,,,,,,,,,,,,,,,,,2
10039,获取主机设备时间,"1005,1",数值,1,通用,获取设备的|@1|时间,获取设备的【时间单位】时间,"1078,1",5|540003,,,,,,,,,,,,,,,,,,,,2
10040,获取完整主机设备时间,"1010,1",字符串,1,通用,获取完整设备时间,获取完整设备时间,,,,,,,,,,,,,,,,,,,,,,2
10041,随机方块类型,"1025,1",方块类型,1,通用,随机方块类型,随机方块类型,,,,,,,,,,,,,,,,,,,,,,2
10042,分割字符串,"1010,1",字符串,1,通用,分割|@1|，分割符为|@2|，截取分割后的第|@3|段,分割【字符串】，分割符为【字符串】，截取分割后的第【数值】段,"1010,1",6|550001,"1010,1",,"1005,1",1|1,,,,,,,,,,,,,,,,
10043,获取指定标记的字符串,"1010,1",字符串,1,通用,获取|@1|，键名为|@2|的内容,获取【字符串】，键名为【字符串】的内容,"1010,1",3|300004,"1010,1",,,,,,,,,,,,,,,,,,,
10044,获取游戏服务器时间,"1005,1",数值,1,通用,获取游戏服务器时间的|@1|时间,获取游戏服务器时间的【时间单位】时间,"1078,1",5|540003,,,,,,,,,,,,,,,,,,,0,21
10045,转换时间戳为时间单位,"1005,1",数值,1,通用,将时间戳|@1|转换为|@2,将时间戳【数值】转换为【时间单位】,"1005,1",,"1078,1",5|540003,,,,,,,,,,,,,,,,,0,21
10046,获取完整服务器时间,"1010,1",字符串,1,通用,获取完整服务器时间,获取完整服务器时间,,,,,,,,,,,,,,,,,,,,,0,21
10047,转换时间戳为完整时间,"1010,1",字符串,1,通用,将时间戳|@1|转换为完整时间,将时间戳【数值】转换为完整时间,"1005,1",,,,,,,,,,,,,,,,,,,,0,21
20001,数值运算,"1005,1",数值,2,数学,@1||@2||@3,【数值】【数学运算符】【数值】,"1005,1",,"1017,1",5|130001,"1005,1",,,,,,,,,,,,,,,,0,
20002,取余,"1005,1",数值,2,数学,@1|Mod|@2,【数值】除以【数值】所得余数,"1005,1",,"1005,1",,,,,,,,,,,,,,,,,,0,
20003,随机数,"1005,1",数值,2,数学,生成|@1|与|@2|之间的随机数,生成【数值】与【数值】之间的随机数,"1005,1",1|1,"1005,1",1|10,,,,,,,,,,,,,,,,,0,
20004,最大值,"1005,1",数值,2,数学,@1|与|@2|的|@3,【数值】与【数值】的【最大最小值】,"1005,1",,"1005,1",,"1015,1",,,,,,,,,,,,,,,,0,
20005,整除,"1005,1",数值,2,数学,@1|整除|@2,【数值】整除【数值】,"1005,1",,"1005,1",,,,,,,,,,,,,,,,,,0,
20006,位置的坐标值,"1005,1",数值,2,数学,@1|的|@2|坐标值,【位置】的【坐标分量】坐标值,"1002,1",,"1006,1",,,,,,,,,,,,,,,,,,0,
20007,位置间的水平角度,"1005,1",数值,2,数学,@1|与|@2|间的水平角度,【位置】与【位置】间的水平角度,"1002,1",,"1002,1",,,,,,,,,,,,,,,,,,0,
20008,位置间的垂直角度,"1005,1",数值,2,数学,@1|与|@2|间的垂直角度,【位置】与【位置】间的垂直角度,"1002,1",,"1002,1",,,,,,,,,,,,,,,,,,0,
20009,幂运算,"1005,1",数值,2,数学,@1|的|@2|次幂,【数值】的【数值】次幂,"1005,1",,"1005,1",,,,,,,,,,,,,,,,,,0,
20010,开方,"1005,1",数值,2,数学,@1|开|@2|次方,【数值】开【数值】次方,"1005,1",,"1005,1",,,,,,,,,,,,,,,,,,0,
20011,绝对值,"1005,1",数值,2,数学,@1|的绝对值,【数值】的绝对值,"1005,1",,,,,,,,,,,,,,,,,,,,0,
20012,三角函数,"1005,1",数值,2,数学,@1||@2||@3,【三角函数】【数值】【角度单位】,"1043,1",,"1005,1",,"1007,1",,,,,,,,,,,,,,,,0,
20013,固定底对数函数,"1005,1",数值,2,数学,@1||@2,【固定底对数】【数值】,"1008,1",,"1005,1",,,,,,,,,,,,,,,,,,0,
20014,任意底对数函数,"1005,1",数值,2,数学,Log|@1||@2,Log【数值】【数值】,"1005,1",,"1005,1",,,,,,,,,,,,,,,,,,0,
20015,取整,"1005,1",数值,2,数学,对|@1|进行取整，方式为|@2,对【数值】进行取整，方式为【取整方式】,"1005,1",,"1009,1",5|70002,,,,,,,,,,,,,,,,,0,
20016,角度与弧度转换,"1005,1",数值,2,数学,将|@1|转换为|@2,将【数值】转换为【角度单位】,"1005,1",,"1007,1",5|50001,,,,,,,,,,,,,,,,,0,
20017,逻辑与,"1014,1",布尔值,2,数学,@1|与|@2,【布尔值】与【布尔值】,"1014,1",,"1014,1",,,,,,,,,,,,,,,,,,0,
20018,逻辑或,"1014,1",布尔值,2,数学,@1|或|@2,【布尔值】或【布尔值】,"1014,1",,"1014,1",,,,,,,,,,,,,,,,,,0,
20019,逻辑异或,"1014,1",布尔值,2,数学,@1|异或|@2,【布尔值】异或【布尔值】,"1014,1",,"1014,1",,,,,,,,,,,,,,,,,,0,
20020,逻辑非,"1014,1",布尔值,2,数学,非|@1,非【布尔值】,"1014,1",,,,,,,,,,,,,,,,,,,,0,
20021,任意值比较,"1014,1",布尔值,2,数学,@1||@2||@3,【任意值】【是否】【任意值】,"1053,1",,"1012,1",5|90001,"1053,1",,,,,,,,,,,,,,,,0,
30001,当前游戏时刻,"1005,0",数值,3,游戏,当前游戏时刻,当前游戏时刻,,,,,,,,,,,,,,,,,,,,,0,
30002,指定排名的队伍,"1023,1",队伍,3,游戏,排名为|@1|的队伍,排名为【数值】的队伍,"1005,1",1|1,,,,,,,,,,,,,,,,,,,0,
30003,当前游戏重力倍数,"1005,0",数值,3,游戏,当前游戏重力倍数,当前游戏重力倍数,,,,,,,,,,,,,,,,,,,,,0,
40001,队伍的属性,"1005,0",数值,4,队伍,@1|的|@2,【队伍】的【队伍属性】,"1023,1",3|50018,"1024,1",,,,,,,,,,,,,,,,,,0,
40002,队伍比较,"1014,1",布尔值,4,队伍,@1||@2||@3,【队伍】【是否】【队伍】,"1023,1",3|50018,"1012,1",5|90001,"1023,1",5|170001,,,,,,,,,,,,,,,0,
50001,玩家的位置,"1002,0",位置,5,玩家,@1|的位置,【玩家】的位置,"1018,1",6|250001,,,,,,,,,,,,,,,,,,,0,
50002,获取玩家准星位置,"1002,1",位置,5,玩家,@1|的准星位置,【玩家】的准星位置,"1018,1",6|250001,,,,,,,,,,,,,,,,,,,0,
50003,玩家的属性,"1005,0",数值,5,玩家,@1|的|@2,【玩家】的【玩家属性】,"1018,1",6|250001,"1020,1",,,,,,,,,,,,,,,,,,0,
50004,拥有道具数量,"1005,1",数值,5,玩家,@1|拥有的|@2|的数量,【玩家】拥有的【道具类型】的数量,"1018,1",6|250001,"1028,1",,,,,,,,,,,,,,,,,,0,
50006,玩家面向的角度,"1005,1",数值,5,玩家,@1|面向的角度,【玩家】面向的角度,"1018,1",6|250001,,,,,,,,,,,,,,,,,,,0,
50007,玩家的名字,"1010,1",字符串,5,玩家,@1|的名字,【玩家】的名字,"1018,1",6|250001,,,,,,,,,,,,,,,,,,,0,
50008,个体判断,"1014,1",布尔值,5,玩家,@1||@2||@3,【玩家】【是否】【玩家】,"1018,1",6|250001,"1012,1",5|90001,"1018,1",,,,,,,,,,,,,,,,0,
50009,处于玩家组判断,"1014,1",布尔值,5,玩家,@1||@2|处于|@3|中,【玩家】【是否】处于【玩家组】中,"1018,1",6|250001,"1012,1",5|90001,"1019,1",,,,,,,,,,,,,,,,0,
50010,处于区域判断,"1014,1",布尔值,5,玩家,@1||@2|处于|@3|中,【玩家】【是否】处于【区域】中,"1018,1",6|250001,"1012,1",5|90001,"1001,1",,,,,,,,,,,,,,,,0,
50011,状态判断,"1014,1",布尔值,5,玩家,@1||@2|拥有|@3|的状态,【玩家】【是否】拥有【状态】的状态,"1018,1",6|250001,"1012,1",5|90001,"1037,1",,,,,,,,,,,,,,,,0,
50012,房主,"1018,1",玩家,5,玩家,房主,房主,,,,,,,,,,,,,,,,,,,,,0,
50018,玩家的队伍,"1023,1",队伍,5,玩家,@1|的队伍,【玩家】的队伍,"1018,1",,,,,,,,,,,,,,,,,,,,0,
50019,玩家的私有变量（区域）,"1001,0",区域,5,玩家,@1|的|@2|（区域）,【玩家】的【私有变量】（区域）,"1018,1",6|250001,"1042,1",,,,,,,,,,,,,,,,,,0,
50020,玩家的私有变量（位置）,"1002,0",位置,5,玩家,@1|的|@2|（位置）,【玩家】的【私有变量】（位置）,"1018,1",6|250001,"1042,1",,,,,,,,,,,,,,,,,,0,
50021,玩家的私有变量（数值）,"1005,0",数值,5,玩家,@1|的|@2|（数值）,【玩家】的【私有变量】（数值）,"1018,1",6|250001,"1042,1",,,,,,,,,,,,,,,,,,0,
50022,玩家的私有变量（字符串）,"1010,0",字符串,5,玩家,@1|的|@2|（字符串）,【玩家】的【私有变量】（字符串）,"1018,1",6|250001,"1042,1",,,,,,,,,,,,,,,,,,0,
50023,玩家的私有变量（布尔值）,"1014,0",布尔值,5,玩家,@1|的|@2|（布尔值）,【玩家】的【私有变量】（布尔值）,"1018,1",6|250001,"1042,1",,,,,,,,,,,,,,,,,,0,
50024,玩家的私有变量（玩家）,"1018,0",玩家,5,玩家,@1|的|@2|（玩家）,【玩家】的【私有变量】（玩家）,"1018,1",6|250001,"1042,1",,,,,,,,,,,,,,,,,,0,
50025,玩家的私有变量（玩家组）,"1019,0",玩家组,5,玩家,@1|的|@2|（玩家组）,【玩家】的【私有变量】（玩家组）,"1018,1",6|250001,"1042,1",,,,,,,,,,,,,,,,,,0,
50026,玩家的私有变量（方块类型）,"1025,0",方块类型,5,玩家,@1|的|@2|（方块类型）,【玩家】的【私有变量】（方块类型）,"1018,1",6|250001,"1042,1",,,,,,,,,,,,,,,,,,0,
50027,玩家的私有变量（道具类型）,"1028,0",道具类型,5,玩家,@1|的|@2|（道具类型）,【玩家】的【私有变量】（道具类型）,"1018,1",6|250001,"1042,1",,,,,,,,,,,,,,,,,,0,
50028,玩家的私有变量（生物）,"1031,0",生物,5,玩家,@1|的|@2|（生物）,【玩家】的【私有变量】（生物）,"1018,1",6|250001,"1042,1",,,,,,,,,,,,,,,,,,0,
50029,玩家的私有变量（生物类型）,"1032,0",生物类型,5,玩家,@1|的|@2|（生物类型）,【玩家】的【私有变量】（生物类型）,"1018,1",6|250001,"1042,1",,,,,,,,,,,,,,,,,,0,
50030,玩家的私有变量（生物组）,"1033,0",生物组,5,玩家,@1|的|@2|（生物组）,【玩家】的【私有变量】（生物组）,"1018,1",6|250001,"1042,1",,,,,,,,,,,,,,,,,,0,
50031,玩家的私有变量（计时器）,"1040,0",计时器,5,玩家,@1|的|@2|（计时器）,【玩家】的【私有变量】（计时器）,"1018,1",6|250001,"1042,1",,,,,,,,,,,,,,,,,,0,
50032,玩家的私有变量（特效类型）,"1045,0",特效类型,5,玩家,@1|的|@2|（特效类型）,【玩家】的【私有变量】（特效类型）,"1018,1",6|250001,"1042,1",,,,,,,,,,,,,,,,,,0,
50033,玩家准心所指的方向,"1047,1",方向,5,玩家,@1|准心所指的方向,【玩家】准心所指的方向,"1018,1",6|250001,,,,,,,,,,,,,,,,,,,0,
50034,玩家的手持道具类型,"1028,1",道具类型,5,玩家,@1|的手持道具类型,【玩家】的手持道具类型,"1018,1",6|250001,,,,,,,,,,,,,,,,,,,,
50035,玩家,"1053,1",任意值,5,玩家,|@1,【玩家】,"1018,1",,,,,,,,,,,,,,,,,,,,,
50036,玩家组,"1053,1",任意值,5,玩家,|@1,【玩家组】,"1019,1",,,,,,,,,,,,,,,,,,,,,
50037,指定玩家外观,"1060,1",外观类型,5,玩家,@1|的外观,【玩家】的外观,"1018,1",6|250001,,,,,,,,,,,,,,,,,,,,
50038,玩家组,"1061,1",任意组,5,玩家,|@1,【玩家组】,"1019,1",,,,,,,,,,,,,,,,,,,,,0
50039,玩家的私有变量（位置组）,"1062,1",位置组,5,玩家,@1|的|@2|（位置组）,【玩家】的【私有变量】（位置组）,"1018,1",6|250001,"1042,1",,,,,,,,,,,,,,,,,,,0
50040,玩家的私有变量（区域组）,"1063,1",区域组,5,玩家,@1|的|@2|（区域组）,【玩家】的【私有变量】（区域组）,"1018,1",6|250001,"1042,1",,,,,,,,,,,,,,,,,,,0
50041,玩家的私有变量（数值组）,"1064,1",数值组,5,玩家,@1|的|@2|（数值组）,【玩家】的【私有变量】（数值组）,"1018,1",6|250001,"1042,1",,,,,,,,,,,,,,,,,,,0
50042,玩家的私有变量（字符串组）,"1065,1",字符串组,5,玩家,@1|的|@2|（字符串组）,【玩家】的【私有变量】（字符串组）,"1018,1",6|250001,"1042,1",,,,,,,,,,,,,,,,,,,0
50043,玩家的私有变量（布尔值组）,"1066,1",布尔值组,5,玩家,@1|的|@2|（布尔值组）,【玩家】的【私有变量】（布尔值组）,"1018,1",6|250001,"1042,1",,,,,,,,,,,,,,,,,,,0
50044,玩家的私有变量（方块类型组）,"1067,1",方块类型组,5,玩家,@1|的|@2|（方块类型组）,【玩家】的【私有变量】（方块类型组）,"1018,1",6|250001,"1042,1",,,,,,,,,,,,,,,,,,,0
50045,玩家的私有变量（道具类型组）,"1068,1",道具类型组,5,玩家,@1|的|@2|（道具类型组）,【玩家】的【私有变量】（道具类型组）,"1018,1",6|250001,"1042,1",,,,,,,,,,,,,,,,,,,0
50046,玩家的私有变量（生物类型组）,"1069,1",生物类型组,5,玩家,@1|的|@2|（生物类型组）,【玩家】的【私有变量】（生物类型组）,"1018,1",6|250001,"1042,1",,,,,,,,,,,,,,,,,,,0
50047,玩家的私有变量（计时器组）,"1070,1",计时器组,5,玩家,@1|的|@2|（计时器组）,【玩家】的【私有变量】（计时器组）,"1018,1",6|250001,"1042,1",,,,,,,,,,,,,,,,,,,0
50048,玩家的私有变量（特效类型组）,"1071,1",特效类型组,5,玩家,@1|的|@2|（特效类型组）,【玩家】的【私有变量】（特效类型组）,"1018,1",6|250001,"1042,1",,,,,,,,,,,,,,,,,,,0
50049,随机外观类型,"1060,1",外观类型,5,玩家,随机外观类型,随机外观类型,,,,,,,,,,,,,,,,,,,,,,2
50050,获取玩家的迷你号,"1010,1",字符串,5,玩家,获取|@1|的迷你号,获取【玩家】的迷你号,"1018,1",6|250001,,,,,,,,,,,,,,,,,,,,9
50051,获取指定迷你号的玩家,"1018,1",玩家,5,玩家,获取迷你号为|@1|的玩家,获取迷你号为【字符串】的玩家,"1010,1",1|1000,,,,,,,,,,,,,,,,,,,,
50052,发射投掷物的玩家,"1018,1",玩家,5,玩家,发射投掷物的玩家,发射投掷物的玩家,,"1083,1",6|600001,,,,,,,,,,,,,,,,,,,
60001,玩家组中的玩家数量,"1005,1",数值,6,玩家组,@1|中玩家的数量,【玩家组】中玩家的数量,"1019,1",,,,,,,,,,,,,,,,,,,,0,
60002,玩家组中的玩家的编号,"1005,1",数值,6,玩家组,@1|中|@2|的编号,【玩家组】中【玩家】的编号,"1019,1",,"1018,1",,,,,,,,,,,,,,,,,,0,
60003,游戏中玩家的数量,"1005,1",数值,6,玩家组,当前游戏中玩家的数量,当前游戏中玩家的数量,,,,,,,,,,,,,,,,,,,,,0,
60004,玩家组中指定编号的玩家,"1018,0",玩家,6,玩家组,@1|中编号为|@2|的玩家,【玩家组】中编号为【数值】的玩家,"1019,1",,"1005,1",,,,,,,,,,,,,,,,,,0,
60005,玩家组中的随机玩家,"1018,1",玩家,6,玩家组,@1|中的随机玩家,【玩家组】中的随机玩家,"1019,1",,,,,,,,,,,,,,,,,,,,0,
60006,所有玩家,"1019,1",玩家组,6,玩家组,所有玩家,所有玩家,,,,,,,,,,,,,,,,,,,,,0,
60007,队伍中的所有玩家,"1019,1",玩家组,6,玩家组,@1|中的所有玩家,【队伍】中的所有玩家,"1023,1",,,,,,,,,,,,,,,,,,,,0,
60008,区域中的所有玩家,"1019,1",玩家组,6,玩家组,@1|中的所有玩家,【区域】中的所有玩家,"1001,1",,,,,,,,,,,,,,,,,,,,0,
70001,生物的位置,"1002,1",位置,7,生物,@1|的位置,【生物】的位置,"1031,1",,,,,,,,,,,,,,,,,,,,0,
70002,生物的属性,"1005,0",数值,7,生物,@1|的|@2,【生物】的【生物属性】,"1031,1",,"1035,1",,,,,,,,,,,,,,,,,,0,
70004,获取生物组中生物编号,"1005,1",数值,7,生物,@1|中|@2|的编号,【生物组】中【生物】的编号,"1033,1",,"1031,1",,,,,,,,,,,,,,,,,,0,
70005,生物面向的角度,"1005,1",数值,7,生物,@1|面向的角度,【生物】面向的角度,"1031,1",,,,,,,,,,,,,,,,,,,,0,
70006,生物的类型名,"1010,1",字符串,7,生物,@1|的类型名,【生物】的类型名,"1031,1",,,,,,,,,,,,,,,,,,,,0,
70007,个体判断,"1014,1",布尔值,7,生物,@1||@2||@3,【生物】【是否】【生物】,"1031,1",,"1012,1",5|90001,"1031,1",,,,,,,,,,,,,,,,0,
70008,类型判断,"1014,1",布尔值,7,生物,@1||@2||@3,【生物】【是否】【生物类型】,"1031,1",,"1012,1",5|90001,"1032,1",,,,,,,,,,,,,,,,0,
70009,处于生物组判断,"1014,1",布尔值,7,生物,@1||@2|处于|@3,【生物】【是否】处于【生物组】,"1031,1",,"1012,1",5|90001,"1033,1",,,,,,,,,,,,,,,,0,
70010,处于区域判断,"1014,1",布尔值,7,生物,@1||@2|处于|@3|中,【生物】【是否】处于【区域】中,"1031,1",,"1012,1",5|90001,"1001,1",,,,,,,,,,,,,,,,0,
70011,状态判断,"1014,1",布尔值,7,生物,@1||@2|拥有|@3|的状态,【生物】【是否】拥有【状态】的状态,"1031,1",,"1012,1",5|90001,"1037,1",,,,,,,,,,,,,,,,0,
70012,生物的队伍,"1023,1",队伍,7,生物,@1|的队伍,【生物】的队伍,"1031,1",,,,,,,,,,,,,,,,,,,,0,
70013,指定生物类型,"1032,1",生物类型,7,生物,@1|的类型,【生物】的类型,"1031,1",,,,,,,,,,,,,,,,,,,,0,
70014,随机生物类型,"1032,1",生物类型,7,生物,随机的生物类型,随机的生物类型,,,,,,,,,,,,,,,,,,,,,0,
70015,生物类型,"1053,1",任意值,7,生物,|@1,【生物类型】,"1032,1",,,,,,,,,,,,,,,,,,,,,
70016,生物组,"1053,1",任意值,7,生物,|@1,【生物组】,"1033,1",,,,,,,,,,,,,,,,,,,,,
70017,指定生物外观,"1060,1",外观类型,7,生物,@1|的外观,【生物】的外观,"1031,1",6|300001,,,,,,,,,,,,,,,,,,,,
70018,生物组,"1061,1",任意组,7,生物,|@1,【生物组】,"1033,1",,,,,,,,,,,,,,,,,,,,,0
70019,生物类型组,"1061,1",任意组,7,生物,|@1,【生物类型组】,"1069,1",,,,,,,,,,,,,,,,,,,,,0
70020,生物,"1053,1",任意值,7,生物,|@1,【生物】,"1031,1",,,,,,,,,,,,,,,,,,,,,0
70021,发射投掷物的生物,"1031,1",生物,7,生物,发射投掷物的生物,发射投掷物的生物,,"1083,1",6|600001,,,,,,,,,,,,,,,,,,,
80001,生物组中生物的数量,"1005,1",数值,8,生物组,@1|中生物的数量,【生物组】中生物的数量,"1033,1",,,,,,,,,,,,,,,,,,,,0,
80002,生物组中指定编号的生物,"1031,0",生物,8,生物组,@1|中编号为|@2|的生物,【生物组】中编号为【数值】的生物,"1033,1",,"1005,1",,,,,,,,,,,,,,,,,,0,
80003,生物组中的随机生物,"1031,1",生物,8,生物组,@1|中的随机生物,【生物组】中的随机生物,"1033,1",,,,,,,,,,,,,,,,,,,,0,
80004,队伍中的所有生物,"1033,1",生物组,8,生物组,@1|中的所有生物,【队伍】中的所有生物,"1023,1",,,,,,,,,,,,,,,,,,,,0,
80005,区域中的所有生物,"1033,1",生物组,8,生物组,@1|中的所有生物,【区域】中的所有生物,"1001,1",,,,,,,,,,,,,,,,,,,,0,
90001,掉落物的位置,"1002,0",位置,9,道具,@1|的位置,【掉落物】的位置,"1030,1",,,,,,,,,,,,,,,,,,,,0,
90002,掉落物的数量,"1005,1",数值,9,道具,@1|的数量,【掉落物】的数量,"1030,1",,,,,,,,,,,,,,,,,,,,0,
90003,玩家背包发生改变的道具的数量,"1005,1",数值,9,道具,玩家背包发生改变的道具的数量,玩家背包发生改变的道具的数量,"1083,1",6|600001,,,,,,,,,,,,,,,,,,,0,
90004,道具的类型名,"1010,1",字符串,9,道具,@1|的类型名,【道具类型】的类型名,"1028,1",,,,,,,,,,,,,,,,,,,,0,
90005,类型判断,"1014,1",布尔值,9,道具,@1||@2||@3,【道具类型】【是否】【道具类型】,"1028,1",,"1012,1",5|90001,"1028,1",,,,,,,,,,,,,,,,0,
90006,掉落物处于区域判断,"1014,1",布尔值,9,道具,@1||@2|处于|@3|中,【掉落物】【是否】处于【区域】中,"1030,1",,"1012,1",5|90001,"1001,1",,,,,,,,,,,,,,,,0,
90007,投掷物处于区域判断,"1014,1",布尔值,9,道具,@1||@2|处于|@3|中,【投掷物】【是否】处于【区域】中,"1044,1",,"1012,1",5|90001,"1001,1",,,,,,,,,,,,,,,,0,
90008,掉落物的类型,"1028,1",道具类型,9,道具,@1|的类型,【掉落物】的类型,"1030,1",,,,,,,,,,,,,,,,,,,,0,
90009,道具类型,"1053,1",任意值,9,道具,|@1,【道具类型】,"1028,1",,,,,,,,,,,,,,,,,,,,,
90012,道具类型组,"1061,1",任意组,9,道具,|@1,【道具类型组】,"1068,1",,,,,,,,,,,,,,,,,,,,,0
90013,随机道具类型,"1028,1",道具类型,9,道具,随机道具类型,随机道具类型,,,,,,,,,,,,,,,,,,,,,,2
100001,当前全局天气模式,"1000,1",天气,10,世界,获取当前全局天气模式,获取当前天气类型,,,,,,,,,,,,,,,,,,,,,0,
100002,位置与位置的距离,"1005,1",数值,10,世界,@1|与|@2|的距离,【位置】与【位置】的距离,"1002,1",,"1002,1",,,,,,,,,,,,,,,,,,0,
100003,天气类型比较,"1014,1",布尔值,10,世界,@1||@2||@3,【天气】【是否】【天气】,"1000,1",3|100001,"1012,1",5|90001,"1000,1",,,,,,,,,,,,,,,,0,
100004,方块类型比较,"1014,1",布尔值,10,世界,@1||@2||@3,【方块类型】【是否】【方块类型】,"1025,1",3|100007,"1012,1",5|90001,"1025,1",,,,,,,,,,,,,,,,0,
100005,方块的开关状态,"1014,1",布尔值,10,世界,@1|的方块的开关状态,【位置】的方块的开关状态,"1002,1",,,,,,,,,,,,,,,,,,,,0,
100006,位置处于区域判断,"1014,1",布尔值,10,世界,@1||@2|处于|@3|中,【位置】【是否】处于【区域】中,"1002,1",,"1012,1",5|90001,"1001,1",,,,,,,,,,,,,,,,0,
100007,指定位置的方块类型,"1025,1",方块类型,10,世界,@1|的方块类型,【位置】的方块类型,"1002,1",,,,,,,,,,,,,,,,,,,,0,
100008,位置到位置的方向,"1047,1",方向,10,世界,@1|到|@2|的方向,【位置】到【位置】的方向,"1002,1",,"1002,1",,,,,,,,,,,,,,,,,,0,
100009,绝对三轴坐标方向,"1047,1",方向,10,世界,X|@1|，Y|@2|，Z|@3|的方向,X【数值】，Y【数值】，Z【数值】的方向,"1005,1",,"1005,1",,"1005,1",,,,,,,,,,,,,,,,0,
100010,绝对球坐标方向,"1047,1",方向,10,世界,方位角|@1|，仰角|@2|的方向,方位角【数值】，仰角【数值】的方向,"1005,1",,"1005,1",,,,,,,,,,,,,,,,,,0,
100011,位置,"1053,1",任意值,10,世界,|@1,【位置】,"1002,1",,,,,,,,,,,,,,,,,,,,,
100012,区域,"1053,1",任意值,10,世界,|@1,【区域】,"1001,1",,,,,,,,,,,,,,,,,,,,,
100013,计时器,"1053,1",任意值,10,世界,|@1,【计时器】,"1040,1",,,,,,,,,,,,,,,,,,,,,
100014,位置组,"1061,1",任意组,10,世界,|@1,【位置组】,"1062,1",,,,,,,,,,,,,,,,,,,,,0
100015,区域组,"1061,1",任意组,10,世界,|@1,【区域组】,"1063,1",,,,,,,,,,,,,,,,,,,,,0
100016,计时器组,"1061,1",任意组,10,世界,|@1,【计时器组】,"1070,1",,,,,,,,,,,,,,,,,,,,,0
100017,随机天气,"1000,1",天气,10,世界,随机天气,随机天气,,,,,,,,,,,,,,,,,,,,,,2
100018,地形组的天气,"1000,1",天气,10,世界,@1|的天气,【地形组】的天气,"1137,1",6|990001,,,,,,,,,,,,,,,,,,,,
110001,指定玩家的载具,"1031,1",生物,11,载具,@1|的载具,【玩家】的载具,"1018,1",6|250001,,,,,,,,,,,,,,,,,,,0,
120001,随机特效类型,"1045,1",特效,12,特效,随机的特效类型,随机的特效类型,,,,,,,,,,,,,,,,,,,,,0,
120002,特效类型,"1053,1",任意值,12,特效,|@1,【特效类型】,"1045,1",,,,,,,,,,,,,,,,,,,,,
120003,特效类型组,"1061,1",任意组,12,特效,|@1,【特效类型组】,"1071,1",,,,,,,,,,,,,,,,,,,,,0
130001,获取当前播放广告位的名字,"1010,1,0",字符串,13,播放,获取当前播放广告位的名字,获取当前播放广告位的名字,"1083,1",6|600001,,,,,,,,,,,,,,,,,,,1,
140001,方块类型,"1053,1",任意值,14,方块,|@1,【方块类型】,"1025,1",,,,,,,,,,,,,,,,,,,,,
140002,方块类型组,"1061,1",任意组,14,方块,|@1,【方块类型组】,"1067,1",,,,,,,,,,,,,,,,,,,,,0
150001,文字板,"1057,1",图文信息,15,图文信息,文字内容为|@1|的文字板，字体大小为|@2|，底板不透明度为|@3|%，编号为|@4,文字内容为【字符串】的文字板，字体大小为【数值】，底板不透明度为【数值】%，编号为【数值】,"1010,1,1",,"1005,1,1",1|16,"1005,1,1",1|0,"1005,1",1|1,,,,,,,,,,,,,,
150002,漂浮文字,"1057,1",图文信息,15,图文信息,文字内容为|@1|的漂浮文字，字体大小为|@2|，编号为|@3,文字内容为【字符串】的漂浮文字，字体大小为【数值】，编号为【数值】,"1010,1",,"1005,1",1|16,"1005,1",1|1,,,,,,,,,,,,,,,,
150003,进度条,"1057,1",图文信息,15,图文信息,当前值|@1|，最大值|@2|的进度条，进度条颜色为|@3|，编号为|@4,当前值【数值】，最大值【数值】的进度条，进度条颜色为【颜色】，编号为【数值】,"1005,1,1",1|100,"1005,1,1",1|100,"1056,1",1|0xFFFFFF,"1005,1",1|1,,,,,,,,,,,,,,
150004,指向对象的箭头,"1057,1",图文信息,15,图文信息,指向|@1|， 箭头大小为|@2|，颜色为|@3|，编号为|@4,指向【对象】， 箭头大小为【数值】，颜色为【颜色】，编号为【数值】,"1048,1",,"1005,1",1|1,"1056,1",1|0xff0000,"1005,1",1|1,,,,,,,,,,,,,,0
150005,指向位置的箭头,"1057,1",图文信息,15,图文信息,指向|@1|， 箭头大小为|@2|，颜色为|@3|，编号为|@4,指向【位置】， 箭头大小为【数值】，颜色为【颜色】，编号为【数值】,"1002,1",,"1005,1",1|1,"1056,1",1|0xff0000,"1005,1",1|1,,,,,,,,,,,,,,0
150006,指向对象的引导面,"1057,1",图文信息,15,图文信息,指向|@1|， 引导面大小为|@2|，颜色为|@3|，编号为|@4,指向【对象】， 引导面大小为【数值】，颜色为【颜色】，编号为【数值】,"1048,1",,"1005,1",1|1,"1056,1",1|0xff0000,"1005,1",1|1,,,,,,,,,,,,,,0
150007,指向位置的引导面,"1057,1",图文信息,15,图文信息,指向|@1|， 引导面大小为|@2|，颜色为|@3|，编号为|@4,指向【位置】， 引导面大小为【数值】，颜色为【颜色】，编号为【数值】,"1002,1",,"1005,1",1|1,"1056,1",1|0xff0000,"1005,1",1|1,,,,,,,,,,,,,,0
150008,指向对象的引导线,"1057,1",图文信息,15,图文信息,指向|@1|， 引导线大小为|@2|，颜色为|@3|，编号为|@4,指向【对象】， 引导线大小为【数值】，颜色为【颜色】，编号为【数值】,"1048,1",,"1005,1",1|1,"1056,1",1|0xff0000,"1005,1",1|1,,,,,,,,,,,,,,0
150009,指向位置的引导线,"1057,1",图文信息,15,图文信息,指向|@1|， 引导线大小为|@2|，颜色为|@3|，编号为|@4,指向【位置】， 引导线大小为【数值】，颜色为【颜色】，编号为【数值】,"1002,1",,"1005,1",1|1,"1056,1",1|0xff0000,"1005,1",1|1,,,,,,,,,,,,,,0
160001,随机颜色,"1056,1",颜色,16,颜色,随机颜色,随机颜色,,,,,,,,,,,,,,,,,,,,,,
170001,位置组中指定编号的位置,"1002,0",位置,17,位置组,@1|中编号为|@2|的位置,【位置组】中编号为【数值】的位置,"1062,1",,"1005,1",1|1,,,,,,,,,,,,,,,,,,0
170002,位置组中的随机位置,"1002,1",位置,17,位置组,@1|中的随机位置,【位置组】中的随机位置,"1062,1",,,,,,,,,,,,,,,,,,,,,0
180001,区域组中指定编号的区域,"1001,0",区域,18,区域组,@1|中编号为|@2|的区域,【区域组】中编号为【数值】的区域,"1063,1",,"1005,1",1|1,,,,,,,,,,,,,,,,,,0
180002,区域组中的随机区域,"1001,1",区域,18,区域组,@1|中的随机区域,【区域组】中的随机区域,"1063,1",,,,,,,,,,,,,,,,,,,,,0
190001,数值组中指定编号的数值,"1005,0",数值,19,数值组,@1|中编号为|@2|的数值,【数值组】中编号为【数值】的数值,"1064,1",,"1005,1",1|1,,,,,,,,,,,,,,,,,,0
190002,数值组中的随机数值,"1005,1",数值,19,数值组,@1|中的随机数值,【数值组】中的随机数值,"1064,1",,,,,,,,,,,,,,,,,,,,,0
190003,获取数值组中的最大值,"1005,1",数值,19,数值组,获取|@1|中的最大值,获取【数值组】中的最大值,"1064,1",,,,,,,,,,,,,,,,,,,,,0
190004,获取数值组中的最小值,"1005,1",数值,19,数值组,获取|@1|中的最小值,获取【数值组】中的最小值,"1064,1",,,,,,,,,,,,,,,,,,,,,
200001,字符串组中指定编号的字符串,"1010,0",字符串,20,字符串组,@1|中编号为|@2|的字符串,【字符串组】中编号为【数值】的字符串,"1065,1",,"1005,1",1|1,,,,,,,,,,,,,,,,,,0
200002,字符串组中的随机字符串,"1010,1",字符串,20,字符串组,@1|中的随机字符串,【字符串组】中的随机字符串,"1065,1",,,,,,,,,,,,,,,,,,,,,0
210001,布尔值组中指定编号的布尔值,"1014,0",布尔值,21,布尔值组,@1|中编号为|@2|的布尔值,【布尔值组】中编号为【数值】的布尔值,"1066,1",,"1005,1",1|1,,,,,,,,,,,,,,,,,,0
210002,布尔值组中的随机布尔值,"1014,1",布尔值,21,布尔值组,@1|中的随机布尔值,【布尔值组】中的随机布尔值,"1066,1",,,,,,,,,,,,,,,,,,,,,0
220001,方块类型组中指定编号的方块类型,"1025,0",方块类型,22,方块类型组,@1|中编号为|@2|的方块类型,【方块类型组】中编号为【数值】的方块类型,"1067,1",,"1005,1",1|1,,,,,,,,,,,,,,,,,,0
220002,方块类型组中的随机方块类型,"1025,1",方块类型,22,方块类型组,@1|中的随机方块类型,【方块类型组】中的随机方块类型,"1067,1",,,,,,,,,,,,,,,,,,,,,0
230001,道具类型组中指定编号的道具类型,"1028,0",道具类型,23,道具类型组,@1|中编号为|@2|的道具类型,【道具类型组】中编号为【数值】的道具类型,"1068,1",,"1005,1",1|1,,,,,,,,,,,,,,,,,,0
230002,道具类型组中的随机道具类型,"1028,1",道具类型,23,道具类型组,@1|中的随机道具类型,【道具类型组】中的随机道具类型,"1068,1",,,,,,,,,,,,,,,,,,,,,0
240001,生物类型组中指定编号的生物类型,"1032,0",生物类型,24,生物类型组,@1|中编号为|@2|的生物类型,【生物类型组】中编号为【数值】的生物类型,"1069,1",,"1005,1",1|1,,,,,,,,,,,,,,,,,,0
240002,生物类型组中的随机生物类型,"1032,1",生物类型,24,生物类型组,@1|中的随机生物类型,【生物类型组】中的随机生物类型,"1069,1",,,,,,,,,,,,,,,,,,,,,0
250001,计时器组中指定编号的计时器,"1040,0",计时器,25,计时器组,@1|中编号为|@2|的计时器,【计时器组】中编号为【数值】的计时器,"1070,1",,"1005,1",1|1,,,,,,,,,,,,,,,,,,0
250002,计时器组中的随机计时器,"1040,1",计时器,25,计时器组,@1|中的随机计时器,【计时器组】中的随机计时器,"1070,1",,,,,,,,,,,,,,,,,,,,,0
260001,特效类型组中指定编号的特效类型,"1045,0",特效类型,26,特效类型组,@1|中编号为|@2|的特效类型,【特效类型组】中编号为【数值】的特效类型,"1071,1",,"1005,1",1|1,,,,,,,,,,,,,,,,,,0
260002,特效类型组中的随机特效类型,"1045,1",特效类型,26,特效类型组,@1|中的随机特效类型,【特效类型组】中的随机特效类型,"1071,1",,,,,,,,,,,,,,,,,,,,,0
270004,获取道具类型图标,"1076,1",图案模板,30,通用,|@1,【道具类型】,"1028,1",,,,,,,,,,,,,,,,,,,,,
270005,获取生物图标,"1076,1",图案模板,30,通用,|@1,【生物】,"1031,1",,,,,,,,,,,,,,,,,,,,,
270006,获取生物类型图标,"1076,1",图案模板,30,通用,|@1,【生物类型】,"1032,1",,,,,,,,,,,,,,,,,,,,,
270007,获取状态图标,"1076,1",图案模板,30,通用,|@1,【状态】,"1037,1",,,,,,,,,,,,,,,,,,,,,
270008,获取方块类型图标,"1076,1",图案模板,30,通用,|@1,【方块类型】,"1025,1",,,,,,,,,,,,,,,,,,,,,
270009,获取玩家当前角色图标,"1076,1",图案模板,30,通用,当前|@1|角色图标,当前【玩家】角色图标,"1018,1",6|250001,,,,,,,,,,,,,,,,,,,,
270010,获取玩家快捷栏图标,"1076,1",图案模板,30,通用,@1|快捷栏为|@2|的图标,【玩家】快捷栏为【数值】的图标,"1018,1",6|250001,"1005,1",1|1,,,,,,,,,,,,,,,,,,
280001,随机投掷物类型,"1029,1",投掷物类型,28,投掷物,随机投掷物类型,随机投掷物类型,,,,,,,,,,,,,,,,,,,,,,2
290001,随机音效,"1046,1",音效,29,音效,随机音效,随机音效,,,,,,,,,,,,,,,,,,,,,,2
300001,服务器获取的位置,"1002,1",位置,30,数据存储,服务器获取的位置,服务器获取的位置,"1083,1",6|600001,,,,,,,,,,,,,,,,,,,,10
300002,服务器获取的区域,"1001,1",区域,30,数据存储,服务器获取的区域,服务器获取的区域,"1083,1",6|600001,,,,,,,,,,,,,,,,,,,,10
300003,服务器获取的数值,"1005,1",数值,30,数据存储,服务器获取的数值,服务器获取的数值,"1083,1",6|600001,,,,,,,,,,,,,,,,,,,,10
300004,服务器获取的字符串,"1010,1",字符串,30,数据存储,服务器获取的字符串,服务器获取的字符串,"1083,1",6|600001,,,,,,,,,,,,,,,,,,,,10
300005,服务器获取的布尔值,"1014,1",布尔值,30,数据存储,服务器获取的布尔值,服务器获取的布尔值,"1083,1",6|600001,,,,,,,,,,,,,,,,,,,,10
300006,服务器获取的玩家,"1018,1",玩家,30,数据存储,服务器获取的玩家,服务器获取的玩家,"1083,1",6|600001,,,,,,,,,,,,,,,,,,,,10
300007,服务器获取的方块类型,"1025,1",方块类型,30,数据存储,服务器获取的方块类型,服务器获取的方块类型,"1083,1",6|600001,,,,,,,,,,,,,,,,,,,,10
300008,服务器获取的道具类型,"1028,1",道具类型,30,数据存储,服务器获取的道具类型,服务器获取的道具类型,"1083,1",6|600001,,,,,,,,,,,,,,,,,,,,10
300009,服务器获取的生物,"1031,1",生物,30,数据存储,服务器获取的生物,服务器获取的生物,"1083,1",6|600001,,,,,,,,,,,,,,,,,,,,10
300010,服务器获取的生物类型,"1032,1",生物类型,30,数据存储,服务器获取的生物类型,服务器获取的生物类型,"1083,1",6|600001,,,,,,,,,,,,,,,,,,,,10
300011,服务器获取的计时器,"1040,1",计时器,30,数据存储,服务器获取的计时器,服务器获取的计时器,"1083,1",6|600001,,,,,,,,,,,,,,,,,,,,10
300012,服务器获取的特效类型,"1045,1",特效类型,30,数据存储,服务器获取的特效类型,服务器获取的特效类型,"1083,1",6|600001,,,,,,,,,,,,,,,,,,,,10
300013,排行榜获取的排名,"1005,1",数值,30,数据存储,排行榜获取的排名,排行榜获取的排名,,,,,,,,,,,,,,,,,,,,,,12
300014,服务器获取的名称（键）,"1010,1",字符串,30,数据存储,获取服务器和排行榜索引名称（键）,获取服务器和排行榜索引名称（键）,,,,,,,,,,,,,,,,,,,,,,12
300015,服务器获取的玩家（键）,"1018,1",玩家,30,数据存储,获取服务器和排行榜索引玩家（键）,获取服务器和排行榜索引玩家（键）,,,,,,,,,,,,,,,,,,,,,,12
