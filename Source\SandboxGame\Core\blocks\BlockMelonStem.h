
#ifndef __BLOCKMELONSTEM_H__
#define __BLOCKMELONSTEM_H__

#include "BlockGrayHerbs.h"

class MelonStemMaterial : public HerbMaterial //tolua_exports
{ //tolua_exports
	//typedef HerbMaterial Super;
	DECLARE_BLOCKMATERIAL(MelonStemMaterial)
	//DECLARE_BLOCKINSTANCE(MelonStemMaterial)
public:
	//tolua_begin
	MelonStemMaterial();
	~MelonStemMaterial();

	//virtual const char *getGeomName();
	virtual void init(int resid) override;
	virtual void createBlockMesh(const BuildSectionMeshData& data, const WCoord &blockpos, SectionMesh *poutmesh) override;
	virtual BlockTexElement *getDestroyTexture(Block pblock, BlockTexDesc &desc) override;
	virtual void createCollideData(CollisionDetect* coldetect, World* pworld, const WCoord& blockpos) override;
	virtual int getPlaceBlockData(World *pworld, const WCoord &blockpos, DirectionType face, float hitptx, float hitpty, float hitptz, int def_blockdata);
	virtual bool onFertilized(World *pworld, const WCoord &blockpos, int fertiliser);
	virtual void dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata, BLOCK_MINE_TYPE droptype, float chance, int uin = -1);
	virtual bool hasDestroyScore(int blockdata);

	virtual void blockTick(World *pworld, const WCoord &blockpos);
	virtual void forceResh(World* pworld, const WCoord& blockpos) override;
	//tolua_end
private:
	virtual bool canThisPlantGrowOnThisBlockID(int blockid);
	virtual void initGeomName() override;
	virtual float getBlockCollideHeight(int blockData);

private:
	RenderBlockMaterial *m_ConnMtl;
	RenderBlockMaterial *m_DisConnMtl;
}; //tolua_exports


#endif