#include "TerritoryQueryInterface.h"
#include "TerritoryManager.h"
#include "container_territory.h"
#include "Core/actors/helper/IClientPlayer.h"
#include <chrono>

// 实现便捷的领地查询接口

bool TerritoryQuery::IsPlayerInOwnTerritory(IClientPlayer* player)
{
    if (!player) return false;
    
    Rainbow::Vector3f position = player->iGetPosition().toVector3() * 100.0f;  // 转换为世界坐标
    unsigned int playerUin = player->getUin();
    
    return IsPlayerInOwnTerritory(position, playerUin);
}

bool TerritoryQuery::IsPlayerInOwnTerritory(const Rainbow::Vector3f& position, unsigned int playerUin)
{
    auto* manager = TerritoryManager::GetInstance();
    if (!manager) return false;
    
    return manager->IsPlayerInAuthorizedTerritory(position, playerUin);
}

bool TerritoryQuery::IsPlayerInOwnTerritory(const WCoord& blockPos, unsigned int playerUin)
{
    Rainbow::Vector3f worldPos = BlockToWorldPos(blockPos);
    return IsPlayerInOwnTerritory(worldPos, playerUin);
}

bool TerritoryQuery::IsInAnyTerritory(const Rainbow::Vector3f& position)
{
    auto* manager = TerritoryManager::GetInstance();
    if (!manager) return false;
    
    return manager->IsPointInAnyTerritory(position);
}

bool TerritoryQuery::IsInAnyTerritory(const WCoord& blockPos)
{
    Rainbow::Vector3f worldPos = BlockToWorldPos(blockPos);
    return IsInAnyTerritory(worldPos);
}

TerritoryContainer* TerritoryQuery::GetTerritoryAt(const Rainbow::Vector3f& position)
{
    auto* manager = TerritoryManager::GetInstance();
    if (!manager) return nullptr;
    
    return manager->GetTerritoryContainingBlock(position);
}

TerritoryContainer* TerritoryQuery::GetTerritoryAt(const WCoord& blockPos)
{
    Rainbow::Vector3f worldPos = BlockToWorldPos(blockPos);
    return GetTerritoryAt(worldPos);
}

bool TerritoryQuery::CanPlayerBuildAt(const Rainbow::Vector3f& position, unsigned int playerUin)
{
    auto* manager = TerritoryManager::GetInstance();
    if (!manager) return true;  // 如果没有管理器，默认允许建造
    
    return manager->CanBuildAtPosition(position, static_cast<long long>(playerUin));
}

bool TerritoryQuery::CanPlayerBuildAt(const WCoord& blockPos, unsigned int playerUin)
{
    Rainbow::Vector3f worldPos = BlockToWorldPos(blockPos);
    return CanPlayerBuildAt(worldPos, playerUin);
}

TerritoryQuery::PlayerTerritoryInfo TerritoryQuery::GetPlayerTerritoryInfo(IClientPlayer* player)
{
    PlayerTerritoryInfo info;
    
    if (!player) return info;
    
    Rainbow::Vector3f position = player->iGetPosition().toVector3() * 100.0f;
    unsigned int playerUin = player->getUin();
    
    return GetTerritoryInfo(position, playerUin);
}

TerritoryQuery::PlayerTerritoryInfo TerritoryQuery::GetTerritoryInfo(const Rainbow::Vector3f& position, unsigned int playerUin)
{
    PlayerTerritoryInfo info;
    
    auto* manager = TerritoryManager::GetInstance();
    if (!manager) return info;
    
    // 获取该位置的领地
    TerritoryContainer* territory = manager->GetTerritoryContainingBlock(position);
    
    if (territory)
    {
        info.isInTerritory = true;
        info.territory = territory;
        info.isAuthorized = territory->IsAuthorized(playerUin);
        info.isOwner = (territory->GetUin() == playerUin);
        
        // 可以根据需要添加获取主人名称的逻辑
        // info.ownerName = GetPlayerNameByUin(territory->getOwnerUin());
    }
    
    return info;
}

TerritoryQuery::TerritorySystemStats TerritoryQuery::GetSystemStats()
{
    TerritorySystemStats stats;
    
    auto* manager = TerritoryManager::GetInstance();
    if (!manager) return stats;
    
    // 获取八叉树统计信息
    auto octreeStats = manager->GetOctreeStats();
    stats.octreeEnabled = octreeStats.octreeEnabled;
    stats.totalTerritories = octreeStats.totalTerritories;
    stats.octreeNodes = octreeStats.totalNodes;
    stats.maxDepth = octreeStats.maxDepth;
    stats.avgTerritoriesPerLeaf = octreeStats.avgTerritoriesPerLeaf;
    
    return stats;
}

void TerritoryQuery::SetOptimizationEnabled(bool enabled)
{
    auto* manager = TerritoryManager::GetInstance();
    if (manager)
    {
        manager->SetOctreeEnabled(enabled);
    }
}

Rainbow::Vector3f TerritoryQuery::BlockToWorldPos(const WCoord& blockPos)
{
    // 将方块坐标转换为世界坐标（每个方块100个单位）
    return blockPos.toVector3() * 100.0f;
}



