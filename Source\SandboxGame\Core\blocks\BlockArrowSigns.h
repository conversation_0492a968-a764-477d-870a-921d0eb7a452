
#ifndef __BLOCK_ARROWSIGNS_H__
#define __BLOCK_ARROWSIGNS_H__

#include "BlockSigns.h"

class BlockArrowSigns : public BlockSigns //tolua_exports
{ //tolua_exports
	DECLARE_BLOCKMATERIAL(BlockArrowSigns)
public:
	//tolua_begin
	virtual void init(int resid);
	virtual void onBlockPlacedBy(World *pworld, const WCoord &blockpos, IClientPlayer *player);
	virtual int getPlaceBlockData(World *pworld, const WCoord &blockpos, DirectionType face, float hitptx, float hitpty, float hitptz, int def_blockdata);
	virtual WorldContainer *createContainer(World *pworld, const WCoord &blockpos) override;
	
	virtual bool onTrigger(World *pworld, const WCoord &blockpos, DirectionType face, IClientPlayer *player, const Rainbow::Vector3f &colpoint=Rainbow::Vector3f(0, 0, 0));
	virtual void onBlockRemoved(World* pworld, const WCoord& blockpos, int blockid, int blockdata);
	virtual bool canAttachedToMecha(int curblockdata, DirectionType dir, int neighbor_id, int neighbor_data) override;

	static bool computeTextXform(Rainbow::WorldPos &pos, Rainbow::Quaternionf &rot, World *pworld, VehicleWorld *vehicleWorld, const WCoord &blockpos);
	static bool isUpBlock(int blockdata);
	static bool isFreestand(int blockid);

	virtual int convertDataByRotate(int blockdata, int rotatetype) override;
	virtual void initDrawType();
	virtual void onNotify(World* pworld, const WCoord& blockpos, int blockid);
	//tolua_end

	virtual void dropBlockAsItem(World* pworld, const WCoord& blockpos, int blockdata = 0, BLOCK_MINE_TYPE droptype = BLOCK_MINE_NOTOOL, float chance = 1.0f, int uin = -1);
	virtual void createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh);

	virtual bool isOpaqueCube() override
	{
		return false;
	}
	virtual void initGeomName() override;
private:
	virtual int getBlockGeomID(int *idbuf, int *dirbuf, const SectionDataHandler* sectionData, const WCoord &blockpos, World* world);
	virtual int getProtoBlockGeomID(int *idbuf, int *dirbuf);
	
private:
	bool m_FreeStanding;

public:
	DECLARE_BLOCKMATERIAL_INSTANCE_BEGIN(BlockArrowSigns)
		DECLARE_BLOCKMATERIAL_INSTANCE_R_PARAM(R_Dir, int)
		DECLARE_BLOCKMATERIAL_INSTANCE_R_PARAM(R_Text, std::string)			// 文本

		void SetText(const std::string& text);
		std::string GetText() const;
	DECLARE_BLOCKMATERIAL_INSTANCE_END(BlockArrowSigns)

}; //tolua_exports

#endif //__BLOCK_ARROWSIGNS_H__