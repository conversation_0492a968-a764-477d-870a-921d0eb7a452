
#ifndef __BLOCKSNOW_H__
#define __BLOCKSNOW_H__

#include "BlockBasic.h"

class SnowCubeMaterial : public BasicBlockMaterial //tolua_exports
{ //tolua_exports
	DECLARE_BLOCKMATERIAL(SnowCubeMaterial)
public:
	//tolua_begin
	virtual void blockTick(World* pworld, const WCoord& blockpos);
	//tolua_end
}; //tolua_exports

class SnowBlockMaterial : public BasicBlockMaterial //tolua_exports
{ //tolua_exports
	//typedef BasicBlockMaterial Super;
	DECLARE_BLOCKMATERIAL(SnowBlockMaterial)
	//DECLARE_BLOCKINSTANCE(SnowBlockMaterial)
public:
	//tolua_begin
	virtual void init(int resid) override;
	virtual float getBlockHeight(int blockdata);

	//virtual bool isOpaqueCube();
	virtual void initDefaultMtl() override;
	virtual void initDrawType() override;
	virtual bool hasSolidTopSurface(int blockdata);
	virtual bool canPutOntoPos(WorldProxy* pworld, const WCoord& blockpos);
	virtual void onNotify(World* pworld, const WCoord& blockpos, int blockid);
	virtual void createBlockMeshAngle(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh);
	virtual void dropBlockAsItem(World* pworld, const WCoord& blockpos, int blockdata = 0, BLOCK_MINE_TYPE droptype = BLOCK_MINE_NOTOOL, float chance = 1.0f, int uin = -1) override;
	virtual void onActorMoving(World* pworld, const WCoord& blockpos, IClientActor* actor);
	virtual bool canBlocksMovement(World* pworld, const WCoord& blockpos)
	{
		return false;
	}
	virtual int getPlaceBlockData(World* pworld, const WCoord& blockpos, DirectionType face, float hitptx, float hitpty, float hitptz, int def_blockdata) override;
	virtual BlockMaterial::BlockType BlockTypeId() { return BlockMaterial::BlockType::BlockType_Snow; }
	//tolua_end

// 	const WCoord SnowBlockNeighbor[4][6] =
// 	{
// 		{WCoord(-1, 0, 0), WCoord(-1, -1, 0), WCoord(-1, 0, -1), WCoord(-1, -1, -1), WCoord(-1, 0, 1), WCoord(-1, -1, 1)},
// 		{WCoord(1, 0, 0), WCoord(1, -1, 0), WCoord(1, 0, -1), WCoord(1, -1, -1), WCoord(1, 0, 1), WCoord(1, -1, 1)},
// 		{WCoord(0, 0, -1), WCoord(0, -1, -1), WCoord(-1, 0, -1), WCoord(-1, -1, -1), WCoord(1, 0, -1), WCoord(1, -1, -1)},
// 		{WCoord(0, 0, 1), WCoord(0, -1, 1), WCoord(-1, 0, 1), WCoord(-1, -1, 1), WCoord(1, 0, 1), WCoord(1, -1, 1)}
// 	};
	const WCoord SnowBlockOutNeighbor[8] =
	{
		WCoord(-1, 0, 0),
		WCoord(1, 0, 0),
		WCoord(0, 0, -1),
		WCoord(0, 0, 1),
		WCoord(-1, 0, -1),
		WCoord(-1, 0, 1),
		WCoord(1, 0, -1),
		WCoord(1, 0, 1)
	};
protected:
	virtual bool isKeepRoundBlock(BlockMaterial* mtl);
}; //tolua_exports

#endif