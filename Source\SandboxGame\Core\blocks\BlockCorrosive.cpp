#include "BlockCorrosive.h"
#include "blocks/BlockMaterialMgr.h"
#include "world.h"
#include "GlobalFunctions.h"
#include "WorldManager.h"
#include "container_erosion.h"
#include "container_sandboxGame.h"
#include "LuaInterfaceProxy.h"
#include "SandboxIdDef.h"
#include "IClientPlayer.h"
#include "ClientActor.h"
#include "ClientPlayer.h"

using namespace MINIW;

IMPLEMENT_BLOCKMATERIAL(BlockCorrosive)

BlockCorrosive::BlockCorrosive()
{
    // 设置为建筑方块类型
    SetToggle(BlockToggle_HasContainer, true);
}

BlockCorrosive::~BlockCorrosive()
{
}

void BlockCorrosive::init(int resid)
{
    ModelBlockMaterial::init(resid);
    
    SetToggle(BlockToggle_HasContainer, true);
    SetToggle(BlockToggle_IsOpaqueCube, false);
}

WorldContainer* BlockCorrosive::createContainer(World* pworld, const WCoord& blockpos)
{
    if (!pworld)
        return nullptr;
    
    // 创建建筑容器
    int blockId = pworld->getBlockID(blockpos);
    int bpTypeId = 0;
    int bpLevel = 0;
    initBuildData(blockId, bpTypeId, bpLevel);
    containerArchitecture* container = SANDBOX_NEW(containerArchitecture, blockpos, blockId, bpTypeId, bpLevel);
    return container;
}

void BlockCorrosive::onBlockAdded(World* pworld, const WCoord& blockpos)
{
    ModelBlockMaterial::onBlockAdded(pworld, blockpos);
    
    // 播放放置效果
    PlayCorrosionEffect(pworld, blockpos);
}

void BlockCorrosive::onBlockRemoved(World* pworld, const WCoord& blockpos, int blockid, int blockdata)
{
    ModelBlockMaterial::onBlockRemoved(pworld, blockpos, blockid, blockdata);
}

void BlockCorrosive::onPlayRandEffect(World* pworld, const WCoord& blockpos)
{
    ModelBlockMaterial::onPlayRandEffect(pworld, blockpos);
    
    // 随机播放腐蚀效果
    if ((rand() % 100) < 15) // 15% 概率
    {
        PlayCorrosionEffect(pworld, blockpos);
    }
}

bool BlockCorrosive::canPutOntoPos(WorldProxy* pworld, const WCoord& blockpos)
{
    // 基础检查
    return ModelBlockMaterial::canPutOntoPos(pworld, blockpos);
}

// BlockArchitecturalBase 实现
WCoord BlockCorrosive::getCoreBlockPos(World* pworld, const WCoord& blockpos, int blockdata)
{
    // 对于单个方块，核心位置就是自身位置
    return blockpos;
}

bool BlockCorrosive::getBlockRange(World* pworld, const WCoord& blockpos, std::vector<WCoord>& blockList, bool includeSelf)
{
    if (!pworld)
        return false;
    
    blockList.clear();
    
    // 对于单个方块结构，范围就是自身
    if (includeSelf)
    {
        blockList.push_back(blockpos);
    }
    
    return !blockList.empty();
}

bool BlockCorrosive::onBlockDamaged(World* pworld, const WCoord& blockpos, IClientPlayer* player, int attack_type, float damage)
{
    return BlockArchitecturalBase::onDamaged(pworld, blockpos, player->GetPlayer(), attack_type, damage);
}

int BlockCorrosive::getBlockHP(World* pworld, const WCoord& blockpos)
{
    WCoord corePos = getCoreBlockPos(pworld, blockpos);
    containerArchitecture* container = dynamic_cast<containerArchitecture*>(pworld->getContainerMgr()->getContainer(corePos));
    if (container)
        return container->getHp();
    return ModelBlockMaterial::getBlockHP(pworld, blockpos);
}

bool BlockCorrosive::onBlockUpGrade(World* pworld, const WCoord& blockpos, int upgradeNum, IClientPlayer* player)
{
    // 使用 BlockArchitecturalBase 的升级系统
    return onUpgradeBlock(pworld, blockpos, upgradeNum, player->GetPlayer(), this) > 0;
}

bool BlockCorrosive::onBlockRepaired(World* pworld, const WCoord& blockpos, IClientPlayer* player, float amount)
{
    return onRepairedBlock(pworld, blockpos, player->GetPlayer(), amount, this) > 0;
}

void BlockCorrosive::onBlockDestroyedBy(World* pworld, const WCoord& blockpos, int blockdata, BLOCK_DESTROY_REASON_T destroytype, IClientActor* bywho)
{
    onDestroyBlock(pworld, blockpos, dynamic_cast<ClientPlayer*>(bywho));
}

void BlockCorrosive::dropBlockAsItem(World* pworld, const WCoord& blockpos, int blockdata, BLOCK_MINE_TYPE droptype, float chance, int uin/* = -1 */)
{
    // 单个方块直接掉落物品
    ModelBlockMaterial::dropBlockAsItem(pworld, blockpos, blockdata, droptype, chance, uin);
}

WorldContainer* BlockCorrosive::getCoreContainer(World* pworld, const WCoord& blockpos)
{
    return GetArchitecturalCoreContainer(pworld, blockpos);
}

void BlockCorrosive::PlayCorrosionEffect(World* pworld, const WCoord& blockpos)
{
    if (!pworld)
        return;
    
#ifndef IWORLD_SERVER_BUILD
    // 播放腐蚀粒子效果
    //pworld->getEffectMgr()->playBlockHitEffect(
    //    getBlockResID(),
    //    blockpos * BLOCK_SIZE + WCoord(BLOCK_SIZE / 2, BLOCK_SIZE / 2, BLOCK_SIZE / 2),
    //    DIR_POS_Y,
    //    30);
#endif
}

bool BlockCorrosive::hasSolidTopSurface(int blockdata)
{
    return true;
}