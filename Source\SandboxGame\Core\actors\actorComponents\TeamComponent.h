#ifndef __TEAM_COMPONENT_H__
#define __TEAM_COMPONENT_H__

#include "ActorComponent_Base.h"
#include "GameNetManager.h"

#include <OgreWCoord.h>
#include <unordered_map>

#include "SandboxGame.h"
#include "world_types.h"

class ActorLiving;
class ClientMob;
class ClientPlayer;

class TeamComponent : public ActorComponentBase //tolua_exports
{ //tolua_exports
public:
	DECLARE_COMPONENTCLASS(TeamComponent)

	//tolua_begin
	TeamComponent();
	int getTeam(){return m_TeamID;};
	virtual void setTeam(int id ,bool ResetAttr);
	virtual bool isSameTeam(ActorLiving *target); //同一边,  不应该攻击
	virtual void addGameScoreByRule(int ruleid, int num = 1); //ruleid使用的是 GAMEMAKER_RULE，这里为了解耦改成int
	//tolua_end
protected:

	int m_TeamID; //0: 不是任何一边,  >=1: 相同的数字表示相同边
}; //tolua_exports

class MobTeamComponent: public TeamComponent //tolua_exports
{ //tolua_exports
public:
	DECLARE_COMPONENTCLASS(MobTeamComponent)

	//tolua_begin
	MobTeamComponent();
	//tolua_end
protected:
	virtual bool isSameTeam(ActorLiving *target) override; //同一边,  不应该攻击
	virtual void addGameScoreByRule(int ruleid, int num = 1) override; //ruleid使用的是 GAMEMAKER_RULE，这里为了解耦改成int	
private:
	ClientMob* m_ownerMob;
}; //tolua_exports

class EXPORT_SANDBOXGAME PlayerTeamComponent : public TeamComponent //tolua_exports
{ //tolua_exports
	DECLARE_COMPONENTCLASS(PlayerTeamComponent)
public:
	//tolua_begin
	PlayerTeamComponent();
	
	//void setMainTeam(int mainteam);
	
	void AddMainTag(int x, int z, int tagindex, int tagcolor, const std::string& tagname = "",bool broadcast = true);
	void RemoveMainTag(int x, int z);
	//std::string GetMainTagLua();

	void ClearMainTag();

	int Getmainteam() const { return m_mainteam; };

	void SynMainTags();

	void Clear();

	void UpdataTeam();
	//tolua_end

	void OnClientMainTags(const PB_SocTeamTagDataCH& datach);
	void OnServerMainTags(const PB_SocTeamTagDataHC& datahc);

	void broadcastPosition();
	void broadcastMainTags();
	bool checkTeam(int uin);
	void setSocTeam(const std::vector<int>& uins,int teamid, int mainteam);
	void AddMainTag(const WCoord& pos, int tagindex, int tagcolor, const std::string& tagname = "", bool broadcast = true);
	void RemoveMainTag(const WCoord& pos);

	void ResetPlayerAttr(); //设置队伍后是否重置属性
protected:
	void onChangeTeam(int oldteamid);

	virtual void setTeam(int id, bool ResetAttr) override;
	virtual void addGameScoreByRule(int ruleid, int num = 1) override; //ruleid使用的是 GAMEMAKER_RULE，这里为了解耦改成int
private:
	ClientPlayer* m_ownerPlayer;
	uint64_t m_lastBroadcastTime = 0;
	std::vector<int> m_uins;							//队伍的所以成员
	int m_mainteam;										//队长
	std::unordered_map<WCoord, std::tuple<uint32_t,std::string> , WCoordHashCoder> mainteamtag;	//队长地图标记,前16 是地图tag index 后16是tag color
}; //tolua_exports

#endif