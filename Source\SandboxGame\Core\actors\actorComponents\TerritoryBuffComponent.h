#pragma once

#include "ActorComponent_Base.h"
#include "SandboxGame.h"

class ClientPlayer;
class TerritoryContainer;

/**
 * 领地Buff组件
 * 负责管理玩家在领地中的buff状态
 * - 进入授权领地且维护材料充足时添加buff 3020
 * - 进入授权领地但维护材料不足时添加buff 3021（建筑腐蚀中）
 * - 进入非授权领地时添加buff 3022  
 * - 离开领地时移除对应buff
 */
class EXPORT_SANDBOXGAME TerritoryBuffComponent : public ActorComponentBase //tolua_exports
{//tolua_exports
	DECLARE_COMPONENTCLASS(TerritoryBuffComponent)

protected:
	virtual void OnEnterOwner(NS_SANDBOX::SandboxNode* owner);
	virtual void OnLeaveOwner(NS_SANDBOX::SandboxNode* owner);

public:
	TerritoryBuffComponent();
	~TerritoryBuffComponent();
	
	virtual void OnTick() override;

	//tolua_begin
	// 手动检查当前位置的领地状态（用于测试）
	void CheckCurrentTerritoryStatus();
	
	// 获取当前的领地buff状态
	int GetCurrentTerritoryBuffId() const { return m_currentTerritoryBuffId; }
	
	// 是否在领地内
	bool IsInTerritory() const { return m_isInTerritory; }
	
	// 是否在授权领地内
	bool IsInAuthorizedTerritory() const { return m_isInAuthorizedTerritory; }
	//tolua_end

private:
	// 检查玩家位置并更新buff状态
	void CheckTerritoryAndUpdateBuff();
	
	// 添加领地buff
	void AddTerritoryBuff(int buffId);
	
	// 移除领地buff
	void RemoveTerritoryBuff(int buffId);
	
	// 清除所有领地buff
	void ClearAllTerritoryBuffs();
	
	// 检查领地是否有足够的维护材料
	bool HasSufficientMaintenanceMaterials(TerritoryContainer* territoryContainer);
	
private:
	ClientPlayer* m_player;
	
	// 领地buff ID常量
	static const int AUTHORIZED_TERRITORY_BUFF_ID = 3020;    // 授权领地buff（维护材料充足）
	static const int TERRITORY_EROSION_BUFF_ID = 3021;       // 授权领地buff（建筑腐蚀中）
	static const int UNAUTHORIZED_TERRITORY_BUFF_ID = 3022;  // 非授权领地buff
	
	// 当前状态
	bool m_isInTerritory;           // 是否在任意领地内
	bool m_isInAuthorizedTerritory; // 是否在授权领地内
	bool m_hasMaintenanceMaterials; // 是否有足够的维护材料
	int m_currentTerritoryBuffId;   // 当前激活的领地buff ID (0表示无buff)
	
	// 上次检查的方块位置（用于优化，只有方块位置变化时才检查）
	WCoord m_lastCheckedBlockPosition;

protected:
};//tolua_exports 
