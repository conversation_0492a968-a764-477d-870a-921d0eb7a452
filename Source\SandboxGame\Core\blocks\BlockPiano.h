
#ifndef __BLOCK_PIANO_H__
#define __BLOCK_PIANO_H__

#include "BlockMaterial.h"

class BlockPiano : public ModelBlockMaterial
{
	DECLARE_BLOCKMATERIAL(BlockPiano)
	//DECLARE_BLOCKINSTANCE(BlockPiano)
public:
	BlockPiano();

	//virtual const char *getGeomName() override;	
	virtual bool onTrigger(World *pworld, const WCoord &blockpos, DirectionType face, IClientPlayer *player, const Rainbow::Vector3f &colpoint= Rainbow::Vector3f(0, 0, 0));
	virtual void dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata = 0, BLOCK_MINE_TYPE droptype = BLOCK_MINE_NOTOOL, float chance = 1.0f, int uin = -1);
	virtual void createCollideData(CollisionDetect *coldetect, World *pworld, const WCoord &blockpos);
	virtual void onBlockPlacedBy(World *pworld, const WCoord &blockpos, IClientPlayer *player);
	virtual void onBlockAdded(World *pworld, const WCoord &blockpos) override;
	virtual void onBlockRemoved(World *pworld, const WCoord &blockpos, int blockid, int blockdata);
	virtual bool canPutOntoPlayer(World *pworld, const WCoord &blockpos, IClientPlayer *player) override;
	virtual void createBlockMesh(const BuildSectionMeshData& data, const WCoord &blockpos, SectionMesh *poutmesh) override;
	virtual int getPlaceBlockDataByPlayer(World *pworld, IClientPlayer *player) override;

	static bool isPianoOccupied(int blockdata);
	static void setPianoOccupied(World *pworld, const WCoord &blockpos, bool occupied);
	void clearNormalBlock(World *pworld, const WCoord &blockpos, int blockdata = -1);
private:
	WCoord getCoreBlockPos(World *pworld, const WCoord &blockpos, int blockdata = -1);
	bool isCoreBlock(World *pworld, const WCoord &blockpos);
	virtual void initGeomName() override;

private:
};

#endif//__BLOCK_PIANO_H__