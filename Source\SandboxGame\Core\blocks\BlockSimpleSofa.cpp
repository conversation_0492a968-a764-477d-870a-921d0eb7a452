
#include "BlockSimpleSofa.h"
#include "BlockMaterialMgr.h"
//#include "OgreMaterial.h"
#include "section.h"
#include "SectionMesh.h"
#include "BlockGeom.h"
#include "Collision.h"
#include "world.h"
#include "Environment.h"
#include "ClientActorManager.h"
#include "IClientPlayer.h"
#include "ChunkGenerator.h"
//#include "GameEvent.h"
#include "DefManagerProxy.h"
#include "VehicleWorld.h"
#include "ActorVehicleAssemble.h"
#include "OgreScriptLuaVM.h"
#include "EffectManager.h"
#include "ClientPlayer.h"

IMPLEMENT_BLOCKMATERIAL(BlockSimpleSofa)
using namespace MINIW;

inline bool IsBedHead(int blockdata)
{
	return (blockdata & 4) != 0;
}

void BlockSimpleSofa::initGeomName()
{
	m_geomName = m_Def->Texture2.c_str();
}
int BlockSimpleSofa::getBlockGeomID(int *idbuf, int *dirbuf, const SectionDataHandler* sectionData, const WCoord &blockpos, World* world)
{
	int blockdata = sectionData->getBlock(blockpos).getData();
	if (IsBedHead(blockdata)) idbuf[0] = 1;
	else idbuf[0] = 0;

	dirbuf[0] = blockdata & 3;
	return 1;
}

//const char *BlockSimpleSofa::getGeomName()
//{
//	return GetBlockDef()->Texture2.c_str();
//}

// bool BlockSimpleSofa::onTrigger(World *pworld, const WCoord &input_blockpos, DirectionType face, IClientPlayer *player, const Rainbow::Vector3f &colpoint)
// {
// 	return true;
// }

void BlockSimpleSofa::onNotify(World *pworld, const WCoord &blockpos, int blockid)
{
	int blockdata = pworld->getBlockData(blockpos);
	int ishead = blockdata & 4;
	int placedir = blockdata & 3;
	placedir = RotateDir90(placedir);
	if (ishead)
	{
		if (pworld->getBlockID(NeighborCoord(blockpos, ReverseDirection(placedir))) != getBlockResID())
		{
			pworld->setBlockAir(blockpos);
		}
	}
	else
	{
		if (pworld->getBlockID(NeighborCoord(blockpos, placedir)) != getBlockResID())
		{
			pworld->setBlockAir(blockpos);
			dropBlockAsItem(pworld, blockpos, blockdata);
		}
	}
}

int BlockSimpleSofa::getProtoBlockGeomID(int *idbuf, int *dirbuf)
{
	idbuf[0] = 0;
	dirbuf[0] = DIR_NEG_X;
	return 1;
}

void BlockSimpleSofa::dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata, BLOCK_MINE_TYPE droptype/* =BLOCK_MINE_NOTOOL */, float chance/* =1.0f */, int uin)
{
	int ishead = blockdata & 4;
	if (!ishead) ModelBlockMaterial::dropBlockAsItem(pworld, blockpos, blockdata, droptype, chance, uin);
}

void BlockSimpleSofa::createCollideData(CollisionDetect *coldetect, World *pworld, const WCoord &blockpos)
{
	coldetect->addObstacle(blockpos*BLOCK_SIZE, blockpos*BLOCK_SIZE+WCoord(BLOCK_SIZE,BLOCK_SIZE/2,BLOCK_SIZE));
}
SectionMesh *BlockSimpleSofa::createBlockProtoMesh(int protodata)
{
	SectionMesh* pmesh = ENG_NEW(SectionMesh)();
	SectionSubMesh* psubmesh = pmesh->getSubMesh(getDefaultMtl(), true);

	BlockGeomMeshInfo meshinfo;

	WCoord center(-BLOCK_SIZE / 2, 0, 0);

	getGeom()->getFaceVerts(meshinfo, 0);
	psubmesh->addTriangleList(meshinfo.vertices.data(), meshinfo.vertices.size(), meshinfo.indices.data(), meshinfo.indices.size(), &center);

	getGeom()->getFaceVerts(meshinfo, 1);
	WCoord pos(BLOCK_SIZE, 0, 0);
	pos += center;
	psubmesh->addTriangleList(meshinfo.vertices.data(), meshinfo.vertices.size(), meshinfo.indices.data(), meshinfo.indices.size(), &pos);

	return pmesh;
}

int BlockSimpleSofa::convertDataByRotate(int blockdata, int rotatetype)
{
	return this->commonConvertDataByRotateWithBit(blockdata, rotatetype, 3, 12);
}

void BlockSimpleSofa::onBlockBeDig(World* pworld, const WCoord& blockpos, int process, IClientPlayer* player)
{
	int blockdata = pworld->getBlockData(blockpos);
	int ishead = blockdata & 4;
	int placedir = blockdata & 3;
	placedir = RotateDir90(placedir);
	WCoord partPos;
	ClientPlayer* playerTmp = player->GetPlayer();
	if (!playerTmp) return;
	if (ishead)
	{
		partPos = NeighborCoord(blockpos, ReverseDirection(placedir));
		if (pworld->getBlockID(partPos) == getBlockResID())
		{
			pworld->getEffectMgr()->playBlockCrackEffect(partPos, process, playerTmp->getObjId());
		}
	}
	else
	{
		partPos = NeighborCoord(blockpos, placedir);
		if (pworld->getBlockID(partPos) == getBlockResID())
		{
			pworld->getEffectMgr()->playBlockCrackEffect(partPos, process, playerTmp->getObjId());
		}
	}
}