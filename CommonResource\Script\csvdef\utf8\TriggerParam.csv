参数ID,参数描述,输入值【开关（0=关闭（默认）|1=开启|2=脚本编辑|3=开发者商店商品界面），作为左值时开关（1=开（默认）|0=关），局部触发器中是否显示（1=显示（默认）|0=隐藏）】,模板库选择【开关（0关闭（默认）|1方块类型|2道具类型|3投掷物类型|4生物类型|5状态|6动画表情|7特效类型|8音效|9装备类型），作为左值时开关（大于等于1=开，填左边的那些ID，（默认）|0=关），局部触发器中是否显示（1=显示（默认）|0=隐藏）】,函数选择【开关（0=关闭（默认）|1=开启），作为左值时开关（1=开（默认）|0=关），局部触发器中是否显示（1=显示（默认）|0=隐藏）】,变量选择【开关（0=关闭（默认）|1=开启），作为左值时开关（1=开（默认）|0=关），局部触发器中是否显示（1=显示（默认）|0=隐藏）】,枚举选择【开关（0=关闭（默认）|Enum表中的枚举类型），作为左值时开关（1=开（默认）|0=关）】,枚举内容过滤【全局（1=显示（默认）|0=隐藏），局部（1=显示（默认）|0=隐藏（0|接需要隐藏的枚举ID，默认全隐藏））】,选择【开关（0=关闭（默认）|Enum表中的枚举类型），作为左值时开关（1=开（默认）|0=关）】,选择内容过滤【全局（1=显示（默认）|0=隐藏），局部（1=显示（默认）|0=隐藏（0|接需要隐藏的枚举ID，默认全隐藏））】,选择触发器【开关（0=关闭（默认）|1=触发器|2=触发器组），作为左值时开关（1=开（默认）|0=关），局部触发器中是否显示（1=显示（默认）|0=隐藏）】,默认值,
ID,Desc,UseInput,UseLibrary,UseFunction,UseVariable,UseEnum,EnumFilter,UseObject,ObjFilter,UseTrigger,DefaultValue,
1000,天气,0,0,1,0,1,,0,,0,,
1001,区域,0,0,1,1,0,,"23,0","1,0|230001",0,,
1002,位置,0,0,1,1,0,,"24,0","1,0|240003",0,,
1003,朝向,0,0,0,0,2,,0,,0,,
1004,视角,0,0,0,0,3,,0,,0,,
1005,数值,"1,0",0,1,1,0,,0,,0,,
1006,坐标分量,0,0,0,0,4,,0,,0,,
1007,角度单位,0,0,0,0,5,,0,,0,,
1008,固定底对数,0,0,0,0,6,,0,,0,,
1009,取整方式,0,0,0,0,7,,0,,0,,
1010,字符串,"1,0",0,1,1,0,,55,,0,,
1011,伤害类型,0,0,0,0,8,,0,,0,,
1012,是否,0,0,0,0,9,,0,,0,,
1013,开关,0,0,0,0,10,,0,,0,,
1014,布尔值,0,0,1,1,"32,0",,0,,0,,
1015,最大最小值,0,0,0,0,11,,0,,0,,
1016,比较计算符,0,0,0,0,12,,0,,0,,
1017,数学运算符,0,0,0,0,13,,0,,0,,
1018,玩家,0,0,1,1,0,,25,,0,,
1019,玩家组,0,0,1,1,0,,0,,0,,
1020,玩家属性,0,0,0,0,14,,0,,0,,
1021,运动状态,0,0,0,0,15,,0,,0,,
1022,玩家设置,0,0,0,0,16,,0,,0,,
1023,队伍,0,0,1,0,17,,0,,0,,
1024,队伍属性,0,0,0,0,18,,0,,0,,
1025,方块类型,0,"1,0",1,1,0,,26,,0,,
1026,方块设置,0,0,0,0,19,,0,,0,,
1027,道具,0,0,0,0,0,,0,,0,,
1028,道具类型,0,"2,0",1,1,0,,27,,0,,
1029,投掷物类型,0,"3,0",1,0,0,,56,,0,,
1030,掉落物,0,0,0,0,0,,29,,0,,
1031,生物,0,0,1,1,0,,"30,0","1,0|300003",0,,
1032,生物类型,0,"4,0",1,1,0,,44,,0,,
1033,生物组,0,0,1,1,0,,0,,0,,
1034,生物行动状态,0,0,0,0,20,,0,,0,,
1035,生物属性,0,0,0,0,21,,0,,0,,
1036,生物设置,0,0,0,0,22,,0,,0,,
1037,状态,0,"5,0",0,0,0,,41,,0,,
1038,触发器,0,0,0,0,0,,0,,1,,
1039,触发器组,0,0,0,0,0,,0,,2,,
1040,计时器,0,0,1,1,0,,0,,0,,
1041,动画表情,1,"6,0",0,0,0,,31,,0,,
1042,私有变量,0,0,0,2,0,,0,,0,,
1043,三角函数,0,0,0,0,33,,0,,0,,
1044,投掷物,0,0,0,0,0,,28,,0,,
1045,特效类型,0,"7,0",1,1,0,,34,,0,,
1046,音效,0,"8,0",1,0,0,,0,,0,,
1047,方向,0,0,1,0,0,,35,,0,,
1048,对象,0,0,1,0,0,,36,,0,,
1049,音调,0,0,0,0,37,,0,,0,,
1050,脚本,2,0,0,0,0,,0,,0,,
1051,按键,0,0,0,0,38,,0,,0,,
1052,快捷栏按键,0,0,0,0,39,,40,,0,,
1053,任意值,0,0,1,0,0,,0,,0,,
1054,装备栏,0,0,0,0,42,,43,,0,,
1055,装备类型,0,"9,0",0,0,0,,0,,0,,
1056,颜色,1,11,1,0,0,,0,,0,,
1057,图文信息,0,0,1,0,0,,0,,0,,
1058,图文信息类型,0,0,0,0,49,,0,,0,,
1059,商品,3,0,0,0,0,,0,,0,,
1060,外观类型,0,12,1,0,0,,0,,0,,
1061,任意组,0,0,1,0,0,,0,,0,,
1062,位置组,0,0,1,1,0,,0,,0,,
1063,区域组,0,0,1,1,0,,0,,0,,
1064,数值组,0,0,1,1,0,,0,,0,,
1065,字符串组,0,0,1,1,0,,0,,0,,
1066,布尔值组,0,0,1,1,0,,0,,0,,
1067,方块类型组,0,0,1,1,0,,0,,0,,
1068,道具类型组,0,0,1,1,0,,0,,0,,
1069,生物类型组,0,0,1,1,0,,0,,0,,
1070,计时器组,0,0,1,1,0,,0,,0,,
1071,特效类型组,0,0,1,1,0,,0,,0,,
1072,排序,0,0,0,0,50,,0,,0,,
1073,视频链接,1,0,0,0,0,,0,,0,,
1074,界面,0,13,0,0,0,0,52,,0,,
1075,元件,0,"14,14",0,0,0,0,"51,0",,0,,
1076,图案模板,0,15,1,0,0,0,0,,0,,
1077,移动方式,0,0,0,0,53,0,0,,0,,
1078,时间单位,0,0,0,0,54,0,0,,0,,
1079,页面状态,0,16,0,0,0,0,0,,0,,
1080,音乐,0,17,0,0,0,0,57,,0,,
1081,暂停恢复,0,0,0,0,58,0,0,,0,,
1082,显示板,0,0,0,0,0,0,59,,0,,
1083,全局对象,0,0,0,0,60,0,0,,0,,（开发用90003 130001 10040 函数加参数 ，详情问秦鹏 ）
1084,排行榜,1,0,0,1,0,0,0,,0,,
1085,表,1,0,0,1,0,0,0,,0,,
1086,播放模式,0,0,0,0,61,0,0,,0,,
1101,指定页面,0,18,0,0,0,0,0,,0,,
1137,地形组,0,0,0,0,99,,0,,0,,
