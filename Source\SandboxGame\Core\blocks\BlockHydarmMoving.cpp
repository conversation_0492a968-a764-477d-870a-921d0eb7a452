
#include "BlockHydarmMoving.h"
#include "container_world.h"
#include "world.h"
#include "BlockMaterialMgr.h"
#include "container_hydarm.h"

IMPLEMENT_BLOCKMATERIAL(BlockHydarmMoving)

WorldContainerHydarm *GetWorldPiston(World *pworld, const WCoord &blockpos)
{
	WorldContainer *container = pworld->getContainerMgr()->getContainer(blockpos);
	return dynamic_cast<WorldContainerHydarm *>(container);
}

void BlockHydarmMoving::onBlockRemoved(World *pworld, const WCoord &blockpos, int blockid, int blockdata)
{
	WorldContainer *container = pworld->getContainerMgr()->getContainer(blockpos);
	if(dynamic_cast<WorldContainerHydarm *>(container))
	{
		pworld->getContainerMgr()->destroyContainer(blockpos);
	}
	else BlockMaterial::onBlockRemoved(pworld, blockpos, blockid, blockdata);
}

bool BlockHydarmMoving::onTrigger(World *pworld, const WCoord &blockpos, DirectionType face, IClientPlayer *player, const Rainbow::Vector3f &colpoint)
{
	if(pworld->getContainerMgr()->getContainer(blockpos) == NULL)
	{
		if (pworld->isRemoteMode())
		{
			return true;
		}

		pworld->setBlockAir(blockpos);
		return true;
	}
	else return false;
}

void BlockHydarmMoving::dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata, BLOCK_MINE_TYPE droptype, float chance, int uin/* = -1 */)
{
	WorldContainerHydarm *wp = GetWorldPiston(pworld, blockpos);
	if(wp)
	{
		//g_BlockMtlMgr.getMaterial(wp->m_StoredBlockID)->dropBlockAsItem(pworld, blockpos, wp->m_StoredBlockData);
	}
}

void BlockHydarmMoving::onNotify(World *pworld, const WCoord &blockpos, int blockid)
{
}

bool BlockHydarmMoving::getAABB(CollideAABB &box, World *pworld, const WCoord &blockpos, int blockid, float t, int dir)
{
	if(blockid != 0 && blockid != getBlockResID())
	{
		BlockMaterial* mtl = g_BlockMtlMgr.getMaterial(blockid);
		if(mtl && mtl->getCollisionBoundingBox(box, pworld, blockpos))
		{
			WCoord dp = g_DirectionCoord[dir] * int(t*BLOCK_SIZE);
			if(g_DirectionCoord[dir].x < 0)
			{
				box.pos.x -= dp.x;
			}
			else
			{
				box.dim.x -= dp.x;
			}

			if(g_DirectionCoord[dir].y < 0)
			{
				box.pos.y -= dp.y;
			}
			else
			{
				box.dim.y -= dp.y;
			}

			if(g_DirectionCoord[dir].z < 0)
			{
				box.pos.z -= dp.z;
			}
			else
			{
				box.dim.z -= dp.z;
			}

			return true;
		}
	}

	return false;
}
