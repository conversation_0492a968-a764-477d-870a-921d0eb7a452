--声明
local topdraguiCtrl = Class("topdraguiCtrl",ClassList["UIBaseCtrl"])

local long_press_time = 0.5

--创建
function topdraguiCtrl:Create(param)
	return ClassList["topdraguiCtrl"].new(param)
end

--初始化
function topdraguiCtrl:Init(param)
	self.super:Init(param)

end

--启动
function topdraguiCtrl:Start()
	self.super:Start()
	self.view:InitView()
	self.moveitem = self.view.widgets.moveitem
	self.moveitemui = self.view.moveiteminfo
	self.drag_end_listeners = {}
	self.move_swap_listeners = {}

	GetInst("MiniUIEventDispatcher"):addEventListener(self.moveitem, UIEventType_TouchEnd, function(obj, context)
		--有右键数据不执行,右键长按拖动时如果左键点击会触发这个,等真正的RightEnd执行
		if self.right_data then
			return
		end
		self:DragEnd(obj, context)
	end)

	GetInst("MiniUIEventDispatcher"):addEventListener(self.moveitem, UIEventType_TouchRightEnd, function(obj, context)
		MiniLog("topdraguiCtrl:UIEventType_TouchRightEnd")
		if self.long_press_timer then
			MiniLog("topdraguiCtrl:UIEventType_TouchRightEnd long_press_timer")
			self:RightDragCancel()
			return
		end

		self:DragEnd(obj, context)
	end)
end

function topdraguiCtrl:RegisterMoveSwapListener(name, listener)
	self.move_swap_listeners[name] = listener
end

function topdraguiCtrl:UnRegisterMoveSwapListener(name)
	self.move_swap_listeners[name] = nil
end

function topdraguiCtrl:EnterMoveSwap()
	-- 如果正在拖拽，则不进入这个模式
	if self.drag_move_listener or self.right_drag_move_listener then
		return false
	end

	self.enter_move_swap = true
	self.moveitem:setVisible(false)
	GetInst("MiniUIManager"):ShowUI("topdraguiAutoGen")
	self.drag_move_listener = GetInst("MiniUIEventDispatcher"):addEventListener(self.view.root, UIEventType_MouseMove, function(obj, context)
		for k,v in pairs(self.move_swap_listeners) do
			local mouse_pos = context:getInput():getPosition()
			if type(v) == "function" then
				v(mouse_pos)
			end
		end
	end)
	return true
end

function topdraguiCtrl:ExitMoveSwap()
	self:DragCancel()
end

--刷新
function topdraguiCtrl:Refresh()
	self.super:Refresh()

end

--隐藏
function topdraguiCtrl:Reset()
	self.super:Reset()
	--self:DragCancel()
end

--关闭
function topdraguiCtrl:Remove()
	self.super:Reset()

	self:DragCancel()
	self.swap_check_func = nil
	self.start_index = nil
end

function topdraguiCtrl:DragCancel()
	MiniLog("topdraguiCtrl:DragCancel")
	if self.long_press_timer then
		self:RightDragCancel()
	end
	self.enter_move_swap = false
	GetInst("MiniUIManager"):HideUI("topdraguiAutoGen")
	--取消拖拽
	if self.drag_move_listener then
		GetInst("MiniUIEventDispatcher"):removeEventListener(self.view.root, self.drag_move_listener)
		self.drag_move_listener = nil
	end

	if self.right_drag_move_listener then
		GetInst("MiniUIEventDispatcher"):removeEventListener(self.view.root, self.right_drag_move_listener)
		self.right_drag_move_listener = nil
	end

	if not self.itemmoved or not self.move_item_info then
		return
	end
	--放回原位
	local mouseindex = MOUSE_PICKITEM_INDEX + 1
	CurMainPlayer:moveItem(mouseindex, self.start_index, self.move_item_info.num)
	self.itemmoved = false
	self.move_item_info = nil
	self.dragendtick = 2
	threadpool:work(function()
		threadpool:wait(0)
		threadpool:wait(0)
		self.dragendtick = nil
	end)
end

--消息处理
function topdraguiCtrl:FGUIHandleEvent(eventName)

end

function topdraguiCtrl:SetDragStartData(data)
	self.swap_check_func = data.swap_check_func
	self.start_index = data.start_index
	self.move_item_info = nil
	self.itemmoved = false  -- 标记是否已经移动过
	self.drag_start_pos = data.drag_start_pos  -- 拖拽开始位置

	-- 延迟到拖动时才开始移动
	local sortingorder = GetInst("MiniUIManager"):getCurrentChildrenMaxSortingOrder()
	local current_sortingorder = self.view.root:getSortingOrder()
	if current_sortingorder < sortingorder then
		self.view.root:setSortingOrder(sortingorder + 1)
	end

	GetInst("MiniUIManager"):ShowUI("topdraguiAutoGen")
	self.drag_move_listener = GetInst("MiniUIEventDispatcher"):addEventListener(self.view.root, UIEventType_MouseMove, function(obj, context)
		MiniLog("topdraguiCtrl:DragMove ")
		self:DragMove(obj, context)
	end)
end

-- swap_check_func 检查交换 如果可以交换返回true 否则返回false
-- 注意！！swap_check_func 如果为nil 则表示不会发生交换
function topdraguiCtrl:DragStart(start_index, obj, context, swap_check_func)
	if self.enter_move_swap then
		return
	end

	-- 如果正在拖拽，则不进行拖拽 (右键长按也会触发DragStart,防止和左键冲突)
	if self.drag_move_listener or self.right_drag_move_listener then
		return
	end

	--如果格子是锁定的，则不进行拖拽
	if GetInst("SocContainerWaitManage"):IsLockGrid(start_index) then
		return
	end

	local itemId = ClientBackpack:getGridItem(start_index)
	local count = ClientBackpack:getGridNum(start_index)
	MiniLog("topdraguiCtrl:DragStart", start_index, itemId, count)

	if itemId == 0 or count == 0 then
		MiniLog("topdraguiCtrl:DragStart itemId == 0 or count == 0", start_index, itemId, count)
		-- self.moveitem:setVisible(false)
		return
	end

	local data = {
		start_index = start_index,
		obj = obj,
		swap_check_func = swap_check_func,
		move_item_id = itemId,
		move_item_count = count
	}
	data.drag_start_pos = context:getInput():getPosition()

	self.moveitem:setVisible(false)
	self:UpdateMoveItemPos(context)

	self:SetDragStartData(data)
end

function topdraguiCtrl:DragMove(obj, context)
	if not self.drag_move_listener then
		return
	end

	local mouse_pos = context:getInput():getPosition()
	local delta_x = mouse_pos.x - self.drag_start_pos.x
	local delta_y = mouse_pos.y - self.drag_start_pos.y

	if not self.itemmoved and (math.abs(delta_x) > 5 or math.abs(delta_y) > 5) then
		MiniLog("topdraguiCtrl:DragMove itemmoved true")
		self.move_item_info = UIUtils:GetItemInfoByGrid(self.start_index)
		if not self.move_item_info or self.move_item_info.itemid == 0 then
			self:DragEnd(obj, context)
			return
		end

		self.itemmoved = true
		self.moveitem:setVisible(true)
		UIUtils:SetPackItem(self.moveitemui, self.move_item_info)

		CurMainPlayer:moveItem(self.start_index, MOUSE_PICKITEM_INDEX + 1, self.move_item_info.num)
	end

	if self.itemmoved then
		self:UpdateMoveItemPos(context)
	end
end

function topdraguiCtrl:RightDragStartCheck(start_index, obj, context, swap_check_func, isshift, rightclick_func)
	if self.enter_move_swap then
		return
	end

	if self.drag_move_listener or self.right_drag_move_listener then
		return
	end

	--如果格子是锁定的，则不进行拖拽
	if GetInst("SocContainerWaitManage"):IsLockGrid(start_index) then
		return
	end

	local itemId = ClientBackpack:getGridItem(start_index)
	local count = ClientBackpack:getGridNum(start_index)
	MiniLog("topdraguiCtrl:RightDragStartCheck", start_index, itemId, count)
	--count <= 1不能拆分
	if itemId == 0 or count <= 1 then
		MiniLog("topdraguiCtrl:RightDragStartCheck itemId == 0 or count <= 1", start_index, itemId, count)
		if type(rightclick_func) == "function" then
			rightclick_func(context:getInput():getPosition())
		end
		return
	end

	local right_data = {}
	self.right_data = right_data

	right_data.start_index = start_index
	right_data.start_count = count
	right_data.obj = obj
	right_data.drag_start_pos = context:getInput():getPosition()
	right_data.swap_check_func = swap_check_func
	right_data.click_start_time = os.time()
	right_data.move_item_id = itemId
	--shift 单个
	right_data.move_item_count = isshift and 1 or math.floor(count * 0.5)
	right_data.original_item_count = count

	self.moveitem:setVisible(false)

	--启动mousemove监听 判断是否还在self.right_data.obj 范围内
	local sortingorder = GetInst("MiniUIManager"):getCurrentChildrenMaxSortingOrder()
	local current_sortingorder = self.view.root:getSortingOrder()
	if current_sortingorder < sortingorder then
		self.view.root:setSortingOrder(sortingorder + 1)
	end

	GetInst("MiniUIManager"):ShowUI("topdraguiAutoGen")
	self.right_drag_move_listener = GetInst("MiniUIEventDispatcher"):addEventListener(self.view.root, UIEventType_MouseMove, function(obj, context)
		MiniLog("topdraguiCtrl:RightDragMove ")
		self:RightDragMove(obj, context)
	end)
	-- 监听右键长按结束
	self.drag_right_touch_end_listener = GetInst("MiniUIEventDispatcher"):addEventListener(self.view.root, UIEventType_TouchRightEnd, function(obj, context)
		MiniLogWarning("topdraguiCtrl:DragRightTouchEnd "..start_index)
		self:RightDragCancel()
		if type(rightclick_func) == "function" then
			rightclick_func(context:getInput():getPosition())
		end
	end)

	self:StartLongPressTimer()
end

function topdraguiCtrl:RightDragMove(obj, context)
	if not self.right_drag_move_listener then
		return
	end

	--鼠标有移动判断是否还在self.right_data.obj 范围内
	local mouse_pos = context:getInput():getPosition()
	if not UIUtils:PosInObj(mouse_pos, self.right_data.obj) then
		self:RightDragCancel()
		return
	end
end

function topdraguiCtrl:StartLongPressTimer()
	MiniLog("topdraguiCtrl:StartLongPressTimer")
	--延迟执行,如果定时器没有被取消说明就是长按
	self.long_press_timer = threadpool:delay(long_press_time,function()
		MiniLog("topdraguiCtrl:StartLongPressTimer long_press_time")
        self.long_press_timer = nil

		GetInst("MiniUIManager"):HideUI("topdraguiAutoGen")
		if self.right_drag_move_listener then
			GetInst("MiniUIEventDispatcher"):removeEventListener(self.view.root, self.right_drag_move_listener)
			self.right_drag_move_listener = nil
		end

		if self.drag_right_touch_end_listener then
			GetInst("MiniUIEventDispatcher"):removeEventListener(self.view.root, self.drag_right_touch_end_listener)
			self.drag_right_touch_end_listener = nil
		end

		self:RightDragStart()
    end)
end

function topdraguiCtrl:RightDragCancel()
	MiniLog("topdraguiCtrl:RightDragCancel")
	GetInst("MiniUIManager"):HideUI("topdraguiAutoGen")
	self.right_data = nil
	threadpool:kick(self.long_press_timer)
	self.long_press_timer = nil
	if self.right_drag_move_listener then
		GetInst("MiniUIEventDispatcher"):removeEventListener(self.view.root, self.right_drag_move_listener)
		self.right_drag_move_listener = nil
	end

	if self.drag_right_touch_end_listener then
		GetInst("MiniUIEventDispatcher"):removeEventListener(self.view.root, self.drag_right_touch_end_listener)
		self.drag_right_touch_end_listener = nil
	end
end

function topdraguiCtrl:IsDragging()
	return self.dragendtick or self.itemmoved
end

function topdraguiCtrl:RightDragStart()
	MiniLog("topdraguiCtrl:RightDragStart")
	if not self.right_data then
		return
	end

	--fix 校验start_index 是否还是原来的东西
	local itemId = ClientBackpack:getGridItem(self.right_data.start_index)
	local count = ClientBackpack:getGridNum(self.right_data.start_index)
	if itemId ~= self.right_data.move_item_id or count ~= self.right_data.original_item_count then
		MiniLog("topdraguiCtrl:RightDragStart itemId ~= self.right_data.move_item_id or count ~= self.right_data.original_item_count", itemId, count)
		self:RightDragCancel()
		return
	end

	local data = {
		start_index = self.right_data.start_index,
		obj = self.right_data.obj,
		drag_start_pos = self.right_data.drag_start_pos,
		swap_check_func = self.right_data.swap_check_func,
		move_item_id = self.right_data.move_item_id,
		move_item_count = self.right_data.move_item_count
	}

	self:SetDragStartData(data)
	self.move_item_info = UIUtils:GetItemInfoByGrid(self.start_index)
	self.move_item_info.num = self.right_data.move_item_count
	CurMainPlayer:moveItem(self.start_index, MOUSE_PICKITEM_INDEX + 1, self.move_item_info.num)

	self.itemmoved = true
	self.moveitem:setVisible(true)
	UIUtils:SetPackItem(self.moveitemui, self.move_item_info)
	self.moveitem:setPosition(self.right_data.drag_start_pos.x, self.right_data.drag_start_pos.y)

	self.right_data = nil
end

function topdraguiCtrl:UpdateMoveItemPos(context)
	local mouse_pos = context:getInput():getPosition()
	self.moveitem:setPosition(mouse_pos.x, mouse_pos.y)
end

-- 注册拖拽结束回调 callback返回目标gridindex
function topdraguiCtrl:RegisterDragEndListener(name, find_callback, after_callback)
	if self.drag_end_listeners[name] then
		MiniLog("topdraguiCtrl:RegisterDragEndListener already registered, error!", name)
		return
	end

	self.drag_end_listeners[name] = {find_callback = find_callback, after_callback = after_callback}
end

function topdraguiCtrl:UnRegisterDragEndListener(name)
	self.drag_end_listeners[name] = nil
end


function topdraguiCtrl:DragEnd(obj, context)
	MiniLog("topdraguiCtrl:DragEnd", obj, context)
	-- self.moveitem:setVisible(false)
	GetInst("MiniUIManager"):HideUI("topdraguiAutoGen")

	if self.drag_move_listener then
		GetInst("MiniUIEventDispatcher"):removeEventListener(self.view.root, self.drag_move_listener)
		self.drag_move_listener = nil
	end

	if self.right_drag_move_listener then
		GetInst("MiniUIEventDispatcher"):removeEventListener(self.view.root, self.right_drag_move_listener)
		self.right_drag_move_listener = nil
	end

	if not self.itemmoved or not self.move_item_info then
		return
	end

	-- grid_index 目标gridindex
	-- nil表示需要丢弃  -1 表示放回原位
	local endpos = context:getInput():getPosition()
	local grid_index = nil
	local after_callback = nil
	for _, callbacks in pairs(self.drag_end_listeners) do
		grid_index = callbacks.find_callback(endpos, self.start_index, self.move_item_info.num)
		if grid_index then
			after_callback = callbacks.after_callback
			break
		end
	end
	MiniLog("topdraguiCtrl:DragEnd grid_index", self.start_index, self.move_item_info.num, grid_index)

	local mouseindex = MOUSE_PICKITEM_INDEX + 1
	if grid_index then
		-- 放回原位
		if grid_index == self.start_index or grid_index == -1 then
			CurMainPlayer:moveItem(mouseindex, self.start_index, self.move_item_info.num)
		else
			local target_itemId = ClientBackpack:getGridItem(grid_index)
			local target_count = ClientBackpack:getGridNum(grid_index)

			-- 检查是否可以交换
			if target_itemId ~= 0 then
				if not self.swap_check_func or self.swap_check_func(grid_index) then
					CurMainPlayer:socMoveItem(self.start_index, grid_index, mouseindex)
				else
					-- 不能交换，放回原位
					CurMainPlayer:moveItem(mouseindex, self.start_index, self.move_item_info.num)
				end
			else
				--CurMainPlayer:moveItem(mouseindex, grid_index, self.move_item_info.num)
				-- 鼠标格子无法定位来源不好埋点换成新的move
				CurMainPlayer:socMoveItem(self.start_index, grid_index, mouseindex)
			end
		end

		if after_callback then
			after_callback(grid_index)
		end
	else
		-- 丢弃
		CurMainPlayer:discardItem(mouseindex, self.move_item_info.num)
	end

	self.itemmoved = false
	self.move_item_info = nil
	self.dragendtick = 2
	threadpool:work(function()
		threadpool:wait(0)
		threadpool:wait(0)
		self.dragendtick = nil
	end)
end
