--启动层
require("luascript.socket.lua")
require("ui.mobile.FestivalActivitiesInterface")
require("ui.mobile.tprint")
require("ui.mobile.tools.Android")
require("ui.mobile.tools.ClassesCache")
require("ui.mobile.tools.DecoratorUtils")
require("ui.mobile.ReportTraceidMgr")
require("ui.mobile.globaldata")
require("ui.mobile.voxel_tools")
require("ui.mobile.tools.ItemUseSkinDefTools")
require("ui.mobile.constants.LuaConstants")
require("ui.mobile.constants.LuaSocConstants")
require("ui.mobile.error_msg")
require("ui.mobile.uicommon")
require("ui.mobile.uiDisplayStatistics")
require("ui.mobile.builderBP")
require("ui.mobile.tools.LayoutManagerFactory")
require("ui.mobile.tools.ModelDraggerFactory")
require("ui.mobile.tools.ModelRotatorFactory")
require("ui.mobile.tools.TimerFactory")
require("ui.mobile.tools.InterpolatorFactory")
require("ui.mobile.tools.AnimatorFactory")
require("ui.mobile.tools.ComplexAnimatorFactory")
require("ui.mobile.http")
require("ui.mobile.hotfix")
require("ui.mobile.ad_logic")
require("ui.mobile.tools.GoogleAnalytics")
require("ui.mobile.ClassEx")

require("miniui.helper.SafeCallFuncHelper")
require("miniui.helper.uiRequire.CommonUIRequireHelper")
require("miniui.helper.uiRequire.UIRequireHelper")
require("miniui.LuaRequireMgr")
require("ui.mobile.LuaPreLoadComResHelper")
require("ui.mobile.LuaPreLoadMgr")
require("ui.mobile.report.ReportGameLuaErrManager")

if GameUI:IsUseAutoLoadLegacyUI() then
    require("legacyui.uiConfig")
end

--开启就会生成新的xml在writepath/legacyui下
if GameUI:IsExportLeagcyUI() then
     require("miniui.cocos.LoadXmlTest")
end
if GameUI:IsUseAutoLoadLegacyUI() then
    GameUI:ParseUIInXml("ui/mobile/cursor.xml")
    GameUI:ParseUIInXml("ui/mobile/Fonts.xml")
    GameUI:ParseUIInXml("ui/mobile/accelkeys.xml")
end

require("ui.mobile.mvc.UIManager")
--account logic
require("miniui.module.login.LoginBase.AccountBase")
require("miniui.module.login.LoginBase.LoginDefines")
require("miniui.module.login.LoginBase.StorageBase")
require("miniui.module.login.LoginBase.LoginBase")
require("miniui.module.login.LoginBase.LoginCfgDefines")
require("miniui.module.NewAccountManager.NewAccountBase")
require("miniui.module.login.LoginCfgMgr")
require("miniui.module.login.LoginModelMgr")
require("miniui.module.login.LoginSwitchCfg")
require("miniui.module.login.LoginManager")
require("miniui.module.NewAccountManager.NewAccountManager")
require("miniui.module.NewAccountManager.NewAccountHelper")

GameUI:LoadXmlForLua("ui/mobile/cursor.xml")
GameUI:LoadXmlForLua("ui/mobile/Fonts.xml")
GameUI:LoadXmlForLua("ui/mobile/uiconfig/uitemplate.xml")
GameUI:LoadXmlForLua("ui/mobile/uicommon.xml")
require("ui.mobile.uitemplate")

GameUI:LoadXmlForLua("ui/mobile/tips.xml")
GameUI:LoadXmlForLua("ui/mobile/messagebox.xml")

require("miniui.manager.MiniUIManager")
require("miniui.manager.MiniUISceneMgr")
require("miniui.manager.MiniUIFunction")
require("miniui.manager.MiniUIEventDispatcher")
require("miniui.manager.MiniUIScheduler")
require("miniui.manager.MiniUIComponents")
require("miniui.manager.MiniUICommonScene")
require("miniui.manager.MiniUICommonNodeClass")
require("miniui.manager.MiniUIInterface")
require("miniui.manager.MiniUiInputMgr")
require("ui.mobile.mvc.UIBaseModel")
require("ui.mobile.mvc.UIBaseView")
require("ui.mobile.mvc.UIBaseCtrl")

GameUI:LoadXmlForLua("ui/mobile/loading.xml")
GameUI:LoadXmlForLua("ui/mobile/account.xml")
GameUI:LoadXmlForLua("ui/mobile/setting.xml")

--工具背包仓库按钮需要引用商店模块
GameUI:LoadXmlForLua("ui/mobile/mvc/shop/Shop.xml")
GameUI:LoadXmlForLua("ui/mobile/mvc/shop/shopCommend/ShopCommend.xml")
GameUI:LoadXmlForLua("ui/mobile/mvc/shop/shopSkinLib/ShopSkinLib.xml")
GameUI:LoadXmlForLua("ui/mobile/mvc/shop/shopCustomSkinLib/ShopCustomSkinLib.xml")
GameUI:LoadXmlForLua("ui/mobile/mvc/shop/shopGain/ShopGain.xml")

GameUI:LoadXmlForLua("ui/mobile/mvc/shop/shopCustomSkinEdit/ShopCustomSkinEdit.xml")
GameUI:LoadXmlForLua("ui/mobile/mvc/shop/shopSkinDisplay/ShopSkinDisplay.xml")
GameUI:LoadXmlForLua("ui/mobile/mvc/shop/shopMounts/ShopMounts.xml")
GameUI:LoadXmlForLua("ui/mobile/mvc/shop/shopRole/ShopRole.xml")
GameUI:LoadXmlForLua("ui/mobile/mvc/shop/shopItem/ShopItem.xml")
GameUI:LoadXmlForLua("ui/mobile/mvc/shop/shopWishList/ShopWishList.xml")
GameUI:LoadXmlForLua("ui/mobile/mvc/shop/shopWarehouse/ShopWarehouse.xml")
GameUI:LoadXmlForLua("ui/mobile/mvc/shop/shopRecharge/ShopRecharge.xml")
GameUI:LoadXmlForLua("ui/mobile/mvc/shop/shopGift/ShopGift.xml")

require("ui.mobile.enum.ugcenum")
require("sandboxengine.common.localdata.LocalData")
require("sandboxengine.common.localdata.LocalDataManager")

require("ui.mobile.mvc.UIBaseModel")
require("ui.mobile.mvc.UIBaseView")
require("ui.mobile.mvc.UIBaseCtrl")
require("ui.mobile.ad_interface")
require("ui.mobile.ad_service")
require("ui.mobile.OverallManager")

GameUI:LoadXmlForLua("ui/mobile/mvc/shop/shopRecharge/ShopRecharge.xml")
require("ui.mobile.AvatarBodyManager")

require("ui.mobile.skinconfigctrl")

require("ui.mobile.lite")
require("ui.mobile.DelayedTaskManager")
require("ui.mobile.mvc.newAccountSystem.AccountSysHelper.AccountSysConfig")
--require("ui.mobile.mvc.newAccountSystem.AccountSysHelper.AccountSysDataManager")
require("ui.mobile.mvc.newAccountSystem.AccountSysHelper.AccountSysInterface")

require("ui.mobile.mvc.comeBackSystem.ComeBackSysConfig.ComeBackSysConfig")

require("ui.mobile.minilobbyinterface")
require("ui.mobile.mvc.homeland.prayTree.HomePrayInterface")
require("ui.mobile.mvc.homeland.GuideTask.HomeLandGuideTaskManager")
require("ui.mobile.lobbyCompatibleFuncInterface")

require("ui.mobile.mvc.NewbieGuide.NewbieGuideManager")
require("ui.mobile.mvc.BirthdayParty.BirthdayPartyMgr")

require("ui.mobile.festivalActivity")
require("ui.mobile.streaming")

require("sandboxengine.gamelogic.GameLoadingSystem")
require("sandboxengine.gameactor.actorhorse.HorseSkillManager")

require("ui.mobile.common.VisualCfgMgr")
require("ui.mobile.mapservice")
require("editor.luascript.EditorInterface")
require("editor.luascript.ministudioHttpMd5")
require("editor.luascript.loadAvatar")

GameUI:LoadXmlForLua("editor/luascript/CoordAxis/CoordAxisFrame.xml")
require("ui.mobile.DelayedTaskManager")

-- 纯粹的配置，公共部分
require("luascript.conf.url_path_conf_base")
 -- 配置接口相关
require("luascript.conf.url_path")
-- 纯粹的配置,海外独有部分
require("luascript.conf.url_path_conf")

require("ui.mobile.deeplink.autojump_fuc")
require("ui.mobile.deeplink.DeepLink")
require("ui.mobile.mvc.account.Mode.GameMode")
require("ui.mobile.mvc.account.FunctionLimit.FunctionLimit")
require("ui.mobile.mvc.account.FunctionLimit.FunctionLimitHandle")
require("miniui.manager.MiniUIManager")

--MiniBase
require("ui.mobile.miniBase.MiniBaseManager")
require("ui.mobile.miniBase.MiniBaseEventManager")

--shop
require("ui.mobile.mvc.shop.shopAskForGift.ShopAskForPay")
require("ui.mobile.mvc.shop.shopAskForGift.ShopAskForHuaWeiPay")

-- url签名加密工具
require("ui.mobile.common.md5Sgin")
require("ministudio.CommonInterface")
