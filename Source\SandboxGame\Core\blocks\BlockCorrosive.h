#ifndef __BLOCK_CORROSIVE_H__
#define __BLOCK_CORROSIVE_H__

#include "BlockMaterial.h"
#include "BlockBasic.h"
#include "BlockErosionHelper.h"
#include "BlockArchitecturalBase.h"

/**
 * 腐蚀性建筑方块
 * 基于 BasicBlockMaterial，具有自身腐蚀功能的建筑方块
 * 使用现有的 ErosionContainer 系统，方块会随时间自动腐蚀并最终消失
 * 参考 BlockMultiWall 的实现，但只是单个方块
 */
class BlockCorrosive : public ModelBlockMaterial, public BlockArchitecturalBase //tolua_exports
{ //tolua_exports
    DECLARE_BLOCKMATERIAL(BlockCorrosive)
    //typedef BasicBlockMaterial Super;
    
public:
    //tolua_begin
    BlockCorrosive();
    virtual ~BlockCorrosive();
    
    // 基础功能实现
    virtual void init(int resid) override;
    virtual void onBlockAdded(World* pworld, const WCoord& blockpos) override;
    virtual void onBlockRemoved(World* pworld, const WCoord& blockpos, int blockid, int blockdata) override;
    virtual void onPlayRandEffect(World* pworld, const WCoord& blockpos) override;
    virtual bool canPutOntoPos(WorldProxy* pworld, const WCoord& blockpos) override;
    
    // 容器系统
    virtual bool hasContainer() override { return true; }
    virtual WorldContainer* createContainer(World* pworld, const WCoord& blockpos) override;
    virtual WorldContainer* getCoreContainer(World* pworld, const WCoord& blockpos) override;

    // 建筑方块功能
    virtual bool onBlockDamaged(World* pworld, const WCoord& blockpos, IClientPlayer* player, int attack_type, float damage) override;
    virtual bool onBlockUpGrade(World* pworld, const WCoord& blockpos, int upgradeNum, IClientPlayer* player) override;
    virtual bool onBlockRepaired(World* pworld, const WCoord& blockpos, IClientPlayer* player, float amount) override;
    virtual void onBlockDestroyedBy(World* pworld, const WCoord& blockpos, int blockdata, BLOCK_DESTROY_REASON_T destroytype, IClientActor* bywho) override;
    virtual void dropBlockAsItem(World* pworld, const WCoord& blockpos, int blockdata = 0, BLOCK_MINE_TYPE droptype = BLOCK_MINE_NOTOOL, float chance = 1.0f, int uin = -1) override;
    virtual int getBlockHP(World* pworld, const WCoord& blockpos) override;
    virtual BlockMaterial::BlockType BlockTypeId() override { return BlockMaterial::BlockType::BlockType_Architecture; }
    
    // BlockArchitecturalBase 必需实现
    virtual WCoord getCoreBlockPos(World* pworld, const WCoord& blockpos, int blockdata = -1) override;
    virtual bool getBlockRange(World* pworld, const WCoord& blockpos, std::vector<WCoord>& blockList, bool includeSelf = false) override;
    
    //tolua_end
    virtual bool hasSolidTopSurface(int blockdata) override;
private:
    // 播放腐蚀效果
    void PlayCorrosionEffect(World* pworld, const WCoord& blockpos);
}; //tolua_exports

#endif // __BLOCK_CORROSIVE_H__
