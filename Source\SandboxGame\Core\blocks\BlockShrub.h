/*
	新加灌木类树木
	code-by: liya
*/

#ifndef __BLOCK_SHRUB_H__
#define __BLOCK_SHRUB_H__

#include "BlockMaterial.h"
#include <unordered_map>

// 灌木成长阶段
//tolua_begin
enum ShrubStage
{
	SHRUB_STAGE_SEED = 0,		// 农作物种子阶段
	SHRUB_STAGE_GROWTH,			// 农作物生长阶段
	SHRUB_STAGE_HARVEST,		// 农作物收成阶段
};
//tolua_end

class BlockShrub : public ModelBlockMaterial //tolua_exports
{ //tolua_exports
	//typedef ModelBlockMaterial Super;
	DECLARE_BLOCKMATERIAL(BlockShrub)
public:
	//tolua_begin
	BlockShrub();
	virtual ~BlockShrub();
	virtual void init(int resid) override;

	//virtual const char *getGeomName() override;
	virtual void createBlockMesh(const BuildSectionMeshData& data, const WCoord &blockpos, SectionMesh *poutmesh) override;
	virtual void onBlockRemoved(World *pworld, const WCoord &blockpos, int blockid, int blockdata) override;

	//virtual BlockDrawType getDrawType() override
	//{
	//	return BLOCKDRAW_GRASS;
	//}

	virtual void blockTick(World *pworld, const WCoord &blockpos);
	virtual void  forceResh(World* pworld, const WCoord& blockpos);

	// 施肥
	virtual bool onFertilized(World *pworld, const WCoord &blockpos, int fertiliser);
	virtual bool canStayOnPos(WorldProxy *pworld, const WCoord &blockpos);
	virtual bool canPutOntoPos(WorldProxy *pworld, const WCoord &blockpos);
	virtual void onNotify(World *pworld, const WCoord &blockpos, int blockid);
	virtual void onBlockDestroyedBy(World *pworld, const WCoord &blockpos, int blockdata, BLOCK_DESTROY_REASON_T destroytype, IClientActor *bywho);

	// 掉落物
	virtual void dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata = 0, BLOCK_MINE_TYPE droptype = BLOCK_MINE_NOTOOL, float chance = 1.0f, int uin = -1);

	void markOrGrowMarked(World *pworld, const WCoord &blockpos);
	// 生长速度
	float getGrowRate(World *pworld, const WCoord &blockpos); //get crops grow rate

	//virtual bool isSolid() { return false; };
	//tolua_end
protected:
	int getMaxBlockdata();
private:
	RenderBlockMaterial* createMaterial(const char *material);
	RenderBlockMaterial* getBlockMaterial(int key);
	int getBlockFaceMeshIndex(int key);
	virtual bool canThisPlantGrowOnThisBlockID(int blockid);

	void checkChange(World *pworld, const WCoord &blockpos);
	virtual void initGeomName() override;
	virtual void initDrawType() override;
private:
	std::map<int, RenderBlockMaterial*>	m_materialMap;
	std::unordered_map<int, int>		m_faceMeshMap;
}; //tolua_exports

#endif

