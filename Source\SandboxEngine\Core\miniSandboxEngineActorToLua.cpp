/*
** Lua binding: miniSandboxEngineActor
*/

#ifndef __cplusplus
#include "stdlib.h"
#endif
#include "string.h"

#include "Minitolua.h"

#include "ui_common.h"

/* Exported function */
TOLUA_API int  tolua_miniSandboxEngineActor_open (lua_State* tolua_S);

#include "actors/helper/ActorTypes.h"
#include "actors/helper/Actor_Base.h"
#include "actors/helper/IClientPlayer.h"
#include "actors/helper/IClientActor.h"
#include "actors/helper/IClientMob.h"
#include "actors/helper/IPlayerControl.h"
#include "actors/helper/IActorLiving.h"
#include "actors/helper/ActorBodySequence.h"
#include "actors/helper/ActorComponent_Base.h"
#include "actors/helper/ClientActorDef.h"
#include "actors/helper/ClientActorHelper.h"
#include "actors/helper/ActorManagerInterface.h"

/* function to release collected object via destructor */
#ifdef __cplusplus

static int tolua_collect_OneAttackData (lua_State* tolua_S)
{
 OneAttackData* self = (OneAttackData*) tolua_tousertype(tolua_S,1,0);
	Mtolua_delete(self);
	return 0;
}

static int tolua_collect_WCoord (lua_State* tolua_S)
{
 WCoord* self = (WCoord*) tolua_tousertype(tolua_S,1,0);
	Mtolua_delete(self);
	return 0;
}

static int tolua_collect_ActorComponentBase (lua_State* tolua_S)
{
 ActorComponentBase* self = (ActorComponentBase*) tolua_tousertype(tolua_S,1,0);
	Mtolua_delete(self);
	return 0;
}

static int tolua_collect_SummonPetInfomation (lua_State* tolua_S)
{
 SummonPetInfomation* self = (SummonPetInfomation*) tolua_tousertype(tolua_S,1,0);
	Mtolua_delete(self);
	return 0;
}

static int tolua_collect_WORLD_ID (lua_State* tolua_S)
{
 WORLD_ID* self = (WORLD_ID*) tolua_tousertype(tolua_S,1,0);
	Mtolua_delete(self);
	return 0;
}
#endif


/* function to register type */
static void tolua_reg_types (lua_State* tolua_S)
{
 tolua_usertype(tolua_S,"IClientPlayer");
 tolua_usertype(tolua_S,"game::common::PB_SkillCDData");
 tolua_usertype(tolua_S,"WCoord");
 tolua_usertype(tolua_S,"ChunkViewer");
 tolua_usertype(tolua_S,"game::common::PB_RoleData");
 tolua_usertype(tolua_S,"BackPackGrid");
 tolua_usertype(tolua_S,"World");
 tolua_usertype(tolua_S,"IClientActor");
 tolua_usertype(tolua_S,"SummonPetInfomation");
 tolua_usertype(tolua_S,"VipInfo");
 tolua_usertype(tolua_S,"ActorManagerInterface");
 tolua_usertype(tolua_S,"IActorLocoMotion");
 tolua_usertype(tolua_S,"IActorLiving");
 tolua_usertype(tolua_S,"ActorBase");
 tolua_usertype(tolua_S,"WorldContainer");
 tolua_usertype(tolua_S,"OneAttackData");
 tolua_usertype(tolua_S,"MNSandbox::SandboxNode");
 tolua_usertype(tolua_S,"WORLD_ID");
 tolua_usertype(tolua_S,"ChunkIOMgr");
 tolua_usertype(tolua_S,"Rainbow::Vector3f");
 tolua_usertype(tolua_S,"MpActorTrackerEntry");
 tolua_usertype(tolua_S,"IBackPack");
 tolua_usertype(tolua_S,"BodyEffectBrief");
 tolua_usertype(tolua_S,"ClientPlayer");
 tolua_usertype(tolua_S,"ActorAttribut");
 tolua_usertype(tolua_S,"std::vector<MpActorTrackerEntry*>");
 tolua_usertype(tolua_S,"IPlayerControl");
 tolua_usertype(tolua_S,"IClientMob");
 tolua_usertype(tolua_S,"Rainbow::SceneMGTNode");
 tolua_usertype(tolua_S,"MNSandbox::SceneComponent");
 tolua_usertype(tolua_S,"Rainbow::IActorBody");
 tolua_usertype(tolua_S,"MNSandbox::Component");
 tolua_usertype(tolua_S,"std::set<int>");
 tolua_usertype(tolua_S,"ActorComponentBase");
}

/* function: MNSandbox::getAttackBodyType */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_MNSandbox_getAttackBodyType00
static int tolua_miniSandboxEngineActor_MNSandbox_getAttackBodyType00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_iscppstring(tolua_S,1,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  const std::string partname = ((const std::string)  tolua_tocppstring(tolua_S,1,0));
  {
   ATTACK_BODY_TYPE tolua_ret = (ATTACK_BODY_TYPE)  MNSandbox::getAttackBodyType(partname);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
   tolua_pushcppstring(tolua_S,(const char*)partname);
  }
 }
 return 2;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getAttackBodyType'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* get function: atktype of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_get_OneAttackData_atktype
static int tolua_get_OneAttackData_atktype(lua_State* tolua_S)
{
  OneAttackData* self = (OneAttackData*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'atktype'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushnumber(tolua_S,(lua_Number)self->atktype);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: atktype of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_set_OneAttackData_atktype
static int tolua_set_OneAttackData_atktype(lua_State* tolua_S)
{
  OneAttackData* self = (OneAttackData*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'atktype'",NULL);
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->atktype = ((ATTACK_TYPE) (int)  tolua_tonumber(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: parttype of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_get_OneAttackData_parttype
static int tolua_get_OneAttackData_parttype(lua_State* tolua_S)
{
  OneAttackData* self = (OneAttackData*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'parttype'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushnumber(tolua_S,(lua_Number)self->parttype);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: parttype of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_set_OneAttackData_parttype
static int tolua_set_OneAttackData_parttype(lua_State* tolua_S)
{
  OneAttackData* self = (OneAttackData*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'parttype'",NULL);
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->parttype = ((ATTACK_BODY_TYPE) (int)  tolua_tonumber(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: atkpoints of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_get_OneAttackData_atkpoints
static int tolua_get_OneAttackData_atkpoints(lua_State* tolua_S)
{
  OneAttackData* self = (OneAttackData*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'atkpoints'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushnumber(tolua_S,(lua_Number)self->atkpoints);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: atkpoints of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_set_OneAttackData_atkpoints
static int tolua_set_OneAttackData_atkpoints(lua_State* tolua_S)
{
  OneAttackData* self = (OneAttackData*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'atkpoints'",NULL);
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->atkpoints = ((float)  tolua_tonumber(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: enchant_atk of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_get_OneAttackData_enchant_atk
static int tolua_get_OneAttackData_enchant_atk(lua_State* tolua_S)
{
  OneAttackData* self = (OneAttackData*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'enchant_atk'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushnumber(tolua_S,(lua_Number)self->enchant_atk);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: enchant_atk of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_set_OneAttackData_enchant_atk
static int tolua_set_OneAttackData_enchant_atk(lua_State* tolua_S)
{
  OneAttackData* self = (OneAttackData*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'enchant_atk'",NULL);
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->enchant_atk = ((float)  tolua_tonumber(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: buff_atk of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_get_OneAttackData_buff_atk
static int tolua_get_OneAttackData_buff_atk(lua_State* tolua_S)
{
  OneAttackData* self = (OneAttackData*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'buff_atk'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushnumber(tolua_S,(lua_Number)self->buff_atk);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: buff_atk of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_set_OneAttackData_buff_atk
static int tolua_set_OneAttackData_buff_atk(lua_State* tolua_S)
{
  OneAttackData* self = (OneAttackData*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'buff_atk'",NULL);
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->buff_atk = ((float)  tolua_tonumber(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: critical of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_get_OneAttackData_critical
static int tolua_get_OneAttackData_critical(lua_State* tolua_S)
{
  OneAttackData* self = (OneAttackData*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'critical'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushboolean(tolua_S,(bool)self->critical);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: critical of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_set_OneAttackData_critical
static int tolua_set_OneAttackData_critical(lua_State* tolua_S)
{
  OneAttackData* self = (OneAttackData*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'critical'",NULL);
  if (!tolua_isboolean(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->critical = ((bool)  tolua_toboolean(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: damage_armor of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_get_OneAttackData_damage_armor
static int tolua_get_OneAttackData_damage_armor(lua_State* tolua_S)
{
  OneAttackData* self = (OneAttackData*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'damage_armor'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushboolean(tolua_S,(bool)self->damage_armor);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: damage_armor of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_set_OneAttackData_damage_armor
static int tolua_set_OneAttackData_damage_armor(lua_State* tolua_S)
{
  OneAttackData* self = (OneAttackData*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'damage_armor'",NULL);
  if (!tolua_isboolean(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->damage_armor = ((bool)  tolua_toboolean(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: ignore_resist of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_get_OneAttackData_ignore_resist
static int tolua_get_OneAttackData_ignore_resist(lua_State* tolua_S)
{
  OneAttackData* self = (OneAttackData*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'ignore_resist'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushboolean(tolua_S,(bool)self->ignore_resist);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: ignore_resist of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_set_OneAttackData_ignore_resist
static int tolua_set_OneAttackData_ignore_resist(lua_State* tolua_S)
{
  OneAttackData* self = (OneAttackData*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'ignore_resist'",NULL);
  if (!tolua_isboolean(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->ignore_resist = ((bool)  tolua_toboolean(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: knockback of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_get_OneAttackData_knockback
static int tolua_get_OneAttackData_knockback(lua_State* tolua_S)
{
  OneAttackData* self = (OneAttackData*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'knockback'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushnumber(tolua_S,(lua_Number)self->knockback);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: knockback of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_set_OneAttackData_knockback
static int tolua_set_OneAttackData_knockback(lua_State* tolua_S)
{
  OneAttackData* self = (OneAttackData*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'knockback'",NULL);
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->knockback = ((float)  tolua_tonumber(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: knockup of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_get_OneAttackData_knockup
static int tolua_get_OneAttackData_knockup(lua_State* tolua_S)
{
  OneAttackData* self = (OneAttackData*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'knockup'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushnumber(tolua_S,(lua_Number)self->knockup);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: knockup of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_set_OneAttackData_knockup
static int tolua_set_OneAttackData_knockup(lua_State* tolua_S)
{
  OneAttackData* self = (OneAttackData*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'knockup'",NULL);
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->knockup = ((float)  tolua_tonumber(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: buffId of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_get_OneAttackData_buffId
static int tolua_get_OneAttackData_buffId(lua_State* tolua_S)
{
  OneAttackData* self = (OneAttackData*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'buffId'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushnumber(tolua_S,(lua_Number)self->buffId);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: buffId of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_set_OneAttackData_buffId
static int tolua_set_OneAttackData_buffId(lua_State* tolua_S)
{
  OneAttackData* self = (OneAttackData*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'buffId'",NULL);
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->buffId = ((int)  tolua_tonumber(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: buffLevel of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_get_OneAttackData_buffLevel
static int tolua_get_OneAttackData_buffLevel(lua_State* tolua_S)
{
  OneAttackData* self = (OneAttackData*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'buffLevel'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushnumber(tolua_S,(lua_Number)self->buffLevel);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: buffLevel of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_set_OneAttackData_buffLevel
static int tolua_set_OneAttackData_buffLevel(lua_State* tolua_S)
{
  OneAttackData* self = (OneAttackData*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'buffLevel'",NULL);
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->buffLevel = ((int)  tolua_tonumber(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: explodePos_x of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_get_OneAttackData_explodePos_x
static int tolua_get_OneAttackData_explodePos_x(lua_State* tolua_S)
{
  OneAttackData* self = (OneAttackData*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'explodePos_x'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushnumber(tolua_S,(lua_Number)self->explodePos_x);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: explodePos_x of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_set_OneAttackData_explodePos_x
static int tolua_set_OneAttackData_explodePos_x(lua_State* tolua_S)
{
  OneAttackData* self = (OneAttackData*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'explodePos_x'",NULL);
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->explodePos_x = ((int)  tolua_tonumber(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: explodePos_y of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_get_OneAttackData_explodePos_y
static int tolua_get_OneAttackData_explodePos_y(lua_State* tolua_S)
{
  OneAttackData* self = (OneAttackData*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'explodePos_y'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushnumber(tolua_S,(lua_Number)self->explodePos_y);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: explodePos_y of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_set_OneAttackData_explodePos_y
static int tolua_set_OneAttackData_explodePos_y(lua_State* tolua_S)
{
  OneAttackData* self = (OneAttackData*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'explodePos_y'",NULL);
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->explodePos_y = ((int)  tolua_tonumber(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: explodePos_z of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_get_OneAttackData_explodePos_z
static int tolua_get_OneAttackData_explodePos_z(lua_State* tolua_S)
{
  OneAttackData* self = (OneAttackData*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'explodePos_z'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushnumber(tolua_S,(lua_Number)self->explodePos_z);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: explodePos_z of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_set_OneAttackData_explodePos_z
static int tolua_set_OneAttackData_explodePos_z(lua_State* tolua_S)
{
  OneAttackData* self = (OneAttackData*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'explodePos_z'",NULL);
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->explodePos_z = ((int)  tolua_tonumber(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: explodeSize of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_get_OneAttackData_explodeSize
static int tolua_get_OneAttackData_explodeSize(lua_State* tolua_S)
{
  OneAttackData* self = (OneAttackData*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'explodeSize'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushnumber(tolua_S,(lua_Number)self->explodeSize);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: explodeSize of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_set_OneAttackData_explodeSize
static int tolua_set_OneAttackData_explodeSize(lua_State* tolua_S)
{
  OneAttackData* self = (OneAttackData*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'explodeSize'",NULL);
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->explodeSize = ((float)  tolua_tonumber(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: fromplayer of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_get_OneAttackData_fromplayer_ptr
static int tolua_get_OneAttackData_fromplayer_ptr(lua_State* tolua_S)
{
  OneAttackData* self = (OneAttackData*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'fromplayer'",NULL);
#else 
  if (!self) return 0;
#endif
   tolua_pushusertype(tolua_S,(void*)self->fromplayer,"IClientPlayer");
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: fromplayer of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_set_OneAttackData_fromplayer_ptr
static int tolua_set_OneAttackData_fromplayer_ptr(lua_State* tolua_S)
{
  OneAttackData* self = (OneAttackData*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'fromplayer'",NULL);
  if (!tolua_isusertype(tolua_S,2,"IClientPlayer",0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->fromplayer = ((IClientPlayer*)  tolua_tousertype(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: isAttackHead of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_get_OneAttackData_isAttackHead
static int tolua_get_OneAttackData_isAttackHead(lua_State* tolua_S)
{
  OneAttackData* self = (OneAttackData*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'isAttackHead'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushboolean(tolua_S,(bool)self->isAttackHead);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: isAttackHead of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_set_OneAttackData_isAttackHead
static int tolua_set_OneAttackData_isAttackHead(lua_State* tolua_S)
{
  OneAttackData* self = (OneAttackData*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'isAttackHead'",NULL);
  if (!tolua_isboolean(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->isAttackHead = ((bool)  tolua_toboolean(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: atkpos of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_get_OneAttackData_atkpos
static int tolua_get_OneAttackData_atkpos(lua_State* tolua_S)
{
  OneAttackData* self = (OneAttackData*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'atkpos'",NULL);
#else 
  if (!self) return 0;
#endif
   tolua_pushusertype(tolua_S,(void*)&self->atkpos,"WCoord");
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: atkpos of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_set_OneAttackData_atkpos
static int tolua_set_OneAttackData_atkpos(lua_State* tolua_S)
{
  OneAttackData* self = (OneAttackData*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'atkpos'",NULL);
  if ((tolua_isvaluenil(tolua_S,2,&tolua_err) || !tolua_isusertype(tolua_S,2,"WCoord",0,&tolua_err)))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->atkpos = *((WCoord*)  tolua_tousertype(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: triggerhit of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_get_OneAttackData_triggerhit
static int tolua_get_OneAttackData_triggerhit(lua_State* tolua_S)
{
  OneAttackData* self = (OneAttackData*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'triggerhit'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushboolean(tolua_S,(bool)self->triggerhit);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: triggerhit of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_set_OneAttackData_triggerhit
static int tolua_set_OneAttackData_triggerhit(lua_State* tolua_S)
{
  OneAttackData* self = (OneAttackData*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'triggerhit'",NULL);
  if (!tolua_isboolean(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->triggerhit = ((bool)  tolua_toboolean(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: touReduce of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_get_OneAttackData_touReduce
static int tolua_get_OneAttackData_touReduce(lua_State* tolua_S)
{
  OneAttackData* self = (OneAttackData*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'touReduce'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushnumber(tolua_S,(lua_Number)self->touReduce);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: touReduce of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_set_OneAttackData_touReduce
static int tolua_set_OneAttackData_touReduce(lua_State* tolua_S)
{
  OneAttackData* self = (OneAttackData*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'touReduce'",NULL);
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->touReduce = ((int)  tolua_tonumber(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: atkTypeNew of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_get_OneAttackData_atkTypeNew
static int tolua_get_OneAttackData_atkTypeNew(lua_State* tolua_S)
{
  OneAttackData* self = (OneAttackData*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'atkTypeNew'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushnumber(tolua_S,(lua_Number)self->atkTypeNew);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: atkTypeNew of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_set_OneAttackData_atkTypeNew
static int tolua_set_OneAttackData_atkTypeNew(lua_State* tolua_S)
{
  OneAttackData* self = (OneAttackData*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'atkTypeNew'",NULL);
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->atkTypeNew = ((int)  tolua_tonumber(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: atkPointsNew of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_get_miniSandboxEngineActor_OneAttackData_atkPointsNew
static int tolua_get_miniSandboxEngineActor_OneAttackData_atkPointsNew(lua_State* tolua_S)
{
 int tolua_index;
  OneAttackData* self;
 lua_pushstring(tolua_S,".self");
 lua_rawget(tolua_S,1);
 self = (OneAttackData*)  lua_touserdata(tolua_S,-1);
#ifndef TOLUA_RELEASE
 {
  tolua_Error tolua_err;
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in array indexing.",&tolua_err);
 }
#endif
 tolua_index = (int)tolua_tonumber(tolua_S,2,0);
#ifndef TOLUA_RELEASE
 if (tolua_index<0 || tolua_index>=10)
  tolua_error(tolua_S,"array indexing out of range.",NULL);
#endif
 tolua_pushnumber(tolua_S,(lua_Number)self->atkPointsNew[tolua_index]);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: atkPointsNew of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_set_OneAttackData_atkPointsNew
static int tolua_set_OneAttackData_atkPointsNew(lua_State* tolua_S)
{
	OneAttackData* self = (OneAttackData*)tolua_tousertype(tolua_S, 1, 0);
#ifndef TOLUA_RELEASE
	tolua_Error tolua_err;
	if (!self) tolua_error(tolua_S, "invalid 'self' in accessing variable 'atkPointsNew'", NULL);
	if (!tolua_istable(tolua_S, 2, 10, &tolua_err))
		tolua_error(tolua_S, "#vinvalid type in variable assignment.", &tolua_err);
#endif
	for (int i = 0; i < 10; i++)
		self->atkPointsNew[i] =  ((float)  tolua_tofieldnumber(tolua_S, 2, i + 1, 0));

	return 0;
}
#endif //#ifndef TOLUA_DISABLE

#ifndef TOLUA_DISABLE_tolua_set_miniSandboxEngineActor_OneAttackData_atkPointsNew
static int tolua_set_miniSandboxEngineActor_OneAttackData_atkPointsNew(lua_State* tolua_S)
{
 int tolua_index;
  OneAttackData* self;
 lua_pushstring(tolua_S,".self");
 lua_rawget(tolua_S,1);
 self = (OneAttackData*)  lua_touserdata(tolua_S,-1);
#ifndef TOLUA_RELEASE
 {
  tolua_Error tolua_err;
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in array indexing.",&tolua_err);
 }
#endif
 tolua_index = (int)tolua_tonumber(tolua_S,2,0);
#ifndef TOLUA_RELEASE
 if (tolua_index<0 || tolua_index>=10)
  tolua_error(tolua_S,"array indexing out of range.",NULL);
#endif
  self->atkPointsNew[tolua_index] = ((float)  tolua_tonumber(tolua_S,3,0));
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: explodePoints of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_get_miniSandboxEngineActor_OneAttackData_explodePoints
static int tolua_get_miniSandboxEngineActor_OneAttackData_explodePoints(lua_State* tolua_S)
{
 int tolua_index;
  OneAttackData* self;
 lua_pushstring(tolua_S,".self");
 lua_rawget(tolua_S,1);
 self = (OneAttackData*)  lua_touserdata(tolua_S,-1);
#ifndef TOLUA_RELEASE
 {
  tolua_Error tolua_err;
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in array indexing.",&tolua_err);
 }
#endif
 tolua_index = (int)tolua_tonumber(tolua_S,2,0);
#ifndef TOLUA_RELEASE
 if (tolua_index<0 || tolua_index>=7)
  tolua_error(tolua_S,"array indexing out of range.",NULL);
#endif
 tolua_pushnumber(tolua_S,(lua_Number)self->explodePoints[tolua_index]);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: explodePoints of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_set_OneAttackData_explodePoints
static int tolua_set_OneAttackData_explodePoints(lua_State* tolua_S)
{
	OneAttackData* self = (OneAttackData*)tolua_tousertype(tolua_S, 1, 0);
#ifndef TOLUA_RELEASE
	tolua_Error tolua_err;
	if (!self) tolua_error(tolua_S, "invalid 'self' in accessing variable 'explodePoints'", NULL);
	if (!tolua_istable(tolua_S, 2, 7, &tolua_err))
		tolua_error(tolua_S, "#vinvalid type in variable assignment.", &tolua_err);
#endif
	for (int i = 0; i < 7; i++)
		self->explodePoints[i] =  ((float)  tolua_tofieldnumber(tolua_S, 2, i + 1, 0));

	return 0;
}
#endif //#ifndef TOLUA_DISABLE

#ifndef TOLUA_DISABLE_tolua_set_miniSandboxEngineActor_OneAttackData_explodePoints
static int tolua_set_miniSandboxEngineActor_OneAttackData_explodePoints(lua_State* tolua_S)
{
 int tolua_index;
  OneAttackData* self;
 lua_pushstring(tolua_S,".self");
 lua_rawget(tolua_S,1);
 self = (OneAttackData*)  lua_touserdata(tolua_S,-1);
#ifndef TOLUA_RELEASE
 {
  tolua_Error tolua_err;
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in array indexing.",&tolua_err);
 }
#endif
 tolua_index = (int)tolua_tonumber(tolua_S,2,0);
#ifndef TOLUA_RELEASE
 if (tolua_index<0 || tolua_index>=7)
  tolua_error(tolua_S,"array indexing out of range.",NULL);
#endif
  self->explodePoints[tolua_index] = ((float)  tolua_tonumber(tolua_S,3,0));
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: damping of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_get_OneAttackData_damping
static int tolua_get_OneAttackData_damping(lua_State* tolua_S)
{
  OneAttackData* self = (OneAttackData*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'damping'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushnumber(tolua_S,(lua_Number)self->damping);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: damping of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_set_OneAttackData_damping
static int tolua_set_OneAttackData_damping(lua_State* tolua_S)
{
  OneAttackData* self = (OneAttackData*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'damping'",NULL);
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->damping = ((float)  tolua_tonumber(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: charge of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_get_OneAttackData_charge
static int tolua_get_OneAttackData_charge(lua_State* tolua_S)
{
  OneAttackData* self = (OneAttackData*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'charge'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushnumber(tolua_S,(lua_Number)self->charge);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: charge of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_set_OneAttackData_charge
static int tolua_set_OneAttackData_charge(lua_State* tolua_S)
{
  OneAttackData* self = (OneAttackData*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'charge'",NULL);
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->charge = ((float)  tolua_tonumber(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: directAttacker of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_get_OneAttackData_directAttacker_ptr
static int tolua_get_OneAttackData_directAttacker_ptr(lua_State* tolua_S)
{
  OneAttackData* self = (OneAttackData*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'directAttacker'",NULL);
#else 
  if (!self) return 0;
#endif
   tolua_pushusertype(tolua_S,(void*)self->directAttacker,"IClientActor");
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: directAttacker of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_set_OneAttackData_directAttacker_ptr
static int tolua_set_OneAttackData_directAttacker_ptr(lua_State* tolua_S)
{
  OneAttackData* self = (OneAttackData*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'directAttacker'",NULL);
  if (!tolua_isusertype(tolua_S,2,"IClientActor",0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->directAttacker = ((IClientActor*)  tolua_tousertype(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: isFixAtkPoint of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_get_OneAttackData_isFixAtkPoint
static int tolua_get_OneAttackData_isFixAtkPoint(lua_State* tolua_S)
{
  OneAttackData* self = (OneAttackData*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'isFixAtkPoint'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushboolean(tolua_S,(bool)self->isFixAtkPoint);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: isFixAtkPoint of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_set_OneAttackData_isFixAtkPoint
static int tolua_set_OneAttackData_isFixAtkPoint(lua_State* tolua_S)
{
  OneAttackData* self = (OneAttackData*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'isFixAtkPoint'",NULL);
  if (!tolua_isboolean(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->isFixAtkPoint = ((bool)  tolua_toboolean(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: bowDamageIns of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_get_OneAttackData_bowDamageIns
static int tolua_get_OneAttackData_bowDamageIns(lua_State* tolua_S)
{
  OneAttackData* self = (OneAttackData*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'bowDamageIns'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushnumber(tolua_S,(lua_Number)self->bowDamageIns);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: bowDamageIns of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_set_OneAttackData_bowDamageIns
static int tolua_set_OneAttackData_bowDamageIns(lua_State* tolua_S)
{
  OneAttackData* self = (OneAttackData*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'bowDamageIns'",NULL);
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->bowDamageIns = ((float)  tolua_tonumber(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: skillDamageIns of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_get_OneAttackData_skillDamageIns
static int tolua_get_OneAttackData_skillDamageIns(lua_State* tolua_S)
{
  OneAttackData* self = (OneAttackData*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'skillDamageIns'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushnumber(tolua_S,(lua_Number)self->skillDamageIns);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: skillDamageIns of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_set_OneAttackData_skillDamageIns
static int tolua_set_OneAttackData_skillDamageIns(lua_State* tolua_S)
{
  OneAttackData* self = (OneAttackData*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'skillDamageIns'",NULL);
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->skillDamageIns = ((float)  tolua_tonumber(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: isNotInherit of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_get_OneAttackData_isNotInherit
static int tolua_get_OneAttackData_isNotInherit(lua_State* tolua_S)
{
  OneAttackData* self = (OneAttackData*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'isNotInherit'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushboolean(tolua_S,(bool)self->isNotInherit);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: isNotInherit of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_set_OneAttackData_isNotInherit
static int tolua_set_OneAttackData_isNotInherit(lua_State* tolua_S)
{
  OneAttackData* self = (OneAttackData*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'isNotInherit'",NULL);
  if (!tolua_isboolean(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->isNotInherit = ((bool)  tolua_toboolean(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: damageFactor of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_get_OneAttackData_damageFactor
static int tolua_get_OneAttackData_damageFactor(lua_State* tolua_S)
{
  OneAttackData* self = (OneAttackData*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'damageFactor'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushnumber(tolua_S,(lua_Number)self->damageFactor);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: damageFactor of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_set_OneAttackData_damageFactor
static int tolua_set_OneAttackData_damageFactor(lua_State* tolua_S)
{
  OneAttackData* self = (OneAttackData*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'damageFactor'",NULL);
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->damageFactor = ((float)  tolua_tonumber(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: ignoreTriggerEvent of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_get_OneAttackData_ignoreTriggerEvent
static int tolua_get_OneAttackData_ignoreTriggerEvent(lua_State* tolua_S)
{
  OneAttackData* self = (OneAttackData*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'ignoreTriggerEvent'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushboolean(tolua_S,(bool)self->ignoreTriggerEvent);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: ignoreTriggerEvent of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_set_OneAttackData_ignoreTriggerEvent
static int tolua_set_OneAttackData_ignoreTriggerEvent(lua_State* tolua_S)
{
  OneAttackData* self = (OneAttackData*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'ignoreTriggerEvent'",NULL);
  if (!tolua_isboolean(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->ignoreTriggerEvent = ((bool)  tolua_toboolean(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* method: new of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_OneAttackData_new00
static int tolua_miniSandboxEngineActor_OneAttackData_new00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertable(tolua_S,1,"OneAttackData",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  {
   OneAttackData* tolua_ret = (OneAttackData*)  Mtolua_new((OneAttackData)());
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"OneAttackData");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'new'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: new_local of class  OneAttackData */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_OneAttackData_new00_local
static int tolua_miniSandboxEngineActor_OneAttackData_new00_local(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertable(tolua_S,1,"OneAttackData",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  {
   OneAttackData* tolua_ret = (OneAttackData*)  Mtolua_new((OneAttackData)());
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"OneAttackData");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'new'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: CreateComponentByFactory of class  ActorBase */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_ActorBase_CreateComponentByFactory00
static int tolua_miniSandboxEngineActor_ActorBase_CreateComponentByFactory00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ActorBase",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ActorBase* self = (ActorBase*)  tolua_tousertype(tolua_S,1,0);
  const std::string name = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
  const std::string product = ((const std::string)  tolua_tocppstring(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'CreateComponentByFactory'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   MNSandbox::SceneComponent* tolua_ret = (MNSandbox::SceneComponent*)  self->CreateComponentByFactory(name,product);
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"MNSandbox::SceneComponent");
   tolua_pushcppstring(tolua_S,(const char*)name);
   tolua_pushcppstring(tolua_S,(const char*)product);
  }
 }
 return 3;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'CreateComponentByFactory'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* get function: monsterid of class  SummonPetInfomation */
#ifndef TOLUA_DISABLE_tolua_get_SummonPetInfomation_monsterid
static int tolua_get_SummonPetInfomation_monsterid(lua_State* tolua_S)
{
  SummonPetInfomation* self = (SummonPetInfomation*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'monsterid'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushnumber(tolua_S,(lua_Number)self->monsterid);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: monsterid of class  SummonPetInfomation */
#ifndef TOLUA_DISABLE_tolua_set_SummonPetInfomation_monsterid
static int tolua_set_SummonPetInfomation_monsterid(lua_State* tolua_S)
{
  SummonPetInfomation* self = (SummonPetInfomation*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'monsterid'",NULL);
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->monsterid = ((int)  tolua_tonumber(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: petid of class  SummonPetInfomation */
#ifndef TOLUA_DISABLE_tolua_get_SummonPetInfomation_petid
static int tolua_get_SummonPetInfomation_petid(lua_State* tolua_S)
{
  SummonPetInfomation* self = (SummonPetInfomation*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'petid'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushnumber(tolua_S,(lua_Number)self->petid);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: petid of class  SummonPetInfomation */
#ifndef TOLUA_DISABLE_tolua_set_SummonPetInfomation_petid
static int tolua_set_SummonPetInfomation_petid(lua_State* tolua_S)
{
  SummonPetInfomation* self = (SummonPetInfomation*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'petid'",NULL);
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->petid = ((int)  tolua_tonumber(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: stage of class  SummonPetInfomation */
#ifndef TOLUA_DISABLE_tolua_get_SummonPetInfomation_stage
static int tolua_get_SummonPetInfomation_stage(lua_State* tolua_S)
{
  SummonPetInfomation* self = (SummonPetInfomation*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'stage'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushnumber(tolua_S,(lua_Number)self->stage);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: stage of class  SummonPetInfomation */
#ifndef TOLUA_DISABLE_tolua_set_SummonPetInfomation_stage
static int tolua_set_SummonPetInfomation_stage(lua_State* tolua_S)
{
  SummonPetInfomation* self = (SummonPetInfomation*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'stage'",NULL);
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->stage = ((int)  tolua_tonumber(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: quality of class  SummonPetInfomation */
#ifndef TOLUA_DISABLE_tolua_get_SummonPetInfomation_quality
static int tolua_get_SummonPetInfomation_quality(lua_State* tolua_S)
{
  SummonPetInfomation* self = (SummonPetInfomation*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'quality'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushnumber(tolua_S,(lua_Number)self->quality);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: quality of class  SummonPetInfomation */
#ifndef TOLUA_DISABLE_tolua_set_SummonPetInfomation_quality
static int tolua_set_SummonPetInfomation_quality(lua_State* tolua_S)
{
  SummonPetInfomation* self = (SummonPetInfomation*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'quality'",NULL);
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->quality = ((int)  tolua_tonumber(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* method: new of class  SummonPetInfomation */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_SummonPetInfomation_new00
static int tolua_miniSandboxEngineActor_SummonPetInfomation_new00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertable(tolua_S,1,"SummonPetInfomation",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  {
   SummonPetInfomation* tolua_ret = (SummonPetInfomation*)  Mtolua_new((SummonPetInfomation)());
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"SummonPetInfomation");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'new'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: new_local of class  SummonPetInfomation */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_SummonPetInfomation_new00_local
static int tolua_miniSandboxEngineActor_SummonPetInfomation_new00_local(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertable(tolua_S,1,"SummonPetInfomation",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  {
   SummonPetInfomation* tolua_ret = (SummonPetInfomation*)  Mtolua_new((SummonPetInfomation)());
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"SummonPetInfomation");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'new'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: CastToActor of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_CastToActor00
static int tolua_miniSandboxEngineActor_IClientPlayer_CastToActor00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'CastToActor'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   IClientActor* tolua_ret = (IClientActor*)  self->CastToActor();
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"IClientActor");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'CastToActor'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: CastToNode of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_CastToNode00
static int tolua_miniSandboxEngineActor_IClientPlayer_CastToNode00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'CastToNode'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   MNSandbox::SandboxNode* tolua_ret = (MNSandbox::SandboxNode*)  self->CastToNode();
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"MNSandbox::SandboxNode");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'CastToNode'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetPlayer of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_GetPlayer00
static int tolua_miniSandboxEngineActor_IClientPlayer_GetPlayer00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'GetPlayer'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   ClientPlayer* tolua_ret = (ClientPlayer*)  self->GetPlayer();
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"ClientPlayer");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'GetPlayer'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getNickname of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_getNickname00
static int tolua_miniSandboxEngineActor_IClientPlayer_getNickname00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getNickname'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   const char* tolua_ret = (const char*)  self->getNickname();
   tolua_pushstring(tolua_S,(const char*)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getNickname'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: setRocketTeleport of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_setRocketTeleport00
static int tolua_miniSandboxEngineActor_IClientPlayer_setRocketTeleport00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isboolean(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  bool b = ((bool)  tolua_toboolean(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'setRocketTeleport'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->setRocketTeleport(b);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'setRocketTeleport'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: isRocketTeleport of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_isRocketTeleport00
static int tolua_miniSandboxEngineActor_IClientPlayer_isRocketTeleport00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'isRocketTeleport'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->isRocketTeleport();
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'isRocketTeleport'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: isStarStationTeleporting of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_isStarStationTeleporting00
static int tolua_miniSandboxEngineActor_IClientPlayer_isStarStationTeleporting00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'isStarStationTeleporting'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->isStarStationTeleporting();
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'isStarStationTeleporting'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: setIsStarStationTeleporting of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_setIsStarStationTeleporting00
static int tolua_miniSandboxEngineActor_IClientPlayer_setIsStarStationTeleporting00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isboolean(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  bool isTeleporting = ((bool)  tolua_toboolean(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'setIsStarStationTeleporting'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->setIsStarStationTeleporting(isTeleporting);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'setIsStarStationTeleporting'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: teleportRidingRocket of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_teleportRidingRocket00
static int tolua_miniSandboxEngineActor_IClientPlayer_teleportRidingRocket00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  int targetmap = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'teleportRidingRocket'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->teleportRidingRocket(targetmap);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'teleportRidingRocket'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: iGetObjId of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_iGetObjId00
static int tolua_miniSandboxEngineActor_IClientPlayer_iGetObjId00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'iGetObjId'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   long long tolua_ret = (long long)  self->iGetObjId();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'iGetObjId'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetDirtyGridIndex of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_GetDirtyGridIndex00
static int tolua_miniSandboxEngineActor_IClientPlayer_GetDirtyGridIndex00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'GetDirtyGridIndex'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::set<int>& tolua_ret = (std::set<int>&)  self->GetDirtyGridIndex();
    tolua_pushusertype(tolua_S,(void*)&tolua_ret,"std::set<int>");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'GetDirtyGridIndex'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: iGetPosition of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_iGetPosition00
static int tolua_miniSandboxEngineActor_IClientPlayer_iGetPosition00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'iGetPosition'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   WCoord& tolua_ret = (WCoord&)  self->iGetPosition();
    tolua_pushusertype(tolua_S,(void*)&tolua_ret,"WCoord");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'iGetPosition'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: SetPlayerPosition of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_SetPlayerPosition00
static int tolua_miniSandboxEngineActor_IClientPlayer_SetPlayerPosition00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,2,&tolua_err) || !tolua_isusertype(tolua_S,2,"const WCoord",0,&tolua_err)) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  const WCoord* position = ((const WCoord*)  tolua_tousertype(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'SetPlayerPosition'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->SetPlayerPosition(*position);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'SetPlayerPosition'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: iGetCurMapID of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_iGetCurMapID00
static int tolua_miniSandboxEngineActor_IClientPlayer_iGetCurMapID00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'iGetCurMapID'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   unsigned short tolua_ret = (unsigned short)  self->iGetCurMapID();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'iGetCurMapID'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: isMoveControlActive of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_isMoveControlActive00
static int tolua_miniSandboxEngineActor_IClientPlayer_isMoveControlActive00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"const IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  const IClientPlayer* self = (const IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'isMoveControlActive'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->isMoveControlActive();
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'isMoveControlActive'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: syncPos2Client of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_syncPos2Client00
static int tolua_miniSandboxEngineActor_IClientPlayer_syncPos2Client00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isboolean(tolua_S,2,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  bool sync_motion = ((bool)  tolua_toboolean(tolua_S,2,false));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'syncPos2Client'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->syncPos2Client(sync_motion);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'syncPos2Client'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: iGetBody of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_iGetBody00
static int tolua_miniSandboxEngineActor_IClientPlayer_iGetBody00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'iGetBody'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   Rainbow::IActorBody* tolua_ret = (Rainbow::IActorBody*)  self->iGetBody();
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"Rainbow::IActorBody");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'iGetBody'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getCurViewRange of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_getCurViewRange00
static int tolua_miniSandboxEngineActor_IClientPlayer_getCurViewRange00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getCurViewRange'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getCurViewRange();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getCurViewRange'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getChunkViewer of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_getChunkViewer00
static int tolua_miniSandboxEngineActor_IClientPlayer_getChunkViewer00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getChunkViewer'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   ChunkViewer* tolua_ret = (ChunkViewer*)  self->getChunkViewer();
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"ChunkViewer");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getChunkViewer'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: SetPlayerAttrViewRange of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_SetPlayerAttrViewRange00
static int tolua_miniSandboxEngineActor_IClientPlayer_SetPlayerAttrViewRange00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  int range = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'SetPlayerAttrViewRange'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->SetPlayerAttrViewRange(range);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'SetPlayerAttrViewRange'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getEmitterWorldContainer of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_getEmitterWorldContainer00
static int tolua_miniSandboxEngineActor_IClientPlayer_getEmitterWorldContainer00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getEmitterWorldContainer'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   WorldContainer* tolua_ret = (WorldContainer*)  self->getEmitterWorldContainer();
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"WorldContainer");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getEmitterWorldContainer'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetPlayerLocoMotion of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_GetPlayerLocoMotion00
static int tolua_miniSandboxEngineActor_IClientPlayer_GetPlayerLocoMotion00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'GetPlayerLocoMotion'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   IActorLocoMotion* tolua_ret = (IActorLocoMotion*)  self->GetPlayerLocoMotion();
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"IActorLocoMotion");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'GetPlayerLocoMotion'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: execCmd of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_execCmd00
static int tolua_miniSandboxEngineActor_IClientPlayer_execCmd00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  const char* cmdstr = ((const char*)  tolua_tostring(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'execCmd'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->execCmd(cmdstr);
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'execCmd'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getIBackPack of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_getIBackPack00
static int tolua_miniSandboxEngineActor_IClientPlayer_getIBackPack00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getIBackPack'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   IBackPack* tolua_ret = (IBackPack*)  self->getIBackPack();
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"IBackPack");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getIBackPack'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: notifyGameInfo2Self of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_notifyGameInfo2Self00
static int tolua_miniSandboxEngineActor_IClientPlayer_notifyGameInfo2Self00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,1,&tolua_err) ||
     !tolua_isstring(tolua_S,5,1,&tolua_err) ||
     !tolua_isstring(tolua_S,6,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,7,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  int infotype = ((int)  tolua_tonumber(tolua_S,2,0));
  int id = ((int)  tolua_tonumber(tolua_S,3,0));
  int num = ((int)  tolua_tonumber(tolua_S,4,0));
  const char* name = ((const char*)  tolua_tostring(tolua_S,5,NULL));
  const char* buff = ((const char*)  tolua_tostring(tolua_S,6,NULL));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'notifyGameInfo2Self'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->notifyGameInfo2Self(infotype,id,num,name,buff);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'notifyGameInfo2Self'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: checkActionAttrState of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_checkActionAttrState00
static int tolua_miniSandboxEngineActor_IClientPlayer_checkActionAttrState00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  int actionattr = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'checkActionAttrState'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->checkActionAttrState(actionattr);
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'checkActionAttrState'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: updateTaskSysProcess of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_updateTaskSysProcess00
static int tolua_miniSandboxEngineActor_IClientPlayer_updateTaskSysProcess00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,6,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  TASKSYS_TYPE type = ((TASKSYS_TYPE) (int)  tolua_tonumber(tolua_S,2,0));
  int target1 = ((int)  tolua_tonumber(tolua_S,3,0));
  int target2 = ((int)  tolua_tonumber(tolua_S,4,0));
  int goalnum = ((int)  tolua_tonumber(tolua_S,5,1));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'updateTaskSysProcess'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->updateTaskSysProcess(type,target1,target2,goalnum);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'updateTaskSysProcess'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: isInSpectatorMode of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_isInSpectatorMode00
static int tolua_miniSandboxEngineActor_IClientPlayer_isInSpectatorMode00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'isInSpectatorMode'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->isInSpectatorMode();
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'isInSpectatorMode'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: setSpectatorUin of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_setSpectatorUin00
static int tolua_miniSandboxEngineActor_IClientPlayer_setSpectatorUin00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  int uin = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'setSpectatorUin'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->setSpectatorUin(uin);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'setSpectatorUin'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getSpectatorType of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_getSpectatorType00
static int tolua_miniSandboxEngineActor_IClientPlayer_getSpectatorType00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getSpectatorType'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   PLAYER_SPECTATOR_TYPE tolua_ret = (PLAYER_SPECTATOR_TYPE)  self->getSpectatorType();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getSpectatorType'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getSpectatorUin of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_getSpectatorUin00
static int tolua_miniSandboxEngineActor_IClientPlayer_getSpectatorUin00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getSpectatorUin'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getSpectatorUin();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getSpectatorUin'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getToSpectatorUin of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_getToSpectatorUin00
static int tolua_miniSandboxEngineActor_IClientPlayer_getToSpectatorUin00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getToSpectatorUin'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getToSpectatorUin();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getToSpectatorUin'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getCurToolID of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_getCurToolID00
static int tolua_miniSandboxEngineActor_IClientPlayer_getCurToolID00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getCurToolID'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getCurToolID();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getCurToolID'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: renderUI of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_renderUI00
static int tolua_miniSandboxEngineActor_IClientPlayer_renderUI00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'renderUI'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->renderUI();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'renderUI'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: isExploiting of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_isExploiting00
static int tolua_miniSandboxEngineActor_IClientPlayer_isExploiting00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'isExploiting'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->isExploiting();
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'isExploiting'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getOpenContainerBaseIndex of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_getOpenContainerBaseIndex00
static int tolua_miniSandboxEngineActor_IClientPlayer_getOpenContainerBaseIndex00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getOpenContainerBaseIndex'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getOpenContainerBaseIndex();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getOpenContainerBaseIndex'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetPlayerFaceDir of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_GetPlayerFaceDir00
static int tolua_miniSandboxEngineActor_IClientPlayer_GetPlayerFaceDir00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,5,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  float x = ((float)  tolua_tonumber(tolua_S,2,0));
  float y = ((float)  tolua_tonumber(tolua_S,3,0));
  float z = ((float)  tolua_tonumber(tolua_S,4,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'GetPlayerFaceDir'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->GetPlayerFaceDir(x,y,z);
   tolua_pushnumber(tolua_S,(lua_Number)x);
   tolua_pushnumber(tolua_S,(lua_Number)y);
   tolua_pushnumber(tolua_S,(lua_Number)z);
  }
 }
 return 3;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'GetPlayerFaceDir'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: SetMoveForward of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_SetMoveForward00
static int tolua_miniSandboxEngineActor_IClientPlayer_SetMoveForward00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  float value = ((float)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'SetMoveForward'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->SetMoveForward(value);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'SetMoveForward'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: SetMoveRight of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_SetMoveRight00
static int tolua_miniSandboxEngineActor_IClientPlayer_SetMoveRight00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  float value = ((float)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'SetMoveRight'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->SetMoveRight(value);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'SetMoveRight'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getUin of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_getUin00
static int tolua_miniSandboxEngineActor_IClientPlayer_getUin00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"const IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  const IClientPlayer* self = (const IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getUin'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getUin();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getUin'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: tryStandup of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_tryStandup00
static int tolua_miniSandboxEngineActor_IClientPlayer_tryStandup00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'tryStandup'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->tryStandup();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'tryStandup'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetPlayerWorld of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_GetPlayerWorld00
static int tolua_miniSandboxEngineActor_IClientPlayer_GetPlayerWorld00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'GetPlayerWorld'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   World* tolua_ret = (World*)  self->GetPlayerWorld();
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"World");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'GetPlayerWorld'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetCurAccountHorse of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_GetCurAccountHorse00
static int tolua_miniSandboxEngineActor_IClientPlayer_GetCurAccountHorse00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'GetCurAccountHorse'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   IClientActor* tolua_ret = (IClientActor*)  self->GetCurAccountHorse();
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"IClientActor");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'GetCurAccountHorse'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: hasUIControl of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_hasUIControl00
static int tolua_miniSandboxEngineActor_IClientPlayer_hasUIControl00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'hasUIControl'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->hasUIControl();
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'hasUIControl'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: stopMusicByTrigger of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_stopMusicByTrigger00
static int tolua_miniSandboxEngineActor_IClientPlayer_stopMusicByTrigger00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isstring(tolua_S,2,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  const char* path = ((const char*)  tolua_tostring(tolua_S,2,NULL));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'stopMusicByTrigger'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->stopMusicByTrigger(path);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'stopMusicByTrigger'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: iGetTeam of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_iGetTeam00
static int tolua_miniSandboxEngineActor_IClientPlayer_iGetTeam00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'iGetTeam'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->iGetTeam();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'iGetTeam'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: SetPlayerTeam of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_SetPlayerTeam00
static int tolua_miniSandboxEngineActor_IClientPlayer_SetPlayerTeam00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  int id = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'SetPlayerTeam'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->SetPlayerTeam(id);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'SetPlayerTeam'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getCustomjson of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_getCustomjson00
static int tolua_miniSandboxEngineActor_IClientPlayer_getCustomjson00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getCustomjson'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   const char* tolua_ret = (const char*)  self->getCustomjson();
   tolua_pushstring(tolua_S,(const char*)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getCustomjson'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: setCustomJson of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_setCustomJson00
static int tolua_miniSandboxEngineActor_IClientPlayer_setCustomJson00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  std::string tolua_var_1 = ((std::string)  tolua_tocppstring(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'setCustomJson'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->setCustomJson(tolua_var_1);
   tolua_pushcppstring(tolua_S,(const char*)tolua_var_1);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'setCustomJson'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: clearTrackerEntry of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_clearTrackerEntry00
static int tolua_miniSandboxEngineActor_IClientPlayer_clearTrackerEntry00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'clearTrackerEntry'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->clearTrackerEntry();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'clearTrackerEntry'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getSkinID of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_getSkinID00
static int tolua_miniSandboxEngineActor_IClientPlayer_getSkinID00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getSkinID'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getSkinID();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getSkinID'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getTrackerEntrys of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_getTrackerEntrys00
static int tolua_miniSandboxEngineActor_IClientPlayer_getTrackerEntrys00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getTrackerEntrys'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::vector<MpActorTrackerEntry*>& tolua_ret = (std::vector<MpActorTrackerEntry*>&)  self->getTrackerEntrys();
    tolua_pushusertype(tolua_S,(void*)&tolua_ret,"std::vector<MpActorTrackerEntry*>");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getTrackerEntrys'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getGameResults of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_getGameResults00
static int tolua_miniSandboxEngineActor_IClientPlayer_getGameResults00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getGameResults'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getGameResults();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getGameResults'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addTrackerEntry of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_addTrackerEntry00
static int tolua_miniSandboxEngineActor_IClientPlayer_addTrackerEntry00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isusertype(tolua_S,2,"MpActorTrackerEntry",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  MpActorTrackerEntry* entry = ((MpActorTrackerEntry*)  tolua_tousertype(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addTrackerEntry'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->addTrackerEntry(entry);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addTrackerEntry'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getGameScore of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_getGameScore00
static int tolua_miniSandboxEngineActor_IClientPlayer_getGameScore00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getGameScore'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getGameScore();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getGameScore'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getBPTitle of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_getBPTitle00
static int tolua_miniSandboxEngineActor_IClientPlayer_getBPTitle00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getBPTitle'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::string tolua_ret = (std::string)  self->getBPTitle();
   tolua_pushcppstring(tolua_S,(const char*)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getBPTitle'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getGameRanking of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_getGameRanking00
static int tolua_miniSandboxEngineActor_IClientPlayer_getGameRanking00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getGameRanking'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getGameRanking();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getGameRanking'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: setTeleportPos of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_setTeleportPos00
static int tolua_miniSandboxEngineActor_IClientPlayer_setTeleportPos00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,2,&tolua_err) || !tolua_isusertype(tolua_S,2,"const WCoord",0,&tolua_err)) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  const WCoord* pos = ((const WCoord*)  tolua_tousertype(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'setTeleportPos'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->setTeleportPos(*pos);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'setTeleportPos'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetAccountSkinID of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_GetAccountSkinID00
static int tolua_miniSandboxEngineActor_IClientPlayer_GetAccountSkinID00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'GetAccountSkinID'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->GetAccountSkinID();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'GetAccountSkinID'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getVipInfo of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_getVipInfo00
static int tolua_miniSandboxEngineActor_IClientPlayer_getVipInfo00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getVipInfo'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   const VipInfo& tolua_ret = (const VipInfo&)  self->getVipInfo();
    tolua_pushusertype(tolua_S,(void*)&tolua_ret,"const VipInfo");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getVipInfo'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: saveToFile of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_saveToFile00
static int tolua_miniSandboxEngineActor_IClientPlayer_saveToFile00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,1,&tolua_err) ||
     !tolua_isusertype(tolua_S,3,"ChunkIOMgr",1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  long long owid = ((long long)  tolua_tonumber(tolua_S,2,0));
  ChunkIOMgr* iomgr = ((ChunkIOMgr*)  tolua_tousertype(tolua_S,3,NULL));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'saveToFile'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->saveToFile(owid,iomgr);
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'saveToFile'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: saveUserData of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_saveUserData00
static int tolua_miniSandboxEngineActor_IClientPlayer_saveUserData00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  long long owid = ((long long)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'saveUserData'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->saveUserData(owid);
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'saveUserData'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: saveTechTree of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_saveTechTree00
static int tolua_miniSandboxEngineActor_IClientPlayer_saveTechTree00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  long long owid = ((long long)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'saveTechTree'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->saveTechTree(owid);
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'saveTechTree'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: CanExposePosToOther of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_CanExposePosToOther00
static int tolua_miniSandboxEngineActor_IClientPlayer_CanExposePosToOther00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'CanExposePosToOther'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->CanExposePosToOther();
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'CanExposePosToOther'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getRealLandingPoint of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_getRealLandingPoint00
static int tolua_miniSandboxEngineActor_IClientPlayer_getRealLandingPoint00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isusertype(tolua_S,3,"World",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  int targetmap = ((int)  tolua_tonumber(tolua_S,2,0));
  World* pworld = ((World*)  tolua_tousertype(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getRealLandingPoint'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   WCoord tolua_ret = (WCoord)  self->getRealLandingPoint(targetmap,pworld);
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((WCoord)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"WCoord");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(WCoord));
     tolua_pushusertype(tolua_S,tolua_obj,"WCoord");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getRealLandingPoint'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: gotoTeleportPos of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_gotoTeleportPos00
static int tolua_miniSandboxEngineActor_IClientPlayer_gotoTeleportPos00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isusertype(tolua_S,2,"World",0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,3,&tolua_err) || !tolua_isusertype(tolua_S,3,"const WCoord",0,&tolua_err)) ||
     (tolua_isvaluenil(tolua_S,4,&tolua_err) || !tolua_isusertype(tolua_S,4,"WCoord",0,&tolua_err)) ||
     !tolua_isnoobj(tolua_S,5,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  World* pworld = ((World*)  tolua_tousertype(tolua_S,2,0));
  const WCoord* blockpos = ((const WCoord*)  tolua_tousertype(tolua_S,3,0));
  WCoord* realpos = ((WCoord*)  tolua_tousertype(tolua_S,4,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'gotoTeleportPos'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->gotoTeleportPos(pworld,*blockpos,*realpos);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'gotoTeleportPos'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: gotoTransferPos of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_gotoTransferPos00
static int tolua_miniSandboxEngineActor_IClientPlayer_gotoTransferPos00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isusertype(tolua_S,2,"World",0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,3,&tolua_err) || !tolua_isusertype(tolua_S,3,"const WCoord",0,&tolua_err)) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  World* pworld = ((World*)  tolua_tousertype(tolua_S,2,0));
  const WCoord* blockpos = ((const WCoord*)  tolua_tousertype(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'gotoTransferPos'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->gotoTransferPos(pworld,*blockpos);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'gotoTransferPos'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: gotoSpawnPoint of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_gotoSpawnPoint00
static int tolua_miniSandboxEngineActor_IClientPlayer_gotoSpawnPoint00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isusertype(tolua_S,2,"World",0,&tolua_err) ||
     !tolua_isboolean(tolua_S,3,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  World* pworld = ((World*)  tolua_tousertype(tolua_S,2,0));
  bool bRandom = ((bool)  tolua_toboolean(tolua_S,3,true));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'gotoSpawnPoint'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->gotoSpawnPoint(pworld,bRandom);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'gotoSpawnPoint'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addAchievement of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_addAchievement00
static int tolua_miniSandboxEngineActor_IClientPlayer_addAchievement00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,6,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  int flags = ((int)  tolua_tonumber(tolua_S,2,0));
  ACHIEVEMENT_TYPE achievetype = ((ACHIEVEMENT_TYPE) (int)  tolua_tonumber(tolua_S,3,0));
  int target_id = ((int)  tolua_tonumber(tolua_S,4,0));
  int num = ((int)  tolua_tonumber(tolua_S,5,1));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addAchievement'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->addAchievement(flags,achievetype,target_id,num);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addAchievement'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: addOWScore of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_addOWScore00
static int tolua_miniSandboxEngineActor_IClientPlayer_addOWScore00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  float score = ((float)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'addOWScore'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->addOWScore(score);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'addOWScore'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: setSyncCustomModelTick of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_setSyncCustomModelTick00
static int tolua_miniSandboxEngineActor_IClientPlayer_setSyncCustomModelTick00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  int tick = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'setSyncCustomModelTick'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->setSyncCustomModelTick(tick);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'setSyncCustomModelTick'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: setSyncTransferTick of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_setSyncTransferTick00
static int tolua_miniSandboxEngineActor_IClientPlayer_setSyncTransferTick00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  int tick = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'setSyncTransferTick'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->setSyncTransferTick(tick);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'setSyncTransferTick'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: checkNewbieWorldProgress of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_checkNewbieWorldProgress00
static int tolua_miniSandboxEngineActor_IClientPlayer_checkNewbieWorldProgress00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  int curLv = ((int)  tolua_tonumber(tolua_S,2,0));
  int curStep = ((int)  tolua_tonumber(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'checkNewbieWorldProgress'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->checkNewbieWorldProgress(curLv,curStep);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'checkNewbieWorldProgress'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: applyEquips of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_applyEquips00
static int tolua_miniSandboxEngineActor_IClientPlayer_applyEquips00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  EQUIP_SLOT_TYPE t = ((EQUIP_SLOT_TYPE) (int)  tolua_tonumber(tolua_S,2,MAX_EQUIP_SLOTS));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'applyEquips'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->applyEquips(t);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'applyEquips'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: isUnlockItem of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_isUnlockItem00
static int tolua_miniSandboxEngineActor_IClientPlayer_isUnlockItem00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  int itemid = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'isUnlockItem'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->isUnlockItem(itemid);
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'isUnlockItem'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: isHost of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_isHost00
static int tolua_miniSandboxEngineActor_IClientPlayer_isHost00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'isHost'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->isHost();
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'isHost'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: isRemote of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_isRemote00
static int tolua_miniSandboxEngineActor_IClientPlayer_isRemote00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'isRemote'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->isRemote();
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'isRemote'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getOWID of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_getOWID00
static int tolua_miniSandboxEngineActor_IClientPlayer_getOWID00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getOWID'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   long long tolua_ret = (long long)  self->getOWID();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getOWID'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getCurOpenedContainer of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_getCurOpenedContainer00
static int tolua_miniSandboxEngineActor_IClientPlayer_getCurOpenedContainer00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getCurOpenedContainer'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   WorldContainer* tolua_ret = (WorldContainer*)  self->getCurOpenedContainer();
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"WorldContainer");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getCurOpenedContainer'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getCurOpenedContainerPos of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_getCurOpenedContainerPos00
static int tolua_miniSandboxEngineActor_IClientPlayer_getCurOpenedContainerPos00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getCurOpenedContainerPos'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   WCoord tolua_ret = (WCoord)  self->getCurOpenedContainerPos();
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((WCoord)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"WCoord");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(WCoord));
     tolua_pushusertype(tolua_S,tolua_obj,"WCoord");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getCurOpenedContainerPos'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: cleanupOpenedContainer of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_cleanupOpenedContainer00
static int tolua_miniSandboxEngineActor_IClientPlayer_cleanupOpenedContainer00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'cleanupOpenedContainer'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->cleanupOpenedContainer();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'cleanupOpenedContainer'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: syncOpenFCMUIToClient of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_syncOpenFCMUIToClient00
static int tolua_miniSandboxEngineActor_IClientPlayer_syncOpenFCMUIToClient00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,2,&tolua_err) || !tolua_isusertype(tolua_S,2,"const WCoord",0,&tolua_err)) ||
     !tolua_isboolean(tolua_S,3,0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,6,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,7,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  const WCoord* blockpos = ((const WCoord*)  tolua_tousertype(tolua_S,2,0));
  bool isedited = ((bool)  tolua_toboolean(tolua_S,3,0));
  std::string url = ((std::string)  tolua_tocppstring(tolua_S,4,0));
  int version = ((int)  tolua_tonumber(tolua_S,5,0));
  int result = ((int)  tolua_tonumber(tolua_S,6,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'syncOpenFCMUIToClient'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->syncOpenFCMUIToClient(*blockpos,isedited,url,version,result);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'syncOpenFCMUIToClient'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getCurOperate of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_getCurOperate00
static int tolua_miniSandboxEngineActor_IClientPlayer_getCurOperate00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getCurOperate'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getCurOperate();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getCurOperate'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getCurOperatePos of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_getCurOperatePos00
static int tolua_miniSandboxEngineActor_IClientPlayer_getCurOperatePos00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  int operatedistance = ((int)  tolua_tonumber(tolua_S,2,5));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getCurOperatePos'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   WCoord tolua_ret = (WCoord)  self->getCurOperatePos(operatedistance);
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((WCoord)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"WCoord");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(WCoord));
     tolua_pushusertype(tolua_S,tolua_obj,"WCoord");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getCurOperatePos'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetPlayerCurPlaceDir of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_GetPlayerCurPlaceDir00
static int tolua_miniSandboxEngineActor_IClientPlayer_GetPlayerCurPlaceDir00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'GetPlayerCurPlaceDir'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->GetPlayerCurPlaceDir();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'GetPlayerCurPlaceDir'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getPlaceDirToBlock of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_getPlaceDirToBlock00
static int tolua_miniSandboxEngineActor_IClientPlayer_getPlaceDirToBlock00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,2,&tolua_err) || !tolua_isusertype(tolua_S,2,"const WCoord",0,&tolua_err)) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  const WCoord* blockpos = ((const WCoord*)  tolua_tousertype(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getPlaceDirToBlock'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getPlaceDirToBlock(*blockpos);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getPlaceDirToBlock'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: shortcutItemUsed of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_shortcutItemUsed00
static int tolua_miniSandboxEngineActor_IClientPlayer_shortcutItemUsed00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isboolean(tolua_S,2,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  bool ignoreDurable = ((bool)  tolua_toboolean(tolua_S,2,false));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'shortcutItemUsed'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->shortcutItemUsed(ignoreDurable);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'shortcutItemUsed'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getHookObj of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_getHookObj00
static int tolua_miniSandboxEngineActor_IClientPlayer_getHookObj00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getHookObj'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   WORLD_ID tolua_ret = (WORLD_ID)  self->getHookObj();
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((WORLD_ID)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"WORLD_ID");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(WORLD_ID));
     tolua_pushusertype(tolua_S,tolua_obj,"WORLD_ID");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getHookObj'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetTreeItemIndex of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_GetTreeItemIndex00
static int tolua_miniSandboxEngineActor_IClientPlayer_GetTreeItemIndex00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'GetTreeItemIndex'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->GetTreeItemIndex();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'GetTreeItemIndex'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getOPWay of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_getOPWay00
static int tolua_miniSandboxEngineActor_IClientPlayer_getOPWay00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getOPWay'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getOPWay();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getOPWay'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: openEditActorModelUI of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_openEditActorModelUI00
static int tolua_miniSandboxEngineActor_IClientPlayer_openEditActorModelUI00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isusertype(tolua_S,2,"WorldContainer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  WorldContainer* container = ((WorldContainer*)  tolua_tousertype(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'openEditActorModelUI'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->openEditActorModelUI(container);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'openEditActorModelUI'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: openContainer of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_openContainer00
static int tolua_miniSandboxEngineActor_IClientPlayer_openContainer00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isusertype(tolua_S,2,"WorldContainer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  WorldContainer* container = ((WorldContainer*)  tolua_tousertype(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'openContainer'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->openContainer(container);
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'openContainer'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: closeContainer of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_closeContainer00
static int tolua_miniSandboxEngineActor_IClientPlayer_closeContainer00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'closeContainer'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->closeContainer();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'closeContainer'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: checkShowMusicClubChatBubble of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_checkShowMusicClubChatBubble00
static int tolua_miniSandboxEngineActor_IClientPlayer_checkShowMusicClubChatBubble00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  float dtime = ((float)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'checkShowMusicClubChatBubble'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->checkShowMusicClubChatBubble(dtime);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'checkShowMusicClubChatBubble'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getCurShortcut of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_getCurShortcut00
static int tolua_miniSandboxEngineActor_IClientPlayer_getCurShortcut00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getCurShortcut'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getCurShortcut();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getCurShortcut'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getCurShortcutItemNum of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_getCurShortcutItemNum00
static int tolua_miniSandboxEngineActor_IClientPlayer_getCurShortcutItemNum00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getCurShortcutItemNum'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getCurShortcutItemNum();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getCurShortcutItemNum'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: sleepInBed of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_sleepInBed00
static int tolua_miniSandboxEngineActor_IClientPlayer_sleepInBed00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,2,&tolua_err) || !tolua_isusertype(tolua_S,2,"const WCoord",0,&tolua_err)) ||
     !tolua_isboolean(tolua_S,3,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  const WCoord* blockpos = ((const WCoord*)  tolua_tousertype(tolua_S,2,0));
  bool refreshRevivePoint = ((bool)  tolua_toboolean(tolua_S,3,true));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'sleepInBed'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->sleepInBed(*blockpos,refreshRevivePoint);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'sleepInBed'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: sleepInVehicleBed of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_sleepInVehicleBed00
static int tolua_miniSandboxEngineActor_IClientPlayer_sleepInVehicleBed00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,2,&tolua_err) || !tolua_isusertype(tolua_S,2,"const WCoord",0,&tolua_err)) ||
     !tolua_isusertype(tolua_S,3,"World",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  const WCoord* blockpos = ((const WCoord*)  tolua_tousertype(tolua_S,2,0));
  World* pworld = ((World*)  tolua_tousertype(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'sleepInVehicleBed'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->sleepInVehicleBed(*blockpos,pworld);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'sleepInVehicleBed'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: sleep of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_sleep00
static int tolua_miniSandboxEngineActor_IClientPlayer_sleep00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,2,&tolua_err) || !tolua_isusertype(tolua_S,2,"const WCoord",0,&tolua_err)) ||
     !tolua_isboolean(tolua_S,3,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  const WCoord* blockpos = ((const WCoord*)  tolua_tousertype(tolua_S,2,0));
  bool refreshRevivePoint = ((bool)  tolua_toboolean(tolua_S,3,false));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'sleep'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->sleep(*blockpos,refreshRevivePoint);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'sleep'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: notifyOpenWindow2Self of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_notifyOpenWindow2Self00
static int tolua_miniSandboxEngineActor_IClientPlayer_notifyOpenWindow2Self00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,6,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  int blockid = ((int)  tolua_tonumber(tolua_S,2,0));
  int x = ((int)  tolua_tonumber(tolua_S,3,-1));
  int y = ((int)  tolua_tonumber(tolua_S,4,-1));
  int z = ((int)  tolua_tonumber(tolua_S,5,-1));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'notifyOpenWindow2Self'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->notifyOpenWindow2Self(blockid,x,y,z);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'notifyOpenWindow2Self'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: BlockBookCabinetVehicleDir of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_BlockBookCabinetVehicleDir00
static int tolua_miniSandboxEngineActor_IClientPlayer_BlockBookCabinetVehicleDir00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,2,&tolua_err) || !tolua_isusertype(tolua_S,2,"Rainbow::Vector3f",0,&tolua_err)) ||
     (tolua_isvaluenil(tolua_S,3,&tolua_err) || !tolua_isusertype(tolua_S,3,"Rainbow::Vector3f",0,&tolua_err)) ||
     !tolua_isusertype(tolua_S,4,"World",0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,5,&tolua_err) || !tolua_isusertype(tolua_S,5,"const WCoord",0,&tolua_err)) ||
     !tolua_isnoobj(tolua_S,6,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  Rainbow::Vector3f* viewDir = ((Rainbow::Vector3f*)  tolua_tousertype(tolua_S,2,0));
  Rainbow::Vector3f* centerDir = ((Rainbow::Vector3f*)  tolua_tousertype(tolua_S,3,0));
  World* pworld = ((World*)  tolua_tousertype(tolua_S,4,0));
  const WCoord* blockpos = ((const WCoord*)  tolua_tousertype(tolua_S,5,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'BlockBookCabinetVehicleDir'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->BlockBookCabinetVehicleDir(*viewDir,*centerDir,pworld,*blockpos);
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'BlockBookCabinetVehicleDir'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getPlayerAttribComponent of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_getPlayerAttribComponent00
static int tolua_miniSandboxEngineActor_IClientPlayer_getPlayerAttribComponent00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getPlayerAttribComponent'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   MNSandbox::Component* tolua_ret = (MNSandbox::Component*)  self->getPlayerAttribComponent();
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"MNSandbox::Component");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getPlayerAttribComponent'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getContainersPassword of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_getContainersPassword00
static int tolua_miniSandboxEngineActor_IClientPlayer_getContainersPassword00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,2,&tolua_err) || !tolua_isusertype(tolua_S,2,"const WCoord",0,&tolua_err)) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  const WCoord* pos = ((const WCoord*)  tolua_tousertype(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getContainersPassword'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getContainersPassword(*pos);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getContainersPassword'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: setContainersPassword of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_setContainersPassword00
static int tolua_miniSandboxEngineActor_IClientPlayer_setContainersPassword00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,2,&tolua_err) || !tolua_isusertype(tolua_S,2,"const WCoord",0,&tolua_err)) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  const WCoord* pos = ((const WCoord*)  tolua_tousertype(tolua_S,2,0));
  int password = ((int)  tolua_tonumber(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'setContainersPassword'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->setContainersPassword(*pos,password);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'setContainersPassword'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: checkIsOpenContainer of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_checkIsOpenContainer00
static int tolua_miniSandboxEngineActor_IClientPlayer_checkIsOpenContainer00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,2,&tolua_err) || !tolua_isusertype(tolua_S,2,"const WCoord",0,&tolua_err)) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  const WCoord* pos = ((const WCoord*)  tolua_tousertype(tolua_S,2,0));
  int index = ((int)  tolua_tonumber(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'checkIsOpenContainer'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->checkIsOpenContainer(*pos,index);
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'checkIsOpenContainer'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: refreshAvarta of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_refreshAvarta00
static int tolua_miniSandboxEngineActor_IClientPlayer_refreshAvarta00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'refreshAvarta'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->refreshAvarta();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'refreshAvarta'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: PlayerWakeUp of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_PlayerWakeUp00
static int tolua_miniSandboxEngineActor_IClientPlayer_PlayerWakeUp00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isboolean(tolua_S,2,0,&tolua_err) ||
     !tolua_isboolean(tolua_S,3,0,&tolua_err) ||
     !tolua_isboolean(tolua_S,4,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,5,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  bool immediately = ((bool)  tolua_toboolean(tolua_S,2,0));
  bool updateallflag = ((bool)  tolua_toboolean(tolua_S,3,0));
  bool setrevive = ((bool)  tolua_toboolean(tolua_S,4,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'PlayerWakeUp'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->PlayerWakeUp(immediately,updateallflag,setrevive);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'PlayerWakeUp'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: SetRunSandboxPlayer of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_SetRunSandboxPlayer00
static int tolua_miniSandboxEngineActor_IClientPlayer_SetRunSandboxPlayer00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isboolean(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  bool value = ((bool)  tolua_toboolean(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'SetRunSandboxPlayer'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->SetRunSandboxPlayer(value);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'SetRunSandboxPlayer'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: IsRunSandboxPlayer of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_IsRunSandboxPlayer00
static int tolua_miniSandboxEngineActor_IClientPlayer_IsRunSandboxPlayer00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'IsRunSandboxPlayer'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->IsRunSandboxPlayer();
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'IsRunSandboxPlayer'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: changePlayerModel of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_changePlayerModel00
static int tolua_miniSandboxEngineActor_IClientPlayer_changePlayerModel00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,1,&tolua_err) ||
     !tolua_isstring(tolua_S,4,1,&tolua_err) ||
     !tolua_isstring(tolua_S,5,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,6,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,7,1,&tolua_err) ||
     !tolua_isboolean(tolua_S,8,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,9,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  int playerindex = ((int)  tolua_tonumber(tolua_S,2,0));
  int mutatemob = ((int)  tolua_tonumber(tolua_S,3,0));
  const char* customskin = ((const char*)  tolua_tostring(tolua_S,4,""));
  const char* custommodel = ((const char*)  tolua_tostring(tolua_S,5,NULL));
  int itemid = ((int)  tolua_tonumber(tolua_S,6,0));
  int blockid = ((int)  tolua_tonumber(tolua_S,7,0));
  bool force = ((bool)  tolua_toboolean(tolua_S,8,false));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'changePlayerModel'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->changePlayerModel(playerindex,mutatemob,customskin,custommodel,itemid,blockid,force);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'changePlayerModel'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: setSpeedUpTimes of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_setSpeedUpTimes00
static int tolua_miniSandboxEngineActor_IClientPlayer_setSpeedUpTimes00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  unsigned char speedUpTimes = ((unsigned char)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'setSpeedUpTimes'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->setSpeedUpTimes(speedUpTimes);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'setSpeedUpTimes'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getSelectedColor of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_getSelectedColor00
static int tolua_miniSandboxEngineActor_IClientPlayer_getSelectedColor00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getSelectedColor'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getSelectedColor();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getSelectedColor'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: setSelectedColor of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_setSelectedColor00
static int tolua_miniSandboxEngineActor_IClientPlayer_setSelectedColor00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  int color = ((int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'setSelectedColor'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->setSelectedColor(color);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'setSelectedColor'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: isUseHearth of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_isUseHearth00
static int tolua_miniSandboxEngineActor_IClientPlayer_isUseHearth00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'isUseHearth'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->isUseHearth();
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'isUseHearth'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: triggerInputEvent of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_triggerInputEvent00
static int tolua_miniSandboxEngineActor_IClientPlayer_triggerInputEvent00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isstring(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  int vkey = ((int)  tolua_tonumber(tolua_S,2,0));
  const char* ktype = ((const char*)  tolua_tostring(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'triggerInputEvent'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->triggerInputEvent(vkey,ktype);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'triggerInputEvent'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getEquipItem of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_getEquipItem00
static int tolua_miniSandboxEngineActor_IClientPlayer_getEquipItem00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  EQUIP_SLOT_TYPE t = ((EQUIP_SLOT_TYPE) (int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getEquipItem'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->getEquipItem(t);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getEquipItem'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getEquipGrid of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_getEquipGrid00
static int tolua_miniSandboxEngineActor_IClientPlayer_getEquipGrid00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  EQUIP_SLOT_TYPE t = ((EQUIP_SLOT_TYPE) (int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getEquipGrid'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   BackPackGrid* tolua_ret = (BackPackGrid*)  self->getEquipGrid(t);
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"BackPackGrid");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getEquipGrid'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getEquipGridWithType of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_getEquipGridWithType00
static int tolua_miniSandboxEngineActor_IClientPlayer_getEquipGridWithType00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  EQUIP_SLOT_TYPE t = ((EQUIP_SLOT_TYPE) (int)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getEquipGridWithType'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   BackPackGrid* tolua_ret = (BackPackGrid*)  self->getEquipGridWithType(t);
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"BackPackGrid");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getEquipGridWithType'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: damageEquipItem of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_damageEquipItem00
static int tolua_miniSandboxEngineActor_IClientPlayer_damageEquipItem00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  EQUIP_SLOT_TYPE t = ((EQUIP_SLOT_TYPE) (int)  tolua_tonumber(tolua_S,2,0));
  int damage = ((int)  tolua_tonumber(tolua_S,3,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'damageEquipItem'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   int tolua_ret = (int)  self->damageEquipItem(t,damage);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'damageEquipItem'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: isShapeShift of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_isShapeShift00
static int tolua_miniSandboxEngineActor_IClientPlayer_isShapeShift00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'isShapeShift'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->isShapeShift();
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'isShapeShift'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: setPianoSoundName of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_setPianoSoundName00
static int tolua_miniSandboxEngineActor_IClientPlayer_setPianoSoundName00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  std::string pSoundName = ((std::string)  tolua_tocppstring(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'setPianoSoundName'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->setPianoSoundName(pSoundName);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'setPianoSoundName'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: setPianoPaticleName of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_setPianoPaticleName00
static int tolua_miniSandboxEngineActor_IClientPlayer_setPianoPaticleName00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  std::string pPaticleName = ((std::string)  tolua_tocppstring(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'setPianoPaticleName'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->setPianoPaticleName(pPaticleName);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'setPianoPaticleName'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: setPaticlePos of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_setPaticlePos00
static int tolua_miniSandboxEngineActor_IClientPlayer_setPaticlePos00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,2,&tolua_err) || !tolua_isusertype(tolua_S,2,"WCoord",0,&tolua_err)) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  WCoord pos = *((WCoord*)  tolua_tousertype(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'setPaticlePos'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->setPaticlePos(pos);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'setPaticlePos'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: setPianoSoundPos of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_setPianoSoundPos00
static int tolua_miniSandboxEngineActor_IClientPlayer_setPianoSoundPos00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,2,&tolua_err) || !tolua_isusertype(tolua_S,2,"WCoord",0,&tolua_err)) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  WCoord pos = *((WCoord*)  tolua_tousertype(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'setPianoSoundPos'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->setPianoSoundPos(pos);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'setPianoSoundPos'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getCurSummonPetID of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_getCurSummonPetID00
static int tolua_miniSandboxEngineActor_IClientPlayer_getCurSummonPetID00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getCurSummonPetID'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   std::string tolua_ret = (std::string)  self->getCurSummonPetID();
   tolua_pushcppstring(tolua_S,(const char*)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getCurSummonPetID'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getCurSummonPetInfo of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_getCurSummonPetInfo00
static int tolua_miniSandboxEngineActor_IClientPlayer_getCurSummonPetInfo00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getCurSummonPetInfo'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   SummonPetInfomation tolua_ret = (SummonPetInfomation)  self->getCurSummonPetInfo();
   {
#ifdef __cplusplus
    void* tolua_obj = Mtolua_new((SummonPetInfomation)(tolua_ret));
     tolua_pushusertype(tolua_S,tolua_obj,"SummonPetInfomation");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#else
    void* tolua_obj = tolua_copy(tolua_S,(void*)&tolua_ret,sizeof(SummonPetInfomation));
     tolua_pushusertype(tolua_S,tolua_obj,"SummonPetInfomation");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
#endif
   }
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getCurSummonPetInfo'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: summonPet of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_summonPet00
static int tolua_miniSandboxEngineActor_IClientPlayer_summonPet00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,5,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,6,0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,7,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,8,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  int monsterid = ((int)  tolua_tonumber(tolua_S,2,0));
  std::string serverid = ((std::string)  tolua_tocppstring(tolua_S,3,0));
  int petid = ((int)  tolua_tonumber(tolua_S,4,0));
  int stage = ((int)  tolua_tonumber(tolua_S,5,0));
  int quality = ((int)  tolua_tonumber(tolua_S,6,0));
  std::string petName = ((std::string)  tolua_tocppstring(tolua_S,7,""));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'summonPet'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->summonPet(monsterid,serverid,petid,stage,quality,petName);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'summonPet'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: changeRoleData of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_changeRoleData00
static int tolua_miniSandboxEngineActor_IClientPlayer_changeRoleData00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isusertype(tolua_S,2,"game::common::PB_RoleData",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  game::common::PB_RoleData* pRoleData = ((game::common::PB_RoleData*)  tolua_tousertype(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'changeRoleData'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->changeRoleData(pRoleData);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'changeRoleData'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: saveSkillCDCompToPB of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_saveSkillCDCompToPB00
static int tolua_miniSandboxEngineActor_IClientPlayer_saveSkillCDCompToPB00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isusertype(tolua_S,2,"game::common::PB_SkillCDData",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  game::common::PB_SkillCDData* skillCDData = ((game::common::PB_SkillCDData*)  tolua_tousertype(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'saveSkillCDCompToPB'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->saveSkillCDCompToPB(skillCDData);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'saveSkillCDCompToPB'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: getSpectatorMode of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_getSpectatorMode00
static int tolua_miniSandboxEngineActor_IClientPlayer_getSpectatorMode00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'getSpectatorMode'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   PLAYER_SPECTATOR_MODE tolua_ret = (PLAYER_SPECTATOR_MODE)  self->getSpectatorMode();
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'getSpectatorMode'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: IsOnPlatform of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_IsOnPlatform00
static int tolua_miniSandboxEngineActor_IClientPlayer_IsOnPlatform00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'IsOnPlatform'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->IsOnPlatform();
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'IsOnPlatform'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetCatchBall of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_GetCatchBall00
static int tolua_miniSandboxEngineActor_IClientPlayer_GetCatchBall00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'GetCatchBall'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   IClientActor* tolua_ret = (IClientActor*)  self->GetCatchBall();
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"IClientActor");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'GetCatchBall'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetPlatformIClientActor of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_GetPlatformIClientActor00
static int tolua_miniSandboxEngineActor_IClientPlayer_GetPlatformIClientActor00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'GetPlatformIClientActor'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   IClientActor* tolua_ret = (IClientActor*)  self->GetPlatformIClientActor();
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"IClientActor");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'GetPlatformIClientActor'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetGunZoom of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_GetGunZoom00
static int tolua_miniSandboxEngineActor_IClientPlayer_GetGunZoom00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'GetGunZoom'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->GetGunZoom();
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'GetGunZoom'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: doWaterCanoonSkill of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_doWaterCanoonSkill00
static int tolua_miniSandboxEngineActor_IClientPlayer_doWaterCanoonSkill00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'doWaterCanoonSkill'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->doWaterCanoonSkill();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'doWaterCanoonSkill'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: setFaceYaw of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_setFaceYaw00
static int tolua_miniSandboxEngineActor_IClientPlayer_setFaceYaw00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isboolean(tolua_S,3,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  float yaw = ((float)  tolua_tonumber(tolua_S,2,0));
  bool needSync = ((bool)  tolua_toboolean(tolua_S,3,false));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'setFaceYaw'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->setFaceYaw(yaw,needSync);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'setFaceYaw'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: isMotionChangeSyncPos of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_isMotionChangeSyncPos00
static int tolua_miniSandboxEngineActor_IClientPlayer_isMotionChangeSyncPos00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'isMotionChangeSyncPos'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->isMotionChangeSyncPos();
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'isMotionChangeSyncPos'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetScreenSpacePos of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_GetScreenSpacePos00
static int tolua_miniSandboxEngineActor_IClientPlayer_GetScreenSpacePos00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     (tolua_isvaluenil(tolua_S,2,&tolua_err) || !tolua_isusertype(tolua_S,2,"Rainbow::Vector3f",0,&tolua_err)) ||
     !tolua_isnumber(tolua_S,3,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,4,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,5,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
  Rainbow::Vector3f pos = *((Rainbow::Vector3f*)  tolua_tousertype(tolua_S,2,0));
  int x = ((int)  tolua_tonumber(tolua_S,3,0));
  int y = ((int)  tolua_tonumber(tolua_S,4,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'GetScreenSpacePos'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->GetScreenSpacePos(pos,x,y);
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
   tolua_pushnumber(tolua_S,(lua_Number)x);
   tolua_pushnumber(tolua_S,(lua_Number)y);
  }
 }
 return 3;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'GetScreenSpacePos'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: IsCurToolBroken of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_IsCurToolBroken00
static int tolua_miniSandboxEngineActor_IClientPlayer_IsCurToolBroken00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'IsCurToolBroken'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->IsCurToolBroken();
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'IsCurToolBroken'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: IsOffline of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_IsOffline00
static int tolua_miniSandboxEngineActor_IClientPlayer_IsOffline00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'IsOffline'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->IsOffline();
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'IsOffline'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: checkInSafeZone of class  IClientPlayer */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_IClientPlayer_checkInSafeZone00
static int tolua_miniSandboxEngineActor_IClientPlayer_checkInSafeZone00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"IClientPlayer",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  IClientPlayer* self = (IClientPlayer*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'checkInSafeZone'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   bool tolua_ret = (bool)  self->checkInSafeZone();
   tolua_pushboolean(tolua_S,(bool)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'checkInSafeZone'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* get function: __Rainbow of class  IClientActor */
#ifndef TOLUA_DISABLE_tolua_get_IClientActor___Rainbow__SceneMGTNode__
static int tolua_get_IClientActor___Rainbow__SceneMGTNode__(lua_State* tolua_S)
{
  IClientActor* self = (IClientActor*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable '__Rainbow'",NULL);
#else 
  if (!self) return 0;
#endif
#ifdef __cplusplus
   tolua_pushusertype(tolua_S,(void*)static_cast<Rainbow::SceneMGTNode*>(self), "Rainbow::SceneMGTNode");
#else
   tolua_pushusertype(tolua_S,(void*)((Rainbow::SceneMGTNode*)self), "Rainbow::SceneMGTNode");
#endif
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* function: ComposePlayerIndex */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_ComposePlayerIndex00
static int tolua_miniSandboxEngineActor_ComposePlayerIndex00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isnumber(tolua_S,1,0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,1,&tolua_err) ||
     !tolua_isnumber(tolua_S,3,1,&tolua_err) ||
     !tolua_isnoobj(tolua_S,4,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  int modelid = ((int)  tolua_tonumber(tolua_S,1,0));
  int geniuslv = ((int)  tolua_tonumber(tolua_S,2,0));
  int skinid = ((int)  tolua_tonumber(tolua_S,3,0));
  {
   int tolua_ret = (int)  ComposePlayerIndex(modelid,geniuslv,skinid);
   tolua_pushnumber(tolua_S,(lua_Number)tolua_ret);
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'ComposePlayerIndex'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: Produce of class  ActorComponentBase */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_ActorComponentBase_Produce00
static int tolua_miniSandboxEngineActor_ActorComponentBase_Produce00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertable(tolua_S,1,"ActorComponentBase",0,&tolua_err) ||
     !tolua_iscppstring(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  const std::string name = ((const std::string)  tolua_tocppstring(tolua_S,2,0));
  {
   ActorComponentBase* tolua_ret = (ActorComponentBase*)  ActorComponentBase::Produce(name);
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"ActorComponentBase");
   tolua_pushcppstring(tolua_S,(const char*)name);
  }
 }
 return 2;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'Produce'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: new of class  ActorComponentBase */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_ActorComponentBase_new00
static int tolua_miniSandboxEngineActor_ActorComponentBase_new00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertable(tolua_S,1,"ActorComponentBase",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  {
   ActorComponentBase* tolua_ret = (ActorComponentBase*)  Mtolua_new((ActorComponentBase)());
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"ActorComponentBase");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'new'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: new_local of class  ActorComponentBase */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_ActorComponentBase_new00_local
static int tolua_miniSandboxEngineActor_ActorComponentBase_new00_local(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertable(tolua_S,1,"ActorComponentBase",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  {
   ActorComponentBase* tolua_ret = (ActorComponentBase*)  Mtolua_new((ActorComponentBase)());
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"ActorComponentBase");
    tolua_register_gc(tolua_S,lua_gettop(tolua_S));
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'new'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: delete of class  ActorComponentBase */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_ActorComponentBase_delete00
static int tolua_miniSandboxEngineActor_ActorComponentBase_delete00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ActorComponentBase",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ActorComponentBase* self = (ActorComponentBase*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'delete'", NULL);
#else 
  if (!self) return 0;
#endif
  Mtolua_delete(self);
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'delete'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: OnTick of class  ActorComponentBase */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_ActorComponentBase_OnTick00
static int tolua_miniSandboxEngineActor_ActorComponentBase_OnTick00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ActorComponentBase",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ActorComponentBase* self = (ActorComponentBase*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'OnTick'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->OnTick();
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'OnTick'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: OnUpdate of class  ActorComponentBase */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_ActorComponentBase_OnUpdate00
static int tolua_miniSandboxEngineActor_ActorComponentBase_OnUpdate00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ActorComponentBase",0,&tolua_err) ||
     !tolua_isnumber(tolua_S,2,0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,3,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ActorComponentBase* self = (ActorComponentBase*)  tolua_tousertype(tolua_S,1,0);
  float elapse = ((float)  tolua_tonumber(tolua_S,2,0));
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'OnUpdate'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   self->OnUpdate(elapse);
  }
 }
 return 0;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'OnUpdate'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetOwnerActor of class  ActorComponentBase */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_ActorComponentBase_GetOwnerActor00
static int tolua_miniSandboxEngineActor_ActorComponentBase_GetOwnerActor00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ActorComponentBase",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ActorComponentBase* self = (ActorComponentBase*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'GetOwnerActor'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   IClientActor* tolua_ret = (IClientActor*)  self->GetOwnerActor();
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"IClientActor");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'GetOwnerActor'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetOwnerMob of class  ActorComponentBase */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_ActorComponentBase_GetOwnerMob00
static int tolua_miniSandboxEngineActor_ActorComponentBase_GetOwnerMob00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ActorComponentBase",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ActorComponentBase* self = (ActorComponentBase*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'GetOwnerMob'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   IClientMob* tolua_ret = (IClientMob*)  self->GetOwnerMob();
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"IClientMob");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'GetOwnerMob'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetOwnerPlayer of class  ActorComponentBase */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_ActorComponentBase_GetOwnerPlayer00
static int tolua_miniSandboxEngineActor_ActorComponentBase_GetOwnerPlayer00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ActorComponentBase",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ActorComponentBase* self = (ActorComponentBase*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'GetOwnerPlayer'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   IClientPlayer* tolua_ret = (IClientPlayer*)  self->GetOwnerPlayer();
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"IClientPlayer");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'GetOwnerPlayer'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* method: GetOwnerActorLiving of class  ActorComponentBase */
#ifndef TOLUA_DISABLE_tolua_miniSandboxEngineActor_ActorComponentBase_GetOwnerActorLiving00
static int tolua_miniSandboxEngineActor_ActorComponentBase_GetOwnerActorLiving00(lua_State* tolua_S)
{
#ifndef TOLUA_RELEASE
 tolua_Error tolua_err;
 if (
     !tolua_isusertype(tolua_S,1,"ActorComponentBase",0,&tolua_err) ||
     !tolua_isnoobj(tolua_S,2,&tolua_err)
 )
  goto tolua_lerror;
 else
#endif
 {
  ActorComponentBase* self = (ActorComponentBase*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in function 'GetOwnerActorLiving'", NULL);
#else 
  if (!self) return 0;
#endif
  {
   IActorLiving* tolua_ret = (IActorLiving*)  self->GetOwnerActorLiving();
    tolua_pushusertype(tolua_S,(void*)tolua_ret,"IActorLiving");
  }
 }
 return 1;
#ifndef TOLUA_RELEASE
 tolua_lerror:
 tolua_error(tolua_S,"#ferror in function 'GetOwnerActorLiving'.",&tolua_err);
 return 0;
#endif
}
#endif //#ifndef TOLUA_DISABLE

/* get function: MaxHP of class  ActorAttribut */
#ifndef TOLUA_DISABLE_tolua_get_ActorAttribut_MaxHP
static int tolua_get_ActorAttribut_MaxHP(lua_State* tolua_S)
{
  ActorAttribut* self = (ActorAttribut*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'MaxHP'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushnumber(tolua_S,(lua_Number)self->MaxHP);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: MaxHP of class  ActorAttribut */
#ifndef TOLUA_DISABLE_tolua_set_ActorAttribut_MaxHP
static int tolua_set_ActorAttribut_MaxHP(lua_State* tolua_S)
{
  ActorAttribut* self = (ActorAttribut*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'MaxHP'",NULL);
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->MaxHP = ((float)  tolua_tonumber(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: NowHP of class  ActorAttribut */
#ifndef TOLUA_DISABLE_tolua_get_ActorAttribut_NowHP
static int tolua_get_ActorAttribut_NowHP(lua_State* tolua_S)
{
  ActorAttribut* self = (ActorAttribut*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'NowHP'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushnumber(tolua_S,(lua_Number)self->NowHP);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: NowHP of class  ActorAttribut */
#ifndef TOLUA_DISABLE_tolua_set_ActorAttribut_NowHP
static int tolua_set_ActorAttribut_NowHP(lua_State* tolua_S)
{
  ActorAttribut* self = (ActorAttribut*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'NowHP'",NULL);
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->NowHP = ((float)  tolua_tonumber(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: HPRecover of class  ActorAttribut */
#ifndef TOLUA_DISABLE_tolua_get_ActorAttribut_HPRecover
static int tolua_get_ActorAttribut_HPRecover(lua_State* tolua_S)
{
  ActorAttribut* self = (ActorAttribut*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'HPRecover'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushnumber(tolua_S,(lua_Number)self->HPRecover);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: HPRecover of class  ActorAttribut */
#ifndef TOLUA_DISABLE_tolua_set_ActorAttribut_HPRecover
static int tolua_set_ActorAttribut_HPRecover(lua_State* tolua_S)
{
  ActorAttribut* self = (ActorAttribut*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'HPRecover'",NULL);
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->HPRecover = ((float)  tolua_tonumber(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: MaxHunger of class  ActorAttribut */
#ifndef TOLUA_DISABLE_tolua_get_ActorAttribut_MaxHunger
static int tolua_get_ActorAttribut_MaxHunger(lua_State* tolua_S)
{
  ActorAttribut* self = (ActorAttribut*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'MaxHunger'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushnumber(tolua_S,(lua_Number)self->MaxHunger);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: MaxHunger of class  ActorAttribut */
#ifndef TOLUA_DISABLE_tolua_set_ActorAttribut_MaxHunger
static int tolua_set_ActorAttribut_MaxHunger(lua_State* tolua_S)
{
  ActorAttribut* self = (ActorAttribut*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'MaxHunger'",NULL);
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->MaxHunger = ((float)  tolua_tonumber(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: NowHunger of class  ActorAttribut */
#ifndef TOLUA_DISABLE_tolua_get_ActorAttribut_NowHunger
static int tolua_get_ActorAttribut_NowHunger(lua_State* tolua_S)
{
  ActorAttribut* self = (ActorAttribut*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'NowHunger'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushnumber(tolua_S,(lua_Number)self->NowHunger);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: NowHunger of class  ActorAttribut */
#ifndef TOLUA_DISABLE_tolua_set_ActorAttribut_NowHunger
static int tolua_set_ActorAttribut_NowHunger(lua_State* tolua_S)
{
  ActorAttribut* self = (ActorAttribut*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'NowHunger'",NULL);
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->NowHunger = ((int)  tolua_tonumber(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: MaxOxygen of class  ActorAttribut */
#ifndef TOLUA_DISABLE_tolua_get_ActorAttribut_MaxOxygen
static int tolua_get_ActorAttribut_MaxOxygen(lua_State* tolua_S)
{
  ActorAttribut* self = (ActorAttribut*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'MaxOxygen'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushnumber(tolua_S,(lua_Number)self->MaxOxygen);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: MaxOxygen of class  ActorAttribut */
#ifndef TOLUA_DISABLE_tolua_set_ActorAttribut_MaxOxygen
static int tolua_set_ActorAttribut_MaxOxygen(lua_State* tolua_S)
{
  ActorAttribut* self = (ActorAttribut*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'MaxOxygen'",NULL);
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->MaxOxygen = ((float)  tolua_tonumber(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: NowOxygen of class  ActorAttribut */
#ifndef TOLUA_DISABLE_tolua_get_ActorAttribut_NowOxygen
static int tolua_get_ActorAttribut_NowOxygen(lua_State* tolua_S)
{
  ActorAttribut* self = (ActorAttribut*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'NowOxygen'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushnumber(tolua_S,(lua_Number)self->NowOxygen);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: NowOxygen of class  ActorAttribut */
#ifndef TOLUA_DISABLE_tolua_set_ActorAttribut_NowOxygen
static int tolua_set_ActorAttribut_NowOxygen(lua_State* tolua_S)
{
  ActorAttribut* self = (ActorAttribut*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'NowOxygen'",NULL);
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->NowOxygen = ((float)  tolua_tonumber(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: MoveSpeed of class  ActorAttribut */
#ifndef TOLUA_DISABLE_tolua_get_ActorAttribut_MoveSpeed
static int tolua_get_ActorAttribut_MoveSpeed(lua_State* tolua_S)
{
  ActorAttribut* self = (ActorAttribut*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'MoveSpeed'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushnumber(tolua_S,(lua_Number)self->MoveSpeed);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: MoveSpeed of class  ActorAttribut */
#ifndef TOLUA_DISABLE_tolua_set_ActorAttribut_MoveSpeed
static int tolua_set_ActorAttribut_MoveSpeed(lua_State* tolua_S)
{
  ActorAttribut* self = (ActorAttribut*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'MoveSpeed'",NULL);
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->MoveSpeed = ((float)  tolua_tonumber(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: RunSpeed of class  ActorAttribut */
#ifndef TOLUA_DISABLE_tolua_get_ActorAttribut_RunSpeed
static int tolua_get_ActorAttribut_RunSpeed(lua_State* tolua_S)
{
  ActorAttribut* self = (ActorAttribut*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'RunSpeed'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushnumber(tolua_S,(lua_Number)self->RunSpeed);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: RunSpeed of class  ActorAttribut */
#ifndef TOLUA_DISABLE_tolua_set_ActorAttribut_RunSpeed
static int tolua_set_ActorAttribut_RunSpeed(lua_State* tolua_S)
{
  ActorAttribut* self = (ActorAttribut*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'RunSpeed'",NULL);
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->RunSpeed = ((float)  tolua_tonumber(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: SwimSpeed of class  ActorAttribut */
#ifndef TOLUA_DISABLE_tolua_get_ActorAttribut_SwimSpeed
static int tolua_get_ActorAttribut_SwimSpeed(lua_State* tolua_S)
{
  ActorAttribut* self = (ActorAttribut*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'SwimSpeed'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushnumber(tolua_S,(lua_Number)self->SwimSpeed);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: SwimSpeed of class  ActorAttribut */
#ifndef TOLUA_DISABLE_tolua_set_ActorAttribut_SwimSpeed
static int tolua_set_ActorAttribut_SwimSpeed(lua_State* tolua_S)
{
  ActorAttribut* self = (ActorAttribut*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'SwimSpeed'",NULL);
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->SwimSpeed = ((float)  tolua_tonumber(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: JumpSpeed of class  ActorAttribut */
#ifndef TOLUA_DISABLE_tolua_get_ActorAttribut_JumpSpeed
static int tolua_get_ActorAttribut_JumpSpeed(lua_State* tolua_S)
{
  ActorAttribut* self = (ActorAttribut*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'JumpSpeed'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushnumber(tolua_S,(lua_Number)self->JumpSpeed);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: JumpSpeed of class  ActorAttribut */
#ifndef TOLUA_DISABLE_tolua_set_ActorAttribut_JumpSpeed
static int tolua_set_ActorAttribut_JumpSpeed(lua_State* tolua_S)
{
  ActorAttribut* self = (ActorAttribut*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'JumpSpeed'",NULL);
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->JumpSpeed = ((float)  tolua_tonumber(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: Weight of class  ActorAttribut */
#ifndef TOLUA_DISABLE_tolua_get_ActorAttribut_Weight
static int tolua_get_ActorAttribut_Weight(lua_State* tolua_S)
{
  ActorAttribut* self = (ActorAttribut*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'Weight'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushnumber(tolua_S,(lua_Number)self->Weight);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: Weight of class  ActorAttribut */
#ifndef TOLUA_DISABLE_tolua_set_ActorAttribut_Weight
static int tolua_set_ActorAttribut_Weight(lua_State* tolua_S)
{
  ActorAttribut* self = (ActorAttribut*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'Weight'",NULL);
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->Weight = ((float)  tolua_tonumber(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: SneakSpeed of class  ActorAttribut */
#ifndef TOLUA_DISABLE_tolua_get_ActorAttribut_SneakSpeed
static int tolua_get_ActorAttribut_SneakSpeed(lua_State* tolua_S)
{
  ActorAttribut* self = (ActorAttribut*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'SneakSpeed'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushnumber(tolua_S,(lua_Number)self->SneakSpeed);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: SneakSpeed of class  ActorAttribut */
#ifndef TOLUA_DISABLE_tolua_set_ActorAttribut_SneakSpeed
static int tolua_set_ActorAttribut_SneakSpeed(lua_State* tolua_S)
{
  ActorAttribut* self = (ActorAttribut*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'SneakSpeed'",NULL);
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->SneakSpeed = ((float)  tolua_tonumber(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: Dodge of class  ActorAttribut */
#ifndef TOLUA_DISABLE_tolua_get_ActorAttribut_Dodge
static int tolua_get_ActorAttribut_Dodge(lua_State* tolua_S)
{
  ActorAttribut* self = (ActorAttribut*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'Dodge'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushnumber(tolua_S,(lua_Number)self->Dodge);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: Dodge of class  ActorAttribut */
#ifndef TOLUA_DISABLE_tolua_set_ActorAttribut_Dodge
static int tolua_set_ActorAttribut_Dodge(lua_State* tolua_S)
{
  ActorAttribut* self = (ActorAttribut*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'Dodge'",NULL);
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->Dodge = ((float)  tolua_tonumber(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: NearAttack of class  ActorAttribut */
#ifndef TOLUA_DISABLE_tolua_get_ActorAttribut_NearAttack
static int tolua_get_ActorAttribut_NearAttack(lua_State* tolua_S)
{
  ActorAttribut* self = (ActorAttribut*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'NearAttack'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushnumber(tolua_S,(lua_Number)self->NearAttack);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: NearAttack of class  ActorAttribut */
#ifndef TOLUA_DISABLE_tolua_set_ActorAttribut_NearAttack
static int tolua_set_ActorAttribut_NearAttack(lua_State* tolua_S)
{
  ActorAttribut* self = (ActorAttribut*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'NearAttack'",NULL);
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->NearAttack = ((float)  tolua_tonumber(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: RemoteAttack of class  ActorAttribut */
#ifndef TOLUA_DISABLE_tolua_get_ActorAttribut_RemoteAttack
static int tolua_get_ActorAttribut_RemoteAttack(lua_State* tolua_S)
{
  ActorAttribut* self = (ActorAttribut*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'RemoteAttack'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushnumber(tolua_S,(lua_Number)self->RemoteAttack);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: RemoteAttack of class  ActorAttribut */
#ifndef TOLUA_DISABLE_tolua_set_ActorAttribut_RemoteAttack
static int tolua_set_ActorAttribut_RemoteAttack(lua_State* tolua_S)
{
  ActorAttribut* self = (ActorAttribut*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'RemoteAttack'",NULL);
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->RemoteAttack = ((float)  tolua_tonumber(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: NearArmor of class  ActorAttribut */
#ifndef TOLUA_DISABLE_tolua_get_ActorAttribut_NearArmor
static int tolua_get_ActorAttribut_NearArmor(lua_State* tolua_S)
{
  ActorAttribut* self = (ActorAttribut*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'NearArmor'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushnumber(tolua_S,(lua_Number)self->NearArmor);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: NearArmor of class  ActorAttribut */
#ifndef TOLUA_DISABLE_tolua_set_ActorAttribut_NearArmor
static int tolua_set_ActorAttribut_NearArmor(lua_State* tolua_S)
{
  ActorAttribut* self = (ActorAttribut*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'NearArmor'",NULL);
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->NearArmor = ((float)  tolua_tonumber(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: RemoteArmor of class  ActorAttribut */
#ifndef TOLUA_DISABLE_tolua_get_ActorAttribut_RemoteArmor
static int tolua_get_ActorAttribut_RemoteArmor(lua_State* tolua_S)
{
  ActorAttribut* self = (ActorAttribut*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'RemoteArmor'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushnumber(tolua_S,(lua_Number)self->RemoteArmor);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: RemoteArmor of class  ActorAttribut */
#ifndef TOLUA_DISABLE_tolua_set_ActorAttribut_RemoteArmor
static int tolua_set_ActorAttribut_RemoteArmor(lua_State* tolua_S)
{
  ActorAttribut* self = (ActorAttribut*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'RemoteArmor'",NULL);
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->RemoteArmor = ((float)  tolua_tonumber(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: Dimension of class  ActorAttribut */
#ifndef TOLUA_DISABLE_tolua_get_ActorAttribut_Dimension
static int tolua_get_ActorAttribut_Dimension(lua_State* tolua_S)
{
  ActorAttribut* self = (ActorAttribut*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'Dimension'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushnumber(tolua_S,(lua_Number)self->Dimension);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: Dimension of class  ActorAttribut */
#ifndef TOLUA_DISABLE_tolua_set_ActorAttribut_Dimension
static int tolua_set_ActorAttribut_Dimension(lua_State* tolua_S)
{
  ActorAttribut* self = (ActorAttribut*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'Dimension'",NULL);
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->Dimension = ((float)  tolua_tonumber(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: Score of class  ActorAttribut */
#ifndef TOLUA_DISABLE_tolua_get_ActorAttribut_Score
static int tolua_get_ActorAttribut_Score(lua_State* tolua_S)
{
  ActorAttribut* self = (ActorAttribut*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'Score'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushnumber(tolua_S,(lua_Number)self->Score);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: Score of class  ActorAttribut */
#ifndef TOLUA_DISABLE_tolua_set_ActorAttribut_Score
static int tolua_set_ActorAttribut_Score(lua_State* tolua_S)
{
  ActorAttribut* self = (ActorAttribut*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'Score'",NULL);
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->Score = ((int)  tolua_tonumber(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: Level of class  ActorAttribut */
#ifndef TOLUA_DISABLE_tolua_get_ActorAttribut_Level
static int tolua_get_ActorAttribut_Level(lua_State* tolua_S)
{
  ActorAttribut* self = (ActorAttribut*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'Level'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushnumber(tolua_S,(lua_Number)self->Level);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: Level of class  ActorAttribut */
#ifndef TOLUA_DISABLE_tolua_set_ActorAttribut_Level
static int tolua_set_ActorAttribut_Level(lua_State* tolua_S)
{
  ActorAttribut* self = (ActorAttribut*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'Level'",NULL);
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->Level = ((int)  tolua_tonumber(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: LevelModeExp of class  ActorAttribut */
#ifndef TOLUA_DISABLE_tolua_get_ActorAttribut_LevelModeExp
static int tolua_get_ActorAttribut_LevelModeExp(lua_State* tolua_S)
{
  ActorAttribut* self = (ActorAttribut*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'LevelModeExp'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushnumber(tolua_S,(lua_Number)self->LevelModeExp);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: LevelModeExp of class  ActorAttribut */
#ifndef TOLUA_DISABLE_tolua_set_ActorAttribut_LevelModeExp
static int tolua_set_ActorAttribut_LevelModeExp(lua_State* tolua_S)
{
  ActorAttribut* self = (ActorAttribut*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'LevelModeExp'",NULL);
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->LevelModeExp = ((int)  tolua_tonumber(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: LevelModeLevel of class  ActorAttribut */
#ifndef TOLUA_DISABLE_tolua_get_ActorAttribut_LevelModeLevel
static int tolua_get_ActorAttribut_LevelModeLevel(lua_State* tolua_S)
{
  ActorAttribut* self = (ActorAttribut*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'LevelModeLevel'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushnumber(tolua_S,(lua_Number)self->LevelModeLevel);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: LevelModeLevel of class  ActorAttribut */
#ifndef TOLUA_DISABLE_tolua_set_ActorAttribut_LevelModeLevel
static int tolua_set_ActorAttribut_LevelModeLevel(lua_State* tolua_S)
{
  ActorAttribut* self = (ActorAttribut*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'LevelModeLevel'",NULL);
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->LevelModeLevel = ((int)  tolua_tonumber(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: Strength of class  ActorAttribut */
#ifndef TOLUA_DISABLE_tolua_get_ActorAttribut_Strength
static int tolua_get_ActorAttribut_Strength(lua_State* tolua_S)
{
  ActorAttribut* self = (ActorAttribut*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'Strength'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushnumber(tolua_S,(lua_Number)self->Strength);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: Strength of class  ActorAttribut */
#ifndef TOLUA_DISABLE_tolua_set_ActorAttribut_Strength
static int tolua_set_ActorAttribut_Strength(lua_State* tolua_S)
{
  ActorAttribut* self = (ActorAttribut*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'Strength'",NULL);
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->Strength = ((float)  tolua_tonumber(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: MaxStrength of class  ActorAttribut */
#ifndef TOLUA_DISABLE_tolua_get_ActorAttribut_MaxStrength
static int tolua_get_ActorAttribut_MaxStrength(lua_State* tolua_S)
{
  ActorAttribut* self = (ActorAttribut*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'MaxStrength'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushnumber(tolua_S,(lua_Number)self->MaxStrength);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: MaxStrength of class  ActorAttribut */
#ifndef TOLUA_DISABLE_tolua_set_ActorAttribut_MaxStrength
static int tolua_set_ActorAttribut_MaxStrength(lua_State* tolua_S)
{
  ActorAttribut* self = (ActorAttribut*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'MaxStrength'",NULL);
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->MaxStrength = ((float)  tolua_tonumber(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: StrengthRecover of class  ActorAttribut */
#ifndef TOLUA_DISABLE_tolua_get_ActorAttribut_StrengthRecover
static int tolua_get_ActorAttribut_StrengthRecover(lua_State* tolua_S)
{
  ActorAttribut* self = (ActorAttribut*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'StrengthRecover'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushnumber(tolua_S,(lua_Number)self->StrengthRecover);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: StrengthRecover of class  ActorAttribut */
#ifndef TOLUA_DISABLE_tolua_set_ActorAttribut_StrengthRecover
static int tolua_set_ActorAttribut_StrengthRecover(lua_State* tolua_S)
{
  ActorAttribut* self = (ActorAttribut*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'StrengthRecover'",NULL);
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->StrengthRecover = ((float)  tolua_tonumber(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: ExtraHP of class  ActorAttribut */
#ifndef TOLUA_DISABLE_tolua_get_ActorAttribut_ExtraHP
static int tolua_get_ActorAttribut_ExtraHP(lua_State* tolua_S)
{
  ActorAttribut* self = (ActorAttribut*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'ExtraHP'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushnumber(tolua_S,(lua_Number)self->ExtraHP);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: ExtraHP of class  ActorAttribut */
#ifndef TOLUA_DISABLE_tolua_set_ActorAttribut_ExtraHP
static int tolua_set_ActorAttribut_ExtraHP(lua_State* tolua_S)
{
  ActorAttribut* self = (ActorAttribut*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'ExtraHP'",NULL);
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->ExtraHP = ((float)  tolua_tonumber(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: Armor of class  ActorAttribut */
#ifndef TOLUA_DISABLE_tolua_get_ActorAttribut_Armor
static int tolua_get_ActorAttribut_Armor(lua_State* tolua_S)
{
  ActorAttribut* self = (ActorAttribut*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'Armor'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushnumber(tolua_S,(lua_Number)self->Armor);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: Armor of class  ActorAttribut */
#ifndef TOLUA_DISABLE_tolua_set_ActorAttribut_Armor
static int tolua_set_ActorAttribut_Armor(lua_State* tolua_S)
{
  ActorAttribut* self = (ActorAttribut*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'Armor'",NULL);
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->Armor = ((float)  tolua_tonumber(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: effect_name of class  BodyEffectBrief */
#ifndef TOLUA_DISABLE_tolua_get_BodyEffectBrief_effect_name
static int tolua_get_BodyEffectBrief_effect_name(lua_State* tolua_S)
{
  BodyEffectBrief* self = (BodyEffectBrief*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'effect_name'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushcppstring(tolua_S,(const char*)self->effect_name);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: effect_name of class  BodyEffectBrief */
#ifndef TOLUA_DISABLE_tolua_set_BodyEffectBrief_effect_name
static int tolua_set_BodyEffectBrief_effect_name(lua_State* tolua_S)
{
  BodyEffectBrief* self = (BodyEffectBrief*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'effect_name'",NULL);
  if (!tolua_iscppstring(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->effect_name = ((std::string)  tolua_tocppstring(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: effect_id of class  BodyEffectBrief */
#ifndef TOLUA_DISABLE_tolua_get_BodyEffectBrief_effect_id
static int tolua_get_BodyEffectBrief_effect_id(lua_State* tolua_S)
{
  BodyEffectBrief* self = (BodyEffectBrief*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'effect_id'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushnumber(tolua_S,(lua_Number)self->effect_id);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: effect_id of class  BodyEffectBrief */
#ifndef TOLUA_DISABLE_tolua_set_BodyEffectBrief_effect_id
static int tolua_set_BodyEffectBrief_effect_id(lua_State* tolua_S)
{
  BodyEffectBrief* self = (BodyEffectBrief*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'effect_id'",NULL);
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->effect_id = ((int)  tolua_tonumber(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: effect_scale of class  BodyEffectBrief */
#ifndef TOLUA_DISABLE_tolua_get_BodyEffectBrief_effect_scale
static int tolua_get_BodyEffectBrief_effect_scale(lua_State* tolua_S)
{
  BodyEffectBrief* self = (BodyEffectBrief*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'effect_scale'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushnumber(tolua_S,(lua_Number)self->effect_scale);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: effect_scale of class  BodyEffectBrief */
#ifndef TOLUA_DISABLE_tolua_set_BodyEffectBrief_effect_scale
static int tolua_set_BodyEffectBrief_effect_scale(lua_State* tolua_S)
{
  BodyEffectBrief* self = (BodyEffectBrief*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'effect_scale'",NULL);
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->effect_scale = ((float)  tolua_tonumber(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* get function: effect_looptime of class  BodyEffectBrief */
#ifndef TOLUA_DISABLE_tolua_get_BodyEffectBrief_effect_looptime
static int tolua_get_BodyEffectBrief_effect_looptime(lua_State* tolua_S)
{
  BodyEffectBrief* self = (BodyEffectBrief*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'effect_looptime'",NULL);
#else 
  if (!self) return 0;
#endif
  tolua_pushnumber(tolua_S,(lua_Number)self->effect_looptime);
 return 1;
}
#endif //#ifndef TOLUA_DISABLE

/* set function: effect_looptime of class  BodyEffectBrief */
#ifndef TOLUA_DISABLE_tolua_set_BodyEffectBrief_effect_looptime
static int tolua_set_BodyEffectBrief_effect_looptime(lua_State* tolua_S)
{
  BodyEffectBrief* self = (BodyEffectBrief*)  tolua_tousertype(tolua_S,1,0);
#ifndef TOLUA_RELEASE
  tolua_Error tolua_err;
  if (!self) tolua_error(tolua_S,"invalid 'self' in accessing variable 'effect_looptime'",NULL);
  if (!tolua_isnumber(tolua_S,2,0,&tolua_err))
   tolua_error(tolua_S,"#vinvalid type in variable assignment.",&tolua_err);
#endif
  self->effect_looptime = ((float)  tolua_tonumber(tolua_S,2,0))
;
 return 0;
}
#endif //#ifndef TOLUA_DISABLE

/* Open function */
TOLUA_API int tolua_miniSandboxEngineActor_open (lua_State* tolua_S)
{
 tolua_open(tolua_S);
 tolua_reg_types(tolua_S);
 tolua_module(tolua_S,NULL,0);
 tolua_beginmodule(tolua_S,NULL);
  tolua_constant(tolua_S,"MOB_HOSTILE",MOB_HOSTILE);
  tolua_constant(tolua_S,"MOB_PASSIVE",MOB_PASSIVE);
  tolua_constant(tolua_S,"MOB_RARE",MOB_RARE);
  tolua_constant(tolua_S,"MOB_WATER",MOB_WATER);
  tolua_constant(tolua_S,"MOB_MOBILETOOL",MOB_MOBILETOOL);
  tolua_constant(tolua_S,"MOB_VILLAGER",MOB_VILLAGER);
  tolua_constant(tolua_S,"MOB_BOSS",MOB_BOSS);
  tolua_constant(tolua_S,"MOB_NPC",MOB_NPC);
  tolua_constant(tolua_S,"MOB_FLY",MOB_FLY);
  tolua_constant(tolua_S,"MOB_TRIXENIE",MOB_TRIXENIE);
  tolua_constant(tolua_S,"MAX_MOBTYPE",MAX_MOBTYPE);
  tolua_constant(tolua_S,"EQUIP_NONE",EQUIP_NONE);
  tolua_constant(tolua_S,"EQUIP_HEAD",EQUIP_HEAD);
  tolua_constant(tolua_S,"EQUIP_BREAST",EQUIP_BREAST);
  tolua_constant(tolua_S,"EQUIP_LEGGING",EQUIP_LEGGING);
  tolua_constant(tolua_S,"EQUIP_SHOE",EQUIP_SHOE);
  tolua_constant(tolua_S,"EQUIP_HEAD_LINING",EQUIP_HEAD_LINING);
  tolua_constant(tolua_S,"EQUIP_BREAST_LINING",EQUIP_BREAST_LINING);
  tolua_constant(tolua_S,"EQUIP_LEGGING_LINING",EQUIP_LEGGING_LINING);
  tolua_constant(tolua_S,"EQUIP_SHOE_LINING",EQUIP_SHOE_LINING);
  tolua_constant(tolua_S,"EQUIP_PIFENG",EQUIP_PIFENG);
  tolua_constant(tolua_S,"EQUIP_WEAPON",EQUIP_WEAPON);
  tolua_constant(tolua_S,"EQUIP_GROUP",EQUIP_GROUP);
  tolua_constant(tolua_S,"MAX_EQUIP_SLOTS",MAX_EQUIP_SLOTS);
  tolua_constant(tolua_S,"EQUIP_BACK_INDEX_NONE",EQUIP_BACK_INDEX_NONE);
  tolua_constant(tolua_S,"EQUIP_BACK_INDEX_0",EQUIP_BACK_INDEX_0);
  tolua_constant(tolua_S,"EQUIP_BACK_INDEX_1",EQUIP_BACK_INDEX_1);
  tolua_constant(tolua_S,"EQUIP_BACK_INDEX_2",EQUIP_BACK_INDEX_2);
  tolua_constant(tolua_S,"EQUIP_BACK_INDEX_3",EQUIP_BACK_INDEX_3);
  tolua_constant(tolua_S,"EQUIP_BACK_INDEX_4",EQUIP_BACK_INDEX_4);
  tolua_constant(tolua_S,"EQUIP_BACK_INDEX_5",EQUIP_BACK_INDEX_5);
  tolua_constant(tolua_S,"EQUIP_BACK_INDEX_6",EQUIP_BACK_INDEX_6);
  tolua_constant(tolua_S,"EQUIP_BACK_INDEX_7",EQUIP_BACK_INDEX_7);
  tolua_constant(tolua_S,"EQUIP_BACK_INDEXMAX",EQUIP_BACK_INDEXMAX);
  tolua_constant(tolua_S,"ATTACK_ALL",ATTACK_ALL);
  tolua_constant(tolua_S,"ATTACK_PUNCH",ATTACK_PUNCH);
  tolua_constant(tolua_S,"ATTACK_RANGE",ATTACK_RANGE);
  tolua_constant(tolua_S,"ATTACK_EXPLODE",ATTACK_EXPLODE);
  tolua_constant(tolua_S,"MAX_PHYSICS_ATTACK",MAX_PHYSICS_ATTACK);
  tolua_constant(tolua_S,"ATTACK_FIRE",ATTACK_FIRE);
  tolua_constant(tolua_S,"ATTACK_RADIATION",ATTACK_RADIATION);
  tolua_constant(tolua_S,"ATTACK_TEMPERATURE",ATTACK_TEMPERATURE);
  tolua_constant(tolua_S,"ATTACK_POISON",ATTACK_POISON);
  tolua_constant(tolua_S,"ATTACK_WITHER",ATTACK_WITHER);
  tolua_constant(tolua_S,"ATTACK_FLASH",ATTACK_FLASH);
  tolua_constant(tolua_S,"ATTACK_ICE",ATTACK_ICE);
  tolua_constant(tolua_S,"MAX_MAGIC_ATTACK",MAX_MAGIC_ATTACK);
  tolua_constant(tolua_S,"ATTACK_SUN",ATTACK_SUN);
  tolua_constant(tolua_S,"ATTACK_FALLING",ATTACK_FALLING);
  tolua_constant(tolua_S,"ATTACK_ANVIL",ATTACK_ANVIL);
  tolua_constant(tolua_S,"ATTACK_CACTUS",ATTACK_CACTUS);
  tolua_constant(tolua_S,"ATTACK_WALL",ATTACK_WALL);
  tolua_constant(tolua_S,"ATTACK_DROWN",ATTACK_DROWN);
  tolua_constant(tolua_S,"ATTACK_SUFFOCATE",ATTACK_SUFFOCATE);
  tolua_constant(tolua_S,"ATTACK_ANTIINJURY",ATTACK_ANTIINJURY);
  tolua_constant(tolua_S,"ATTACK_BLOCK_LASER",ATTACK_BLOCK_LASER);
  tolua_constant(tolua_S,"ATTACK_FIXED",ATTACK_FIXED);
  tolua_constant(tolua_S,"PHYSICS_ATTACK",PHYSICS_ATTACK);
  tolua_constant(tolua_S,"TRUE_DAMAGE",TRUE_DAMAGE);
  tolua_constant(tolua_S,"ATTACK_THORNBALL",ATTACK_THORNBALL);
  tolua_constant(tolua_S,"ANIMAL_KILL",ANIMAL_KILL);
  tolua_constant(tolua_S,"RADIATION_KILL",RADIATION_KILL);
  tolua_constant(tolua_S,"HUNGER_KILL",HUNGER_KILL);
  tolua_constant(tolua_S,"THIRSTY_KILL",THIRSTY_KILL);
  tolua_constant(tolua_S,"HEAD_KILL",HEAD_KILL);
  tolua_constant(tolua_S,"FOOD_KILL",FOOD_KILL);
  tolua_constant(tolua_S,"THIS_KILL",THIS_KILL);
  tolua_constant(tolua_S,"SOCFIRE_KILL",SOCFIRE_KILL);
  tolua_constant(tolua_S,"SOCICE_KILL",SOCICE_KILL);
  tolua_constant(tolua_S,"SOCFLASH_KILL",SOCFLASH_KILL);
  tolua_constant(tolua_S,"ATTACK_TARGET_ALL",ATTACK_TARGET_ALL);
  tolua_constant(tolua_S,"ATTACK_TARGET_PLAYER",ATTACK_TARGET_PLAYER);
  tolua_constant(tolua_S,"ATTACK_TARGET_ANIMAL",ATTACK_TARGET_ANIMAL);
  tolua_constant(tolua_S,"ATTACK_TARGET_SAVAGE",ATTACK_TARGET_SAVAGE);
  tolua_constant(tolua_S,"ATTACK_TARGET_UNDEAD",ATTACK_TARGET_UNDEAD);
  tolua_constant(tolua_S,"ATTACK_TARGET_ANCIENT",ATTACK_TARGET_ANCIENT);
  tolua_constant(tolua_S,"ATTACK_TARGET_MECHANICAL",ATTACK_TARGET_MECHANICAL);
  tolua_constant(tolua_S,"ATTACK_TARGET_ALIEN",ATTACK_TARGET_ALIEN);
  tolua_constant(tolua_S,"ATTACK_TARGET_AQUATIC",ATTACK_TARGET_AQUATIC);
  tolua_constant(tolua_S,"ATTACK_TARGET_FLYING",ATTACK_TARGET_FLYING);
  tolua_constant(tolua_S,"ATTACK_TARGET_ZOMBIE",ATTACK_TARGET_ZOMBIE);
  tolua_constant(tolua_S,"ATTACK_TARGET_OTHERS",ATTACK_TARGET_OTHERS);
  tolua_constant(tolua_S,"ENCHANT_NULL",ENCHANT_NULL);
  tolua_constant(tolua_S,"ENCHANT_SHARP",ENCHANT_SHARP);
  tolua_constant(tolua_S,"ENCHANT_KNOCK",ENCHANT_KNOCK);
  tolua_constant(tolua_S,"ENCHANT_KNOCKUP",ENCHANT_KNOCKUP);
  tolua_constant(tolua_S,"ENCHANT_ROB",ENCHANT_ROB);
  tolua_constant(tolua_S,"ENCHANT_TARGET_FIRE",ENCHANT_TARGET_FIRE);
  tolua_constant(tolua_S,"ENCHANT_MULTIATTACK",ENCHANT_MULTIATTACK);
  tolua_constant(tolua_S,"ENCHANT_VAMPIRE",ENCHANT_VAMPIRE);
  tolua_constant(tolua_S,"ENCHANT_DURABLE",ENCHANT_DURABLE);
  tolua_constant(tolua_S,"ENCHANT_PROTECTION",ENCHANT_PROTECTION);
  tolua_constant(tolua_S,"ENCHANT_THORNS",ENCHANT_THORNS);
  tolua_constant(tolua_S,"ENCHANT_ATTACKER_BUFF",ENCHANT_ATTACKER_BUFF);
  tolua_constant(tolua_S,"ENCHANT_KNOCK_RESIST",ENCHANT_KNOCK_RESIST);
  tolua_constant(tolua_S,"ENCHANT_KNOCK_ATTACKER",ENCHANT_KNOCK_ATTACKER);
  tolua_constant(tolua_S,"ENCHANT_BOW_ATTACK",ENCHANT_BOW_ATTACK);
  tolua_constant(tolua_S,"ENCHANT_ARROW_FREE",ENCHANT_ARROW_FREE);
  tolua_constant(tolua_S,"ENCHANT_DIG_SPEED",ENCHANT_DIG_SPEED);
  tolua_constant(tolua_S,"ENCHANT_DIG_PROB",ENCHANT_DIG_PROB);
  tolua_constant(tolua_S,"ENCHANT_DIG_MAXNUM",ENCHANT_DIG_MAXNUM);
  tolua_constant(tolua_S,"ENCHANT_DIG_PRECISE",ENCHANT_DIG_PRECISE);
  tolua_constant(tolua_S,"ENCHANT_ARROW_EXPLODE",ENCHANT_ARROW_EXPLODE);
  tolua_constant(tolua_S,"ENCHANT_FALL_PROTECT",ENCHANT_FALL_PROTECT);
  tolua_constant(tolua_S,"ENCHANT_FALL_SPEED",ENCHANT_FALL_SPEED);
  tolua_constant(tolua_S,"ENCHANT_CLIMB",ENCHANT_CLIMB);
  tolua_constant(tolua_S,"ENCHANT_TARGET_BLEED",ENCHANT_TARGET_BLEED);
  tolua_constant(tolua_S,"ENCHANT_LIGHTNING_CHAIN",ENCHANT_LIGHTNING_CHAIN);
  tolua_constant(tolua_S,"ENCHANT_FISH",ENCHANT_FISH);
  tolua_constant(tolua_S,"ENCHANT_DURABLE_PROTECT",ENCHANT_DURABLE_PROTECT);
  tolua_constant(tolua_S,"ENCHANT_TEMPDEF",ENCHANT_TEMPDEF);
  tolua_constant(tolua_S,"ENCHANT_TARGET_ICE",ENCHANT_TARGET_ICE);
  tolua_constant(tolua_S,"ENCHANT_PUNCH_INC",ENCHANT_PUNCH_INC);
  tolua_constant(tolua_S,"ENCHANT_DAMAGE_DEC",ENCHANT_DAMAGE_DEC);
  tolua_constant(tolua_S,"ENCHANT_RANGE_DAMAGE_DEC",ENCHANT_RANGE_DAMAGE_DEC);
  tolua_constant(tolua_S,"ENCHANT_EXPLODE_DEC",ENCHANT_EXPLODE_DEC);
  tolua_constant(tolua_S,"ENCHANT_TOUGH_REC",ENCHANT_TOUGH_REC);
  tolua_constant(tolua_S,"ENCHANT_JUMP_INC",ENCHANT_JUMP_INC);
  tolua_constant(tolua_S,"ENCHANT_TRANSFORM1",ENCHANT_TRANSFORM1);
  tolua_constant(tolua_S,"ENCHANT_DURABLE_PROB",ENCHANT_DURABLE_PROB);
  tolua_constant(tolua_S,"ENCHANT_FISHING_TIME_DEC",ENCHANT_FISHING_TIME_DEC);
  tolua_constant(tolua_S,"ENCHANT_SLOWDOWN_PROB",ENCHANT_SLOWDOWN_PROB);
  tolua_constant(tolua_S,"ENCHANT_DIGGING_STRENGTH_DEC",ENCHANT_DIGGING_STRENGTH_DEC);
  tolua_constant(tolua_S,"ENCHANT_FALL_DAMAGE",ENCHANT_FALL_DAMAGE);
  tolua_constant(tolua_S,"ENCHANT_STAR_INC",ENCHANT_STAR_INC);
  tolua_constant(tolua_S,"ENCHANT_WEAPON_SKILL_CD_DEC",ENCHANT_WEAPON_SKILL_CD_DEC);
  tolua_constant(tolua_S,"ENCHANT_COMBO_LAST_INC",ENCHANT_COMBO_LAST_INC);
  tolua_constant(tolua_S,"ENCHANT_REPEL_RES",ENCHANT_REPEL_RES);
  tolua_constant(tolua_S,"ENCHANT_MONSTER_DAMAGE",ENCHANT_MONSTER_DAMAGE);
  tolua_constant(tolua_S,"ENCHANT_ADDBUFF",ENCHANT_ADDBUFF);
  tolua_constant(tolua_S,"MAX_ENCHANT_TYPE",MAX_ENCHANT_TYPE);
  tolua_constant(tolua_S,"MODATTR_MOVE_SPEED",MODATTR_MOVE_SPEED);
  tolua_constant(tolua_S,"MODATTR_SWIM_SPEED",MODATTR_SWIM_SPEED);
  tolua_constant(tolua_S,"MODATTR_JUMP_SPEED",MODATTR_JUMP_SPEED);
  tolua_constant(tolua_S,"MODATTR_ATTACK_PUNCH",MODATTR_ATTACK_PUNCH);
  tolua_constant(tolua_S,"MODATTR_ATTACK_RANGE",MODATTR_ATTACK_RANGE);
  tolua_constant(tolua_S,"MODATTR_ATTACK_EXPLODE",MODATTR_ATTACK_EXPLODE);
  tolua_constant(tolua_S,"MODATTR_ATTACK_FIRE",MODATTR_ATTACK_FIRE);
  tolua_constant(tolua_S,"MODATTR_ATTACK_POISON",MODATTR_ATTACK_POISON);
  tolua_constant(tolua_S,"MODATTR_ATTACK_WITHER",MODATTR_ATTACK_WITHER);
  tolua_constant(tolua_S,"MODATTR_ATTACK_PLAYER",MODATTR_ATTACK_PLAYER);
  tolua_constant(tolua_S,"MODATTR_ATTACK_UNDEAD",MODATTR_ATTACK_UNDEAD);
  tolua_constant(tolua_S,"MODATTR_ATTACK_ANIMAL",MODATTR_ATTACK_ANIMAL);
  tolua_constant(tolua_S,"MODATTR_ATTACK_ICE",MODATTR_ATTACK_ICE);
  tolua_constant(tolua_S,"MODATTR_DAMAGED_PUNCH",MODATTR_DAMAGED_PUNCH);
  tolua_constant(tolua_S,"MODATTR_DAMAGED_RANGE",MODATTR_DAMAGED_RANGE);
  tolua_constant(tolua_S,"MODATTR_DAMAGED_EXPLODE",MODATTR_DAMAGED_EXPLODE);
  tolua_constant(tolua_S,"MODATTR_DAMAGED_FIRE",MODATTR_DAMAGED_FIRE);
  tolua_constant(tolua_S,"MODATTR_DAMAGED_POISON",MODATTR_DAMAGED_POISON);
  tolua_constant(tolua_S,"MODATTR_DAMAGED_WITHER",MODATTR_DAMAGED_WITHER);
  tolua_constant(tolua_S,"MODATTR_DAMAGED_FALLING",MODATTR_DAMAGED_FALLING);
  tolua_constant(tolua_S,"MODATTR_ARMOR_PUNCH",MODATTR_ARMOR_PUNCH);
  tolua_constant(tolua_S,"MODATTR_ARMOR_RANGE",MODATTR_ARMOR_RANGE);
  tolua_constant(tolua_S,"MODATTR_ARMOR_EXPLODE",MODATTR_ARMOR_EXPLODE);
  tolua_constant(tolua_S,"MODATTR_DAMAGE_ABSORB",MODATTR_DAMAGE_ABSORB);
  tolua_constant(tolua_S,"MODATTR_CRITICAL_HIT",MODATTR_CRITICAL_HIT);
  tolua_constant(tolua_S,"MODATTR_KNOCK",MODATTR_KNOCK);
  tolua_constant(tolua_S,"MODATTR_KNOCK_RESIST",MODATTR_KNOCK_RESIST);
  tolua_constant(tolua_S,"MODATTR_KNOCK_RESIST_PROB",MODATTR_KNOCK_RESIST_PROB);
  tolua_constant(tolua_S,"MODATTR_ACTOR_SCALE",MODATTR_ACTOR_SCALE);
  tolua_constant(tolua_S,"MAX_MOB_MODATTR",MAX_MOB_MODATTR);
  tolua_constant(tolua_S,"MODATTR_DIG_SPEED",MODATTR_DIG_SPEED);
  tolua_constant(tolua_S,"MODATTR_LUCK_DIG",MODATTR_LUCK_DIG);
  tolua_constant(tolua_S,"MODATTR_LUCK_KILLMOB",MODATTR_LUCK_KILLMOB);
  tolua_constant(tolua_S,"MODATTR_VIEW_BRIGHT",MODATTR_VIEW_BRIGHT);
  tolua_constant(tolua_S,"MODATTR_OXYGEN_SUPPLY",MODATTR_OXYGEN_SUPPLY);
  tolua_constant(tolua_S,"MOBATTR_DAMAGED_ZOMBIE",MOBATTR_DAMAGED_ZOMBIE);
  tolua_constant(tolua_S,"MOBATTR_ATTACK_GUN",MOBATTR_ATTACK_GUN);
  tolua_constant(tolua_S,"MAX_PLAYER_MODATTR",MAX_PLAYER_MODATTR);
  tolua_constant(tolua_S,"MAX_MOD_ATTRIB",MAX_MOD_ATTRIB);
  tolua_constant(tolua_S,"STAMINA_WALK",STAMINA_WALK);
  tolua_constant(tolua_S,"STAMINA_SPRINT",STAMINA_SPRINT);
  tolua_constant(tolua_S,"STAMINA_SWIM",STAMINA_SWIM);
  tolua_constant(tolua_S,"STAMINA_JUMP",STAMINA_JUMP);
  tolua_constant(tolua_S,"STAMINA_SPRINTJUMP",STAMINA_SPRINTJUMP);
  tolua_constant(tolua_S,"STAMINA_DESTROYBLOCK",STAMINA_DESTROYBLOCK);
  tolua_constant(tolua_S,"STAMINA_ATTACK",STAMINA_ATTACK);
  tolua_constant(tolua_S,"STAMINA_HURT",STAMINA_HURT);
  tolua_constant(tolua_S,"STAMINA_ADDLIFE",STAMINA_ADDLIFE);
  tolua_constant(tolua_S,"MAX_STAMINA_METHOD",MAX_STAMINA_METHOD);
  tolua_constant(tolua_S,"GENIUS_NONE",GENIUS_NONE);
  tolua_constant(tolua_S,"GENIUS_BASEATK_INC",GENIUS_BASEATK_INC);
  tolua_constant(tolua_S,"GENIUS_PUNCHHURT_INC",GENIUS_PUNCHHURT_INC);
  tolua_constant(tolua_S,"GENIUS_RANGEHURT_INC",GENIUS_RANGEHURT_INC);
  tolua_constant(tolua_S,"GENIUS_EXPLODEHURT_INC",GENIUS_EXPLODEHURT_INC);
  tolua_constant(tolua_S,"GENIUS_PHYSICSHURT_INC",GENIUS_PHYSICSHURT_INC);
  tolua_constant(tolua_S,"GENIUS_FIREHURT_INC",GENIUS_FIREHURT_INC);
  tolua_constant(tolua_S,"GENIUS_POISONHURT_INC",GENIUS_POISONHURT_INC);
  tolua_constant(tolua_S,"GENIUS_WITHERHURT_INC",GENIUS_WITHERHURT_INC);
  tolua_constant(tolua_S,"GENIUS_MAGICHURT_INC",GENIUS_MAGICHURT_INC);
  tolua_constant(tolua_S,"GENIUS_PUNCHHURT_DEC",GENIUS_PUNCHHURT_DEC);
  tolua_constant(tolua_S,"GENIUS_RANGEHURT_DEC",GENIUS_RANGEHURT_DEC);
  tolua_constant(tolua_S,"GENIUS_EXPLODEHURT_DEC",GENIUS_EXPLODEHURT_DEC);
  tolua_constant(tolua_S,"GENIUS_PHYSICSHURT_DEC",GENIUS_PHYSICSHURT_DEC);
  tolua_constant(tolua_S,"GENIUS_FIREHURT_DEC",GENIUS_FIREHURT_DEC);
  tolua_constant(tolua_S,"GENIUS_POISONHURT_DEC",GENIUS_POISONHURT_DEC);
  tolua_constant(tolua_S,"GENIUS_WITHERHURT_DEC",GENIUS_WITHERHURT_DEC);
  tolua_constant(tolua_S,"GENIUS_MAGICHURT_DEC",GENIUS_MAGICHURT_DEC);
  tolua_constant(tolua_S,"GENIUS_ROB_MOBDROP",GENIUS_ROB_MOBDROP);
  tolua_constant(tolua_S,"GENIUS_DIGSPEED_INC",GENIUS_DIGSPEED_INC);
  tolua_constant(tolua_S,"GENIUS_MOVESPEED_INC",GENIUS_MOVESPEED_INC);
  tolua_constant(tolua_S,"GENIUS_TOOLDUR",GENIUS_TOOLDUR);
  tolua_constant(tolua_S,"GENIUS_ARCHEOLOGY",GENIUS_ARCHEOLOGY);
  tolua_constant(tolua_S,"GENIUS_SURVIVE",GENIUS_SURVIVE);
  tolua_constant(tolua_S,"GENIUS_SEARCH_JAR",GENIUS_SEARCH_JAR);
  tolua_constant(tolua_S,"GENIUS_FOREBODE",GENIUS_FOREBODE);
  tolua_constant(tolua_S,"GENIUS_TWOJUMP",GENIUS_TWOJUMP);
  tolua_constant(tolua_S,"BODYFX_HURT",BODYFX_HURT);
  tolua_constant(tolua_S,"BODYFX_FIRE",BODYFX_FIRE);
  tolua_constant(tolua_S,"BODYFX_PORTAL",BODYFX_PORTAL);
  tolua_constant(tolua_S,"BODYFX_ACCUMFIRE",BODYFX_ACCUMFIRE);
  tolua_constant(tolua_S,"BODYFX_DRAGONFIRE",BODYFX_DRAGONFIRE);
  tolua_constant(tolua_S,"BODYFX_DRAGONSUMMON",BODYFX_DRAGONSUMMON);
  tolua_constant(tolua_S,"BODYFX_TAME_SUCCEED",BODYFX_TAME_SUCCEED);
  tolua_constant(tolua_S,"BODYFX_TAME_FAILED",BODYFX_TAME_FAILED);
  tolua_constant(tolua_S,"BODYFX_TAME_FOOD",BODYFX_TAME_FOOD);
  tolua_constant(tolua_S,"BODYFX_TAME_NOFOOD",BODYFX_TAME_NOFOOD);
  tolua_constant(tolua_S,"BODYFX_AI_NEEDREEDS",BODYFX_AI_NEEDREEDS);
  tolua_constant(tolua_S,"BODYFX_FEAR",BODYFX_FEAR);
  tolua_constant(tolua_S,"BODYFX_ROLECOLLECT",BODYFX_ROLECOLLECT);
  tolua_constant(tolua_S,"BODYFX_ROLEJUMP",BODYFX_ROLEJUMP);
  tolua_constant(tolua_S,"BODYFX_DEADPROTECT",BODYFX_DEADPROTECT);
  tolua_constant(tolua_S,"BODYFX_DRAGONDIE0",BODYFX_DRAGONDIE0);
  tolua_constant(tolua_S,"BODYFX_DRAGONDIE1",BODYFX_DRAGONDIE1);
  tolua_constant(tolua_S,"BODYFX_DRAGONDIE2",BODYFX_DRAGONDIE2);
  tolua_constant(tolua_S,"BODYFX_HORSE_FLY",BODYFX_HORSE_FLY);
  tolua_constant(tolua_S,"BODYFX_DISAPPEAR",BODYFX_DISAPPEAR);
  tolua_constant(tolua_S,"BODYFX_HORSE_BENTENG",BODYFX_HORSE_BENTENG);
  tolua_constant(tolua_S,"BODYFX_DANCE",BODYFX_DANCE);
  tolua_constant(tolua_S,"TOOLFX_JETPACK2",TOOLFX_JETPACK2);
  tolua_constant(tolua_S,"BODYFX_INTERACTION",BODYFX_INTERACTION);
  tolua_constant(tolua_S,"HUDFX_HEADSHOT",HUDFX_HEADSHOT);
  tolua_constant(tolua_S,"HUDFX_NORMALSHOT",HUDFX_NORMALSHOT);
  tolua_constant(tolua_S,"HUDFX_VEHICLESHOT",HUDFX_VEHICLESHOT);
  tolua_constant(tolua_S,"BODYFX_MILKING",BODYFX_MILKING);
  tolua_constant(tolua_S,"BODYFX_AI_ANGRY",BODYFX_AI_ANGRY);
  tolua_constant(tolua_S,"BODYFX_AI_SLEEP",BODYFX_AI_SLEEP);
  tolua_constant(tolua_S,"BODYFX_TRANSPORT",BODYFX_TRANSPORT);
  tolua_constant(tolua_S,"BODYFX_FORBIDDEN",BODYFX_FORBIDDEN);
  tolua_constant(tolua_S,"BODYFX_CONCEAL",BODYFX_CONCEAL);
  tolua_constant(tolua_S,"BODYFX_WEAPON_FIRE",BODYFX_WEAPON_FIRE);
  tolua_constant(tolua_S,"BODYFX_DIZZY",BODYFX_DIZZY);
  tolua_constant(tolua_S,"BODYFX_MAKETROUBLE",BODYFX_MAKETROUBLE);
  tolua_constant(tolua_S,"BODYFX_TRAINMOVE",BODYFX_TRAINMOVE);
  tolua_constant(tolua_S,"BODYFX_AI_HUNGRY",BODYFX_AI_HUNGRY);
  tolua_constant(tolua_S,"BODYFX_BALL_CHARGE",BODYFX_BALL_CHARGE);
  tolua_constant(tolua_S,"BODYFX_BALL_SHOOT_RELEASE",BODYFX_BALL_SHOOT_RELEASE);
  tolua_constant(tolua_S,"BODYFX_ENCH_FALL",BODYFX_ENCH_FALL);
  tolua_constant(tolua_S,"BASKETBALL_OBSTRUCT",BASKETBALL_OBSTRUCT);
  tolua_constant(tolua_S,"BODYFX_BASKETBALL_DRIBBLERUSH",BODYFX_BASKETBALL_DRIBBLERUSH);
  tolua_constant(tolua_S,"BODYFX_BASKETBALL_CHARGE",BODYFX_BASKETBALL_CHARGE);
  tolua_constant(tolua_S,"BASKETBALL_GRAB",BASKETBALL_GRAB);
  tolua_constant(tolua_S,"BODYFX_AI_STAND_SLEEP",BODYFX_AI_STAND_SLEEP);
  tolua_constant(tolua_S,"BODYFX_BOT_SUSPEND",BODYFX_BOT_SUSPEND);
  tolua_constant(tolua_S,"BODYFX_BOT_PROJECT",BODYFX_BOT_PROJECT);
  tolua_constant(tolua_S,"BODYFX_BOT_FISSURE",BODYFX_BOT_FISSURE);
  tolua_constant(tolua_S,"BODYFX_BOT_FISSURE_FAST",BODYFX_BOT_FISSURE_FAST);
  tolua_constant(tolua_S,"BODYFX_BOT_HIGHLIGHT",BODYFX_BOT_HIGHLIGHT);
  tolua_constant(tolua_S,"BODYFX_BOT_TRAILING",BODYFX_BOT_TRAILING);
  tolua_constant(tolua_S,"BODYFX_SCORPION_CONCEAL",BODYFX_SCORPION_CONCEAL);
  tolua_constant(tolua_S,"BODYFX_MILKING_TOXIC",BODYFX_MILKING_TOXIC);
  tolua_constant(tolua_S,"HUDFX_DEADSHOT",HUDFX_DEADSHOT);
  tolua_constant(tolua_S,"BODYFX_EXPLODE_HURT",BODYFX_EXPLODE_HURT);
  tolua_constant(tolua_S,"BODYFX_MAX",BODYFX_MAX);
  tolua_constant(tolua_S,"SPECTATOR_MODE_NONE",SPECTATOR_MODE_NONE);
  tolua_constant(tolua_S,"SPECTATOR_MODE_FAILURE",SPECTATOR_MODE_FAILURE);
  tolua_constant(tolua_S,"SPECTATOR_MODE_JUDGE",SPECTATOR_MODE_JUDGE);
  tolua_constant(tolua_S,"SPECTATOR_TYPE_FREE",SPECTATOR_TYPE_FREE);
  tolua_constant(tolua_S,"SPECTATOR_TYPE_FOLLW",SPECTATOR_TYPE_FOLLW);
  tolua_constant(tolua_S,"SPECTATOR_TYPE_OUTCONTROL",SPECTATOR_TYPE_OUTCONTROL);
  tolua_constant(tolua_S,"ATTACK_BODY_HEAD",ATTACK_BODY_HEAD);
  tolua_constant(tolua_S,"ATTACK_BODY_UPBODY",ATTACK_BODY_UPBODY);
  tolua_constant(tolua_S,"ATTACK_BODY_DOWNBODY",ATTACK_BODY_DOWNBODY);
  tolua_constant(tolua_S,"ATTACK_BODY_MAX",ATTACK_BODY_MAX);
  tolua_module(tolua_S,"MNSandbox",0);
  tolua_beginmodule(tolua_S,"MNSandbox");
   tolua_function(tolua_S,"getAttackBodyType",tolua_miniSandboxEngineActor_MNSandbox_getAttackBodyType00);
  tolua_endmodule(tolua_S);
  #ifdef __cplusplus
  tolua_cclass(tolua_S,"OneAttackData","OneAttackData","",tolua_collect_OneAttackData);
#ifdef SANDBOX_BIND_LUATYPE
  SANDBOX_BIND_LUATYPE(OneAttackData, "OneAttackData", "OneAttackData", "")
#endif
  #else
  tolua_cclass(tolua_S,"OneAttackData","OneAttackData","",NULL);
#ifdef SANDBOX_BIND_LUATYPE
  SANDBOX_BIND_LUATYPE(OneAttackData, "OneAttackData", "OneAttackData", "")
#endif
  #endif
  tolua_beginmodule(tolua_S,"OneAttackData");
   tolua_variable(tolua_S,"atktype",tolua_get_OneAttackData_atktype,tolua_set_OneAttackData_atktype);
   tolua_variable(tolua_S,"parttype",tolua_get_OneAttackData_parttype,tolua_set_OneAttackData_parttype);
   tolua_variable(tolua_S,"atkpoints",tolua_get_OneAttackData_atkpoints,tolua_set_OneAttackData_atkpoints);
   tolua_variable(tolua_S,"enchant_atk",tolua_get_OneAttackData_enchant_atk,tolua_set_OneAttackData_enchant_atk);
   tolua_variable(tolua_S,"buff_atk",tolua_get_OneAttackData_buff_atk,tolua_set_OneAttackData_buff_atk);
   tolua_variable(tolua_S,"critical",tolua_get_OneAttackData_critical,tolua_set_OneAttackData_critical);
   tolua_variable(tolua_S,"damage_armor",tolua_get_OneAttackData_damage_armor,tolua_set_OneAttackData_damage_armor);
   tolua_variable(tolua_S,"ignore_resist",tolua_get_OneAttackData_ignore_resist,tolua_set_OneAttackData_ignore_resist);
   tolua_variable(tolua_S,"knockback",tolua_get_OneAttackData_knockback,tolua_set_OneAttackData_knockback);
   tolua_variable(tolua_S,"knockup",tolua_get_OneAttackData_knockup,tolua_set_OneAttackData_knockup);
   tolua_variable(tolua_S,"buffId",tolua_get_OneAttackData_buffId,tolua_set_OneAttackData_buffId);
   tolua_variable(tolua_S,"buffLevel",tolua_get_OneAttackData_buffLevel,tolua_set_OneAttackData_buffLevel);
   tolua_variable(tolua_S,"explodePos_x",tolua_get_OneAttackData_explodePos_x,tolua_set_OneAttackData_explodePos_x);
   tolua_variable(tolua_S,"explodePos_y",tolua_get_OneAttackData_explodePos_y,tolua_set_OneAttackData_explodePos_y);
   tolua_variable(tolua_S,"explodePos_z",tolua_get_OneAttackData_explodePos_z,tolua_set_OneAttackData_explodePos_z);
   tolua_variable(tolua_S,"explodeSize",tolua_get_OneAttackData_explodeSize,tolua_set_OneAttackData_explodeSize);
   tolua_variable(tolua_S,"fromplayer",tolua_get_OneAttackData_fromplayer_ptr,tolua_set_OneAttackData_fromplayer_ptr);
   tolua_variable(tolua_S,"isAttackHead",tolua_get_OneAttackData_isAttackHead,tolua_set_OneAttackData_isAttackHead);
   tolua_variable(tolua_S,"atkpos",tolua_get_OneAttackData_atkpos,tolua_set_OneAttackData_atkpos);
   tolua_variable(tolua_S,"triggerhit",tolua_get_OneAttackData_triggerhit,tolua_set_OneAttackData_triggerhit);
   tolua_variable(tolua_S,"touReduce",tolua_get_OneAttackData_touReduce,tolua_set_OneAttackData_touReduce);
   tolua_variable(tolua_S,"atkTypeNew",tolua_get_OneAttackData_atkTypeNew,tolua_set_OneAttackData_atkTypeNew);
   tolua_variable(tolua_S, "atkPointsNew", NULL, tolua_set_OneAttackData_atkPointsNew);
   tolua_array(tolua_S,"atkPointsNew",tolua_get_miniSandboxEngineActor_OneAttackData_atkPointsNew,tolua_set_miniSandboxEngineActor_OneAttackData_atkPointsNew);
   tolua_variable(tolua_S, "explodePoints", NULL, tolua_set_OneAttackData_explodePoints);
   tolua_array(tolua_S,"explodePoints",tolua_get_miniSandboxEngineActor_OneAttackData_explodePoints,tolua_set_miniSandboxEngineActor_OneAttackData_explodePoints);
   tolua_variable(tolua_S,"damping",tolua_get_OneAttackData_damping,tolua_set_OneAttackData_damping);
   tolua_variable(tolua_S,"charge",tolua_get_OneAttackData_charge,tolua_set_OneAttackData_charge);
   tolua_variable(tolua_S,"directAttacker",tolua_get_OneAttackData_directAttacker_ptr,tolua_set_OneAttackData_directAttacker_ptr);
   tolua_variable(tolua_S,"isFixAtkPoint",tolua_get_OneAttackData_isFixAtkPoint,tolua_set_OneAttackData_isFixAtkPoint);
   tolua_variable(tolua_S,"bowDamageIns",tolua_get_OneAttackData_bowDamageIns,tolua_set_OneAttackData_bowDamageIns);
   tolua_variable(tolua_S,"skillDamageIns",tolua_get_OneAttackData_skillDamageIns,tolua_set_OneAttackData_skillDamageIns);
   tolua_variable(tolua_S,"isNotInherit",tolua_get_OneAttackData_isNotInherit,tolua_set_OneAttackData_isNotInherit);
   tolua_variable(tolua_S,"damageFactor",tolua_get_OneAttackData_damageFactor,tolua_set_OneAttackData_damageFactor);
   tolua_variable(tolua_S,"ignoreTriggerEvent",tolua_get_OneAttackData_ignoreTriggerEvent,tolua_set_OneAttackData_ignoreTriggerEvent);
   tolua_function(tolua_S,"new",tolua_miniSandboxEngineActor_OneAttackData_new00);
   tolua_function(tolua_S,"new_local",tolua_miniSandboxEngineActor_OneAttackData_new00_local);
   tolua_function(tolua_S,".call",tolua_miniSandboxEngineActor_OneAttackData_new00_local);
  tolua_endmodule(tolua_S);
  tolua_constant(tolua_S,"GSOUND_DIG",GSOUND_DIG);
  tolua_constant(tolua_S,"GSOUND_DESTROY",GSOUND_DESTROY);
  tolua_constant(tolua_S,"GSOUND_PLACE",GSOUND_PLACE);
  tolua_constant(tolua_S,"GSOUND_FALLGROUND",GSOUND_FALLGROUND);
  tolua_constant(tolua_S,"GSOUND_WALK",GSOUND_WALK);
  tolua_constant(tolua_S,"MAX_GSOUND_TYPE",MAX_GSOUND_TYPE);
  tolua_constant(tolua_S,"STATUS_EFFECT_DIGGINGSPEED",STATUS_EFFECT_DIGGINGSPEED);
  tolua_constant(tolua_S,"STATUS_EFFECT_JUMP",STATUS_EFFECT_JUMP);
  tolua_constant(tolua_S,"STATUS_EFFECT_HUNGERLOSS",STATUS_EFFECT_HUNGERLOSS);
  tolua_constant(tolua_S,"STATUS_EFFECT_VIEWBRIGHTNESS",STATUS_EFFECT_VIEWBRIGHTNESS);
  tolua_constant(tolua_S,"STATUS_EFFECT_REPELSTRENGTHEN",STATUS_EFFECT_REPELSTRENGTHEN);
  tolua_constant(tolua_S,"STATUS_EFFECT_REPELRESIST",STATUS_EFFECT_REPELRESIST);
  tolua_constant(tolua_S,"STATUS_EFFECT_ABSORBDAMAGE",STATUS_EFFECT_ABSORBDAMAGE);
  tolua_constant(tolua_S,"STATUS_EFFECT_STRIKEFLY",STATUS_EFFECT_STRIKEFLY);
  tolua_constant(tolua_S,"STATUS_EFFECT_MODELVOLUME",STATUS_EFFECT_MODELVOLUME);
  tolua_constant(tolua_S,"STATUS_EFFECT_MODELCHANGE",STATUS_EFFECT_MODELCHANGE);
  tolua_constant(tolua_S,"STATUS_EFFECT_MOVEREVERSE",STATUS_EFFECT_MOVEREVERSE);
  tolua_constant(tolua_S,"STATUS_EFFECT_INVULNERABLE",STATUS_EFFECT_INVULNERABLE);
  tolua_constant(tolua_S,"STATUS_EFFECT_BUBBLE",STATUS_EFFECT_BUBBLE);
  tolua_constant(tolua_S,"STATUS_EFFECT_CANNOTMOVE",STATUS_EFFECT_CANNOTMOVE);
  tolua_constant(tolua_S,"STATUS_EFFECT_CLIMBWALL",STATUS_EFFECT_CLIMBWALL);
  tolua_constant(tolua_S,"STATUS_EFFECT_FORBIDRUN",STATUS_EFFECT_FORBIDRUN);
  tolua_constant(tolua_S,"STATUS_EFFECT_DROP",STATUS_EFFECT_DROP);
  tolua_constant(tolua_S,"STATUS_EFFECT_PERCIPIENCE",STATUS_EFFECT_PERCIPIENCE);
  tolua_constant(tolua_S,"DEMAND_NOT",DEMAND_NOT);
  tolua_constant(tolua_S,"DEMAND_GIFT",DEMAND_GIFT);
  tolua_constant(tolua_S,"DEMAND_HAIR_DYE",DEMAND_HAIR_DYE);
  tolua_constant(tolua_S,"DEMAND_HAIR_CUT",DEMAND_HAIR_CUT);
  tolua_constant(tolua_S,"DEMAND_TAMED",DEMAND_TAMED);
  tolua_constant(tolua_S,"DEMAND_FOOD",DEMAND_FOOD);
  tolua_constant(tolua_S,"DEMAND_TOOL",DEMAND_TOOL);
  tolua_constant(tolua_S,"PROFESSION_NOT",PROFESSION_NOT);
  tolua_constant(tolua_S,"PROFESSION_WOODCUTTER",PROFESSION_WOODCUTTER);
  tolua_constant(tolua_S,"PROFESSION_MELEE_GUARD",PROFESSION_MELEE_GUARD);
  tolua_constant(tolua_S,"PROFESSION_FARMER",PROFESSION_FARMER);
  tolua_constant(tolua_S,"PROFESSION_REMOTE_GUARD",PROFESSION_REMOTE_GUARD);
  tolua_constant(tolua_S,"PROFESSION_HELPER",PROFESSION_HELPER);
  tolua_constant(tolua_S,"PROFESSION_HUNTER",PROFESSION_HUNTER);
  tolua_constant(tolua_S,"PROFESSION_ARCHITECT",PROFESSION_ARCHITECT);
  tolua_cclass(tolua_S,"ActorBase","ActorBase","MNSandbox::SandboxNode",NULL);
#ifdef SANDBOX_BIND_LUATYPE
  SANDBOX_BIND_LUATYPE(ActorBase, "ActorBase", "ActorBase", "MNSandbox::SandboxNode")
#endif
  tolua_beginmodule(tolua_S,"ActorBase");
   tolua_function(tolua_S,"CreateComponentByFactory",tolua_miniSandboxEngineActor_ActorBase_CreateComponentByFactory00);
  tolua_endmodule(tolua_S);
  tolua_constant(tolua_S,"PLAYER_NOTIFYINFO_GETITEM",PLAYER_NOTIFYINFO_GETITEM);
  tolua_constant(tolua_S,"PLAYER_NOTIFYINFO_TIPS",PLAYER_NOTIFYINFO_TIPS);
  tolua_constant(tolua_S,"PLAYER_NOTIFYINFO_DEATH",PLAYER_NOTIFYINFO_DEATH);
  tolua_constant(tolua_S,"PLAYER_NOTIFYINFO_HORSEEGG",PLAYER_NOTIFYINFO_HORSEEGG);
  tolua_constant(tolua_S,"PLAYER_NOTIFYINFO_KILLPLAYER",PLAYER_NOTIFYINFO_KILLPLAYER);
  tolua_constant(tolua_S,"PLAYER_NOTIFYINFO_GETMULTIITEM",PLAYER_NOTIFYINFO_GETMULTIITEM);
  tolua_constant(tolua_S,"PLAYER_NOTIFYINFO_TASKGET",PLAYER_NOTIFYINFO_TASKGET);
  tolua_constant(tolua_S,"PLAYEROP_WAY_NORMAL",PLAYEROP_WAY_NORMAL);
  tolua_constant(tolua_S,"PLAYEROP_WAY_FOOTBALLER",PLAYEROP_WAY_FOOTBALLER);
  tolua_constant(tolua_S,"PLAYEROP_WAY_GRAVITYGUN",PLAYEROP_WAY_GRAVITYGUN);
  tolua_constant(tolua_S,"PLAYEROP_WAY_BASKETBALLER",PLAYEROP_WAY_BASKETBALLER);
  tolua_constant(tolua_S,"PLAYEROP_WAY_FISHING",PLAYEROP_WAY_FISHING);
  tolua_constant(tolua_S,"PLAYEROP_WAY_PUSHSNOWBALL",PLAYEROP_WAY_PUSHSNOWBALL);
  tolua_constant(tolua_S,"PLAYEROP_NULL",PLAYEROP_NULL);
  tolua_constant(tolua_S,"PLAYEROP_ATTACK_BOW",PLAYEROP_ATTACK_BOW);
  tolua_constant(tolua_S,"PLAYEROP_EATFOOD",PLAYEROP_EATFOOD);
  tolua_constant(tolua_S,"PLAYEROP_DRINKWATER",PLAYEROP_DRINKWATER);
  tolua_constant(tolua_S,"PLAYEROP_DIG",PLAYEROP_DIG);
  tolua_constant(tolua_S,"PLAYEROP_USE_ITEM_SKILL",PLAYEROP_USE_ITEM_SKILL);
  tolua_constant(tolua_S,"PLAYEROP_CATCH_BALL",PLAYEROP_CATCH_BALL);
  tolua_constant(tolua_S,"PLAYEROP_SHOOT",PLAYEROP_SHOOT);
  tolua_constant(tolua_S,"PLAYEROP_PASS_BALL",PLAYEROP_PASS_BALL);
  tolua_constant(tolua_S,"PLAYEROP_TACKLE",PLAYEROP_TACKLE);
  tolua_constant(tolua_S,"PLAYEROP_TACKLE_END",PLAYEROP_TACKLE_END);
  tolua_constant(tolua_S,"PLAYEROP_BALL_CHARGE_BEGIN",PLAYEROP_BALL_CHARGE_BEGIN);
  tolua_constant(tolua_S,"PLAYEROP_THROW_GRAVITYACTOR",PLAYEROP_THROW_GRAVITYACTOR);
  tolua_constant(tolua_S,"PLAYEROP_CATCH_GRAVITYACTOR",PLAYEROP_CATCH_GRAVITYACTOR);
  tolua_constant(tolua_S,"PLAYEROP_GRAVITY_CHARGE_BEGIN",PLAYEROP_GRAVITY_CHARGE_BEGIN);
  tolua_constant(tolua_S,"PLAYEROP_BASKETBALL_BLOCK_SHOT",PLAYEROP_BASKETBALL_BLOCK_SHOT);
  tolua_constant(tolua_S,"PLAYEROP_BASKETBALL_BLOCK_SHOT_END",PLAYEROP_BASKETBALL_BLOCK_SHOT_END);
  tolua_constant(tolua_S,"PLAYEROP_BASKETBALL_OBSTRUCT",PLAYEROP_BASKETBALL_OBSTRUCT);
  tolua_constant(tolua_S,"PLAYEROP_BASKETBALL_OBSTRUCT_END",PLAYEROP_BASKETBALL_OBSTRUCT_END);
  tolua_constant(tolua_S,"PLAYEROP_BASKETBALL_GRAB",PLAYEROP_BASKETBALL_GRAB);
  tolua_constant(tolua_S,"PLAYEROP_BASKETBALL_GRAB_END",PLAYEROP_BASKETBALL_GRAB_END);
  tolua_constant(tolua_S,"PLAYEROP_BASKETBALL_DRIBBLERUN",PLAYEROP_BASKETBALL_DRIBBLERUN);
  tolua_constant(tolua_S,"PLAYEROP_BASKETBALL_DRIBBLERUN_END",PLAYEROP_BASKETBALL_DRIBBLERUN_END);
  tolua_constant(tolua_S,"PLAYEROP_BASKETBALL_PASS",PLAYEROP_BASKETBALL_PASS);
  tolua_constant(tolua_S,"PLAYEROP_BASKETBALL_PASS_END",PLAYEROP_BASKETBALL_PASS_END);
  tolua_constant(tolua_S,"PLAYEROP_BASKETBALL_SHOOT",PLAYEROP_BASKETBALL_SHOOT);
  tolua_constant(tolua_S,"PLAYEROP_BASKETBALL_SHOOT_END",PLAYEROP_BASKETBALL_SHOOT_END);
  tolua_constant(tolua_S,"PLAYEROP_BASKETBALL_CHARGE_BEGIN",PLAYEROP_BASKETBALL_CHARGE_BEGIN);
  tolua_constant(tolua_S,"PLAYEROP_SHEILD_DEFENCE_BEGIN",PLAYEROP_SHEILD_DEFENCE_BEGIN);
  tolua_constant(tolua_S,"PLAYEROP_PUSHSNOWBALL_CHARGE_BEGIN",PLAYEROP_PUSHSNOWBALL_CHARGE_BEGIN);
  tolua_constant(tolua_S,"PLAYEROP_PUSHSNOWBALL_SHOOT",PLAYEROP_PUSHSNOWBALL_SHOOT);
  tolua_constant(tolua_S,"PLAYEROP_PUSHSNOWBALL_SHOOT_END",PLAYEROP_PUSHSNOWBALL_SHOOT_END);
  tolua_constant(tolua_S,"PLAYEROP_PUSHSNOWBALL_MAKEBALL",PLAYEROP_PUSHSNOWBALL_MAKEBALL);
  tolua_constant(tolua_S,"PLAYEROP_PUSHSNOWBALL_MAKEMAN",PLAYEROP_PUSHSNOWBALL_MAKEMAN);
  tolua_constant(tolua_S,"PLAYEROP_PUSHSNOWBALL_JUMP",PLAYEROP_PUSHSNOWBALL_JUMP);
  tolua_constant(tolua_S,"PLAYEROP_STATUS_BEGIN",PLAYEROP_STATUS_BEGIN);
  tolua_constant(tolua_S,"PLAYEROP_STATUS_END",PLAYEROP_STATUS_END);
  tolua_constant(tolua_S,"PLAYEROP_STATUS_CANCEL",PLAYEROP_STATUS_CANCEL);
  tolua_constant(tolua_S,"FREEZING_STATE_CLEAN",FREEZING_STATE_CLEAN);
  tolua_constant(tolua_S,"FREEZING_STATE_CANMOVE",FREEZING_STATE_CANMOVE);
  tolua_constant(tolua_S,"FREEZING_STATE_NOMOVE",FREEZING_STATE_NOMOVE);
  tolua_constant(tolua_S,"MOTION_STATIC",MOTION_STATIC);
  tolua_constant(tolua_S,"MOTION_MOVE",MOTION_MOVE);
  tolua_constant(tolua_S,"MOTION_RUN",MOTION_RUN);
  tolua_constant(tolua_S,"MOTION_JUMP",MOTION_JUMP);
  tolua_constant(tolua_S,"MOTION_TWOJUMP",MOTION_TWOJUMP);
  tolua_constant(tolua_S,"MOTION_SNEAK",MOTION_SNEAK);
  tolua_constant(tolua_S,"MOTION_FALLGROUND",MOTION_FALLGROUND);
  tolua_constant(tolua_S,"NOT_HAS_TARGET",NOT_HAS_TARGET);
  tolua_constant(tolua_S,"BEFORE_DESTION",BEFORE_DESTION);
  tolua_constant(tolua_S,"HIT_DESTION",HIT_DESTION);
  tolua_constant(tolua_S,"BEHIND_DESTION",BEHIND_DESTION);
  #ifdef __cplusplus
  tolua_cclass(tolua_S,"SummonPetInfomation","SummonPetInfomation","",tolua_collect_SummonPetInfomation);
#ifdef SANDBOX_BIND_LUATYPE
  SANDBOX_BIND_LUATYPE(SummonPetInfomation, "SummonPetInfomation", "SummonPetInfomation", "")
#endif
  #else
  tolua_cclass(tolua_S,"SummonPetInfomation","SummonPetInfomation","",NULL);
#ifdef SANDBOX_BIND_LUATYPE
  SANDBOX_BIND_LUATYPE(SummonPetInfomation, "SummonPetInfomation", "SummonPetInfomation", "")
#endif
  #endif
  tolua_beginmodule(tolua_S,"SummonPetInfomation");
   tolua_variable(tolua_S,"monsterid",tolua_get_SummonPetInfomation_monsterid,tolua_set_SummonPetInfomation_monsterid);
   tolua_variable(tolua_S,"petid",tolua_get_SummonPetInfomation_petid,tolua_set_SummonPetInfomation_petid);
   tolua_variable(tolua_S,"stage",tolua_get_SummonPetInfomation_stage,tolua_set_SummonPetInfomation_stage);
   tolua_variable(tolua_S,"quality",tolua_get_SummonPetInfomation_quality,tolua_set_SummonPetInfomation_quality);
   tolua_function(tolua_S,"new",tolua_miniSandboxEngineActor_SummonPetInfomation_new00);
   tolua_function(tolua_S,"new_local",tolua_miniSandboxEngineActor_SummonPetInfomation_new00_local);
   tolua_function(tolua_S,".call",tolua_miniSandboxEngineActor_SummonPetInfomation_new00_local);
  tolua_endmodule(tolua_S);
  tolua_constant(tolua_S,"NOGUN",NOGUN);
  tolua_constant(tolua_S,"GUN",GUN);
  tolua_constant(tolua_S,"CUSTOMGUN",CUSTOMGUN);
  tolua_cclass(tolua_S,"IClientPlayer","IClientPlayer","",NULL);
#ifdef SANDBOX_BIND_LUATYPE
  SANDBOX_BIND_LUATYPE(IClientPlayer, "IClientPlayer", "IClientPlayer", "")
#endif
  tolua_beginmodule(tolua_S,"IClientPlayer");
   tolua_function(tolua_S,"CastToActor",tolua_miniSandboxEngineActor_IClientPlayer_CastToActor00);
   tolua_function(tolua_S,"CastToNode",tolua_miniSandboxEngineActor_IClientPlayer_CastToNode00);
   tolua_function(tolua_S,"GetPlayer",tolua_miniSandboxEngineActor_IClientPlayer_GetPlayer00);
   tolua_function(tolua_S,"getNickname",tolua_miniSandboxEngineActor_IClientPlayer_getNickname00);
   tolua_function(tolua_S,"setRocketTeleport",tolua_miniSandboxEngineActor_IClientPlayer_setRocketTeleport00);
   tolua_function(tolua_S,"isRocketTeleport",tolua_miniSandboxEngineActor_IClientPlayer_isRocketTeleport00);
   tolua_function(tolua_S,"isStarStationTeleporting",tolua_miniSandboxEngineActor_IClientPlayer_isStarStationTeleporting00);
   tolua_function(tolua_S,"setIsStarStationTeleporting",tolua_miniSandboxEngineActor_IClientPlayer_setIsStarStationTeleporting00);
   tolua_function(tolua_S,"teleportRidingRocket",tolua_miniSandboxEngineActor_IClientPlayer_teleportRidingRocket00);
   tolua_function(tolua_S,"iGetObjId",tolua_miniSandboxEngineActor_IClientPlayer_iGetObjId00);
   tolua_function(tolua_S,"GetDirtyGridIndex",tolua_miniSandboxEngineActor_IClientPlayer_GetDirtyGridIndex00);
   tolua_function(tolua_S,"iGetPosition",tolua_miniSandboxEngineActor_IClientPlayer_iGetPosition00);
   tolua_function(tolua_S,"SetPlayerPosition",tolua_miniSandboxEngineActor_IClientPlayer_SetPlayerPosition00);
   tolua_function(tolua_S,"iGetCurMapID",tolua_miniSandboxEngineActor_IClientPlayer_iGetCurMapID00);
   tolua_function(tolua_S,"isMoveControlActive",tolua_miniSandboxEngineActor_IClientPlayer_isMoveControlActive00);
   tolua_function(tolua_S,"syncPos2Client",tolua_miniSandboxEngineActor_IClientPlayer_syncPos2Client00);
   tolua_function(tolua_S,"iGetBody",tolua_miniSandboxEngineActor_IClientPlayer_iGetBody00);
   tolua_function(tolua_S,"getCurViewRange",tolua_miniSandboxEngineActor_IClientPlayer_getCurViewRange00);
   tolua_function(tolua_S,"getChunkViewer",tolua_miniSandboxEngineActor_IClientPlayer_getChunkViewer00);
   tolua_function(tolua_S,"SetPlayerAttrViewRange",tolua_miniSandboxEngineActor_IClientPlayer_SetPlayerAttrViewRange00);
   tolua_function(tolua_S,"getEmitterWorldContainer",tolua_miniSandboxEngineActor_IClientPlayer_getEmitterWorldContainer00);
   tolua_function(tolua_S,"GetPlayerLocoMotion",tolua_miniSandboxEngineActor_IClientPlayer_GetPlayerLocoMotion00);
   tolua_function(tolua_S,"execCmd",tolua_miniSandboxEngineActor_IClientPlayer_execCmd00);
   tolua_function(tolua_S,"getIBackPack",tolua_miniSandboxEngineActor_IClientPlayer_getIBackPack00);
   tolua_function(tolua_S,"notifyGameInfo2Self",tolua_miniSandboxEngineActor_IClientPlayer_notifyGameInfo2Self00);
   tolua_function(tolua_S,"checkActionAttrState",tolua_miniSandboxEngineActor_IClientPlayer_checkActionAttrState00);
   tolua_function(tolua_S,"updateTaskSysProcess",tolua_miniSandboxEngineActor_IClientPlayer_updateTaskSysProcess00);
   tolua_function(tolua_S,"isInSpectatorMode",tolua_miniSandboxEngineActor_IClientPlayer_isInSpectatorMode00);
   tolua_function(tolua_S,"setSpectatorUin",tolua_miniSandboxEngineActor_IClientPlayer_setSpectatorUin00);
   tolua_function(tolua_S,"getSpectatorType",tolua_miniSandboxEngineActor_IClientPlayer_getSpectatorType00);
   tolua_function(tolua_S,"getSpectatorUin",tolua_miniSandboxEngineActor_IClientPlayer_getSpectatorUin00);
   tolua_function(tolua_S,"getToSpectatorUin",tolua_miniSandboxEngineActor_IClientPlayer_getToSpectatorUin00);
   tolua_function(tolua_S,"getCurToolID",tolua_miniSandboxEngineActor_IClientPlayer_getCurToolID00);
   tolua_function(tolua_S,"renderUI",tolua_miniSandboxEngineActor_IClientPlayer_renderUI00);
   tolua_function(tolua_S,"isExploiting",tolua_miniSandboxEngineActor_IClientPlayer_isExploiting00);
   tolua_function(tolua_S,"getOpenContainerBaseIndex",tolua_miniSandboxEngineActor_IClientPlayer_getOpenContainerBaseIndex00);
   tolua_function(tolua_S,"GetPlayerFaceDir",tolua_miniSandboxEngineActor_IClientPlayer_GetPlayerFaceDir00);
   tolua_function(tolua_S,"SetMoveForward",tolua_miniSandboxEngineActor_IClientPlayer_SetMoveForward00);
   tolua_function(tolua_S,"SetMoveRight",tolua_miniSandboxEngineActor_IClientPlayer_SetMoveRight00);
   tolua_function(tolua_S,"getUin",tolua_miniSandboxEngineActor_IClientPlayer_getUin00);
   tolua_function(tolua_S,"tryStandup",tolua_miniSandboxEngineActor_IClientPlayer_tryStandup00);
   tolua_function(tolua_S,"GetPlayerWorld",tolua_miniSandboxEngineActor_IClientPlayer_GetPlayerWorld00);
   tolua_function(tolua_S,"GetCurAccountHorse",tolua_miniSandboxEngineActor_IClientPlayer_GetCurAccountHorse00);
   tolua_function(tolua_S,"hasUIControl",tolua_miniSandboxEngineActor_IClientPlayer_hasUIControl00);
   tolua_function(tolua_S,"stopMusicByTrigger",tolua_miniSandboxEngineActor_IClientPlayer_stopMusicByTrigger00);
   tolua_function(tolua_S,"iGetTeam",tolua_miniSandboxEngineActor_IClientPlayer_iGetTeam00);
   tolua_function(tolua_S,"SetPlayerTeam",tolua_miniSandboxEngineActor_IClientPlayer_SetPlayerTeam00);
   tolua_function(tolua_S,"getCustomjson",tolua_miniSandboxEngineActor_IClientPlayer_getCustomjson00);
   tolua_function(tolua_S,"setCustomJson",tolua_miniSandboxEngineActor_IClientPlayer_setCustomJson00);
   tolua_function(tolua_S,"clearTrackerEntry",tolua_miniSandboxEngineActor_IClientPlayer_clearTrackerEntry00);
   tolua_function(tolua_S,"getSkinID",tolua_miniSandboxEngineActor_IClientPlayer_getSkinID00);
   tolua_function(tolua_S,"getTrackerEntrys",tolua_miniSandboxEngineActor_IClientPlayer_getTrackerEntrys00);
   tolua_function(tolua_S,"getGameResults",tolua_miniSandboxEngineActor_IClientPlayer_getGameResults00);
   tolua_function(tolua_S,"addTrackerEntry",tolua_miniSandboxEngineActor_IClientPlayer_addTrackerEntry00);
   tolua_function(tolua_S,"getGameScore",tolua_miniSandboxEngineActor_IClientPlayer_getGameScore00);
   tolua_function(tolua_S,"getBPTitle",tolua_miniSandboxEngineActor_IClientPlayer_getBPTitle00);
   tolua_function(tolua_S,"getGameRanking",tolua_miniSandboxEngineActor_IClientPlayer_getGameRanking00);
   tolua_function(tolua_S,"setTeleportPos",tolua_miniSandboxEngineActor_IClientPlayer_setTeleportPos00);
   tolua_function(tolua_S,"GetAccountSkinID",tolua_miniSandboxEngineActor_IClientPlayer_GetAccountSkinID00);
   tolua_function(tolua_S,"getVipInfo",tolua_miniSandboxEngineActor_IClientPlayer_getVipInfo00);
   tolua_function(tolua_S,"saveToFile",tolua_miniSandboxEngineActor_IClientPlayer_saveToFile00);
   tolua_function(tolua_S,"saveUserData",tolua_miniSandboxEngineActor_IClientPlayer_saveUserData00);
   tolua_function(tolua_S,"saveTechTree",tolua_miniSandboxEngineActor_IClientPlayer_saveTechTree00);
   tolua_function(tolua_S,"CanExposePosToOther",tolua_miniSandboxEngineActor_IClientPlayer_CanExposePosToOther00);
   tolua_function(tolua_S,"getRealLandingPoint",tolua_miniSandboxEngineActor_IClientPlayer_getRealLandingPoint00);
   tolua_function(tolua_S,"gotoTeleportPos",tolua_miniSandboxEngineActor_IClientPlayer_gotoTeleportPos00);
   tolua_function(tolua_S,"gotoTransferPos",tolua_miniSandboxEngineActor_IClientPlayer_gotoTransferPos00);
   tolua_function(tolua_S,"gotoSpawnPoint",tolua_miniSandboxEngineActor_IClientPlayer_gotoSpawnPoint00);
   tolua_function(tolua_S,"addAchievement",tolua_miniSandboxEngineActor_IClientPlayer_addAchievement00);
   tolua_function(tolua_S,"addOWScore",tolua_miniSandboxEngineActor_IClientPlayer_addOWScore00);
   tolua_function(tolua_S,"setSyncCustomModelTick",tolua_miniSandboxEngineActor_IClientPlayer_setSyncCustomModelTick00);
   tolua_function(tolua_S,"setSyncTransferTick",tolua_miniSandboxEngineActor_IClientPlayer_setSyncTransferTick00);
   tolua_function(tolua_S,"checkNewbieWorldProgress",tolua_miniSandboxEngineActor_IClientPlayer_checkNewbieWorldProgress00);
   tolua_function(tolua_S,"applyEquips",tolua_miniSandboxEngineActor_IClientPlayer_applyEquips00);
   tolua_function(tolua_S,"isUnlockItem",tolua_miniSandboxEngineActor_IClientPlayer_isUnlockItem00);
   tolua_function(tolua_S,"isHost",tolua_miniSandboxEngineActor_IClientPlayer_isHost00);
   tolua_function(tolua_S,"isRemote",tolua_miniSandboxEngineActor_IClientPlayer_isRemote00);
   tolua_function(tolua_S,"getOWID",tolua_miniSandboxEngineActor_IClientPlayer_getOWID00);
   tolua_function(tolua_S,"getCurOpenedContainer",tolua_miniSandboxEngineActor_IClientPlayer_getCurOpenedContainer00);
   tolua_function(tolua_S,"getCurOpenedContainerPos",tolua_miniSandboxEngineActor_IClientPlayer_getCurOpenedContainerPos00);
   tolua_function(tolua_S,"cleanupOpenedContainer",tolua_miniSandboxEngineActor_IClientPlayer_cleanupOpenedContainer00);
   tolua_function(tolua_S,"syncOpenFCMUIToClient",tolua_miniSandboxEngineActor_IClientPlayer_syncOpenFCMUIToClient00);
   tolua_function(tolua_S,"getCurOperate",tolua_miniSandboxEngineActor_IClientPlayer_getCurOperate00);
   tolua_function(tolua_S,"getCurOperatePos",tolua_miniSandboxEngineActor_IClientPlayer_getCurOperatePos00);
   tolua_function(tolua_S,"GetPlayerCurPlaceDir",tolua_miniSandboxEngineActor_IClientPlayer_GetPlayerCurPlaceDir00);
   tolua_function(tolua_S,"getPlaceDirToBlock",tolua_miniSandboxEngineActor_IClientPlayer_getPlaceDirToBlock00);
   tolua_function(tolua_S,"shortcutItemUsed",tolua_miniSandboxEngineActor_IClientPlayer_shortcutItemUsed00);
   tolua_function(tolua_S,"getHookObj",tolua_miniSandboxEngineActor_IClientPlayer_getHookObj00);
   tolua_function(tolua_S,"GetTreeItemIndex",tolua_miniSandboxEngineActor_IClientPlayer_GetTreeItemIndex00);
   tolua_function(tolua_S,"getOPWay",tolua_miniSandboxEngineActor_IClientPlayer_getOPWay00);
   tolua_function(tolua_S,"openEditActorModelUI",tolua_miniSandboxEngineActor_IClientPlayer_openEditActorModelUI00);
   tolua_function(tolua_S,"openContainer",tolua_miniSandboxEngineActor_IClientPlayer_openContainer00);
   tolua_function(tolua_S,"closeContainer",tolua_miniSandboxEngineActor_IClientPlayer_closeContainer00);
   tolua_function(tolua_S,"checkShowMusicClubChatBubble",tolua_miniSandboxEngineActor_IClientPlayer_checkShowMusicClubChatBubble00);
   tolua_function(tolua_S,"getCurShortcut",tolua_miniSandboxEngineActor_IClientPlayer_getCurShortcut00);
   tolua_function(tolua_S,"getCurShortcutItemNum",tolua_miniSandboxEngineActor_IClientPlayer_getCurShortcutItemNum00);
   tolua_function(tolua_S,"sleepInBed",tolua_miniSandboxEngineActor_IClientPlayer_sleepInBed00);
   tolua_function(tolua_S,"sleepInVehicleBed",tolua_miniSandboxEngineActor_IClientPlayer_sleepInVehicleBed00);
   tolua_function(tolua_S,"sleep",tolua_miniSandboxEngineActor_IClientPlayer_sleep00);
   tolua_function(tolua_S,"notifyOpenWindow2Self",tolua_miniSandboxEngineActor_IClientPlayer_notifyOpenWindow2Self00);
   tolua_function(tolua_S,"BlockBookCabinetVehicleDir",tolua_miniSandboxEngineActor_IClientPlayer_BlockBookCabinetVehicleDir00);
   tolua_function(tolua_S,"getPlayerAttribComponent",tolua_miniSandboxEngineActor_IClientPlayer_getPlayerAttribComponent00);
   tolua_function(tolua_S,"getContainersPassword",tolua_miniSandboxEngineActor_IClientPlayer_getContainersPassword00);
   tolua_function(tolua_S,"setContainersPassword",tolua_miniSandboxEngineActor_IClientPlayer_setContainersPassword00);
   tolua_function(tolua_S,"checkIsOpenContainer",tolua_miniSandboxEngineActor_IClientPlayer_checkIsOpenContainer00);
   tolua_function(tolua_S,"refreshAvarta",tolua_miniSandboxEngineActor_IClientPlayer_refreshAvarta00);
   tolua_function(tolua_S,"PlayerWakeUp",tolua_miniSandboxEngineActor_IClientPlayer_PlayerWakeUp00);
   tolua_function(tolua_S,"SetRunSandboxPlayer",tolua_miniSandboxEngineActor_IClientPlayer_SetRunSandboxPlayer00);
   tolua_function(tolua_S,"IsRunSandboxPlayer",tolua_miniSandboxEngineActor_IClientPlayer_IsRunSandboxPlayer00);
   tolua_function(tolua_S,"changePlayerModel",tolua_miniSandboxEngineActor_IClientPlayer_changePlayerModel00);
   tolua_function(tolua_S,"setSpeedUpTimes",tolua_miniSandboxEngineActor_IClientPlayer_setSpeedUpTimes00);
   tolua_function(tolua_S,"getSelectedColor",tolua_miniSandboxEngineActor_IClientPlayer_getSelectedColor00);
   tolua_function(tolua_S,"setSelectedColor",tolua_miniSandboxEngineActor_IClientPlayer_setSelectedColor00);
   tolua_function(tolua_S,"isUseHearth",tolua_miniSandboxEngineActor_IClientPlayer_isUseHearth00);
   tolua_function(tolua_S,"triggerInputEvent",tolua_miniSandboxEngineActor_IClientPlayer_triggerInputEvent00);
   tolua_function(tolua_S,"getEquipItem",tolua_miniSandboxEngineActor_IClientPlayer_getEquipItem00);
   tolua_function(tolua_S,"getEquipGrid",tolua_miniSandboxEngineActor_IClientPlayer_getEquipGrid00);
   tolua_function(tolua_S,"getEquipGridWithType",tolua_miniSandboxEngineActor_IClientPlayer_getEquipGridWithType00);
   tolua_function(tolua_S,"damageEquipItem",tolua_miniSandboxEngineActor_IClientPlayer_damageEquipItem00);
   tolua_function(tolua_S,"isShapeShift",tolua_miniSandboxEngineActor_IClientPlayer_isShapeShift00);
   tolua_function(tolua_S,"setPianoSoundName",tolua_miniSandboxEngineActor_IClientPlayer_setPianoSoundName00);
   tolua_function(tolua_S,"setPianoPaticleName",tolua_miniSandboxEngineActor_IClientPlayer_setPianoPaticleName00);
   tolua_function(tolua_S,"setPaticlePos",tolua_miniSandboxEngineActor_IClientPlayer_setPaticlePos00);
   tolua_function(tolua_S,"setPianoSoundPos",tolua_miniSandboxEngineActor_IClientPlayer_setPianoSoundPos00);
   tolua_function(tolua_S,"getCurSummonPetID",tolua_miniSandboxEngineActor_IClientPlayer_getCurSummonPetID00);
   tolua_function(tolua_S,"getCurSummonPetInfo",tolua_miniSandboxEngineActor_IClientPlayer_getCurSummonPetInfo00);
   tolua_function(tolua_S,"summonPet",tolua_miniSandboxEngineActor_IClientPlayer_summonPet00);
   tolua_function(tolua_S,"changeRoleData",tolua_miniSandboxEngineActor_IClientPlayer_changeRoleData00);
   tolua_function(tolua_S,"saveSkillCDCompToPB",tolua_miniSandboxEngineActor_IClientPlayer_saveSkillCDCompToPB00);
   tolua_function(tolua_S,"getSpectatorMode",tolua_miniSandboxEngineActor_IClientPlayer_getSpectatorMode00);
   tolua_function(tolua_S,"IsOnPlatform",tolua_miniSandboxEngineActor_IClientPlayer_IsOnPlatform00);
   tolua_function(tolua_S,"GetCatchBall",tolua_miniSandboxEngineActor_IClientPlayer_GetCatchBall00);
   tolua_function(tolua_S,"GetPlatformIClientActor",tolua_miniSandboxEngineActor_IClientPlayer_GetPlatformIClientActor00);
   tolua_function(tolua_S,"GetGunZoom",tolua_miniSandboxEngineActor_IClientPlayer_GetGunZoom00);
   tolua_function(tolua_S,"doWaterCanoonSkill",tolua_miniSandboxEngineActor_IClientPlayer_doWaterCanoonSkill00);
   tolua_function(tolua_S,"setFaceYaw",tolua_miniSandboxEngineActor_IClientPlayer_setFaceYaw00);
   tolua_function(tolua_S,"isMotionChangeSyncPos",tolua_miniSandboxEngineActor_IClientPlayer_isMotionChangeSyncPos00);
   tolua_function(tolua_S,"GetScreenSpacePos",tolua_miniSandboxEngineActor_IClientPlayer_GetScreenSpacePos00);
   tolua_function(tolua_S,"IsCurToolBroken",tolua_miniSandboxEngineActor_IClientPlayer_IsCurToolBroken00);
   tolua_function(tolua_S,"IsOffline",tolua_miniSandboxEngineActor_IClientPlayer_IsOffline00);
   tolua_function(tolua_S,"checkInSafeZone",tolua_miniSandboxEngineActor_IClientPlayer_checkInSafeZone00);
  tolua_endmodule(tolua_S);
  tolua_cclass(tolua_S,"IClientActor","IClientActor","ActorBase",NULL);
#ifdef SANDBOX_BIND_LUATYPE
  SANDBOX_BIND_LUATYPE(IClientActor, "IClientActor", "IClientActor", "ActorBase")
#endif
  tolua_beginmodule(tolua_S,"IClientActor");
   tolua_variable(tolua_S,"__Rainbow__SceneMGTNode__",tolua_get_IClientActor___Rainbow__SceneMGTNode__,NULL);
  tolua_endmodule(tolua_S);
  tolua_cclass(tolua_S,"IClientMob","IClientMob","",NULL);
#ifdef SANDBOX_BIND_LUATYPE
  SANDBOX_BIND_LUATYPE(IClientMob, "IClientMob", "IClientMob", "")
#endif
  tolua_beginmodule(tolua_S,"IClientMob");
  tolua_endmodule(tolua_S);
  tolua_cclass(tolua_S,"IPlayerControl","IPlayerControl","",NULL);
#ifdef SANDBOX_BIND_LUATYPE
  SANDBOX_BIND_LUATYPE(IPlayerControl, "IPlayerControl", "IPlayerControl", "")
#endif
  tolua_beginmodule(tolua_S,"IPlayerControl");
  tolua_endmodule(tolua_S);
  tolua_cclass(tolua_S,"IActorLiving","IActorLiving","",NULL);
#ifdef SANDBOX_BIND_LUATYPE
  SANDBOX_BIND_LUATYPE(IActorLiving, "IActorLiving", "IActorLiving", "")
#endif
  tolua_beginmodule(tolua_S,"IActorLiving");
  tolua_endmodule(tolua_S);
  tolua_constant(tolua_S,"SEQ_STAND",SEQ_STAND);
  tolua_constant(tolua_S,"SEQ_WALK",SEQ_WALK);
  tolua_constant(tolua_S,"SEQ_ATTACK",SEQ_ATTACK);
  tolua_constant(tolua_S,"SEQ_DIE",SEQ_DIE);
  tolua_constant(tolua_S,"SEQ_JUMP",SEQ_JUMP);
  tolua_constant(tolua_S,"SEQ_SKATEBOARD_DRIFT",SEQ_SKATEBOARD_DRIFT);
  tolua_constant(tolua_S,"SEQ_SKATEBOARD_SHOW1",SEQ_SKATEBOARD_SHOW1);
  tolua_constant(tolua_S,"SEQ_SKATEBOARD_SHOW2",SEQ_SKATEBOARD_SHOW2);
  tolua_constant(tolua_S,"SEQ_SKATEBOARD_SHOW3",SEQ_SKATEBOARD_SHOW3);
  tolua_constant(tolua_S,"SEQ_SKATEBOARD_IDLE2WALK",SEQ_SKATEBOARD_IDLE2WALK);
  tolua_constant(tolua_S,"SEQ_SKATEBOARD_WALK2IDLE",SEQ_SKATEBOARD_WALK2IDLE);
  tolua_constant(tolua_S,"SEQ_SKATEBOARD_JUMP2BLOCK",SEQ_SKATEBOARD_JUMP2BLOCK);
  tolua_constant(tolua_S,"SEQ_SKATEBOARD_ONBLOCKFRONT",SEQ_SKATEBOARD_ONBLOCKFRONT);
  tolua_constant(tolua_S,"SEQ_SKATEBOARD_DRIFT2",SEQ_SKATEBOARD_DRIFT2);
  tolua_constant(tolua_S,"SEQ_SKATEBOARD_JUMP2BLOCK2",SEQ_SKATEBOARD_JUMP2BLOCK2);
  tolua_constant(tolua_S,"SEQ_IDLEACTION",SEQ_IDLEACTION);
  tolua_constant(tolua_S,"SEQ_IDLEACTION2",SEQ_IDLEACTION2);
  tolua_constant(tolua_S,"SEQ_IDLEACTIONINVITE",SEQ_IDLEACTIONINVITE);
  tolua_constant(tolua_S,"SEQ_BEHIT",SEQ_BEHIT);
  tolua_constant(tolua_S,"SEQ_LAYDOWN",SEQ_LAYDOWN);
  tolua_constant(tolua_S,"SEQ_SITDOWN",SEQ_SITDOWN);
  tolua_constant(tolua_S,"SEQ_HELM",SEQ_HELM);
  tolua_constant(tolua_S,"SEQ_SWIM",SEQ_SWIM);
  tolua_constant(tolua_S,"SEQ_RUN",SEQ_RUN);
  tolua_constant(tolua_S,"SEQ_MOBEAT",SEQ_MOBEAT);
  tolua_constant(tolua_S,"SEQ_MOBDRINK",SEQ_MOBDRINK);
  tolua_constant(tolua_S,"SEQ_MOBSLEEP",SEQ_MOBSLEEP);
  tolua_constant(tolua_S,"SEQ_MOBDANCE",SEQ_MOBDANCE);
  tolua_constant(tolua_S,"SEQ_MOBTOPPLEOVER",SEQ_MOBTOPPLEOVER);
  tolua_constant(tolua_S,"SEQ_MOBLOGGERHEAD",SEQ_MOBLOGGERHEAD);
  tolua_constant(tolua_S,"SEQ_MOBSCROLL",SEQ_MOBSCROLL);
  tolua_constant(tolua_S,"SEQ_MOBKICK",SEQ_MOBKICK);
  tolua_constant(tolua_S,"SEQ_MOBDODGE",SEQ_MOBDODGE);
  tolua_constant(tolua_S,"SEQ_MOBCONCEAL",SEQ_MOBCONCEAL);
  tolua_constant(tolua_S,"SEQ_MOBHOWL",SEQ_MOBHOWL);
  tolua_constant(tolua_S,"SEQ_MOBMAKETROUBLE",SEQ_MOBMAKETROUBLE);
  tolua_constant(tolua_S,"SEQ_MOBBREATHE",SEQ_MOBBREATHE);
  tolua_constant(tolua_S,"SEQ_MOBHOLD",SEQ_MOBHOLD);
  tolua_constant(tolua_S,"SEQ_MOBREVERSE",SEQ_MOBREVERSE);
  tolua_constant(tolua_S,"SEQ_SHOOTARROW",SEQ_SHOOTARROW);
  tolua_constant(tolua_S,"SEQ_EAT",SEQ_EAT);
  tolua_constant(tolua_S,"SEQ_DRINK",SEQ_DRINK);
  tolua_constant(tolua_S,"SEQ_SHOOTARROW_WALK",SEQ_SHOOTARROW_WALK);
  tolua_constant(tolua_S,"SEQ_TAMED",SEQ_TAMED);
  tolua_constant(tolua_S,"SEQ_FLY",SEQ_FLY);
  tolua_constant(tolua_S,"SEQ_JUMPTWO",SEQ_JUMPTWO);
  tolua_constant(tolua_S,"SEQ_SKINFLY",SEQ_SKINFLY);
  tolua_constant(tolua_S,"SEQ_SKINSTAND",SEQ_SKINSTAND);
  tolua_constant(tolua_S,"SEQ_SITCHAIR",SEQ_SITCHAIR);
  tolua_constant(tolua_S,"SEQ_DISAPPEAR",SEQ_DISAPPEAR);
  tolua_constant(tolua_S,"SEQ_SHOWTIME",SEQ_SHOWTIME);
  tolua_constant(tolua_S,"SEQ_DIG_MULTI",SEQ_DIG_MULTI);
  tolua_constant(tolua_S,"SEQ_DIG_CHARGE",SEQ_DIG_CHARGE);
  tolua_constant(tolua_S,"SEQ_SKINNING",SEQ_SKINNING);
  tolua_constant(tolua_S,"SEQ_TOOL_LOOP",SEQ_TOOL_LOOP);
  tolua_constant(tolua_S,"SEQ_TOOL_ATTACK",SEQ_TOOL_ATTACK);
  tolua_constant(tolua_S,"SEQ_ITEMSKILL_START",SEQ_ITEMSKILL_START);
  tolua_constant(tolua_S,"SEQ_ITEMSKILL_ATTACK",SEQ_ITEMSKILL_ATTACK);
  tolua_constant(tolua_S,"SEQ_GUN_IDLE",SEQ_GUN_IDLE);
  tolua_constant(tolua_S,"SEQ_GUN_FIRE",SEQ_GUN_FIRE);
  tolua_constant(tolua_S,"SEQ_GUN_RELOAD",SEQ_GUN_RELOAD);
  tolua_constant(tolua_S,"SEQ_KICK_BALL",SEQ_KICK_BALL);
  tolua_constant(tolua_S,"SEQ_TACKLE",SEQ_TACKLE);
  tolua_constant(tolua_S,"SEQ_WIZARDMAGIC",SEQ_WIZARDMAGIC);
  tolua_constant(tolua_S,"SEQ_BUMP_PRE",SEQ_BUMP_PRE);
  tolua_constant(tolua_S,"SEQ_THINK",SEQ_THINK);
  tolua_constant(tolua_S,"SEQ_RUB_HAND",SEQ_RUB_HAND);
  tolua_constant(tolua_S,"SEQ_GET_UP",SEQ_GET_UP);
  tolua_constant(tolua_S,"SEQ_HUNGER_SIT",SEQ_HUNGER_SIT);
  tolua_constant(tolua_S,"SEQ_SCREAM",SEQ_SCREAM);
  tolua_constant(tolua_S,"SEQ_TIRED",SEQ_TIRED);
  tolua_constant(tolua_S,"SEQ_HALFGIANT_RIGHTHANDATK",SEQ_HALFGIANT_RIGHTHANDATK);
  tolua_constant(tolua_S,"SEQ_HALFGIANT_LEFTHANDATK",SEQ_HALFGIANT_LEFTHANDATK);
  tolua_constant(tolua_S,"SEQ_HALFGIANT_BOTHHANDSATK",SEQ_HALFGIANT_BOTHHANDSATK);
  tolua_constant(tolua_S,"SEQ_HALFGIANT_BORN",SEQ_HALFGIANT_BORN);
  tolua_constant(tolua_S,"SEQ_HALFGIANT_STUN",SEQ_HALFGIANT_STUN);
  tolua_constant(tolua_S,"SEQ_HALFGIANT_TRANSFORM",SEQ_HALFGIANT_TRANSFORM);
  tolua_constant(tolua_S,"SEQ_GIANT_BOW",SEQ_GIANT_BOW);
  tolua_constant(tolua_S,"SEQ_GIANT_ATKGROUND",SEQ_GIANT_ATKGROUND);
  tolua_constant(tolua_S,"SEQ_GIANT_BORN",SEQ_GIANT_BORN);
  tolua_constant(tolua_S,"SEQ_GIANT_STUN",SEQ_GIANT_STUN);
  tolua_constant(tolua_S,"SEQ_GIANT_ROCKWAVE_READY",SEQ_GIANT_ROCKWAVE_READY);
  tolua_constant(tolua_S,"SEQ_GIANT_ROCKWAVE_CAST",SEQ_GIANT_ROCKWAVE_CAST);
  tolua_constant(tolua_S,"SEQ_GIANT_REVENGEROAR",SEQ_GIANT_REVENGEROAR);
  tolua_constant(tolua_S,"SEQ_GIANT_ATKAIR",SEQ_GIANT_ATKAIR);
  tolua_constant(tolua_S,"SEQ_GIANT_TRANSFORM",SEQ_GIANT_TRANSFORM);
  tolua_constant(tolua_S,"SEQ_PLAY_ACT",SEQ_PLAY_ACT);
  tolua_constant(tolua_S,"SEQ_CATCH_GRAVITYACTOR",SEQ_CATCH_GRAVITYACTOR);
  tolua_constant(tolua_S,"SEQ_SWIM_DIVING",SEQ_SWIM_DIVING);
  tolua_constant(tolua_S,"SEQ_SWIM_RUSH",SEQ_SWIM_RUSH);
  tolua_constant(tolua_S,"SEQ_SWIM_IDLE",SEQ_SWIM_IDLE);
  tolua_constant(tolua_S,"SEQ_BASKETBALL_OBSTRUCT",SEQ_BASKETBALL_OBSTRUCT);
  tolua_constant(tolua_S,"SEQ_BASKETBALL_BLOCK_SHOT",SEQ_BASKETBALL_BLOCK_SHOT);
  tolua_constant(tolua_S,"SEQ_BASKETBALL_SHOOT_AND_PASS",SEQ_BASKETBALL_SHOOT_AND_PASS);
  tolua_constant(tolua_S,"SEQ_BASKETBALL_DRIBBLE",SEQ_BASKETBALL_DRIBBLE);
  tolua_constant(tolua_S,"SEQ_BASKETBALL_GRAB_BEFORE",SEQ_BASKETBALL_GRAB_BEFORE);
  tolua_constant(tolua_S,"SEQ_BASKETBALL_GRAB",SEQ_BASKETBALL_GRAB);
  tolua_constant(tolua_S,"SEQ_SHAPE_SHIFT",SEQ_SHAPE_SHIFT);
  tolua_constant(tolua_S,"SEQ_RE_SHAPE_SHIFT",SEQ_RE_SHAPE_SHIFT);
  tolua_constant(tolua_S,"SEQ_SPITFIRE_LAND",SEQ_SPITFIRE_LAND);
  tolua_constant(tolua_S,"SEQ_SPITFIRE_AIR",SEQ_SPITFIRE_AIR);
  tolua_constant(tolua_S,"SEQ_SPITFIRE_LAND_WAIT_CD",SEQ_SPITFIRE_LAND_WAIT_CD);
  tolua_constant(tolua_S,"SEQ_SPITFIRE_AIR_WAIT_CD",SEQ_SPITFIRE_AIR_WAIT_CD);
  tolua_constant(tolua_S,"SEQ_SAVAGE_DANCE",SEQ_SAVAGE_DANCE);
  tolua_constant(tolua_S,"SEQ_SAVAGE_CRUEL",SEQ_SAVAGE_CRUEL);
  tolua_constant(tolua_S,"SEQ_SAVAGE_SHOCK",SEQ_SAVAGE_SHOCK);
  tolua_constant(tolua_S,"SEQ_SAVAGE_SING",SEQ_SAVAGE_SING);
  tolua_constant(tolua_S,"SEQ_CARRIED",SEQ_CARRIED);
  tolua_constant(tolua_S,"SEQ_SHAKE_HEAD",SEQ_SHAKE_HEAD);
  tolua_constant(tolua_S,"SEQ_SAVAGE_CELEBRATE",SEQ_SAVAGE_CELEBRATE);
  tolua_constant(tolua_S,"SEQ_CARRYING",SEQ_CARRYING);
  tolua_constant(tolua_S,"SEQ_EXTREMIS_WALK",SEQ_EXTREMIS_WALK);
  tolua_constant(tolua_S,"SEQ_EXTREMIS_STAND_1",SEQ_EXTREMIS_STAND_1);
  tolua_constant(tolua_S,"SEQ_EXTREMIS_STAND_2",SEQ_EXTREMIS_STAND_2);
  tolua_constant(tolua_S,"SEQ_EXTREMIS_STAND_3",SEQ_EXTREMIS_STAND_3);
  tolua_constant(tolua_S,"SEQ_NOD",SEQ_NOD);
  tolua_constant(tolua_S,"SEQ_STAND_HUNGER",SEQ_STAND_HUNGER);
  tolua_constant(tolua_S,"SEQ_FLEE",SEQ_FLEE);
  tolua_constant(tolua_S,"SEQ_CAY",SEQ_CAY);
  tolua_constant(tolua_S,"SEQ_TALK",SEQ_TALK);
  tolua_constant(tolua_S,"SEQ_STARFIRE",SEQ_STARFIRE);
  tolua_constant(tolua_S,"SEQ_STARFIRE_CD",SEQ_STARFIRE_CD);
  tolua_constant(tolua_S,"SEQ_MOONFLY",SEQ_MOONFLY);
  tolua_constant(tolua_S,"SEQ_SAYHELLO",SEQ_SAYHELLO);
  tolua_constant(tolua_S,"SEQ_ANGRY",SEQ_ANGRY);
  tolua_constant(tolua_S,"SEQ_THANKS",SEQ_THANKS);
  tolua_constant(tolua_S,"SEQ_POSE",SEQ_POSE);
  tolua_constant(tolua_S,"SEQ_FORTUNEMOOSKILL",SEQ_FORTUNEMOOSKILL);
  tolua_constant(tolua_S,"SEQ_HOMELAND_PET_STANDBY",SEQ_HOMELAND_PET_STANDBY);
  tolua_constant(tolua_S,"SEQ_HOMELAND_PET_IDLE",SEQ_HOMELAND_PET_IDLE);
  tolua_constant(tolua_S,"SEQ_HOMELAND_PET_SITDOWN",SEQ_HOMELAND_PET_SITDOWN);
  tolua_constant(tolua_S,"SEQ_HOMELAND_PET_LYINGDOWN",SEQ_HOMELAND_PET_LYINGDOWN);
  tolua_constant(tolua_S,"SEQ_HOMELAND_PET_HAPPY",SEQ_HOMELAND_PET_HAPPY);
  tolua_constant(tolua_S,"SEQ_HOMELAND_PET_SHAKEINGHEAD",SEQ_HOMELAND_PET_SHAKEINGHEAD);
  tolua_constant(tolua_S,"SEQ_HOMECHEST_PLANT_STANDBY",SEQ_HOMECHEST_PLANT_STANDBY);
  tolua_constant(tolua_S,"SEQ_HOMECHEST_PLANT_FAWN",SEQ_HOMECHEST_PLANT_FAWN);
  tolua_constant(tolua_S,"SEQ_VACANT2_ANNIM1",SEQ_VACANT2_ANNIM1);
  tolua_constant(tolua_S,"SEQ_VACANT2_ANNIM2",SEQ_VACANT2_ANNIM2);
  tolua_constant(tolua_S,"SEQ_VACANT2_ANNIM3",SEQ_VACANT2_ANNIM3);
  tolua_constant(tolua_S,"SEQ_VACANT2_ANNIM4",SEQ_VACANT2_ANNIM4);
  tolua_constant(tolua_S,"SEQ_VACANT2_ANNIM5",SEQ_VACANT2_ANNIM5);
  tolua_constant(tolua_S,"SEQ_VACANT2_ANNIM6",SEQ_VACANT2_ANNIM6);
  tolua_constant(tolua_S,"SEQ_VACANT2_ANNIM7",SEQ_VACANT2_ANNIM7);
  tolua_constant(tolua_S,"SEQ_VACANT2_ANNIM8",SEQ_VACANT2_ANNIM8);
  tolua_constant(tolua_S,"SEQ_VACANT2_ANNIM9",SEQ_VACANT2_ANNIM9);
  tolua_constant(tolua_S,"SEQ_VACANT2_ANNIM10",SEQ_VACANT2_ANNIM10);
  tolua_constant(tolua_S,"SEQ_VACANT2_ANNIM11",SEQ_VACANT2_ANNIM11);
  tolua_constant(tolua_S,"SEQ_VACANT1_ANNIM1",SEQ_VACANT1_ANNIM1);
  tolua_constant(tolua_S,"SEQ_VACANT1_ANNIM2",SEQ_VACANT1_ANNIM2);
  tolua_constant(tolua_S,"SEQ_VACANT1_ANNIM3",SEQ_VACANT1_ANNIM3);
  tolua_constant(tolua_S,"SEQ_VACANT1_ANNIM4",SEQ_VACANT1_ANNIM4);
  tolua_constant(tolua_S,"SEQ_VACANT1_ANNIM5",SEQ_VACANT1_ANNIM5);
  tolua_constant(tolua_S,"SEQ_VACANT1_ANNIM6",SEQ_VACANT1_ANNIM6);
  tolua_constant(tolua_S,"SEQ_VACANT1_ANNIM7",SEQ_VACANT1_ANNIM7);
  tolua_constant(tolua_S,"SEQ_VACANT1_ANNIM8",SEQ_VACANT1_ANNIM8);
  tolua_constant(tolua_S,"SEQ_VACANT1_ANNIM9",SEQ_VACANT1_ANNIM9);
  tolua_constant(tolua_S,"SEQ_VACANT1_ANNIM10",SEQ_VACANT1_ANNIM10);
  tolua_constant(tolua_S,"SEQ_VACANT1_ANNIM11",SEQ_VACANT1_ANNIM11);
  tolua_constant(tolua_S,"SEQ_VACANT1_ANNIM12",SEQ_VACANT1_ANNIM12);
  tolua_constant(tolua_S,"SEQ_VACANT1_ANNIM13",SEQ_VACANT1_ANNIM13);
  tolua_constant(tolua_S,"SEQ_VACANT1_ANNIM14",SEQ_VACANT1_ANNIM14);
  tolua_constant(tolua_S,"SEQ_VACANT1_ANNIM15",SEQ_VACANT1_ANNIM15);
  tolua_constant(tolua_S,"SEQ_VACANT1_ANNIM16",SEQ_VACANT1_ANNIM16);
  tolua_constant(tolua_S,"SEQ_VACANT1_ANNIM17",SEQ_VACANT1_ANNIM17);
  tolua_constant(tolua_S,"SEQ_VACANT1_ANNIM18",SEQ_VACANT1_ANNIM18);
  tolua_constant(tolua_S,"SEQ_EARTHMAN_ANNIM",SEQ_EARTHMAN_ANNIM);
  tolua_constant(tolua_S,"SEQ_BUTTERFLY_CHANGEBLOCK",SEQ_BUTTERFLY_CHANGEBLOCK);
  tolua_constant(tolua_S,"SEQ_BUTTERFLY_FLYSTAY",SEQ_BUTTERFLY_FLYSTAY);
  tolua_constant(tolua_S,"SEQ_STAND_SLEEP",SEQ_STAND_SLEEP);
  tolua_constant(tolua_S,"SEQ_FLYMOB_FALLGROUND",SEQ_FLYMOB_FALLGROUND);
  tolua_constant(tolua_S,"SEQ_COOK",SEQ_COOK);
  tolua_constant(tolua_S,"SEQ_FLASH",SEQ_FLASH);
  tolua_constant(tolua_S,"SEQ_HOVER",SEQ_HOVER);
  tolua_constant(tolua_S,"SEQ_HAPPLYFLY",SEQ_HAPPLYFLY);
  tolua_constant(tolua_S,"SEQ_UPSETFLY",SEQ_UPSETFLY);
  tolua_constant(tolua_S,"SEQ_SCORPION_HIDE",SEQ_SCORPION_HIDE);
  tolua_constant(tolua_S,"SEQ_SCORPION_DIRLLOUT",SEQ_SCORPION_DIRLLOUT);
  tolua_constant(tolua_S,"SEQ_SCORPION_TAIL",SEQ_SCORPION_TAIL);
  tolua_constant(tolua_S,"SEQ_SCORPION_NAIL",SEQ_SCORPION_NAIL);
  tolua_constant(tolua_S,"SEQ_SCORPION_STANDBY",SEQ_SCORPION_STANDBY);
  tolua_constant(tolua_S,"SEQ_SCORPION_WALK",SEQ_SCORPION_WALK);
  tolua_constant(tolua_S,"SEQ_SCORPION_HIT",SEQ_SCORPION_HIT);
  tolua_constant(tolua_S,"SEQ_SCORPION_DIE",SEQ_SCORPION_DIE);
  tolua_constant(tolua_S,"SEQ_SCORPION_ATTACK",SEQ_SCORPION_ATTACK);
  tolua_constant(tolua_S,"SEQ_SCORPION_DEADLY",SEQ_SCORPION_DEADLY);
  tolua_constant(tolua_S,"SEQ_SANDMAN_HIDE",SEQ_SANDMAN_HIDE);
  tolua_constant(tolua_S,"SEQ_SANDMAN_RANGEDATK",SEQ_SANDMAN_RANGEDATK);
  tolua_constant(tolua_S,"SEQ_SANDMAN_FAKEDEATH",SEQ_SANDMAN_FAKEDEATH);
  tolua_constant(tolua_S,"SEQ_SANDMAN_REVIVE",SEQ_SANDMAN_REVIVE);
  tolua_constant(tolua_S,"SEQ_SANDMAN_ABSORB",SEQ_SANDMAN_ABSORB);
  tolua_constant(tolua_S,"SEQ_CAMEL_DRINK",SEQ_CAMEL_DRINK);
  tolua_constant(tolua_S,"SEQ_CAMEL_RANGEDATK",SEQ_CAMEL_RANGEDATK);
  tolua_constant(tolua_S,"SEQ_SANDWORM_STANBY",SEQ_SANDWORM_STANBY);
  tolua_constant(tolua_S,"SEQ_SANDWORM_WALK",SEQ_SANDWORM_WALK);
  tolua_constant(tolua_S,"SEQ_SANDWORM_HURT",SEQ_SANDWORM_HURT);
  tolua_constant(tolua_S,"SEQ_SANDWORM_DIE",SEQ_SANDWORM_DIE);
  tolua_constant(tolua_S,"SEQ_SANDWORM_ROAR",SEQ_SANDWORM_ROAR);
  tolua_constant(tolua_S,"SEQ_SANDWORM_ATTACK",SEQ_SANDWORM_ATTACK);
  tolua_constant(tolua_S,"SEQ_SANDWORM_VERTIGO",SEQ_SANDWORM_VERTIGO);
  tolua_constant(tolua_S,"SEQ_SANDWORM_BITE1",SEQ_SANDWORM_BITE1);
  tolua_constant(tolua_S,"SEQ_SANDWORM_BITE2",SEQ_SANDWORM_BITE2);
  tolua_constant(tolua_S,"SEQ_SANDWORM_MARACAS",SEQ_SANDWORM_MARACAS);
  tolua_constant(tolua_S,"SEQ_SANDWORM_DRILLING_IN",SEQ_SANDWORM_DRILLING_IN);
  tolua_constant(tolua_S,"SEQ_SANDWORM_DRILLING_OUT",SEQ_SANDWORM_DRILLING_OUT);
  tolua_constant(tolua_S,"SEQ_SANDWORM_TOW",SEQ_SANDWORM_TOW);
  tolua_constant(tolua_S,"SEQ_SANDWORM_DASH",SEQ_SANDWORM_DASH);
  tolua_constant(tolua_S,"SEQ_SANDWORM_DRILLING_IN_STATE",SEQ_SANDWORM_DRILLING_IN_STATE);
  tolua_constant(tolua_S,"SEQ_SANDWORM_NIBBLE",SEQ_SANDWORM_NIBBLE);
  tolua_constant(tolua_S,"SEQ_DANCE1",SEQ_DANCE1);
  tolua_constant(tolua_S,"SEQ_DANCE2",SEQ_DANCE2);
  tolua_constant(tolua_S,"SEQ_DANCE3",SEQ_DANCE3);
  tolua_constant(tolua_S,"SEQ_DANCE4",SEQ_DANCE4);
  tolua_constant(tolua_S,"SEQ_DANCE5",SEQ_DANCE5);
  tolua_constant(tolua_S,"SEQ_WAKEUP",SEQ_WAKEUP);
  tolua_constant(tolua_S,"SEQ_MUSIC_IDLE",SEQ_MUSIC_IDLE);
  tolua_constant(tolua_S,"SEQ_MUSIC_PLAY",SEQ_MUSIC_PLAY);
  tolua_constant(tolua_S,"SEQ_GUARD_NOD",SEQ_GUARD_NOD);
  tolua_constant(tolua_S,"SEQ_DESERTBUSSINESS_CRYDOWN",SEQ_DESERTBUSSINESS_CRYDOWN);
  tolua_constant(tolua_S,"SEQ_DESERTBUSSINESS_GEST",SEQ_DESERTBUSSINESS_GEST);
  tolua_constant(tolua_S,"SEQ_DESERTBUSSINESS_REFUSE",SEQ_DESERTBUSSINESS_REFUSE);
  tolua_constant(tolua_S,"SEQ_DESERTBUSSINESS_COLLECT",SEQ_DESERTBUSSINESS_COLLECT);
  tolua_constant(tolua_S,"SEQ_DJUMP",SEQ_DJUMP);
  tolua_constant(tolua_S,"SEQ_ENTERWORLD",SEQ_ENTERWORLD);
  tolua_constant(tolua_S,"SEQ_REVIVE",SEQ_REVIVE);
  tolua_constant(tolua_S,"SEQ_HIPPOCAMPUS_BIND",SEQ_HIPPOCAMPUS_BIND);
  tolua_constant(tolua_S,"SEQ_CRAB_DIG_BLOCK",SEQ_CRAB_DIG_BLOCK);
  tolua_constant(tolua_S,"SEQ_CRAB_STRUGGLE",SEQ_CRAB_STRUGGLE);
  tolua_constant(tolua_S,"SEQ_CRAB_CLAMB",SEQ_CRAB_CLAMB);
  tolua_constant(tolua_S,"SEQ_CRAB_SHOW_HAND",SEQ_CRAB_SHOW_HAND);
  tolua_constant(tolua_S,"SEQ_CRAB_CLIMB",SEQ_CRAB_CLIMB);
  tolua_constant(tolua_S,"SEQ_FLYINGFISH_SWIM",SEQ_FLYINGFISH_SWIM);
  tolua_constant(tolua_S,"SEQ_FLYINGFISH_ESCAPA",SEQ_FLYINGFISH_ESCAPA);
  tolua_constant(tolua_S,"SEQ_FLYINGFISH_FLY",SEQ_FLYINGFISH_FLY);
  tolua_constant(tolua_S,"SEQ_FLYINGFISH_TAKE_OFF",SEQ_FLYINGFISH_TAKE_OFF);
  tolua_constant(tolua_S,"SEQ_FLYINGFISH_ENTRY_WATER",SEQ_FLYINGFISH_ENTRY_WATER);
  tolua_constant(tolua_S,"SEQ_JELLYFISH_SHAKE_1",SEQ_JELLYFISH_SHAKE_1);
  tolua_constant(tolua_S,"SEQ_JELLYFISH_SHAKE_2",SEQ_JELLYFISH_SHAKE_2);
  tolua_constant(tolua_S,"SEQ_JELLYFISH_EXHAUST",SEQ_JELLYFISH_EXHAUST);
  tolua_constant(tolua_S,"SEQ_JELLYFISH_FLOOD_GAS",SEQ_JELLYFISH_FLOOD_GAS);
  tolua_constant(tolua_S,"SEQ_SHARK_BITE_1",SEQ_SHARK_BITE_1);
  tolua_constant(tolua_S,"SEQ_SHARK_BITE_2",SEQ_SHARK_BITE_2);
  tolua_constant(tolua_S,"SEQ_MOUBULA_TAIL_ATTACK",SEQ_MOUBULA_TAIL_ATTACK);
  tolua_constant(tolua_S,"SEQ_FISHCONCEAL",SEQ_FISHCONCEAL);
  tolua_constant(tolua_S,"SEQ_MOUBULA_REST_DOWN",SEQ_MOUBULA_REST_DOWN);
  tolua_constant(tolua_S,"SEQ_MOUBULA_REST_UP",SEQ_MOUBULA_REST_UP);
  tolua_constant(tolua_S,"SEQ_ENDING_FISHING",SEQ_ENDING_FISHING);
  tolua_constant(tolua_S,"SEQ_LAYEGG",SEQ_LAYEGG);
  tolua_constant(tolua_S,"SEQ_BATREVERSE",SEQ_BATREVERSE);
  tolua_constant(tolua_S,"SEQ_BATATTACH",SEQ_BATATTACH);
  tolua_constant(tolua_S,"SEQ_MOBCONCEAL_NEW",SEQ_MOBCONCEAL_NEW);
  tolua_constant(tolua_S,"SEQ_AVATAT_SUMMON",SEQ_AVATAT_SUMMON);
  tolua_constant(tolua_S,"SEQ_BOT_IDLE",SEQ_BOT_IDLE);
  tolua_constant(tolua_S,"SEQ_BOT_MOVE",SEQ_BOT_MOVE);
  tolua_constant(tolua_S,"SEQ_BOT_ENTER",SEQ_BOT_ENTER);
  tolua_constant(tolua_S,"SEQ_BOT_ENTER_FAST",SEQ_BOT_ENTER_FAST);
  tolua_constant(tolua_S,"SEQ_BOT_LEAVE",SEQ_BOT_LEAVE);
  tolua_constant(tolua_S,"SEQ_BOT_LEAVE_FAST",SEQ_BOT_LEAVE_FAST);
  tolua_constant(tolua_S,"SEQ_BOT_AWAIT1",SEQ_BOT_AWAIT1);
  tolua_constant(tolua_S,"SEQ_BOT_AWAIT2",SEQ_BOT_AWAIT2);
  tolua_constant(tolua_S,"SEQ_BOT_PROJECTION1",SEQ_BOT_PROJECTION1);
  tolua_constant(tolua_S,"SEQ_BOT_PROJECTION2",SEQ_BOT_PROJECTION2);
  tolua_constant(tolua_S,"SEQ_BOT_PROJECTION_END",SEQ_BOT_PROJECTION_END);
  tolua_constant(tolua_S,"SEQ_BOT_ANXIOUS",SEQ_BOT_ANXIOUS);
  tolua_constant(tolua_S,"SEQ_BOT_TALK",SEQ_BOT_TALK);
  tolua_constant(tolua_S,"SEQ_BOT_POINT_RIGHTUP",SEQ_BOT_POINT_RIGHTUP);
  tolua_constant(tolua_S,"SEQ_BOT_POINT_DOWN",SEQ_BOT_POINT_DOWN);
  tolua_constant(tolua_S,"SEQ_BOT_POINT_LEFTUP",SEQ_BOT_POINT_LEFTUP);
  tolua_constant(tolua_S,"SEQ_BOT_AWAKE",SEQ_BOT_AWAKE);
  tolua_constant(tolua_S,"SEQ_GLISSADE",SEQ_GLISSADE);
  tolua_constant(tolua_S,"SEQ_DOG_PADDLE",SEQ_DOG_PADDLE);
  tolua_constant(tolua_S,"SEQ_SNOWMAN_STAND_PANIC",SEQ_SNOWMAN_STAND_PANIC);
  tolua_constant(tolua_S,"SEQ_SNOWMAN_MOVE_PANIC",SEQ_SNOWMAN_MOVE_PANIC);
  tolua_constant(tolua_S,"SEQ_SNOWMAN_STAND",SEQ_SNOWMAN_STAND);
  tolua_constant(tolua_S,"SEQ_SNOWHARE_LOOKAROUND",SEQ_SNOWHARE_LOOKAROUND);
  tolua_constant(tolua_S,"SEQ_SNOWHARE_TEMPT",SEQ_SNOWHARE_TEMPT);
  tolua_constant(tolua_S,"SEQ_SNOWHARE_HIDE",SEQ_SNOWHARE_HIDE);
  tolua_constant(tolua_S,"SEQ_IB_ATTACK1",SEQ_IB_ATTACK1);
  tolua_constant(tolua_S,"SEQ_IB_FMOVE_BEGIN",SEQ_IB_FMOVE_BEGIN);
  tolua_constant(tolua_S,"SEQ_IB_FMOVE_LOOP",SEQ_IB_FMOVE_LOOP);
  tolua_constant(tolua_S,"SEQ_IB_FMOVE_END",SEQ_IB_FMOVE_END);
  tolua_constant(tolua_S,"SEQ_IB_ICEROCK",SEQ_IB_ICEROCK);
  tolua_constant(tolua_S,"SEQ_IB_ICEBALL",SEQ_IB_ICEBALL);
  tolua_constant(tolua_S,"SEQ_IB_AWAKE",SEQ_IB_AWAKE);
  tolua_constant(tolua_S,"SEQ_IB_IDLE2",SEQ_IB_IDLE2);
  tolua_constant(tolua_S,"SEQ_IB_ATTACK2",SEQ_IB_ATTACK2);
  tolua_constant(tolua_S,"SEQ_IB_JUMP_BEGIN",SEQ_IB_JUMP_BEGIN);
  tolua_constant(tolua_S,"SEQ_IB_JUMP_LOOP",SEQ_IB_JUMP_LOOP);
  tolua_constant(tolua_S,"SEQ_IB_JUMP_END",SEQ_IB_JUMP_END);
  tolua_constant(tolua_S,"SEQ_IB_ICEROCK2",SEQ_IB_ICEROCK2);
  tolua_constant(tolua_S,"SEQ_IB_ICEBALL2",SEQ_IB_ICEBALL2);
  tolua_constant(tolua_S,"SEQ_IB_SKILL1_BEGIN",SEQ_IB_SKILL1_BEGIN);
  tolua_constant(tolua_S,"SEQ_IB_SKILL1_LOOP",SEQ_IB_SKILL1_LOOP);
  tolua_constant(tolua_S,"SEQ_IB_BEATEN2",SEQ_IB_BEATEN2);
  tolua_constant(tolua_S,"SEQ_IB_SLEEP",SEQ_IB_SLEEP);
  tolua_constant(tolua_S,"SEQ_IB_JUMPIN",SEQ_IB_JUMPIN);
  tolua_constant(tolua_S,"SEQ_IB_JUMPOUT",SEQ_IB_JUMPOUT);
  tolua_constant(tolua_S,"SEQ_IB_ATTACK2_B",SEQ_IB_ATTACK2_B);
  tolua_constant(tolua_S,"SEQ_IB_ATTACK2_L",SEQ_IB_ATTACK2_L);
  tolua_constant(tolua_S,"SEQ_IB_ATTACK2_E",SEQ_IB_ATTACK2_E);
  tolua_constant(tolua_S,"SEQ_IB_SKILL2",SEQ_IB_SKILL2);
  tolua_constant(tolua_S,"SEQ_IB_SKILL3",SEQ_IB_SKILL3);
  tolua_constant(tolua_S,"SEQ_IB_SKILL1_END",SEQ_IB_SKILL1_END);
  tolua_constant(tolua_S,"SEQ_IB_INJURED1",SEQ_IB_INJURED1);
  tolua_constant(tolua_S,"SEQ_IB_INJURED2",SEQ_IB_INJURED2);
  tolua_constant(tolua_S,"SEQ_COMBOATK_IDLE_BODY",SEQ_COMBOATK_IDLE_BODY);
  tolua_constant(tolua_S,"SEQ_COMBOATK_MOVE_BODY",SEQ_COMBOATK_MOVE_BODY);
  tolua_constant(tolua_S,"SEQ_PIANO_PLAY",SEQ_PIANO_PLAY);
  tolua_constant(tolua_S,"SEQ_TRANSFER",SEQ_TRANSFER);
  tolua_constant(tolua_S,"SEQ_IB_ATTACK2_F",SEQ_IB_ATTACK2_F);
  tolua_constant(tolua_S,"SEQ_HUNT_ATTACK",SEQ_HUNT_ATTACK);
  tolua_constant(tolua_S,"SEQ_RAISE_SHIELD",SEQ_RAISE_SHIELD);
  tolua_constant(tolua_S,"SEQ_BROKEN_TOUGHNESS",SEQ_BROKEN_TOUGHNESS);
  tolua_constant(tolua_S,"SEQ_DOUBLEWEAPON_DIGING",SEQ_DOUBLEWEAPON_DIGING);
  tolua_constant(tolua_S,"SEQ_VACANTVORTEX_CREATE",SEQ_VACANTVORTEX_CREATE);
  tolua_constant(tolua_S,"SEQ_VACANTVORTEX_END",SEQ_VACANTVORTEX_END);
  tolua_constant(tolua_S,"SEQ_DUDU_THROB",SEQ_DUDU_THROB);
  tolua_constant(tolua_S,"SEQ_ELK_PANIC",SEQ_ELK_PANIC);
  tolua_constant(tolua_S,"SEQ_FOX_FIERCE",SEQ_FOX_FIERCE);
  tolua_constant(tolua_S,"SEQ_LEFTRAISE_SHIELD",SEQ_LEFTRAISE_SHIELD);
  tolua_constant(tolua_S,"SEQ_ATTTACT_POWER",SEQ_ATTTACT_POWER);
  tolua_constant(tolua_S,"SEQ_ATTTACT_BOMB",SEQ_ATTTACT_BOMB);
  tolua_constant(tolua_S,"SEQ_REVIVE_BASIC",SEQ_REVIVE_BASIC);
  tolua_constant(tolua_S,"SEQ_FOLLOW_MOVE_BODY",SEQ_FOLLOW_MOVE_BODY);
  tolua_constant(tolua_S,"SEQ_FOLLOW_MOVE_BODY1",SEQ_FOLLOW_MOVE_BODY1);
  tolua_constant(tolua_S,"SEQ_CLIMB",SEQ_CLIMB);
  tolua_constant(tolua_S,"SEQ_MOB_FLY",SEQ_MOB_FLY);
  tolua_constant(tolua_S,"SEQ_UI_STAND",SEQ_UI_STAND);
  tolua_constant(tolua_S,"SEQ_SNEAK_IDLE",SEQ_SNEAK_IDLE);
  tolua_constant(tolua_S,"SEQ_SNEAK_WALK",SEQ_SNEAK_WALK);
  tolua_constant(tolua_S,"SEQ_CRAWL_FORWARD",SEQ_CRAWL_FORWARD);
  tolua_constant(tolua_S,"SEQ_CRAWL_BACKWARD",SEQ_CRAWL_BACKWARD);
  tolua_constant(tolua_S,"SEQ_CRAWL_LEFT",SEQ_CRAWL_LEFT);
  tolua_constant(tolua_S,"SEQ_CRAWL_RIGHT",SEQ_CRAWL_RIGHT);
  tolua_constant(tolua_S,"SEQ_LAYDOWN_IDLE",SEQ_LAYDOWN_IDLE);
  tolua_constant(tolua_S,"SEQ_TOOL_UPBODY_IDLE",SEQ_TOOL_UPBODY_IDLE);
  tolua_constant(tolua_S,"SEQ_TOOL_UPBODY_WALK",SEQ_TOOL_UPBODY_WALK);
  tolua_constant(tolua_S,"SEQ_TOOL_UPBODY_RUN",SEQ_TOOL_UPBODY_RUN);
  tolua_constant(tolua_S,"MAX_SEQ",MAX_SEQ);
  tolua_function(tolua_S,"ComposePlayerIndex",tolua_miniSandboxEngineActor_ComposePlayerIndex00);
  #ifdef __cplusplus
  tolua_cclass(tolua_S,"ActorComponentBase","ActorComponentBase","MNSandbox::Component",tolua_collect_ActorComponentBase);
#ifdef SANDBOX_BIND_LUATYPE
  SANDBOX_BIND_LUATYPE(ActorComponentBase, "ActorComponentBase", "ActorComponentBase", "MNSandbox::Component")
#endif
  #else
  tolua_cclass(tolua_S,"ActorComponentBase","ActorComponentBase","MNSandbox::Component",NULL);
#ifdef SANDBOX_BIND_LUATYPE
  SANDBOX_BIND_LUATYPE(ActorComponentBase, "ActorComponentBase", "ActorComponentBase", "MNSandbox::Component")
#endif
  #endif
  tolua_beginmodule(tolua_S,"ActorComponentBase");
   tolua_function(tolua_S,"Produce",tolua_miniSandboxEngineActor_ActorComponentBase_Produce00);
   tolua_function(tolua_S,"new",tolua_miniSandboxEngineActor_ActorComponentBase_new00);
   tolua_function(tolua_S,"new_local",tolua_miniSandboxEngineActor_ActorComponentBase_new00_local);
   tolua_function(tolua_S,".call",tolua_miniSandboxEngineActor_ActorComponentBase_new00_local);
   tolua_function(tolua_S,"delete",tolua_miniSandboxEngineActor_ActorComponentBase_delete00);
   tolua_function(tolua_S,"OnTick",tolua_miniSandboxEngineActor_ActorComponentBase_OnTick00);
   tolua_function(tolua_S,"OnUpdate",tolua_miniSandboxEngineActor_ActorComponentBase_OnUpdate00);
   tolua_function(tolua_S,"GetOwnerActor",tolua_miniSandboxEngineActor_ActorComponentBase_GetOwnerActor00);
   tolua_function(tolua_S,"GetOwnerMob",tolua_miniSandboxEngineActor_ActorComponentBase_GetOwnerMob00);
   tolua_function(tolua_S,"GetOwnerPlayer",tolua_miniSandboxEngineActor_ActorComponentBase_GetOwnerPlayer00);
   tolua_function(tolua_S,"GetOwnerActorLiving",tolua_miniSandboxEngineActor_ActorComponentBase_GetOwnerActorLiving00);
  tolua_endmodule(tolua_S);
  tolua_constant(tolua_S,"OBJ_TYPE_EXPORB",OBJ_TYPE_EXPORB);
  tolua_constant(tolua_S,"OBJ_TYPE_ARROW",OBJ_TYPE_ARROW);
  tolua_constant(tolua_S,"OBJ_TYPE_TNT",OBJ_TYPE_TNT);
  tolua_constant(tolua_S,"OBJ_TYPE_FALLSAND",OBJ_TYPE_FALLSAND);
  tolua_constant(tolua_S,"OBJ_TYPE_FLYBLOCK",OBJ_TYPE_FLYBLOCK);
  tolua_constant(tolua_S,"OBJ_TYPE_VALUE",OBJ_TYPE_VALUE);
  tolua_constant(tolua_S,"OBJ_TYPE_STRING",OBJ_TYPE_STRING);
  tolua_constant(tolua_S,"OBJ_TYPE_PISTON",OBJ_TYPE_PISTON);
  tolua_constant(tolua_S,"OBJ_TYPE_THROWABLE",OBJ_TYPE_THROWABLE);
  tolua_constant(tolua_S,"OBJ_TYPE_ENDEREYE",OBJ_TYPE_ENDEREYE);
  tolua_constant(tolua_S,"OBJ_TYPE_MOBSPAWNER",OBJ_TYPE_MOBSPAWNER);
  tolua_constant(tolua_S,"OBJ_TYPE_SIGNS",OBJ_TYPE_SIGNS);
  tolua_constant(tolua_S,"OBJ_TYPE_FIREWORK",OBJ_TYPE_FIREWORK);
  tolua_constant(tolua_S,"OBJ_TYPE_BOAT",OBJ_TYPE_BOAT);
  tolua_constant(tolua_S,"OBJ_TYPE_FUNNEL",OBJ_TYPE_FUNNEL);
  tolua_constant(tolua_S,"OBJ_TYPE_EMITTER",OBJ_TYPE_EMITTER);
  tolua_constant(tolua_S,"OBJ_TYPE_HORSE",OBJ_TYPE_HORSE);
  tolua_constant(tolua_S,"OBJ_TYPE_EFFECT",OBJ_TYPE_EFFECT);
  tolua_constant(tolua_S,"OBJ_TYPE_ITEMEXPO",OBJ_TYPE_ITEMEXPO);
  tolua_constant(tolua_S,"OBJ_TYPE_COBBLE",OBJ_TYPE_COBBLE);
  tolua_constant(tolua_S,"OBJ_TYPE_OTHERPROJECTILE",OBJ_TYPE_OTHERPROJECTILE);
  tolua_constant(tolua_S,"OBJ_TYPE_MECHA_UNIT",OBJ_TYPE_MECHA_UNIT);
  tolua_constant(tolua_S,"OBJ_TYPE_MECHA_DRIVER",OBJ_TYPE_MECHA_DRIVER);
  tolua_constant(tolua_S,"OBJ_TYPE_BOSS",OBJ_TYPE_BOSS);
  tolua_constant(tolua_S,"OBJ_TYPE_FIREBALL",OBJ_TYPE_FIREBALL);
  tolua_constant(tolua_S,"OBJ_TYPE_SENSOR",OBJ_TYPE_SENSOR);
  tolua_constant(tolua_S,"OBJ_TYPE_RAILKNOT",OBJ_TYPE_RAILKNOT);
  tolua_constant(tolua_S,"OBJ_TYPE_THROWBLOCK",OBJ_TYPE_THROWBLOCK);
  tolua_constant(tolua_S,"OBJ_TYPE_ROCKET",OBJ_TYPE_ROCKET);
  tolua_constant(tolua_S,"OBJ_TYPE_RADIOUNIT",OBJ_TYPE_RADIOUNIT);
  tolua_constant(tolua_S,"OBJ_TYPE_INTERPRETERUNIT",OBJ_TYPE_INTERPRETERUNIT);
  tolua_constant(tolua_S,"OBJ_TYPE_BLOCK_LASER",OBJ_TYPE_BLOCK_LASER);
  tolua_constant(tolua_S,"OBJ_TYPE_GIANT",OBJ_TYPE_GIANT);
  tolua_constant(tolua_S,"OBJ_TYPE_DRAGON",OBJ_TYPE_DRAGON);
  tolua_constant(tolua_S,"OBJ_TYPE_FLYMONSTER",OBJ_TYPE_FLYMONSTER);
  tolua_constant(tolua_S,"OBJ_TYPE_AQUATICMONSTER",OBJ_TYPE_AQUATICMONSTER);
  tolua_constant(tolua_S,"OBJ_TYPE_REGIONREPLICATOR",OBJ_TYPE_REGIONREPLICATOR);
  tolua_constant(tolua_S,"OBJ_TYPE_BUILDBLUEPRINT",OBJ_TYPE_BUILDBLUEPRINT);
  tolua_constant(tolua_S,"OBJ_TYPE_LASER",OBJ_TYPE_LASER);
  tolua_constant(tolua_S,"OBJ_TYPE_HOOK",OBJ_TYPE_HOOK);
  tolua_constant(tolua_S,"OBJ_TYPE_COLLIDER",OBJ_TYPE_COLLIDER);
  tolua_constant(tolua_S,"OBJ_TYPE_VEHICLE",OBJ_TYPE_VEHICLE);
  tolua_constant(tolua_S,"OBJ_TYPE_MODELCRAFT",OBJ_TYPE_MODELCRAFT);
  tolua_constant(tolua_S,"OBJ_TYPE_WORKSHOP",OBJ_TYPE_WORKSHOP);
  tolua_constant(tolua_S,"OBJ_TYPE_TRANSFER",OBJ_TYPE_TRANSFER);
  tolua_constant(tolua_S,"OBJ_TYPE_BOOKEDITORTABLE",OBJ_TYPE_BOOKEDITORTABLE);
  tolua_constant(tolua_S,"OBJ_TYPE_WHEEL",OBJ_TYPE_WHEEL);
  tolua_constant(tolua_S,"OBJ_TYPE_FULLYCUSTOMMODEL",OBJ_TYPE_FULLYCUSTOMMODEL);
  tolua_constant(tolua_S,"OBJ_TYPE_SHAPESHIFT_HORSE",OBJ_TYPE_SHAPESHIFT_HORSE);
  tolua_constant(tolua_S,"OBJ_TYPE_ACTIONER",OBJ_TYPE_ACTIONER);
  tolua_constant(tolua_S,"OBJ_TYPE_DRIVERSEAT",OBJ_TYPE_DRIVERSEAT);
  tolua_constant(tolua_S,"OBJ_TYPE_DRAGON_MOUNT",OBJ_TYPE_DRAGON_MOUNT);
  tolua_constant(tolua_S,"OBJ_TYPE_ARM_PRISMATIC",OBJ_TYPE_ARM_PRISMATIC);
  tolua_constant(tolua_S,"OBJ_TYPE_ARM_SPHERIAL",OBJ_TYPE_ARM_SPHERIAL);
  tolua_constant(tolua_S,"OBJ_TYPE_VILLAGER",OBJ_TYPE_VILLAGER);
  tolua_constant(tolua_S,"OBJ_TYPE_VILLAGESOUVENIR",OBJ_TYPE_VILLAGESOUVENIR);
  tolua_constant(tolua_S,"OBJ_TYPE_IMPORTMODEL",OBJ_TYPE_IMPORTMODEL);
  tolua_constant(tolua_S,"OBJ_TYPE_MOON_MOUNT",OBJ_TYPE_MOON_MOUNT);
  tolua_constant(tolua_S,"OBJ_TYPE_VACANTBOSS",OBJ_TYPE_VACANTBOSS);
  tolua_constant(tolua_S,"OBJ_TYPE_ALTAR",OBJ_TYPE_ALTAR);
  tolua_constant(tolua_S,"OBJ_TYPE_ONEQUARTERBLOCK",OBJ_TYPE_ONEQUARTERBLOCK);
  tolua_constant(tolua_S,"OBJ_TYPE_FLYSNAKEGOD",OBJ_TYPE_FLYSNAKEGOD);
  tolua_constant(tolua_S,"OBJ_TYPE_SKIN_NPC",OBJ_TYPE_SKIN_NPC);
  tolua_constant(tolua_S,"OBJ_TYPE_DOUDU_MOUNT",OBJ_TYPE_DOUDU_MOUNT);
  tolua_constant(tolua_S,"OBJ_TYPE_HOMELAND_LIVES",OBJ_TYPE_HOMELAND_LIVES);
  tolua_constant(tolua_S,"OBJ_TYPE_HOMELAND_PRAY",OBJ_TYPE_HOMELAND_PRAY);
  tolua_constant(tolua_S,"OBJ_TYPE_HOMELAND_NPC",OBJ_TYPE_HOMELAND_NPC);
  tolua_constant(tolua_S,"OBJ_TYPE_HOMELANT_PLANT",OBJ_TYPE_HOMELANT_PLANT);
  tolua_constant(tolua_S,"OBJ_TYPE_HONOR_FRAME",OBJ_TYPE_HONOR_FRAME);
  tolua_constant(tolua_S,"OBJ_TYPE_ACTOR_PET",OBJ_TYPE_ACTOR_PET);
  tolua_constant(tolua_S,"OBJ_TYPE_KEY_PEDESTAL",OBJ_TYPE_KEY_PEDESTAL);
  tolua_constant(tolua_S,"OBJ_TYPE_FARMLAND",OBJ_TYPE_FARMLAND);
  tolua_constant(tolua_S,"OBJ_TYPE_STARSTATION_TRANSFER_CONSOLE",OBJ_TYPE_STARSTATION_TRANSFER_CONSOLE);
  tolua_constant(tolua_S,"OBJ_TYPE_STARSTATION_TRANSFER_CABIN",OBJ_TYPE_STARSTATION_TRANSFER_CABIN);
  tolua_constant(tolua_S,"OBJ_TYPE_FEED_TROUGH",OBJ_TYPE_FEED_TROUGH);
  tolua_constant(tolua_S,"OBJ_TYPE_PERISTELE",OBJ_TYPE_PERISTELE);
  tolua_constant(tolua_S,"OBJ_TYPE_PUMPKIN_HORSE",OBJ_TYPE_PUMPKIN_HORSE);
  tolua_constant(tolua_S,"OBJ_TYPE_TRIXENIE",OBJ_TYPE_TRIXENIE);
  tolua_constant(tolua_S,"OBJ_TYPE_COAGULATION",OBJ_TYPE_COAGULATION);
  tolua_constant(tolua_S,"OBJ_TYPE_KEYEFFECT",OBJ_TYPE_KEYEFFECT);
  tolua_constant(tolua_S,"OBJ_TYPE_MINCLUB",OBJ_TYPE_MINCLUB);
  tolua_constant(tolua_S,"OBJ_TYPE_SANDWORM",OBJ_TYPE_SANDWORM);
  tolua_constant(tolua_S,"OBJ_TYPE_DESERTBUSINESSMAN",OBJ_TYPE_DESERTBUSINESSMAN);
  tolua_constant(tolua_S,"OBJ_TYPE_DESERTBUSINESSMANGUARD",OBJ_TYPE_DESERTBUSINESSMANGUARD);
  tolua_constant(tolua_S,"OBJ_TYPE_DESERTVILLAGER",OBJ_TYPE_DESERTVILLAGER);
  tolua_constant(tolua_S,"OBJ_TYPE_PACKHORSE",OBJ_TYPE_PACKHORSE);
  tolua_constant(tolua_S,"OBJ_TYPE_SANDMAN",OBJ_TYPE_SANDMAN);
  tolua_constant(tolua_S,"OBJ_TYPE_VENOM",OBJ_TYPE_VENOM);
  tolua_constant(tolua_S,"OBJ_TYPE_SEASPIRITGUARDING",OBJ_TYPE_SEASPIRITGUARDING);
  tolua_constant(tolua_S,"OBJ_TYPE_FISHINGVILLAGER",OBJ_TYPE_FISHINGVILLAGER);
  tolua_constant(tolua_S,"OBJ_TYPE_FISHERMAN",OBJ_TYPE_FISHERMAN);
  tolua_constant(tolua_S,"OBJ_TYPE_POSEIDONSTATUE",OBJ_TYPE_POSEIDONSTATUE);
  tolua_constant(tolua_S,"OBJ_TYPE_THORNBALL",OBJ_TYPE_THORNBALL);
  tolua_constant(tolua_S,"OBJ_TYPE_COCONUT_PRO",OBJ_TYPE_COCONUT_PRO);
  tolua_constant(tolua_S,"OBJ_TYPE_CRAB",OBJ_TYPE_CRAB);
  tolua_constant(tolua_S,"OBJ_TYPE_HIPPOCAMPUS",OBJ_TYPE_HIPPOCAMPUS);
  tolua_constant(tolua_S,"OBJ_TYPE_SMALL_HIPPOCAMPUS",OBJ_TYPE_SMALL_HIPPOCAMPUS);
  tolua_constant(tolua_S,"OBJ_TYPE_HIPPOCAMPUS_HORSE",OBJ_TYPE_HIPPOCAMPUS_HORSE);
  tolua_constant(tolua_S,"OBJ_TYPE_PIRATE_SHIP",OBJ_TYPE_PIRATE_SHIP);
  tolua_constant(tolua_S,"OBJ_TYPE_COCONUT",OBJ_TYPE_COCONUT);
  tolua_constant(tolua_S,"OBJ_TYPE_SHOW_OMOD",OBJ_TYPE_SHOW_OMOD);
  tolua_constant(tolua_S,"OBJ_TYPE_GIANT_SCALLOPS",OBJ_TYPE_GIANT_SCALLOPS);
  tolua_constant(tolua_S,"OBJ_TYPE_PEARL",OBJ_TYPE_PEARL);
  tolua_constant(tolua_S,"OBJ_TYPE_FISHHOOK",OBJ_TYPE_FISHHOOK);
  tolua_constant(tolua_S,"OBJ_TYPE_COLORPALETTE",OBJ_TYPE_COLORPALETTE);
  tolua_constant(tolua_S,"OBJ_TYPE_SOLIDSAND",OBJ_TYPE_SOLIDSAND);
  tolua_constant(tolua_S,"OBJ_TYPE_FISHFRAME",OBJ_TYPE_FISHFRAME);
  tolua_constant(tolua_S,"OBJ_TYPE_SMALL_TORCH",OBJ_TYPE_SMALL_TORCH);
  tolua_constant(tolua_S,"OBJ_TYPE_POPULUS_LEAF",OBJ_TYPE_POPULUS_LEAF);
  tolua_constant(tolua_S,"OBJ_TYPE_LIGHTMUSHROOM",OBJ_TYPE_LIGHTMUSHROOM);
  tolua_constant(tolua_S,"OBJ_TYPE_KEYDOOR",OBJ_TYPE_KEYDOOR);
  tolua_constant(tolua_S,"OBJ_TYPE_COLLECTING_PIPE",OBJ_TYPE_COLLECTING_PIPE);
  tolua_constant(tolua_S,"OBJ_TYPE_PORTAL",OBJ_TYPE_PORTAL);
  tolua_constant(tolua_S,"OBJ_TYPE_SNOWMAN",OBJ_TYPE_SNOWMAN);
  tolua_constant(tolua_S,"OBJ_TYPE_ICECRYSTALSHROOM",OBJ_TYPE_ICECRYSTALSHROOM);
  tolua_constant(tolua_S,"OBJ_TYPE_SNOWBALL",OBJ_TYPE_SNOWBALL);
  tolua_constant(tolua_S,"OBJ_TYPE_SCHOOLFENCE",OBJ_TYPE_SCHOOLFENCE);
  tolua_constant(tolua_S,"OBJ_TYPE_FUSIONCAGE",OBJ_TYPE_FUSIONCAGE);
  tolua_constant(tolua_S,"OBJ_TYPE_VORTEX",OBJ_TYPE_VORTEX);
  tolua_constant(tolua_S,"OBJ_TYPE_JAR",OBJ_TYPE_JAR);
  tolua_constant(tolua_S,"OBJ_TYPE_TRAVELING_TRADER_NPC",OBJ_TYPE_TRAVELING_TRADER_NPC);
  tolua_constant(tolua_S,"OBJ_TYPE_STARSTATION_CARGO",OBJ_TYPE_STARSTATION_CARGO);
  tolua_constant(tolua_S,"OBJ_TYPE_TALKINGSTATUE",OBJ_TYPE_TALKINGSTATUE);
  tolua_constant(tolua_S,"OBJ_TYPE_COMPUTER",OBJ_TYPE_COMPUTER);
  tolua_constant(tolua_S,"OBJ_TYPE_MONSTERSUMMONER",OBJ_TYPE_MONSTERSUMMONER);
  tolua_constant(tolua_S,"OBJ_TYPE_MOD_CONTAINER",OBJ_TYPE_MOD_CONTAINER);
  tolua_constant(tolua_S,"OBJ_TYPE_MOD_CONTAINER_TRANSFER",OBJ_TYPE_MOD_CONTAINER_TRANSFER);
  tolua_constant(tolua_S,"OBJ_TYPE_STORAGEBOXHORSE",OBJ_TYPE_STORAGEBOXHORSE);
  tolua_constant(tolua_S,"OBJ_TYPE_PLAYERCORPSE",OBJ_TYPE_PLAYERCORPSE);
  tolua_constant(tolua_S,"OBJ_TYPE_EROSION_CONTAINER",OBJ_TYPE_EROSION_CONTAINER);
  tolua_constant(tolua_S,"OBJ_TYPE_CUBECHEST",OBJ_TYPE_CUBECHEST);
  tolua_constant(tolua_S,"OBJ_TYPE_AIR_PLANE",OBJ_TYPE_AIR_PLANE);
  tolua_constant(tolua_S,"OBJ_TYPE_ARCHITECTURE",OBJ_TYPE_ARCHITECTURE);
  tolua_constant(tolua_S,"OBJ_TYPE_ELECTRIC_MACHINE",OBJ_TYPE_ELECTRIC_MACHINE);
  tolua_constant(tolua_S,"OBJ_TYPE_TORCH_CONTAINER",OBJ_TYPE_TORCH_CONTAINER);
  tolua_constant(tolua_S,"OBJ_TYPE_GAMEOBJECT",OBJ_TYPE_GAMEOBJECT);
  tolua_constant(tolua_S,"TriggerEffect_Player",TriggerEffect_Player);
  tolua_constant(tolua_S,"TriggerEffect_Mob",TriggerEffect_Mob);
  tolua_constant(tolua_S,"TriggerEffect_Projectile",TriggerEffect_Projectile);
  tolua_constant(tolua_S,"TriggerEffect_DropItem",TriggerEffect_DropItem);
  tolua_constant(tolua_S,"ACTORFLAG_ONGROUND",ACTORFLAG_ONGROUND);
  tolua_constant(tolua_S,"ACTORFLAG_PERSISTENCE",ACTORFLAG_PERSISTENCE);
  tolua_constant(tolua_S,"ACTORFLAG_SNEAK",ACTORFLAG_SNEAK);
  tolua_constant(tolua_S,"ACTORFLAG_FLY",ACTORFLAG_FLY);
  tolua_constant(tolua_S,"ACTORFLAG_INFUSE",ACTORFLAG_INFUSE);
  tolua_constant(tolua_S,"ACTORFLAG_SHEARED",ACTORFLAG_SHEARED);
  tolua_constant(tolua_S,"ACTORFLAG_RUN",ACTORFLAG_RUN);
  tolua_constant(tolua_S,"ACTORFLAG_SLEEP",ACTORFLAG_SLEEP);
  tolua_constant(tolua_S,"ACTORFLAG_SIT",ACTORFLAG_SIT);
  tolua_constant(tolua_S,"ACTORFLAG_AI_SIT",ACTORFLAG_AI_SIT);
  tolua_constant(tolua_S,"ACTORFLAG_AI_SLEEP",ACTORFLAG_AI_SLEEP);
  tolua_constant(tolua_S,"ACTORFLAG_AI_MATE",ACTORFLAG_AI_MATE);
  tolua_constant(tolua_S,"ACTORFLAG_AI_EAT",ACTORFLAG_AI_EAT);
  tolua_constant(tolua_S,"ACTORFLAG_AI_DANCING",ACTORFLAG_AI_DANCING);
  tolua_constant(tolua_S,"ACTORFLAG_AI_MILKING",ACTORFLAG_AI_MILKING);
  tolua_constant(tolua_S,"ACTORFLAG_AI_SIT_COMMAND",ACTORFLAG_AI_SIT_COMMAND);
  tolua_constant(tolua_S,"ACTORFLAG_AI_TOPPLEOVER",ACTORFLAG_AI_TOPPLEOVER);
  tolua_constant(tolua_S,"ACTORFLAG_AI_SCROLL",ACTORFLAG_AI_SCROLL);
  tolua_constant(tolua_S,"ACTORFLAG_AI_CONCEAL",ACTORFLAG_AI_CONCEAL);
  tolua_constant(tolua_S,"ACTORFLAG_AI_BREATHE",ACTORFLAG_AI_BREATHE);
  tolua_constant(tolua_S,"ACTORFLAG_AI_HOLD",ACTORFLAG_AI_HOLD);
  tolua_constant(tolua_S,"ACTORFLAG_AI_BUMP",ACTORFLAG_AI_BUMP);
  tolua_constant(tolua_S,"ACTORFLAG_AI_HUNGERSIT",ACTORFLAG_AI_HUNGERSIT);
  tolua_constant(tolua_S,"ACTORFLAG_AI_DIE",ACTORFLAG_AI_DIE);
  tolua_constant(tolua_S,"ACTORFLAG_AI_NPCSLEEP",ACTORFLAG_AI_NPCSLEEP);
  tolua_constant(tolua_S,"ACTORFLAG_AI_TIRED",ACTORFLAG_AI_TIRED);
  tolua_constant(tolua_S,"ACTORFLAG_AI_CRAFT",ACTORFLAG_AI_CRAFT);
  tolua_constant(tolua_S,"ACTORFLAG_FLOATAGE",ACTORFLAG_FLOATAGE);
  tolua_constant(tolua_S,"ACTORFLAG_AI_WARNING",ACTORFLAG_AI_WARNING);
  tolua_constant(tolua_S,"ACTORFLAG_NOD",ACTORFLAG_NOD);
  tolua_constant(tolua_S,"ACTORFLAG_AI_ESCAPE",ACTORFLAG_AI_ESCAPE);
  tolua_constant(tolua_S,"ACTORFLAG_INTERACTIVE_CORPSE",ACTORFLAG_INTERACTIVE_CORPSE);
  tolua_constant(tolua_S,"ACTOR_ANIM_FLAG_NOD",ACTOR_ANIM_FLAG_NOD);
  tolua_constant(tolua_S,"ACTOR_ANIM_FLAG_EAT",ACTOR_ANIM_FLAG_EAT);
  tolua_constant(tolua_S,"ACTOR_ANIM_FLAG_SIT_BY_HUNGER",ACTOR_ANIM_FLAG_SIT_BY_HUNGER);
  tolua_constant(tolua_S,"ACTOR_ANIM_FLAG_STAND_BY_HUNGER",ACTOR_ANIM_FLAG_STAND_BY_HUNGER);
  tolua_constant(tolua_S,"ACTOR_ANIM_FLAG_FLEE",ACTOR_ANIM_FLAG_FLEE);
  tolua_constant(tolua_S,"ACTOR_ANIM_FLAG_SIT",ACTOR_ANIM_FLAG_SIT);
  tolua_constant(tolua_S,"ACTOR_ANIM_FLAG_DANCE",ACTOR_ANIM_FLAG_DANCE);
  tolua_constant(tolua_S,"ACTOR_ANIM_FLAG_SING",ACTOR_ANIM_FLAG_SING);
  tolua_constant(tolua_S,"ACTOR_ANIM_FLAG_CAY",ACTOR_ANIM_FLAG_CAY);
  tolua_constant(tolua_S,"ACTOR_ANIM_FLAG_RUB_HAND",ACTOR_ANIM_FLAG_RUB_HAND);
  tolua_constant(tolua_S,"ACTOR_ANIM_FLAG_TALK",ACTOR_ANIM_FLAG_TALK);
  tolua_constant(tolua_S,"ACTOR_ANIM_FLAG_SHOOT",ACTOR_ANIM_FLAG_SHOOT);
  tolua_constant(tolua_S,"ACTOR_ANIM_FLAG_THINK",ACTOR_ANIM_FLAG_THINK);
  tolua_constant(tolua_S,"ACTOR_ANIM_FLAG_STAND_SLEEP",ACTOR_ANIM_FLAG_STAND_SLEEP);
  tolua_constant(tolua_S,"ACTOR_ANIM_FLAG_WAKEUP",ACTOR_ANIM_FLAG_WAKEUP);
  tolua_constant(tolua_S,"ACTOR_ANIM_FLAG_DEFENSE",ACTOR_ANIM_FLAG_DEFENSE);
  tolua_constant(tolua_S,"ACTOR_ANIM_FLAG_LEFTDEFENSE",ACTOR_ANIM_FLAG_LEFTDEFENSE);
  tolua_constant(tolua_S,"LandTerrain",LandTerrain);
  tolua_constant(tolua_S,"WaterTerrain",WaterTerrain);
  tolua_constant(tolua_S,"LavaTerrain",LavaTerrain);
  tolua_constant(tolua_S,"ACTORMOVE_NORMAL",ACTORMOVE_NORMAL);
  tolua_constant(tolua_S,"ACTORMOVE_JUMP",ACTORMOVE_JUMP);
  tolua_cclass(tolua_S,"ActorAttribut","ActorAttribut","",NULL);
#ifdef SANDBOX_BIND_LUATYPE
  SANDBOX_BIND_LUATYPE(ActorAttribut, "ActorAttribut", "ActorAttribut", "")
#endif
  tolua_beginmodule(tolua_S,"ActorAttribut");
   tolua_variable(tolua_S,"MaxHP",tolua_get_ActorAttribut_MaxHP,tolua_set_ActorAttribut_MaxHP);
   tolua_variable(tolua_S,"NowHP",tolua_get_ActorAttribut_NowHP,tolua_set_ActorAttribut_NowHP);
   tolua_variable(tolua_S,"HPRecover",tolua_get_ActorAttribut_HPRecover,tolua_set_ActorAttribut_HPRecover);
   tolua_variable(tolua_S,"MaxHunger",tolua_get_ActorAttribut_MaxHunger,tolua_set_ActorAttribut_MaxHunger);
   tolua_variable(tolua_S,"NowHunger",tolua_get_ActorAttribut_NowHunger,tolua_set_ActorAttribut_NowHunger);
   tolua_variable(tolua_S,"MaxOxygen",tolua_get_ActorAttribut_MaxOxygen,tolua_set_ActorAttribut_MaxOxygen);
   tolua_variable(tolua_S,"NowOxygen",tolua_get_ActorAttribut_NowOxygen,tolua_set_ActorAttribut_NowOxygen);
   tolua_variable(tolua_S,"MoveSpeed",tolua_get_ActorAttribut_MoveSpeed,tolua_set_ActorAttribut_MoveSpeed);
   tolua_variable(tolua_S,"RunSpeed",tolua_get_ActorAttribut_RunSpeed,tolua_set_ActorAttribut_RunSpeed);
   tolua_variable(tolua_S,"SwimSpeed",tolua_get_ActorAttribut_SwimSpeed,tolua_set_ActorAttribut_SwimSpeed);
   tolua_variable(tolua_S,"JumpSpeed",tolua_get_ActorAttribut_JumpSpeed,tolua_set_ActorAttribut_JumpSpeed);
   tolua_variable(tolua_S,"Weight",tolua_get_ActorAttribut_Weight,tolua_set_ActorAttribut_Weight);
   tolua_variable(tolua_S,"SneakSpeed",tolua_get_ActorAttribut_SneakSpeed,tolua_set_ActorAttribut_SneakSpeed);
   tolua_variable(tolua_S,"Dodge",tolua_get_ActorAttribut_Dodge,tolua_set_ActorAttribut_Dodge);
   tolua_variable(tolua_S,"NearAttack",tolua_get_ActorAttribut_NearAttack,tolua_set_ActorAttribut_NearAttack);
   tolua_variable(tolua_S,"RemoteAttack",tolua_get_ActorAttribut_RemoteAttack,tolua_set_ActorAttribut_RemoteAttack);
   tolua_variable(tolua_S,"NearArmor",tolua_get_ActorAttribut_NearArmor,tolua_set_ActorAttribut_NearArmor);
   tolua_variable(tolua_S,"RemoteArmor",tolua_get_ActorAttribut_RemoteArmor,tolua_set_ActorAttribut_RemoteArmor);
   tolua_variable(tolua_S,"Dimension",tolua_get_ActorAttribut_Dimension,tolua_set_ActorAttribut_Dimension);
   tolua_variable(tolua_S,"Score",tolua_get_ActorAttribut_Score,tolua_set_ActorAttribut_Score);
   tolua_variable(tolua_S,"Level",tolua_get_ActorAttribut_Level,tolua_set_ActorAttribut_Level);
   tolua_variable(tolua_S,"LevelModeExp",tolua_get_ActorAttribut_LevelModeExp,tolua_set_ActorAttribut_LevelModeExp);
   tolua_variable(tolua_S,"LevelModeLevel",tolua_get_ActorAttribut_LevelModeLevel,tolua_set_ActorAttribut_LevelModeLevel);
   tolua_variable(tolua_S,"Strength",tolua_get_ActorAttribut_Strength,tolua_set_ActorAttribut_Strength);
   tolua_variable(tolua_S,"MaxStrength",tolua_get_ActorAttribut_MaxStrength,tolua_set_ActorAttribut_MaxStrength);
   tolua_variable(tolua_S,"StrengthRecover",tolua_get_ActorAttribut_StrengthRecover,tolua_set_ActorAttribut_StrengthRecover);
   tolua_variable(tolua_S,"ExtraHP",tolua_get_ActorAttribut_ExtraHP,tolua_set_ActorAttribut_ExtraHP);
   tolua_variable(tolua_S,"Armor",tolua_get_ActorAttribut_Armor,tolua_set_ActorAttribut_Armor);
  tolua_endmodule(tolua_S);
  tolua_cclass(tolua_S,"BodyEffectBrief","BodyEffectBrief","",NULL);
#ifdef SANDBOX_BIND_LUATYPE
  SANDBOX_BIND_LUATYPE(BodyEffectBrief, "BodyEffectBrief", "BodyEffectBrief", "")
#endif
  tolua_beginmodule(tolua_S,"BodyEffectBrief");
   tolua_variable(tolua_S,"effect_name",tolua_get_BodyEffectBrief_effect_name,tolua_set_BodyEffectBrief_effect_name);
   tolua_variable(tolua_S,"effect_id",tolua_get_BodyEffectBrief_effect_id,tolua_set_BodyEffectBrief_effect_id);
   tolua_variable(tolua_S,"effect_scale",tolua_get_BodyEffectBrief_effect_scale,tolua_set_BodyEffectBrief_effect_scale);
   tolua_variable(tolua_S,"effect_looptime",tolua_get_BodyEffectBrief_effect_looptime,tolua_set_BodyEffectBrief_effect_looptime);
  tolua_endmodule(tolua_S);
  tolua_cclass(tolua_S,"ActorManagerInterface","ActorManagerInterface","",NULL);
#ifdef SANDBOX_BIND_LUATYPE
  SANDBOX_BIND_LUATYPE(ActorManagerInterface, "ActorManagerInterface", "ActorManagerInterface", "")
#endif
  tolua_beginmodule(tolua_S,"ActorManagerInterface");
  tolua_endmodule(tolua_S);
 tolua_endmodule(tolua_S);
 return 1;
}


#if defined(LUA_VERSION_NUM) && LUA_VERSION_NUM >= 501
 TOLUA_API int luaopen_miniSandboxEngineActor (lua_State* tolua_S) {
 return tolua_miniSandboxEngineActor_open(tolua_S);
};
#endif

