#include "LinearTerrainIDGenerator.h"
#include "chunkrandom.h"
#include "BiomeRegionGenConfig.h"
#include "ChunkGenerator.h"
#include "PlayManagerInterface.h"
#include "world.h"
#include "LuaInterfaceProxy.h"

// #define DO_LOG_GENIDS
#ifdef DO_LOG_GENIDS
#define LOG_GENID(n) logGenIDs(n, retbuf, ox, oz, xlen, zlen)
#else
#define LOG_GENID(n)
#endif

#define NAME_MAX_LENGTH 256
#define COPY_MAX 25
#define NODE_CONNECT_MAX 4
#define MAP_SIZE_MAX 100
#define SCALE_ZOOM 4
#define LOG_SCALE_ZOOM 2

#define LINE_TERRAIN_DEBUG
#define INVALID_VALUE -1
#define INVALID_INDEX -2

class TGenUnitLine: public TerrIDGenUnit //填充
{
public:
	TGenUnitLine(UWorldSeed seed, int  mapid, int scale) : TerrIDGenUnit(seed, NULL, mapid), m_scale(scale)
	{

	}
	virtual void genTerrainIDs(std::vector<int>& retbuf, int ox, int oz, int xlen, int zlen, TerrainSpecialData* data);
	std::vector<TerrgenRange> m_Poss;
	int m_mapX = 0;
	int m_mapZ = 0;
	int m_scale = 1;
	ChunkIndex m_bottomIndex;
private:

};

void TGenUnitLine::genTerrainIDs(std::vector<int>& retbuf, int ox, int oz, int xlen, int zlen, TerrainSpecialData* data)
{
	//TerrainPosTrans(TerrainIDGenerator::m_curGen->m_icePlant, -m_edgeThickness * 2, 0);
	retbuf.resize(xlen * zlen, 0);
	int curX = 0;
	int curZ = 0;
	for (int x = 0; x < xlen; x++)
	{
		for (int z = 0; z < zlen; z++)
		{
			// curX = (ox + x)/m_scale - m_bottomIndex.x;
			// curZ = (oz + z)/m_scale - m_bottomIndex.z;
			curX = (ox + x) - m_bottomIndex.x;
			curZ = (oz + z) - m_bottomIndex.z;
			if (curX < 0 || curX >= m_mapX || curZ < 0 || curZ >= m_mapZ)
			{
				retbuf[z * xlen + x] = 0;
			}
			else
			{
				// 就在m_Poss里面找
				auto p = std::find_if(m_Poss.begin(), m_Poss.end(), [curX, curZ](const TerrgenRange& A)
					{
						return A.x == curX && A.z == curZ;
					});
				if (p != m_Poss.end())
				{
					retbuf[z * xlen + x] = p->biomeId;
				}
				else
				{
					retbuf[z * xlen + x] = 0;
				}
			}
		}
	}
	LOG_GENID("TGenUnitLine");
}

class SOCLandTGenUnit: public TerrIDGenUnit //填充
{
public:
	SOCLandTGenUnit(UWorldSeed seed, TerrIDGenUnit* parent, int  mapid) : TerrIDGenUnit(seed, parent, mapid){}
	virtual void genTerrainIDs(std::vector<int>& retbuf, int ox, int oz, int xlen, int zlen, TerrainSpecialData* data);

	int randomSel(int i, int j)
	{
		return randInt(2) == 0 ? i : j;
	}
public:
	int m_mapX = 0;
	int m_mapZ = 0;
	std::vector<LandSubBiomeData> m_LandSubBiomeDatas;
	ChunkIndex m_bottomIndex;
	std::vector<int> m_wholeChunksBiomeIds;
protected:
	void GenerateLandSubBiome(LandSubBiomeData& landSubBiomeData, std::vector<int>& retbuf, int ox, int oz, int xlen, int zlen, TerrainSpecialData* data);
	bool isIncludeSOCLandArea(int x, int z, int xlen, int zlen);
private:
	std::vector<int> m_totalBiomeIds;
};

void SOCLandTGenUnit::GenerateLandSubBiome(LandSubBiomeData& landSubBiomeData, std::vector<int>& retbuf, int ox, int oz, int xlen, int zlen, TerrainSpecialData* data)
{
	int startx = landSubBiomeData.bottomLeft.x;
	int startz = landSubBiomeData.bottomLeft.z;
	int maxx = landSubBiomeData.topRight.x;
	int maxz = landSubBiomeData.topRight.z;
	int biomeid = landSubBiomeData.biomeid;
	int secondBiomeid = landSubBiomeData.secondBiomeid;
	int scaleType = landSubBiomeData.scaleType;
	if (ox >= startx && ox <= (maxx - 1) && oz >= startz && oz <= (maxz - 1))
	{
		if (ox >= (startx + 1) && ox < (maxx - 1) && oz >= (startz + 1) && oz < (maxz - 1))
		{
			for (int x = 0; x < xlen; ++x)
			{
				for (int z = 0; z < zlen; ++z)
				{
					retbuf[z * xlen + x] = secondBiomeid;//randomSel(retbuf[z * xlen + x], tmpBiomeid);
				}
			}
		}
		else
		{
			for (auto& item : retbuf)
			{
				item = biomeid;
			}
		}
	}
	//else if (ox >= startx-1 && ox <= maxx && oz >= startz && oz <= maxz)
	//{
	//	if (biomeid == BIOME_CANYON)
	//	{
	//		biomeid = BIOME_CANYON_EAGE;
	//	}
	//	else if (biomeid == BIOME_EXTREMEHILLS)
	//	{
	//		biomeid = BIOME_EXTREMEHILLS_EDGE;
	//	}
	//	for (int x = 0; x < xlen; ++x)
	//	{
	//		for (int z = 0; z < zlen; ++z)
	//		{
	//			retbuf[z * xlen + x] = randomSel(retbuf[z * xlen + x], biomeid);
	//		}
	//	}
	//}

}

bool SOCLandTGenUnit::isIncludeSOCLandArea(int x, int z, int xlen, int zlen)
{
	for (int cx = x; cx < x + xlen; ++cx	)
	{
		for (int cz = z; cz < z + zlen; ++cz)
		{
			for (auto& item : m_LandSubBiomeDatas)
			{
				if (cx >= item.bottomLeft.x && cx < item.topRight.x && cz >= item.bottomLeft.z && cz < item.topRight.z)
				{
					return true;
				}
			}
		}
	}
	return false;
}

void SOCLandTGenUnit::genTerrainIDs(std::vector<int>& retbuf, int ox, int oz, int xlen, int zlen, TerrainSpecialData* data)
{
	// if (m_Parent) m_Parent->genTerrainIDs(retbuf, ox, oz, xlen, zlen, data);
	// if (m_LandSubBiomeDatas.size() > 0 && data)
	// {
	// 	//int realX = (ox + 2) >> 2/* - m_bottomIndex.x*/;
	// 	//int realZ = (oz + 2) >> 2/* - m_bottomIndex.z*/;
	// 	//if (xlen == 16 && zlen == 16)
	// 	//{
	// 	//	realX = ox / 16;
	// 	//	realZ = oz / 16;
	// 	//}
	// 	int realX = data->curChunkIdx.x;
	// 	int realZ = data->curChunkIdx.z;
	// 	for (auto& landSubBiomeData : m_LandSubBiomeDatas)
	// 	{
	// 		GenerateLandSubBiome(landSubBiomeData, retbuf, realX, realZ, xlen, zlen, data);
	// 	}
	// }
	if (m_wholeChunksBiomeIds.size() > 0)
	{
		if (isIncludeSOCLandArea(ox, oz, xlen, zlen))
		{
			retbuf.resize(xlen * zlen, BIOME_OCEAN);
			for (int x = 0; x < xlen; ++x)
			{
				for (int z = 0; z < zlen; ++z)
				{
					int curx = ox + x - m_bottomIndex.x;
					int curz = oz + z - m_bottomIndex.z;
					int idx = curz * m_mapX + curx;
					if (idx < 0 || idx >= m_wholeChunksBiomeIds.size())
					{
						retbuf[z * xlen + x] = BIOME_OCEAN;
					}
					else
					{
						retbuf[z * xlen + x] = m_wholeChunksBiomeIds[idx];
					}
				}
			}
			return;
		}
	}
	
	if (m_Parent) m_Parent->genTerrainIDs(retbuf, ox, oz, xlen, zlen, data);
	LOG_GENID("SOCLandTGenUnit");
}

/****************************************************************************************************/
LinearTerrainIDGenerator::LinearTerrainIDGenerator(UWorldSeed seed, TERRAIN_TYPE terrtype):TerrainIDGenerator(seed,terrtype), m_ChunkSeed(0), m_spaceTreeMgr(nullptr)
{
	{
		m_BaseSeed = 1000;
		m_BaseSeed *= m_BaseSeed * 6364136223846793005L + 1442695040888963407L;
		m_BaseSeed += 1000;
		m_BaseSeed *= m_BaseSeed * 6364136223846793005L + 1442695040888963407L;
		m_BaseSeed += 1000;
		m_BaseSeed *= m_BaseSeed * 6364136223846793005L + 1442695040888963407L;
		m_BaseSeed += 1000;
		m_WorldGenSeed = seed;
	}
	m_RootGen = nullptr;
	m_RootIndex = nullptr;
}


int LinearTerrainIDGenerator::getMapIndex(int x, int z)
{
	if (x < 0 || z < 0 || x >= m_mapX || z >= m_mapZ)
	{
		LOG_WARNING("LinearTerrainIDGenerator:getMapIndex error, x :%d, z: %d", x, z);
		assert(0);
		return INVALID_INDEX;
	}
	return z * m_mapX + x;
}

int LinearTerrainIDGenerator::getMapValue(int x, int z)
{
	int index = getMapIndex(x, z);
	if (index < 0 || index >= m_maps.size())
	{
		return INVALID_INDEX;
	}
	return m_maps[index];
}

void LinearTerrainIDGenerator::setMapValue(int x, int z, int v)
{
	int index = getMapIndex(x, z);
	if (index < 0 || index >= m_maps.size())
	{
		return;
	}
	m_maps[index] = v;
}

void LinearTerrainIDGenerator::initSubBiomeData(int mapSizeType)
{
	m_mSubBiomeDatas.clear();
	if (mapSizeType == MAP_ERROR_SIZE) return;
	ConstAtLua* lua_const = GetLuaInterfaceProxy().get_lua_const();
	std::vector<int> biomeCountList;
	// 之后的功能可以进行读取配置进行子地形数据生成
	if (mapSizeType == MAP_BIG_SIZE)
	{
		biomeCountList.push_back(lua_const->SocLargeMapSmallTerrain_plains_forest);
		biomeCountList.push_back(lua_const->SocLargeMapSmallTerrain_plains_foresthill);
		biomeCountList.push_back(lua_const->SocLargeMapSmallTerrain_plains_lake);
		biomeCountList.push_back(lua_const->SocLargeMapSmallTerrain_plains_canyon);

		biomeCountList.push_back(lua_const->SocLargeMapSmallTerrain_deserts_deserthill);
		biomeCountList.push_back(lua_const->SocLargeMapSmallTerrain_deserts_desertoasis);

		biomeCountList.push_back(lua_const->SocLargeMapSmallTerrain_iceplains_icemountains);
		biomeCountList.push_back(lua_const->SocLargeMapSmallTerrain_iceplains_frizzelake);
	}
	else if (mapSizeType == MAP_MIDDLE_SIZE)
	{
		biomeCountList.push_back(lua_const->SocMediumMapSmallTerrain_plains_forest);
		biomeCountList.push_back(lua_const->SocMediumMapSmallTerrain_plains_foresthill);
		biomeCountList.push_back(lua_const->SocMediumMapSmallTerrain_plains_lake);
		biomeCountList.push_back(lua_const->SocMediumMapSmallTerrain_plains_canyon);

		biomeCountList.push_back(lua_const->SocMediumMapSmallTerrain_deserts_deserthill);
		biomeCountList.push_back(lua_const->SocMediumMapSmallTerrain_deserts_desertoasis);

		biomeCountList.push_back(lua_const->SocMediumMapSmallTerrain_iceplains_icemountains);
		biomeCountList.push_back(lua_const->SocMediumMapSmallTerrain_iceplains_frizzelake);
	}
	else if (mapSizeType == MAP_SMALL_SIZE)
	{
		biomeCountList.push_back(lua_const->SocSmallMapSmallTerrain_plains_forest);
		biomeCountList.push_back(lua_const->SocSmallMapSmallTerrain_plains_foresthill);
		biomeCountList.push_back(lua_const->SocSmallMapSmallTerrain_plains_lake);
		biomeCountList.push_back(lua_const->SocSmallMapSmallTerrain_plains_canyon);

		biomeCountList.push_back(lua_const->SocSmallMapSmallTerrain_deserts_deserthill);
		biomeCountList.push_back(lua_const->SocSmallMapSmallTerrain_deserts_desertoasis);

		biomeCountList.push_back(lua_const->SocSmallMapSmallTerrain_iceplains_icemountains);
		biomeCountList.push_back(lua_const->SocSmallMapSmallTerrain_iceplains_frizzelake);
	}

	std::vector<SubBiomeDatas> subBiomeDatasPlains;
	SubBiomeDatas subBiomeDatasForest;
	subBiomeDatasForest.biomeids = { {BIOME_FOREST,BIOME_FOREST}, {BIOME_RAINFOREST, BIOME_RAINFOREST} };
	subBiomeDatasForest.count = biomeCountList[0];
	subBiomeDatasForest.secondBiomeType = 1;
	subBiomeDatasForest.xRange = 20;
	subBiomeDatasForest.zRange = 16;
	subBiomeDatasPlains.push_back(subBiomeDatasForest);

	SubBiomeDatas subBiomeDatasForestHills;
	subBiomeDatasForestHills.biomeids = { {BIOME_FOREST_HILLS, BIOME_FOREST_HILLS}, {BIOME_EXTREMEHILLS, BIOME_EXTREMEHILLS_EDGE}, {BIOME_JUNGLE_HILLS, BIOME_JUNGLE_HILLS} };
	subBiomeDatasForestHills.count = biomeCountList[1];
	subBiomeDatasForestHills.secondBiomeType = 2;
	subBiomeDatasForestHills.xRange = 12;
	subBiomeDatasForestHills.zRange = 12;
	subBiomeDatasPlains.push_back(subBiomeDatasForestHills);

	SubBiomeDatas subBiomeDatasPlainsLake;
	subBiomeDatasPlainsLake.biomeids = { {BIOME_PLAINS_LAKE, BIOME_PLAINS_LAKE}, {BIOME_FOREST_LAKE, BIOME_FOREST_LAKE}, {BIOME_RAINFOREST_LAKE, BIOME_RAINFOREST_LAKE} };
	subBiomeDatasPlainsLake.count = biomeCountList[2];
	subBiomeDatasPlainsLake.secondBiomeType = 3;
	subBiomeDatasPlainsLake.xRange = 20;
	subBiomeDatasPlainsLake.zRange = 16;
	subBiomeDatasPlains.push_back(subBiomeDatasPlainsLake);

	SubBiomeDatas subBiomeDatasCanyon;
	subBiomeDatasCanyon.biomeids = { {BIOME_CANYON, BIOME_CANYON_EAGE} };
	subBiomeDatasCanyon.count = biomeCountList[3];
	subBiomeDatasCanyon.secondBiomeType = 4;
	subBiomeDatasCanyon.xRange = 12;
	subBiomeDatasCanyon.zRange = 4;
	subBiomeDatasPlains.push_back(subBiomeDatasCanyon);

	m_mSubBiomeDatas[BIOME_PLAINS] = subBiomeDatasPlains;

	std::vector<SubBiomeDatas> subBiomeDatasDeserts;
	SubBiomeDatas subBiomeDatasDesertHills;
	subBiomeDatasDesertHills.biomeids = { {BIOME_DESERT_HILLS, BIOME_DESERT_HILLS} };
	subBiomeDatasDesertHills.count = biomeCountList[4];
	subBiomeDatasDesertHills.secondBiomeType = 5;
	subBiomeDatasDesertHills.xRange = 1;
	subBiomeDatasDesertHills.zRange = 6;
	subBiomeDatasDeserts.push_back(subBiomeDatasDesertHills);

	SubBiomeDatas subBiomeDatasDesertOasis;
	subBiomeDatasDesertOasis.biomeids = { {BIOME_DESERT_LAKE, BIOME_DESERT_LAKE} };
	subBiomeDatasDesertOasis.count = biomeCountList[5];
	subBiomeDatasDesertOasis.secondBiomeType = 6;
	subBiomeDatasDesertOasis.xRange = 8;
	subBiomeDatasDesertOasis.zRange = 8;
	subBiomeDatasDeserts.push_back(subBiomeDatasDesertOasis);

	m_mSubBiomeDatas[BIOME_DESERT] = subBiomeDatasDeserts;

	std::vector<SubBiomeDatas> subBiomeDatasIce;
	SubBiomeDatas subBiomeDatasIceMountains;
	subBiomeDatasIceMountains.biomeids = { {BIOME_ICE_MOUNTAINS, BIOME_ICE_PLAINS} };
	subBiomeDatasIceMountains.count = biomeCountList[6];
	subBiomeDatasIceMountains.secondBiomeType = 7;
	subBiomeDatasIceMountains.xRange = 16;
	subBiomeDatasIceMountains.zRange = 16;
	subBiomeDatasIce.push_back(subBiomeDatasIceMountains);

	SubBiomeDatas subBiomeDatasIceFrizzeLake;
	subBiomeDatasIceFrizzeLake.biomeids = { {BIOME_ICE_PLAINS_FRIZEB_LAKE, BIOME_ICE_PLAINS_FRIZEB_LAKE} };
	subBiomeDatasIceFrizzeLake.count = biomeCountList[7];
	subBiomeDatasIceFrizzeLake.secondBiomeType = 8;
	subBiomeDatasIceFrizzeLake.xRange = 8;
	subBiomeDatasIceFrizzeLake.zRange = 8;
	subBiomeDatasIce.push_back(subBiomeDatasIceFrizzeLake);

	m_mSubBiomeDatas[BIOME_ICE_PLAINS] = subBiomeDatasIce;
}

bool LinearTerrainIDGenerator::flatSpaceMapGen(int distXZ)
{
	// 以下是正常的地图生成逻辑，但现在使用缩放后的尺寸
	int needsize = (m_mapX - 2 * distXZ) * (m_mapZ - 2 * distXZ);
	m_nodes.resize(1);
	m_nodes[0].biomeid = BIOME_PLAINS;
	if (needsize > m_nodes.size())
	{
		m_nodes.resize(needsize, m_nodes[0]);
	}

	unsigned index = 0;
	for (int z = distXZ; z < m_mapZ - distXZ; ++z)
	{
		for (int x = distXZ; x < m_mapX - distXZ; ++x)
		{
			if (index < m_nodes.size())
			{
				auto& node = m_nodes[index];
				node.x = x;
				node.z = z;
				index++;
			}
		}
	}

	// 保护中心区域 x z 应当一致
	int landwidth = m_mapX - 2*distXZ;

	// 其他群落生成
	int biom1 = BIOME_DESERT;
	int biom2 = BIOME_ICE_PLAINS;

	// 计算特殊生物群系的数量，按照4:3:3的比例
	// 总面积为landwidth * landwidth，平原占4份，沙漠和冰原各占3份
	int totalParts = 5; // 6 + 2 + 2
	int specailcout = (needsize) / totalParts; // 沙漠和冰原各占3份

	int startX = 0;
	int startZ = 0;
	int biomdir = randInt(2);  // 群落展开方向  0 z轴 1 x轴
	if (biomdir == 0) // 从边缘X=0开始
	{
		startX = 0;
		startZ = randInt(landwidth);
	}
	else // 从边缘Z=0开始
	{
		startX = randInt(landwidth);
		startZ = 0;
	}

	LOG_INFO("Start Biom1  x:%d, z:%d biomdir:%d count%d =========", startX, startZ, biomdir, specailcout);
	GenerateSpecialBiome(startX, startZ, biom1, specailcout, landwidth, biomdir, 0);

	startX = landwidth - startX - 1;
	startZ = landwidth - startZ - 1;
	LOG_INFO("Start Biom2  x:%d, z:%d biomdir:%d count%d =========", startX, startZ, biomdir, specailcout);
	GenerateSpecialBiome(startX, startZ, biom2, specailcout, landwidth, biomdir, 1);

	// 对群落边缘和内部边界进行随机化处理
	LOG_INFO("开始随机化群落边缘...");
	RandomizeBiomeBorders(distXZ, landwidth);
	
	LOG_INFO("开始随机化内陆群落边界...");
	RandomizeBiomeInteriorBorders(distXZ, landwidth);

	// 保持原有的日志输出代码
	int idx = 0;
	LOG_INFO("flatSpaceMapGen---------------------------------------------------");
	for (int z = distXZ; z < m_mapZ - distXZ; ++z)
	{
		std::string s;
		for (int x = distXZ; x < m_mapX - distXZ; ++x)
		{
			auto& node = m_nodes[idx];
			idx++;

			char tmpbuf[256];
			sprintf(tmpbuf, "%d ", node.biomeid);
			s += tmpbuf;
		}
		LOG_INFO(s.c_str());
	}
	LOG_INFO("---------------------------------------------------");

	return true;
}

// 新增辅助函数，用于随机化群落边缘
void LinearTerrainIDGenerator::RandomizeBiomeBorders(int distXZ, int landWidth)
{
	// 根据地图大小计算侵蚀参数
	const float mapSizeFactor = std::min(1.0f, std::max(0.5f, static_cast<float>(m_mapX) / 100.0f));
	const float borderRandomizationIntensity = 0.8f * mapSizeFactor;  
	
	// 根据地图大小调整边界最大宽度
	const int maxBorderWidth = std::max(1, static_cast<int>(m_mapX / 25));

	// 创建主侵蚀随机种子，基于世界种子
	int baseErosionSeed = m_WorldGenSeed + m_mapX * 1000 + m_mapZ * 100;
	baseErosionSeed = (baseErosionSeed * 1103515245 + 12345) & 0x7fffffff;
	srand(baseErosionSeed);
	
	// 随机化基础侵蚀概率
	float randomProbFactor = 0.8f + (static_cast<float>(GenRandomInt(40)) / 100.0f); // 0.8-1.2范围的随机因子
	const float baseErosionProbability = 0.7f * (0.8f + 0.4f * mapSizeFactor) * randomProbFactor;
	
	// 为大地图增加连续性参数
	const bool isLargeMap = m_mapX >= 80;
	const float continuityFactor = isLargeMap ? 1.0f : 0.85f; // 连续性因子，大地图更高
	const int minIslandSize = isLargeMap ? 4 : 2; // 最小岛屿大小，小于此大小的孤岛将被移除
	
	LOG_INFO("地图边缘随机化参数：地图大小=%dx%d, 侵蚀强度=%.2f, 最大边界宽度=%d, 基础侵蚀概率=%.2f, 连续性=%.2f", 
	         m_mapX, m_mapZ, borderRandomizationIntensity, maxBorderWidth, baseErosionProbability, continuityFactor);
	
	// 创建一个临时数组用于存储节点的更新
	std::vector<std::pair<int, int>> nodesToErode;
	
	// 创建一个标记数组，标记哪些格子是"真正的边缘"（与外部海洋相连）
	std::vector<bool> isEdgeCell((m_mapX - 2 * distXZ) * (m_mapZ - 2 * distXZ), false);
	
	// 第一步：找出直接与地图边缘接壤的格子，这些是确定的边缘格子
	for (int z = distXZ; z < m_mapZ - distXZ; ++z)
	{
		for (int x = distXZ; x < m_mapX - distXZ; ++x)
		{
			// 如果格子在地图的最外圈
			bool isMapEdge = (x == distXZ || x == m_mapX - distXZ - 1 || 
							 z == distXZ || z == m_mapZ - distXZ - 1);
								
			if (isMapEdge)
			{
				int nodeIndex = (x - distXZ) * landWidth + (z - distXZ);
				isEdgeCell[nodeIndex] = true;
			}
		}
	}
	
	// 新增：创建连续性网格，用于模拟更自然的海岸线侵蚀
	std::vector<float> erosionGrid;
	if(isLargeMap)
	{
		// 初始化网格值
		erosionGrid.resize((m_mapX - 2 * distXZ) * (m_mapZ - 2 * distXZ), 0.0f);
		
		// 创建初始的连续噪声
		const int noiseScale = std::max(2, m_mapX / 40); // 噪声尺度

		float randOffsetX = static_cast<float>(GenRandomInt(1000)) / 100.0f;
		float randOffsetZ = static_cast<float>(GenRandomInt(1000)) / 100.0f;
		float randFreq1 = 0.7f + static_cast<float>(GenRandomInt(60)) / 100.0f;  // 0.7-1.3
		float randFreq2 = 1.3f + static_cast<float>(GenRandomInt(60)) / 100.0f;  // 1.3-1.9
		float randFreq3 = 0.6f + static_cast<float>(GenRandomInt(60)) / 100.0f;  // 0.6-1.2
		
		// 对于大地图，使用简化的Perlin噪声模拟生成连续噪声
		for(int z = 0; z < m_mapZ - 2 * distXZ; ++z)
		{
			for(int x = 0; x < m_mapX - 2 * distXZ; ++x)
			{
				float nx = (float)x / noiseScale;
				float nz = (float)z / noiseScale;
				
				// 简化的噪声计算，使用正弦函数组合模拟Perlin噪声，加入随机频率
				float noise = 0.5f * (1.0f + sin(nx * randFreq1 + nz * 1.2f) * 0.5f + 
									 sin(nx * 1.5f - nz * randFreq2) * 0.3f + 
									 sin(nx * randFreq3 + nz * 2.5f) * 0.2f);
				
				// 添加随机微扰，进一步增强随机性
				float microRandom = (static_cast<float>(GenRandomInt(100)) / 1000.0f) - 0.05f; // ±5%的随机扰动
				noise = std::min(1.0f, std::max(0.0f, noise + microRandom));
				
				// 距离边缘越近，噪声值越高（更容易被侵蚀）
				int distToEdgeX = std::min(x, (m_mapX - 2 * distXZ - 1) - x);
				int distToEdgeZ = std::min(z, (m_mapZ - 2 * distXZ - 1) - z);
				int distToEdge = std::min(distToEdgeX, distToEdgeZ);
				
				float edgeFactor = 1.0f - std::min(1.0f, (float)distToEdge / maxBorderWidth);
				noise = noise * 0.3f + edgeFactor * 0.7f; // 距离边缘因素影响更大
				
				erosionGrid[z * (m_mapX - 2 * distXZ) + x] = noise;
			}
		}
		
		// 应用高斯平滑以增加连续性
		std::vector<float> smoothedGrid = erosionGrid;
		const int smoothRadius = 1;
		for(int z = smoothRadius; z < m_mapZ - 2 * distXZ - smoothRadius; ++z)
		{
			for(int x = smoothRadius; x < m_mapX - 2 * distXZ - smoothRadius; ++x)
			{
				float sum = 0.0f;
				float weight = 0.0f;
				
				for(int dz = -smoothRadius; dz <= smoothRadius; ++dz)
				{
					for(int dx = -smoothRadius; dx <= smoothRadius; ++dx)
					{
						float w = exp(-(dx*dx + dz*dz) / 2.0f);
						sum += erosionGrid[(z + dz) * (m_mapX - 2 * distXZ) + (x + dx)] * w;
						weight += w;
					}
				}
				
				smoothedGrid[z * (m_mapX - 2 * distXZ) + x] = sum / weight;
			}
		}
		
		erosionGrid = smoothedGrid;
	}
	
	// 第二步：找出靠近地图边缘的陆地节点（第一轮侵蚀）
	for (int z = distXZ; z < m_mapZ - distXZ; ++z)
	{
		for (int x = distXZ; x < m_mapX - distXZ; ++x)
		{
			int nodeIndex = (x - distXZ) * landWidth + (z - distXZ);
			if (nodeIndex >= 0 && nodeIndex < m_nodes.size())
			{
				int currentBiome = m_nodes[nodeIndex].biomeid;
				if (currentBiome == 0) continue; // 跳过已经是海洋的区域
				
				// 计算到地图边缘的距离
				int distToEdgeX = std::min(x - distXZ, (m_mapX - distXZ - 1) - x);
				int distToEdgeZ = std::min(z - distXZ, (m_mapZ - distXZ - 1) - z);
				int distToEdge = std::min(distToEdgeX, distToEdgeZ);
				
				// 只处理靠近地图边缘的节点，避免在内陆形成湖泊
				if (distToEdge <= maxBorderWidth)
				{
					float erosionProbability;
					
					if(isLargeMap)
					{
						// 对大地图使用连续性网格来决定侵蚀概率
						int gridX = x - distXZ;
						int gridZ = z - distXZ;
						float noiseValue = erosionGrid[gridZ * (m_mapX - 2 * distXZ) + gridX];
						
						// 对距离边缘很近的点增加侵蚀概率
						if(distToEdge <= maxBorderWidth / 3)
						{
							noiseValue = std::min(1.0f, noiseValue * 1.5f);
						}
						
						erosionProbability = baseErosionProbability * noiseValue * borderRandomizationIntensity;
					}
					else
					{
						// 小地图沿用原来的距离因子逻辑
						float distanceFactor = 1.0f - (distToEdge / (float)maxBorderWidth);
						
						// 对于大地图，增加侵蚀概率的梯度
						if (m_mapX > 50)
						{
							distanceFactor = pow(distanceFactor, 0.8f); // 使梯度更加平缓
						}
						
						erosionProbability = baseErosionProbability * distanceFactor * borderRandomizationIntensity;
					}
					
					// 检查邻近格子的生物群系状态，增加连续性
					if(isLargeMap && continuityFactor > 0)
					{
						int oceanNeighbors = 0;
						int totalNeighbors = 0;
						
						for(int dz = -1; dz <= 1; ++dz)
						{
							for(int dx = -1; dx <= 1; ++dx)
							{
								if(dx == 0 && dz == 0) continue;
								
								int nx = x + dx;
								int nz = z + dz;
								
								if(nx >= distXZ && nx < m_mapX - distXZ && nz >= distXZ && nz < m_mapZ - distXZ)
								{
									int neighborIndex = (nx - distXZ) * landWidth + (nz - distXZ);
									if(neighborIndex >= 0 && neighborIndex < m_nodes.size())
									{
										totalNeighbors++;
										if(m_nodes[neighborIndex].biomeid == BIOME_OCEAN)
										{
											oceanNeighbors++;
										}
									}
								}
							}
						}
						
						// 邻居中海洋越多，侵蚀概率越高；反之越低
						if(totalNeighbors > 0)
						{
							float neighborRatio = (float)oceanNeighbors / totalNeighbors;
							erosionProbability = erosionProbability * (1.0f - continuityFactor) + 
												 neighborRatio * continuityFactor;
						}
					}
					
					// 随机决定是否将此节点标记为侵蚀
					if (static_cast<float>(GenRandomInt(100)) / 100.0f < erosionProbability)
					{
						nodesToErode.push_back(std::make_pair(x, z));
					}
				}
			}
		}
	}
	
	// 随机打乱侵蚀节点列表
	int shuffleSeed1 = baseErosionSeed + static_cast<int>(nodesToErode.size() * 13);
	srand(shuffleSeed1);
	std::random_shuffle(nodesToErode.begin(), nodesToErode.end());
	LOG_INFO("第一轮侵蚀使用洗牌种子: %d", shuffleSeed1);
	
	// 应用第一轮侵蚀
	for (const auto& node : nodesToErode)
	{
		int x = node.first;
		int z = node.second;
		int nodeIndex = (x - distXZ) * landWidth + (z - distXZ);
		
		if (nodeIndex >= 0 && nodeIndex < m_nodes.size())
		{
			m_nodes[nodeIndex].biomeid = BIOME_OCEAN; // 将节点设为海洋
			isEdgeCell[nodeIndex] = true;   // 标记为边缘海洋
		}
	}
	
	// 第三步：创建更多的不规则边缘形状（第二轮侵蚀）
	nodesToErode.clear();
	
	// 根据地图大小调整第二轮侵蚀的范围
	const int secondRoundSearchDepth = std::max(2, static_cast<int>(maxBorderWidth * 1.5));
	
	for (int z = distXZ; z < m_mapZ - distXZ; ++z)
	{
		for (int x = distXZ; x < m_mapX - distXZ; ++x)
		{
			int nodeIndex = (x - distXZ) * landWidth + (z - distXZ);
			if (nodeIndex >= 0 && nodeIndex < m_nodes.size())
			{
				int currentBiome = m_nodes[nodeIndex].biomeid;
				if (currentBiome == 0) continue; // 跳过已经是海洋的区域
				
				// 检查是否与已标记的边缘海洋相邻
				bool adjacentToEdgeOcean = false;
				int oceanNeighbors = 0;
				int totalNeighbors = 0;
				
				// 扩大搜索范围，适应大地图
				const int searchRange = isLargeMap ? 2 : 1;
				for (int dz = -searchRange; dz <= searchRange; ++dz)
				{
					for (int dx = -searchRange; dx <= searchRange; ++dx)
					{
						if (dx == 0 && dz == 0) continue; // 跳过自身
						
						// 对角线格子权重降低
						if(abs(dx) + abs(dz) > searchRange) continue;
						
						int nx = x + dx;
						int nz = z + dz;
						
						if (nx >= distXZ && nx < m_mapX - distXZ && nz >= distXZ && nz < m_mapZ - distXZ)
						{
							totalNeighbors++;
							int neighborIndex = (nx - distXZ) * landWidth + (nz - distXZ);
							if (neighborIndex >= 0 && neighborIndex < m_nodes.size() && neighborIndex < isEdgeCell.size())
							{
								// 计算海洋邻居
								if (m_nodes[neighborIndex].biomeid == BIOME_OCEAN)
								{
									oceanNeighbors++;
									if (isEdgeCell[neighborIndex])
									{
										adjacentToEdgeOcean = true;
									}
								}
							}
						}
					}
				}
				
				// 如果与边缘海洋相邻，有较低概率被侵蚀
				if (adjacentToEdgeOcean)
				{
					// 对大地图增加侵蚀概率，但考虑连续性
					float erosionProbability = 0.3f * borderRandomizationIntensity * (1.0f + 0.5f * mapSizeFactor);
					
					// 对大地图考虑连续性
					if(isLargeMap && totalNeighbors > 0)
					{
						float neighborRatio = (float)oceanNeighbors / totalNeighbors;
						erosionProbability = erosionProbability * (1.0f - continuityFactor) + 
											 neighborRatio * continuityFactor * 1.5f; // 邻居因素更重要
					}
					
					if (static_cast<float>(GenRandomInt(100)) / 100.0f < erosionProbability)
					{
						nodesToErode.push_back(std::make_pair(x, z));
					}
				}
			}
		}
	}
	
	// 应用第二轮侵蚀，并继续标记新的边缘格子
	int shuffleSeed2 = baseErosionSeed + 39527; // 使用不同的种子
	srand(shuffleSeed2);
	std::random_shuffle(nodesToErode.begin(), nodesToErode.end());
	LOG_INFO("第二轮侵蚀使用洗牌种子: %d", shuffleSeed2);
	
	for (const auto& node : nodesToErode)
	{
		int x = node.first;
		int z = node.second;
		int nodeIndex = (x - distXZ) * landWidth + (z - distXZ);
		
		if (nodeIndex >= 0 && nodeIndex < m_nodes.size())
		{
			m_nodes[nodeIndex].biomeid = BIOME_OCEAN; // 将节点设为海洋
			isEdgeCell[nodeIndex] = true;   // 标记为边缘海洋
		}
	}
	
	// 第四步：第三轮侵蚀，进一步增强边缘的随机性，但保持连续性
	nodesToErode.clear();
	
	// 根据地图大小调整第三轮侵蚀的深度
	const int thirdRoundDepth = std::max(1, static_cast<int>(maxBorderWidth * 0.7f));
	
	for (int z = distXZ; z < m_mapZ - distXZ; ++z)
	{
		for (int x = distXZ; x < m_mapX - distXZ; ++x)
		{
			int nodeIndex = (x - distXZ) * landWidth + (z - distXZ);
			if (nodeIndex >= 0 && nodeIndex < m_nodes.size())
			{
				int currentBiome = m_nodes[nodeIndex].biomeid;
				if (currentBiome == 0) continue; // 跳过已经是海洋的区域
				
				// 检查是否与已标记的边缘海洋相邻
				bool adjacentToEdgeOcean = false;
				int oceanNeighbors = 0;
				int totalNeighbors = 0;
				
				// 大地图使用更大的搜索范围
				const int finalSearchRange = isLargeMap ? 2 : 1;
				for (int dz = -finalSearchRange; dz <= finalSearchRange; ++dz)
				{
					for (int dx = -finalSearchRange; dx <= finalSearchRange; ++dx)
					{
						if (dx == 0 && dz == 0) continue; // 跳过自身
						
						// 对角线格子权重降低
						if(abs(dx) + abs(dz) > finalSearchRange + 1) continue;
						
						int nx = x + dx;
						int nz = z + dz;
						
						if (nx >= distXZ && nx < m_mapX - distXZ && nz >= distXZ && nz < m_mapZ - distXZ)
						{
							totalNeighbors++;
							int neighborIndex = (nx - distXZ) * landWidth + (nz - distXZ);
							if (neighborIndex >= 0 && neighborIndex < m_nodes.size() && neighborIndex < isEdgeCell.size())
							{
								// 计算海洋邻居数量，特别关注边缘海洋
								if (m_nodes[neighborIndex].biomeid == BIOME_OCEAN) // 相邻格子是海洋
								{
									oceanNeighbors++;
									if (isEdgeCell[neighborIndex])
									{
										adjacentToEdgeOcean = true;
									}
								}
							}
						}
					}
				}
				
				// 如果与边缘海洋相邻，或者有多个海洋邻居，有概率被侵蚀
				if (adjacentToEdgeOcean || oceanNeighbors >= 2)
				{
					// 第三轮侵蚀概率调整
					float erosionProbability;
					
					if(isLargeMap && totalNeighbors > 0)
					{
						// 更强调连续性，根据邻居比例决定
						float neighborRatio = (float)oceanNeighbors / totalNeighbors;
						float baseProb = 0.2f * borderRandomizationIntensity;
						erosionProbability = baseProb * (1.0f - continuityFactor * 1.2f) + 
											 neighborRatio * continuityFactor * 1.2f;
						
						// 确保高比例的海洋邻居会增加侵蚀概率
						if(neighborRatio > 0.5f)
						{
							erosionProbability = std::max(erosionProbability, baseProb * 1.5f);
						}
					}
					else
					{
						// 使用原有的计算方式
						float neighborFactor = 1.0f + oceanNeighbors * (0.1f + 0.05f * mapSizeFactor);
						erosionProbability = 0.2f * borderRandomizationIntensity * neighborFactor;
					}
					
					if (static_cast<float>(GenRandomInt(100)) / 100.0f < erosionProbability)
					{
						nodesToErode.push_back(std::make_pair(x, z));
					}
				}
			}
		}
	}
	
	// 应用第三轮侵蚀
	int shuffleSeed3 = baseErosionSeed + 87654; // 再次使用不同的种子
	srand(shuffleSeed3);
	std::random_shuffle(nodesToErode.begin(), nodesToErode.end());
	LOG_INFO("第三轮侵蚀使用洗牌种子: %d", shuffleSeed3);
	
	// 大地图可能需要更多的边缘变化，但要保持连续性
	int erosionLimit = m_mapX > 80 ? nodesToErode.size() : static_cast<int>(nodesToErode.size() * 0.8f);
	int appliedErosions = 0;
	
	for (const auto& node : nodesToErode)
	{
		if (appliedErosions >= erosionLimit) break;
		
		int x = node.first;
		int z = node.second;
		int nodeIndex = (x - distXZ) * landWidth + (z - distXZ);
		
		if (nodeIndex >= 0 && nodeIndex < m_nodes.size())
		{
			m_nodes[nodeIndex].biomeid = BIOME_OCEAN; // 将节点设为海洋
			isEdgeCell[nodeIndex] = true;   // 标记为边缘海洋
			appliedErosions++;
		}
	}
	
	// 在三轮侵蚀结束后，添加沙滩生物群系作为海陆过渡带
	LOG_INFO("开始生成沙滩过渡带...");

	
	// 创建一个数组用于存储要转换为沙滩的节点
	std::vector<std::pair<int, int>> beachNodes;

	// 沙滩生成参数 - 设为100%概率生成，确保所有与海洋相邻的陆地都变成沙滩
	const float baseBeachProb = 1.0f; // 基础沙滩生成概率设为100%

	// 查找所有与海洋相邻的陆地节点排除沙漠和冰原地形
	for (int z = distXZ; z < m_mapZ - distXZ; ++z)
	{
		for (int x = distXZ; x < m_mapX - distXZ; ++x)
		{
			int nodeIndex = (x - distXZ) * landWidth + (z - distXZ);
			if (nodeIndex >= 0 && nodeIndex < m_nodes.size())
			{
				int currentBiome = m_nodes[nodeIndex].biomeid;
				if (currentBiome == 0 || currentBiome == BIOME_BEACH || currentBiome == BIOME_DESERT || currentBiome == BIOME_ICE_PLAINS) continue; // 跳过海洋和已经是沙滩的区域
				
				// 检查是否与海洋相邻
				bool adjacentToOcean = false;
				
				// 检查八个方向的邻居
				for (int dz = -1; dz <= 1; ++dz)
				{
					for (int dx = -1; dx <= 1; ++dx)
					{
						if (dx == 0 && dz == 0) continue; // 跳过自身
						
						int nx = x + dx;
						int nz = z + dz;
						
						if (nx >= distXZ && nx < m_mapX - distXZ && nz >= distXZ && nz < m_mapZ - distXZ)
						{
							int neighborIndex = (nx - distXZ) * landWidth + (nz - distXZ);
							if (neighborIndex >= 0 && neighborIndex < m_nodes.size())
							{
								if (m_nodes[neighborIndex].biomeid == BIOME_OCEAN) // 相邻格子是海洋
								{
									adjacentToOcean = true;
									break;
								}
							}
						}
						// 新增：如果这个格子是地图的边缘格子，也视为与海洋相邻
						else if (nx < distXZ || nx >= m_mapX - distXZ || nz < distXZ || nz >= m_mapZ - distXZ)
						{
							adjacentToOcean = true;
							break;
						}
					}
					if (adjacentToOcean) break; // 如果已经找到相邻的海洋，提前跳出循环
				}
				
				// 新增：如果格子在陆地边缘（距离distXZ不超过1格），也视为与海洋相邻
				if (!adjacentToOcean)
				{
					if (x == distXZ || x == m_mapX - distXZ - 1 || z == distXZ || z == m_mapZ - distXZ - 1)
					{
						adjacentToOcean = true;
					}
				}
				
				// 只要与海洋相邻，就添加到沙滩节点列表
				if (adjacentToOcean)
				{
					beachNodes.push_back(std::make_pair(x, z));
				}
			}
		}
	}

	// 应用沙滩生物群系转换 - 直接将所有与海洋相邻的陆地变为沙滩
	for (const auto& node : beachNodes)
	{
		int x = node.first;
		int z = node.second;
		int nodeIndex = (x - distXZ) * landWidth + (z - distXZ);
		
		if (nodeIndex >= 0 && nodeIndex < m_nodes.size())
		{
			m_nodes[nodeIndex].biomeid = BIOME_BEACH; // 将节点设为沙滩
		}
	}

	// 新增：第二轮沙滩生成，确保海边一定有沙滩
	// 检查每个海洋格子旁是否有沙滩，如果没有沙滩，则将靠近海洋的陆地格子改为沙滩
	std::vector<std::pair<int, int>> secondRoundBeachNodes;

	for (int z = distXZ; z < m_mapZ - distXZ; ++z)
	{
		for (int x = distXZ; x < m_mapX - distXZ; ++x)
		{
			int nodeIndex = (x - distXZ) * landWidth + (z - distXZ);
			if (nodeIndex >= 0 && nodeIndex < m_nodes.size())
			{
				// 只检查海洋格子
				if (m_nodes[nodeIndex].biomeid == BIOME_OCEAN)
				{
					// 检查这个海洋格子周围是否有陆地但没有沙滩
					bool hasLand = false;
					bool hasBeach = false;
					std::vector<std::pair<int, int>> landNeighbors;
					
					// 检查周围8个方向
					for (int dz = -1; dz <= 1; ++dz)
					{
						for (int dx = -1; dx <= 1; ++dx)
						{
							if (dx == 0 && dz == 0) continue; // 跳过自身
							
							int nx = x + dx;
							int nz = z + dz;
							
							if (nx >= distXZ && nx < m_mapX - distXZ && nz >= distXZ && nz < m_mapZ - distXZ)
							{
								int neighborIndex = (nx - distXZ) * landWidth + (nz - distXZ);
								if (neighborIndex >= 0 && neighborIndex < m_nodes.size())
								{
									if (m_nodes[neighborIndex].biomeid == BIOME_BEACH)
									{
										hasBeach = true;
									}
									else if (m_nodes[neighborIndex].biomeid != 0 && m_nodes[neighborIndex].biomeid != BIOME_DESERT && m_nodes[neighborIndex].biomeid != BIOME_ICE_PLAINS) // 是陆地且不是沙滩和沙漠冰原
									{
										hasLand = true;
										landNeighbors.push_back(std::make_pair(nx, nz));
									}
								}
							}
						}
					}
					
					// 如果周围有陆地但没有沙滩，将最近的一块陆地变成沙滩
					if (hasLand && !hasBeach && !landNeighbors.empty())
					{
						// 可以选择随机一块陆地变成沙滩，或者全部变成沙滩
						// 这里选择将所有相邻陆地都变成沙滩
						for (const auto& neighbor : landNeighbors)
						{
							secondRoundBeachNodes.push_back(neighbor);
						}
					}
				}
			}
		}
	}

	// 应用第二轮沙滩转换
	for (const auto& node : secondRoundBeachNodes)
	{
		int x = node.first;
		int z = node.second;
		int nodeIndex = (x - distXZ) * landWidth + (z - distXZ);
		
		if (nodeIndex >= 0 && nodeIndex < m_nodes.size())
		{
			m_nodes[nodeIndex].biomeid = BIOME_BEACH; // 将节点设为沙滩
		}
	}

	// 第三轮：确保地图边缘的所有陆地格子都是沙滩
	for (int z = distXZ; z < m_mapZ - distXZ; ++z)
	{
		for (int x = distXZ; x < m_mapX - distXZ; ++x)
		{
			// 只处理地图边缘的一圈格子
			if (x == distXZ || x == m_mapX - distXZ - 1 || z == distXZ || z == m_mapZ - distXZ - 1)
			{
				int nodeIndex = (x - distXZ) * landWidth + (z - distXZ);
				if (nodeIndex >= 0 && nodeIndex < m_nodes.size())
				{
					// 如果是陆地且不是沙滩，则变成沙滩
					if (m_nodes[nodeIndex].biomeid != 0 && m_nodes[nodeIndex].biomeid != BIOME_BEACH && m_nodes[nodeIndex].biomeid != BIOME_DESERT && m_nodes[nodeIndex].biomeid != BIOME_ICE_PLAINS)
					{
						m_nodes[nodeIndex].biomeid = BIOME_BEACH;
					}
				}
			}
		}
	}

	LOG_INFO("沙滩生成完成，第一轮共生成沙滩格子: %d，第二轮补充: %d", beachNodes.size(), secondRoundBeachNodes.size());
}

// 生成特定位置的生物群落
void LinearTerrainIDGenerator::GenerateSpecialBiome(int startX, int startZ, int biomeId, int biomeCount, int landWidth, int biomdir, int growdir)
{
	// 计算矩形区域的大小
	int rectHeight = static_cast<int>(ceil(sqrt(biomeCount * 1.0)));
	int rectWidth = rectHeight + GenRandomInt(rectHeight/3) + 1;

	int width = rectWidth;
	int height = rectHeight;

	int step = (growdir == 0) ? 1 : -1;

	// 根据起始点调整放置方向
	if (biomdir == 0) // z轴方向扩展
	{
		// 保持原有逻辑
	}
	else // x轴方向扩展
	{
		width = rectHeight;
		height = rectWidth;
	}

	// 原有的边界检查代码
	if (step == 1)
	{
		if (startZ + width >= landWidth)
		{
			startZ = landWidth - width;
		}
		if (startX + height >= landWidth)
		{
			startX = landWidth - height;
		}
	}
	else
	{
		if (startZ - width < 0)
		{
			startZ = width - 1;
		}
		if (startX - height <0)
		{
			startX = height - 1;
		}
	}

	LOG_INFO("GenerateSpecialBiome startx:%d startz:%d width:%d height:%d", startX, startZ, width, height);

	int filledCount = 0;
	
	int zStart = startZ;
	int zEnd = (growdir == 0) ? startZ + width : startZ - width;

	int xStart = startX;
	int xEnd = (growdir == 0) ? startX + height : startX - height;

	if (biomdir == 0)
	{
		// 严格控制范围在[0, landWidth-1]内
		
		for (int x = xStart; x != xEnd && filledCount < biomeCount; x += step)
		{
			// 统一的循环结构
			for (int z = zStart; z != zEnd && filledCount < biomeCount; z += step)
			{
				int index = x * landWidth + z;
				// LOG_INFO("Set Biom2  x:%d, z:%d index:%d biom =========", x, z, index, biomeId);
				m_nodes[index].biomeid = biomeId;
				filledCount++;
			}
		}
	}
	else
	{

		for (int z = zStart; z != zEnd && filledCount < biomeCount; z += step)
		{
			// 统一的循环结构
			for (int x = xStart; x != xEnd && filledCount < biomeCount; x += step)
			{
				int index = x * landWidth + z;
				LOG_INFO("Set Biom1  x:%d, z:%d index:%d biom:%d ========= ", x, z, index, biomeId);
				m_nodes[index].biomeid = biomeId;
				filledCount++;
			}
		}
	}
}

// 随机化内陆生物群落边界
void LinearTerrainIDGenerator::RandomizeBiomeInteriorBorders(int distXZ, int landWidth)
{
	// 随机化参数
	const float interiorRandomizationIntensity = 0.6f;  // 内陆边界随机强度
	const int borderNoiseDepth = 2;  // 边界噪声深度（影响区域大小）
	const float baseBorderChangeProb = 0.5f;  // 基础边界变化概率
	
	// 创建一个边界侵蚀缓冲区，避免循环中立即修改影响判断
	std::vector<std::tuple<int, int, int>> borderChanges; // x, z, new_biome_id
	
	// 安全边距，避免处理靠近海岸的区域
	const int safeMargin = 3;
	
	// 第一步：找出内陆区域中不同群落的边界节点
	for (int z = distXZ + safeMargin; z < m_mapZ - distXZ - safeMargin; ++z)
	{
		for (int x = distXZ + safeMargin; x < m_mapX - distXZ - safeMargin; ++x)
		{
			int nodeIndex = (x - distXZ) * landWidth + (z - distXZ);
			if (nodeIndex >= 0 && nodeIndex < m_nodes.size())
			{
				int currentBiome = m_nodes[nodeIndex].biomeid;
				if (currentBiome == 0) continue; // 跳过海洋区域
				
				// 检查周围8个方向，寻找不同群落
				std::map<int, int> neighborBiomes; // biome_id -> count
				for (int dz = -1; dz <= 1; ++dz)
				{
					for (int dx = -1; dx <= 1; ++dx)
					{
						if (dx == 0 && dz == 0) continue; // 跳过自身
						
						int nx = x + dx;
						int nz = z + dz;
						
						if (nx >= distXZ && nx < m_mapX - distXZ && 
							nz >= distXZ && nz < m_mapZ - distXZ)
						{
							int neighborIndex = (nx - distXZ) * landWidth + (nz - distXZ);
							if (neighborIndex >= 0 && neighborIndex < m_nodes.size())
							{
								int neighborBiome = m_nodes[neighborIndex].biomeid;
								if (neighborBiome != 0 && neighborBiome != currentBiome) // 不同的陆地群落
								{
									neighborBiomes[neighborBiome]++;
								}
							}
						}
					}
				}
				
				// 如果有不同的相邻生物群落，则这是边界节点
				if (!neighborBiomes.empty())
				{
					// 边界侵蚀概率随着相邻不同群落的数量增加而增加
					float erosionProbability = baseBorderChangeProb * interiorRandomizationIntensity;
					
					if (static_cast<float>(GenRandomInt(100)) / 100.0f < erosionProbability)
					{
						// 选择一个相邻的群落ID进行替换
						// 群落出现次数越多，被选中概率越高
						int totalWeight = 0;
						for (const auto& pair : neighborBiomes)
						{
							totalWeight += pair.second;
						}
						
						int randomWeight = GenRandomInt(totalWeight);
						int cumulativeWeight = 0;
						int selectedBiome = currentBiome; // 默认保持当前群落
						
						for (const auto& pair : neighborBiomes)
						{
							cumulativeWeight += pair.second;
							if (randomWeight < cumulativeWeight)
							{
								selectedBiome = pair.first;
								break;
							}
						}
						
						// 记录要修改的节点
						borderChanges.push_back(std::make_tuple(x, z, selectedBiome));
					}
				}
			}
		}
	}
	
	// 应用边界变化
	for (const auto& change : borderChanges)
	{
		int x = std::get<0>(change);
		int z = std::get<1>(change);
		int newBiome = std::get<2>(change);
		
		int nodeIndex = (x - distXZ) * landWidth + (z - distXZ);
		if (nodeIndex >= 0 && nodeIndex < m_nodes.size())
		{
			m_nodes[nodeIndex].biomeid = newBiome;
		}
	}
	
	// 第二步：扩展边界噪声，使边界更加模糊和自然
	for (int noisePass = 0; noisePass < borderNoiseDepth; ++noisePass)
	{
		borderChanges.clear();
		
		// 降低每次迭代的概率，避免过度侵蚀
		float passMultiplier = 1.0f - (noisePass / (float)(borderNoiseDepth + 1));
		
		for (int z = distXZ + safeMargin; z < m_mapZ - distXZ - safeMargin; ++z)
		{
			for (int x = distXZ + safeMargin; x < m_mapX - distXZ - safeMargin; ++x)
			{
				int nodeIndex = (x - distXZ) * landWidth + (z - distXZ);
				if (nodeIndex >= 0 && nodeIndex < m_nodes.size())
				{
					int currentBiome = m_nodes[nodeIndex].biomeid;
					if (currentBiome == 0) continue; // 跳过海洋区域
					
					// 收集相邻格子的生物群落信息
					std::map<int, int> neighborBiomes;
					for (int dz = -1; dz <= 1; ++dz)
					{
						for (int dx = -1; dx <= 1; ++dx)
						{
							if (dx == 0 && dz == 0) continue;
							
							int nx = x + dx;
							int nz = z + dz;
							
							if (nx >= distXZ && nx < m_mapX - distXZ && 
								nz >= distXZ && nz < m_mapZ - distXZ)
							{
								int neighborIndex = (nx - distXZ) * landWidth + (nz - distXZ);
								if (neighborIndex >= 0 && neighborIndex < m_nodes.size())
								{
									int neighborBiome = m_nodes[neighborIndex].biomeid;
									if (neighborBiome != 0) // 只考虑陆地群落
									{
										neighborBiomes[neighborBiome]++;
									}
								}
							}
						}
					}
					
					// 如果周围有其他生物群落
					if (neighborBiomes.size() > 1)
					{
						// 降低每次迭代的概率
						float noiseProb = baseBorderChangeProb * interiorRandomizationIntensity * passMultiplier;
						
						if (static_cast<float>(GenRandomInt(100)) / 100.0f < noiseProb)
						{
							// 随机选择一个相邻群落（包括自己的群落）
							int totalNeighbors = 0;
							for (const auto& pair : neighborBiomes)
							{
								totalNeighbors += pair.second;
							}
							
							int randomChoice = GenRandomInt(totalNeighbors);
							int currentSum = 0;
							int selectedBiome = currentBiome;
							
							for (const auto& pair : neighborBiomes)
							{
								currentSum += pair.second;
								if (randomChoice < currentSum)
								{
									selectedBiome = pair.first;
									break;
								}
							}
							
							// 只有当选择了不同的群落时才记录变化
							if (selectedBiome != currentBiome)
							{
								borderChanges.push_back(std::make_tuple(x, z, selectedBiome));
							}
						}
					}
				}
			}
		}
		
		// 应用此轮噪声变化
		for (const auto& change : borderChanges)
		{
			int x = std::get<0>(change);
			int z = std::get<1>(change);
			int newBiome = std::get<2>(change);
			
			int nodeIndex = (x - distXZ) * landWidth + (z - distXZ);
			if (nodeIndex >= 0 && nodeIndex < m_nodes.size())
			{
				m_nodes[nodeIndex].biomeid = newBiome;
			}
		}
	}
	
	LOG_INFO("内陆群落边界随机化完成，共处理节点: %d", borderChanges.size());
}


LinearTerrainTreeNode* LinearTerrainIDGenerator::getTreeNodeByID(const Rainbow::FixedString& s)
{
	auto p = std::find_if(m_nodes.rbegin(), m_nodes.rend(), [s](const LinearTerrainTreeNode& A) {return A.id == s; });
	if (p != m_nodes.rend())
	{
		return &(*p);
	}
	return nullptr;
}

LinearTerrainTreeNode* LinearTerrainIDGenerator::getTreeNodeByIndex(int nodeIndex)
{
	if (nodeIndex < 0 || nodeIndex >= m_nodes.size())
	{
		LOG_WARNING("LinearTerrainIDGenerator nodeGet failed, getIndex:%d", nodeIndex);
		assert(0);
		return nullptr;
	}
	return &m_nodes[nodeIndex];
}

void LinearTerrainIDGenerator::_clearStackTop()
{
	if (m_stack.empty()) return;
	LinearTerrainTreeNode* node = getTreeNodeByIndex(m_stack.top());
	if (node)
	{
		node->use = false;
		setMapValue(node->x, node->z, INVALID_VALUE);
	}
	else
	{
		LOG_WARNING("LinearTerrainIDGenerator clearStack error, node nullptr, getIndex:%d", m_stack.top());
		assert(0);
	}
	m_stack.pop();
}

void LinearTerrainIDGenerator::clearStack(int index)
{
	if (m_stack.empty()) return;
	int nodeIndex = m_stack.top();
	while (nodeIndex != index && !m_stack.empty())
	{
		_clearStackTop();
		nodeIndex = m_stack.top();
	}
	_clearStackTop();
}

void LinearTerrainIDGenerator::getGenIDs(std::vector<int>& retids, int ox, int oz, int xlen, int zlen, TerrainSpecialData* data)
{
	if (m_RootGen)
		m_RootGen->genTerrainIDs(retids, ox, oz, xlen, zlen, data);
}

void LinearTerrainIDGenerator::getGenIndices(std::vector<int>& retids, int ox, int oz, int xlen, int zlen, TerrainSpecialData* data)
{
	if (m_RootIndex)
		m_RootIndex->genTerrainIDs(retids, ox, oz, xlen, zlen, data);
}

void LinearTerrainIDGenerator::createTree(int mapX, int mapZ, int gridlen)
{
	LOG_INFO("NewGen_Start");
	//构造空间树
	m_mapX = mapX;
	m_mapZ = mapZ;

	//20是个估值
	int disX = GetSeaSize();

	if (flatSpaceMapGen(disX))
	{

		return;
	}
}

int LinearTerrainIDGenerator::GetSeaSize()
{
	if (m_mapX < 20)
	    return 2;

	int seaSize = m_mapX * 0.1;
	
	return seaSize;
}

bool LinearTerrainIDGenerator::GetPlaneRange(World* pworld, ChunkIndex& startCI, ChunkIndex& endCI)
{
	auto provider = pworld->getChunkProvider();
    int startX = provider->getStartChunkX();
    int endX = provider->getEndChunkX();
    int startZ = provider->getStartChunkZ();
    int endZ = provider->getEndChunkZ();

	// 大小异常
	if (endX - startX > 10000) return false;

	//int seaSize = GetSeaSize() * 4 + 1;  // 海洋chunksize

    startCI.x = startX + m_nRealSeaSize;
    startCI.z = startZ + m_nRealSeaSize;
    endCI.x = endX - m_nRealSeaSize;
    endCI.z = endZ - m_nRealSeaSize;

	return true;
}

void LinearTerrainIDGenerator::initWholeChunksBiomeIds(std::vector<int>& tmpretbuf, std::vector<LandSubBiomeData>& landSubBiomeDatas, int realChunkX, int realChunkZ, int seaSize, ChunkIndex sqrtLB)
{
	// 创建候选点列表，用于随机选择生成位置
	std::vector<std::pair<int, int>> candidatePoints;
	seaSize *= 2;
	// 收集所有可能的候选生成点
	for (int x = seaSize; x < realChunkX - seaSize; x++)
	{
		for (int z = seaSize; z < realChunkZ - seaSize; z++)
		{
			auto biome = tmpretbuf[x + z * realChunkX];
			if (biome == BIOME_PLAINS || biome == BIOME_DESERT || biome == BIOME_ICE_PLAINS)
			{
				candidatePoints.push_back(std::make_pair(x, z));
			}
		}
	}
	
	// 打乱候选点顺序
	for (int i = 0; i < candidatePoints.size(); i++) {
		int j = GenRandomInt(candidatePoints.size());
		if (i != j) {
			std::swap(candidatePoints[i], candidatePoints[j]);
		}
	}
	
	// 定义最小距离矩阵，控制不同生态群落之间的最小距离
	const int minDistanceMatrix[3][3] = {
		{26, 38, 38},  // PLAINS的最小距离要求 
		{38, 26, 38},  // DESERT的最小距离要求
		{38, 38, 26}   // ICE_PLAINS的最小距离要求
	};
	
	// 已选择的点列表
	std::vector<std::tuple<int, int, int>> selectedPoints; // x, z, biomeType(0=plains, 1=desert, 2=ice)
	
	// 设置不同群系的生成概率权重
	const int biomeWeights[3] = {5, 3, 2}; // 平原、沙漠、冰原的权重比例
	int totalWeight = biomeWeights[0] + biomeWeights[1] + biomeWeights[2];
	
	// 处理候选点
	for (const auto& point : candidatePoints)
	{
		int x = point.first;
		int z = point.second;
		auto biome = tmpretbuf[x + z * realChunkX];
		
		// 计算生成概率 - 在边缘地带降低概率
		float distFromEdgeX = std::min(x - seaSize, realChunkX - seaSize - x) / static_cast<float>(realChunkX - 2 * seaSize);
		float distFromEdgeZ = std::min(z - seaSize, realChunkZ - seaSize - z) / static_cast<float>(realChunkZ - 2 * seaSize);
		float edgeFactor = std::min(distFromEdgeX, distFromEdgeZ) * 4.0f; // 越靠近边缘，生成概率越低
		
		// 基础生成概率
		int genChance = 40 + GenRandomInt(30); // 40-70% 的基础概率
		genChance = static_cast<int>(genChance * edgeFactor);
		
		if (GenRandomInt(100) >= genChance) continue;
		
		// 检查与现有点的距离
		bool tooClose = false;
		int biomeTypeIdx = 0;
		if (biome == BIOME_DESERT) biomeTypeIdx = 1;
		else if (biome == BIOME_ICE_PLAINS) biomeTypeIdx = 2;
		
		for (const auto& existingPoint : selectedPoints)
		{
			int ex = std::get<0>(existingPoint);
			int ez = std::get<1>(existingPoint);
			int eType = std::get<2>(existingPoint);
			
			// 计算曼哈顿距离而不是欧几里得距离，更快且足够使用
			int distance = abs(x - ex) + abs(z - ez);
			
			// 获取最小距离要求
			int minRequiredDist = minDistanceMatrix[biomeTypeIdx][eType];
			
			// 加入随机性，有时允许更近的距离
			minRequiredDist = static_cast<int>(minRequiredDist * (0.9f + 0.4f * GenRandomFloat()));
			
			if (distance < minRequiredDist)
			{
				tooClose = true;
				break;
			}
		}
		
		if (tooClose) continue;
		
		// 添加到已选择点
		selectedPoints.push_back(std::make_tuple(x, z, biomeTypeIdx));
		
		// 生成子地形 - 先获取配置中的基础尺寸
		auto biomelist = m_mSubBiomeDatas.find(biome);
		if (biomelist == m_mSubBiomeDatas.end()) continue; // 如果没有找到对应的子地形配置，跳过
		
		auto& subfirstBiomeDatas = biomelist->second;
		std::vector<int> canCreatesubfirstbiomeList;
		int subfirstbiomeidx = 0;
		for (auto& subfirstBiomeData : subfirstBiomeDatas)
		{
			if (subfirstBiomeData.count > 0)
			{
				// 添加权重系统，让某些子地形更常见
				int weight = 1;
				if (subfirstbiomeidx < 3) weight = 3;  // 前几种类型更常见
				for (int w = 0; w < weight; w++) {
					canCreatesubfirstbiomeList.push_back(subfirstbiomeidx);
				}
			}
			subfirstbiomeidx++;
		}
		if (canCreatesubfirstbiomeList.empty()) continue; // 没有可生成的子地形
		
		// 随机选择一个子地形类型
		int canCreatesubfirstbiomeListidx = randInt(canCreatesubfirstbiomeList.size());
		auto& selectedSubBiome = subfirstBiomeDatas[canCreatesubfirstbiomeList[canCreatesubfirstbiomeListidx]];
		
		// 使用配置中的尺寸作为基准，添加适度的随机变化
		int baseXRange = selectedSubBiome.xRange;
		int baseZRange = selectedSubBiome.zRange;
		
		// 添加±20%的随机变化，但确保最小尺寸
		float sizeVariation = 0.8f + 0.4f * GenRandomFloat(); // 0.8-1.2的变化范围
		int randSpacex = std::max(6, static_cast<int>(baseXRange * sizeVariation));
		int randSpacez = std::max(6, static_cast<int>(baseZRange * sizeVariation));
		
		// 根据地图大小调整尺寸
		if (realChunkX > 200) { // 大地图可以有更大的子地形
			randSpacex = static_cast<int>(randSpacex * 1.2f);
			randSpacez = static_cast<int>(randSpacez * 1.2f);
		} else if (realChunkX < 100) { // 小地图需要缩小子地形
			randSpacex = static_cast<int>(randSpacex * 0.8f);
			randSpacez = static_cast<int>(randSpacez * 0.8f);
		}
		
		//判断是否符合间隔距离生成地形
		bool canCreateSubBiome = true;
		
		int centerx = x;
		int centerz = z;
		
		// 随机偏移中心点，但范围要相对于地形大小
		int offsetRange = std::max(2, std::min(randSpacex, randSpacez) / 4);
		centerx += GenRandomInt(-offsetRange, offsetRange + 1);
		centerz += GenRandomInt(-offsetRange, offsetRange + 1);

		int startx = centerx - randSpacex / 2;
		int startz = centerz - randSpacez / 2;
		int checkMaxX = startx + randSpacex;
		int checkMaxZ = startz + randSpacez;
		if (startx < seaSize || startz < seaSize || checkMaxX >= (realChunkX - seaSize) || checkMaxZ >= (realChunkZ - seaSize))
		{
			continue;
		}

		for (int checkx = startx; checkx < checkMaxX; ++checkx)
		{
			for (int checkz = startz; checkz < checkMaxZ; ++checkz)
			{
				if (tmpretbuf[checkx + checkz * realChunkX] != biome)
				{
					canCreateSubBiome = false;
					break;
				}
			}
		}
		if (canCreateSubBiome)
		{
			// 选择二级子地形
			int subsecondbiomeidx = randInt(selectedSubBiome.biomeids.size());
			auto subsecondbiome = selectedSubBiome.biomeids[subsecondbiomeidx];

					int subBiomeStartX = centerx;
					int subBiomeStartZ = centerz;
					int maxx = centerx;
					int maxz = centerz;
					
					// 随机旋转，使用我们计算的实际尺寸
					bool hor = randInt(2) == 0;
					
					// 使用之前计算的实际尺寸
					int xRange = randSpacex;
					int zRange = randSpacez;

					if (hor)
					{
						subBiomeStartX = centerx - std::max(1, xRange / 2);
						maxx = subBiomeStartX + xRange;
						subBiomeStartZ = centerz - std::max(1, zRange / 2);
						maxz = subBiomeStartZ + zRange;
					}
					else
					{
						subBiomeStartX = centerx - std::max(1, zRange / 2);
						maxx = subBiomeStartX + zRange;
						subBiomeStartZ = centerz - std::max(1, xRange / 2);
						maxz = subBiomeStartZ + xRange;
					}

					// 为形状变形创建噪声种子
					resetChunkSeed(centerx * 31, centerz * 17);
					
					// 选择地形形状类型
					int shapeType = randInt(100);
					
					// 增加变形强度范围
					int deformStrength = 2 + randInt(4); // 2-5块的突出/凹进
					
					// 更多样化的形状生成
					if (shapeType < 20) // 20% 概率生成椭圆形
					{
						// 椭圆形地形
						float radiusX = std::max(1.0f, xRange / 2.0f) + randInt(3) - 1;
						float radiusZ = std::max(1.0f, zRange / 2.0f) + randInt(3) - 1;
						
						// 椭圆的随机偏心率
						float eccentricity = 0.5f + 0.5f * GenRandomFloat();
						if (randInt(2) == 0) {
							radiusX *= eccentricity;
						} else {
							radiusZ *= eccentricity;
						}
						
						for (int x = subBiomeStartX - deformStrength; x < maxx + deformStrength; ++x)
						{
							for (int z = subBiomeStartZ - deformStrength; z < maxz + deformStrength; ++z)
							{
								if (x >= seaSize && x < realChunkX - seaSize && 
									z >= seaSize && z < realChunkZ - seaSize)
								{
									// 计算到椭圆中心的距离
									float dx = (x - centerx) / radiusX;
									float dz = (z - centerz) / radiusZ;
									float distance = sqrt(dx * dx + dz * dz);
									
									// 添加噪声使边界更自然
									float noiseValue = sin(x * 0.3f + z * 0.5f) * 0.2f + 
													  cos(x * 0.7f - z * 0.3f) * 0.15f;
									distance += noiseValue;
									
									if (distance <= 1.0f)
									{
										// 根据距离边界的远近决定是否为边缘
										bool isEdge = distance > 0.8f || randInt(15) == 0;
										
										if (isEdge) {
											tmpretbuf[x + z * realChunkX] = subsecondbiome.secondBiomeid;
										} else {
											tmpretbuf[x + z * realChunkX] = subsecondbiome.mainBiomeid;
										}
									}
								}
							}
						}
					}
					else if (shapeType < 25) // 5% 概率生成星形（从15%减少到5%）
					{
						// 星形地形
						int numPoints = 5 + randInt(3); // 5-7个角
						float baseRadius = std::max(1.0f, std::min(xRange, zRange) / 2.0f);
						float innerRadius = baseRadius * (0.4f + GenRandomFloat() * 0.3f);
						
						for (int x = subBiomeStartX - deformStrength; x < maxx + deformStrength; ++x)
						{
							for (int z = subBiomeStartZ - deformStrength; z < maxz + deformStrength; ++z)
							{
								if (x >= seaSize && x < realChunkX - seaSize && 
									z >= seaSize && z < realChunkZ - seaSize)
								{
									float dx = x - centerx;
									float dz = z - centerz;
									float angle = atan2(dz, dx);
									float distance = sqrt(dx * dx + dz * dz);
									
									// 计算星形的边界半径
									float angleNorm = fmod(angle + 2 * 3.14159f, 2 * 3.14159f / numPoints);
									float t = angleNorm / (2 * 3.14159f / numPoints);
									float currentRadius = innerRadius + (baseRadius - innerRadius) * 
														(0.5f + 0.5f * cos(t * 3.14159f));
									
									// 添加噪声
									float noiseValue = sin(angle * 3) * 0.1f * baseRadius;
									currentRadius += noiseValue;
									
									if (distance <= currentRadius)
									{
										bool isEdge = distance > currentRadius * 0.85f || randInt(12) == 0;
										
										if (isEdge) {
											tmpretbuf[x + z * realChunkX] = subsecondbiome.secondBiomeid;
										} else {
											tmpretbuf[x + z * realChunkX] = subsecondbiome.mainBiomeid;
										}
									}
								}
							}
						}
					}
					else if (shapeType < 45) // 20% 概率生成岛屿状（从15%增加到20%）
					{
						// 岛屿状地形（多个连接的圆形）
						int numIslands = 2 + randInt(3); // 2-4个岛屿
						std::vector<std::pair<int, int>> islandCenters;
						
						for (int i = 0; i < numIslands; i++) {
							int xRangeHalf = std::max(1, xRange / 2);
							int zRangeHalf = std::max(1, zRange / 2);
							int offsetX = randInt(xRangeHalf) - xRangeHalf / 2;
							int offsetZ = randInt(zRangeHalf) - zRangeHalf / 2;
							islandCenters.push_back(std::make_pair(centerx + offsetX, centerz + offsetZ));
						}
						
						for (int x = subBiomeStartX - deformStrength; x < maxx + deformStrength; ++x)
						{
							for (int z = subBiomeStartZ - deformStrength; z < maxz + deformStrength; ++z)
							{
								if (x >= seaSize && x < realChunkX - seaSize && 
									z >= seaSize && z < realChunkZ - seaSize)
								{
									bool inAnyIsland = false;
									bool isEdge = false;
									
									for (const auto& center : islandCenters)
									{
										float dx = x - center.first;
										float dz = z - center.second;
										float distance = sqrt(dx * dx + dz * dz);
										
										float radius = 3 + randInt(4); // 3-6的半径
										
										// 添加噪声
										float noiseValue = sin(x * 0.5f + z * 0.3f) * 0.8f + 
														  cos(x * 0.2f - z * 0.7f) * 0.6f;
										distance += noiseValue;
										
										if (distance <= radius)
										{
											inAnyIsland = true;
											if (distance > radius * 0.75f) {
												isEdge = true;
											}
										}
									}
									
									if (inAnyIsland)
									{
										if (isEdge || randInt(18) == 0) {
											tmpretbuf[x + z * realChunkX] = subsecondbiome.secondBiomeid;
										} else {
											tmpretbuf[x + z * realChunkX] = subsecondbiome.mainBiomeid;
										}
									}
								}
							}
						}
					}
					else if (shapeType < 65) // 20% 概率生成河流状（从15%增加到20%）
					{
						// 河流状地形
						int numBranches = 2 + randInt(3); // 2-4个分支
						
						for (int branch = 0; branch < numBranches; branch++)
						{
							// 随机起始点和方向
							int xRangeThird = std::max(1, xRange / 3);
							int zRangeThird = std::max(1, zRange / 3);
							float startX = centerx + randInt(xRangeThird) - xRangeThird / 2;
							float startZ = centerz + randInt(zRangeThird) - zRangeThird / 2;
							float direction = GenRandomFloat() * 2 * 3.14159f;
							
							int length = 8 + randInt(8); // 8-15的长度
							float width = 2 + GenRandomFloat() * 2; // 2-4的宽度
							
							for (int i = 0; i < length; i++)
							{
								float currentX = startX + cos(direction) * i;
								float currentZ = startZ + sin(direction) * i;
								
								// 轻微弯曲
								direction += (GenRandomFloat() - 0.5f) * 0.3f;
								
								// 绘制这段河流
								for (int dx = -width; dx <= width; dx++)
								{
									for (int dz = -width; dz <= width; dz++)
									{
										int x = currentX + dx;
										int z = currentZ + dz;
										
										if (x >= seaSize && x < realChunkX - seaSize && 
											z >= seaSize && z < realChunkZ - seaSize)
										{
											float dist = sqrt(dx * dx + dz * dz);
											if (dist <= width)
											{
												bool isEdge = dist > width * 0.7f || randInt(10) == 0;
												
												if (isEdge) {
													tmpretbuf[x + z * realChunkX] = subsecondbiome.secondBiomeid;
												} else {
													tmpretbuf[x + z * realChunkX] = subsecondbiome.mainBiomeid;
												}
											}
										}
									}
								}
							}
						}
					}
					else // 35% 概率生成增强的不规则形状（概率保持不变）
					{
						// 高度不规则的有机形状
						// 创建更复杂的噪声边界
						std::vector<int> leftEdgeNoise(maxz - subBiomeStartZ + 1, 0);
						std::vector<int> rightEdgeNoise(maxz - subBiomeStartZ + 1, 0);
						std::vector<int> topEdgeNoise(maxx - subBiomeStartX + 1, 0);
						std::vector<int> bottomEdgeNoise(maxx - subBiomeStartX + 1, 0);
						
						// 生成更复杂的边界噪声
						for (int i = 0; i < leftEdgeNoise.size(); i++) {
							// 使用正弦波生成更平滑的边界
							float sinValue = sin(i * 0.5f + centerx * 0.1f);
							float cosValue = cos(i * 0.3f + centerz * 0.1f);
							leftEdgeNoise[i] = (sinValue + cosValue) * deformStrength / 2;
							
							// 添加随机扰动
							if (randInt(3) == 0) {
								leftEdgeNoise[i] += randInt(deformStrength + 1) - deformStrength / 2;
							}
						}
						
						for (int i = 0; i < rightEdgeNoise.size(); i++) {
							float sinValue = sin(i * 0.4f + centerx * 0.15f);
							float cosValue = cos(i * 0.6f + centerz * 0.08f);
							rightEdgeNoise[i] = (sinValue + cosValue) * deformStrength / 2;
							
							if (randInt(3) == 0) {
								rightEdgeNoise[i] += randInt(deformStrength + 1) - deformStrength / 2;
							}
						}
						
						for (int i = 0; i < topEdgeNoise.size(); i++) {
							float sinValue = sin(i * 0.35f + centerz * 0.12f);
							float cosValue = cos(i * 0.55f + centerx * 0.09f);
							topEdgeNoise[i] = (sinValue + cosValue) * deformStrength / 2;
							
							if (randInt(3) == 0) {
								topEdgeNoise[i] += randInt(deformStrength + 1) - deformStrength / 2;
							}
						}
						
						for (int i = 0; i < bottomEdgeNoise.size(); i++) {
							float sinValue = sin(i * 0.45f + centerz * 0.11f);
							float cosValue = cos(i * 0.25f + centerx * 0.13f);
							bottomEdgeNoise[i] = (sinValue + cosValue) * deformStrength / 2;
							
							if (randInt(3) == 0) {
								bottomEdgeNoise[i] += randInt(deformStrength + 1) - deformStrength / 2;
							}
						}
						
						// 添加多种特殊形状的组合
						int numSpecialShapes = 1 + randInt(3); // 1-3个特殊形状
						std::vector<std::tuple<int, int, int, int>> specialShapes; // type, x, z, size
						
						for (int i = 0; i < numSpecialShapes; i++) {
							int shapeType = randInt(8); // 0-7种不同形状
							int xRangeHalf = std::max(1, xRange / 2);
							int zRangeHalf = std::max(1, zRange / 2);
							int shapeX = centerx + randInt(xRangeHalf) - xRangeHalf / 2;
							int shapeZ = centerz + randInt(zRangeHalf) - zRangeHalf / 2;
							int shapeSize = 2 + randInt(4); // 2-5的大小
							specialShapes.push_back(std::make_tuple(shapeType, shapeX, shapeZ, shapeSize));
						}
						
						// 填充地形数据
						for (int x = subBiomeStartX - deformStrength; x < maxx + deformStrength; ++x)
						{
							for (int z = subBiomeStartZ - deformStrength; z < maxz + deformStrength; ++z)
							{
								if (x >= seaSize && x < realChunkX - seaSize && 
									z >= seaSize && z < realChunkZ - seaSize)
								{
									// 基础形状检测
									bool isInside = true;
									int relativeX = x - subBiomeStartX;
									int relativeZ = z - subBiomeStartZ;
									
									// 检查是否在变形边界内
									if (relativeZ >= 0 && relativeZ < leftEdgeNoise.size()) {
										if (x < subBiomeStartX + leftEdgeNoise[relativeZ] || 
											x >= maxx + rightEdgeNoise[relativeZ]) {
											isInside = false;
										}
									}
									
									if (relativeX >= 0 && relativeX < topEdgeNoise.size()) {
										if (z < subBiomeStartZ + bottomEdgeNoise[relativeX] || 
											z >= maxz + topEdgeNoise[relativeX]) {
											isInside = false;
										}
									}
									
									// 检查特殊形状
									for (const auto& shape : specialShapes)
									{
										int type = std::get<0>(shape);
										int sx = std::get<1>(shape);
										int sz = std::get<2>(shape);
										int size = std::get<3>(shape);
										
										int dx = x - sx;
										int dz = z - sz;
										
										bool inSpecialShape = false;
										
										switch (type)
										{
											case 0: // 圆形缺口
												if (dx * dx + dz * dz <= size * size) {
													isInside = false;
												}
												break;
											case 1: // 方形突出
												if (abs(dx) <= size && abs(dz) <= size) {
													isInside = true;
												}
												break;
											case 2: // 三角形突出
												if (abs(dx) + abs(dz) <= size) {
													isInside = true;
												}
												break;
											case 3: // L形突出
												{
													int halfSize = std::max(1, size / 2);
													if ((abs(dx) <= size && abs(dz) <= halfSize) ||
														(abs(dx) <= halfSize && abs(dz) <= size)) {
														isInside = true;
													}
												}
												break;
											case 4: // 十字形突出
												{
													int thirdSize = std::max(1, size / 3);
													if ((abs(dx) <= size && abs(dz) <= thirdSize) ||
														(abs(dx) <= thirdSize && abs(dz) <= size)) {
														isInside = true;
													}
												}
												break;
											case 5: // 椭圆形缺口
												{
													int halfSize = std::max(1, size / 2);
													if ((dx * dx) / (float)(size * size) + (dz * dz) / (float)(halfSize * halfSize) <= 1) {
														isInside = false;
													}
												}
												break;
											case 6: // 星形突出
												{
													float angle = atan2(dz, dx);
													float dist = sqrt(dx * dx + dz * dz);
													float starRadius = size * (0.7f + 0.3f * cos(angle * 3));
													if (dist <= starRadius) {
														isInside = true;
													}
												}
												break;
											case 7: // 螺旋缺口
												{
													float angle = atan2(dz, dx);
													float dist = sqrt(dx * dx + dz * dz);
													float spiralRadius = size * (0.5f + 0.5f * sin(angle * 2 + dist * 0.5f));
													if (dist <= spiralRadius) {
														isInside = false;
													}
												}
												break;
										}
									}
									
									if (isInside) {
										// 增强的边缘检测
										bool isEdge = false;
										
										// 检查是否靠近边界
										if (relativeZ >= 0 && relativeZ < leftEdgeNoise.size()) {
											if (x <= subBiomeStartX + leftEdgeNoise[relativeZ] + 1 || 
												x >= maxx + rightEdgeNoise[relativeZ] - 1) {
												isEdge = true;
											}
										}
										
										if (relativeX >= 0 && relativeX < topEdgeNoise.size()) {
											if (z <= subBiomeStartZ + bottomEdgeNoise[relativeX] + 1 || 
												z >= maxz + topEdgeNoise[relativeX] - 1) {
												isEdge = true;
											}
										}
										
										// 检查特殊形状边界
										for (const auto& shape : specialShapes)
										{
											int sx = std::get<1>(shape);
											int sz = std::get<2>(shape);
											int size = std::get<3>(shape);
											
											int dx = x - sx;
											int dz = z - sz;
											
											if (abs(dx) <= size + 1 && abs(dz) <= size + 1) {
												isEdge = true;
												break;
											}
										}
										
										// 随机边界噪声
										if (randInt(25) == 0) {
											isEdge = true;
										} else if (isEdge && randInt(12) == 0) {
											isEdge = false;
										}
										
										if (isEdge) {
											tmpretbuf[x + z * realChunkX] = subsecondbiome.secondBiomeid;
										} else {
											tmpretbuf[x + z * realChunkX] = subsecondbiome.mainBiomeid;
										}
									}
								}
							}
						}
					}
					
					// 计算实际边界框 - 为了更新LandSubBiomeData
					int effectiveLeft = subBiomeStartX - deformStrength;
					int effectiveRight = maxx + deformStrength;
					int effectiveTop = subBiomeStartZ - deformStrength;
					int effectiveBottom = maxz + deformStrength;
					
					// 边界安全检查
					effectiveLeft = std::max(effectiveLeft, seaSize);
					effectiveRight = std::min(effectiveRight, realChunkX - seaSize);
					effectiveTop = std::max(effectiveTop, seaSize);
					effectiveBottom = std::min(effectiveBottom, realChunkZ - seaSize);
					
					LandSubBiomeData landSubBiomeData;
					landSubBiomeData.bottomLeft = ChunkIndex(effectiveLeft + sqrtLB.x, effectiveTop + sqrtLB.z);
					landSubBiomeData.topRight = ChunkIndex(effectiveRight + sqrtLB.x, effectiveBottom + sqrtLB.z);
					landSubBiomeData.biomeid = subsecondbiome.mainBiomeid;
					landSubBiomeData.secondBiomeid = subsecondbiome.secondBiomeid;
					landSubBiomeData.scaleType = selectedSubBiome.secondBiomeType;
					landSubBiomeDatas.push_back(landSubBiomeData);
					selectedSubBiome.count--;

					LOG_INFO_BUILD("#####Create Map record subBiome mainid = %d, secondid = %d, startchunk x = %d, z = %d, endchunk x = %d, z = %d"
						, subsecondbiome.mainBiomeid, subsecondbiome.secondBiomeid, landSubBiomeData.bottomLeft.x, landSubBiomeData.bottomLeft.z, landSubBiomeData.topRight.x, landSubBiomeData.topRight.z);
		}
	}

	LOG_INFO_BUILD("#####Create Map record subBiome count = %d"
		, landSubBiomeDatas.size());
	LogStringMsg("!~genTerrgen size = %d ox = %d, oz = %d, xlen = %d zlen = %d", tmpretbuf.size(), sqrtLB.x, sqrtLB.z, realChunkX, realChunkZ);
	std::string str = "";
	for (size_t i = 0; i < realChunkZ; i++)
	{
		str.clear();
		for (size_t j = 0; j < realChunkX; j++)
		{
			str += std::to_string(tmpretbuf[i * realChunkX + j]) + ",";
		}
		if (str.length() > 0)
		{
			LogStringMsg(str.c_str());
		}
	}
	LogStringMsg("!~landSubBiomeDatas size = %d", landSubBiomeDatas.size());
	for (auto& landSubBiomeData : landSubBiomeDatas)
	{
		LogStringMsg("!~landSubBiomeData: bottomLeft = %d, %d, topRight = %d, %d, biomeid = %d", landSubBiomeData.bottomLeft.x, landSubBiomeData.bottomLeft.z, landSubBiomeData.topRight.x, landSubBiomeData.topRight.z, landSubBiomeData.biomeid);
	}
}

void LinearTerrainIDGenerator::genTerrgen(ChunkGenerator* chunkgen)
{
	// 计算动态的scale，根据realChunkX计算
	int realChunkX = chunkgen->getEndChunkX() - chunkgen->getStartChunkX() + 1;
	int realChunkZ = chunkgen->getEndChunkZ() - chunkgen->getStartChunkZ() + 1;
	
	LogStringMsg("!~genTerrgen startX = %d, startZ = %d, endX = %d, endZ = %d", chunkgen->getStartChunkX(), chunkgen->getStartChunkZ(), chunkgen->getEndChunkX(), chunkgen->getEndChunkZ());
	// 验证地图尺寸
	if (realChunkX <= 0 || realChunkZ <= 0 || realChunkX%4 != 0)
	{
		LOG_WARNING("LinearTerrainIDGenerator genTerrgen chunkLength error!");
		//assert(0);
		//return;
	}
	ConstAtLua* lua_const = GetLuaInterfaceProxy().get_lua_const();
	int mapSizeType = MAP_ERROR_SIZE;
	if (realChunkX >= lua_const->SocLargeMapSizeMin && realChunkZ >= lua_const->SocLargeMapSizeMin)
	{
		mapSizeType = MAP_BIG_SIZE;
	}
	else if (realChunkX < lua_const->SocLargeMapSizeMin && realChunkZ < lua_const->SocLargeMapSizeMin
		&& realChunkX >= lua_const->SocMediumMapSizeMin && realChunkZ >= lua_const->SocMediumMapSizeMin)
	{
		mapSizeType = MAP_MIDDLE_SIZE;
	}
	else if (realChunkX >= lua_const->SocSmallMapSizeMin && realChunkZ >= lua_const->SocSmallMapSizeMin)
	{
		mapSizeType = MAP_SMALL_SIZE;
	}
	initSubBiomeData(mapSizeType);
	// 三种预设的sqrtScale值
	const int PRESET_SCALES[3] = {4, 8, 16};
	const int TARGET_MAP_SIZE = 25; // 目标地图大小
	
	// 选择最佳的sqrtScale
	int sqrtScale = PRESET_SCALES[0]; // 默认使用第一个值
	int closestDiff = abs(realChunkX / PRESET_SCALES[0] - TARGET_MAP_SIZE);
	
	for (int i = 1; i < 3; i++) 
	{
		int currentScale = PRESET_SCALES[i];
		int currentMapSize = realChunkX / currentScale;
		int currentDiff = abs(currentMapSize - TARGET_MAP_SIZE);
		
		if (currentDiff < closestDiff) 
		{
			sqrtScale = currentScale;
			closestDiff = currentDiff;
		}
	}
	
	// 检查是否能整除
	if (realChunkX % sqrtScale != 0) 
	{
		LOG_WARNING("所选sqrtScale(%d)不能整除realChunkX(%d)，可能导致边界问题!", sqrtScale, realChunkX);
	}
	
	// 确保scale是sqrtScale的平方
	int scale = sqrtScale * sqrtScale;
	int targetMapSize = realChunkX / sqrtScale;
	
	LOG_INFO("选择的地形比例: scale=%d (sqrt=%d), 最终地图大小约为%d (目标: %d)", 
	          scale, sqrtScale, targetMapSize, TARGET_MAP_SIZE);
	
	int lg2Scale = 0;
	{
		lg2Scale = std::log2(scale) / LOG_SCALE_ZOOM;
		// int cscale = std::pow(SCALE_ZOOM, lg2Scale);
		// if (cscale != scale)
		// {
		// 	LOG_WARNING("LinearTerrainIDGenerator genTerrgen scale not suit, scale:%d, cscale:%d!", scale, cscale);
		// 	return;
		// 	assert(0);
		// }
		// scale = cscale;
	}

	//先给个默认的
	TGenUnitLine* newLayer = ENG_NEW(TGenUnitLine)(1000, 0, sqrtScale);
	TerrIDGenUnit* curLayer = newLayer;
	m_RootGen = curLayer;
	m_RootIndex = curLayer;

	clearData();
	//先把layer之类的初始化了

	if (realChunkX % sqrtScale != 0 || realChunkZ % sqrtScale != 0)
	{
		LOG_WARNING("LinearTerrainIDGenerator genTerrgen chunkLength not suit, sizeX:%d, sizeZ:%d!", realChunkX, realChunkZ);
		//assert(0);
		//return;
	}
	int mapSizeX = realChunkX / sqrtScale;
	int mapSizeZ = realChunkZ / sqrtScale;
	if (mapSizeX <= 0 || mapSizeX > MAP_SIZE_MAX || mapSizeZ <= 0 || mapSizeZ > MAP_SIZE_MAX)
	{
		LOG_WARNING("LinearTerrainIDGenerator genTerrgen mapSize not suit, sizeX:%d, sizeZ:%d!", mapSizeX, mapSizeZ);
		assert(0);
		return;
	}
	int scaleNum = lg2Scale;
	// 可能在不同层级会插入其他地形.
	
	for (int zoomIndex = 0; zoomIndex < scaleNum; zoomIndex++)
	{
		curLayer = TGenUnitZoom::magnify(1000, curLayer, 1);
	}

	curLayer = ENG_NEW(SOCLandTGenUnit)(1000, curLayer, 0);
	SOCLandTGenUnit* psocland = (SOCLandTGenUnit*)curLayer;

	curLayer = TGenUnitZoom::magnify(1000, curLayer, 2);
	TGenUnitZoom* plastzoom = (TGenUnitZoom*)curLayer;


	// if (GetCityConfigInterface())
	// {
	// 	curLayer = ENG_NEW(TGenUnitSpecialSubOrderBiomeRandomFill)(1000, curLayer, 0);
	// }

	m_RootGen = curLayer;

	curLayer = ENG_NEW(TGenUnitVoronoiZoom)(10, curLayer);
	m_RootIndex = curLayer;
	curLayer->resetWorldSeed(m_WorldGenSeed);
	resetWorldSeed(m_WorldGenSeed);

	createTree(mapSizeX, mapSizeZ, sqrtScale);

	auto& idPos = newLayer->m_Poss;
	idPos.resize(m_nodes.size());
	newLayer->m_mapX = m_mapX;
	newLayer->m_mapZ = m_mapZ;
	ChunkIndex mapBottomLeft(chunkgen->getStartChunkX() / sqrtScale, chunkgen->getStartChunkZ() / sqrtScale);
	newLayer->m_bottomIndex = mapBottomLeft;
	for (int nodeIndex = 0; nodeIndex < m_nodes.size(); nodeIndex++)
	{
		idPos[nodeIndex].x = m_nodes[nodeIndex].x;
		idPos[nodeIndex].z = m_nodes[nodeIndex].z;
		idPos[nodeIndex].biomeId = m_nodes[nodeIndex].biomeid;
	}
	
	std::vector<int> test;
	plastzoom->genTerrainIDs(test, chunkgen->getStartChunkX(), chunkgen->getStartChunkZ(), realChunkX, realChunkZ, nullptr);
		
	LogStringMsg("!~genTerrgen testdata size = %d ox = %d, oz = %d, xlen = %d zlen = %d", test.size(), chunkgen->getStartChunkX(), chunkgen->getStartChunkZ(), realChunkX, realChunkZ);
	std::string ts = "";
	for (size_t i = 0; i < realChunkZ; i++)
	{
		ts.clear();
		for (size_t j = 0; j < realChunkX; j++)
		{
			ts += std::to_string(test[i * realChunkX + j]) + ",";
		}
		if (ts.length() > 0)
		{
			LogStringMsg(ts.c_str());
		}
	}

	int sqrtX = 0;
	int sqrtZ = 0;
	int seaSize = GetSeaSize() * sqrtScale + 2;
	m_nRealSeaSize = seaSize;
	std::vector<int>& tmpretbuf = psocland->m_wholeChunksBiomeIds;
	// std::vector<int> tmpretbuf((realChunkX + 1) * (realChunkZ + 1), BIOME_OCEAN);
	tmpretbuf.resize((realChunkX + 1) * (realChunkZ + 1), BIOME_OCEAN);
	const auto& baseShapeMap = newLayer->m_Poss;
	ChunkIndex sqrtLB(chunkgen->getStartChunkX(), chunkgen->getStartChunkZ());
	psocland->m_bottomIndex = sqrtLB;
	psocland->m_mapX = realChunkX;
	psocland->m_mapZ = realChunkZ;
	for (int maxsizex = seaSize; maxsizex < realChunkX - seaSize; maxsizex++)
	{
		for (int maxsizez = seaSize; maxsizez < realChunkZ - seaSize; maxsizez++)
		{
			sqrtX = (maxsizex) / sqrtScale;
			sqrtZ = (maxsizez) / sqrtScale;
			
			auto p = std::find_if(baseShapeMap.begin(), baseShapeMap.end(), [sqrtX, sqrtZ](const TerrgenRange& range)
			{
				return range.x == sqrtX && range.z == sqrtZ;
			});
			if (p != baseShapeMap.end())
			{
				tmpretbuf[maxsizex + maxsizez * realChunkX] = p->biomeId;
			}
		}
	}
	auto& landSubBiomeDatas = psocland->m_LandSubBiomeDatas;
	initWholeChunksBiomeIds(tmpretbuf, landSubBiomeDatas, realChunkX, realChunkZ, seaSize, sqrtLB);
	//psocland->genTerrainIDs(tmpretbuf, chunkgen->getStartChunkX(), chunkgen->getStartChunkZ(), realChunkX + 1, realChunkZ + 1, nullptr);
}

void LinearTerrainIDGenerator::initTreeNode(int gridlen)
{
}

void LinearTerrainIDGenerator::clearTreeNode()
{
	m_nodes.clear();
}

bool LinearTerrainIDGenerator::nodeIsUseByIndex(int nodeIndex)
{
	if (nodeIndex < 0 || nodeIndex >= m_nodes.size())
	{
		LOG_WARNING("LinearTerrainIDGenerator nodeGet failed, getIndex:%d", nodeIndex);
		assert(0);
		return false;
	}
	return m_nodes[nodeIndex].use;
}

void LinearTerrainIDGenerator::clearData()
{
	m_mapX = 0;
	m_mapZ = 0;
	m_maps.clear();
	m_nodes.clear();
	std::stack<int> tmp;
	m_stack.swap(tmp);
}

