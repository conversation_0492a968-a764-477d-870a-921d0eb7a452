
#pragma once

#include "BlockMaterial.h"
#include "BlockBed.h"
#include "container_world.h"

class BlockCanvas : public ModelBlockMaterial ,public BedLogicInterface //tolua_exports
{ //tolua_exports
	DECLARE_BLOCKMATERIAL(BlockCanvas)
	//typedef ModelBlockMaterial Super;
public:
	//tolua_begin
	BlockCanvas();
	virtual void init(int resid) override;
	//virtual const char *getGeomName() override;
	virtual bool onTrigger(World* pworld, const WCoord& blockpos, DirectionType face, IClientPlayer* player, const Rainbow::Vector3f& colpoint = Rainbow::Vector3f(0, 0, 0))override;
	
	virtual void createCollideData(CollisionDetect *coldetect, World *pworld, const WCoord &blockpos) override;
	virtual void onBlockAdded(World* pworld, const WCoord& blockpos) override;
	virtual void onBlockRemoved(World *pworld, const WCoord &blockpos, int blockid, int blockdata) override;
	//virtual bool hasContainer()const override;
	virtual WorldCanvas* createContainer(World* pworld, const WCoord& blockpos) override;
	virtual void createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh) override;
	
	virtual void onBlockPlacedBy(World* pworld, const WCoord& blockpos, IClientPlayer* player) override;
	virtual int getPlaceBlockDataByPlayer(World* pworld, IClientPlayer* player) override;
	void clearNormalBlock(World* pworld, const WCoord& blockpos, int blockdata = -1);
	virtual int getPlaceBlockData(World *pworld, const WCoord &blockpos, DirectionType face, float hitptx, float hitpty, float hitptz, int def_blockdata)override;
	virtual bool canPutOntoPlayer(World* pworld, const WCoord& blockpos, IClientPlayer* player) override;
	virtual void dropBlockAsItem(World* pworld, const WCoord& blockpos, int blockdata = 0, BLOCK_MINE_TYPE droptype = BLOCK_MINE_NOTOOL, float chance = 1.0f, int uin = -1) override;

	////////////////////////////logic/////////////////////////////////
	virtual bool IsBedOccupied(World* pworld, const WCoord& blockpos, int blockdata) override;
	virtual void setBedOccupied(World* pworld, const WCoord& blockpos, bool occupied) override;
	virtual bool getNearestEmptyChunkCoordinates(WCoord& ret, World* pworld, const WCoord& blockpos, int loopcount) override;
	virtual void getEyePosInBed(World* pworld, const WCoord& sleeppos, WCoord& eyepos, Rainbow::Vector3f& lookdir) override;
	virtual WORLD_ID getBindActor(World* pworld, const WCoord& blockpos) override;
	virtual void setBindActor(World* pworld, const WCoord& blockpos, WORLD_ID bindactor) override;
	virtual WCoord getSleepPosition(World* pworld, const WCoord& blockpos) override;
	virtual WorldContainer* getCoreContainer(World* pworld, const WCoord& blockpos) override;
	virtual bool IsSleepNeedHide(World* pworld, const WCoord& blockpos) override { return true; } ;

	bool isCoreBlock(World* pworld, const WCoord& blockpos);
	WCoord getCoreBlockPos(World* pworld, const WCoord& blockpos, int blockdata = -1);
	WCoord getSleepEffectPos(World* pworld, const WCoord& blockpos);
	bool haveActorSleep(World* pworld, const WCoord& blockpos);
	//tolua_end
protected:
	virtual int doSleep(World* pworld, IClientPlayer* player, const WCoord& blockpos)const;
	virtual int getBlockGeomID(int* idbuf, int* dirbuf, const SectionDataHandler* sectionData, const WCoord& blockpos, World* world)override;
private:
	void setCoreBlockData(World* pworld, const WCoord& blockpos,int blockData);
	int getCoreBlockData(World* pworld, const WCoord& blockpos);
	WorldCanvas * sureContainer(World* pworld,const WCoord& blockpos);
	virtual void initGeomName() override;
}; //tolua_exports