

#include "PlayerCheat.h"
#include <cstring>
#include "Common/OgreShared.h"
#include "ClientPlayer.h"
#include "OgreTimer.h"
#include "MpActorManager.h"
#include "MpActorTrackerEntry.h"
#include "GameNetManager.h"
#include "ClientErrCode.h"
#include "RiddenComponent.h"
#include <map>
#include "ClientActorFuncWrapper.h"
#include "backpack.h"
#include "DefManagerProxy.h"
#include "Collision.h"
#include "BlockMaterialMgr.h"
#include "Platforms/PlatformInterface.h"
#include "ICloudProxy.h"
#include "IClientGameManagerInterface.h"
#include "PlayerLocoMotion.h"
#include "OgreStringUtil.h"
#include "SandboxGFunc.h"
#include "GameModeDef.h"
#include "GunUseComponent.h"
#include "WorldManager.h"
#include "SandboxIdDef.h"
#include "PermitsDef.h"
#include "SandboxResult.h"
#include "WorldManager.h"
#include "ClientInfoProxy.h"

#include "LivingAttrib.h"
#include "ActorLocoMotion.h"
#include "Components/CapsuleCollider.h"
#include "util/SandboxConfig.h"
#include "Debug/DebugMgr.h"
#include "GameScene/MovableObject.h"
#include "chunk.h"
#include "ZmqProxy.h"
#include "Network/Http/WebRequest.h"

using namespace Rainbow;
using namespace MINIW;
using namespace MNSandbox;
#define CHEAT_CHECK_JETFLY 0
#define CHEAT_CHECK_ONGROUND 0

#ifndef test_flag_bit
#define test_flag_bit(flag, bit) (bool)(flag & (1 << (bit - 1)))
#endif

#ifndef set_change_flag_bit2
#define set_change_flag_bit2(changeFlag, bit, val) {if (val) {changeFlag |= (1 << (bit - 1));} else {changeFlag &= ~(1 << (bit - 1));} }
#endif


const unsigned AntiSetting::CheatConfig::StayStep;
const unsigned AntiSetting::CheatConfig::UpStep;
const unsigned AntiSetting::CheatConfig::UpCount;
const unsigned AntiSetting::CheatConfig::SwingStep;
const unsigned AntiSetting::CheatConfig::FlyCheatKick;
const int      AntiSetting::CheatConfig::s_MaxClickCount;


// 返回当前是否是单机或编辑模式
bool _isSingleOrGodMode()
{
	if (!g_WorldMgr)
		return false;
	if (g_WorldMgr->isGodMode())  // 编辑模式
		return true;
	if (GetClientInfoProxy()->getMultiPlayer() == 0)  // 单机模式
		return true;
	return false;
}
// 配置个检测的容错范围
namespace AntiSetting {
	CheatConfig gCheatConfig;

	// 此值为闭区间值，即大于该值才认为作弊
	static map<int, int> s_setting = {
		{CHEAT_TAG_ONGROUND, 10},
		{CHEAT_TAG_JETFLY, 20},
		{CHEAT_TAG_FLYMOD, 2},
		{CHEAT_TAG_SPEED, 10},
		{CHEAT_TAG_FLY_STAY, 1},
		{CHEAT_TAG_FLY_UP, 3},
		{CHEAT_TAG_JUMP_HEIGHT, 3},
		{CHEAT_TAG_TACKLE, 3},
		{CHEAT_TAG_GRAB, 3},
		{CHEAT_TAG_DRIBBLE, 3},
		{CHEAT_TAG_HOST_CLIP, 3},
		{CHEAT_TAG_FLAG, 2},
		{CHEAT_TAG_AD_SHOP, 1},
		{CHEAT_TAG_MUSIC_PAPER_GAIN, 2},
		{CHEAT_TAG_SHOOT, 2},
		{CHEAT_TAG_HEARTBEAT, 1},
		{CHEAT_TAG_ROTATION, 3},
		{CHEAT_TAG_WATER, 3},
	};
	// 移动检测日志间隔
	unsigned LogStep = 60 * 1000;

	// true表示需要上报作弊了
	bool judgeCheat(CHEAT_CHECK_TAG tag, int count)
	{
		return count > s_setting[tag];
	}

	std::map<int, unsigned long long> g_PlayerKicked;
	// 以下开关型值，使用位标记，第一位开关，第二位提示 第三位踢人
    // 例如 0关闭 1检测 3检测+提示 5检测+踢人
	bool IsSwitchOff(unsigned value) { return value == 0; }
	bool IsNotifyOn(unsigned value) { return test_flag_bit(value, 2); }
	bool IsKickOn(unsigned value) { return test_flag_bit(value, 3); }
	/**
	 * @brief 判断uin是否因为触发作弊被踢出游戏
	 * @note 只在uin被踢出一段时间内生效, 踢出一段时间后会返回false
	 * @return true 触发作弊踢出
	*/
	bool IsPlayerCheatKicked(int uin)
	{
		auto it = g_PlayerKicked.find(uin);
		if (it != g_PlayerKicked.end())
		{
			unsigned long long now = GetIClientGameManagerInterface()->getICurGame()? GetIClientGameManagerInterface()->getICurGame()->getGameTick(): 0;
			if (now == 0)  // 游戏加载中, 此时g_PlayerKicked列表中的id一律被认为触发了作弊踢出
				return true;
			if (it->second > now)
			{  // 游戏tick已被重置
				g_PlayerKicked.erase(it);
				return false;
			}
			if (now - it->second > gCheatConfig.CheatKickForbidTick)
			{  // 被踢出时间已超过范围, 可以解除状态
				g_PlayerKicked.erase(it);
				return false;
			}
			return true;
		}
		return false;
	}
	void CheatKickPlayer(int uin, int error_code)
	{
		g_PlayerKicked[uin] = GetIClientGameManagerInterface()->getICurGame()? GetIClientGameManagerInterface()->getICurGame()->getGameTick(): 0;
		if (GameNetManager::getInstance()->getConnection())
			GameNetManager::getInstance()->getConnection()->kickoffMember(uin, error_code);
			
		jsonxx::Object log;
		log.import("error_code", error_code);
		GetICloudProxyPtr()->InfoLog(uin, 0, "cheat_kick", log);
		GetICloudProxyPtr()->SimpleSLOG("CheatKickPlayer, uin=%d err=%d, forbid message in %d tick", uin, error_code, gCheatConfig.CheatKickForbidTick);
	}

	TimesRecorder g_MapItemTimesRecorder(NULL, "map_item_times", false);

	float GetRuntimeFloat(){
		const static float val = Rainbow::Timer::getTimeUS() & 0xffff;
		return val;
	}
	unsigned GetRuntimeUnsigned(){
		const static unsigned val = Rainbow::Timer::getTimeUS() & 0xffff;
		return val;
	}
	int getRandomInt(int min, int max)
	{
		static ChunkRandGen RandomGen(Rainbow::Timer::getTimeUS());
		return RandomGen.get(min, max);
	}

	int ProtocolHostIn = 0;  // 记录当前本机(主机)正在处理的协议ID
	int ProtocolSender = 0;  // 记录当前本机(主机)正在处理的协议的发送者ID
	std::string EventHostIn = "";  // 记录当前本机(主机)正在处理的事件名
	std::string HttpRequestHostIn = "";  // 记录当前本机(主机)正在处理的HTTP请求名
	std::string LuaMessageHostIn = "";  // 记录当前本机(主机)正在处理的Lua消息名
	uint64_t PickupItem = 0; // 记录当前本机(主机)正在处理的物品ID

	void setInProtocol(int protocol_id, int sender){
#ifdef IWORLD_SERVER_BUILD
		ProtocolSender = sender;
		ProtocolHostIn = protocol_id;
#endif
	}
	void outProtocol(){
#ifdef IWORLD_SERVER_BUILD
		ProtocolHostIn = 0;
		ProtocolSender = 0;
#endif
	}
	void recordGainItem(int uin, int item_id, int num, int old_id = 0, int old_count = 0){
#ifdef IWORLD_SERVER_BUILD

		bool bTrySave = false;
        SandboxResult sandboxResult = SandboxEventDispatcherManager::GetGlobalInstance().Emit("ArchiveManager_getNeedSyncArchive");
        if (sandboxResult.IsExecSuccessed())
        {
            bTrySave = sandboxResult.GetData_Bool();
        }

		if (!bTrySave)
		    return;
		if (!GetWorldManagerPtr() || GetWorldManagerPtr()->isSurviveMode() || GetWorldManagerPtr()->isGodMode())
			return;
		{
			char key_buf[128];

			ClientPlayer * player = NULL;
			if (uin && g_WorldMgr)
			{
				auto iplayer = g_WorldMgr->getPlayerByUin(uin);
				player = iplayer ? iplayer->GetPlayer() : nullptr;
			}
			jsonxx::Object ob;
			if (ProtocolHostIn && ProtocolSender)
			{
				snprintf(key_buf, sizeof(key_buf), "g_%d_%d", item_id, ProtocolHostIn);
				ob << "protocol" << ProtocolHostIn << "sender" << ProtocolSender;
			}
			else
				snprintf(key_buf, sizeof(key_buf), "g_%d", item_id);
			ob << "item" << item_id << "num" << num << "type" << "gain";
			if (!EventHostIn.empty())
                ob << "event" << EventHostIn;
            if (!HttpRequestHostIn.empty())
                ob << "http" << HttpRequestHostIn;
            if (!LuaMessageHostIn.empty())
				ob << "lua_message" << LuaMessageHostIn;
			if (old_id)
				ob << "old_id" << old_id << "old_count" << old_count;
			if (PickupItem)
				ob << "objid" << PickupItem;
			if (player && player->GetCheatHandler())
				player->GetCheatHandler()->getItemTimesRecorder().recordMessage(key_buf, ob);
			else
			{
				ob << "uin" << uin;
				g_MapItemTimesRecorder.recordMessage(key_buf, ob);
			}
		}
		if (gCheatConfig.SwitchRecordGainItem)
		{
			jsonxx::Object cheat;
			cheat << "item" << item_id;
			cheat << "count" << num;
			if (ProtocolHostIn){
				cheat << "protocol" << ProtocolHostIn;
				cheat << "sender" << ProtocolSender;
			}
			if (old_id){
				cheat << "old_id" << old_id;
				cheat << "old_count" << old_count;
			}
			Rainbow::GetICloudProxyPtr()->InfoLog(uin, 0, "cheat_item_record", cheat);
		}
#endif
	}
	/**
	 * @brief 记录地图上掉落的物品
	 * @param objid 物品生成的对象ID
	 * @param item_id 物品ID
	 * @param num 物品数量
	 */
	void recordMapItem(int64_t objid, int item_id, int num){
#ifdef IWORLD_SERVER_BUILD
        bool bTrySave = false;
        SandboxResult sandboxResult = SandboxEventDispatcherManager::GetGlobalInstance().Emit("ArchiveManager_getNeedSyncArchive");
        if (sandboxResult.IsExecSuccessed())
        {
            bTrySave = sandboxResult.GetData_Bool();
        }

		if (!bTrySave)
		    return;
		
		if (!GetWorldManagerPtr() || GetWorldManagerPtr()->isSurviveMode() || GetWorldManagerPtr()->isGodMode())
			return;
		char key_buf[128];

		ClientPlayer * player = NULL;
		if (ProtocolHostIn && ProtocolSender)
		{
			if (g_WorldMgr)
			{
				auto iplayer = g_WorldMgr->getPlayerByUin(ProtocolSender);
				player = iplayer ? iplayer->GetPlayer() : nullptr;
			}
			snprintf(key_buf, sizeof(key_buf), "map_%d_%d", item_id, ProtocolHostIn);
		}
		else
			snprintf(key_buf, sizeof(key_buf), "map_%d", item_id);
		jsonxx::Object ob;
		ob << "objid" << objid << "item" << item_id << "num" << num  << "type" << "map";
		if (ProtocolHostIn)
			ob << "protocol" << ProtocolHostIn;
		if (player && player->GetCheatHandler())
			player->GetCheatHandler()->getItemTimesRecorder().recordMessage(key_buf, ob);
		else
			g_MapItemTimesRecorder.recordMessage(key_buf, ob);
#endif
	}
	/**
	 * @brief 记录玩家拾取地图上掉落的物品
	 * @param objid 物品生成的对象ID
	 * @param item_id 物品ID
	 * @param num 物品数量
	 * @param uin 拾取者uin
	 */
	void recordMapItemPick(int objid, int item_id, int num, int uin){
#ifdef IWORLD_SERVER_BUILD
// 停用
/*
		if (gCheatConfig.SwitchRecordGainItem)
		{
			if (!ArchiveManager::getSingletonPtr()->getNeedSyncArchive())
				return;
			jsonxx::Object cheat;
			cheat << "item" << item_id;
			cheat << "count" << num;
			cheat << "objid" << objid;
			if (ProtocolHostIn){
				cheat << "protocol" << ProtocolHostIn;
				cheat << "sender" << ProtocolSender;
			}
			cheat << "p" << 1;
			Rainbow::GetICloudProxyPtr()->InfoLog(uin, 0, "cheat_map_item_record", cheat);
		}
*/
#endif
	}

	bool CheckUserAuth(int uin, const std::string& clientAuth)
	{
#ifdef WINDOWS_SERVER
		return true;
#endif
		
#ifdef IWORLD_SERVER_BUILD
		auto gp = GetClientInfoProxy();
		int env = gp->getGameData("game_env");
		if (env % 10 == 1)
			return true;
		if (gCheatConfig.NotCheckAuth != 0)
			return true;

		std::vector<std::string> ret;
		StringUtil::split(ret, clientAuth, "|");
		if (clientAuth.size() < 32 || ret.size() != 4)
			return true;

		const string& authClient = ret[0];
		const string& sign = ret[1];
		if (sign.size() == 0)
			return true;

		const string& s2t = ret[2];
		const string& clientTime = ret[3];
		static std::vector<std::string> s_KeyArr;
		static bool s_InitKey = false;
		if (!s_InitKey)
		{
			s_InitKey = true;
			if (Rainbow::GetICloudProxyPtr()->IsUseNewAuth())
			{
				s_KeyArr.push_back(gp->getEnterParam("auth1"));
				s_KeyArr.push_back(gp->getEnterParam("auth2"));
			}
			s_KeyArr.push_back("c8c93222583741bd828579b3d3efd43b");
		}

		for (auto& key : s_KeyArr)
		{
			if (key.empty())
				continue;
			char buff[100] = { 0 };
			sprintf(buff, "%u%s%s", uin, key.c_str(), s2t.c_str());
			string s2Server = gFunc_getmd5(buff);
			if (s2Server == sign)
				return true;
		}

		jsonxx::Object cheat;
		cheat << "client_sign" << sign;
		cheat << "client_s2t" << s2t;
		cheat << "client_time" << clientTime;
		Rainbow::GetICloudProxyPtr()->InfoLog(uin, 0, "cheat_other_login", cheat);
		Rainbow::GetICloudProxyPtr()->SimpleErrLog(uin, 0, "role_enter_err", "MpGameSurviveNetHandler::handleRoleEnterWorld2Host() cheat other login");

		return false;

#else
		return true;
#endif
	}
	void setInEvent(const std::string& event)
	{
		#ifdef DEDICATED_SERVER
		AntiSetting::EventHostIn = event;
		#endif
	}
	void setOutEvent()
	{
		#ifdef DEDICATED_SERVER
		AntiSetting::EventHostIn = "";
		#endif
	}
	EventRecorder::EventRecorder(const std::string& event)
	{
		setInEvent(event);
	}
	EventRecorder::~EventRecorder()
	{
		setOutEvent();
	}

	PickupRecorder::PickupRecorder(int64_t objid)
	{
		PickupItem = objid;
	}
	PickupRecorder::~PickupRecorder()
	{
		PickupItem = 0;
	}

	void recordPlaceBlock(int block_id, int data, int world_id, int x, int y, int z)
	{
#ifdef IWORLD_SERVER_BUILD
		if (GetICloudProxyPtr()->IsBlockRecordPlace(block_id))
		{
			jsonxx::Object cheat;
			cheat << "block_id" << block_id;
			cheat << "data" << data;
			jsonxx::Array pos;
			pos << world_id << x << y << z;
			cheat << "pos" << pos;
			if (ProtocolHostIn)
				cheat << "protocol" << ProtocolHostIn;
			GetICloudProxyPtr()->InfoLog(ProtocolSender, 0, "cheat_place_block", cheat);
		}
#endif
	}

	HostProtocolLimiter::~HostProtocolLimiter()
	{
		for (auto it=m_RoleProtocolLimiter.begin(); it != m_RoleProtocolLimiter.end(); ++it)
		{
			delete it->second;
		}
		m_RoleProtocolLimiter.clear();

		for (auto it=m_ProtocolLimitConfigMap.begin(); it != m_ProtocolLimitConfigMap.end(); ++it)
		{
			if (it->second)
				delete it->second;
		}
		m_ProtocolLimitConfigMap.clear();
	}
	/**
	 * @brief 从json array加载 period, limit 
	 * @param 日志用key
	 * @return true 加载成功
	*/
	inline bool loadNumberFromJsonPair(const jsonxx::Array & pair, int &period, int &limit, const std::string &key)
	{
		if (pair.size() < 2)
		{
			GetICloudProxyPtr()->SimpleSLOG("%s array size<2; key=%s", __FUNCTION__, key.c_str());
			return false;
		}
		if (!pair.has<jsonxx::Number>(0) || !pair.has<jsonxx::Number>(1))
		{
			GetICloudProxyPtr()->SimpleSLOG("%s pair not number; key=%s", __FUNCTION__, key.c_str());
			return false;
		}

		period = pair.get<jsonxx::Number>(0);
		limit = pair.get<jsonxx::Number>(1);
		{
			GetICloudProxyPtr()->SimpleSLOG("%s key=%s period=%d limit=%d", __FUNCTION__, key.c_str(), period, limit);
		}
		return true;
	}

	/**
	 * @brief 从json array 加载 配置列表
	 * @param 日志用key
	 * @return 加载的数量
	*/
	inline int loadConfigFromJsonArray(const jsonxx::Array & limits, std::vector<st_period_limit> & config, const std::string &key)
	{
		int load_count = 0;
		int period, limit;
		config.clear();
		config.reserve(limits.size());
		for (int i=0; i<limits.size(); ++i)
		{
			if (!limits.has<jsonxx::Array>(i))
			{
				GetICloudProxyPtr()->SimpleSLOG("%s config not pair array; key=%s index=%d", __FUNCTION__, key.c_str(), i);
				continue;
			}
			char tmp[8];
			snprintf(tmp, sizeof(tmp), "_%d", i);
			const jsonxx::Array &config_unit_array = limits.get<jsonxx::Array>(i);
			if (!loadNumberFromJsonPair(config_unit_array, period, limit, key + tmp))
			{
				GetICloudProxyPtr()->SimpleSLOG("%s config load pair error; key=%s index=%d", __FUNCTION__, key.c_str(), i);
				continue;
			}
			if (period > st_period_limit::PERIOD_MAX || period < 0)
			{
				GetICloudProxyPtr()->SimpleSLOG("%s config load period %d warning, set to max; key=%s index=%d", __FUNCTION__, period, key.c_str(), i);
				period = st_period_limit::PERIOD_MAX;
			}
			if (config_unit_array.size()>= 3 && config_unit_array.has<jsonxx::Number>(2))
				config.emplace_back(period, limit, (unsigned)config_unit_array.get<jsonxx::Number>(2));
			else
				config.emplace_back(period, limit);
			++ load_count;
		}
		return load_count;
	}
	/**
	 * @brief 加载协议频率限制配置
	 * @note 分为 total: 所有协议汇总配置
	 * @note default: 单条协议默认配置
	 * @note codes: 单条协议按协议号配置
	 * 
	 * @return bool true: 加载成功
	*/
	bool HostProtocolLimiter::loadConfig(const jsonxx::Object& js)
	{  // total, default, codes, rate, aids
		m_Active = true;
		GetICloudProxyPtr()->SimpleErrLog(0, 0, "protocol_limit_start", "");

		// 加载总体的限制
		if (js.has<jsonxx::Array>("total"))
		{
			GetICloudProxyPtr()->SimpleSLOG("%s load total", __FUNCTION__);
			loadConfigFromJsonArray(js.get<jsonxx::Array>("total"), m_ProtocolLimitTotalConfig, "total");
		}

		// 加载默认协议的限制
		if (js.has<jsonxx::Array>("default"))
		{
			GetICloudProxyPtr()->SimpleSLOG("%s load default", __FUNCTION__);
			loadConfigFromJsonArray(js.get<jsonxx::Array>("default"), m_ProtocolLimitDefaultConfig, "default");
		}
		
		// 加载单个协议的限制
		if (js.has<jsonxx::Array>("codes"))
		{
			auto &codes_config_array = js.get<jsonxx::Array>("codes");
			for(int i=0; i < codes_config_array.size(); ++i)
			{
				if (!codes_config_array.has<jsonxx::Object>(i))
				{
					GetICloudProxyPtr()->SimpleSLOG("%s config error: not Object index=%d", __FUNCTION__, i);
					continue;
				}
				auto &config_js = codes_config_array.get<jsonxx::Object>(i);
				if (!config_js.has<jsonxx::Number>("code") || !config_js.has<jsonxx::Array>("limits"))
				{
					GetICloudProxyPtr()->SimpleSLOG("%s config error: no code number, or no limits array; index=%d", __FUNCTION__, i);
					continue;
				}
				int code = config_js.get<jsonxx::Number>("code");
				GetICloudProxyPtr()->SimpleSLOG("%s load code=%d index=%d", __FUNCTION__, code, i);
				std::vector<st_period_limit> * config = new std::vector<st_period_limit>;

				char tmp[32];
				snprintf(tmp, sizeof(tmp), "code_%d_%d", i, code);
				loadConfigFromJsonArray(config_js.get<jsonxx::Array>("limits"), *config, tmp);
				m_ProtocolLimitConfigMap[code] = config;
			}
		}
		return true;
	}

	/**
	 * @brief 云服统计协议频率
	*/
	void recordProtocolMax(int uin, int pb_code)
	{
	#ifdef IWORLD_SERVER_BUILD
		if (!gCheatConfig.SwitchCountProtocol)
		{
			return;
		}
		unsigned long long tick = GetIClientGameManagerInterface()->getICurGame()->getGameTick();
		static unsigned long long s_LastTick = 0/* 上一tick */, s_LastRecordTick = 0 /* 上次记录日志的tick */;
		static int s_PlayerTotalCountMaxInTick;  // 输出后清理, 保存周期内每tick收到每玩家所有协议最大数量
		static std::map<int, int> s_PBCodeMaxCountInPeriod;  // 输出后清理 {pb_code: max_count}, 保存周期内每tick收到每玩家单个协议最大数量 
		static std::map<int, std::map<int, int>> s_PlayerPBCodeMaxCountInTick;  // {uin: {pb_code: count}}, 每tick 清理, 每tick记录角色收到的协议数量
		if (tick != s_LastTick)
		{  // 每 tick 记录一次, 每 1200 tick 写一次日志
			s_LastTick = tick;
			int uin_total_max_count_in_tick = 0;  // uin_total 的最大值
			int uin_total = 0;  // 单个玩家的协议量汇总
			for (auto uin_iter = s_PlayerPBCodeMaxCountInTick.begin(); uin_iter != s_PlayerPBCodeMaxCountInTick.end(); ++uin_iter)
			{
				int pb_count_max_in_tick = 0;
				for (auto code_iter = uin_iter->second.begin(); code_iter != uin_iter->second.end(); ++ code_iter)
				{
					int code = code_iter->first;
					int count = code_iter->second;
					uin_total += count;
					if (count > s_PBCodeMaxCountInPeriod[code])
					{
						s_PBCodeMaxCountInPeriod[code] = count;
					}
				}
				if (uin_total > uin_total_max_count_in_tick)
					uin_total_max_count_in_tick = uin_total;
				uin_total = 0;
			}
			if (uin_total_max_count_in_tick > s_PlayerTotalCountMaxInTick)
				s_PlayerTotalCountMaxInTick = uin_total_max_count_in_tick;
			s_PlayerPBCodeMaxCountInTick.clear();
		}

		if (tick - s_LastRecordTick >= 1200)
		{  // 每 tick 记录一次, 每 1200 tick 写一次日志
			s_LastRecordTick = tick;
			if (s_PlayerTotalCountMaxInTick > 0)
			{
				jsonxx::Object json_ob;
				char key[16];
				for (auto it = s_PBCodeMaxCountInPeriod.begin(); it != s_PBCodeMaxCountInPeriod.end(); ++it)
				{
					snprintf(key, sizeof(key), "%d", it->first);
					json_ob.import(key, it->second);
				}
				json_ob.import("total", s_PlayerTotalCountMaxInTick);
				GetICloudProxyPtr()->InfoLog(0, 0, "protocol_count", json_ob);
				s_PBCodeMaxCountInPeriod.clear();
				s_PlayerTotalCountMaxInTick = 0;
			}
		}
		else if (tick < s_LastRecordTick)
			s_LastRecordTick = tick;  // 重开一局游戏, tick 会重置

		s_PlayerPBCodeMaxCountInTick[uin][pb_code] += 1;
	#endif // IWORLD_SERVER_BUILD
	}

	/**
	 * @brief  主机执行, 检测收到的协议频率是否超限, 分为所有协议总体和单个协议限制
	 * @note   
	 * @param  uin: 发送客机的角色id
	 * @param  pb_code: 协议ID
	 * @param  size: 协议体大小
	 * @retval true 未超限
	 */
	bool HostProtocolLimiter::checkLimit(int uin, int pb_code, int size)
	{
		if (!m_Active)  // 通过配置开关控制
			return true;
		if  (IsPlayerCheatKicked(uin))
			return false;
	#ifndef IWORLD_SERVER_BUILD
		if (!GetGameNetManagerPtr())
			return true;
		if (GetGameNetManagerPtr()->getMyUin() == uin)  // 客机自身及联机主机自身不受限制, 仅在主机限制客机
			return true;
	#endif
		RoleLimit * limiter = NULL;
		auto player_iter = m_RoleProtocolLimiter.find(uin);
		if (player_iter == m_RoleProtocolLimiter.end())
		{
			m_RoleProtocolLimiter[uin] = limiter = new RoleLimit(&m_ProtocolLimitTotalConfig);
			limiter->m_Logger.setUin(uin);
		}
		else
			limiter = player_iter->second;
		
		uint64_t now = GetIClientGameManagerInterface()->getICurGame()->getGameTick();
		if (now < limiter->m_LastTime)
		{
			limiter->reset();
		}
		limiter->m_LastTime = now;


		ExecAtQuit on_exit;
		unsigned times, interval, flags;

		on_exit.addExec([&](){
			limiter->m_TotalProtocolLimiter.recordTime(now);
		});
		
		auto it = limiter->m_PBCodeLimiter.find(pb_code);
		if (it == limiter->m_PBCodeLimiter.end())
		{
			auto config_it = m_ProtocolLimitConfigMap.find(pb_code);
			std::vector<st_period_limit>* config = (config_it != m_ProtocolLimitConfigMap.end()? config_it->second: &m_ProtocolLimitDefaultConfig);
			limiter->m_PBCodeLimiter.emplace(pb_code, config);
		}
		on_exit.addExec([&](){
			limiter->m_PBCodeLimiter[pb_code].recordTime(now);
		});

		if (limiter->m_TotalProtocolLimiter.checkLimit(now, &times, &interval, &flags))
		{  // 总量限制
			jsonxx::Object log;
			log.import("type", "total");
			log.import("tick", now);
			log.import("limit_times", times);
			log.import("limit_interval", interval);
			log.import("pb_code", pb_code);
			if (IsKickOn(flags))
			{
				log.import("kick", flags);
				CheatKickPlayer(uin, ERR_CODE_CHEAT_PROTO_LIMIT);
			}

			char tmp[32];
			snprintf(tmp, sizeof(tmp), "%d", pb_code);
			limiter->m_Logger.recordMessage(tmp, log);

			// GetICloudProxyPtr()->SimpleSLOG("%s uin=%d total limited pb_code=%d now=%d times=%d interval=%d", __FUNCTION__, uin, pb_code, now, times, interval);
			return false;
		}

		if (limiter->m_PBCodeLimiter[pb_code].checkLimit(now, &times, &interval, &flags))
		{  // 单个协议限制
			jsonxx::Object log;
			log.import("type", "code");
			log.import("tick", now);
			log.import("limit_times", times);
			log.import("limit_interval", interval);
			log.import("pb_code", pb_code);
			if (IsKickOn(flags))
			{
				log.import("kick", flags);
				CheatKickPlayer(uin, ERR_CODE_CHEAT_PROTO_LIMIT);
			}
			char tmp[32];
			snprintf(tmp, sizeof(tmp), "%d", pb_code);
			limiter->m_Logger.recordMessage(tmp, log);

			// GetICloudProxyPtr()->SimpleSLOG("%s uin=%d code limited pb_code=%d now=%d times=%d interval=%d", __FUNCTION__, uin, pb_code, now, times, interval);
			return false;
		}
		return true;
	}

	void HostProtocolLimiter::onPlayerLeave(int uin)
	{
		auto it = m_RoleProtocolLimiter.find(uin);
		if (it != m_RoleProtocolLimiter.end())
		{
			delete it->second;
			m_RoleProtocolLimiter.erase(it);
		}
	}
	std::map<int, int> g_ChangedFlag;
	void onPlayerLeave(int uin)
	{
		AntiSetting::HostProtocolLimiter::getInstance()->onPlayerLeave(uin);
	}
	void logFlagChange(int flagid, int value, const std::string& reason)
	{
		auto it = g_ChangedFlag.find(flagid);
		if (it == g_ChangedFlag.end() || value != it->second)
		{
			g_ChangedFlag[flagid] = value;
			if (GetDebugMgr().getLogLevel() >= 10)
				LOG_INFO("flag changed flag=%d value=%d reason=%s", flagid, value, reason.c_str());
		}
	}

	bool checkFlagMatch(int flagid, int value)
	{
		auto it = g_ChangedFlag.find(flagid);
		if (it == g_ChangedFlag.end())
		{
			// LOG_INFO("checkFlagMatch not found flagid=%d value=%d", flagid, value);
			return true;
		}
		if (it->second != value)
		{	
			if (GetDebugMgr().getLogLevel() >= 10)
				LOG_INFO("checkFlagMatch not match flagid=%d value=%d old=%d", flagid, value, it->second);
			return false;
		}
		return true;
	}

	/**
	 * @brief 主机上使用, 返回角色是否强制使用新移动, 注意需要与客机的 switch_new_move_sync 开关配合, move_sync_off_uins
	 * @param uin 角色ID
	 * @return true 强制使用
	*/
	bool forceUseNewSync(int uin)
	{
		if (gCheatConfig.SwitchForceUseNewSync)
		{
			if (gCheatConfig.MoveControlOffUins.count(uin) == 0)
				return true;
		}
		return false;
	}
	
	IMPLEMENT_LAZY_SINGLETON(HostProtocolLimiter)
}  // end namespace AntiSetting
using namespace AntiSetting;

void g_InUrlRequestCallback(const std::string &url)
{
	#ifdef DEDICATED_SERVER
	size_t idx = url.find('?');
	if (idx!= std::string::npos)
		AntiSetting::HttpRequestHostIn = url.substr(0, idx);
	else
		AntiSetting::HttpRequestHostIn = url;
	#endif
}
void g_OutUrlRequestCallback()
{
	#ifdef DEDICATED_SERVER
	AntiSetting::HttpRequestHostIn = "";
	#endif
}
void g_InLuaMessage(const std::string &name)
{
	#ifdef DEDICATED_SERVER
	AntiSetting::LuaMessageHostIn = name;
	#endif
}
void g_OutLuaMessage()
{
	#ifdef DEDICATED_SERVER
	AntiSetting::LuaMessageHostIn = "";
	#endif
}

// 返回unit的灰度配置是否生效
bool checkGreyUnit(const jsonxx::Object &unit, const std::string & key)
{
	// 限制版本, 版本不符合则不生效
	if (unit.has<jsonxx::Array>("versions"))
	{
		bool match = false; // 版本是否匹配
		const jsonxx::Array &versions = unit.get<jsonxx::Array>("versions");
		int room_version = GetClientInfoProxy()->GetClientVersion() >> 8;  // 配置只xx.xx, 需要将当前版本转换为这个格式的int
		for (int i=0; i < versions.size(); ++i)
		{
			if (!versions.has<jsonxx::String>(i))
			{
				GetICloudProxyPtr()->SimpleSLOG("checkGreyUnit config version not string; index=%d; key=%s", i, key.c_str());
				continue;
			}
			if (GetClientInfoProxy()->clientVersionFromStr(versions.get<jsonxx::String>(i).c_str()) == room_version)
			{
				match = true;
				GetICloudProxyPtr()->SimpleSLOG("checkGreyUnit version match; index=%d; match=%s; host=%s; key=%s",
					 i, versions.get<jsonxx::String>(i).c_str(), GetClientInfoProxy()->GetClientVersionStr().c_str(), key.c_str()
				);

				break;
			}
		}
		if (!match)
		{  // 版本不匹配
			GetICloudProxyPtr()->SimpleSLOG( "checkGreyUnit version unmatch off; room_version=%s %d key=%s", GetClientInfoProxy()->GetClientVersionStr().c_str(), room_version, key.c_str());
			return false;
		}
	}
	// 检测地图, 地图匹配则认为生效
	if (unit.has<jsonxx::Array>("aids"))
	{
		const jsonxx::Array &aids = unit.get<jsonxx::Array>("aids");
		char aid_str[64];
		snprintf(aid_str, sizeof(aid_str), "%lld", GetWorldManagerPtr()? GetWorldManagerPtr()->getFromWorldID(): 0);  // 配置成字符串, 防止数字太大失去精度

		for (int i=0; i < aids.size(); ++i)
		{
			if (!aids.has<jsonxx::String>(i))
			{
				GetICloudProxyPtr()->SimpleSLOG("checkGreyUnit config aid not string; index=%d; key=%s", i, key.c_str());
				continue;
			}
			if (strncmp(aid_str, aids.get<jsonxx::String>(i).c_str(), sizeof(aid_str)) == 0)
			{  // 地图匹配则直接匹配成功
				GetICloudProxyPtr()->SimpleSLOG("checkGreyUnit aid match; index=%d; match=%s; key=%s", i, aids.get<jsonxx::String>(i).c_str(), key.c_str());
				return true;
			}
		}
	}
	// 检测概率, 随机成功则生效
	if (unit.has<jsonxx::Number>("rate"))
	{
		int rate = unit.get<jsonxx::Number>("rate");
		if (rate >= 100)
		{
			GetICloudProxyPtr()->SimpleSLOG("checkGreyUnit rate match; rate=%d; key=%s", rate, key.c_str());
			return true;
		}
		else if (rate <= 0)
		{
			GetICloudProxyPtr()->SimpleSLOG("checkGreyUnit rate unmatch; rate=%d; key=%s", rate, key.c_str());
			return false;
		}
		else
		{
			int random = getRandomInt(1, 100);
			if (random <= rate)
			{
				GetICloudProxyPtr()->SimpleSLOG("checkGreyUnit rate match; rate=%d; random=%d; key=%s", rate, random, key.c_str());
				return true;
			}
		}
	}
	GetICloudProxyPtr()->SimpleSLOG("checkGreyUnit unmatch; key=%s", key.c_str());
	return false;
}

// --- CheatConfig start ---
void loadGreyArray(std::vector<const jsonxx::Object *>& configs_order, const jsonxx::Array& subs, const std::string& name)
{
	char tmp_key[64];
	for (int i=0; i<subs.size(); ++i)
	{
		if (!subs.has<jsonxx::Object>(i))
		{
			SLOG(INFO) << "CheatConfig::initConifg " << name << "grey not object; index" << i;
			continue;
		}
		auto &unit = subs.get<jsonxx::Object>(i);
		snprintf(tmp_key, sizeof(tmp_key), "%s_setting_%d", name.c_str(), i);
		if (checkGreyUnit(unit, tmp_key))
			configs_order.push_back(&unit);
	}
}

void CheatConfig::initConifg()
{
	if (InitConf)
		return;
	InitConf = true;
	
	char json_str_temp[8192] = { 0 };
	MINIW::ScriptVM::game()->callFunction("GetHostAntiSetting", ">s", json_str_temp);
	jsonxx::Object ob;
	ob.parse(json_str_temp);

	map<string, st_config_addr_default> load_keys = {
		{"not_check_auth",&NotCheckAuth},
		{"switch_multi_login",&SwitchMultiLogin},
		{"fly", &SwitchCheckFly},
		{"speed", &SwitchCheckSpeed},
		{"speed_ratio", &SpeedRatio},
		{"switch_use_item_distance", &SwitchUseItemDistance},
		{"use_item_dist_limit_base", &UseItemDistLimitBase},
		{"switch_dig_distance", &SwitchDigDistance},
		{"switch_place_block_distance",& SwitchPlaceBlockDistance},
		{"block_distance_limit",&BlockDistLimit },
		{"switch_use_map_edit", &SwitchUseMapEdit},
		{"switch_jump_height", &SwitchJumpHeight},
		{"switch_tackle", &SwitchTackle},
		{"switch_grab", &SwitchGrab},
		{"switch_dribble", &SwitchDribble},
		{"switch_move_protocal", &SwitchCheckRoleMove},
		{"check_move_timeout", {&CheckRoleMoveTimeMS, 10000}},
		{"switch_ace_taken", &SwitchAceTaken},
		{"switch_heartbeat_speed", &SwitchHeartBeatSpeed},
		{"switch_clip_state", &SwitchCheckClipState},
		{"switch_onground", &SwitchOnGound},
		{"switch_inwater", &SwitchInWater},
		{"switch_client_inwater", &SwitchClientInwater},
		{"switch_packgift", &SwitchPackGift},
		{"switch_host_clip", &SwitchHostCheckClip},
		{"switch_host_clip_radius", &SwitchHostCheckClipRadius},
		{"switch_no_coll", &SwitchNoCollision},
		{"switch_record_gain_item", &SwitchRecordGainItemType},
		{"interval_flag_sync", {&IntervalFlagSyncBase, 2000}},
		{"switch_ad_shop_intv", &SwitchADShopInterval},
		{"switch_music_paper_gain", &SwitchMusicPaperGain},
		{"switch_move_type", {&SwitchMoveType, 1}},
		{"switch_set_item_check_id", {&SwitchCheckSetItem, 1}},
		{"switch_flag_cheat", {&SwitchFlagCheat, 0}},
		{"switch_gun", {&SwitchGunCheck, 0}},
		{"switch_gun_shoot_ratio", {&SwitchGunShootRatio, 75}},
		{"switch_gun_reload_ratio", {&SwitchGunReloadRatio, 75}},
		{"switch_revive", {&SwitchReviveCheck, 0}},
		{"switch_revive_fix", {&ReviveFix, 2}},
		{"switch_horse_check", {&SwitchHorseCheck, 1}},
		{"switch_pet_check", {&SwitchPetCheck, 1}},
		{"switch_interact_check", &SwitchInteractCheck},
		{"interact_range_limit", {&InteractRangeLimit, 600}},
		{"switch_catch_ball_protocol", &SwitchCatchBallProtocol},
		{"switch_dig_cost", &SwitchDigCost},
		{"switch_rotation", &SwitchRotation},
		{"switch_new_move_sync", &SwitchNewMoveControlSyncRate},
		{"switch_move_sync_range", {&MoveControlSyncRangeLimit, 11}},
		{"switch_move_sync_accept_all", &SwitchNewMoveControlAcceptAll},
		{"switch_new_no_coll", &SwitchNewNoCollision},
		{"check_kick_forbid_tick", {&CheatKickForbidTick, 20 * 20}},
		{"switch_force_new_sync", &SwitchForceUseNewSync},
		{"switch_wrong_half_block", &SwitchCheckWrongHalfBlock},
		{"switch_move_check_value", {&SwitchMoveCheckValue, 20000}},
		{"switch_move_check_times", {&SwitchMoveCheckCurrentTimes, 30}},
		{"switch_move_check_extra", {&SwitchMoveCheckExtraTimes, 1}},
		{"switch_chat_content_length", {&SwitchChatContentLength, 512}},
		{"switch_shoot_minute", {&SwitchShootIntervalMinute, 30}},
	};
	
	/* 配置优先级
		云服: server grey > server > 总配置 grey > 总配置
		客户端: client grey > client > 总配置 grey > 总配置
	*/
#ifdef IWORLD_SERVER_BUILD
	#define _CHEAT_LOAD_TYPE "server"
#else
	#define _CHEAT_LOAD_TYPE "client"
#endif // IWORLD_SERVER_BUILD
	std::vector<const jsonxx::Object *> configs_order;
	if (ob.has<jsonxx::Object>(_CHEAT_LOAD_TYPE "_setting"))
	{
		jsonxx::Object &setting = ob.get<jsonxx::Object>(_CHEAT_LOAD_TYPE "_setting");
		if (setting.has<jsonxx::Array>("grey"))
			loadGreyArray(configs_order, setting.get<jsonxx::Array>("grey"), _CHEAT_LOAD_TYPE);
		configs_order.push_back(&setting);
	}
	if (ob.has<jsonxx::Array>("grey"))
		loadGreyArray(configs_order, ob.get<jsonxx::Array>("grey"), "main");
	configs_order.push_back(&ob);

	std::ostringstream os;
	for (auto iter = load_keys.begin(); iter != load_keys.end(); iter++) {
		bool found = false;
		// for (auto configs_iter = configs_order.begin(); configs_iter!= configs_order.end(); ++configs_iter)
		// {
		// 	if ((*configs_iter)->has<jsonxx::Number>(iter->first)) {
		// 		*(iter->second.addr) = (*configs_iter)->get<jsonxx::Number>(iter->first);
		// 		found = true;
		// 		break;
		// 	}
		// }
		if (!found)
			*(iter->second.addr) = iter->second.default_value;
		os << iter->first.c_str() << ":" << *(iter->second.addr) << ", ";
		
	}
	GetICloudProxyPtr()->SimpleSLOG("GetHostAntiSetting, %s", os.str().c_str());
	enum CheatLoadMod {
		CLM_ProtocolLimit,  // 加载协议频率限制
		CLM_ClientLogAction,  // 加载客机上报action log
		CLM_ChatLimit,  // 加载聊天限制
	};
	std::set<CheatLoadMod> loaded;
	for (auto configs_iter = configs_order.begin(); configs_iter!= configs_order.end(); ++configs_iter)
	{
		if (loaded.count(CLM_ProtocolLimit) == 0 && (*configs_iter)->has<jsonxx::Object>("protocol_limit"))
		{ // 加载协议频率限制配置
			if (HostProtocolLimiter::getInstance()->loadConfig((*configs_iter)->get<jsonxx::Object>("protocol_limit")))
			{
				GetICloudProxyPtr()->SimpleSLOG("protocol_limit load succeed");
				loaded.insert(CLM_ProtocolLimit);
			}
		}
		if (loaded.count(CLM_ChatLimit) == 0 && (*configs_iter)->has<jsonxx::Object>("chat_limit"))
		{ // 加载聊天限制配置
			if (ChatLimiter::getInstance()->loadConfig((*configs_iter)->get<jsonxx::Object>("chat_limit")))
			{
				GetICloudProxyPtr()->SimpleSLOG("chat_limit load succeed");
				loaded.insert(CLM_ChatLimit);
			}
		}
		if (loaded.count(CLM_ClientLogAction) == 0 && (*configs_iter)->has<jsonxx::Object>("client_log_action"))
		{ // 加载客机上报action log 配置
			loaded.insert(CLM_ClientLogAction);

			const jsonxx::Object& actions = (*configs_iter)->get<jsonxx::Object>("client_log_action");
			auto &kv_map = actions.kv_map();
			for (auto kv_it=kv_map.begin(); kv_it != kv_map.end(); ++kv_it)
			{
				if (!kv_it->second->is<jsonxx::Array>())
				{
					GetICloudProxyPtr()->SimpleSLOG("client_log_action load not Array, key=%s", kv_it->first.c_str());
					continue;
				}
				const jsonxx::Array &arr = kv_it->second->get<jsonxx::Array>();
				if (arr.size() < 2)
				{
					GetICloudProxyPtr()->SimpleSLOG("client_log_action load array size error, key=%s size=%d", kv_it->first.c_str(), arr.size());
					continue;
				}
				if (!arr.has<jsonxx::Number>(0) || !arr.has<jsonxx::Number>(1))
				{
					GetICloudProxyPtr()->SimpleSLOG("client_log_action load array not number, key=%s", kv_it->first.c_str());
					continue;
				}
				ClientLogAction.emplace(kv_it->first, st_config_action_error(arr.get<jsonxx::Number>(0), arr.get<jsonxx::Number>(1)));
				GetICloudProxyPtr()->SimpleSLOG("client_log_action load %s=%d %d", kv_it->first.c_str(), (int)arr.get<jsonxx::Number>(0), (int)arr.get<jsonxx::Number>(1));
			}
		}
	}

	if (ob.has<jsonxx::Object>("move_sync_config"))
	{
		loadMoveSyncConfig(ob.get<jsonxx::Object>("move_sync_config"));
	}
#ifdef IWORLD_SERVER_BUILD
	// 统计tick协议量
	const char *count_protocol = GetClientInfoProxy()->getEnterParam("count_protocol");
	if (count_protocol && count_protocol[0])
		SwitchCountProtocol = true;
	else
		SwitchCountProtocol = false;
#endif  // IWORLD_SERVER_BUILD
	if (gCheatConfig.SwitchRecordGainItemType == 1 || (gCheatConfig.SwitchRecordGainItemType == 0 && Rainbow::GetICloudProxyPtr()->IsRecordItemGain()))
		SwitchRecordGainItem = true;
	
	if (gCheatConfig.SwitchNewMoveControlAcceptAll == 0)
		GetICloudProxyPtr()->SimpleErrLog(0, 0, "move_sync_check_on", "");
}

void CheatConfig::updateCheatConfig(unsigned long long tick)
{
	
#ifdef IWORLD_SERVER_BUILD
	constexpr int interval = 5 * 60;
	static unsigned long long last_update_time = 0;
	if (!g_zmqMgr)
		return;
	if (!InitConf)
		return;
	unsigned long long now = MINIW::GetTimeStamp();
	if (last_update_time + interval > now)
		return;
	last_update_time = now;
	char url[1024] = { 0 };
	snprintf(url, sizeof(url), "%s/nacos/v1/cs/configs?group=DEFAULT_GROUP&dataId=gs_cheat_config", g_zmqMgr->GetNacosAddr().c_str());
	GetICloudProxyPtr()->SimpleSLOG("%s url:%s",__FUNCTION__ , url);
	const static map<string, st_config_addr_default> load_keys = {
		{"switch_move_check_value", &SwitchMoveCheckValue},
		{"switch_move_check_times", &SwitchMoveCheckCurrentTimes},
		{"switch_move_check_extra", &SwitchMoveCheckExtraTimes},
		{"switch_move_sync_accept_all", &SwitchNewMoveControlAcceptAll},
		{"switch_shoot_minute", &SwitchShootIntervalMinute},
	};
	Rainbow::Http::WebRequest::Create(url)->Request(nullptr, nullptr, 
		[this](bool success, Rainbow::Http::WebRequest* request) {
			if (success) {
				auto respon = request->GetResponse();

				std::string content(respon.data(), respon.size());
				if (NacosConfig == content)
					return;
				NacosConfig = content;
				jsonxx::Object obj;
				if (obj.parse(content))
				{
					for (auto iter = load_keys.begin(); iter != load_keys.end(); iter++) {
					{
						if (obj.has<jsonxx::Number>(iter->first))
						{
							*(iter->second.addr) = obj.get<jsonxx::Number>(iter->first);
							SLOG(INFO) << "nacos cheat config " << iter->first << " = " << *(iter->second.addr);
						}
					}
				}
			}
			else {
				SLOG(INFO)<< "updateCheatConfig fail";
			}
		}
	});
#endif
}

void CheatConfig::loadMoveSyncConfig(const jsonxx::Object & ms_conf)
{
	// 限制版本, 版本不符合则不生效
	if (ms_conf.has<jsonxx::Array>("versions"))
	{
		bool match = false; // 版本是否匹配
		const jsonxx::Array &versions = ms_conf.get<jsonxx::Array>("versions");
		int room_version = GetClientInfoProxy()->GetClientVersion() >> 8;  // 配置只xx.xx, 需要将当前版本转换为这个格式的int
		for (int i=0; i < versions.size(); ++i)
		{
			if (!versions.has<jsonxx::String>(i))
			{
				LOG_INFO("move_sync_config config version not string; index=%d", i);
				continue;
			}
			if (GetClientInfoProxy()->clientVersionFromStr(versions.get<jsonxx::String>(i).c_str()) == room_version)
			{
				match = true;
				LOG_INFO("move_sync_config version match; index=%d; match=%s; host=%s",
					i, versions.get<jsonxx::String>(i).c_str(), GetClientInfoProxy()->GetClientVersionStr().c_str()
				);

				break;
			}
		}
		if (!match)
		{  // 版本不匹配
			LOG_INFO( "move_sync_config version unmatch off; room_version=%s %d ", GetClientInfoProxy()->GetClientVersionStr().c_str(), room_version);
			SwitchNewMoveControlSyncRate = 0;
		}
	}
	if (ms_conf.has<jsonxx::Array>("move_sync_on_uins")) {
		auto& on = ms_conf.get<jsonxx::Array>("move_sync_on_uins");
		for (int i = 0; i < on.size(); ++i)
		{
			if (!on.has<jsonxx::Number>(i))
			{
				LOG_INFO("move_sync_on_uins config error, %d not number", i);
				continue;
			}
			MoveControlOnUins.insert(on.get<jsonxx::Number>(i));
		}
	}
	if (ms_conf.has<jsonxx::Array>("move_sync_off_uins")) {
		auto& on = ms_conf.get<jsonxx::Array>("move_sync_off_uins");
		for (int i = 0; i < on.size(); ++i)
		{
			if (!on.has<jsonxx::Number>(i))
			{
				LOG_INFO("move_sync_off_uins config error, %d not number", i);
				continue;
			}
			MoveControlOffUins.insert(on.get<jsonxx::Number>(i));
		}
	}
}

// --- CheatConfig end ---

/*********** PeriodLimitRecorder start **************/

/**
 * @brief 设置检查间隔的配置
 * 
 * @param config 多个上限的列表 [{period: 时间段, limit: 时间段内允许达到的次数上限}]
 * @return void 
 */
void PeriodLimitRecorder::setConfig(const std::vector<st_period_limit> *config)
{
	m_Config = config;
	if (config == NULL)
	{
		m_TimeRecords.clear();
        return;
	}

	unsigned max_size = 0;
    for (auto iter = config->begin(); iter!= config->end(); iter++)
	{
		if (iter->limit == 0 && iter->period)
		{ // 禁止
			m_Forbidden = true;
			m_TimeRecords.clear();
			return;
		}
		if (iter->limit > max_size)
			max_size = iter->limit;
	}
	m_TimeRecords = std::vector<uint64_t>(max_size, 0);
}

void PeriodLimitRecorder::reset()
{
	for(size_t i = 0; i< m_TimeRecords.size(); ++i)
	{
		m_TimeRecords[i] = 0;
	}
}

/**
 * @brief 检查time是否触发超过时间段内的次数上限
 * 
 * @param time 时间
 * @param times 返回字段, 超限时返回超限触发的limit
 * @param interval 返回字段, 超限时返回超限触发的时间间隔
 * @return true 超限
 * @return false 未超限
 */
bool PeriodLimitRecorder::checkLimit(uint64_t time, unsigned *times, unsigned *interval, unsigned *flags)
{
	if (m_Forbidden)
		return true;
	if (m_TimeRecords.empty())
	    return false;
    for (auto iter = m_Config->begin(); iter!= m_Config->end(); iter++)
	{
		/** 
		 * 检查距离当前limit次的时间, 判断当前时间与此时间间隔是否超过period
		 * 如 配置{period: 3, limit: 5}表示5ms内不超过3次 records [11, 11, 12, 12, 13]
		 * 第13ms时检测, 取records中最后一次前第5次的下标为0, 值为11, 判断当前时间13 与records[0] 的间隔是否 < period
		 */
		size_t comp_index = (m_Index + m_TimeRecords.size() + 1 - iter->limit) % m_TimeRecords.size();
		if (m_TimeRecords[comp_index] && (time - m_TimeRecords[comp_index] < iter->period))
		{
			if (times)
				*times = iter->limit;
			if (interval)
				*interval = time - m_TimeRecords[comp_index];
			if (flags)
				*flags = iter->flags;
			return true;
		}
	}
	return false;
}

// 记录一次时间
void PeriodLimitRecorder::recordTime(uint64_t time)
{
	if (m_TimeRecords.empty())
		return;
	m_TimeRecords[(++m_Index) % m_TimeRecords.size()] = time;
}

/*********** PeriodLimitRecorder end **************/


PlayerCheatData::PlayerCheatData(ClientPlayer* owner):
	m_Owner(owner), m_IsVip(false), m_StaySky(0), m_FlyUp(0), m_FlyDirectionUp(false), m_SwingCount(0), m_UpCount(0), m_FlyCheatCount(0), m_FlyKickoff(false)
	, m_MusicPaperTimesRecorder(owner, "cheat_music_paper_times")
	, m_MapItemTimesRecorder(owner, "map_item_times")
	, m_MoveTypeTimesRecorder(owner, "cheat_move_type")
	, m_GunTimesRecorder(owner, "cheat_gun_times")
	, m_ReviveTimesRecorder(owner, "cheat_revive_times")
	, m_LastAirJump(false)
	, m_FlownInThisGame(false)
	, m_LastClientTick(0)
{
	m_FlagCheating = false;
	m_ClickCount = 0;
	ResetAll();
	gCheatConfig.initConifg();
	m_Env = MINIW::GetClientEnv();
	m_ADShopTraderID = 0;
	m_NoMoveTypeStartTime = 0;
	m_LegalFlying = false;
	m_LastDeadTime = 0;
	m_LastGunShootTime = 0;
}

PlayerCheatData::~PlayerCheatData()
{
	AntiSetting::g_ChangedFlag.clear();
}

void PlayerCheatData::ResetCheatData()
{
	memset(m_CheckingData, 0, CHEAT_TAG_MAX * sizeof(int));
	memset(m_LogTime, 0, CHEAT_TAG_MAX * sizeof(unsigned int));
}

void PlayerCheatData::ResetCheckInfo()
{
	memset(m_CheckInfoSet, 0, ECI_TYPE_MAX * sizeof(bool));
}

void PlayerCheatData::ResetAll()
{
	ResetCheatData();
	ResetCheckInfo();
}

void PlayerCheatData::ResetTag(CHEAT_CHECK_TAG tag)
{
	m_CheckingData[tag] = 0;
}

int PlayerCheatData::GetCount(CHEAT_CHECK_TAG tag)
{
	return m_CheckingData[tag];
}

bool PlayerCheatData::ProcessCheat(CHEAT_CHECK_TAG tag, bool cheat)
{
	changeCheatCount(tag, cheat ? 1 : -1);

	return judgeCheat(tag, GetCount(tag));
}


void PlayerCheatData::SetCheckData(int infoType, const std::string& detail)
{
	m_CheckInfoSet[infoType] = true;
	switch (infoType)
	{
	case ECI_TYPE_SHOP_EXTRA_AWARD:
	{
		m_AdShopExtraAward.parse(detail);
		break;
	}
	case ECI_TYPE_DEV_STORE_INFO:
	{
		m_DevStore.parse(detail);
		break;
	}
	case ECI_TYPE_SHOP_STORE_INFO:
	{
		m_ShopStore.parse(detail);
		break;
	}
	case ECI_TYPE_SKIN_INFO:
	{
		jsonxx::Object skinInfo;
		skinInfo.parse(detail);
		m_IsVip = false;
		m_SkinData.reset();
		if (skinInfo.has<jsonxx::Number>("vip"))
		{
			m_IsVip = skinInfo.get<jsonxx::Number>("vip");
		}
		if (skinInfo.has<jsonxx::Array>("skins"))
		{
			m_SkinData = skinInfo.get<jsonxx::Array>("skins");
		}
		break;
	}
	case ECI_TYPE_HORSE_INFO:
	{
		jsonxx::Array horse_ids;
		horse_ids.parse(detail);
		for (size_t i = 0; i < horse_ids.size(); ++i)
		{
			if (horse_ids.has<jsonxx::Number>(i))
				m_AccountHorseList.insert(horse_ids.get<jsonxx::Number>(i));
			else
				SLOG(ERROR) << "SetCheckData ECI_TYPE_HORSE_INFO index=" << i << " not number " << detail;
		}
	}
	default:
		break;
	}
}

bool PlayerCheatData::CheckAdShopExtraAward(int awardId, int itemId, int count)
{
	if (!m_Owner)
		return true;

	// 未设置就暂不检测了
	//if (!m_CheckInfoSet[ECI_TYPE_SHOP_EXTRA_AWARD])
	//	return true;
	for (size_t i = 0; i < m_AdShopExtraAward.size(); ++i)
	{
		jsonxx::Object& award = m_AdShopExtraAward.get<jsonxx::Object>(i);
		if (!award.has<jsonxx::Number>("id") || !award.has<jsonxx::Number>("itemid") || !award.has<jsonxx::Number>("itemnum"))
		{
			jsonxx::Object cheat;
			cheat << "client_item_id" << itemId;
			cheat << "client_item_count" << count;
			cheat << "server_object" << award;
			Rainbow::GetICloudProxyPtr()->InfoLog(m_Owner->getUin(), m_Owner->getOWID(), "cheat_adshop_extra_award_format", cheat);
			return false;
		}

		jsonxx::Number aid = award.get<jsonxx::Number>("id");
		if (aid == awardId)
		{
			jsonxx::Number serverItemId = award.get<jsonxx::Number>("itemid");
			jsonxx::Number serverCount = award.get<jsonxx::Number>("itemnum");
			if (serverItemId != itemId || serverCount != count)
			{
				jsonxx::Object cheat;
				cheat << "client_item_id" << itemId;
				cheat << "client_item_count" << count;
				cheat << "server_item_id" << serverItemId;
				cheat << "server_item_count" << serverCount;
				Rainbow::GetICloudProxyPtr()->InfoLog(m_Owner->getUin(), m_Owner->getOWID(), "cheat_adshop_extra_award", cheat);
				return false;
			}
			return true;
		}
	}
	jsonxx::Object cheat;
	cheat << "client_item_id" << itemId;
	cheat << "client_item_count" << count;
	Rainbow::GetICloudProxyPtr()->InfoLog(m_Owner->getUin(), m_Owner->getOWID(), "cheat_adshop_extra_award_have", cheat);
	return false;
}

bool PlayerCheatData::CheckExtractStoreItem(int storeIndex, int itemId, int count)
{
	if (!m_Owner)
		return true;
	if (storeIndex == 1)
		return checkDevStoreItem(itemId, count);
	return checkShopStoreItem(itemId, count);
}

bool PlayerCheatData::checkDevStoreItem(int itemId, int count)
{
	// 未设置就暂不检测了
	//if (!m_CheckInfoSet[ECI_TYPE_DEV_STORE_INFO])
	//	return true;
	for (size_t i = 0; i < m_DevStore.size(); ++i)
	{
		jsonxx::Object& itemInfo = m_DevStore.get<jsonxx::Object>(i);
		if (!itemInfo.has<jsonxx::Number>("ItemID") || !itemInfo.has<jsonxx::Number>("ItemNum"))
		{
			jsonxx::Object cheat;
			cheat << "client_item_id" << itemId;
			cheat << "client_item_count" << count;
			cheat << "server_object" << itemInfo;
			Rainbow::GetICloudProxyPtr()->InfoLog(m_Owner->getUin(), m_Owner->getOWID(), "cheat_dev_store_format", cheat);
			return false;
		}
		jsonxx::Number serverItemId = itemInfo.get<jsonxx::Number>("ItemID");
		jsonxx::Number serverCount = itemInfo.get<jsonxx::Number>("ItemNum");
		if (serverItemId == itemId)
		{
			if (serverCount < count)
			{
				jsonxx::Object cheat;
				cheat << "client_item_id" << itemId;
				cheat << "client_item_count" << count;
				cheat << "server_item_id" << serverItemId;
				cheat << "server_item_count" << serverCount;
				Rainbow::GetICloudProxyPtr()->InfoLog(m_Owner->getUin(), m_Owner->getOWID(), "cheat_dev_store_extract", cheat);
				return false;
			}
			return true;
		}
	}
	jsonxx::Object cheat;
	cheat << "client_item_id" << itemId;
	cheat << "client_item_count" << count;
	Rainbow::GetICloudProxyPtr()->InfoLog(m_Owner->getUin(), m_Owner->getOWID(), "cheat_dev_store_have", cheat);
	return false;
}

bool PlayerCheatData::checkShopStoreItem(int itemId, int count)
{
	// 未设置就暂不检测了
	//if (!m_CheckInfoSet[ECI_TYPE_SHOP_STORE_INFO])
	//	return true;
	// 商店仓库一次只能提取一个
	if (count != 1)
	{
		jsonxx::Object cheat;
		cheat << "client_item_id" << itemId;
		cheat << "client_item_count" << count;
		Rainbow::GetICloudProxyPtr()->InfoLog(m_Owner->getUin(), m_Owner->getOWID(), "cheat_shop_store_count", cheat);
		return false;
	}
	for (size_t i = 0; i < m_ShopStore.size(); ++i)
	{
		jsonxx::Object& itemInfo = m_ShopStore.get<jsonxx::Object>(i);
		if (!itemInfo.has<jsonxx::Number>("itemId") || !itemInfo.has<jsonxx::Number>("num"))
		{
			jsonxx::Object cheat;
			cheat << "client_item_id" << itemId;
			cheat << "client_item_count" << count;
			cheat << "server_object" << itemInfo;
			Rainbow::GetICloudProxyPtr()->InfoLog(m_Owner->getUin(), m_Owner->getOWID(), "cheat_shop_store_format", cheat);
			return false;
		}
		jsonxx::Number serverItemId = itemInfo.get<jsonxx::Number>("itemId");
		jsonxx::Number serverCount = itemInfo.get<jsonxx::Number>("num");
		if (serverItemId == itemId)
		{
			if (serverCount < count)
			{
				jsonxx::Object cheat;
				cheat << "client_item_id" << itemId;
				cheat << "client_item_count" << count;
				cheat << "server_item_id" << serverItemId;
				cheat << "server_item_count" << serverCount;
				Rainbow::GetICloudProxyPtr()->InfoLog(m_Owner->getUin(), m_Owner->getOWID(), "cheat_shop_store_extract", cheat);
				return false;
			}
			return true;
		}
	}

	jsonxx::Object cheat;
	cheat << "client_item_id" << itemId;
	cheat << "client_item_count" << count;
	Rainbow::GetICloudProxyPtr()->InfoLog(m_Owner->getUin(), m_Owner->getOWID(), "cheat_shop_store_have", cheat);
	return false;
}

bool PlayerCheatData::CheckSkinUse(int skinId)
{
	if (!m_Owner || skinId <= 0)
		return true;
	// 未设置就暂不检测了
	//if (!m_CheckInfoSet[ECI_TYPE_SKIN_INFO])
	//	return true;

	for (size_t i = 0; i < m_SkinData.size(); ++i)
	{
		jsonxx::Object& itemInfo = m_SkinData.get<jsonxx::Object>(i);
		if (!itemInfo.has<jsonxx::Number>("id") || !itemInfo.has<jsonxx::Number>("vip"))
		{
			jsonxx::Object cheat;
			cheat << "client_skin_id" << skinId;
			cheat << "server_object" << itemInfo;
			Rainbow::GetICloudProxyPtr()->InfoLog(m_Owner->getUin(), m_Owner->getOWID(), "cheat_skin_info_format", cheat);
			return false;
		}
		jsonxx::Number serverSkinId = itemInfo.get<jsonxx::Number>("id");
		bool needVip = itemInfo.get<jsonxx::Number>("vip") > 0;
		if (serverSkinId == skinId)
		{
			if (needVip && !m_IsVip)
			{
				jsonxx::Object cheat;
				cheat << "client_skin_id" << skinId;
				cheat << "server_skin_id" << serverSkinId;
				cheat << "server_need_vip" << needVip;
				cheat << "server_is_vip" << m_IsVip;
				Rainbow::GetICloudProxyPtr()->InfoLog(m_Owner->getUin(), m_Owner->getOWID(), "cheat_use_skin_vip", cheat);
				return false;
			}
			return true;
		}
	}

	jsonxx::Object cheat;
	cheat << "client_skin_id" << skinId;
	Rainbow::GetICloudProxyPtr()->InfoLog(m_Owner->getUin(), m_Owner->getOWID(), "cheat_use_skin", cheat);
	return false;
}

void PlayerCheatData::changeCheatCount(CHEAT_CHECK_TAG tag, int count)
{
	m_CheckingData[tag] += count;
	if (m_CheckingData[tag] < 0)
		m_CheckingData[tag] = 0;

	if (m_CheckingData[tag] > s_setting[tag])
		m_CheckingData[tag] = s_setting[tag] + 1;
}

// 2021-12-02 codeby:liusijia 检测移动协议中数据  返回非0表示作弊
int PlayerCheatData::CheckMoveCheat(int changeFlag, const WCoord& targetPos, float clientSpeed)
{
	if (!m_Owner || m_Owner->isInSpectatorMode())
		return 0;
	
	if (_isSingleOrGodMode())
		return 0;

	// 审核人员不用判断
	if (GetClientInfoProxy()->IsUserOuterChecker(m_Owner->getUin()))
		return 0;

	auto pWorld = m_Owner->getWorld();
	if (!pWorld)
		return 0;

	{
		const char *log_flag = GetClientInfoProxy()->getEnterParam("debug_move_flag");
		if ((log_flag && log_flag[0]))
			SLOG(INFO) << "flag uin=" << m_Owner->getUin() << " flag=" << changeFlag;
	}
	unsigned now = Rainbow::Timer::getSystemTick();
	
	PlayerLocoMotion *loc = static_cast<PlayerLocoMotion *>(m_Owner->getLocoMotion());
	if (loc){
		m_JumpHeightChange.checkFlag(loc->getJumpHeight() , now);
	}
	m_LegalFlying = false;

	if (isTestEnv() && m_Owner->getApiid() == 999)
	{
		const char *force_check = GetClientInfoProxy()->getEnterParam("force_check_fly");
		if (!(force_check && force_check[0]))
			m_LegalFlying = true;
	}
	if (!m_LegalFlying && validClientFly())
	{
		m_LegalFlying = true;
	}
	//检测移动模式
	int moveRet = checkMoveMode(changeFlag, targetPos);
	if (moveRet != 0)
	{
		return moveRet;
	}
	

	// changeflag检测
	std::string msg;
	m_FlagCheating = checkMoveFlagCheat(changeFlag, msg);
	if (!m_LegalFlying && !IsSwitchOff(gCheatConfig.SwitchFlagCheat) && m_FlagCheating)
	{
		if (now > m_LogTime[CHEAT_TAG_FLAG])
		{
			m_LogTime[CHEAT_TAG_FLAG] = now + 2000;
			jsonxx::Object cheat;
			cheat << "client_changeflag" << changeFlag;
			cheat << "server_msg" << msg;
			Rainbow::GetICloudProxyPtr()->InfoLog(m_Owner->getUin(), m_Owner->getOWID(), "cheat_modify_changeflag", cheat);

			if (ProcessCheat(CHEAT_TAG_FLAG, true))
				processKickOrNotify(gCheatConfig.SwitchFlagCheat, ERR_CODE_CHEAT_FLY);
		}
		return ERR_CODE_CHEAT_FLY;
	}

	bool clientOnground = test_flag_bit(changeFlag, 4);
	//服务端自行判断ongroud
	bool serverOnground = isOnGround(targetPos);
	checkOnGroundFlag(serverOnground);
	checkOnGroundCheat(changeFlag, serverOnground);
	checkInWaterCheat(changeFlag, serverOnground);
	checkMoveType(changeFlag);

	//服务器和客户端onground不一致，以服务器为准
	if (serverOnground != clientOnground)
	{
		set_change_flag_bit2(changeFlag, 4, serverOnground);
		//LOG_INFO("fix onground");
	}

	//2022/4/19 使用载具不检测飞天作弊 codeby:liushuxin
	auto rideComponent = m_Owner->getRiddenComponent();
	if (rideComponent && rideComponent->isRiding())
	{
		return 0;
	}

	//2022/4/18 使用道具时不检测飞天作弊，避免使用冲天炮相关飞行道具误判 codeby:liushuxin
	/* 去除这个异常判断 20220913 by huanglin 
	if (m_Owner->getCurDorsumID() != 0)
	{
		return 0;
	}
	*/

	if (!m_gotoPos.isZero())
	{
		if (m_gotoPos == m_Owner->getLocoMotion()->getPosition()) {
			//2022/6/2 传送后未移动过 codeby:liushuxin
			return 0;
		}
		else
		{
			m_gotoPos.setElement(0, 0, 0);
		}
	}

	//检测移动速度
	if ((!IsSwitchOff(gCheatConfig.SwitchCheckSpeed)) && checkMoveSpeed(changeFlag, clientSpeed))
	{
		return ERR_CODE_CHEAT_MOVE_SPEED;
	}

	if (!m_LegalFlying && !IsSwitchOff(gCheatConfig.SwitchCheckFly))
	{
		//滞留空中处理
		saveStaySky(changeFlag, targetPos);

		//检测持续上升
		if (checkContinueUp(changeFlag, targetPos))
		{
			m_FlyCheatCount++;
			return ERR_CODE_CHEAT_FLY_KEEPUP;
		}

		//检测上下移动
		if (checkSwing(changeFlag, targetPos))
		{
			m_FlyCheatCount++;
			return ERR_CODE_CHEAT_FLY_SWING;
		}
	}

	return 0;
}


/**
 * @brief 检查并记录主机上此玩家的onground标记变化
 * 
 * @return true 标记发生变化
 */
bool PlayerCheatData::checkOnGroundFlag(bool serverOnground)
{
	/*
 	ActorLocoMotion* locmove = m_Owner->getLocoMotion();
	
	if (!locmove)
		return false;
	bool serverOnground = locmove->m_OnGround; // 此处不准确
	*/
	if ((m_OnGroundChange.m_LastFlagChange == 0) || (serverOnground != (bool)m_OnGroundChange.m_LastFlag)){  // 主机本身的标记发生变化, 此时客机发送任何标记都认为正常
		m_OnGroundChange.m_LastFlagChange = MINIW::GetTimeStampMS();
		m_OnGroundChange.m_LastFlag = serverOnground;
		return true;
	}
	return false;
}

/**
 * @brief 检查onground标记
 * 
 * @param changeFlag 客户端上报的flag集合
 * @param serverOnGround 服务器自身的onground标记
 * @return true 有外挂嫌疑
 */
bool PlayerCheatData::checkOnGroundCheat(int changeFlag, bool serverOnGround)
{
	if (!m_Owner || IsSwitchOff(gCheatConfig.SwitchOnGound))
		return false;

	bool onground = test_flag_bit(changeFlag, 4);
	if (onground && ! serverOnGround)
	{
		UDPConnection * connect = GameNetManager::getInstance()->getConnection();
		int last_ping = connect ? connect->getClientLastPing(m_Owner->getUin()) : 0;
		if (last_ping < 0)
			last_ping = 0;
		
		uint64_t now = MINIW::GetTimeStampMS();
		// 能接受的状态同步期, 在主机状态变化后的一段时间内客机任何标记都认为正常
		int valid_time_interval = last_ping * 2 + gCheatConfig.IntervalFlagSyncBase;
		if (m_OnGroundChange.m_LastFlagChange && (now >= m_OnGroundChange.m_LastFlagChange + valid_time_interval))
		{
			if (now > m_LogTime[CHEAT_TAG_ONGROUND])
			{
				m_LogTime[CHEAT_TAG_ONGROUND] = now + 500;
				jsonxx::Object cheat;
				cheat << "last_changeflag" << m_OnGroundChange.m_LastFlagChange;
				cheat << "now" << now;
				Rainbow::GetICloudProxyPtr()->InfoLog(m_Owner->getUin(), m_Owner->getOWID(), "cheat_modify_ground_use", cheat);
				if (ProcessCheat(CHEAT_TAG_ONGROUND, true))
					processKickOrNotify(gCheatConfig.SwitchOnGound, ERR_CODE_CHEAT_ONGROUD);
			}
			return true;
		}
	}
	return false;
}
/**
 * @brief 游泳检查
 * 
 * @param changeFlag 标记位
 * @param serverOnGround 不再使用
 * @return true 违规
 * @return false 未发现违规
 */
bool PlayerCheatData::checkInWaterCheat(int changeFlag, bool serverOnGround)
{
	if (IsSwitchOff(gCheatConfig.SwitchInWater))
		return false;
	
	if (m_Owner->getRiddenComponent() && m_Owner->getRiddenComponent()->isRiding())
	{
		return false;
	}

	bool inLiquid = test_flag_bit(changeFlag, 10);  // 是否在液体中
	bool swimming = test_flag_bit(changeFlag, 13); // 是否在水中行走状态
	// 水中行走状态但不在水中, 则认为
	if (swimming && !inLiquid)
	{
		{
			jsonxx::Object cheat;
			cheat << "client_inwater" << inLiquid;
			cheat << "client_water_use" << swimming;
			cheat << "server_ongroud" << serverOnGround;
			Rainbow::GetICloudProxyPtr()->InfoLog(m_Owner->getUin(), m_Owner->getOWID(), "cheat_modify_water_use", cheat);
		}
		if (ProcessCheat(CHEAT_TAG_WATER, true))
			processKickOrNotify(gCheatConfig.SwitchInWater, ERR_CODE_CHEAT_WATER);

		return true;
	}
	else
		ProcessCheat(CHEAT_TAG_WATER, false);

	return false;
}

bool PlayerCheatData::checkMoveFlagCheat(int changeFlag, std::string& msg)
{
	if (!m_Owner || changeFlag == 0)
		return false;
	int uin = m_Owner->getUin();
	// 检测高位
	bool testHigh = (test_flag_bit(changeFlag, 29) == (bool)(uin & 1) &&
					 test_flag_bit(changeFlag, 30) == (bool)(uin & 2) &&
					 test_flag_bit(changeFlag, 31) == (bool)(uin & 4));
	if (!testHigh)
	{
		msg = "test high failed";
		return true;
	}
	// 检测保护标记
	bool testModify = (test_flag_bit(changeFlag, 12) && 
					   test_flag_bit(changeFlag, 16) );
	if (!testModify)
	{
		msg = "test modify failed";
		return true;
	}

	if (test_flag_bit(changeFlag, 14) == 1)
	{
		if (test_flag_bit(changeFlag, 17) == 0 || !validClientFly())
		{
			msg = "cheat using fly";
			return true;
		}
	}
	
	bool testFly = (test_flag_bit(changeFlag, 15) == 1 &&
					test_flag_bit(changeFlag, 17) == 0);
	if (testFly)
	{
		msg = "cheat gravity";
		return true;
	}

	bool testFlown = (test_flag_bit(changeFlag, 20) == 1 &&
					!m_FlownInThisGame);
	if (testFlown)
	{
		msg = "cheat flown";
		return true;
	}
	// 游泳标记
	/*bool testWater = (test_flag_bit(changeFlag, 13) && !test_flag_bit(changeFlag, 10));
	if (!testWater)
	{
		msg = "test water failed";
		return true;
	}*/

	return false;
}

/**
 * @brief 检查move type标记异常
 * 
 * @param changeFlag 客户端上报的flag集合
 * @return true 使用外挂嫌疑
 */
bool PlayerCheatData::checkMoveType(int changeFlag)
{
	if (MNSandbox::Config::GetSingleton().IsSandboxMode())
		return false;
	if (!m_Owner || IsSwitchOff(gCheatConfig.SwitchMoveType))
		return false;
	
	auto rideCom = m_Owner->getRiddenComponent();
	auto functionWrapper = m_Owner->getFuncWrapper();
	if ((rideCom && rideCom->isRiding()) || 
		(functionWrapper && !functionWrapper->getCanMove()) || 
		!m_Owner->checkActionAttrState(PLAYER_PERMITS_TYPE::ENABLE_MOVE))
		return false;
	
	int testMoveType = (test_flag_bit(changeFlag, 13) +
		test_flag_bit(changeFlag, 14) + test_flag_bit(changeFlag, 15));
	uint64_t now = MINIW::GetTimeStamp();
	if (testMoveType == 0)
	{
		if (!m_NoMoveTypeStartTime)
			m_NoMoveTypeStartTime = now;
		else
		{
			if (now - m_NoMoveTypeStartTime > 3)
			{
				m_MoveTypeTimesRecorder.recordMessage("low", "0");
				processKickOrNotify(gCheatConfig.SwitchMoveType, ERR_CODE_CHEAT_MOVE_TYPE);
				return false;
			}
		}
	}
	else if (testMoveType > 1)
	{
		char buf[4];
		snprintf(buf, sizeof(buf) - 1, "%d", testMoveType);
		m_MoveTypeTimesRecorder.recordMessage("high", buf);
		processKickOrNotify(gCheatConfig.SwitchMoveType, ERR_CODE_CHEAT_MOVE_TYPE);
		return false;
	}
	else
	{
		m_NoMoveTypeStartTime = 0;
	}
	return true;
}

/**
 * @brief 判断主机当前是否允许此玩家飞行
 * 
 * @return true 允许
 */
bool PlayerCheatData::validClientFly(){
	if (m_Owner->getFlying())
		return true;
	// 获取客机ping值
	UDPConnection * connect = GameNetManager::getInstance()->getConnection();
	int last_ping = connect ? connect->getClientLastPing(m_Owner->getUin()) : 0;
	if (last_ping < 0)
		last_ping = 0;
	
	uint64_t now = MINIW::GetTimeStampMS();
	// 能接受的状态同步期, 在主机状态变化后的一段时间内客机任何标记都认为正常
	int valid_time_interval = last_ping * 2 + gCheatConfig.IntervalFlagSyncBase;
	if (m_FlyModeChange.inChangePeriod(now, valid_time_interval))
		return true;
	return false;
}

int PlayerCheatData::checkMoveMode(int changeFlag, const WCoord & targetPos)
{
	unsigned now = Rainbow::Timer::getSystemTick();

	bool onground = test_flag_bit(changeFlag, 4);
	bool flymode = test_flag_bit(changeFlag, 5);
	// bool onVehicle = test_flag_bit(changeFlag, 6);
	// bool onLadder = test_flag_bit(changeFlag, 7);

 	ActorLocoMotion* locmove = m_Owner->getLocoMotion();
	WCoord oldpos = locmove->m_Position;

	bool jetfly = test_flag_bit(changeFlag, 3);
	// 1. jetfly 检测 如果客户端上报在jetfly状态，而实际没有装备，可以认为是作弊
	if (jetfly && !IsSwitchOff(gCheatConfig.SwitchFlagCheat))
	{
		if (m_Owner->getCurDorsumID() != ITEM_JETPACK)
		{
			if (ProcessCheat(CHEAT_TAG_JETFLY, true))  // 连续n次检查到身上没有火箭背包, 则认为作弊
			{
				jsonxx::Object cheat;
				cheat << "server_dorsumid" << m_Owner->getCurDorsumID();
				Rainbow::GetICloudProxyPtr()->InfoLog(m_Owner->getUin(), m_Owner->getOWID(), "cheat_jetfly_state", cheat);
				processKickOrNotify(gCheatConfig.SwitchFlagCheat, ERR_CODE_CHEAT_JETFLY);
				return ERR_CODE_CHEAT_JETFLY;
			}
		}
		else
		{ // 检查到装备了火箭背包则重置计数
			ProcessCheat(CHEAT_TAG_JETFLY, false);
			m_LegalFlying = true;
		}
	}

#if CHEAT_CHECK_ONGROUND
	// 2. onground 检测
	if (onground) // && !onVehicle && !onLadder)
	{
		CollideAABB box;
		m_Owner->getCollideBox(box);
		box.pos = box.pos - oldpos + targetPos;
		int y = pWorld->moveBox(box, WCoord(0, -10, 0)).y;
		bool cheatGround = (y <= -10);
		// cheatGround 放在后面保证前面函数的执行
		if (ProcessCheat(CHEAT_TAG_ONGROUND, cheatGround) && cheatGround)
		{
			if (now > m_LogTime[CHEAT_TAG_ONGROUND])
			{
				m_LogTime[CHEAT_TAG_ONGROUND] = now + LogStep;
				jsonxx::Object cheat;
				cheat << "client_posx" << targetPos.x;
				cheat << "client_posy" << targetPos.y;
				cheat << "client_posz" << targetPos.z;
				cheat << "client_onground" << onground;
				cheat << "server_checkmove_y" << y;
				Rainbow::GetICloudProxyPtr()->InfoLog(m_Owner->getUin(), m_Owner->getOWID(), "cheat_onground_state", cheat);
			}

			return ERR_CODE_CHEAT_ONGROUD;
		}
	}
#endif

	// 3. 检测飞行模式
	if (!m_LegalFlying && !IsSwitchOff(gCheatConfig.SwitchFlagCheat) && flymode)
	{
		// 内网客户端 + 内网服务器不用判断
		if (isTestEnv() && m_Owner->getApiid() == 999 && !GetClientInfoProxy()->getEnterParam("force_check_fly")[0])
			return 0;

		if (now > m_LogTime[CHEAT_TAG_FLYMOD])
		{
			m_LogTime[CHEAT_TAG_FLYMOD] = now + 2000;
			jsonxx::Object cheat;
			cheat << "client_flymod" << flymode;
			cheat << "server_flymod" << m_Owner->getFlying();
			cheat << "game_mod" << g_WorldMgr->getGameMode();
			cheat << "client_apiid" << m_Owner->getApiid();
			Rainbow::GetICloudProxyPtr()->InfoLog(m_Owner->getUin(), m_Owner->getOWID(), "cheat_flymode", cheat);
			
			if (ProcessCheat(CHEAT_TAG_FLYMOD, true))
				processKickOrNotify(gCheatConfig.SwitchFlagCheat, ERR_CODE_CHEAT_FLY_MOD);
		}

		return ERR_CODE_CHEAT_FLY_MOD;
	}

	// 4. 检查chip状态
	if (!IsSwitchOff(gCheatConfig.SwitchCheckClipState))
	{
		bool clip = test_flag_bit(changeFlag, 8);
		bool useClip = test_flag_bit(changeFlag, 9);
		if (useClip && now > m_LogTime[CHEAT_TAG_CLIP])
		{
			m_LogTime[CHEAT_TAG_CLIP] = now + LogStep;
			jsonxx::Object cheat;
			cheat << "client_clip" << clip;
			cheat << "client_useclip" << useClip;
			Rainbow::GetICloudProxyPtr()->InfoLog(m_Owner->getUin(), m_Owner->getOWID(), "cheat_clipmove", cheat);

			processKickOrNotify(gCheatConfig.SwitchCheckClipState, ERR_CODE_CHEAT_CLIP_MOVE);
			return 0;
		}
	}

	return 0;
}

bool PlayerCheatData::checkMoveSpeed(int changeFlag, float clientSpeed)
{
	//服务端移动速度
	auto funcWrapper = m_Owner->getFuncWrapper();
	if (funcWrapper == nullptr)
	{
		return false;
	}

	float serverSpeed = funcWrapper->getAIMoveSpeed();
	if (serverSpeed == 0 || clientSpeed == 0)
	{
		return false;
	}

	bool speedCheat = false;
	float exceedSpeed = 0;
	unsigned now = Rainbow::Timer::getSystemTick();
	if (serverSpeed >= clientSpeed)
	{
		speedCheat = false;
	} 
	else
	{
		exceedSpeed = (clientSpeed - serverSpeed) / serverSpeed;
		speedCheat = exceedSpeed > Rainbow::GetICloudProxyPtr()->MoveSpeedCheck();
	}

	/* if (speedCheat)
	{
		LOG_INFO("move info server:%f, client:%f", serverSpeed, clientSpeed);
	} */

	if (ProcessCheat(CHEAT_TAG_SPEED, speedCheat))
	{
		if (now > m_LogTime[CHEAT_TAG_SPEED])
		{
			m_LogTime[CHEAT_TAG_SPEED] = now + LogStep;
			jsonxx::Object cheat;
			cheat << "client_speed" << clientSpeed;
			cheat << "server_speed" << serverSpeed;
			cheat << "change_flag" << changeFlag;
			cheat << "exceed_speed" << exceedSpeed;
			Rainbow::GetICloudProxyPtr()->InfoLog(m_Owner->getUin(), m_Owner->getOWID(), "cheat_movespeed", cheat);
		}

		//LOG_INFO("move info cheat!");
		return true;
	}

	return false;
}

void PlayerCheatData::saveStaySky(int changeFlag, const WCoord & targetPos)
{
	// bool jetfly = test_flag_bit(changeFlag, 3);
	bool onground = test_flag_bit(changeFlag, 4);
	bool flymode = test_flag_bit(changeFlag, 5);
	// bool onVehicle = test_flag_bit(changeFlag, 6);
	// bool onLadder = test_flag_bit(changeFlag, 7);

	if (onground || flymode) // || jetfly || onVehicle || onLadder) 
	{
		m_StaySky = 0;
		//LOG_INFO("test, clean stay stamp, mod change");
		return;
	}

 	ActorLocoMotion* locmove = m_Owner->getLocoMotion();
	if (locmove == nullptr)
	{
		return;
	}

	if (m_GroundPos.isZero() )
	{
		m_GroundPos = locmove->m_Position;
		//LOG_INFO("test, save init ground pos x:%d, y:%d, z:%d", m_GroundPos.x, m_GroundPos.y, m_GroundPos.z);
	}

	//onGround状态切换
	if (locmove->m_OnGround && !onground)
	{
		//记录离地前位置
		m_GroundPos = locmove->m_Position;
		//LOG_INFO("test, save ground pos x:%d, y:%d, z:%d", m_GroundPos.x, m_GroundPos.y, m_GroundPos.z);
	}

	WCoord dp = targetPos - locmove->m_Position;
	if (dp.y != 0)
	{
		m_StaySky = 0;
		//LOG_INFO("test, clean stay stamp, dp.y != 0");
		return;
	}

	//记录滞空时间
	if (m_StaySky == 0)
	{
		m_StaySky = Rainbow::Timer::getSystemTick();
		//LOG_INFO("test, save stay stamp :%d", m_StaySky);
	}
}

void PlayerCheatData::resetPostion(const WCoord& pos)
{
	if (m_Owner->getWorld() == nullptr)
	{
		return;
	}

	MpActorTrackerEntry *entry = m_Owner->getWorld()->getMpActorMgr()->getTrackerEntry(m_Owner->getObjId());
	if (entry == nullptr)
	{
		return;
	}

	ActorLocoMotion* locmove = m_Owner->getLocoMotion();
	if (locmove == nullptr)
	{
		return;
	}
	locmove->setPosition(pos.x, pos.y, pos.z);

	entry->sendActorMovementToClient(m_Owner->getUin(), m_Owner, locmove->m_RotateYaw, locmove->m_RotationPitch);
}

void PlayerCheatData::Tick()
{
	if (_isSingleOrGodMode())
		return;
	m_ClickCount -= 1;
	if (m_ClickCount < 0) m_ClickCount = 0;

	if (!IsSwitchOff(gCheatConfig.SwitchCheckFly))
	{
		if (m_StaySky == 0)
		{
			return;
		}
		auto functionWrapper = m_Owner->getFuncWrapper();
		if ((functionWrapper && !functionWrapper->getCanMove()) || !m_Owner->checkActionAttrState(ENABLE_MOVE))
		{
			return;
		}

		auto now = Rainbow::Timer::getSystemTick();
		if (now - m_StaySky > gCheatConfig.StayStep)
		{
			//LOG_INFO("test stay, reset ground pos x:%d, y:%d, z:%d", m_GroundPos.x, m_GroundPos.y, m_GroundPos.z);
			resetPostion(m_GroundPos);
			m_StaySky = now;
			NotifyOwnerCheat(ERR_CODE_CHEAT_FLY_STAY);

			if (now > m_LogTime[CHEAT_TAG_FLY_STAY])
			{
				m_LogTime[CHEAT_TAG_FLY_STAY] = now + LogStep;
				jsonxx::Object cheat;
				cheat << "stay_sky" << m_StaySky;
				cheat << "stay_step" << gCheatConfig.StayStep;
				cheat << "stay_time_ms" << now - m_StaySky;
				Rainbow::GetICloudProxyPtr()->InfoLog(m_Owner->getUin(), m_Owner->getOWID(), "cheat_fly_stay", cheat);
			}
			m_FlyCheatCount++;
		}

		//2022/4/11 飞行作弊次数触发踢出房间 codeby:liushuxin
		//确保踢出逻辑只触发一次
		if (!m_FlyKickoff && m_FlyCheatCount > gCheatConfig.FlyCheatKick)
		{
			m_FlyKickoff = true;
			jsonxx::Object cheat;
			cheat << "fly_cheat_count" << m_FlyCheatCount;
			cheat << "fly_cheat_kick" << gCheatConfig.FlyCheatKick;
			Rainbow::GetICloudProxyPtr()->InfoLog(m_Owner->getUin(), m_Owner->getOWID(), "cheat_fly_kick", cheat);

			if (GameNetManager::getInstance()->getConnection())
				GameNetManager::getInstance()->getConnection()->kickoffMember(m_Owner->getUin(), ERR_CODE_CHEAT_FLY);
		}
	}
}

bool PlayerCheatData::checkContinueUp(int changeFlag, const WCoord & targetPos)
{
	// bool jetfly = test_flag_bit(changeFlag, 3);
	bool onground = test_flag_bit(changeFlag, 4);
	bool flymode = test_flag_bit(changeFlag, 5);
	// bool onVehicle = test_flag_bit(changeFlag, 6);
	// bool onLadder = test_flag_bit(changeFlag, 7);

	if (onground || flymode) // || jetfly || onVehicle || onLadder) 
	{
		m_FlyUp = 0;
		//LOG_INFO("test up, clean fly up stamp, mod change");
		return false;
	}

 	ActorLocoMotion* locmove = m_Owner->getLocoMotion();
	if (locmove == nullptr)
	{
		return false;
	}

	auto now = Rainbow::Timer::getSystemTick();
	WCoord dp = targetPos - locmove->m_Position;
	if (dp.y > 0 && m_FlyUp == 0)
	{
		m_FlyUp = now;
		//LOG_INFO("test up, save fly up stamp :%d", m_FlyUp);
	}

	if (dp.y <= 0)
	{
		m_FlyUp = 0;
		m_UpCount = 0;
		//LOG_INFO("test up, clean fly up stamp, dp.y < 0");
	}
	else
	{
		m_UpCount++;
	}

	//连续上升定义：在特定时间内，收到特定次数向上方向的移动
	if (m_FlyUp != 0 && now - m_FlyUp > gCheatConfig.UpStep && m_UpCount > gCheatConfig.UpCount)
	{
		m_FlyUp = 0;
		m_UpCount = 0;
		//LOG_INFO("test up, reset ground pos x:%d, y:%d, z:%d", m_GroundPos.x, m_GroundPos.y, m_GroundPos.z);
		resetPostion(m_GroundPos);

		if (now > m_LogTime[CHEAT_TAG_FLY_UP])
		{
			m_LogTime[CHEAT_TAG_FLY_UP] = now + LogStep;
			jsonxx::Object cheat;
			cheat << "up_start" << m_FlyUp;
			cheat << "up_step" << gCheatConfig.UpStep;
			cheat << "up_time_ms" << now - m_FlyUp;
			Rainbow::GetICloudProxyPtr()->InfoLog(m_Owner->getUin(), m_Owner->getOWID(), "cheat_fly_up", cheat);
		}
		return true;
	}

	return false;
}

bool PlayerCheatData::checkSwing(int changeFlag, const WCoord & targetPos)
{
	// bool jetfly = test_flag_bit(changeFlag, 3);
	bool onground = test_flag_bit(changeFlag, 4);
	bool flymode = test_flag_bit(changeFlag, 5);
	// bool onVehicle = test_flag_bit(changeFlag, 6);
	// bool onLadder = test_flag_bit(changeFlag, 7);

	if (onground || flymode) // || jetfly || onVehicle || onLadder) 
	{
		m_FlyDirectionUp = false;
		m_SwingCount = 0;
		//LOG_INFO("test swing, clean direction, mod change");
		return false;
	}

 	ActorLocoMotion* locmove = m_Owner->getLocoMotion();
	if (locmove == nullptr)
	{
		return false;
	}

	auto now = Rainbow::Timer::getSystemTick();
	WCoord dp = targetPos - locmove->m_Position;

	if (dp.y == 0)
	{
		return false;
	}

	bool nowDir = dp.y > 0 ? true : false;

	if (m_FlyDirectionUp != nowDir)
	{
		m_SwingCount++;
		m_FlyDirectionUp = nowDir;
		//LOG_INFO("test swing,  direction change count:%d", m_SwingCount);
	}

	if (m_SwingCount >= gCheatConfig.SwingStep)
	{
		m_FlyDirectionUp = false;
		m_SwingCount = 0;
		//LOG_INFO("test swing, reset ground pos x:%d, y:%d, z:%d", m_GroundPos.x, m_GroundPos.y, m_GroundPos.z);
		resetPostion(m_GroundPos);

		if (now > m_LogTime[CHEAT_TAG_FLY_SWING])
		{
			m_LogTime[CHEAT_TAG_FLY_SWING] = now + LogStep;
			jsonxx::Object cheat;
			cheat << "swing_step" << gCheatConfig.SwingStep;
			Rainbow::GetICloudProxyPtr()->InfoLog(m_Owner->getUin(), m_Owner->getOWID(), "cheat_fly_swing", cheat);
		}
		return true;
	}
	return false;
}

bool PlayerCheatData::isOnGround(const WCoord & targetPos)
{
	if (_isSingleOrGodMode())
		return true;
	//判断目标格子下方是否空气 , 解决星站，游泳，喷气管道误判
	WCoord findPos = CoordDivBlock(targetPos - WCoord(0, BLOCK_SIZE, 0));
	Block findBlock = m_Owner->getWorld()->getBlock(findPos);

	bool targetNotAir = !findBlock.isAir();
	if (targetNotAir)
	{
		return true;
	}

	WCoord& standWCoord = m_Owner->getPosition();

	//2022/5/24  codeby:liushuxin TODO  服务器未加载相关数据判断碰撞无效
	/*
	bool footCollide = m_Owner->isExistInCollide(standWCoord + WCoord(0, -BLOCK_SIZE, 0));
	if (footCollide)
	{
		return true;
	}
	*/

	std::vector<WCoord> blockPos = {
		CoordDivBlock(standWCoord),
		//头顶, 情况：抓钩(因上升惯性贴屋顶)
		CoordDivBlock(standWCoord + WCoord(0, BLOCK_SIZE, 0)),
		CoordDivBlock(standWCoord + WCoord(0, 2*BLOCK_SIZE, 0)),
		//身体周围,  情况：爬水柱(允许人在水柱外贴边上爬)
		CoordDivBlock(standWCoord + WCoord(-BLOCK_SIZE, 0, 0)),
		CoordDivBlock(standWCoord + WCoord(BLOCK_SIZE, 0, 0)),
		CoordDivBlock(standWCoord + WCoord(0, 0, BLOCK_SIZE)),
		CoordDivBlock(standWCoord + WCoord(0, 0, -BLOCK_SIZE)),
		CoordDivBlock(standWCoord + WCoord(-BLOCK_SIZE, 0, BLOCK_SIZE)),
		CoordDivBlock(standWCoord + WCoord(-BLOCK_SIZE, 0, -BLOCK_SIZE)),
		CoordDivBlock(standWCoord + WCoord(BLOCK_SIZE, 0, BLOCK_SIZE)),
		CoordDivBlock(standWCoord + WCoord(BLOCK_SIZE, 0, -BLOCK_SIZE)),
		//脚底周围	, 情况: 潜行模式（在格子边缘脚底踩空）
		CoordDivBlock(standWCoord + WCoord(-BLOCK_SIZE, -BLOCK_SIZE, 0)),
		CoordDivBlock(standWCoord + WCoord(BLOCK_SIZE, -BLOCK_SIZE, 0)),
		CoordDivBlock(standWCoord + WCoord(0, -BLOCK_SIZE, BLOCK_SIZE)),
		CoordDivBlock(standWCoord + WCoord(0, -BLOCK_SIZE, -BLOCK_SIZE)),
		CoordDivBlock(standWCoord + WCoord(-BLOCK_SIZE, -BLOCK_SIZE, BLOCK_SIZE)),
		CoordDivBlock(standWCoord + WCoord(-BLOCK_SIZE, -BLOCK_SIZE, -BLOCK_SIZE)),
		CoordDivBlock(standWCoord + WCoord(BLOCK_SIZE, -BLOCK_SIZE, BLOCK_SIZE)),
		CoordDivBlock(standWCoord + WCoord(BLOCK_SIZE, -BLOCK_SIZE, -BLOCK_SIZE)),
		//脚底下一格子，预防特殊格子
		CoordDivBlock(standWCoord + WCoord(0, -BLOCK_SIZE, 0)),
	};
	for (int i = 0; i < blockPos.size(); i++)
	{
		Block block = m_Owner->getWorld()->getBlock(blockPos[i]);
		if (!block.isAir())
		{
			return true;
		}
	}

	//排除人踩人和站在载具上 
	CollideAABB box;
	m_Owner->getCollideBox(box);
	box.expand(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE);
	std::vector<IClientActor *>actors;
	m_Owner->getWorld()->getActorsInBox(actors, box, -1, true);
	//获取的actor包含自己
	if (actors.size() >= 2)
	{
		return true;
	}

	return false;
}

bool PlayerCheatData::CheckClickBlock(const WCoord& pos, int blockid, World* pWorld)
{
	if (!m_Owner)
		return true;

	// 5：OWTYPE_GAMEMAKER_RUN 自制玩法的运行模式
	if (!g_WorldMgr || g_WorldMgr->getGameMode() != 5)
		return true;

	if (m_ClickCount >= gCheatConfig.s_MaxClickCount)
		return false;

	// 校验本地id是否匹配
	auto world = pWorld ? pWorld : m_Owner->getWorld();
	if (world && blockid != 0)
	{
		int serverId = world->getBlockID(pos);
		if (serverId != blockid)
			return false;
	}

	m_ClickCount++;

	return true;
}

void PlayerCheatData::OnGotoPosEx(const WCoord & pos)
{
	m_gotoPos = pos;
}
/**
 * @brief 服务器检查客户端使用物品协议的位置, 如果与服务器位置差距过大则返回服务器位置作为使用位, 并认为客户端有作弊嫌疑
 * 
 * @param client_pos 客户端坐标
 * @param item_id 使用的物品ID
 * @return WCoord 取代客户端坐标, 作为服务器运算使用位置
 */
WCoord PlayerCheatData::GetUsetItemPosition(const WCoord& client_pos, int item_id)
{
	if (!m_Owner || IsSwitchOff(gCheatConfig.SwitchUseItemDistance) || (gCheatConfig.UseItemDistLimitBase <= 0))
		return client_pos;

	if (_isSingleOrGodMode())
		return client_pos;
	const ItemDef *def = GetDefManagerProxy()->getItemDef(item_id);
	// 仅限制枪械和弓
	if (!def || !(def->UseTarget == ITEM_USE_GUN || def->UseTarget == ITEM_USE_BOW))
		return client_pos;

	int limit = gCheatConfig.UseItemDistLimitBase;
	WCoord server_pos = m_Owner->getEyePosition();
	WCoord delta = client_pos - server_pos;
	
	WCoord use_wcoord;
	if (delta.lengthSquared() > limit) {
		jsonxx::Object cheat;
		jsonxx::Array server_pos_json, client_post_json;
		server_pos_json << server_pos.x << server_pos.y << server_pos.z;
		client_post_json << client_pos.x << client_pos.y << client_pos.z;
		cheat << "server_postion" << server_pos_json;
		cheat << "client_postion" << client_post_json;
		cheat << "server_delta" << delta.lengthSquared();
		cheat << "client_item" << item_id;
		cheat << "item_type" << def->UseTarget;
		
		Rainbow::GetICloudProxyPtr()->InfoLog(m_Owner->getUin(), m_Owner->getOWID(), "cheat_use_item_distance", cheat);

		processKickOrNotify(gCheatConfig.SwitchUseItemDistance, ERR_CODE_CHEAT_ITEM_DISTANCE);
		use_wcoord = server_pos; 
	}
	else 
		use_wcoord = client_pos;

	return use_wcoord;
}
/**
 * @brief 服务器检查挖掘协议的位置, 如果与服务器位置差距过大则认为客户端有作弊嫌疑
 * 
 * @param block_pos 挖掘目标位置
 * @param dig_method 挖掘方法
 * @return true 正常挖掘
 * @return false 作弊嫌疑
 */
bool PlayerCheatData::CheckDigInstance(const WCoord &block_pos, int dig_method){
	if (!m_Owner || IsSwitchOff(gCheatConfig.SwitchDigDistance))
		return true;
	
	if (_isSingleOrGodMode())
		return true;
	// 蓄力挖掘没有此限制
	if (DIG_METHOD_CHARGE == dig_method){
		return true;
	}

	WCoord server_role_pos = CoordDivBlock(m_Owner->getPosition());
	WCoord delta = server_role_pos - block_pos;

	if (delta.lengthSquared() > gCheatConfig.BlockDistLimit) {
		jsonxx::Object cheat;
		jsonxx::Array server_role_pos_json, block_post_json;
		
		server_role_pos_json << server_role_pos.x << server_role_pos.y << server_role_pos.z;
		block_post_json << block_pos.x << block_pos.y << block_pos.z;
		cheat << "server_postion" << server_role_pos_json;
		cheat << "client_postion" << block_post_json;
		cheat << "server_delta" << delta.lengthSquared();
		cheat << "client_method" << dig_method;
		Rainbow::GetICloudProxyPtr()->InfoLog(m_Owner->getUin(), m_Owner->getOWID(), "cheat_dig_distance", cheat);

		processKickOrNotify(gCheatConfig.SwitchDigDistance, ERR_CODE_CHEAT_DIG_DISTANCE);

		LOG_WARNING("挖掘距离太远 ");

		return false;
	}
	return true;
}
/**
 * @brief 服务器检查放置方块协议的位置, 如果与服务器位置差距过大则认为客户端有作弊嫌疑
 * 
 * @param block_pos 放置目标位置
 * @return true 正常放置
 * @return false 作弊嫌疑
 */
bool PlayerCheatData::CheckPlaceBlockInstance(const WCoord &block_pos){
	if (!m_Owner || IsSwitchOff(gCheatConfig.SwitchPlaceBlockDistance))
		return true;
	
	if (_isSingleOrGodMode())
		return true;

	WCoord server_role_pos = CoordDivBlock(m_Owner->getPosition());
	WCoord delta = server_role_pos - block_pos;

	if (delta.lengthSquared() > gCheatConfig.BlockDistLimit) {
		jsonxx::Object cheat;
		jsonxx::Array server_role_pos_json, block_post_json;
		
		server_role_pos_json << server_role_pos.x << server_role_pos.y << server_role_pos.z;
		block_post_json << block_pos.x << block_pos.y << block_pos.z;
		cheat << "server_postion" << server_role_pos_json;
		cheat << "client_postion" << block_post_json;
		cheat << "server_delta" << delta.lengthSquared();
		Rainbow::GetICloudProxyPtr()->InfoLog(m_Owner->getUin(), m_Owner->getOWID(), "cheat_place_block_distance", cheat);

		processKickOrNotify(gCheatConfig.SwitchPlaceBlockDistance, ERR_CODE_CHEAT_PLACE_DISTANCE);

		return false;
	}
	return true;
}

/**
 * @brief 检测是否有使用地图编辑器权限
 * 
 * @return true 正常
 * @return false 作弊嫌疑
 */
bool PlayerCheatData::CheckUseMapEdit(){
	if (!m_Owner || IsSwitchOff(gCheatConfig.SwitchUseMapEdit))
		return true;
	 
	if (_isSingleOrGodMode())
		return true;
	int toolId = m_Owner->getCurToolID();
	bool disableDanger = GetClientInfoProxy()->getPlayerPermits(m_Owner->getUin(), CS_PERMIT_DANGER);
	if (toolId != ITEM_MAPEDIT  // 需要手持地形编辑器
		|| (disableDanger == 1 && GetClientInfoProxy()->isItemBan(toolId)) // 禁用危险品 且 当前手持物品被禁用
	)
	{
		jsonxx::Object cheat;
		cheat << "server_toolId" << toolId;
		cheat << "server_needtool" << ITEM_MAPEDIT;

		Rainbow::GetICloudProxyPtr()->InfoLog(m_Owner->getUin(), m_Owner->getOWID(), 
			"cheat_use_mapedit", cheat);

		processKickOrNotify(gCheatConfig.SwitchUseMapEdit, ERR_CODE_CHEAT_MAP_EDIT);
		return false;
	}
	return true;
}


/**
 * @brief 检查客户端的上传跳跃是否正常
 * 
 * @return true 正常
 * @return false 作弊嫌疑
 */
bool PlayerCheatData::CheckJump(float client_height, bool onground, bool airjump){
	if (!m_Owner || IsSwitchOff(gCheatConfig.SwitchJumpHeight))
		return true;

	bool last_is_air_jump = m_LastAirJump;
	m_LastAirJump = airjump;
	unsigned cheat_type = 0;  // 0-未发现 1-跳跃高度 2-连续连跳 3-onround 4-禁用天赋时连跳
	jsonxx::Object cheat;
	do
	{
		uint64_t now = Rainbow::Timer::getSystemTick();
		if (!m_JumpHeightChange.inChangePeriod(now, 2000))
		{
			const float limit = 0.2f;
			PlayerLocoMotion *loc = static_cast<PlayerLocoMotion *>(m_Owner->getLocoMotion());
			if (!loc){
				return true;
			}
			float server_height = loc->getJumpHeight();
			if (client_height > server_height+limit || client_height < server_height-limit)
			{
				cheat_type = 1;
				cheat << "server_height" << server_height;
				cheat << "client_height" << client_height;
				break;
			}
		}
		if (airjump)
		{
			if (last_is_air_jump)
			{
				cheat_type = 2;
				break;
			}
			if (g_WorldMgr && !g_WorldMgr->getPlayerPermit(ENABLE_SPECIALPROP))
			{
				cheat_type = 4;
				break;
			}
		}
		
		if (!m_OnGroundChange.inChangePeriod(now, 2000))
		{
			if (onground && m_Owner->getLocoMotion() && !m_Owner->getLocoMotion()->getOnGround())
			{
				cheat_type = 3;
				
				cheat << "last_change_onground" << m_OnGroundChange.m_LastFlagChange;
				break;
			}
		}


	} while (false);
	
	if (ProcessCheat(CHEAT_TAG_JUMP_HEIGHT, cheat_type)){
		cheat << "cheat_type" << cheat_type;
		Rainbow::GetICloudProxyPtr()->InfoLog(m_Owner->getUin(), m_Owner->getOWID(), "cheat_jump_height", cheat);
		processKickOrNotify(gCheatConfig.SwitchJumpHeight, ERR_CODE_CHEAT_JUMP_HEIGHT);

		return false;
	}
	
	return true;
}

/**
 * @brief 检查客户端的上传的足球模式滑铲距离是否正常
 * @param range 客户端上传的滑铲距离
 * @return true 正常
 * @return false 作弊嫌疑
 */
bool PlayerCheatData::CheckTackleRange(int range){
	if (!m_Owner || IsSwitchOff(gCheatConfig.SwitchTackle))
		return true;

	bool isCheat = ((m_Owner->getOPWay() != PLAYEROP_WAY_FOOTBALLER) // 当前不是足球模式
		|| (range != m_Owner->getTackleRange())); // 滑铲距离异常
	if (ProcessCheat(CHEAT_TAG_TACKLE, isCheat)){
		jsonxx::Object cheat;
		cheat << "server_range" << m_Owner->getTackleRange();
		cheat << "client_range" << range;
		cheat << "server_op" << m_Owner->getOPWay();
		Rainbow::GetICloudProxyPtr()->InfoLog(m_Owner->getUin(), m_Owner->getOWID(), "cheat_tackle_range", cheat);

		processKickOrNotify(gCheatConfig.SwitchTackle, ERR_CODE_CHEAT_TACKLE);

		return false;
	}
	
	return true;
}
/**
 * @brief 检查客户端的上传的篮球模式抢断距离是否正常
 * @param range 客户端上传的抢断距离
 * @return true 正常
 * @return false 作弊嫌疑
 */
bool PlayerCheatData::CheckGrabRange(int range){
	if (!m_Owner || IsSwitchOff(gCheatConfig.SwitchGrab))
		return true;

	bool isCheat = ((m_Owner->getOPWay() != PLAYEROP_WAY_BASKETBALLER) // 当前不是篮球模式
		|| (range != m_Owner->getGrabRange())  // 抢断距离异常
		); 
	if (ProcessCheat(CHEAT_TAG_GRAB, isCheat)){
		jsonxx::Object cheat;
		cheat << "server_range" << m_Owner->getGrabRange();
		cheat << "client_range" << range;
		cheat << "server_op" << m_Owner->getOPWay();
		Rainbow::GetICloudProxyPtr()->InfoLog(m_Owner->getUin(), m_Owner->getOWID(), "cheat_grab_range", cheat);

		processKickOrNotify(gCheatConfig.SwitchGrab, ERR_CODE_CHEAT_GRAB);

		return false;
	}
	
	return true;
}
/**
 * @brief 检查客户端的上传的篮球模式带球冲刺距离是否正常
 * @param range 客户端上传的带球冲刺距离
 * @return true 正常
 * @return false 作弊嫌疑
 */
bool PlayerCheatData::CheckDribbleRange(int range){
	if (!m_Owner || IsSwitchOff(gCheatConfig.SwitchDribble))
		return true;

	bool isCheat = ((m_Owner->getOPWay() != PLAYEROP_WAY_BASKETBALLER) // 当前不是篮球模式
		|| (range != m_Owner->getDribbleRange())  // 带球冲刺距离异常
		|| (!m_Owner->getCatchBall())  // 未持球
		); 
	if (ProcessCheat(CHEAT_TAG_DRIBBLE, isCheat)){
		jsonxx::Object cheat;
		cheat << "server_range" << m_Owner->getDribbleRange();
		cheat << "client_range" << range;
		cheat << "server_op" << m_Owner->getOPWay();
		cheat << "server_catch_ball" << (m_Owner->getCatchBall()? "true": "false");
		Rainbow::GetICloudProxyPtr()->InfoLog(m_Owner->getUin(), m_Owner->getOWID(), "cheat_dribble_range", cheat);

		processKickOrNotify(gCheatConfig.SwitchDribble, ERR_CODE_CHEAT_DRIBBLE);

		return false;
	}
	return true;
}


// 返回true表示ace被剥离
bool PlayerCheatData::CheckAceTakenAway(const std::string& aceinfo)
{
	if (!m_Owner || IsSwitchOff(gCheatConfig.SwitchAceTaken) || aceinfo.empty())
		return false;

	return Rainbow::GetICloudProxyPtr()->CheckAceTaken(aceinfo);

	// TssSdkAntiData2 data = {0};
	// int ret = tss_data_descrypt2(aceinfo.data(), aceinfo.length(), &data);
	// if (ret == -1)
	// {
	// 	jsonxx::Object log;
	// 	log << "aceinfo" << aceinfo;
	// 	log << "ret" << ret;
	// 	Rainbow::GetICloudProxyPtr()->InfoLog(m_Owner->getUin(), 0, "ace_descrypt_err", log);
	// 	return false;
	// }
	//
	// if ((data.state_word1 & AntiDataMagicFlag2_UpdateCDNRunned) == 0 &&
	// 	(data.state_word1 & AntiDataMagicFlag2_ScheduleEngIsRunnig) == 0 &&
	// 	(data.state_word1 & AntiDataMagicFlag2_CommDatDownloaded) == 0)
	// {
	// 	jsonxx::Object cheat;
	// 	cheat << "aceinfo" << aceinfo;
	// 	cheat << "ace_version" << data.version;
	// 	cheat << "state_world1" << data.state_word1;
	// 	cheat << "state_cdn_run" << (int)(data.state_word1 & AntiDataMagicFlag2_UpdateCDNRunned);
	// 	cheat << "state_engine_run" << (int)(data.state_word1 & AntiDataMagicFlag2_ScheduleEngIsRunnig);
	// 	cheat << "state_data_download" << (int)(data.state_word1 & AntiDataMagicFlag2_CommDatDownloaded);
	// 	Rainbow::GetICloudProxyPtr()->InfoLog(m_Owner->getUin(), 0, "cheat_ace_tacken", cheat);
	//
	// 	processKickOrNotify(gCheatConfig.SwitchAceTaken, ERR_CODE_CHEAT_ACE_TAKEN);
	// 	
	// 	return true;
	// }
	//
	// return false;
}

void PlayerCheatData::processKickOrNotify(unsigned switchValue, unsigned errCode)
{
	if (IsKickOn(switchValue))
	{
		CheatKickPlayer(m_Owner->getUin(), errCode);
	}
	else if (IsNotifyOn(switchValue))
	{
		NotifyOwnerCheat(errCode);
	}
}

void PlayerCheatData::NotifyOwnerCheat(int errCode)
{
	if (!m_Owner)
		return;
	m_Owner->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 1001300, errCode);
	MINIW::ScriptVM::game()->callFunction("SendClientReprotCheat", "ii", m_Owner->getUin(), errCode);
}

void PlayerCheatData::ProcessMoveMsgBlock(int msgCode)
{
	jsonxx::Object cheat;
	cheat << "client_msgcode" << msgCode;
	Rainbow::GetICloudProxyPtr()->InfoLog(m_Owner->getUin(), 0, "cheat_move_protocal", cheat);
	processKickOrNotify(gCheatConfig.SwitchCheckRoleMove, ERR_CODE_CHEAT_MOVE_PROTOCAL);
}

// true表示有问题
bool PlayerCheatData::CheckHeartBeatCheat(unsigned serverTime, unsigned clientTime, const std::string& aceinfo)
{
	if (!m_Owner || IsSwitchOff(gCheatConfig.SwitchHeartBeatSpeed))
		return false;
	// 首次 如果这两个值一直一样，则可能客户端使用外挂锁定值了，需要在服务器做发送数据缓存再校验客户端的上报时间是否合法
	if (serverTime == clientTime)
		return false;

	unsigned int curTime = Rainbow::Timer::getSystemTick();
	// 使用tick检测，精度较高 暂时容错2秒
	bool cheat = (clientTime > (curTime + 2000) || clientTime < serverTime);
	if (ProcessCheat(CHEAT_TAG_HEARTBEAT, cheat))
	{
		jsonxx::Object cheat;
		cheat << "server_time" << serverTime;
		cheat << "client_time" << clientTime;
		cheat << "server_cur_time" << curTime;
		Rainbow::GetICloudProxyPtr()->InfoLog(m_Owner->getUin(), 0, "cheat_time_scale", cheat);

		processKickOrNotify(gCheatConfig.SwitchHeartBeatSpeed, ERR_CODE_CHEAT_TIME_SPEED);
		return true;
	}

	// 检测aceinfo
	return CheckAceTakenAway(aceinfo);
}

bool PlayerCheatData::CheckPackGiftCheat(int packIndex, int costItemId, int costItemCount, const std::map<int, int>& addMap)
{
	if (!m_Owner || !m_Owner->getBackPack())
		return false;

	if (IsSwitchOff(gCheatConfig.SwitchPackGift))
		return false;

	int hasItemID = m_Owner->getBackPack()->getGridItem(packIndex);
	int hasItemCount = m_Owner->getBackPack()->getGridNum(packIndex);

	jsonxx::Object log;
	log << "client_packindex" << packIndex;
	log << "server_itemid" << hasItemID;
	log << "server_count" << hasItemCount;
	log << "client_costitem" << costItemId;
	log << "client_costitemcount" << costItemCount;

	int lastGift = GetLastGiftItem();
	SetLastGiftItem(0);
	if (test_flag_bit(gCheatConfig.SwitchPackGift, 1))
	{
		if (lastGift == 0 || lastGift != hasItemID || hasItemCount <= 0 || costItemCount < 0)
		{
			log << "server_lastgiftid" << lastGift;
			Rainbow::GetICloudProxyPtr()->InfoLog(m_Owner->getUin(), 0, "cheat_gift_item", log);
			return true;
		}
	}

	// 校验道具id是否在配置中
	ItemDef* itemDef =  GetDefManagerProxy()->getAutoUseForeignID(hasItemID);
	if (!itemDef)
	{
		Rainbow::GetICloudProxyPtr()->InfoLog(m_Owner->getUin(), 0, "cheat_gift_no_itemdef", log);
		return true;
	}

	PackGiftDef* def = GetDefManagerProxy()->getPackGiftDef(itemDef->ID);
	if (!def)
	{
		Rainbow::GetICloudProxyPtr()->InfoLog(m_Owner->getUin(), 0, "cheat_gift_not_packgift", log);
		return true;
	}

	if (test_flag_bit(gCheatConfig.SwitchPackGift, 2))
	{
		// 检测数量 packtype为1是随机 否则为固定奖励
		if ((def->iPackType == 1 && addMap.size() > def->iMaxOpenNum) ||
			(def->iPackType != 1 && addMap.size() > def->vPackItemList.size()))
		{
			log << "server_max_opennum" << def->iMaxOpenNum;
			log << "client_add_count" << addMap.size();
			log << "server_pack_type" << def->iPackType;
			log << "server_itemlist_size" << def->vPackItemList.size();
			Rainbow::GetICloudProxyPtr()->InfoLog(m_Owner->getUin(), 0, "cheat_gift_item_count", log);
			return true;
		}
	}

	// 校验道具id是否在配置中   
	// TODO(liusijia) 整个随机过程需要修改到主机端来
	if (test_flag_bit(gCheatConfig.SwitchPackGift, 3))
	{
		std::set<int> defItemSet;
		for (auto& it : def->vPackItemList) {
			int defItemId = it.iItemInfo / 1000;
			ItemDef* addItemDef = GetDefManagerProxy()->getAutoUseForeignID(defItemId);
			defItemSet.insert(addItemDef ? addItemDef->ID : 101);
		}

		for (auto& pair : addMap)
		{
			if (defItemSet.find(pair.first) == defItemSet.end())
			{
				ostringstream os;
				for (auto id : defItemSet)
				{
					os << id << ',';
				}
				log << "server_items" << os.str();
				log << "client_want_item" << pair.first;
				Rainbow::GetICloudProxyPtr()->InfoLog(m_Owner->getUin(), 0, "cheat_gift_item_get", log);
				return true;
			}
		}
	}

	Rainbow::GetICloudProxyPtr()->InfoLog(m_Owner->getUin(), 0, "cheat_gift_item_use", log);
	return false;
}

/**
 * @brief 检查客户端的上传的碰撞盒是否正常, 是否有穿墙嫌疑
 * @param height 客户端上传的高度
 * @param size 客户端上传的尺寸
 * @return true 正常
 */
bool PlayerCheatData::CheckClipBound(int height, int size){
	if (!m_Owner || IsSwitchOff(gCheatConfig.SwitchHostCheckClip))
		return true;

	ActorLocoMotion* locmove = m_Owner->getLocoMotion();
	if (!locmove)
		return true;
	
	bool isCheat = (
		height != locmove->m_BoundHeight ||
		size != locmove->m_BoundSize
	);
	if (ProcessCheat(CHEAT_TAG_HOST_CLIP, isCheat)){
		jsonxx::Object cheat;
		cheat << "server_height" << locmove->m_BoundHeight;
		cheat << "server_size" << locmove->m_BoundSize;
		cheat << "client_height" << height;
		cheat << "client_size" << size;
		Rainbow::GetICloudProxyPtr()->InfoLog(m_Owner->getUin(), m_Owner->getOWID(), "cheat_clip_bound", cheat);

		processKickOrNotify(gCheatConfig.SwitchHostCheckClip, ERR_CODE_CHEAT_CLIP_HOST);

		return false;
	}
	return true;
}

/**
 * @brief 检查客户端的上传的role controller参数是否正常, 是否有穿墙嫌疑
 * @param radius 客户端上传
 * @param half_height 客户端上传
 * @return true 正常
 */
bool PlayerCheatData::CheckClipController(float radius, float half_height){
	if (!m_Owner || IsSwitchOff(gCheatConfig.SwitchHostCheckClipRadius))
		return true;

	PlayerLocoMotion* locmove = dynamic_cast<PlayerLocoMotion*> (m_Owner->getLocoMotion());
	if (!locmove)
		return true;

	float server_radius = 0.0f;
	float server_half_height = 0.0f;
	if (locmove->getPhysType() == RolePhysType::PHYS_ROLECONTROLLER)
	{
		auto* role_controller = locmove->getRoleController();
		if (!role_controller)
			return true;

		server_radius = role_controller->GetRadius();
		server_half_height = role_controller->GetHalfHeight();
	}
	else if (locmove->getPhysType() == RolePhysType::PHYS_RIGIDBODY)
	{
		auto* rigid = locmove->getRigidDynamicActor();
		if (!rigid)
			return true;

		Rainbow::CapsuleCollider* collider = rigid->GetComponent<CapsuleCollider>();
		if (!collider)
			return true;

		server_radius = collider->GetRadius();
		server_half_height = collider->GetHeight() / 2 - server_radius;
	}
	
	bool isCheat = (
		radius > server_radius + Rainbow::EPSILON || radius < server_radius - Rainbow::EPSILON ||
		half_height > server_half_height + Rainbow::EPSILON || half_height < server_half_height - Rainbow::EPSILON
	);
	if (ProcessCheat(CHEAT_TAG_HOST_CLIP, isCheat)){
		jsonxx::Object cheat;
		cheat << "server_radius" << server_radius;
		cheat << "server_half_height" << half_height;
		cheat << "client_radius" << radius;
		cheat << "client_halfheight" << half_height;
		Rainbow::GetICloudProxyPtr()->InfoLog(m_Owner->getUin(), m_Owner->getOWID(), "cheat_clip_controller", cheat);

		processKickOrNotify(gCheatConfig.SwitchHostCheckClipRadius, ERR_CODE_CHEAT_CLIP_HOST);

		return false;
	}
	return true;
}

class CheatCollisionDetect: public CollisionDetect{
public:
	virtual void addObstacle(const WCoord &minpos, const WCoord &maxpos){
		const int DIM_MAX = 100;  // 模型超过了100x100x100, 在其它格子计算时不会判断它的边界, 进入它的格子时判断边界认为已经进入就不限制继续移动了
		if (maxpos.x - minpos.x > DIM_MAX || maxpos.y - minpos.y > DIM_MAX || maxpos.z - minpos.z > DIM_MAX)
			return;
		CollisionDetect::addObstacle(minpos, maxpos);
	}
};

/**
 * @brief 检测玩家与方块的重合
 * @param player 玩家对象
 * @param pos 目标位置
 * @return boolean, true -- 有重合
*/
bool PlayerCheatData::isCollideInteract(ClientPlayer *player, const WCoord &pos)
{
	if (!player)
		return false;
	if (player->getWorld()) {
		auto pworld = player->getWorld();
		PlayerLocoMotion * locmove = static_cast<PlayerLocoMotion *>(player->getLocoMotion());
		
		/*  无法使用, 暂时屏蔽 2024.05.09 by huanglin
		do
		{
			auto pPhysScene = pworld->m_PhysScene;
			if (!pPhysScene)
				break;
			float boundheight = (locmove->m_BoundHeight - locmove->m_BoundSize) / 2.0f;
			float boundsize = locmove->m_BoundSize / 2.0f;

			Rainbow::Vector3f position;
			static unsigned int iSweepLayerMask = ~(1 << Rainbow::CustomGameLayerIndex::kLayerIndexCustom_Player);
			Rainbow::Vector3f p1, p2;
			boundsize = boundsize - 2;

			p1 = Rainbow::Vector3f(pos.x, pos.y + boundsize + 10, pos.z);
			p2 = Rainbow::Vector3f(pos.x, pos.y + boundheight, pos.z);
			Rainbow::ColliderCache cache = pPhysScene->OverlapCapsule(boundsize / 2, p1, p2, iSweepLayerMask, true);
			if (!cache.empty())
				break;
			return false;
		} while (0);
		*/
		{
			CollideAABB box;  // 玩家的碰撞盒
			locmove->getCheatCollideBox(box, pos);
			{ // 此处基本是World::isBoxCollide的复制, 只将碰撞盒检测类改为了CheatCollisionDetect
				WCoord minpos(box.pos), maxpos(box.pos + box.dim);
				WCoord grid1 = CoordDivBlock(minpos);
				WCoord grid2 = CoordDivBlock(WCoord(maxpos.x - 1, maxpos.y - 1, maxpos.z - 1)); //只是边重叠不算碰撞

				static CheatCollisionDetect coldetect;
				coldetect.reset(minpos, maxpos);

				for (int z = grid1.z; z <= grid2.z; z++)
				{
					for (int y = grid1.y; y <= grid2.y; y++)
					{
						for (int x = grid1.x; x <= grid2.x; x++)
						{
							Block block = pworld->getBlock(WCoord(x, y, z));
							int blockid = block.getResID();
							if (block.isEmpty() || blockid == BLOCK_UNLOAD)
								continue;
							BlockMaterial *blockmtl = BlockMaterialMgr::getSingleton().getMaterial(block.getResID());
							if (blockmtl && blockmtl->defBlockMove())
							{
								WCoord ob1(x*BLOCK_SIZE, y*BLOCK_SIZE, z*BLOCK_SIZE);
								WCoord ob2((x + 1)*BLOCK_SIZE, (y + 1)*BLOCK_SIZE, (z + 1)*BLOCK_SIZE);

								if (coldetect.intersect(ob1, ob2))
								{  // 将有重合的位置加入碰撞盒检测
									blockmtl->createCollideData(&coldetect, pworld, WCoord(x, y, z));
								}
							}
						}
					}
				}

				if (coldetect.intersectBox(box))  // 检测是否有碰撞盒重合
					return true;
			}
		}
	}
	return false;
}

/**
 * @brief 服务器检测玩家位置是否在墙里, 是否需要被重置位置
 * @param oldpos 移动前的位置
 * @param reset_pos 结果参数, 当return为true时才会被设置, 表示玩家位置需要被设置为这个值
 * @return true 需要重置玩家位置
 */
bool PlayerCheatData::checkCollide(const WCoord &oldpos, WCoord &reset_pos){
	if (!m_Owner || IsSwitchOff(gCheatConfig.SwitchNoCollision))
		return false;

	if (_isSingleOrGodMode())
		return false;
	auto locmove = m_Owner->getLocoMotion();
	if(locmove->isInsideOpaqueBlock())
	{
		WCoord pos;
		if(locmove->getOneNoOpaqueBlock(&pos))
		{
			reset_pos = pos;
			return true;
		}
	}
	if (isCollideInteract(m_Owner, locmove->getPosition()))  // 检测是否有碰撞盒重合
	{
		reset_pos = oldpos;
		return true;
	}
	return false;
}

/**
 * @brief 检查ADShop请求获取物品是否正常
 * 
 * @param tabid 商品列表ID
 * @param goods_id 商品ID
 * @return true 正常操作
 */
bool PlayerCheatData::checkADShopCheat(int tabid, int goods_id){
	if (!m_Owner || IsSwitchOff(gCheatConfig.SwitchADShopInterval))
		return true;
	
	if (!m_ADShopTraderID){
		jsonxx::Object cheat;
		cheat << "tabid" << tabid;
		cheat << "goods_id" << goods_id;
		Rainbow::GetICloudProxyPtr()->InfoLog(m_Owner->getUin(), m_Owner->getOWID(), "cheat_ad_shop", cheat);
		if (ProcessCheat(CHEAT_TAG_AD_SHOP, true))
			processKickOrNotify(5, ERR_CODE_CHEAT_AD_SHOP);  // 累计到一定次数直接踢出房间
		return false;
	}
	
	return true;
}

/**
 * @brief 记录ADShop当前触发的商人ID
 * 
 * @param objid 商人ID
 * @return true 成功
 */
bool PlayerCheatData::onInteractADTrader(uint64_t objid)
{
	m_ADShopTraderID = objid;
	ProcessCheat(CHEAT_TAG_AD_SHOP, false);
	return true;
}

/**
 * @brief 检查获取乐谱是否正常
 * 
 * @param item_id 乐谱ID
 * @param num 乐谱数量
 * @return boolon true 正常
 */
bool PlayerCheatData::checkGainMusicPaper(int item_id, int num)
{
	if (!m_Owner || IsSwitchOff(gCheatConfig.SwitchMusicPaperGain))
		return true;
	
	const int interval_limit = 3;
	std::ostringstream log;
	// 2022-02-14 codeby:liusijia 目前只有乐谱使用，暂时屏蔽其他道具非法获取
	if (item_id != 11910 || num != 1)
	{
		log << "item_" << item_id << "_" << num;
		m_MusicPaperTimesRecorder.recordMessage("item_num", log.str());
		if (ProcessCheat(CHEAT_TAG_MUSIC_PAPER_GAIN, true))
			processKickOrNotify(gCheatConfig.SwitchMusicPaperGain, ERR_CODE_CHEAT_MUSIC_PAPER_GAIN);
		return false;
	}
	uint64_t now = MINIW::GetTimeStamp();
	if (now - m_LastGainMusicPaperTime < interval_limit)
	{
		log << now - m_LastGainMusicPaperTime;
		m_MusicPaperTimesRecorder.recordMessage("interval", log.str());
		if (ProcessCheat(CHEAT_TAG_MUSIC_PAPER_GAIN, true))
			processKickOrNotify(gCheatConfig.SwitchMusicPaperGain, ERR_CODE_CHEAT_MUSIC_PAPER_GAIN);
		return false;
	}
	m_LastGainMusicPaperTime = now;

	return true;
}

PlayerCheatData::GunStatus& PlayerCheatData::getGunStatus(unsigned toolid)
{
	auto iter = m_GunStatus.find(toolid);
	if (iter == m_GunStatus.end())
	{
		int reload_fix = 0;
		if (toolid == 15000)
			reload_fix = -400;

		unsigned shoot_interval = m_Owner->getGunLogical()->getGunUse(toolid) * gCheatConfig.SwitchGunShootRatio / 100;
		unsigned reload_interval = m_Owner->getGunLogical()->getReloadTime();
		reload_interval = (reload_interval + reload_fix) * gCheatConfig.SwitchGunReloadRatio / 100;
		m_GunStatus.emplace(toolid, GunStatus{shoot_interval, reload_interval, 0});
		return m_GunStatus.at(toolid);
	}
	return iter->second;
}

/**
 * @brief 检查射击间隔是否正常
 * @note: 射击间隔: 指枪械表中定义的枪械装弹需要消耗的时间
 * 
 * 
 * @param client_time 客户端上报的system tick, 一个相对时间, 单位ms
 * @return true 正常
 * @return false 异常
 */
bool PlayerCheatData::checkGunShoot(int64_t client_time)
{
	if (!m_Owner || IsSwitchOff(gCheatConfig.SwitchGunCheck))
		return true;
	
	int toolid = m_Owner->getCurToolID();
	const GunDef *def = GetDefManagerProxy()->getGunDef(toolid);
	if (!def)
		return false;
	bool is_cheat = false;
	GunStatus &gun_status = getGunStatus(toolid);
	if (client_time - gun_status.m_LastShootTime < gun_status.m_ShootInterval)
	{// 射击间隔低于设定
		is_cheat = true;
	}
	if (is_cheat)
	{
		if(ProcessCheat(CHEAT_TAG_SHOOT, true))
		{
			processKickOrNotify(gCheatConfig.SwitchGunCheck, ERR_CODE_CHEAT_SHOOT);
		}

		jsonxx::Object cheat;
		cheat << "system_tick" << client_time;
		cheat << "interval" << client_time - gun_status.m_LastShootTime;
		cheat << "limit" << gun_status.m_ShootInterval;
		m_GunTimesRecorder.recordMessage("shoot", cheat);

		return false;
	}

	gun_status.m_LastShootTime = client_time;
	m_LastGunShootTime = client_time;

	return true;
}

/**
 * @brief 检查射击装弹间隔是否正常
 * @note: 装弹时间: 指枪械表中定义的枪械装弹需要消耗的时间
 * 
 * @param client_time client_time 客户端上报的system tick, 一个相对时间, 单位ms
 * @return true 正常
 * @return false 异常
 */
bool PlayerCheatData::checkGunReload(int64_t client_time)
{
	if (!m_Owner || IsSwitchOff(gCheatConfig.SwitchGunCheck) || client_time == 0)
		return true;

	int toolid = m_Owner->getCurToolID();
	const GunDef *def = GetDefManagerProxy()->getGunDef(toolid);
	if (!def)
		return false;
	
	bool is_cheat = false;
	GunStatus &gun_status = getGunStatus(toolid);
	if (client_time - m_LastGunShootTime < gun_status.m_ReloadInterval)
	{// 换弹夹时间距离上次射击间隔低于设定
		is_cheat = true;
	}
	if (is_cheat)
	{
		if(ProcessCheat(CHEAT_TAG_SHOOT, true))
		{
			processKickOrNotify(gCheatConfig.SwitchGunCheck, ERR_CODE_CHEAT_SHOOT);
		}
		jsonxx::Object cheat;
		cheat << "system_tick" << client_time;
		cheat << "interval" << client_time - m_LastGunShootTime;
		cheat << "limit" << gun_status.m_ReloadInterval;
		m_GunTimesRecorder.recordMessage("reload", cheat);
		return false;
	}

	return true;
}

void PlayerCheatData::onPlayerDie()
{
	if (!m_Owner || IsSwitchOff(gCheatConfig.SwitchReviveCheck))
		return ;
	
	m_LastDeadTime = MINIW::GetTimeStampMS();
}

/**
 * @brief 检查复活请求是否正常
 * 
 * @note World::getReviveMode 返回值: 0-手动复活, 1-自动复活, 
 * @param revive_type 复活类型, 由客户端上传, 类型定义在客户端团队
 * @return true 正常
 * @return false 
 */
bool PlayerCheatData::checkRevive(int revive_type)
{
	if (!m_Owner || IsSwitchOff(gCheatConfig.SwitchReviveCheck))
		return true;

	World *world = m_Owner->getWorld();
	if (!world)
		return false;
	
	REVIVE_CHEAT_TYPE cheat_type = REVIVE_CHEAT_TYPE::RCT_NONE;
	float revive_delay = 0.0f;  // 死亡后多久可以复活
	int mode = world->getReviveMode(revive_delay);  // 复活模式 0-手动复活 1-自动复活 
	int64_t now = MINIW::GetTimeStampMS();
	int valid_interval = (int)((revive_delay - gCheatConfig.ReviveFix) * 1000);
	if (valid_interval > 0 && now - m_LastDeadTime < valid_interval)
	{ // 复活时间小于配置
		cheat_type = RCT_INTERVAL_CHEAT;
	}
	else
	{
		if (mode == 1)
		{ // 自动复活模式, 支持 revive_type 0
			if (revive_type != 0)
			{
				cheat_type = RCT_INVALID_TYPE;
			}
		}
		else if (mode == 0)
		{ // 手动复活模式, 支持 revive_type 0和1(1-使用星星在原地复活)
			switch (revive_type)
			{
			case 2:
			case 105:
			{ // 广告复活方式 2 105
				do
				{
					if (m_Owner->isPCControl())
					{  // PC不支持广告
						cheat_type = RCT_AD_PC;
						break;
					}
					if (now - m_LastDeadTime < (5 - gCheatConfig.ReviveFix) * 1000)  // 最短的广告是5s
					{
						cheat_type = RCT_AD_INTERVAL;
						break;
					}
					else
					{
						jsonxx::Object ad_revive_record;
						ad_revive_record << "revive_type" << revive_type;
						ad_revive_record << "revive_mode" << (int)mode;
						ad_revive_record << "revive_delay" << (int)revive_delay;
						ad_revive_record << "dead_time" << m_LastDeadTime;
						Rainbow::GetICloudProxyPtr()->InfoLog(m_Owner->getUin(), 0, "ad_revive", ad_revive_record);
					}

				} while (false);
				break;
			}
			case 0:
			case 1:
				break;
			default:
				cheat_type = RCT_INVALID_TYPE;
			}
		}
		else
		{  // 目前只支持0和1
			cheat_type = RCT_INVALID_TYPE;
		}
	}
	if (cheat_type != RCT_NONE)
	{
		jsonxx::Object cheat;
		cheat << "cheat_type" << (int)cheat_type;
		cheat << "revive_type" << revive_type;
		cheat << "revive_mode" << (int)mode;
		cheat << "revive_delay" << (int)revive_delay;
		cheat << "dead_time" << m_LastDeadTime;
		m_ReviveTimesRecorder.recordMessage("revive", cheat);
		processKickOrNotify(gCheatConfig.SwitchReviveCheck, ERR_CODE_CHEAT_REVIVE);
		return false;
	}
	return true;
}

/**
 * @brief 检查召唤坐骑请求是否正常
 * 
 * @param horse_id 坐骑ID, 由客户端上传
 * @return true 正常
 * @return false 异常
 */
bool PlayerCheatData::checkSummonHorse(int horse_id)
{
	if (!m_Owner || IsSwitchOff(gCheatConfig.SwitchHorseCheck))
		return true;
	
	if (!horse_id)
		return true;
	
	if (!g_WorldMgr)
	{
		SLOG(INFO) << "checkSummonHorse no worldmgr uin=" << m_Owner->getUin() << ";horse_id=" << horse_id;
		return false;
	}
	
	if (!g_WorldMgr->getPlayerPermit(ENABLE_CALLRINDER, m_Owner->getTeam()))
	{
		SLOG(INFO) << "checkSummonHorse rule forbidden uin=" << m_Owner->getUin() << ";horse_id=" << horse_id;
		return false;
	}

	if (!m_AccountHorseList.count(horse_id))
	{
		jsonxx::Object cheat;
		cheat << "horse_id" << horse_id;
		jsonxx::Array horses;
		for (auto it = m_AccountHorseList.begin(); it != m_AccountHorseList.end(); ++it)
		{
			horses << *it;
		}
		cheat << "valid_horses" << horses;
        Rainbow::GetICloudProxyPtr()->InfoLog(m_Owner->getUin(), 0, "cheat_interact_invalid", cheat);
		processKickOrNotify(gCheatConfig.SwitchHorseCheck, ERR_CODE_CHEAT_HORSE);
        return false;
	}
	return true;
}

/**
 * @brief 检查召唤宠物请求是否正常
 * 
 * @param pet_id 宠物ID, 由客户端上传, 0表示取消召唤
 * @return true 正常
 * @return false 异常
 */
bool PlayerCheatData::checkSummonPet(int pet_id)
{
	if (!m_Owner || IsSwitchOff(gCheatConfig.SwitchPetCheck))
		return true;
	
	if (pet_id == 0)
		return true;
	
	if (!g_WorldMgr)
	{
		SLOG(INFO) << "checkSummonPet no worldmgr uin=" << m_Owner->getUin() << ";pet_id=" << pet_id;
		return false;
	}
	
	if (!g_WorldMgr->getPlayerPermit(ENABLE_CALLRINDER, m_Owner->getTeam()))
	{
		SLOG(INFO) << "checkSummonPet rule forbidden uin=" << m_Owner->getUin() << ";pet_id=" << pet_id;
		return false;
	}
	return true;
}

inline jsonxx::Array WCoord2Array(const WCoord& pos)
{
	jsonxx::Array arr;
	arr << pos.x << pos.y << pos.z;
	return arr;
}

/**
 * @brief 检测与actor的交互
 * 
 * @param target 目标对象
 * @param interact_type 交互类型  (0 - attack, 1 - check interact then attack, especially for mobile 2 - only interact)
 * @return true 正常
 * @return false 异常
 */
bool PlayerCheatData::checkInteractTarget(ClientActor* target, int interact_type)
{
	if (!m_Owner || IsSwitchOff(gCheatConfig.SwitchInteractCheck) || gCheatConfig.InteractRangeLimit == 0)
		return true;
	if (!target)
		return true;
	if (_isSingleOrGodMode())
		return true;
	
	bool isOuter = false;
	ScriptVM::game()->callFunction("IsUserOuterChecker", "i>b", m_Owner->getUin(), &isOuter);
	if (isOuter)
		return true;
	
	// distanceTo 可能会超过float的表达上限
	float range = (m_Owner->getPosition() - target->getPosition()).length();
	float radius = target->getLocoMotion()->m_HitBoundWidth / 2;
	if (range > gCheatConfig.InteractRangeLimit + radius)
	{
		jsonxx::Object cheat;
		cheat << "range" << range;
		cheat << "radius" << radius;
		cheat << "src" << WCoord2Array(m_Owner->getPosition());
		cheat << "dest" << WCoord2Array(target->getPosition());
		
        Rainbow::GetICloudProxyPtr()->InfoLog(m_Owner->getUin(), 0, "cheat_interact_target", cheat);
		processKickOrNotify(gCheatConfig.SwitchInteractCheck, ERR_CODE_CHEAT_INTERACT_ACTOR);
		return false;
	}
	return true;
}

/**
 * @brief 检测角色转向角度是否异常
 * 
 * @param yaw 水平角度
 * @param pitch 垂直角度
 * @return true 正常
 */
bool PlayerCheatData::checkRotation(float yaw, float pitch)
{
	if (!m_Owner || IsSwitchOff(gCheatConfig.SwitchRotation))
		return true;
	
	if (pitch > 180)
		pitch -= 360.f;
	if (ProcessCheat(CHEAT_TAG_HEARTBEAT, pitch > 90 || pitch < -90))
	{
		jsonxx::Object cheat;
		cheat << "yaw" << yaw;
		cheat << "pitch" << pitch;
		
        Rainbow::GetICloudProxyPtr()->InfoLog(m_Owner->getUin(), 0, "cheat_rotation", cheat);
		processKickOrNotify(gCheatConfig.SwitchRotation, ERR_CODE_CHEAT_ROTATION);
		return false;
	}
	return true;
}

void PlayerCheatData::onFlyModeChange(bool flag)
{
	// SLOG(INFO) << "onFlyModeChange uin=" << m_Owner->getUin() << " mode=" << flag;
	m_FlyModeChange.checkFlag(flag ? 1 : 0, MINIW::GetTimeStampMS());
	if (flag)
		m_FlownInThisGame = true;
}


#define float_equal(a, b) (abs(a-b) < 0.0001)
/**
 * 临时方案, 判断角色是否站在有问题的半砖上
*/
bool PlayerCheatData::onErrorHalfBlock(const WCoord& pos)
{
	if (!m_Owner || IsSwitchOff(gCheatConfig.SwitchCheckWrongHalfBlock))
		return false;
	if (!m_Owner)
		return false;
	auto pworld = m_Owner->getWorld();
	if (!pworld)
		return false;
	CollideAABB box;
	m_Owner->getCollideBox(box);
	WCoord minpos(pos), maxpos(pos + box.dim);
	
	WCoord grid1 = CoordDivBlock(pos);
	grid1.y -= 1;
	WCoord grid2 = CoordDivBlock(WCoord(maxpos.x, minpos.y, maxpos.z));
	grid2.y = grid1.y + 1;
	
	for (int z = grid1.z; z <= grid2.z; z++)
	{
		for (int y = grid1.y; y <= grid2.y; y++)
		{
			for (int x = grid1.x; x <= grid2.x; x++)
			{
				WCoord blockpos(x, y, z);
				Chunk *pchunk = pworld->getChunk(blockpos);
				if (pchunk && (unsigned)y < CHUNK_BLOCK_Y)
				{
					WCoord grid = blockpos - pchunk->m_Origin;
					auto block = pchunk->getBlock(grid);
					int blockid = block.getResID();
					if (blockid > 0)
					{
						BlockMaterial *blockmtl = g_BlockMtlMgr.getMaterial(blockid);
						SolidBlockMaterial *solid = dynamic_cast<SolidBlockMaterial*> (blockmtl);
						if (solid && solid->defBlockMove())
						{
							if ( float_equal(solid->getBlockHeight(block.getData()), 0.49))
								return true;
						}
					}
				}
			}
		}
	}
	return false;
}

/**
 * @brief 按客机上报的事件处理
 * @param event 事件
 * @detail 客机上报的json
*/
void PlayerCheatData::OnClientAction(const std::string &event, const jsonxx::Object &detail)
{
	jsonxx::Object log(detail);
	log << "key" << event;
	Rainbow::GetICloudProxyPtr()->InfoLog(m_Owner->getUin(), 0, "cheat_client_action", log);

	auto it = gCheatConfig.ClientLogAction.find(event);
	if (it == gCheatConfig.ClientLogAction.end())
		return;
	processKickOrNotify(it->second.action, it->second.error_code);
}

/**
 * @brief 判断客户端上传的tick是否合法
 * @note 此函数是基于使用tick的协议有序到达的假设
 */
bool PlayerCheatData::checkClientTick(long long tick)
{
	// +5 是允许客户端协议顺序错乱的时间范围
	bool ret = (tick == 0) || tick + 5 >= m_LastClientTick;

	if (tick > m_LastClientTick)
		m_LastClientTick = tick;
	return ret;
}

void PlayerCheatData::recordClientTick(long long tick)
{
	if (tick > m_LastClientTick)
		m_LastClientTick = tick;
}

// --- TimesRecorder start ---
TimesRecorder::~TimesRecorder()
{
	if (m_LogRelease)
	{
		// 玩家退出时写入日志
		for(auto iter=m_MessageTimes.begin(); iter!=m_MessageTimes.end(); ++iter)
		{
			_outLog(iter->first, iter->second, "end");
		}
	}
	m_Owner = NULL;
}

void TimesRecorder::recordMessage(const std::string &key, const std::string &detail, unsigned times, unsigned count)
{
	auto iter = m_MessageTimes.find(key);
	if (iter == m_MessageTimes.end())
	{
		auto p = m_MessageTimes.emplace(key, _st_times_record{MINIW::GetTimeStamp(), times, 0, count});
		if (!p.second)
			return;
		iter = p.first;
	}
	else
	{
		iter->second.times += times;
		iter->second.count += count;
	}

	if (iter->second.times >= iter->second.last_output_times + m_RecordMassageTimes)
	{// 输出日志需要的数量不断累加, 100->(+200)300->(+300)600->(+400)1000...
		m_RecordMassageTimes += 100;
		iter->second.last_output_times = iter->second.times;
	}
	else if (iter->second.times > 2)
		return;

	_outLog(iter->first, iter->second, detail);
}

void TimesRecorder::recordMessage(const std::string &key, const jsonxx::Object &detail, unsigned times, unsigned count)
{
	auto iter = m_MessageTimes.find(key);
	if (iter == m_MessageTimes.end())
	{
		auto p = m_MessageTimes.emplace(key, _st_times_record{MINIW::GetTimeStamp(), times, 0, count});
		if (!p.second)
			return;
		iter = p.first;
	}
	else
	{
		iter->second.times += times;
		iter->second.count += count;
	}

	if (iter->second.times >= iter->second.last_output_times + m_RecordMassageTimes)
	{// 输出日志需要的数量不断累加, 100->(+200)300->(+300)600->(+400)1000...
		m_RecordMassageTimes += 100;
		iter->second.last_output_times = iter->second.times;
	}
	else if (iter->second.times > 2)
		return;

	_outLog(iter->first, iter->second, detail);
}
inline void TimesRecorder::_outLog(const std::string& msg, const _st_times_record &record, const std::string& cur_detail)
{
	static jsonxx::Object _st;
	jsonxx::Object cheat;
	cheat << "key" << msg;
	cheat << "times" << jsonxx::Number(record.times);
	cheat << "count" << jsonxx::Number(record.count);
	cheat << "start_time" << jsonxx::Number(record.start_time);
	if (!cur_detail.empty())
		cheat << "cur_detail" << cur_detail;
	#ifdef IWORLD_SERVER_BUILD
		Rainbow::GetICloudProxyPtr()->InfoLog(_getUin(_st), 0, m_Event, cheat);
	#else
		LOG_INFO("TimesRecorder uin=%d event=%s log=%s", _getUin(_st), m_Event.c_str(), cheat.json_nospace().c_str());
	#endif
}
inline void TimesRecorder::_outLog(const std::string& msg, const _st_times_record &record, const jsonxx::Object &cur_detail)
{
	jsonxx::Object cheat;
	cheat << "key" << msg;
	cheat << "times" << jsonxx::Number(record.times);
	cheat << "count" << jsonxx::Number(record.count);
	cheat << "start_time" << jsonxx::Number(record.start_time);
	cheat.import(cur_detail);
	
	#ifdef IWORLD_SERVER_BUILD
		Rainbow::GetICloudProxyPtr()->InfoLog(_getUin(cur_detail), 0, m_Event, cheat);
	#else
		LOG_INFO("TimesRecorder uin=%d event=%s log=%s", _getUin(cur_detail), m_Event.c_str(), cheat.json_nospace().c_str());
	#endif
}
inline int TimesRecorder::_getUin(const jsonxx::Object &cur_detail)
{
	if (!m_OwnerUin)
		m_OwnerUin = m_Owner ? m_Owner->getUin(): -1;
	if (cur_detail.has<jsonxx::Number>("uin"))
		return cur_detail.get<jsonxx::Number>("uin");
	return m_OwnerUin;
}
// --- TimesRecorder end ---

//  -- ChatLimiter start --
ChatLimiter::ChatLimiter():
	m_DenyRepeatCount(0), m_MuteRepeatCount(0), m_MuteTimeRepeat(0), m_Active(false)
{
	
}
bool ChatLimiter::loadConfig(const jsonxx::Object &js)
{
	m_Active = true;
	GetICloudProxyPtr()->SimpleSLOG("%s load period", __FUNCTION__);
	loadConfigFromJsonArray(js.get<jsonxx::Array>("periods"), m_Periods, "ChatLimiter period");
	if (js.has<jsonxx::Number>("repeat_times_deny"))
		m_DenyRepeatCount = js.get<jsonxx::Number>("repeat_times_deny");  // 重复发言多少次触发拒绝
	if (js.has<jsonxx::Number>("repeat_times_mute") && js.has<jsonxx::Number>("repeat_mute_seconds"))
	{
		m_MuteRepeatCount = js.get<jsonxx::Number>("repeat_times_mute");  // 重复发言多少次触发禁言
		m_MuteTimeRepeat = js.get<jsonxx::Number>("repeat_mute_seconds");  // 重复发言后触发的禁言时间
	}
	return true;
}
/**
 * @brief 检测角色是否被禁言
 */
bool ChatLimiter::isMute(int uin)
{
	if (!m_Active)
		return false;

	if (m_RoleStatus.count(uin) == 0)
		return false;
	
	uint64_t now = MINIW::GetTimeStamp();
	if (!m_RoleStatus[uin]->isMuteEnd(now))
	{
		// 正在禁言中
		return true;
	}
	return false;
}

/**
 * @brief 检测角色聊天是否超限
 * @param uin: 角色id
 * @param content: 角色消息
 * @return boolean true-超限  false-未超限
 */
bool ChatLimiter::checkChat(int uin, const std::string &content, const std::string &extend)
{
	if (!m_Active)
		return false;
	uint64_t now = MINIW::GetTimeStamp();
	if (m_RoleStatus.count(uin) == 0)
	{
		m_RoleStatus.emplace(uin, new RoleStatus(&m_Periods));
	}
	if (!m_RoleStatus[uin]->isMuteEnd(now))
	{
		// 正在禁言中
		return true;
	}
	
	char realContent[2048];
	MINIW::ScriptVM::game()->callFunction("CS_getPureContent", "ss>s", content.c_str(), extend.c_str(), realContent);

	unsigned interval, times, flags;
	if (m_RoleStatus[uin]->recorder.checkLimit(now, &times, &interval, &flags))
	{
		jsonxx::Object log;
		log << "interval" << interval;
		log << "times" << times;
		log << "content" << realContent;
		if (flags)  // flags配置触发此规则后的禁言时间
		{
			m_RoleStatus[uin]->mute_end = now + flags;
			log << "flags" << flags;
			log << "mute_end" << m_RoleStatus[uin]->mute_end;
		}
		GetICloudProxyPtr()->InfoLog(uin, 0, "cheat_chat_limit", log);
		return true;
	}
	m_RoleStatus[uin]->recorder.recordTime(now);

	if (m_RoleStatus[uin]->last_content == realContent)

	{
		++ m_RoleStatus[uin]->repeat_times;
		if (m_MuteTimeRepeat && m_MuteRepeatCount && (m_RoleStatus[uin]->repeat_times >= m_MuteRepeatCount))
		{// 配置了重复禁言时间则设置禁言
			m_RoleStatus[uin]->mute_end = now + m_MuteTimeRepeat;
			
			jsonxx::Object log;
			log << "repeat" << m_RoleStatus[uin]->repeat_times;
			log << "content" << realContent;
			log << "mute_end" << m_RoleStatus[uin]->mute_end;
			GetICloudProxyPtr()->InfoLog(uin, 0, "cheat_chat_repeat", log);
			return true;
		}
		else if (m_DenyRepeatCount && m_RoleStatus[uin]->repeat_times >= m_DenyRepeatCount)
		{
			return true;
		}
	}
	else
	{
		m_RoleStatus[uin]->repeat_times = 0;
		m_RoleStatus[uin]->last_content = content;
	}

	return false;
}
IMPLEMENT_LAZY_SINGLETON(ChatLimiter)
// -- ChatLimiter end --
