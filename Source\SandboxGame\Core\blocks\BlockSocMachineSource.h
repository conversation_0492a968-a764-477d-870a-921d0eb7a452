
#ifndef __BLOCK_SOC_MECHINE_SOURCE_H__
#define __BLOCK_SOC_MECHINE_SOURCE_H__

#include "BlockMaterial.h"

class BlockSocMachineSource : public ModelBlockMaterial //tolua_exports
{ //tolua_exports
	DECLARE_BLOCKMATERIAL(BlockSocMachineSource)
public:
	BlockSocMachineSource();
	virtual ~BlockSocMachineSource();
	virtual void init(int resid) override;
	virtual bool onTrigger(World* pworld, const WCoord& blockpos, DirectionType face, IClientPlayer* player, const Rainbow::Vector3f& colpoint = Rainbow::Vector3f(0, 0, 0)) override;
	virtual void createCollideData(CollisionDetect* coldetect, World* pworld, const WCoord& blockpos) override;
	virtual WorldContainer* createContainer(World* pworld, const WCoord& blockpos) override;
	virtual void createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh) override;
	virtual void onBlockPlacedBy(World* pworld, const WCoord& blockpos, IClientPlayer* player) override;

}; //tolua_exports


#endif