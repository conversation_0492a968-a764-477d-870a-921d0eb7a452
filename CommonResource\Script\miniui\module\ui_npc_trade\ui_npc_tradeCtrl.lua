--声明
local ui_npc_tradeCtrl = Class("ui_npc_tradeCtrl",ClassList["UIBaseCtrl"])

--创建
function ui_npc_tradeCtrl:Create(param)
	return ClassList["ui_npc_tradeCtrl"].new(param)
end

--初始化
function ui_npc_tradeCtrl:Init(param)
	self.super:Init(param)

end

--启动
function ui_npc_tradeCtrl:Start()
	self.super:Start()
	self.view:InitView()

end

--刷新
function ui_npc_tradeCtrl:Refresh()
	self.super:Refresh()

end

--隐藏
function ui_npc_tradeCtrl:Reset()
	self.super:Reset()

end

--关闭
function ui_npc_tradeCtrl:Remove()
	self.super:Reset()

end

--消息处理
function ui_npc_tradeCtrl:FGUIHandleEvent(eventName)

end

