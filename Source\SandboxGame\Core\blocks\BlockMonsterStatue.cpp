#include "BlockMonsterStatue.h"
#include "world.h"
#include "DefManagerProxy.h"
#include "EffectManager.h"
#include "basesection.h"
#include "SectionMesh.h"
#include "BlockGeom.h"
#include "ShareRenderMaterial.h"
#include "BlockMaterialMgr.h"
#include "Math/Vector3f.h"
#include "container_modelbase.h"

IMPLEMENT_BLOCKMATERIAL(BlockMonsterStatueSpecial)
BLOCKMONSTERSTATUE_SPECIAL_IMPLEMENT(1, 1, 1, GOLD);
BLOCKMONSTERSTATUE_SPECIAL_IMPLEMENT(1, 1, 1, SILIVER);
BLOCKMONSTERSTATUE_SPECIAL_IMPLEMENT(1, 1, 1, STONE);
BLOCKMONSTERSTATUE_SPECIAL_IMPLEMENT(1, 2, 1, GOLD);
BLOCKMONSTERSTATUE_SPECIAL_IMPLEMENT(1, 2, 1, SILIVER);
BLOCKMONSTERSTATUE_SPECIAL_IMPLEMENT(1, 2, 1, STONE);
BLOCKMONSTERSTATUE_SPECIAL_IMPLEMENT(2, 2, 2, GOLD);
BLOCKMONSTERSTATUE_SPECIAL_IMPLEMENT(2, 2, 2, SILIVER);
BLOCKMONSTERSTATUE_SPECIAL_IMPLEMENT(2, 2, 2, STONE);
BLOCKMONSTERSTATUE_SPECIAL_IMPLEMENT(3, 3, 3, GOLD);
BLOCKMONSTERSTATUE_SPECIAL_IMPLEMENT(3, 3, 3, SILIVER);
BLOCKMONSTERSTATUE_SPECIAL_IMPLEMENT(3, 3, 3, STONE);
BLOCKMONSTERSTATUE_SPECIAL_IMPLEMENT(3, 3, 3, BOSS);
IMPLEMENT_BLOCKMATERIAL(BlockMonsterStatueBottom)
IMPLEMENT_BLOCKMATERIAL(BlockMonsterStatueLayer)
IMPLEMENT_BLOCKMATERIAL(BlockMonsterStatueFoundation)

//struct CompareLess {
//	bool operator()(const int lhs,
//		const int rhs) const
//	{
//		int n = rhs - lhs;
//		if (n <= 0)
//		{
//			return false;
//		}
//		return false;
//	}
//};

struct MonsterStatueMap
{
	typedef std::vector<BlockMonsterStatueSpecial::StatueInfo> StatueMap;
	void init()
	{
		haveInit = true;
		m_monsterMap.reserve(40);
		m_monsterMap.push_back({ 3101, 2 , 1, 2, 1, 1.0f, Rainbow::Vector3f(0.0f, -1.0f, -0.05f) });
		m_monsterMap.push_back({ 3877, 2 , 1, 2, 1, 1.0f, Rainbow::Vector3f(0, -1.0f, 0) });
		m_monsterMap.push_back({ 3105, 2 , 1, 2, 1, 1.0f, Rainbow::Vector3f(0.0f, -1.0f, -0.05f) });
		m_monsterMap.push_back({ 3102, 2 , 1, 1, 1, 1.0f, Rainbow::Vector3f(0, -1.0f, 0) });
		m_monsterMap.push_back({ 3873, 2 , 1, 2, 1, 1.0f, Rainbow::Vector3f(0, -1.0f, 0) });
		m_monsterMap.push_back({ 3874, 2 , 1, 2, 1, 1.0f, Rainbow::Vector3f(0, -1.0f, 0) });
		m_monsterMap.push_back({ 3875, 2 , 1, 2, 1, 1.0f, Rainbow::Vector3f(0, -1.0f, 0) });
		m_monsterMap.push_back({ 3876, 2 , 1, 2, 1, 1.0f, Rainbow::Vector3f(0, -1.0f, 0) });
		m_monsterMap.push_back({ 3107, 2 , 1, 1, 1, 1.0f, Rainbow::Vector3f(-0.15f, -1.0f, -0.18f) });
		m_monsterMap.push_back({ 3892, 2 , 1, 1, 1, 1.0f, Rainbow::Vector3f(0, -1.0f, 0) });
		m_monsterMap.push_back({ 3821, 2 , 1, 2, 1, 1.0f, Rainbow::Vector3f(-0.1f, -1.55f, 0) });
		m_monsterMap.push_back({ 3220, 2 , 1, 2, 1, 1.0f, Rainbow::Vector3f(0, -1.0f, 0) });
		m_monsterMap.push_back({ 3221, 2 , 1, 2, 1, 1.0f, Rainbow::Vector3f(0, -1.0f, 0) });
		m_monsterMap.push_back({ 3109, 2 , 1, 2, 1, 1.0f, Rainbow::Vector3f(0, -1.0f, 0) });
		m_monsterMap.push_back({ 3824, 2 , 1, 1, 1, 1.0f, Rainbow::Vector3f(0, -1.0f, 0) });
		m_monsterMap.push_back({ 3224, 2 , 1, 2, 1, 1.0f, Rainbow::Vector3f(0, -1.0f, 0) });
		m_monsterMap.push_back({ 3826, 2 , 1, 2, 1, 1.0f, Rainbow::Vector3f(0, -1.0f, 0) });
		m_monsterMap.push_back({ 3130, 2 , 1, 2, 1, 1.0f, Rainbow::Vector3f(0, -1.0f, 0) });
		m_monsterMap.push_back({ 3131, 2 , 1, 2, 1, 1.0f, Rainbow::Vector3f(0, -1.0f, 0) });
		m_monsterMap.push_back({ 3110, 2 , 1, 2, 1, 1.0f, Rainbow::Vector3f(0, -1.0f, 0) });
		m_monsterMap.push_back({ 3111, 2 , 1, 2, 1, 1.0f, Rainbow::Vector3f(0, -1.0f, 0) });
		m_monsterMap.push_back({ 3112, 2 , 1, 2, 1, 1.0f, Rainbow::Vector3f(0, -1.0f, 0) });
		m_monsterMap.push_back({ 3103, 2 , 1, 2, 1, 1.0f, Rainbow::Vector3f(0, -1.0f, 0) });
		m_monsterMap.push_back({ 3915, 2 , 1, 2, 1, 1.0f, Rainbow::Vector3f(-0.2f, -1.0f, 0) });
		m_monsterMap.push_back({ 3135, 2 , 1, 2, 1, 1.0f, Rainbow::Vector3f(0, -1.0f, 0) });
		m_monsterMap.push_back({ 3225, 2 , 2, 2, 2, 0.24f, Rainbow::Vector3f(0, -1.0f, 0) });
		m_monsterMap.push_back({ 3226, 2 , 2, 2, 2, 0.2f,Rainbow::Vector3f(0, -1.0f, 0) });
		m_monsterMap.push_back({ 3227, 2 , 2, 2, 2, 0.16f, Rainbow::Vector3f(0, -1.0f, 0) });
		m_monsterMap.push_back({ 3878, 2 , 3, 3, 3});
		m_monsterMap.push_back({ 3502, 2 , 3, 3, 3});
		m_monsterMap.push_back({ 3514, 2 , 3, 3, 3});
		m_monsterMap.push_back({ 3515, 2 , 3, 3, 3});
		m_monsterMap.push_back({ 3517, 2 , 3, 3, 3});
	}
	BlockMonsterStatueSpecial::StatueInfo find(int blockid)
	{
		int index = -1;
		if (blockid >= 200096 && blockid <= 200100)
		{
			index = blockid - 200096 + 28;
		}
		else
		{
			index = (blockid - 200012) / 3;
		}
		if (index < 0 || index > m_monsterMap.size())
		{
			return BlockMonsterStatueSpecial::StatueInfo(-1, -1);
		}
		return m_monsterMap[index];
	}
	int getMtlIndex(int blockid)
	{
		if (blockid >= 200096 && blockid <= 200100)
		{
			return 3;
		}
		return (blockid - 200012) % 3;
	}
	bool haveInit = false;
	StatueMap m_monsterMap;
};

MonsterStatueMap monsterMap;

int monsterStatueGetMonsterId(int blockid)
{
	auto info = monsterMap.find(blockid);
	if (info.isValid())
	{
		return info.monsterid;
	}
	return -1;
}

BlockMonsterStatueSpecial::BlockMonsterStatueSpecial():m_paramIntensity(1.0f), m_paramProportion(1.0f)
{
	if (!monsterMap.haveInit)
	{
		monsterMap.init();
	}
	//memset(m_mtls, 0, sizeof(int) * MONSTER_STATUE_MAX);
}

//BlockMonsterStatueSpecial::~BlockMonsterStatueSpecial()
//{
//	//for (int i = 0; i < MONSTER_STATUE_MAX; i++)
//	//{
//	//	ENG_RELEASE(m_mtls[i]);
//	//}
//}

bool BlockMonsterStatueSpecial::hasContainer()
{
	int index = monsterMap.getMtlIndex(m_BlockResID);
	if (index == 3)
	{
		return false;
	}

	return true;
}

void BlockMonsterStatueSpecial::init(int resid)
{
	BlockPileSpecial::init(resid);
	
	if (hasContainer())
	{
		return;
	}

	auto def = GetBlockDef();
	if (!def)
	{
		return;
	}
	int gettextype = GETTEX_WITHDEFAULT;
	int num = m_info.geom;
	auto geom = getGeom();
	if (!geom)
	{
		return;
	}
	/*int num = geom->getMeshCount();
	if (num > MONSTER_STATUE_MAX)
	{
		assert(0);
		return;
	}*/
	std::string prefix = def->Texture1.c_str();
	prefix += "_base";
	char newName[256];
	char mtlName[256];
	int index = monsterMap.getMtlIndex(resid);
	float item_proportion;
	float item_intensity = 2;
	Rainbow::ColorRGBAf color(0.99f, 1.0f, 0.71f, 1.0f);
	Rainbow::ColorRGBAf itemColor(0.99f, 1.0f, 0.71f, 1.0f);
	//for (int i = 0; i < num; i++)
	{
		if (index == 0)
		{
			sprintf(newName, "matcap_wood");
			item_intensity = m_paramIntensity;
			item_proportion = m_paramProportion;
			color.r = 0.05f;
			color.g = 0.1f;
			color.b = 0.1f;
			itemColor.r = 0.f;
			itemColor.g = 0.05f;
			itemColor.b = 0.03f;
		}
		else
		{
			//sprintf(newName, "statue_img");
			item_proportion = 2;
			if (index == 1)
			{
				item_intensity = m_paramIntensity;
				item_proportion = m_paramProportion;
				color.r = 1.f;
				color.g = 1.f;
				color.b = 1.f;
				itemColor.r = 0.5f;
				itemColor.g = 0.5f;
				itemColor.b = 0.5f;
				sprintf(newName, "default");
			}
			else if (index == 2)
			{
				item_intensity = 1;
				item_proportion = 2;
				color.r = 1.f;
				color.g = 1.f;
				color.b = 0.f;
				itemColor.r = 1.f;
				itemColor.g = 1.f;
				itemColor.b = 0.f;
				sprintf(newName, "default");
			}
			else if (index == 3)
			{
				color.r = 0.4f;
				color.g = 0.78f;
				color.b = 1.f;
				itemColor = color;
				item_intensity = 1;
				item_proportion = 2;
				sprintf(newName, "%s", def->Texture1.c_str());
			}
		}
		sprintf(mtlName, "%s_base_%s", def->Texture1.c_str(), m_append.c_str());
		auto mtl = g_BlockMtlMgr.createRenderMaterial(newName, GetBlockDef(), gettextype, getDrawType(), getMipmapMethod(), mtlName);
		if (mtl)
		{
			mtl->getMaterial();
			mtl->getItemMaterial();
			mtl->setMatcapMonsterStatusParam(m_paramIntensity, m_paramProportion, item_intensity, item_proportion, m_paramMaterialName.c_str(), color, itemColor);
			int index = getRenderMtlMgr().addMtl(mtl);
			ENG_RELEASE(mtl);
			m_defaultMtlIndex = index;
		}
	}
}

void BlockMonsterStatueSpecial::initDefaultMtl()
{
	if (hasContainer())
	{
		ModelBlockMaterial::initDefaultMtl();
		return;
	}

	if (m_defaultMtlIndex != UINT_MAX)
		return;
	auto mtl = g_BlockMtlMgr.createRenderMaterial(GetBlockDef()->Texture1.c_str(), GetBlockDef(), GETTEX_WITHDEFAULT, getDrawType(), getMipmapMethod());
	m_defaultMtlIndex = getRenderMtlMgr().addMtl(mtl);
	ENG_RELEASE(mtl);
}

//bool BlockMonsterStatueSpecial::onTrigger(World* pworld, const WCoord& blockpos, DirectionType face, IClientPlayer* player, const Rainbow::Vector3f& colpoint)
//{
//	if (!pworld)
//	{
//		return false;
//	}
//	if (pworld->isRemoteMode())
//	{
//		return true;
//	}
//	if (!pworld->getEffectMgr())
//	{
//		return false;
//	}
//	/*if (!m_info.isValid())
//	{
//		return false;
//	}*/
//	/*auto def = GetDefManagerProxy()->getMonsterDef(m_info.monsterid);
//	if (!def)
//	{
//		return false;
//	}
//	pworld->getEffectMgr()->playSound(BlockCenterCoord(blockpos), def->SaySound.c_str(), 0.8, 0, 3, -1);*/
//
//	return false;
//}


void BlockMonsterStatueSpecial::createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh)
{
	if (hasContainer())
	{
		return;
	}

	//if (!isSpecialBlock())
	//{
	//	return;
	//}
	BlockGeomTemplate* geom = getGeom(data.m_LODLevel);
	if (geom == NULL) return;
	//if (!m_info.isValid())
	//{
	//	return;
	//}
	auto psection = data.m_SharedSectionData;
	Rainbow::ColorRGBAf verts_light[1] = { Rainbow::ColorRGBAf::black };
	psection->getBlockVertexLight(blockpos, verts_light);
	//RenderBlockMaterial* pmtl = getGeomMtl(psection, blockpos, data.m_World);
	//这里不能判空，会影响云服
	/*if (pmtl == nullptr)
	{
		return;
	}*/
	BlockGeomMeshInfo meshinfo;
	//SectionSubMesh* psubmesh = poutmesh->getSubMesh(pmtl);
	Block pblock = psection->getBlock(blockpos);
	int placeDir = pblock.getData();
	int ngeom = geom->getMeshCount();
	if (ngeom > MONSTER_STATUE_MAX)
	{
		assert(0);
		return;
	}
	//由于是在左下角位置 绘制的.需要平移下
	GetGeomDesc desc;
	geom->initGetGeomDesc(desc);
	desc.placedir = ReverseDirection(placeDir);
	desc.blockheight = 1.0f;
	desc.uvdir = 0;
	desc.offsety = 0;
	desc.mirrorx = false;
	desc.mirrory = false;
	desc.mirrorz = false;
	desc.offsetx = (getRangX() - 1) * 0.5f;
	desc.offsetz = (getRangZ() - 1) * 0.5f;
	desc.offsety = -0.5f;
	Rainbow::Matrix4x4f tm = Rainbow::Matrix4x4f::identity;
	float BlockRotateAngle[4] = { Rainbow::kHalfPI, -Rainbow::kHalfPI, 0, Rainbow::kOnePI };
	tm.SetAxisAngle(Rainbow::Vector3f(0, 1.0f, 0), BlockRotateAngle[desc.placedir]);
	Rainbow::Matrix4x4f move;
	move = Rainbow::Matrix4x4f::identity;
	move.SetPosition(Rainbow::Vector3f(desc.offsetx, desc.offsety, desc.offsetz));
	move = tm.Transpose() * move * tm;
	auto position = move.GetPosition();
	desc.offsetx = position.x;
	desc.offsetz = position.z;
	desc.offsety = position.y;
	for (int i = 0; i < ngeom; i++)
	{
		/*	if (!geom)
				break;*/

		int dir = placeDir;
		//int mirrortype = (dirbuf[i] >> 16) & 3;

		//geom->getFaceVerts(meshinfo, i, false, &desc);
		if (!geom->getFaceVerts(meshinfo, i, false, &desc))
		{
			continue;
		}
		RenderBlockMaterial* pmtl = getDefaultMtl();
		if (!pmtl)
		{
			continue;
		}
		//	pmtl->setMatcapMonsterStatusParam(m_paramIntensity, m_paramProportion);
		SectionSubMesh* psubmesh = poutmesh->getSubMesh(pmtl);
		float f = 0.0f;
		if (psubmesh)
		{
			//非空判断
			if (isColorableBlock() && !isUseCustomModel())
			{
				BlockColor bv = getBlockColor(pblock.getData());
				bv.a = 0;
				psubmesh->addGeomBlockLight(meshinfo, &blockpos, verts_light, &bv, pmtl ? pmtl->getUVTile() : &f);
			}
			else
			{
				psubmesh->addGeomBlockLight(meshinfo, &blockpos, verts_light, NULL, pmtl ? pmtl->getUVTile() : &f);
			}
		}
	}
}

SectionMesh* BlockMonsterStatueSpecial::createBlockProtoMesh(int protodata)
{
	if (getGeom() == NULL || hasContainer()) return NULL;
	SectionMesh* pmesh = ENG_NEW(SectionMesh)();

	BlockGeomMeshInfo meshinfo;

	int ngeom = getGeom()->getMeshCount();
	if (ngeom > MONSTER_STATUE_MAX)
	{
		assert(0);
		return pmesh;
	}
	//由于是在左下角位置 绘制的.需要平移下
	GetGeomDesc desc;
	getGeom()->initGetGeomDesc(desc);
	desc.placedir = DIR_NEG_Z;
	desc.blockheight = 1.0f;
	desc.uvdir = 0;
	desc.offsety = 0;
	desc.mirrorx = false;
	desc.mirrory = false;
	desc.mirrorz = false;
	desc.offsetx = 0;
	desc.offsetz = 0;
	desc.offsety = -0.5;
	Rainbow::Matrix4x4f tm = Rainbow::Matrix4x4f::identity;
	float BlockRotateAngle[4] = { Rainbow::kHalfPI, -Rainbow::kHalfPI, 0, Rainbow::kOnePI };
	tm.SetAxisAngle(Rainbow::Vector3f(0, 1.0f, 0), BlockRotateAngle[desc.placedir]);
	Rainbow::Matrix4x4f move;
	move = Rainbow::Matrix4x4f::identity;
	move.SetPosition(Rainbow::Vector3f(desc.offsetx, desc.offsety, desc.offsetz));
	move = tm.Transpose() * move * tm;
	auto position = move.GetPosition();
	desc.offsetx = position.x;
	desc.offsetz = position.z;
	desc.offsety = position.y;
	for (int i = 0; i < ngeom; i++)
	{
		RenderBlockMaterial* pmtl = getDefaultMtl();
		if (!pmtl)
		{
			continue;
		}
		SectionSubMesh* psubmesh = pmesh->getSubMesh(pmtl, true);
		getGeom()->getFaceVerts(meshinfo, i, false, &desc);
		if (isColorableBlock() && !isUseCustomModel())
		{
			BlockColor bv = getBlockColor(protodata);
			bv.a = 0;
			psubmesh->addGeomFaceLight(meshinfo, NULL, s_DefaultFaceVertexLights, &bv);
		}
		else
		{
			psubmesh->addGeomFaceLight(meshinfo, NULL, s_DefaultFaceVertexLights, NULL);
		}
	}

	return pmesh;
}

WorldContainer* BlockMonsterStatueSpecial::createContainer(World* pworld, const WCoord& blockpos)
{
	if (!pworld || !hasContainer())
	{
		return nullptr;
	}

	auto def = GetBlockDef();
	if (!def)
	{
		return nullptr;
	}

	ModleMobContainer* container = SANDBOX_NEW(ModleMobContainer, blockpos);

	int mobId = monsterStatueGetMonsterId(m_BlockResID);
	if (!container || mobId <= -1)
	{
		return nullptr;
	}
	container->setMobInfo(mobId);
	container->setMatType(1);

	char newName[256];
	int index = monsterMap.getMtlIndex(m_BlockResID);
	auto info = monsterMap.find(m_BlockResID);
	float item_intensity = 2;
	Rainbow::ColorRGBAf color(0.99f, 1.0f, 0.71f, 1.0f);
	Rainbow::ColorRGBAf itemColor;
	sprintf(newName, "matcap_%s", m_paramMaterialName.c_str());
	container->setCustomMatcapTex(newName);
	if (index == 0)
	{
		color.r = 0.05f;
		color.g = 0.1f;
		color.b = 0.1f;
		container->setCustomDiffuseTex("matcap_wood.png");
		itemColor.r = 0.f;
		itemColor.g = 0.05f;
		itemColor.b = 0.03f;
		m_paramProportion = 1.7f;
	}
	else if (index == 1)
	{
		color.r = 1.f;
		color.g = 1.f;
		color.b = 1.f;
		container->setCustomDiffuseTex("default");
		itemColor.r = 0.5f;
		itemColor.g = 0.5f;
		itemColor.b = 0.5f;
		m_paramProportion = 1.7f;
	}
	else if (index == 2)
	{
		color.r = 1.f;
		color.g = 1.f;
		color.b = 0.f;
		container->setCustomDiffuseTex("default");
		itemColor.r = 1.f;
		itemColor.g = 1.f;
		itemColor.b = 0.f;
		m_paramProportion = 1.8f;
	}
	else if (index == 3)
	{
		color.r = 0.4f;
		color.g = 0.78f;
		color.b = 1.f;
		sprintf(newName, "%s.png", def->Texture1.c_str());
		container->setCustomDiffuseTex(newName);
		itemColor = color;
	}
	Rainbow::Vector3f offset;
	offset.x = (getRangX() - 1) * 0.5f;
	offset.z = (getRangZ() - 1) * 0.5f;
	// 偏移量需要跟不同朝向计算
	int dir = pworld->getBlockData(blockpos);
	if (dir == DIR_NEG_X)
	{
		offset.x += -info.offset.x;
		offset.z += -info.offset.z;
	}
	else if (dir == DIR_POS_X)
	{
		offset.x += info.offset.x;
		offset.z += info.offset.z;
	}
	else if (dir == DIR_NEG_Z)
	{
		offset.x += info.offset.z;
		offset.z += -info.offset.x;
	}
	else if (dir == DIR_POS_Z)
	{
		offset.x += -info.offset.z;
		offset.z += info.offset.x;
	}
	offset.y = info.offset.y;
	container->setMatIntensity(m_paramIntensity);
	container->setMatProportion(m_paramProportion);

	MonsterStatueDef *pAnimDef = GetDefManagerProxy()->getMonsterStatueDefById(m_BlockResID);
	if (pAnimDef)
	{
		container->setAnimId(pAnimDef->AnimID);
		container->setAnimTick(pAnimDef->AnimTick);
	}
	container->setBodyRotate(dir);
	container->setOffset(offset);
	container->setScale(info.scale);

	return container;
}

const MonsterStatueSpecialMatInfo& BlockMonsterStatueSpecial::getItemMatInfo()
{
	auto def = GetBlockDef();
	if (!def || !hasContainer())
	{
		return m_itemMatInfo;
	}

	char newName[256];
	int index = monsterMap.getMtlIndex(m_BlockResID);
	float item_intensity = 2;
	Rainbow::ColorRGBAf color(0.99f, 1.0f, 0.71f, 1.0f);
	Rainbow::ColorRGBAf itemColor;
	sprintf(newName, "matcap_%s", m_paramMaterialName.c_str());
	m_itemMatInfo.matcapTexName = newName;
	if (index == 0)
	{
		itemColor.r = 0.f;
		itemColor.g = 0.05f;
		itemColor.b = 0.03f;
		m_itemMatInfo.color = itemColor.GetHex();
		m_itemMatInfo.paramIntensity = m_paramIntensity;
		m_itemMatInfo.paramProportion = 1.7f;
		m_itemMatInfo.mainTexName = "matcap_wood.png";
	}
	else if (index == 1)
	{
		itemColor.r = 0.5f;
		itemColor.g = 0.5f;
		itemColor.b = 0.5f;
		m_itemMatInfo.color = itemColor.GetHex();
		m_itemMatInfo.paramIntensity = m_paramIntensity;
		m_itemMatInfo.paramProportion = 1.7f;
		m_itemMatInfo.mainTexName = "default";
	}
	else if (index == 2)
	{
		itemColor.r = 1.f;
		itemColor.g = 1.f;
		itemColor.b = 0.f;
		m_itemMatInfo.color = itemColor.GetHex();
		m_itemMatInfo.paramIntensity = 1.0f;
		m_itemMatInfo.paramProportion = 1.8f;
		m_itemMatInfo.mainTexName = "default";
	}
	else if (index == 3)
	{
		sprintf(newName, "%s.png", def->Texture1.c_str());
		itemColor = color;
		m_itemMatInfo.color = itemColor.GetHex();
		m_itemMatInfo.paramIntensity = 1.0f;
		m_itemMatInfo.paramProportion = 2.0f;
		m_itemMatInfo.mainTexName = newName;
	}
	auto info = monsterMap.find(m_BlockResID);
	m_itemMatInfo.scale = info.scale;

	return m_itemMatInfo;
}

/*******************************************************************BlockMonsterStatueFoundation*****************************************************************************************/
bool BlockMonsterStatueFoundation::onTrigger(World* pworld, const WCoord& blockpos, DirectionType face, IClientPlayer* player, const Rainbow::Vector3f& colpoint)
{
	return false;
}

void BlockMonsterStatueFoundation::createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh)
{
	BlockGeomTemplate* geom = getGeom(data.m_LODLevel);
	if (geom == NULL) return;
	//if (!m_info.isValid())
	//{
	//	return;
	//}
	auto psection = data.m_SharedSectionData;
	if (!psection)
	{
		return;
	}
	Rainbow::ColorRGBAf verts_light[1] = { Rainbow::ColorRGBAf::black };
	psection->getBlockVertexLight(blockpos, verts_light);
	Block pblock = psection->getBlock(blockpos);
	auto blockdata = pblock.getData();
	if (findXNEG(blockdata) || findZNEG(blockdata))
	{
		return;
	}
	Block topBlock = psection->getNeighborBlock(blockpos, WCoord(0, 1, 0));
	if (!pileIsSpecialBlockId(topBlock.getResID()))
	{
		return;
	}
	auto info = monsterMap.find(topBlock.getResID());
	int rangeX = info.rangeX;
	int rangeZ = info.rangeZ;
	int rangeY = info.rangeY;
	//由于是在左下角位置 绘制的.需要平移下
	GetGeomDesc desc;
	geom->initGetGeomDesc(desc);
	desc.placedir = DIR_NEG_Z;
	desc.blockheight = 1.0f;
	desc.uvdir = 0;
	desc.offsety = 0;
	desc.mirrorx = false;
	desc.mirrory = false;
	desc.mirrorz = false;
	desc.offsetx = (rangeX - 1) * 0.5;
	desc.offsetz = (rangeZ - 1) * 0.5;
	Rainbow::Matrix4x4f tm = Rainbow::Matrix4x4f::identity;
	float BlockRotateAngle[4] = { Rainbow::kHalfPI, -Rainbow::kHalfPI, 0, Rainbow::kOnePI };
	tm.SetAxisAngle(Rainbow::Vector3f(0, 1.0f, 0), BlockRotateAngle[desc.placedir]);
	Rainbow::Matrix4x4f move;
	move = Rainbow::Matrix4x4f::identity;
	move.SetPosition(Rainbow::Vector3f(desc.offsetx, 0, desc.offsetz));
	move = tm.Transpose() * move * tm;
	auto position = move.GetPosition();
	desc.offsetx = position.x;
	desc.offsetz = position.z;
	BlockGeomMeshInfo meshinfo;
//	for (int i = 0; i < 1; i++)
	//{
		/*	if (!geom)
				break;*/

		int dir = DIR_NEG_Z;
		//int mirrortype = (dirbuf[i] >> 16) & 3;
		int index = monsterMap.getMtlIndex(topBlock.getResID());
		if (index < 0 || index >= 4)
		{
			return;
		}
		if (!geom->getFaceVerts(meshinfo, index, false, &desc))
		{
			return;
		}
		RenderBlockMaterial* pmtl = getRenderMtlMgr().getMtl(m_mtlsIndex[index]);
		if (!pmtl)
		{
			return;
		}
		//	pmtl->setMatcapMonsterStatusParam(m_paramIntensity, m_paramProportion);
		SectionSubMesh* psubmesh = poutmesh->getSubMesh(pmtl);
		float f = 0.0f;
		if (psubmesh)
		{
			//非空判断
			if (isColorableBlock() && !isUseCustomModel())
			{
				BlockColor bv = getBlockColor(pblock.getData());
				bv.a = 0;
				psubmesh->addGeomBlockLight(meshinfo, &blockpos, verts_light, &bv, pmtl ? pmtl->getUVTile() : &f);
			}
			else
			{
				psubmesh->addGeomBlockLight(meshinfo, &blockpos, verts_light, NULL, pmtl ? pmtl->getUVTile() : &f);
			}
		}
	//}
}

void BlockMonsterStatueFoundation::initDefaultMtl()
{
	{
		auto mtl = g_BlockMtlMgr.createRenderMaterial("pedestal_wood", GetBlockDef(), GETTEX_WITHDEFAULT, getDrawType());
		m_defaultMtlIndex = getRenderMtlMgr().addMtl(mtl);
		m_mtlsIndex[0] = m_defaultMtlIndex;
		OGRE_RELEASE(mtl);
	}
	{
		auto mtl = g_BlockMtlMgr.createRenderMaterial("pedestal_stone", GetBlockDef(), GETTEX_WITHDEFAULT, getDrawType());
		int index = getRenderMtlMgr().addMtl(mtl);
		m_mtlsIndex[1] = index;
		OGRE_RELEASE(mtl);
	}
	{
		auto mtl = g_BlockMtlMgr.createRenderMaterial("pedestal_gold", GetBlockDef(), GETTEX_WITHDEFAULT, getDrawType());
		int index = getRenderMtlMgr().addMtl(mtl);
		m_mtlsIndex[2] = index;
		OGRE_RELEASE(mtl);
	}
	{
		auto mtl = g_BlockMtlMgr.createRenderMaterial("pedestal_boss", GetBlockDef(), GETTEX_WITHDEFAULT, getDrawType());
		int index = getRenderMtlMgr().addMtl(mtl);
		m_mtlsIndex[3] = index;
		OGRE_RELEASE(mtl);
	}
}

void BlockMonsterStatueFoundation::dropBlockAsItem(World* pworld, const WCoord& blockpos, int blockdata, BLOCK_MINE_TYPE droptype, float chance, int uin)
{
	if (!pworld)
	{
		return;
	}

	const BlockDef* prevDef = m_Def;
	Block block = getSpecialBlockOnDestory(pworld, blockpos, blockdata);
	BlockMaterial* pBlockMtl = g_BlockMtlMgr.getMaterial(block.getResID());
	if (pBlockMtl)
	{
		m_Def = pBlockMtl->GetBlockDef();
	}

	// 雕像需要用核心方块的Def生成掉落物
	BlockMaterial::dropBlockAsItem(pworld, blockpos, blockdata, droptype, chance, uin);
	m_Def = prevDef;
}