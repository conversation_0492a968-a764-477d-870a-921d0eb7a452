
#ifndef __BLOCKCROPPER_H__
#define __BLOCKCROPPER_H__

#include "BlockGrayHerbs.h"

class CropperMaterial : public HerbMaterial //tolua_exports
{ //tolua_exports
	//typedef HerbMaterial Super;
	DECLARE_BLOCKMATERIAL(CropperMaterial)
public:
	//tolua_begin
	CropperMaterial();
	virtual ~CropperMaterial();

	virtual void init(int resid) override;
	virtual void createBlockMesh(const BuildSectionMeshData& data, const WCoord &blockpos, SectionMesh *poutmesh) override;
	virtual BlockTexElement *getDestroyTexture(Block pblock, BlockTexDesc &desc) override;
	virtual bool onFertilized(World *pworld, const WCoord &blockpos, int fertiliser);

	virtual void blockTick(World *pworld, const WCoord &blockpos);
	void forceResh(World* pworld, const WCoord& blockpos);

	virtual void dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata, BLOCK_MINE_TYPE droptype/* =BLOCK_MINE_NOTOOL */, float chance/* =1.0f */, int uin = -1);
	virtual bool hasDestroyScore(int blockdata);
	virtual void createCollideData(CollisionDetect *coldetect, World *pworld, const WCoord &blockpos);

	virtual void onBlockAdded(World *pworld, const WCoord &blockpos);
	virtual void onBlockRemoved(World *pworld, const WCoord &blockpos, int blockid, int blockdata);
	//tolua_end
private:
	virtual bool canThisPlantGrowOnThisBlockID(int blockid);
	virtual int getMaxGrowStage()
	{
		return 7;
	}
	//virtual const char *getGeomName()
	//{
	//	return "wheat";
	//}
	int getStage(int blockdata);
private:
	virtual void initGeomName() override;
private:
	enum
	{
		MAX_STAGES = 6
	};
	RenderBlockMaterial *m_Mtls[MAX_STAGES];

	int m_MaxStages;
}; //tolua_exports

class BlockRice : public CropperMaterial //tolua_exports
{ //tolua_exports
	DECLARE_BLOCKMATERIAL(BlockRice)
public:
	//tolua_begin
	//virtual const char *getGeomName()
	//{
	//	return "rice";
	//}
	virtual bool canPutOntoPos(WorldProxy *pworld, const WCoord &blockpos) override;
	virtual bool canStayOnPos(WorldProxy *pworld, const WCoord &blockpos) override;
	virtual void onBlockRemoved(World *pworld, const WCoord &blockpos, int blockid, int blockdata) override;
	//tolua_end
private:
	virtual void initGeomName() override;
}; //tolua_exports


#endif
