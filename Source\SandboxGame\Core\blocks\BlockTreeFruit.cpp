
#include "BlockTreeFruit.h"
#include "BlockMaterialMgr.h"
#include "section.h"
#include "SectionMesh.h"
#include "BlockGeom.h"
#include "world.h"
#include "special_blockid.h"
#include "WorldProxy.h"
#include "worldData/coreMisc.h"

using namespace MINIW;

IMPLEMENT_BLOCKMATERIAL(TreeFruitMaterial)
//IMPLEMENT_BLOCKINSTANCE(TreeFruitMaterial)

TreeFruitMaterial::TreeFruitMaterial()
{
	//for(int i=0; i<3; i++)
	//{
	//	m_StageMtls[i] = NULL;
	//}
	memset(m_stageMtlsIndex, UINT_MAX, sizeof(m_stageMtlsIndex));
}

TreeFruitMaterial::~TreeFruitMaterial()
{
	//for(int i=0; i<3; i++)
	//{
	//	OGRE_RELEASE(m_StageMtls[i]);
	//}
	memset(m_stageMtlsIndex, UINT_MAX, sizeof(m_stageMtlsIndex));
}

//BlockDrawType TreeFruitMaterial::getDrawType()
//{
//	return BLOCKDRAW_GRASS;
//}

void TreeFruitMaterial::init(int resid)
{
	Super::init(resid);
	SetToggle(BlockToggle_RandomTick, true);

	if(m_LoadOnlyLogic) return;

	
	RenderBlockMaterial* stageMtls[StageNum];
	g_BlockMtlMgr.createRenderMaterialStages(stageMtls, StageNum, GetBlockDef(), GetBlockDef()->Texture1.c_str(), BLOCKDRAW_GRASS);
	//getRenderMtlMgr().removeMtl(m_defaultMtlIndex);
	//m_Mtl = m_StageMtls[0];
	//m_Mtl->addRef();

	getRenderMtlMgr().removeMtl(m_defaultMtlIndex);
	m_defaultMtlIndex = getRenderMtlMgr().addMtl(stageMtls[0]);

	for (int i = 0; i < StageNum; i++)
	{
		m_stageMtlsIndex[i] = getRenderMtlMgr().addMtl(stageMtls[i]);
		OGRE_RELEASE(stageMtls[i]);
	}
	
}

void TreeFruitMaterial::initDrawType()
{
	m_blockDrawType = BLOCKDRAW_GRASS;
}

void TreeFruitMaterial::initGeomName()
{
	m_geomName = m_Def->Texture2.c_str();
}
//const char *TreeFruitMaterial::getGeomName()
//{
//	return GetBlockDef()->Texture2.c_str();
//}

inline int toStage(int blockdata)
{
	return (blockdata/5) % 3;
}
inline int toDir(int blockdata)
{
	int dir = blockdata % 5;
	return (dir == DIR_NEG_Y) ? DIR_POS_Y : dir;
}
inline int toData(int stage, int dir)
{
	if (dir == DIR_POS_Y)
	   dir = DIR_NEG_Y;
	return ((stage*5) + dir) % 15;
}
bool TreeFruitMaterial::canAttachedToMecha(int curblockdata, DirectionType dir, int neighbor_id, int neighbor_data)
{
	return toDir(curblockdata) == dir;
}

void TreeFruitMaterial::createBlockMesh(const BuildSectionMeshData& data, const WCoord &blockpos, SectionMesh *poutmesh)
{
	BlockGeomTemplate* geom = getGeom(data.m_LODLevel);
	if (geom == nullptr) return;

	auto psection = data.m_SharedSectionData;
	Block pblock = psection->getBlock(blockpos);
	Rainbow::ColorRGBAf verts_light[1] = { Rainbow::ColorRGBAf::black };
	int bdata = pblock.getData();
	int stage = toStage(bdata);
	int dir = toDir(bdata);

	assert(stage < 3);
	RenderBlockMaterial *pmtl = getRenderMtlMgr().getMtl(m_stageMtlsIndex[stage]);

	SectionSubMesh *psubmesh = poutmesh->getSubMesh(pmtl, false);

	psection->getBlockVertexLight(blockpos, verts_light);

	BlockGeomMeshInfo meshinfo;


	geom->getFaceVerts(meshinfo, stage, 1.0f, 0, dir);
	
	psubmesh->addGeomBlockLight(meshinfo, &blockpos, verts_light, NULL, pmtl->getUVTile());
}

SectionMesh *TreeFruitMaterial::createBlockProtoMesh(int protodata)
{
	return NULL;
}

BlockTexElement *TreeFruitMaterial::getDestroyTexture(Block pblock, BlockTexDesc &desc)
{
	desc.blendmode = BLEND_ALPHATEST;
	desc.gray = false;
	return  getRenderMtlMgr().getMtl(m_stageMtlsIndex[StageNum - 1])->getTexElement();
}

void TreeFruitMaterial::blockTick(World *pworld, const WCoord &blockpos)
{
	int blockdata = pworld->getBlockData(blockpos);
	if(!canStayOnPos(pworld->getWorldProxy(), blockpos))
	{
		dropBlockAsItem(pworld, blockpos, blockdata);
		pworld->setBlockAll(blockpos, 0, 0, 2);
	}
	else if(pworld->genRandomInt(7) == 0)
	{
		int stage = toStage(blockdata);
		if(stage < 2)
		{
			pworld->setBlockData(blockpos, toData(stage+1, toDir(blockdata)), 2);
		}
	}
}

bool TreeFruitMaterial::canStayOnPos(WorldProxy *pworld, const WCoord &blockpos)
{
	WCoord ng = NeighborCoord(blockpos, toDir(pworld->getBlockData(blockpos)));
	if(pworld->getBlockID(ng) == BLOCK_LEAVE_JUNGLE || pworld->getBlockID(ng) == BLOCK_LEAVE_SPUCE || pworld->getBlockID(ng) == BLOCK_PLANTSPACE_WOODS || pworld->getBlockID(ng) == BLOCK_PLANTSPACE_HASLEAF_WOODS)
	{
		return true;
	}
	else return false;
}

int TreeFruitMaterial::getPlaceBlockData(World *pworld, const WCoord &blockpos, DirectionType face, float hitptx, float hitpty, float hitptz, int def_blockdata)
{
	return ReverseDirection(face);
}

void TreeFruitMaterial::onNotify(World *pworld, const WCoord &blockpos, int blockid)
{
	if(!canStayOnPos(pworld->getWorldProxy(), blockpos))
	{
		dropBlockAsItem(pworld, blockpos, pworld->getBlockData(blockpos));
		pworld->setBlockAll(blockpos, 0, 0, 2);
	}
}

bool TreeFruitMaterial::onFertilized(World *pworld, const WCoord &blockpos, int fertiliser)
{
	int blockdata = pworld->getBlockData(blockpos);
	int stage = toStage(blockdata);
	int dir = toDir(blockdata);
	stage = stage + GenRandomInt(0,1);
	if(stage > 2) stage = 2;

	pworld->setBlockData(blockpos, toData(stage, dir), 2);
	return true;
}

void TreeFruitMaterial::dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata/* =0 */, BLOCK_MINE_TYPE droptype/* =BLOCK_MINE_NOTOOL */, float chance/* =1.0f */, int uin)
{
	int stage = toStage(blockdata);
	int num = stage>=2 ? 3 : 1;
	auto blockid = pworld->getBlockID(blockpos);
	auto mtl = g_BlockMtlMgr.getMaterial(blockid);
	if (!mtl)
	{
		return;
	}
	if (BLOCK_PLANTSPACE_OXYGEN_FRUIT == blockid)
	{
		if (stage < 2)
		{
			doDropItem(pworld, blockpos, mtl->getBlockToolMineDropId(0));//GetBlockDef()->ToolMineDrops[0].item);
		}
		else
		{
			doDropItem(pworld, blockpos, mtl->getBlockToolMineDropId(1));//GetBlockDef()->ToolMineDrops[1].item);
		}
	}
	else
	{
		for(int i=0; i<num; i++)
		{
			doDropItem(pworld, blockpos, mtl->getBlockToolMineDropId(0));//GetBlockDef()->ToolMineDrops[0].item);
		}
	}
}

bool TreeFruitMaterial::hasDestroyScore(int blockdata)
{
	return toStage(blockdata) >= 2;
}

int TreeFruitMaterial::getBlockGeomID(int *idbuf, int *dirbuf, const SectionDataHandler* sectionData, const WCoord &blockpos, World* world)
{
	Block pblock = sectionData->getBlock(blockpos);

	idbuf[0] = 0;
	dirbuf[0] = toDir(pblock.getData());

	return 1;
}