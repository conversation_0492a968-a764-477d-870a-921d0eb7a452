#include "BlockPile.h"
#include "world.h"
#include "BlockMaterialMgr.h"
#include "EffectManager.h"
#include "DefManagerProxy.h"

#ifndef  BLOCKPILEUSE_SIMPLE
	IMPLEMENT_BLOCKMATERIAL(BlockPile)
	IMPLEMENT_BLOCKMATERIAL(BlockPileTop)
	IMPLEMENT_BLOCKMATERIAL(BlockPileCenter)
	IMPLEMENT_BLOCKMATERIAL(BlockPileBottom)
	IMPLEMENT_BLOCKMATERIAL(BlockPileSpecial)

	#define FIND_MAX 10

	extern int monsterStatueGetMonsterId(int blockid);
	void BlockPile::onNotify(World* pworld, const WCoord& blockpos, int blockid)
	{
		if (!pworld)
		{
			return;
		}
		if (pworld->isRemoteMode())
		{
			return;
		}
		if (!pileIsSpecialBlockId(blockid) && !pileIsNormalBlock(blockid))
		{
			auto block = getSpecialBlock(pworld, blockpos);
			if (block.isEmpty())
			{
				return;
			}
			if (pileIsMonsterStatueSpecialBlockId(block.getResID()))
			{
				bool powered = pworld->isBlockIndirectlyGettingPowered(blockpos);
				if (!powered)
				{
					return;
				}
				if (!pworld->getEffectMgr())
				{
					return;
				}
				int monsterid = monsterStatueGetMonsterId(block.getResID());
				auto def = GetDefManagerProxy()->getMonsterDef(monsterid);
				if (!def)
				{
					return;
				}
				pworld->getEffectMgr()->playSound(BlockCenterCoord(blockpos), def->SaySound.c_str(), 1, 1, 3, -1);
			}
			return;
		}
		int data = 0;
		if (isSpecialBlock())
		{
			data = 5;
		}
		else
		{
			data = pworld->getBlockData(blockpos);
		}
		bool needDelete = false;
		//判断四周方块, 因为特殊方块是在左下角,所以只有-x,-z才会遇到
		do
		{
			if (needCheckX())
			{
				//x
				if (findXPOS(data))
				{
					int blockid = pworld->getBlockID(blockpos + WCoord(1, 0, 0));
					if (!checkXPOSId(blockid, data))
					{
						needDelete = true;
						break;
					}
				}

				//-x
				if (findXNEG(data))
				{
					int blockid = pworld->getBlockID(blockpos + WCoord(-1, 0, 0));
					if (!checkXNEGId(blockid, data))
					{
						needDelete = true;
						break;
					}
				}
			}
			if (needCheckZ())
			{
				//z
				if (findZPOS(data))
				{
					int blockid = pworld->getBlockID(blockpos + WCoord(0, 0, 1));
					if (!checkZPOSId(blockid, data))
					{
						needDelete = true;
						break;
					}
				}

				//-z
				if (findZNEG(data))
				{
					int blockid = pworld->getBlockID(blockpos + WCoord(0, 0, -1));
					if (!checkZNEGId(blockid, data))
					{
						needDelete = true;
						break;
					}
				}
			}
			//判断上下
			if (needCheckTop())
			{
				int blockid = pworld->getBlockID(blockpos + WCoord(0, 1, 0));
				if (!checkTopId(blockid, data))
				{
					needDelete = true;
					break;
				}
			}
			if (needCheckDown())
			{
				int blockid = pworld->getBlockID(blockpos + WCoord(0, -1, 0));
				if (!checkDownId(blockid, data))
				{
					needDelete = true;
					break;
				}
			}
		} while (0);
		if (needDelete)
		{
			pworld->setBlockAir(blockpos);
		}
	}

	bool BlockPile::checkTopId(int blockid, int data) const
	{
		return isTopId(blockid, data);
	}

	bool BlockPile::checkDownId(int blockid, int data) const
	{
		if (isDownId(blockid, data))
		{
			return true;
		}
		else
		{
			if (needCheckSpecial())
			{
				if (!isSpecialId(blockid, data))
				{
					return false;
				}
				return true;
			}
		}
		return false;
	}

	bool BlockPile::checkXPOSId(int blockid, int data) const
	{
		return isSurroundId(blockid, data);
	}

	bool BlockPile::checkXNEGId(int blockid, int data) const
	{
		if (blockid != getSurroundId())
		{
			if (needCheckSpecial())
			{
				if (!isSpecialId(blockid, data))
				{
					return false;
				}
				return true;
			}
			return false;
		}
		return true;
	}

	bool BlockPile::checkZPOSId(int blockid, int data) const
	{
		return isSurroundId(blockid, data);
	}

	bool BlockPile::checkZNEGId(int blockid, int data) const
	{
		if (isSurroundId(blockid, data))
		{
			return true;
		}
		else if(needCheckSpecial())
		{
			if (isSpecialId(blockid, data))
			{
				return true;
			}
		}
		return false;
	}

	void BlockPile::createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh)
	{
		return;
	}

	bool BlockPile::checkSpecialBlock(int blockid) const
	{
		if (blockid == BLOCK_PILE_TOP || blockid == BLOCK_PILE_CENTER || blockid == BLOCK_PILE_BOTTOM)
		{
			return false;
		}
		auto material = GetBlockMaterialMgr().getMaterial(blockid);
		if (!material)
		{
			return false;
		}
		return material->BlockTypeId() == BlockType_PileSpecial;
	}

	Block BlockPile::getSpecialBlock(World* pworld, const WCoord& blockpos)
	{
		if (!pworld)
		{
			return Block();
		}
		Block cur = pworld->getBlock(blockpos);
		return _getSpecialBlock(pworld, cur, blockpos);
	}

	Block BlockPile::getSpecialBlockOnDestory(World* pworld, const WCoord& blockpos, int blockdata)
	{
		if (!pworld)
		{
			return Block();
		}
		Block cur;
		cur.setAll(m_BlockResID, blockdata);
		return _getSpecialBlock(pworld, cur, blockpos);
	}

	Block BlockPile::_getSpecialBlock(World* pworld, Block cur, const WCoord& blockpos)
	{
		int id;
		int findX = 0;
		int findZ = 0;
		int findY = 0;
		bool find = false;
		//因为特殊方块都是在左下角的
		WCoord curPos = blockpos;
		//怪物雕像底座也能激发,但是它找特殊方块的逻辑不一样.
		if (m_BlockResID == BLOCK_MONSTER_STATUE_FOUNDATION)
		{
			int resort = 0;
			while (true)
			{
				if (resort > 200)
				{
					assert(0);
					break;
				}
				if (cur.getResID() != BLOCK_MONSTER_STATUE_FOUNDATION)
				{
					break;
				}
				resort++;
				int data = cur.getData();
				//特殊方块就在底座左下方块的上方
				if (!findXNEG(data) && !findZNEG(data))
				{
					curPos.y += 1;
					cur = pworld->getBlock(curPos);
					if (isSpecialId(cur.getResID(), cur.getData()))
					{
						find = true;
					}
					break;
				}
				if (findXNEG(data))
				{
					findX++;
					curPos.x--;
				}
				if (findZNEG(data))
				{
					findZ++;
					curPos.z--;
				}
				cur = pworld->getBlock(curPos);
			}
		}
		else
		{
			while ((id = cur.getResID(), find = isSpecialId(id, cur.getData()), !find))
			{
				int data = cur.getData();
				//只能往周边找了
				if (id == BLOCK_PILE_BOTTOM || id == BLOCK_MONSTER_STATUE_BOTTOM || id == BLOCK_MONSTER_STATUE_LAYER || id == BLOCK_PILE_LAYER)
				{
					if (findXNEG(data))
					{
						findX++;
						curPos.x--;
					}
					if (findZNEG(data))
					{
						findZ++;
						curPos.z--;
					}
					cur = pworld->getBlock(curPos);
				}
				else if (id == BLOCK_PILE_TOP || id == BLOCK_PILE_CENTER)
				{
					curPos.y--;
					findY++;
					cur = pworld->getBlock(curPos);
				}
				else
				{
					//assert(0);
					break;
				}
				if (findX > FIND_MAX || findZ > FIND_MAX || findY > FIND_MAX)
				{
					assert(0);
					break;
				}
			}
		}
		if (!find)
		{
			return Block();
		}
		return cur;
	}

	bool BlockPile::onTrigger(World* pworld, const WCoord& blockpos, DirectionType face, IClientPlayer* player, const Rainbow::Vector3f& colpoint)
	{
		if (!pworld)
		{
			return false;
		}
		if (pworld->isRemoteMode())
		{
			return true;
		}
		auto block = getSpecialBlock(pworld, blockpos);
		if (block.isEmpty())
		{
			return false;
		}
		if (pileIsMonsterStatueSpecialBlockId(block.getResID()))
		{
			if (!pworld->getEffectMgr())
			{
				return false;
			}
			int monsterid = monsterStatueGetMonsterId(block.getResID());
			auto def = GetDefManagerProxy()->getMonsterDef(monsterid);
			if (!def)
			{
				return false;
			}
			pworld->getEffectMgr()->playSound(BlockCenterCoord(blockpos), def->SaySound.c_str(), 1, 1, 3, -1);
			return true;
		}
		return false;
	}

	bool BlockPileTop::isDownId(int blockid, int data) const
	{
		return blockid == downId() || blockid == centerId() || blockid == BLOCK_MONSTER_STATUE_BOTTOM;
	}

	void BlockPile::dropBlockAsItem(World* pworld, const WCoord& blockpos, int blockdata, BLOCK_MINE_TYPE droptype, float chance, int uin/* = -1 */)
	{
		if (!pworld)
		{
			return;
		}

		const BlockDef* prevDef = m_Def;
		Block block = getSpecialBlockOnDestory(pworld, blockpos, blockdata);
		BlockMaterial* pBlockMtl = g_BlockMtlMgr.getMaterial(block.getResID());
		if (pBlockMtl)
		{
			m_Def = pBlockMtl->GetBlockDef();
		}

		// 雕像需要用核心方块的Def生成掉落物
		BlockMaterial::dropBlockAsItem(pworld, blockpos, blockdata, droptype, chance);
		m_Def = prevDef;
	}

    bool BlockPileCenter::isTopId(int blockid, int data) const
	{
		return blockid == topId() || blockid == centerId();
	}
	/*SectionMesh* BlockPile::createBlockProtoMesh(int protodata)
	{
		return NULL;
	}*/
#else
	IMPLEMENT_BLOCKMATERIAL(BlockPile)
	IMPLEMENT_BLOCKMATERIAL(BlockPileSpecial)

	void BlockPile::onBlockRemoved(World* pworld, const WCoord& blockpos, int blockid, int blockdata)
	{
		removeOtherBlock(pworld, blockpos, blockdata);
	}

	void BlockPile::removeOtherBlock(World* pworld, const WCoord& blockpos, int blockdata)
	{
		WCoord bottomLeft;
		for (int y = 0; y < m_rangeY; y++)
		{
			for (int x = 0; x < m_rangeX; x++)
			{
				for (int z = 0; z < m_rangeZ; z++)
				{
					//我们找出左下角
					bottomLeft = blockpos - WCoord(x, y, z);
					
				}
			}
		}
	}

#endif // ! BLOCKPILEUSE_SIMPLE