#include "BlockKeyOfBrokenSword.h"
#include "special_blockid.h"
#include "IClientPlayer.h"
#include "section.h"
#include "world.h"
#include "Math/Quaternionf.h"
#include "container_keypedestal.h"
#include "ActorVehicleAssemble.h"
#include "ActorVillager.h"
#include "backpack.h"
#include "ClientPlayer.h"
#include "OgreScriptLuaVM.h"
#include "DefManagerProxy.h"
#include "IPlayerControl.h"
#include "container_effect.h"
IMPLEMENT_BLOCKMATERIAL(BlockKeyOfBrokenSword)

BlockKeyOfBrokenSword::BlockKeyOfBrokenSword()
{

}

BlockKeyOfBrokenSword::~BlockKeyOfBrokenSword()
{

}

void BlockKeyOfBrokenSword::init(int resid)
{
	ModelBlockMaterial::init(resid);

	SetToggle(BlockToggle_HasContainer, true);
}

void BlockKeyOfBrokenSword::onBlockPlacedBy(World* pworld, const WCoord& blockpos, IClientPlayer* player)
{
	ClientPlayer* playerTmp = player->GetPlayer();
	if (!playerTmp) return;
	int placeDir = playerTmp->getCurPlaceDir();
	if (placeDir == DIR_NEG_Y || placeDir == DIR_POS_Y)
	{
		placeDir = DIR_NEG_X;
	}
	pworld->setBlockData(blockpos, placeDir);
}

void BlockKeyOfBrokenSword::onNotify(World* pworld, const WCoord& blockpos, int blockid)
{
}

bool BlockKeyOfBrokenSword::onTrigger(World* pworld, const WCoord& blockpos, DirectionType face, IClientPlayer* player, const Rainbow::Vector3f& colpoint)
{
	if (pworld->getCurMapID() == MAPID_GROUND)
	{
		if (0 == player->getCurToolID())
		{
			pworld->setBlockAir(blockpos);

			ClientPlayer* playerTmp = player->GetPlayer();
			if (!playerTmp) return false;
			playerTmp->getBackPack()->setItem(BLOCK_KEY_OF_BROKEN_SWORD, playerTmp->getBackPack()->getShortcutStartIndex() + playerTmp->getCurShortcut(), 1);
// 			int index = player->getCurShortcut();
// 			player->getBackPack()->addItem(588, 1, 1);
		}
	}
	else if (pworld->getCurMapID() == MAPID_LIEYANSTAR)
	{
		pworld->blockKeyOnTrigger(blockpos, player, player->getCurToolID() == 0);
	}

	return true;
}

void BlockKeyOfBrokenSword::onBlockAdded(World* pworld, const WCoord& blockpos)
{
	ModelBlockMaterial::onBlockAdded(pworld, blockpos);
	/*WCoord down = blockpos;
	down.y -= 1;
	int resid = pworld->getBlockID(down);
	if (BLOCK_FLAME_STAR_PEDESTAL == resid)
	{
		pworld->notifyBlock(down, resid);
	}*/
}

void BlockKeyOfBrokenSword::onBlockRemoved(World* pworld, const WCoord& blockpos, int blockid, int blockdata)
{
	ModelBlockMaterial::onBlockRemoved(pworld, blockpos, blockid, blockdata);
	/*WCoord down = blockpos;
	down.y -= 1;
	int resid = pworld->getBlockID(down);
	if (BLOCK_FLAME_STAR_PEDESTAL == resid)
	{
		pworld->notifyBlock(down, resid);
	}*/
	WorldEffectContainer *container = dynamic_cast<WorldEffectContainer *>(pworld->getContainerMgr()->getContainer(blockpos));
	if (container)
	{
		pworld->getContainerMgr()->destroyContainer(blockpos);
	}
}

float BlockKeyOfBrokenSword::getDestroyHardness(int blockdata, IClientPlayer *player)
{
	if (blockdata & 4) //未解除封印不能挖掘
	{
		return -1;
	}
	
	return BlockMaterial::getDestroyHardness(blockdata, player);
}

void BlockKeyOfBrokenSword::dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata, BLOCK_MINE_TYPE droptype/* =BLOCK_MINE_NOTOOL */, float chance/* =1.0f */, int uin)
{
	ModelBlockMaterial::dropBlockAsItem(pworld, blockpos, blockdata, droptype, chance, uin);
}

WorldContainer *BlockKeyOfBrokenSword::createContainer(World *pworld, const WCoord &blockpos)
{
	int effectId = CONTAINEFX_KEYOFBROKENSWORDSEAL;
	int blockdata = pworld->getBlockData(blockpos);
	if (blockdata & 4)
		effectId = CONTAINEFX_KEYOFBROKENSWORD;

	return SANDBOX_NEW(WorldEffectContainer, effectId, blockpos, WCoord(BLOCK_SIZE / 2, 0, BLOCK_SIZE / 2), 0);
}

int BlockKeyOfBrokenSword::getBlockGeomID(int* idbuf, int* dirbuf, const SectionDataHandler* sectionData, const WCoord& blockpos, World* world)
{
	if (!sectionData) return 1;
	int blockdata = sectionData->getBlock(blockpos).getData();
	int dir = blockdata & 3;
	idbuf[0] = 0;
	dirbuf[0] = dir;
	return 1;
}

int BlockKeyOfBrokenSword::getProtoBlockGeomID(int* idbuf, int* dirbuf)
{
	idbuf[0] = 0;
	dirbuf[0] = 0;
	return 1;
}