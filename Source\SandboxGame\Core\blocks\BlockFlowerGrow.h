
#ifndef __BLOCKFLOWERGROW_H__
#define __BLOCKFLOWERGROW_H__

#include "BlockGrayHerbs.h"

class FlowerGrowMaterial : public HerbMaterial //tolua_exports
{ //tolua_exports
	//typedef HerbMaterial Super;
	DECLARE_BLOCKMATERIAL(FlowerGrowMaterial)
public:
	//tolua_begin
	FlowerGrowMaterial();
	virtual ~FlowerGrowMaterial();

	virtual void init(int resid) override;
	virtual void createBlockMesh(const BuildSectionMeshData& data, const WCoord &blockpos, SectionMesh *poutmesh) override;
	virtual BlockTexElement *getDestroyTexture(Block pblock, BlockTexDesc &desc) override;
	virtual bool onFertilized(World *pworld, const WCoord &blockpos, int fertiliser);
	bool canGrowOnFreezePit(int blockId);
	virtual void blockTick(World *pworld, const WCoord &blockpos);
	virtual void forceResh(World* pworld, const WCoord& blockpos) override;
	virtual void dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata, BLOCK_MINE_TYPE droptype/* =BLOCK_MINE_NOTOOL */, float chance/* =1.0f */, int uin = -1);
	virtual bool hasDestroyScore(int blockdata);
	virtual bool canGrowInBlockLight(World* pworld, const WCoord& blockpos);
	virtual void createCollideData(CollisionDetect *coldetect, World *pworld, const WCoord &blockpos);
	virtual bool onActorCollidedWithBlock(World* pworld, const WCoord& blockpos, IClientActor* actor) override;
	//int getBlockGeomID(int *idbuf, int *dirbuf, const SharedSectionData *sectionData, const WCoord &blockpos, World* world);
	//tolua_end
	virtual void setGrowBlockID(World* pworld, const WCoord& blockpos, int blockid, int blockdata);
private:
	bool onMakeFlower(World* pworld, const WCoord& blockpos);
	virtual bool canThisPlantGrowOnThisBlockID(int blockid);
	
	virtual int getMaxGrowStage()
	{
		return 7;
	}
	//virtual const char *getGeomName()
	//{
	//	return "flowergrow";
	//}
	int getStage(int blockdata);
	virtual void initGeomName() override;
private:
	enum
	{
		MAX_STAGES = 6
	};
	RenderBlockMaterial *m_Mtls[MAX_STAGES];
	int m_MaxStages; 
}; //tolua_exports


#endif
