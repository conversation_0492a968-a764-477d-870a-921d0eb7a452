
#ifndef __BLOCKTREEFRUIT_H__
#define __BLOCKTREEFRUIT_H__

#include "BlockMaterial.h"

class TreeFruitMaterial : public ModelBlockMaterial //tolua_exports
{ //tolua_exports
	//typedef ModelBlockMaterial Super;
	DECLARE_BLOCKMATERIAL(TreeFruitMaterial)
	//DECLARE_BLOCKINSTANCE(CocoaMaterial)
public:
	const static int StageNum = 3;
	//tolua_begin
	TreeFruitMaterial();
	virtual ~TreeFruitMaterial();

	//virtual const char *getGeomName();

	virtual void init(int resid) override;

	virtual bool canAttachedToMecha(int curblockdata, DirectionType dir, int neighbor_id, int neighbor_data) override;
	virtual void createBlockMesh(const BuildSectionMeshData& data, const WCoord &blockpos, SectionMesh *poutmesh) override;
	virtual SectionMesh *createBlockProtoMesh(int protodata = 0) override;
	virtual BlockTexElement *getDestroyTexture(Block pblock, BlockTexDesc &desc) override;

	//virtual BlockDrawType getDrawType() override;

	virtual void blockTick(World *pworld, const WCoord &blockpos);
	virtual bool canStayOnPos(WorldProxy *pworld, const WCoord &blockpos);
	virtual void onNotify(World *pworld, const WCoord &blockpos, int blockid);
	virtual int getPlaceBlockData(World *pworld, const WCoord &blockpos, DirectionType face, float hitptx, float hitpty, float hitptz, int def_blockdata);
	virtual bool onFertilized(World *pworld, const WCoord &blockpos, int fertiliser);
	virtual void dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata=0, BLOCK_MINE_TYPE droptype=BLOCK_MINE_NOTOOL, float chance=1.0f, int uin = -1);
	virtual bool hasDestroyScore(int blockdata);
	virtual int getBlockGeomID(int *idbuf, int *dirbuf, const SectionDataHandler* sectionData, const WCoord &blockpos, World* world);
	//tolua_end

private:
	virtual void initGeomName() override;
	virtual void initDrawType() override;
private:
	//RenderBlockMaterial *m_StageMtls[3];
	unsigned int m_stageMtlsIndex[StageNum];
	
}; //tolua_exports

#endif