
#include "BlockMycelium.h"
#include "BlockMaterialMgr.h"
#include "section.h"
#include "SectionMesh.h"
#include "BlockGeom.h"
#include "Collision.h"
#include "world.h"
#include "OgreUtils.h"
#include "special_blockid.h"
#include "Ecosystem.h"
//#include "OgreMaterial.h"
#include "Graphics/Texture.h"
#include "worldData/coreMisc.h"

using namespace MINIW;
IMPLEMENT_BLOCKMATERIAL(BlockMycelium)
IMPLEMENT_BLOCKMATERIAL(BlockMyceliumModel)
//IMPLEMENT_BLOCKINSTANCE(BlockMycelium)

static int s_flag2id[] = { 226,227,465 };
static int s_maxMushroomCount = sizeof(s_flag2id) / sizeof(int);
static int canGrowOnBlock[] = { 100,101,104,116 };//- 长草土块（itemid100），土块（itemid101），石块（itemid104），萌眼星石块（itemid116）
BlockMycelium::BlockMycelium()
{
	m_MaxLightValue = 13;
	m_MtlMycelium = NULL;
}

BlockMycelium::~BlockMycelium()
{
	ENG_RELEASE(m_MtlMycelium);
}

//const char *BlockMycelium::getGeomName()
//{
//	return "hurbs";
//}

void BlockMycelium::init(int resid)
{
	Super::init(resid);
	SetToggle(BlockToggle_RandomTick, true);

	

	if (m_LoadOnlyLogic) return;

	char texname[256];
	sprintf(texname, "%s", GetBlockDef()->Texture1.c_str());
	m_MtlMycelium = g_BlockMtlMgr.createRenderMaterial(texname, GetBlockDef(), GETTEX_WITHDEFAULT, BLOCKDRAW_OPAQUE);
}

void BlockMycelium::initGeomName()
{
	m_geomName = "mycelium";
}
BlockTexElement *BlockMycelium::getDestroyTexture(Block pblock, BlockTexDesc &desc)
{
	desc.gray = true;
	desc.blendmode = BLEND_ALPHATEST;

	return m_MtlMycelium->getTexElement();
}

void BlockMycelium::createBlockMesh(const BuildSectionMeshData& data, const WCoord &blockpos, SectionMesh *poutmesh)
{
	BlockGeomTemplate* geom = getGeom(data.m_LODLevel);
	if (!geom)
		return;

	auto psection = data.m_SharedSectionData;
	Block pblock = psection->getBlock(blockpos);
	Rainbow::ColorRGBAf verts_light[1] = { Rainbow::ColorRGBAf::black };
	psection->getBlockVertexLight(blockpos, verts_light);

	BlockGeomMeshInfo meshinfo;
	RenderBlockMaterial *pmtl;


	float v = 1.0f;
	ChunkRandGen chunkrand;
	WCoord wpos = psection->getOrigin() + blockpos;
	WCoordHashCoder coder;
	chunkrand.setSeed(coder(wpos));

	float max_scale = 1.3f;
	float min_scale = 0.9f;
	float angle = (chunkrand.getFloat()) * 360;
	float tx = (chunkrand.getFloat() - chunkrand.getFloat()) * 0.4f;
	float tz = (chunkrand.getFloat() - chunkrand.getFloat()) * 0.4f;
	float scale = min_scale + (chunkrand.getFloat()) * (max_scale - min_scale);

	geom->getFaceScaleVerts(meshinfo, 0, scale, scale, 0, angle > 7 ? angle : 7, 0, NULL, tx, m_RenderPotOffsetY, tz, 0);
	pmtl = m_MtlMycelium;


	SectionSubMesh *psubmesh = poutmesh->getSubMesh(pmtl);

	//Ecosystem *biome = psection->getBiomeGen(data.m_World, blockpos);
	BlockColor vertcolor(255, 255, 255, 0);
	psubmesh->addGeomBlockLight(meshinfo, &blockpos, verts_light, &vertcolor, pmtl->getUVTile());
}

int BlockMycelium::getPlaceBlockData(World *pworld, const WCoord &blockpos, DirectionType face, float hitptx, float hitpty, float hitptz, int def_blockdata)
{
	return face;
}

void BlockMycelium::blockTick(World *pworld, const WCoord &blockpos)
{
	HerbMaterial::blockTick(pworld, blockpos);

	int blockid = pworld->getBlockID(DownCoord(blockpos));

	if (pworld->getBlockLightValue(blockpos + WCoord(0, 1, 0)) < m_MaxLightValue || blockid == BLOCK_REDSOIL)
	{
		if (GenRandomInt(0, 9) == 0)
		{
			int blockdata = pworld->getBlockData(blockpos);

			int _growFlag = blockdata & 3;

			if (_growFlag < 3)
			{
				pworld->setBlockData(blockpos, ++blockdata, 2);
			}
			else
			{
				pworld->setBlockAll(blockpos, getIdByBlockData(blockdata), 0);
			}
		}
	}
}

int BlockMycelium::getIdByBlockData(int pBlockData)
{
	int _mushTypeFlag = pBlockData >> 2;
	if (_mushTypeFlag > s_maxMushroomCount - 1) return 226;
	return s_flag2id[_mushTypeFlag];
}

int BlockMycelium::getBlockDataById(int pId, int pOldData)
{
	for (int i = 0; i < s_maxMushroomCount; i++)
	{
		if (s_flag2id[i] == pId)
		{
			return pOldData | (i << 2);
		}
	}
	return pOldData;
}

bool BlockMycelium::canThisPlantGrowOnThisBlockID(int blockid)
{
	return g_BlockMtlMgr.getMaterial(blockid)->isOpaqueCube() && blockid != BLOCK_SAND && blockid != 29;//29也是沙子，避免放在沙子上的情况
}

bool BlockMycelium::onFertilized(World *pworld, const WCoord &blockpos, int fertiliser)
{
	if (pworld->getCurMapID() >= MAPID_MENGYANSTAR)
	{
		if (pworld->getBlockID(TopCoord(blockpos)) != BLOCK_PLANTSPACE_OXYGEN) return false;
	}

	int blockdata = pworld->getBlockData(blockpos);

	int _growFlag = blockdata & 3;

	if (_growFlag < 3)
	{
		pworld->setBlockData(blockpos, ++blockdata, 2);
	}

	blockTick(pworld, blockpos);

	return true;
}

bool BlockMycelium::canPutOntoPos(WorldProxy *pworld, const WCoord &blockpos)
{
	return HerbMaterial::canPutOntoPos(pworld, blockpos) && canStayOnPos(pworld, blockpos);
}

bool BlockMycelium::canStayOnPos(WorldProxy *pworld, const WCoord &blockpos)
{
	int blockid = pworld->getBlockID(DownCoord(blockpos));
	if (blockid == BLOCK_REDSOIL) return true;

	return (pworld->getFullBlockLightValue(blockpos) < m_MaxLightValue && canThisPlantGrowOnThisBlockID(blockid));
}


///////////////////////////////////////////////////////////////////
BlockMyceliumModel::BlockMyceliumModel()
{
	m_MaxLightValue = 7;
}

BlockMyceliumModel::~BlockMyceliumModel()
{

}

void BlockMyceliumModel::dropBlockAsItem(World* pworld, const WCoord& blockpos, int blockdata , BLOCK_MINE_TYPE droptype, float chance, int uin/* = -1 */)
{
	return;
	if (blockdata<2)
	{
		int dropId = m_BlockResID;
		if (m_BlockResID == BLOCK_MUSHROOM)//后续需要改成掉落种子itemId
		{
			dropId = BLOCK_MUSHROOM_SEED;
		}
		else if (m_BlockResID == BLOCK_MUSHROOM_RED)
		{
			dropId = BLOCK_MUSHROOM_RED_SEED;
		}
		doDropItem(pworld, blockpos, dropId);
		pworld->destroyBlockEx(blockpos.x, blockpos.y, blockpos.z, false);
	}
	else
	{
		BlockMaterial::dropBlockAsItem(pworld, blockpos, blockdata, droptype, chance);
	}
}

void BlockMyceliumModel::onBlockPlacedBy(World* pworld, const WCoord& blockpos, IClientPlayer* player)
{
	if (m_BlockResID == BLOCK_MUSHROOM || m_BlockResID == BLOCK_MUSHROOM_RED)//成熟土笋紫苏放置也是成熟的
	{
		pworld->setBlockData(blockpos, 2, 2);
	}
}


void BlockMyceliumModel::setMushRoomChange(World* pworld, const WCoord& blockpos)
{
	//- 紫苏变异：【星光孢子杆】（itemid261）变成【变异星光孢子杆】（itemid286），【星光孢子】（itemid277）变成【变异紫苏】（itemid267）
	//- 土笋变异：【星光孢子杆】（itemid261）变成【变异星光孢子杆】（itemid286），【星光孢子】（itemid277）变成【变异土笋】（itemid266）
	//在一定范围内找到星光孢子树干
	WCoord mutantPos;
	if (pworld->getBlockInRange(mutantPos, BLOCK_PLANTSPACE_STARMUSHROOM_WOODS, blockpos, 2, -1, 1))
	{
		if (getBlockResID() == BLOCK_MUSHROOM)
		{
			pworld->setBlockAll(mutantPos, BLOCK_PLANTSPACE_MUTANTMUSHROOM_RED_WOODS, 0, 2);
			//pworld->setBlockAll(blockpos, BLOCK_PLANTSPACE_MUTANTMUSHROOM_LEAVS, 0, 2);
		}
		else
		{
			pworld->setBlockAll(mutantPos, BLOCK_PLANTSPACE_MUTANTMUSHROOM_RED_WOODS, 8, 2);
			//pworld->setBlockAll(blockpos, BLOCK_PLANTSPACE_MUTANTMUSHROOM_RED_LEAVS, 0, 2);
		}
			
			
	}
	//在一定范围内找到星光孢子树叶
	if (pworld->getBlockInRange(mutantPos, BLOCK_PLANTSPACE_STARMUSHROOM_LEAVS, blockpos, 2, -1, 1, MAX_PLANTSPACE_STARMUSHROOM_LEAVS_TYPE))
	{
		pworld->setBlockAll(mutantPos, BLOCK_PLANTSPACE_MUTANTMUSHROOM_LEAVS+ BLOCK_MUSHROOM- getBlockResID(), 0, 2);
		//if (getBlockResID() == BLOCK_MUSHROOM)
		//	pworld->setBlockAll(blockpos, BLOCK_PLANTSPACE_MUTANTMUSHROOM_LEAVS, 0, 2);
		//else
		//	pworld->setBlockAll(blockpos, BLOCK_PLANTSPACE_MUTANTMUSHROOM_RED_LEAVS, 0, 2);
	}
}

void BlockMyceliumModel::blockTick(World* pworld, const WCoord& blockpos)
{
	HerbMaterial::blockTick(pworld, blockpos);

	int blockid = pworld->getBlockID(DownCoord(blockpos));

	int blockdata = pworld->getBlockData(blockpos);
	int _growFlag = blockdata & 3;
	int topY = pworld->getTopHeight(blockpos.x, blockpos.z);
	int blocktopid=pworld->getBlockID(TopCoord(blockpos));
	if (TopCoord(blockpos).y == topY && (blocktopid == BLOCK_AIR || blocktopid == BLOCK_ALIEN_AIR))
	{
		dropBlockAsItem(pworld, blockpos, _growFlag);
		return;
	}
	if (pworld->getBlockLightValue(blockpos) > m_MaxLightValue || topY <= blockpos.y)
	{
		dropBlockAsItem(pworld, blockpos, _growFlag);
		return;
	}
	if (_growFlag==2)
	{
		setMushRoomChange(pworld, blockpos);
	}
	if (_growFlag < 2)
	{
		if (pworld->getBlockLightValue(blockpos) <= m_MaxLightValue)
		{
			if (GenRandomInt(0, 9) == 0)
			{
				pworld->setBlockData(blockpos, ++blockdata, 2);
				if (_growFlag == 1)
				{
					if(m_BlockResID == BLOCK_MUSHROOM_SEED)
						pworld->setBlockAll(blockpos, BLOCK_MUSHROOM, blockdata, 2);
					if(m_BlockResID == BLOCK_MUSHROOM_RED_SEED)
						pworld->setBlockAll(blockpos, BLOCK_MUSHROOM_RED, blockdata, 2);
				}
			}
		}
	}
}

bool BlockMyceliumModel::canPutOntoPos(WorldProxy* pworld, const WCoord& blockpos)
{
	int topY=pworld->getTopHeight(blockpos.x, blockpos.z);
	if (topY <= blockpos.y) return false;//- 只能在亮度<=7，且上方有方块遮挡的方块上放置
	return canStayOnPos(pworld, blockpos);
}

bool BlockMyceliumModel::canStayOnPos(WorldProxy* pworld, const WCoord& blockpos)
{
	int blockid = pworld->getBlockID(DownCoord(blockpos));
	return (pworld->getWorld()->getBlockLightValue(blockpos) <= m_MaxLightValue && canThisPlantGrowOnThisBlockID(blockid));
}

bool BlockMyceliumModel::canThisPlantGrowOnThisBlockID(int blockid)//可以放置的方块
{
	for (int i = 0; i < 4; i++)
	{
		if (blockid == canGrowOnBlock[i])
		{
			return true;
		}
	}
	return false;
}

bool BlockMyceliumModel::onFertilized(World* pworld, const WCoord& blockpos, int fertiliser)
{
	//if (pworld->getCurMapID() >= MAPID_MENGYANSTAR)
	//{
	//	if (pworld->getBlockID(TopCoord(blockpos)) != 20) return false;
	//}

	const BlockDef* def = GetBlockDef();
	if (def && def->CropsSign == 0 || def->GrowthTimeNum == 0)
	{
		int blockdata = pworld->getBlockData(blockpos);

		int _growFlag = blockdata & 3;
		FertilizedPlayEffect(pworld, blockpos);

		if (_growFlag < 2)
		{
			pworld->setBlockData(blockpos, ++blockdata, 2);
			if (_growFlag == 1)
			{
				if (m_BlockResID == BLOCK_MUSHROOM_SEED)
					pworld->setBlockAll(blockpos, BLOCK_MUSHROOM, blockdata, 2);
				if (m_BlockResID == BLOCK_MUSHROOM_RED_SEED)
					pworld->setBlockAll(blockpos, BLOCK_MUSHROOM_RED, blockdata, 2);
			}
		}

		//blockTick(pworld, blockpos);

	}
	else
	{
		return dealFertilized(pworld, blockpos, fertiliser);
	}

	return true;
}


void BlockMyceliumModel::createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh)
{
	BlockGeomTemplate* geom = getGeom(data.m_LODLevel);
	if (!geom)
		return;

	auto psection = data.m_SharedSectionData;
	Block pblock = psection->getBlock(blockpos);

	bool hastbn = false;
	Rainbow::ColorRGBAf verts_light[1] = { Rainbow::ColorRGBAf::black };
	psection->getBlockVertexLight(blockpos, verts_light);
	int idbuf[32];
	int dirbuf[32];
	int ngeom = getBlockGeomID(idbuf, dirbuf, psection, blockpos, data.m_World);
	BlockGeomMeshInfo meshinfo;
	RenderBlockMaterial* pmtl = getRenderMtlMgr().getMtl(m_defaultMtlIndex);// getGeomMtl(psection, blockpos);
	SectionSubMesh* psubmesh = poutmesh->getSubMesh(pmtl);

	int nBlockId = pblock.getResID();
	for (int i = 0; i < ngeom; i++)
	{

		int dir = dirbuf[i] & 0xffff;
		int mirrortype = (dirbuf[i] >> 16) & 3;

		if (mirrortype > 0) geom->getFaceVerts(meshinfo, idbuf[i], 1.0f, 0, dir, mirrortype, NULL, 0, hastbn);
		else geom->getFaceVerts(meshinfo, idbuf[i], 1.0f, 0, dir, 0, NULL, 0, hastbn);

		if (isColorableBlock() && !isUseCustomModel())
		{
			BlockColor bv = getBlockColor(pblock.getData());
			bv.a = 0;
			psubmesh->addGeomBlockLight(meshinfo, &blockpos, verts_light, &bv, pmtl->getUVTile());
		}
		else
		{
			psubmesh->addGeomBlockLight(meshinfo, &blockpos, verts_light, NULL, pmtl->getUVTile());
		}
	}
}

void BlockMyceliumModel::init(int resid)
{
	BlockMycelium::init(resid);

	if (m_LoadOnlyLogic) return;

}

void BlockMyceliumModel::initDefaultMtl()
{
	char texname[256];
	int gettextype;
	const char* newtexname = getBaseTexName(texname, m_Def, gettextype);
	auto mtl = g_BlockMtlMgr.createRenderMaterial(newtexname, m_Def, gettextype, getDrawType(), getMipmapMethod());
	m_defaultMtlIndex = getRenderMtlMgr().addMtl(mtl);
	OGRE_RELEASE(mtl);
}

BlockTexElement* BlockMyceliumModel::getDestroyTexture(Block pblock, BlockTexDesc& desc)
{
	desc.blendmode = BLEND_ALPHATEST;
	desc.gray = false;

	return getRenderMtlMgr().getMtl(m_defaultMtlIndex)->getTexElement();
}

const char* BlockMyceliumModel::getBaseTexName(char* texname, const BlockDef* def, int& gettextype)
{
	gettextype = GETTEX_WITHDEFAULT;
	return def->Texture1.c_str();
}

void BlockMyceliumModel::initGeomName()
{
	m_geomName = m_Def->Texture2.c_str();
}


int BlockMyceliumModel::getBlockGeomID(int* idbuf, int* dirbuf, const SectionDataHandler* sectionData, const WCoord& blockpos, World* world)
{
	Block pblock = sectionData->getBlock(blockpos);
	int stage = pblock.getData() & 3;
	int meshsize = getGeom()->getMeshCount();

	if (stage >= meshsize)
		stage = meshsize - 1;

	idbuf[0] = stage;
	dirbuf[0] = pblock.getData() % 4;

	return 1;
}