<?xml version="1.0" encoding="utf-8" standalone="yes" ?>
<Ui>
	<Script file="editor/luascript/CoordAxis/CoordAxisFrameModel.lua"/>
	<Script file="editor/luascript/CoordAxis/CoordAxisFrameView.lua"/>
	<Script file="editor/luascript/CoordAxis/CoordAxisFrameCtrl.lua"/>
	
	<Frame name="CoordAxisFrame" parent="UIClient" frameStrata="BACKGROUND" framelevel="2"  hidden="true" input_transparent="true" >
        <Size>
			<AbsDimension x="195" y="195"/>
		</Size>
		<Anchors>
			<Anchor point="topright" relativePoint="topright" relativeTo="$parent">
				<Offset>
					<AbsDimension x="20" y="-20" />
				</Offset>
			</Anchor>
		</Anchors>

        <Layers>
			<Layer level="BACKGROUND">
			<!--
                <Texture name="$parentBackGround" file="ui/mobile/texture2/operate.xml" uvname="img_board_map.png">
                    <Size>
                        <AbsDimension x="180" y="180"/>
                    </Size>
                    <Anchors>
                        <Anchor point="center" relativePoint="center" relativeTo="$parent">
                            <Offset>
                                <AbsDimension x="0" y="0"/>
                            </Offset>
                        </Anchor>
                    </Anchors>
                </Texture>
				-->
			</Layer>
		</Layers>
		<Frames>
			<ModelView name="$parentModelView">
                <Size>
                    <RelDimension x="1.0" y="1.0" />
                </Size>
            	<Anchors>
					<Anchor point="center" relativeTo="$parent" relativePoint="center">
						<Offset>
							<AbsDimension x="0" y="0" />
						</Offset>
					</Anchor>
				</Anchors>
                <Scripts>
					<OnMouseDown>GetInst("UIManager"):GetCtrl("CoordAxisFrame"):RotateBtnDown();</OnMouseDown>
					<OnMouseMove>GetInst("UIManager"):GetCtrl("CoordAxisFrame"):RotateViewMoved();</OnMouseMove>
                    <OnMouseUp>GetInst("UIManager"):GetCtrl("CoordAxisFrame"):RotateBtnUp();</OnMouseUp>
				</Scripts>
            </ModelView>
		</Frames>
		<Scripts>
            <OnEvent>GetInst("UIManager"):GetCtrl("CoordAxisFrame"):OnEvent();</OnEvent>
            <OnUpdate>GetInst("UIManager"):GetCtrl("CoordAxisFrame"):OnUpdate();</OnUpdate> 
		</Scripts>
	</Frame>
</Ui>
