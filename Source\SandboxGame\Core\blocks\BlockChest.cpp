
#include "BlockChest.h"
#include "BlockMaterialMgr.h"
#include "section.h"
#include "SectionMesh.h"
#include "BlockGeom.h"
#include "container_world.h"
#include "world.h"
#include "WorldProxy.h"
#include "IClientPlayer.h"
#include "IPlayerControl.h"
#include "special_blockid.h"
#include "EffectManager.h"
#include "Collision.h"
#include "IPlayerControl.h"
#include "WorldManager.h"
#include "container_erosion_storage.h"
//#include "OgreMaterial.h"

using namespace MINIW;

IMPLEMENT_BLOCKMATERIAL(ChestMaterial)


void ChestMaterial::init(int resid)
{
	ModelBlockMaterial::init(resid);
	//属性初始化
	SetToggle(BlockToggle_HasContainer, true);
	SetToggle(BlockToggle_CanOutputQuantityEnergy, true);
}

ChestMaterial::ChestMaterial()
{

}

ChestMaterial::~ChestMaterial()
{

}

void ChestMaterial::createBlockMesh(const BuildSectionMeshData& data, const WCoord &blockpos, SectionMesh *poutmesh)
{
	BlockGeomTemplate* geom = getGeom(data.m_LODLevel);
	if (!geom)
		return;
	auto psection = data.m_SharedSectionData;
	Block pblock = psection->getBlock(blockpos);

	int i = 0;
	for(; i<6; i++)
	{
		Block neighbor = psection->getNeighborBlock(blockpos, (DirectionType)i);
		if(neighbor.isEmpty() == false && neighbor.getResID() == pblock.getResID())
		{
			break;
		}
	}

	int boxid;
	int placedir = pblock.getData()%4;
	bool isopen = pblock.getData() >= 4;
	int ismirror = 0;

	if(i < 4)//double chest
	{
		boxid = 2;
		if(placedir==DIR_NEG_X && i==DIR_POS_Z || 
		   placedir==DIR_POS_X && i==DIR_NEG_Z ||
		   placedir==DIR_NEG_Z && i==DIR_NEG_X ||
		   placedir==DIR_POS_Z && i==DIR_POS_X) 
		   ismirror = 1;
	}
	else if(i < 6)
	{
		 boxid = 3;
		 if(i == DIR_NEG_Y) 
		 {
		     boxid = 4; 
		 }
	}
	else
	{
		boxid = 0;
	}
	if(isopen) boxid += 1;

	if (getBlockResID() == 150022)
	{
		boxid = isopen ? 1:0;
	}
	else
	{
		//此处去除箱子合并的显示逻辑， code - by： fukaijian
		boxid = 0;
		ismirror = 0;
	}

	Rainbow::ColorRGBAf verts_light[1] = { Rainbow::ColorRGBAf::black };
	psection->getBlockVertexLight(blockpos, verts_light);

	SectionSubMesh *psubmesh = poutmesh->getSubMesh(getDefaultMtl());

	BlockGeomMeshInfo meshinfo;
	geom->getFaceVerts(meshinfo, boxid, 1.0f, 0, placedir, ismirror);
	psubmesh->addGeomBlockLight(meshinfo, &blockpos, verts_light, NULL, getDefaultMtl()->getUVTile());
}

SectionMesh *ChestMaterial::createBlockProtoMesh(int protodata)
{
	SectionMesh* pmesh = ENG_NEW(SectionMesh)();
	SectionSubMesh* psubmesh = pmesh->getSubMesh(getDefaultMtl(), true);

	BlockGeomMeshInfo meshinfo;

	getGeom(0)->getFaceVerts(meshinfo, 0);
	psubmesh->addTriangleList(meshinfo.vertices.data(), meshinfo.vertices.size(), meshinfo.indices.data(), meshinfo.indices.size());

	return pmesh;
}

bool ChestMaterial::canPutOntoPos(WorldProxy *pworld, const WCoord &blockpos)
{	
	//此处去除箱子合并的摆放逻辑， code - by： fukaijian
	/*int count = 0;

	for(int dir=0; dir<6; dir++)
	{
		if(pworld->getBlockID(NeighborCoord(blockpos, dir)) == getBlockResID())
		{
			count++;
		}
	}

	if(count > 1) return false;

	for(int dir=0; dir<6; dir++)
	{
		if(isThereANeighborChest(pworld, NeighborCoord(blockpos,dir))) return false;
	}*/

	// 非组合式 但新增家具有一个模型占多格的情况
	if (m_BlockResID == BLOCK_CHEST_SANRIO_HELLOKITTY || m_BlockResID == BLOCK_CHEST_SANRIO_CINNAMOROLL || m_BlockResID == BLOCK_CHEST_SANRIO_KUROMI ||
		m_BlockResID == BLOCK_CHEST_SANRIO_MELODY || m_BlockResID == BLOCK_CHEST_SANRIO_KEROPPI || m_BlockResID == BLOCK_CHEST_SANRIO_PURIN ||
		m_BlockResID == BLOCK_CHEST_SANRIO_TWINSTAR)
	{
		int placedir = DIR_NEG_X; //默认初始化
		if (GetIPlayerControl()) placedir = GetIPlayerControl()->GetPlayerControlCurPlaceDir();

		const WCoord& leftPos = leftOnPlaceDir(blockpos, placedir);
		Block leftBlock = pworld->getBlock(leftPos);
		if (leftBlock.getResID() == m_BlockResID) return false;

		const WCoord& rightPos = rightOnPlaceDir(blockpos, placedir);
		Block rightBlock = pworld->getBlock(rightPos);
		if (rightBlock.getResID() > 0) return false;

		const WCoord& backPos = backOnPlaceDir(blockpos, placedir);
		Block backBlock = pworld->getBlock(backPos);
		if (backBlock.getResID() == m_BlockResID) return false;

		//右下斜边
		const WCoord& rleanPos = rightLeanOnPlaceDir(blockpos, ReverseDirection(placedir));
		Block rleanPosBlock = pworld->getBlock(rleanPos);
		if (rleanPosBlock.getResID() == m_BlockResID) return false;

		//正前边 衣柜横着摆放时
		const WCoord& frontPos = frontOnPlaceDir(blockpos, placedir);
		Block frontBlock = pworld->getBlock(frontPos);
		if (frontBlock.getResID() == m_BlockResID)
		{
			int blockdir = frontBlock.getData() % 4;
			if (placedir == DIR_NEG_X && blockdir == DIR_POS_Z ||
				placedir == DIR_POS_X && blockdir == DIR_NEG_Z ||
				placedir == DIR_NEG_Z && blockdir == DIR_NEG_X ||
				placedir == DIR_POS_Z && blockdir == DIR_POS_X)
				return false;
		}
	}

	return true;
}

WCoord ChestMaterial::leftOnPlaceDir(const WCoord& blockpos, int placedir)
{
	WCoord result = blockpos;

	if (placedir == DIR_NEG_X)
		result.z = blockpos.z + 1;
	else if (placedir == DIR_POS_X)
		result.z = blockpos.z - 1;
	else if (placedir == DIR_NEG_Z)
		result.x = blockpos.x - 1;
	else
		result.x = blockpos.x + 1;

	return result;
}

WCoord ChestMaterial::rightOnPlaceDir(const WCoord& blockpos, int placedir)
{
	WCoord result = blockpos;

	if (placedir == DIR_NEG_X)
		result.z = blockpos.z - 1;
	else if (placedir == DIR_POS_X)
		result.z = blockpos.z + 1;
	else if (placedir == DIR_NEG_Z)
		result.x = blockpos.x + 1;
	else
		result.x = blockpos.x - 1;

	return result;
}

WCoord ChestMaterial::backOnPlaceDir(const WCoord& blockpos, int placedir)
{
	WCoord result = blockpos;

	if (placedir == DIR_NEG_X)
		result.x = blockpos.x - 1;
	else if (placedir == DIR_POS_X)
		result.x = blockpos.x + 1;
	else if (placedir == DIR_NEG_Z)
		result.z = blockpos.z - 1;
	else
		result.z = blockpos.z + 1;

	return result;
}

WCoord ChestMaterial::rightLeanOnPlaceDir(const WCoord& blockpos, int placedir)
{
	WCoord result = blockpos;

	if (placedir == DIR_NEG_X)
	{
		result.x = blockpos.x + 1;
		result.z = blockpos.z + 1;
	}
	else if (placedir == DIR_POS_X)
	{
		result.x = blockpos.x - 1;
		result.z = blockpos.z - 1;
	}
	else if (placedir == DIR_NEG_Z)
	{
		result.x = blockpos.x - 1;
		result.z = blockpos.z + 1;
	}
	else
	{
		result.x = blockpos.x + 1;
		result.z = blockpos.z - 1;
	}

	return result;
}

WCoord ChestMaterial::frontOnPlaceDir(const WCoord& blockpos, int placedir)
{
	WCoord result = blockpos;

	if (placedir == DIR_NEG_X)
		result.x = blockpos.x + 1;
	else if (placedir == DIR_POS_X)
		result.x = blockpos.x - 1;
	else if (placedir == DIR_NEG_Z)
		result.z = blockpos.z + 1;
	else
		result.z = blockpos.z - 1;

	return result;
}

bool ChestMaterial::isThereANeighborChest(WorldProxy *pworld, const WCoord &blockpos)
{
	if(pworld->getBlockID(blockpos) != getBlockResID()) return false;

	for(int dir=0; dir<6; dir++)
	{
		if(pworld->getBlockID(NeighborCoord(blockpos, dir)) == getBlockResID()) return true;
	}

	return false;
}

bool ChestMaterial::onTrigger(World *pworld, const WCoord &blockpos, DirectionType face, IClientPlayer *player, const Rainbow::Vector3f &colpoint)
{
	if (pworld->isRemoteMode())
	{
		return true;
	}

	if(pworld->getContainerMgr() == NULL) return true;

	if (HOME_GARDEN_WORLD == pworld->getMapSpecialType())
		return true;

	int DIR_NEG_X = 0,	DIR_POS_X = 1,	
		DIR_NEG_Z = 2,	DIR_POS_Z = 3,
		DIR_NEG_Y = 4,	DIR_POS_Y = 5;

	WorldContainer *container = pworld->getContainerMgr()->getContainer(blockpos);

	//此处去除箱子合并的UI逻辑， code - by： fukaijian
	/*if(pworld->getBlockID(blockpos) == getBlockResID())
	{
		int placeDir = pworld->getBlockData(blockpos) % 4;
		WCoord lpos = blockpos, rpos = blockpos;

		if (placeDir == DIR_NEG_X)
		{
			lpos.z += 1; 
			rpos.z -= 1;
		}
		else if (placeDir == DIR_POS_X)
		{
			lpos.z -= 1;
			rpos.z += 1;
		}
		else if (placeDir == DIR_NEG_Z)
		{
			lpos.x -= 1;
			rpos.x += 1;
		}
		else
		{
			lpos.x += 1;
			rpos.x -= 1;
		}


		WCoord toppos = blockpos, bottompos = blockpos;
		toppos.y += 1;
		bottompos.y -= 1;


		if (pworld->getBlockID(lpos) == getBlockResID())
		{
			auto leftContainer = dynamic_cast<WorldStorageBox*>
				(pworld->getContainerMgr()->getContainer(lpos));
			if (leftContainer && container) 
			{
				leftContainer->append(dynamic_cast<WorldStorageBox*>(container));
				container = leftContainer;
			}
		}
		else if (pworld->getBlockID(rpos) == getBlockResID())
		{
			auto riteContainer = dynamic_cast<WorldStorageBox*>
				(pworld->getContainerMgr()->getContainer(rpos));
			if (riteContainer && container)
			{
				auto tc = dynamic_cast<WorldStorageBox*>(container);
				if (tc != nullptr) tc->append(riteContainer);
			}
		}
		else if(pworld->getBlockID(toppos) == getBlockResID())
		{
			auto topContainer = dynamic_cast<WorldStorageBox*>
				(pworld->getContainerMgr()->getContainer(toppos));
			if (topContainer && container) 
			{
				topContainer->append(dynamic_cast<WorldStorageBox*>(container));
				container = topContainer;
			}
		}
		else if(pworld->getBlockID(bottompos) == getBlockResID())
		{
			auto bottomContainer = dynamic_cast<WorldStorageBox*>
				(pworld->getContainerMgr()->getContainer(bottompos));
			if (bottomContainer && container)
			{
				auto tc = dynamic_cast<WorldStorageBox*>(container);
				if (tc != nullptr) tc->append(bottomContainer);
			}
		}
	}*/
	if(container && player)
	{
		pworld->getEffectMgr()->playSound(BlockCenterCoord(blockpos), "misc.chest_open", 1.0f, GenRandomFloat()*0.2f+0.8f);
		player->openContainer(container);
		GetWorldManagerPtr()->getWorldInfoManager()->openContainerNoticeActorVillager(pworld, player, blockpos);
	}

	auto mapData = g_WorldMgr->getMapData(pworld->getCurMapID(), true);
	if (mapData != NULL)
	{
		for (auto p = mapData->treasureBoxList.begin(); p != mapData->treasureBoxList.end(); p++)
		{
			if (p->x == blockpos.x && p->y == blockpos.y && p->z == blockpos.z)
			{
				mapData->treasureBoxList.erase(p);
				break;
			}
		}
	}

	return true;
}

int ChestMaterial::outputQuantityEnergy(World *pworld, const WCoord &blockpos, DirectionType dir)
{
	if(pworld->getContainerMgr() == NULL) return 0;
	WorldContainer *container = pworld->getContainerMgr()->getContainer(blockpos);
	if(container) return container->calComparatorInputOverride();
	else return 0;
}

WorldContainer *ChestMaterial::createContainer(World *pworld, const WCoord &blockpos)
{
	if (GetWorldManagerPtr() && GetWorldManagerPtr()->isGodMode())
	{
		return SANDBOX_NEW(WorldStorageBox, blockpos);
	}
	return SANDBOX_NEW(ErosionStorageBox, blockpos,getBlockResID());
}

void ChestMaterial::createCollideData(CollisionDetect *coldetect, World *pworld, const WCoord &blockpos)
{
	coldetect->addObstacle(blockpos*BLOCK_SIZE, blockpos*BLOCK_SIZE+WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
}

void ChestMaterial::createPickData(CollisionDetect *coldetect, World *pworld, const WCoord &blockpos)
{
	ModelBlockMaterial::createCollideData(coldetect, pworld, blockpos);
}

int ChestMaterial::convertDataByRotate(int blockdata, int rotatetype)
{
	return this->commonConvertDataByRotateWithBit(blockdata, rotatetype, 3, 12);
}


IMPLEMENT_BLOCKMATERIAL_INSTANCE_BEGIN(ChestMaterial)
	IMPLEMENT_BLOCKMATERIAL_INSTANCE_R_PARAM(ChestMaterial, R_Dir, int)(0, "Dir", "Block", &ChestMaterialInstance::GetBlockDir, &ChestMaterialInstance::SetBlockDir);
	IMPLEMENT_BLOCKMATERIAL_INSTANCE_R_PARAM(ChestMaterial, R_MaxCount, int)(1, "MaxCount", "Block", &ChestMaterialInstance::GetMaxCount, &ChestMaterialInstance::SetMaxCount);
IMPLEMENT_BLOCKMATERIAL_INSTANCE_END()

void ChestMaterialInstance::SetBlockDir(const int& dir)
{
	int placedir = dir;//0;
	int blockdata = m_pworld->getBlockData(m_pos);

	auto newBlockData = (blockdata & 0xFFFC) | placedir;
	m_pworld->setBlockData(m_pos, newBlockData, 3);
}

int ChestMaterialInstance::GetBlockDir() const
{
	return m_block.getData() & 3;
}

void ChestMaterialInstance::SetMaxCount(const int& count)
{
	WorldStorageBox* container = dynamic_cast<WorldStorageBox*>(m_Container);
	if (container)
		container->m_GridCount = count;
}

int ChestMaterialInstance::GetMaxCount() const
{
	WorldStorageBox* container = dynamic_cast<WorldStorageBox*>(m_Container);
	if (container)
		return container->m_GridCount;
	return 0;
}

void ChestMaterial::onBlockRemoved(World* pworld, const WCoord& blockpos, int blockid, int blockdata)
{
	auto mapData = g_WorldMgr->getMapData(pworld->getCurMapID(), true);
	if (mapData != NULL)
	{
		for (auto p = mapData->treasureBoxList.begin(); p != mapData->treasureBoxList.end(); p++)
		{
			if (p->x == blockpos.x && p->y == blockpos.y && p->z == blockpos.z)
			{
				mapData->treasureBoxList.erase(p);
				break;
			}
		}
	}
}

void ChestMaterial::onBlockPlacedBy(World* pworld, const WCoord& blockpos, IClientPlayer* player)
{
	int blockdata=pworld->getBlockData(blockpos);
	if (blockdata == 0)
	{
		int dir = player->GetPlayerCurPlaceDir();
		pworld->setBlockData(blockpos, dir, 3);
	}
}
