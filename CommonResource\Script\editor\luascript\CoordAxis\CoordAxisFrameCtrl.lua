--声明
local CoordAxisFrameCtrl = Class("CoordAxisFrameCtrl",ClassList["UIBaseCtrl"])

--创建
function CoordAxisFrameCtrl:Create(param)
	print("CoordAxisFrameCtrl:Create:");
	return ClassList["CoordAxisFrameCtrl"].new(param)
end

--初始化
function CoordAxisFrameCtrl:Init(param)
    print("CoordAxisFrameCtrl:Init:");
    self.lBtnDown = false
    self.AbsX = -10
    self.AbsY = -0

	--注册事件
	getglobal("CoordAxisFrame"):setUpdateTime(0.05);
end

--事件处理
function CoordAxisFrameCtrl:OnEvent()
	local ge = GameEventQue:getCurEvent();	
end

--update
function CoordAxisFrameCtrl:OnUpdate()
    self.view.modelView:refreshCoordAxisCamera()

    if self.lBtnDown == true then
        self.view.coordFrame:SetPoint("topright", "", "topright", self.AbsX, self.AbsY)
    end
end

--启动
function CoordAxisFrameCtrl:Start()
    print("CoordAxisFrameCtrl:Start:");
end

--重置
function CoordAxisFrameCtrl:Reset()
    print("CoordAxisFrameCtrl:Reset:");
    self.view:Reset()
    self.lBtnDown = false
    self.AbsX = -10
    self.AbsY = -0
end 

--刷新
function CoordAxisFrameCtrl:Refresh()
    print("CoordAxisFrameCtrl:Refresh:");
    self.view:Refresh()
    self.lBtnDown = false
    self.AbsX = -10
    self.AbsY = -0
end

function CoordAxisFrameCtrl:RotateBtnDown()
    print("CoordAxisFrameCtrl:RotateBtnDown:");
    self.lBtnDown = true
end

function CoordAxisFrameCtrl:RotateViewMoved()
    print("CoordAxisFrameCtrl:RotateViewMoved:");
    local clientWidth, clientHeight = ClientMgr:getClientWindowSize(0, 0)
    local frameRect = self.view.coordFrame:getAbsRect()
    local width = frameRect:getWidth()
    if arg5 == 0 and self.lBtnDown == true then
        self.AbsX = -(clientWidth - arg3) + width / 2
        self.AbsY = arg4
    end
end

function CoordAxisFrameCtrl:RotateBtnUp()
    print("CoordAxisFrameCtrl:RotateBtnUp:");
    self.lBtnDown = false
end