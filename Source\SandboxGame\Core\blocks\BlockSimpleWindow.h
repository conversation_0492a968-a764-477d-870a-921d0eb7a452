#ifndef __BLOCKSIMPLEWINDOW_H__
#define __BLOCKSIMPLEWINDOW_H__

#include "BlockMaterial.h"

class BlockSimpleWindow: public ModelBlockMaterial //tolua_exports
{ //tolua_exports
	DECLARE_BLOCKMATERIAL(BlockSimpleWindow)
public:
	//tolua_begin
	BlockSimpleWindow();
	virtual ~BlockSimpleWindow();
	virtual void init(int resid) override;
	//virtual const char *getGeomName();
	virtual const char *getBaseTexName(char *texname, const BlockDef *def, int &gettextype);
	virtual void onNotify(World *pworld, const WCoord &blockpos, int blockid);
	virtual void dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata = 0, BLOCK_MINE_TYPE droptype = BLOCK_MINE_NOTOOL, float chance = 1.0f, int uin = -1);

	virtual bool canPutOntoPos(WorldProxy* pworld, const WCoord& blockpos) override;
	//virtual BlockDrawType getDrawType() override
	//{
	//	return BLOCKDRAW_XPARENT;
	//}
	virtual SectionMesh *createBlockProtoMesh(int protodata = 0);
	virtual void createBlockMesh(const BuildSectionMeshData& data, const WCoord &blockpos, SectionMesh *poutmesh) override;
	//tolua_end
private:
	virtual void initGeomName() override;
	virtual void initDrawType() override;
}; //tolua_exports

class BlockFridge : public BlockSimpleWindow //tolua_exports
{ //tolua_exports
	DECLARE_BLOCKMATERIAL(BlockFridge)
public:
	virtual void init(int resid) override;
	//tolua_begin
	//virtual BlockDrawType getDrawType() override
	//{
	//	return BLOCKDRAW_OPAQUE;
	//}
	virtual void createBlockMesh(const BuildSectionMeshData& data, const WCoord &blockpos, SectionMesh *poutmesh) override;
	//tolua_end
private:
	virtual int getBlockGeomID(int *idbuf, int *dirbuf, const SectionDataHandler* sectionData, const WCoord &blockpos, World* world);
	virtual void initDrawType() override;
}; //tolua_exports

#endif
