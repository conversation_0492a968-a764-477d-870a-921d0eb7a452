/*
*	file: BlockMineStone
*   矿石上镶嵌宝石
*   renjie 2023/11/19
*/
#ifndef __BLOCK_MINESTONE_H__
#define __BLOCK_MINESTONE_H__

#include "BlockMaterial.h"
#define WORLDTYPENUM 3 //三个星球

class BlockMineStone : public ModelBlockMaterial //tolua_exports
{ //tolua_exports
	DECLARE_BLOCKMATERIAL(BlockMineStone)
public:
	virtual void init(int resid);
	//tolua_begin
	BlockMineStone();
	virtual ~BlockMineStone();
	virtual void createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh);
	virtual void createBlockMeshAngle(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh);
	virtual BlockTexElement* getDestroyTexture(Block pblock, BlockTexDesc& desc) override;
	virtual int getBlockGeomID(int* idbuf, int* dirbuf, const SectionDataHandler* sectionData, const WCoord& blockpos, World* world);
	virtual void createCollideData(CollisionDetect* coldetect, World* pworld, const WCoord& blockpos);
	virtual bool coverNeighbor(int curblockdata, SolidBlockMaterial* neighbor, int neighbor_data, DirectionType dir);
	virtual SectionMesh* createBlockProtoMesh(int protodata = 0);
	virtual RenderBlockMaterial* getFaceMtlForMineStone(int dir);
	virtual void setMaterialStatus(RenderBlockMaterial* m_StoneMtl ,int resid);
	BLOCK_RENDERTYPE_T GetAttrRenderType() const;/* 渲染类型 */
	virtual void setSubMesh(SectionSubMesh* psubmesh,RenderBlockMaterial* rbmtl, DirectionType dir);//设置mesh
	virtual void checkShowFace(const BuildSectionMeshData& data, const WCoord& blockpos, bool* showBuf);
	virtual bool checkRender(const BuildSectionMeshData& data,const WCoord& blockpos, DirectionType dir);
	virtual bool checkRenderForFace(const BuildSectionMeshData& data, const WCoord& blockpos, DirectionType dir);
	virtual BlockDrawType getDrawType() override
	{
		return BLOCKDRAW_OPAQUE;
	}
	//virtual BlockDrawType getDrawType() override { return BLOCKDRAW_STATUE; }
	//tolua_end
protected:
	RenderBlockMaterial* m_Mtl_x;
	RenderBlockMaterial* m_Mtl_y;
	RenderBlockMaterial* m_Mtl_z;
	RenderBlockMaterial* m_StoneMtl; 
	RenderBlockMaterial* m_Mtl_Proto;
}; //tolua_exports

class BlockMineStoneDiffMtl : public BlockMineStone //tolua_exports //钨金钛合金在不同星球上底板不同
{ //tolua_exports
	DECLARE_BLOCKMATERIAL(BlockMineStoneDiffMtl)
public:
	virtual void init(int resid);
	//tolua_begin
	BlockMineStoneDiffMtl();
	virtual ~BlockMineStoneDiffMtl();
	virtual RenderBlockMaterial* getFaceMtlForDiffWorld(int id, int dir);
	virtual SectionMesh* createBlockProtoMesh(int protodata = 0);
	virtual BlockTexElement* getDestroyTexture(Block pblock, BlockTexDesc& desc) override;
	virtual void createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh);
	virtual void createBlockMeshAngle(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh);
	//virtual BlockDrawType getDrawType() override { return BLOCKDRAW_STATUE; }
	//tolua_end
protected:
	RenderBlockMaterial* m_Mtl_xs[WORLDTYPENUM];
	RenderBlockMaterial* m_Mtl_ys[WORLDTYPENUM];
	RenderBlockMaterial* m_Mtl_zs[WORLDTYPENUM];
	RenderBlockMaterial* m_StoneMtls[WORLDTYPENUM];
}; //tolua_exports

class BlockMineStoneRune : public BlockMineStone  //符文矿石
{ 
	DECLARE_BLOCKMATERIAL(BlockMineStoneRune)
public:
	virtual void dropBlockAsItemWithToolId(World* pworld, const WCoord& blockpos, int blockdata, BLOCK_MINE_TYPE droptype, float chance, int useToolId, int uin = -1) override;
	void spawnMonster(World* pworld, const WCoord& basepos);
};



#endif