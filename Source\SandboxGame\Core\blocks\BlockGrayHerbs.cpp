﻿
#include "BlockGrayHerbs.h"
#include "BlockMaterialMgr.h"
#include "section.h"
#include "chunk.h"
#include "SectionMesh.h"
#include "BlockGeom.h"
#include "Ecosystem.h"
#include "Collision.h"
#include "WorldManager.h"
#include "WeatherManager.h"
//#include "OgreMaterial.h"
#include "Graphics/Texture.h"
#include "world.h"
#include "special_blockid.h"
#include "WorldManager.h"
#include "TemperatureManager.h"
#include "LuaInterfaceProxy.h"
#include "ClientMob.h"
#include "ActorLocoMotion.h"
#include "EffectManager.h"

using namespace MINIW;
IMPLEMENT_BLOCKMATERIAL(HerbMaterial)
IMPLEMENT_BLOCKMATERIAL(GrayHerbMaterial)

float getPlantGrowRate(World *pworld, const WCoord &blockpos,int resid)
{
    float rate = 1.0f;
    int x = blockpos.x;
    int y = blockpos.y;
    int z = blockpos.z;

    int blockid1 = pworld->getBlockID(x, y, z - 1);
    int blockid2 = pworld->getBlockID(x, y, z + 1);
    int blockid3 = pworld->getBlockID(x - 1, y, z);
    int blockid4 = pworld->getBlockID(x + 1, y, z);
    int blockid5 = pworld->getBlockID(x - 1, y, z - 1);
    int blockid6 = pworld->getBlockID(x + 1, y, z - 1);
    int blockid7 = pworld->getBlockID(x + 1, y, z + 1);
    int blockid8 = pworld->getBlockID(x - 1, y, z + 1);
    bool adj_x = blockid3 == resid || blockid4 == resid;
    bool adj_z = blockid1 == resid || blockid2 == resid;
    bool corner = blockid5 == resid || blockid6 == resid || blockid7 == resid || blockid8 == resid;
	
	bool isWinterFlowerSeed = (resid == BLOCK_WINTER_FLOWER_SEED);
    for(int i = x - 1; i <= x + 1; ++i)
    {
        for(int j = z - 1; j <= z + 1; ++j)
        {
            int downid = pworld->getBlockID(i, y - 1, j);
            float tmprate = 0.0f;

            if(downid == BLOCK_FARMLAND || downid == BLOCK_BURYLAND || downid == BLOCK_FARMLAND_PIT || downid == BLOCK_FARMLAND_RED)
            {
                tmprate = 1.0f;

                if(pworld->getBlockData(i, y - 1, j) > 0) //wet 湿润
                {
                    tmprate = 3.0f;
                }
            }
			else if(downid == BLOCK_GRASS_WOOD_GRAY_FARMLAND)//草木灰耕地土块 
			{
                tmprate = 5.0f;
            }
			else if (downid == BLOCK_DIRT_FREEZE_PIT)//冻土
			{
				if (isWinterFlowerSeed) //冬温花特殊
				{
					tmprate = 1.0f;
				}
			}

            if (i != x || j != z)
            {
                tmprate /= 4.0f;
            }

            rate += tmprate;
        }
    }

    if(corner || adj_x && adj_z)
    {
        rate /= 2.0f;
    }

    return rate;
}

void HerbMaterial::init(int resid)
{
	Super::init(resid);
	//属性初始化
	SetToggle(BlockToggle_IsSolid, false);
	m_nSpecialLogicType[0] |= BlockMaterial::BlockSpceialLogicTeam0::BlockFaceToPlane;
	m_nSpecialLogicType[0] |= BlockMaterial::BlockSpceialLogicTeam0::BranchNotLink;
}

//bool HerbMaterial::isSolid()
//{
//	return false;
//}


void HerbMaterial::onNotify(World *pworld, const WCoord &blockpos, int blockid)
{
	BlockMaterial::onNotify(pworld, blockpos, blockid);

	checkHerbChange(pworld, blockpos);
}

void HerbMaterial::blockTick(World *pworld, const WCoord &blockpos)
{
	checkHerbChange(pworld, blockpos);
}

bool HerbMaterial::canPutOntoPos(WorldProxy *pworld, const WCoord &blockpos)
{
	if(!BlockMaterial::canPutOntoPos(pworld, blockpos)) return false;
	if(!canThisPlantGrowOnThisBlockID(pworld->getBlockID(DownCoord(blockpos)))) return false;

	for(int i=1; i<GetBlockDef()->Height; i++)
	{
		if(!BlockMaterial::canPutOntoPos(pworld, blockpos+WCoord(0,i,0))) return false;
	}

	return true;
}

bool HerbMaterial::canStayOnPos(WorldProxy *pworld, const WCoord &blockpos)
{
	if(GetBlockDef()->Height > 1)
	{
		if(pworld->getBlockID(DownCoord(blockpos)) == getBlockResID()) return true;
		if(pworld->getBlockID(TopCoord(blockpos)) != getBlockResID()) return false;
	}

	//return (pworld->getFullBlockLightValue(blockpos)>=8 || pworld->canBlockSeeTheSky(blockpos.x,blockpos.y,blockpos.z)) && canThisPlantGrowOnThisBlockID(pworld->getBlockID(DownCoord(blockpos)));
	return canThisPlantGrowOnThisBlockID(pworld->getBlockID(DownCoord(blockpos)));
}

bool HerbMaterial::canThisPlantGrowOnThisBlockID(int blockid)
{
	if (blockid == BLOCK_WINTER_FLOWER_SEED)
		return blockid == BLOCK_DIRT_FREEZE_PIT;
	return blockid==BLOCK_GRASS || blockid==BLOCK_DIRT || blockid==BLOCK_FARMLAND || blockid == BLOCK_FARMLAND_RED || blockid==BLOCK_SULPHURROCK || blockid==BLOCK_BURYLAND || blockid == BLOCK_FARMLAND_PIT||blockid == BLOCK_GRASS_WOOD_GRAY_FARMLAND || blockid==BLOCK_DIRT_FREEZE || blockid == BLOCK_DIRT_FREEZE_PIT;
}

void HerbMaterial::dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata/* =0 */, BLOCK_MINE_TYPE droptype/* =BLOCK_MINE_NOTOOL */, float chance/* =1.0f */, int uin/* = -1 */)
{
	if(GetBlockDef()->Height > 1)
	{
		if((blockdata&8) != 0) return;
	}

	BlockMaterial::dropBlockAsItem(pworld, blockpos, blockdata, droptype, chance, uin);
}

void HerbMaterial::checkHerbChange(World *pworld, const WCoord &blockpos)
{
	TriggerBlockAddRemoveDisable Tmp(pworld);

	if(!canStayOnPos(pworld->getWorldProxy(), blockpos))
	{
		dropBlockAsItem(pworld, blockpos, pworld->getBlockData(blockpos));
		pworld->setBlockAll(blockpos, 0, 0, 3);
	}
}

float HerbMaterial::getGrowRate(World *pworld, const WCoord &blockpos)
{
	bool isTemperatureActive = pworld->GetWorldMgr()->getTemperatureMgr()->GetTemperatureActive();
	float rate = getPlantGrowRate(pworld,blockpos,getBlockResID());
	int defLevel = GetBlockDef()->GrowthTempRange;
	if (defLevel != TEMPERATURE_LEVEL_NONE && isTemperatureActive)
	{
		float temp = 0.0f;
		int posLevel = TEMPERATURE_LEVEL_NONE;
		pworld->GetWorldMgr()->getTemperatureMgr()->GetBlockTemperatureAndLevel(pworld, blockpos, temp, posLevel);
		float factorRateMin = GetLuaInterfaceProxy().get_lua_const()->growth_rate_min / 100;
		float factor = (1.0f - ((float)Rainbow::Abs(posLevel - defLevel) / 6)) * (1.0f - factorRateMin) + factorRateMin;
		if (factor == 0)
			factor = 1;
		rate *= factor;
	}
	return rate;
}

bool HerbMaterial::dealNewGrow(World* pworld, const WCoord& blockpos, int dataindex,int maxdata)
{
	const BlockDef* def = GetBlockDef();
	if (!def ||!pworld || def->CropsSign == 0 || def->GrowthTimeNum == 0)
	{
		return false;
	}
	int blockdata = pworld->getBlockData(blockpos);
	WorldManager* worldmanager = pworld->GetWorldMgr();
	if (worldmanager &&blockdata < maxdata)
	{
		int plantTime = worldmanager->getPlantTime(blockpos, pworld->getCurMapID());
		if (plantTime < 0)
		{
			return false;
		}
		int factor = getTemperatureFactor(pworld, blockpos);
		int downid = pworld->getBlockID(blockpos.x, blockpos.y - 1, blockpos.z);
		int curindex = (blockdata * def->GrowthTimeNum) / maxdata;
		int rate = 0;
		if (curindex < def->GrowthTimeNum)
		{
			rate=def->GrowthTime[curindex];
		}
		if ((curindex * def->GrowthTimeNum) / maxdata < blockdata)
		{
			curindex += 1;
		}
		int alltime = 0;
		for (int i = 0; i < def->GrowthTimeNum; ++i)
		{
			alltime += def->GrowthTime[i];
		}
		int realrate = rate;
		int realAllTime = alltime;
		int subrate = getBlockGrowSpeedUpTime(pworld, blockpos, rate);
		int suballtime = getBlockGrowSpeedUpTime(pworld, blockpos, alltime);;
		realrate -= subrate;
		realAllTime -= suballtime;
		subrate = getActorGrowSpeedUpTime(pworld, blockpos, rate);
		suballtime = getActorGrowSpeedUpTime(pworld, blockpos, alltime);;
		realrate -= subrate;
		realAllTime -= suballtime;
		realrate = realrate * factor;
		realAllTime = realAllTime * factor;
		int worldtime = worldmanager->getDayNightTime();
		//转化成小时
		int timelenth = worldtime - plantTime;
		int time = (timelenth * 1.0) / TICKS_ONEDAY * 24.0;
		int FertilizedUpTime = worldmanager->getFertilizedUpTime(blockpos, pworld->getCurMapID());
		FertilizedUpTime = ((FertilizedUpTime * 24.0) / TICKS_ONEDAY);
		time += FertilizedUpTime;
		if (time >= realAllTime)
		{
			blockdata = maxdata;
			pworld->setBlockData(blockpos, blockdata, dataindex);
			return true;
		}
		else
		{
			for (int i = 0; i < curindex; ++i)
			{
				time -= def->GrowthTime[i];
			}
			
			if (time >= realrate)
			{
				curindex = curindex + 1;

				int blockdata = ((curindex * 1.0) * maxdata) / def->GrowthTimeNum;
				if (blockdata > maxdata || (curindex == def->GrowthTimeNum && blockdata < maxdata))
				{
					blockdata = maxdata;
				}
				pworld->setBlockData(blockpos, blockdata, dataindex);
				return true;
			}
		}

	}
	return false;
}

bool  HerbMaterial::dealFertilized(World* pworld, const WCoord& blockpos, int itemid)
{
	const BlockDef* def = GetBlockDef();
	if (!def || !pworld || def->CropsSign == 0 || def->GrowthTimeNum == 0)
	{
		return false;
	}
	WorldManager* worldmanager = pworld->GetWorldMgr();
	if(worldmanager)
	{
		int plantTime = worldmanager->getPlantTime(blockpos, pworld->getCurMapID());
		int blockdata = pworld->getBlockData(blockpos);
		double rate = 0;
		MINIW::ScriptVM::game()->callFunction("GetFertilizerValueByItemId", "i>d", itemid, &rate);
		rate *= (TICKS_ONEDAY / 24.0);
		worldmanager->addFertilizedUpTime(blockpos, pworld->getCurMapID(), rate);
		FertilizedPlayEffect(pworld, blockpos);
		blockTick(pworld, blockpos);
		return true;
	}
	return false;
}
void HerbMaterial::FertilizedPlayEffect(World* pworld, const WCoord& blockpos)
{
	pworld->getEffectMgr()->playParticleEffectAsync("particles/zhiwushenzhang.ent", BlockCenterCoord(blockpos), 200, 0, 0, true);
}

 int  HerbMaterial::getMaxBlockdata()
{
	 return 7;
}
 int HerbMaterial::getTemperatureFactor(World* pworld, const WCoord& blockpos)
 {
 
	 bool isTemperatureActive = pworld->GetWorldMgr()->getTemperatureMgr()->GetTemperatureActive();
	 int defLevel = GetBlockDef()->GrowthTempRange;
	 int factor = 0;
	 if (defLevel != TEMPERATURE_LEVEL_NONE && isTemperatureActive)
	 {
		 int posLevel = TEMPERATURE_LEVEL_NONE;
		 float temp = 0.0f;
		 pworld->GetWorldMgr()->getTemperatureMgr()->GetBlockTemperatureAndLevel(pworld, blockpos, temp, posLevel);
		 float factorRateMin = GetLuaInterfaceProxy().get_lua_const()->growth_rate_min / 100;
		 factor = (1.0f - ((float)Rainbow::Abs(posLevel - defLevel) / 6)) * (1.0f - factorRateMin) + factorRateMin;
	 }
	 if (factor == 0)
	 {
		 factor = 1;
	 }
	 return factor;
 }

 int HerbMaterial::getBlockGrowSpeedUpTime(World* pworld, const WCoord& blockpos, int time)
 {
	 int result = 0;
	 int downid = pworld->getBlockID(blockpos.x, blockpos.y - 1, blockpos.z);
	 BlockDef* downdef = GetDefManagerProxy()->getBlockDef(downid);
	 if (downid == BLOCK_FARMLAND || downid == BLOCK_BURYLAND || downid == BLOCK_FARMLAND_PIT || downid == BLOCK_FARMLAND_RED)
	 {

		 if (pworld->getBlockData(blockpos.x, blockpos.y - 1, blockpos.z) > 0) //wet 湿润
		 {
			 result = 0.1 * time;

		 }
	 }
	 else if (downid == BLOCK_GRASS_WOOD_GRAY_FARMLAND) //草木灰土地
	 {
		 result = 0.1 * time;
	 }
	 return result;
 }
 int HerbMaterial::getActorGrowSpeedUpTime(World* pworld, const WCoord& blockpos, int time)
 {
	 //稻草人加速
	 int result = 0;
	 float rangexz = 4;
	 CollideAABB box;
	 box.pos = blockpos * BLOCK_SIZE;
	 box.dim = WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE);
	 box.expand((int)(rangexz * BLOCK_SIZE), 2 * BLOCK_SIZE, (int)(rangexz * BLOCK_SIZE));
	 std::vector<IClientActor*>actors;;
	 pworld->getActorsOfTypeInBox(actors, box, OBJ_TYPE_MONSTER);
	 int speedUpMonsterID = 3121;
	 WCoord pos = BlockBottomCenter(blockpos);
	 for (size_t i = 0; i < actors.size(); i++)
	 {
		 ClientMob* mob = static_cast<ClientMob*>(actors[i]);
		 if (mob->getDef()->ID == speedUpMonsterID)
		 {
			 WCoord vec = mob->getLocoMotion()->getPosition() - pos;
			 float dist = vec.length();
			 if (dist < rangexz * BLOCK_SIZE)
			 {
				 result = 0.1 * time;
				 break;
			 }
		 }
	 }
	 return result;
 }


//草木灰上的植物移除的时候 检测耐力是否耗尽 变成普通土块
void HerbMaterial::onBlockDestroyedBy(World *pworld, const WCoord &blockpos, int blockdata, BLOCK_DESTROY_REASON_T destroytype, IClientActor *bywho)
{
    //获取下面块
    WCoord downpos = DownCoord(blockpos);
    int blockid = pworld->getBlockID(downpos);
    if (blockid == BLOCK_GRASS_WOOD_GRAY_FARMLAND)//如果是种在草木灰耕地上
    {//消耗次数
        //return false;
        int blockdata = pworld->getBlockData(downpos);
        if(blockdata == 0)
        {
            pworld->setBlockAll(downpos, BLOCK_DIRT, 0);//变土块
        }else{
            //消耗放HerbMaterial::onBlockPlacedBy会有问题,因为使用种子的时候,不会触发onBlockPlacedBy
            pworld->setBlockData(downpos, blockdata-1, 0);//草木灰耕地上 栽了植物后,会消耗次数
        }
    }
}

//--------------------------------------------------------------------------------------------------------------------------
GrayHerbMaterial::GrayHerbMaterial() : m_BottomMtl(NULL), m_TopMtl(NULL), m_SnowMtl(NULL), m_TopSnowCoverMtl(NULL), m_BottomSnowCoverMtl(NULL), m_WhiteMtl(NULL)
{

}

GrayHerbMaterial::~GrayHerbMaterial()
{
	ENG_RELEASE(m_BottomMtl);
	ENG_RELEASE(m_TopMtl);
	ENG_RELEASE(m_SnowMtl);
	ENG_RELEASE(m_WhiteMtl);
	ENG_RELEASE(m_TopSnowCoverMtl);
	ENG_RELEASE(m_BottomSnowCoverMtl);
}

//const char *GrayHerbMaterial::getGeomName()
//{
//	return "hurbs";
//}

void GrayHerbMaterial::init(int resid)
{
	Super::init(resid);
	m_nSpecialLogicType[0] |= BlockMaterial::BlockSpceialLogicTeam0::BlockFaceToPlane;
	m_nSpecialLogicType[0] |= BlockMaterial::BlockSpceialLogicTeam0::BranchNotLink;
	m_nSpecialLogicType[0] |= BlockMaterial::BlockSpceialLogicTeam0::IsRoundBlock;
	SetToggle(BlockToggle_RandomTick, true);
	if(m_LoadOnlyLogic) return;

	bool doublelayer = false;
	char texname[256];

	m_BottomMtl = g_BlockMtlMgr.createRenderMaterial(GetBlockDef()->Texture1.c_str(), GetBlockDef(), GETTEX_NORMAL, BLOCKDRAW_GRASS);
	m_BottomSnowCoverMtl = g_BlockMtlMgr.createRenderMaterial(GetBlockDef()->Texture1.c_str(), GetBlockDef(), GETTEX_NORMAL, BLOCKDRAW_SNOWCOVER_GRASS);
	m_SnowMtl = g_BlockMtlMgr.createRenderMaterial("snow", m_Def, GETTEX_WITHDEFAULT, getDrawType(), 0);
	sprintf(texname, "%s_white", GetBlockDef()->Texture1.c_str());
	m_WhiteMtl = g_BlockMtlMgr.createRenderMaterial(texname, GetBlockDef(), GETTEX_NORMAL, BLOCKDRAW_GRASS);
	if(m_BottomMtl == NULL)
	{
		
		m_BottomMtl = g_BlockMtlMgr.createRenderMaterial(texname, GetBlockDef(), GETTEX_WITHDEFAULT, BLOCKDRAW_GRASS);
		doublelayer = true;
	}
	if (m_BottomSnowCoverMtl == NULL)
	{
		sprintf(texname, "%s_bottom", GetBlockDef()->Texture1.c_str());
		m_BottomSnowCoverMtl = g_BlockMtlMgr.createRenderMaterial(texname, GetBlockDef(), GETTEX_WITHDEFAULT, BLOCKDRAW_SNOWCOVER_GRASS);
		doublelayer = true;
	}

	if(doublelayer)
	{
		sprintf(texname, "%s_top", GetBlockDef()->Texture1.c_str());
		m_TopMtl = g_BlockMtlMgr.createRenderMaterial(texname, GetBlockDef(), GETTEX_WITHDEFAULT, BLOCKDRAW_GRASS);
		m_TopSnowCoverMtl = g_BlockMtlMgr.createRenderMaterial(texname, GetBlockDef(), GETTEX_WITHDEFAULT, BLOCKDRAW_SNOWCOVER_GRASS);
	}
	if (m_WhiteMtl==NULL)
	{
		m_WhiteMtl = m_BottomMtl;
		m_WhiteMtl->AddRef();
	}


	if (g_BlockMtlMgr.IsUseVertexAnimationEffect(resid))
	{
		AppendBlockEffect(BLOCKEFFECT_VERTEX_ANIMATION);
		if (m_BottomMtl)
			m_BottomMtl->SetBlockEffectFlag(BLOCKEFFECT_VERTEX_ANIMATION);
		if (m_TopMtl)
			m_TopMtl->SetBlockEffectFlag(BLOCKEFFECT_VERTEX_ANIMATION);
		if (m_TopSnowCoverMtl)
			m_TopSnowCoverMtl->SetBlockEffectFlag(BLOCKEFFECT_VERTEX_ANIMATION);
		if (m_BottomSnowCoverMtl)
			m_BottomSnowCoverMtl->SetBlockEffectFlag(BLOCKEFFECT_VERTEX_ANIMATION);
		if (m_WhiteMtl)
			m_WhiteMtl->SetBlockEffectFlag(BLOCKEFFECT_VERTEX_ANIMATION);

	}

	if (g_BlockMtlMgr.IsUseVertexSnowCoverEffect(resid))
	{
		if (m_TopSnowCoverMtl)
			m_TopSnowCoverMtl->SetBlockEffectFlag(BLOCKEFFECT_VERTEX_SNOW);
		if (m_BottomSnowCoverMtl)
			m_BottomSnowCoverMtl->SetBlockEffectFlag(BLOCKEFFECT_VERTEX_SNOW);
	}
}

void GrayHerbMaterial::setPlantSnowCoverThickness(Rainbow::Vector4f value)
{
	if (m_PlantSnowCoverThickness != value)
	{
		m_PlantSnowCoverThickness = value;
		if (m_BottomSnowCoverMtl)
			m_BottomSnowCoverMtl->setPlantSnowCoverThickness(value);
		if (m_TopSnowCoverMtl)
			m_TopSnowCoverMtl->setPlantSnowCoverThickness(value);
	}
}

void GrayHerbMaterial::BlendBlockVertexColor(const Rainbow::Vector4f& blockWorldPos, BlockColor& vertexColor)
{
	//only grass enable random color
	auto blockid = this->getBlockResID();
	if (blockid != BLOCK_TALL_GRASS&& blockid!= 238/*荆棘草*/) return;
	//小草的顶点色采样noise值
	g_BlockMtlMgr.BlendGrassVertexColor(blockWorldPos, vertexColor);
}



void GrayHerbMaterial::update(unsigned int dtick)
{
	HerbMaterial::update(dtick);
	if (IsBlockEffectDirty())
	{
		bool success = false;
		bool enableVertexAnimation = HasBlockEffect(BLOCKEFFECT_VERTEX_ANIMATION);
		bool enableVertexsnow = HasBlockEffect(BLOCKEFFECT_VERTEX_SNOW);
		if (m_BottomMtl) 
		{
			success |= m_BottomMtl->SetBlockEffectEnable(BLOCKEFFECT_VERTEX_ANIMATION, enableVertexAnimation);
		}
		if (m_BottomSnowCoverMtl)
		{
			success |= m_BottomSnowCoverMtl->SetBlockEffectEnable(BLOCKEFFECT_VERTEX_ANIMATION, enableVertexAnimation);
			success |= m_BottomSnowCoverMtl->SetBlockEffectEnable(BLOCKEFFECT_VERTEX_SNOW, enableVertexsnow);
		}
		if (m_TopMtl) 
		{
			success |= m_TopMtl->SetBlockEffectEnable(BLOCKEFFECT_VERTEX_ANIMATION, enableVertexAnimation);
		}
		if (m_TopSnowCoverMtl)
		{
			success |= m_TopSnowCoverMtl->SetBlockEffectEnable(BLOCKEFFECT_VERTEX_ANIMATION, enableVertexAnimation);
			success |= m_TopSnowCoverMtl->SetBlockEffectEnable(BLOCKEFFECT_VERTEX_SNOW, enableVertexsnow);
		}
		if (m_WhiteMtl)
		{
			success |= m_WhiteMtl->SetBlockEffectEnable(BLOCKEFFECT_VERTEX_ANIMATION, enableVertexAnimation);
		}
		if (success) 
		{
			SetBlockEffectDirty(false);
		}
	}
}

void GrayHerbMaterial::blockTick(World* pworld, const WCoord& blockpos)
{
	int blockId = pworld->getBlockID(blockpos);
	if (blockId == BLOCK_TALL_GRASS)
	{
		int blockdata = pworld->getBlockData(blockpos);
		if (pworld && pworld->getWeatherMgr())
		{
			int weather = pworld->getWeatherMgr()->getWeather(blockpos);
			if ((blockdata & 4) == 0 && (weather == GROUP_BLIZZARD_WEATHER || weather == GROUP_SNOW_WEATHER))
			{
				pworld->setBlockAll(blockpos, blockId, blockdata | 4, 2);//覆雪
			}
		}
	}
}


void GrayHerbMaterial::initGeomName()
{
	//m_geomName = "hurbs";
	m_geomName = "agave";
	//todo load lod mesh

}
BlockTexElement *GrayHerbMaterial::getDestroyTexture(Block pblock, BlockTexDesc &desc)
{
	desc.blendmode = BLEND_ALPHATEST;
	desc.gray = true;

	RenderBlockMaterial *mtl = (pblock.getData()!=0 && m_TopMtl!=NULL) ? m_TopMtl:m_BottomMtl;
	return mtl->getTexElement();
}


void GrayHerbMaterial::createBlockMesh(const BuildSectionMeshData& data, const WCoord &blockpos, SectionMesh *poutmesh)
{
	BlockGeomTemplate* geom = getGeom(data.m_LODLevel);
	if (!geom) return;

	auto psection = data.m_SharedSectionData;
	Block pblock = psection->getBlock(blockpos);
	int curblockdata = pblock.getData();
	bool isSnowing = false;
	if ((curblockdata&4)==4)
	{
		if (data.m_SectionMesh!=NULL)
		{
			isSnowing = true;
		}
	}
	Ecosystem* biome = psection->getBiomeGenOnJob(data.m_World, data.m_SharedChunkData, blockpos);
	BlockColor vertcolor(255, 255, 255, 0);
	if (biome)
		vertcolor = biome->getGrassColor();
	bool top_layer = false;
	float tx, ty, tz;
	float angle = 7.0f;
	float scale = 1.0f;
	const float max_scale = 1.4f;
	const float min_scale = 0.6f;
	if (m_RenderInPot == 0)
	{
		ChunkRandGen chunkrand;
		WCoord wpos = psection->getOrigin() + blockpos;
		WCoordHashCoder coder;
		chunkrand.setSeed(coder(wpos));

		tx = (chunkrand.getFloat() - chunkrand.getFloat()) * 0.4f;
		tz = (chunkrand.getFloat() - chunkrand.getFloat()) * 0.4f;
		ty = 0;
		angle = (chunkrand.getFloat()) * 360;
		scale = min_scale + (chunkrand.getFloat()) * (max_scale - min_scale);
		if (m_TopMtl)
		{
			Block pdownblock = psection->getNeighborBlock(blockpos, DIR_NEG_Y);
			if (pdownblock.getResID() == m_BlockResID) top_layer = true;
		}
	}
	else
	{
		if (m_TopMtl && m_RenderInPot == 2) top_layer = true;
		tx = tz = 0.0f;
		ty = m_RenderPotOffsetY;
	}
	Rainbow::ColorRGBAf verts_light[1] = { Rainbow::ColorRGBAf::white };
	psection->getBlockVertexLight(blockpos, verts_light);
	RenderBlockMaterial* pmtl = NULL;
	if (isSnowing == true)
	{
		pmtl = m_WhiteMtl;
		vertcolor.r = 255;
		vertcolor.g = 255;
		vertcolor.b = 255;
		vertcolor.a = 0;
	}
	else
	{
		pmtl = top_layer ? m_TopMtl : m_BottomMtl;
	}
	SectionSubMesh* psubmesh = poutmesh->getSubMesh(pmtl, false, data.m_LODLevel);
	if (!psubmesh) return;
	//psubmesh->m_FarHide = true;

	BlockGeomMeshInfo meshinfo;
	//getGeom()->getModelFaceVerts(meshinfo, 0, DIR_NEG_Z, tx, ty, tz);
	//uvdir 从1改成0,因为1会走到uv缩放的逻辑，导致物品显示错乱;
	geom->getGrassModelFaceVerts(meshinfo, 0, angle > 7 ? angle : 7, scale, 0, tx, ty, tz);
	psubmesh->addGeomBlockLight(meshinfo, &blockpos, verts_light, &vertcolor, pmtl->getUVTile(), this);
	
	if (isSnowing == true)
	{
		RenderBlockMaterial* pmtl_snow = m_SnowMtl;
		SectionSubMesh* psubmesh_snow = poutmesh->getSubMesh(pmtl_snow, false, data.m_LODLevel);
		if (!psubmesh_snow) return;
		BlockGeomMeshInfo meshinfo_snow;
		geom->getFaceVerts(meshinfo_snow, 1, 1.0f, 0, 0, 0);
		psubmesh_snow->addGeomBlockLight(meshinfo_snow, &blockpos, verts_light, NULL, pmtl_snow->getUVTile());
	}
}

int GrayHerbMaterial::getBlockGeomID(int* idbuf, int* dirbuf, const SectionDataHandler* sectionData, const WCoord& blockpos, World* world)
{
	int data = sectionData->getBlock(blockpos).getData();
	if ((data&4)==4)
	{
		data = data & 9;//覆雪的话需要还原data，9->1001
	}
	idbuf[0] = data;
	int returnNegom = 1;
	/*idbuf[1] = 1;
	returnNegom = 2;
	dirbuf[1] = DIR_NEG_Z;*/
	
	int meshsize = getGeom(0)->getMeshCount();
	if (data >= meshsize) {
		idbuf[0] = 0;
	}
	dirbuf[0] = DIR_NEG_Z;
	return returnNegom;
}

int GrayHerbMaterial::getProtoBlockGeomID(int* idbuf, int* dirbuf)
{
	idbuf[0] = 0;
	dirbuf[0] = DIR_NEG_Z;
	return 1;
}

SectionMesh* GrayHerbMaterial::createBlockProtoMesh(int protodata)
{
	SectionMesh* pmesh = ENG_NEW(SectionMesh)();
	RenderBlockMaterial* pmtl = m_BottomMtl;
	SectionSubMesh* psubmesh = pmesh->getSubMesh(pmtl,true);

	BlockGeomMeshInfo meshinfo;

	getGeom(0)->getFaceVerts(meshinfo, 0);
	psubmesh->addGeomFaceLight(meshinfo, NULL, s_DefaultFaceVertexLights, NULL);
	//psubmesh->addTriangleList(meshinfo.vertices.data(), meshinfo.vertices.size(), meshinfo.indices.data(), meshinfo.indices.size(), nullptr);

	return pmesh;
}
void GrayHerbMaterial::createCollideData(CollisionDetect *coldetect, World *pworld, const WCoord &blockpos)
{
	WCoord pos = blockpos*BLOCK_SIZE;
	int step = BLOCK_SIZE/6;
	coldetect->addObstacle(pos+WCoord(step,0,step), pos+WCoord(BLOCK_SIZE-step, BLOCK_SIZE-step*2, BLOCK_SIZE-step));
}
