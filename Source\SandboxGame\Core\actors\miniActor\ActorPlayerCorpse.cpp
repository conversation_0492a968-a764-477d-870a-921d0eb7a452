#include "ActorPlayerCorpse.h"
#include "LuaInterfaceProxy.h"
#include "DefManagerProxy.h"
#include "ClientActorManager.h"
#include "ObserverEventManager.h"
#include "WorldManager.h"
#include "SandBoxManager.h"
#include "ClientActorHelper.h"
#include "SoundComponent.h"
#include "EffectComponent.h"
#include "TemperatureComponent.h"
#include "ActorBody.h"
#include "SandboxIdDef.h"
//#include "ItemManager.h"
//#include "UIManager.h"
#include "MpActorManager.h"
#include "PlayerControl.h"
#include "DynamicContainer.h"
#include "GameAnalytics.h"
#include "json/jsonxx.h"
#include "PlayerAttrib.h"

IMPLEMENT_SCENEOBJECTCLASS(ActorPlayerCorpse)

ActorPlayerCorpse::ActorPlayerCorpse() 
    : m_PlayerUin(0)
    , m_PlayerName("")
    , m_PlayerLevel(1)
    , m_PlayerModelIndex(1)
    , m_HairId(0)
    , m_HairColor(0)
    , m_FaceId(0)
    , m_CustomSkins("")
    , m_CorpseBackpack(nullptr)
    , m_DecayTicks(6000)   // 默认6000个tick (大约5分钟，假设每秒20个tick)
    , m_TickCount(0)
    , m_FadeStartTick(0)
    , m_IsLooted(false)
    , m_IsFading(false)
    , m_LootListener(nullptr)
    , m_ItemTakenListener(nullptr)
    , m_CurrentLooterID(0)
	,m_DynamicContainer(nullptr)
{
	// 初始化运动向量为0
	m_Motion = Vector3f(0.0f, 0.0f, 0.0f);
	m_OnGroundTime = 0;
    // 创建尸体背包
    //m_CorpseBackpack = new BackPack(20); // 20格物品栏
    
    // 创建必要的组件
    //CreateComponent<EffectComponent>("EffectComponent");
    //CreateComponent<TimeComponent>("TimeComponent");
    //CreateComponent<SoundComponent>("SoundComponent");
    
    // 设置为可交互的NPC
    //setInteractable(true);
    

    init();
    // 初始化事件系统
    createEvent();
}

ActorPlayerCorpse::~ActorPlayerCorpse()
{
    ENG_DELETE(m_Body);
    ENG_DELETE(m_DynamicContainer);
    //// 清理背包
    //if (m_CorpseBackpack)
    //{
    //    delete m_CorpseBackpack;
    //    m_CorpseBackpack = nullptr;
    //}
    //
    //// 清理事件监听器
    //if (m_LootListener)
    //{
    //    Event2().UnSubscribe("LootCorpse", m_LootListener);
    //    m_LootListener = nullptr;
    //}
    //
    //if (m_ItemTakenListener)
    //{
    //    Event2().UnSubscribe("TakeItemFromCorpse", m_ItemTakenListener);
    //    m_ItemTakenListener = nullptr;
    //}
}

void ActorPlayerCorpse::createEvent()
{
    //// 尸体拾取事件
    //typedef ListenerFunctionRef<ClientPlayer*> ListenerLootCorpse;
    //ListenerLootCorpse* listenerLootCorpse = SANDBOX_NEW(ListenerLootCorpse, [&](ClientPlayer* player) -> void {
    //    if (player && canLootCorpse(player))
    //    {
    //        openCorpseLoot(player);
    //    }
    //});
    //m_LootListener = listenerLootCorpse;
    //Event2().Subscribe("LootCorpse", listenerLootCorpse);
    //
    //// 从尸体拿取物品事件
    //typedef ListenerFunctionRef<ClientPlayer*, int, int> ListenerTakeItem;
    //ListenerTakeItem* listenerTakeItem = SANDBOX_NEW(ListenerTakeItem, [&](ClientPlayer* player, int slotIndex, int count) -> void {
    //    if (player && canLootCorpse(player))
    //    {
    //        TakeItemFromCorpse(player, slotIndex, count);
    //    }
    //});
    //m_ItemTakenListener = listenerTakeItem;
    //Event2().Subscribe("TakeItemFromCorpse", listenerTakeItem);
}

ActorPlayerCorpse* ActorPlayerCorpse::CreateFromPlayer(ClientPlayer* player)
{
    if (!player)
        return nullptr;
        
    World* world = player->getWorld();
    if (!world)
        return nullptr;
        
    // 创建尸体实例
    ActorManager* actorMgr = dynamic_cast<ActorManager*>(world->getActorMgr());
    if (!actorMgr)
        return nullptr;
        
    ActorPlayerCorpse* corpse = SANDBOX_NEW(ActorPlayerCorpse);
    if (!corpse)
        return nullptr;
        
    // 设置位置和朝向（略微抬高以防止陷入地面）
    WCoord playerPos = player->getPosition();
    WCoord corpsePos = playerPos;
    corpsePos.y += 100; // 微调高度
    
    // 使用ActorManager来生成Actor并添加到世界
    actorMgr->spawnActor(corpse, corpsePos.x, corpsePos.y, corpsePos.z, 
                         player->getLocoMotion()->m_RotateYaw, 0.0f);
    
    // 保存玩家基本信息
    corpse->m_PlayerUin = player->getUin();
    //corpse->m_PlayerName = player->getDisplayName();
    //corpse->m_PlayerLevel = player->getLevel();
    
    jsonxx::Array arr;

// 设置尸体存储箱格子数量为30
	if (corpse->m_DynamicContainer)
	{
		int currentSlot = 0; // 跟踪当前正在使用的尸体存储槽位

		// 从玩家获取背包对象
		BackPack* playerBackpack = player->getBackPack();
		if (playerBackpack)
		{
			// 定义要检查的容器索引数组
			int containerIndices[] = {
				EQUIP_START_INDEX,      // 装备栏 (优先复制装备)
				BACKPACK_START_INDEX,   // 主背包
				SHORTCUT_START_INDEX,   // 快捷栏
				SHORTCUTEX_START_INDEX, // 扩展快捷栏
				EXT_BACKPACK_START_INDEX // 扩展背包
			};

			// 遍历所有容器类型
			for (int containerIndex : containerIndices)
			{
				// 获取当前容器
				PackContainer* container = playerBackpack->getPack(containerIndex);
				if (container)
				{
					// 遍历容器中的所有格子
					for (size_t i = 0; i < container->m_Grids.size(); i++)
					{
						// 如果玩家格子非空且我们的尸体存储还有空间
						if (!container->m_Grids[i].isEmpty())
						{
							// 复制物品到尸体存储箱
							corpse->m_DynamicContainer->SetGrid(currentSlot, container->m_Grids[i]);

                            jsonxx::Object item_obj;
                            item_obj << "item_id" << container->m_Grids[i].getItemID();
                            item_obj << "item_count" << container->m_Grids[i].getNum();
                            arr.import(item_obj);

							// 从玩家背包中移除物品
							BackPackGrid emptyGrid;
							emptyGrid.reset(container->m_Grids[i].getIndex());
							container->m_Grids[i] = emptyGrid;

							// 通知背包格子变化
							container->afterChangeGrid(container->m_Grids[i].getIndex());

							currentSlot++; // 移动到下一个尸体存储槽位
						}
					}
				}
			}
		}
	}

    //玩家死亡埋点,这里可以拿掉落所以放这里
    const SocAttackInfo& info = player->getSocAttackInfo();
    const DieInfoCsvDef* dieinfodef = GetDefManagerProxy()->getDieinfoCsvDef((int)(info.atktype));
    const ItemDef* itemdef = GetDefManagerProxy()->getItemDef(info.toolid);
    std::string dieinfo = "";
    int weapon_type = -1;
    if (dieinfodef)
    {
        dieinfo = GetDefManagerProxy()->getStringDef(dieinfodef->LanguageId);
    }

    if (itemdef)
    {
        weapon_type = itemdef->Type;
    }

    const WCoord& pos = player->getPosition();
    std::string location = "[";
    location += std::to_string(pos.x);
    location += ",";
    location += std::to_string(pos.y);
    location += ",";
    location += std::to_string(pos.z);
    location += "]";

    GameAnalytics::TrackEvent("player_die", {
        {"killer_uin",info.playerid},
        {"death_reason",dieinfo},
        {"weapon_type",weapon_type},
        {"distance",info.length},
        {"location",location},
        {"dropped_items",arr.json()},
        });

    //// 设置一个合适的尸体显示名称
    //corpse->setDisplayName(core::string::Format("%s的尸体", player->getDisplayName().c_str()));
    //
    //// 复制玩家外观
    //ActorBody* playerBody = player->getBody();
    //if (playerBody)
    //{
    //    // 保存外观信息以便重新加载
    //    corpse->m_PlayerModelIndex = playerBody->getPlayerIndex();
    //    corpse->m_CustomSkins = playerBody->getCustomSkins() ? playerBody->getCustomSkins() : "";
    //    corpse->m_HairId = player->getHairId();
    //    corpse->m_HairColor = player->getHairColor();
    //    corpse->m_FaceId = player->getFaceId();
    //    
    //    // 初始化尸体模型
    //    corpse->getBody()->initPlayer(
    //        playerBody->getPlayerIndex(),
    //        0, // 没有变异
    //        corpse->m_CustomSkins.c_str(),
    //        0,
    //        false, // 同步加载
    //        false  // 不加载高品质资源
    //    );
    //    
    //    // 设置发型和脸型
    //    if (corpse->m_HairId > 0)
    //    {
    //        corpse->getBody()->setHairId(corpse->m_HairId);
    //        corpse->getBody()->setHairColor(corpse->m_HairColor);
    //    }
    //    
    //    if (corpse->m_FaceId > 0)
    //    {
    //        corpse->getBody()->setFaceId(corpse->m_FaceId);
    //    }
    //}
    //
    //// 创建尸体背包
    //corpse->m_CorpseBackpack = new BackPack(20); // 20格物品栏
    //
    //// 转移部分玩家物品到尸体
    //Backpack* playerBackpack = player->getBackpack();
    //if (playerBackpack && corpse->m_CorpseBackpack)
    //{
    //    // 随机选择一部分玩家物品放入尸体（例如1/3的概率）
    //    for (int i = 0; i < playerBackpack->getCapacity(); i++)
    //    {
    //        const BackpackGrid& grid = playerBackpack->getGrid(i);
    //        if (grid.count > 0 && (rand() % 3 == 0)) // 1/3概率转移
    //        {
    //            // 从玩家背包移除物品
    //            int itemId = grid.id;
    //            int count = grid.count;
    //            int quality = grid.quality;
    //            
    //            // 尝试将物品从玩家背包移到尸体背包
    //            if (player->removeItem(itemId, count))
    //            {
    //                // 添加到尸体背包
    //                corpse->m_CorpseBackpack->addItem(itemId, count, quality);
    //            }
    //        }
    //    }
    //}
    //
    //// 应用尸体视觉效果
    //corpse->ApplyCorpseVisualEffects();
    //
    //// 播放尸体生成音效
    //SoundComponent* sound = corpse->getComponent<SoundComponent>();
    //if (sound)
    //{
    //    sound->playSound("sounds/player_death.ogg");
    //}
    
    return corpse;
}

void ActorPlayerCorpse::init()
{
    ENG_DELETE(m_Body);
    if (!m_Body)
    {
        m_Body = newActorBody();
        m_Body->initPlayer(12, 0, "4");
        m_Body->playAnim(SEQ_DIE);
		Rainbow::FixedString modelPath = "emptpy";
        m_Body->setModelPath(modelPath);
    }
    initStorageBox();

	// 设置尸体碰撞边界 - 使用更大的碰撞范围确保不会穿透地面
	getLocoMotion()->setBound(50, 90);
	getLocoMotion()->m_yOffset = 45; // 设置高度偏移，确保正确检测地面碰撞

}

void ActorPlayerCorpse::initStorageBox()
{
	if (!m_DynamicContainer)
	{
		WCoord pos = WCoord(getPosition().x / BLOCK_SIZE, getPosition().y / BLOCK_SIZE, getPosition().z / BLOCK_SIZE);
        m_DynamicContainer = SANDBOX_NEW(DynamicContainer, pos, 0, 50);
        m_DynamicContainer->setBaseIndex(STORAGE_START_INDEX);
        m_DynamicContainer->actor_storage = true;
		if (m_pWorld)
		{
            m_DynamicContainer->enterWorld(m_pWorld);
		}
	}
}

ActorBody* ActorPlayerCorpse::newActorBody()
{
	ActorBody* actorBody = ENG_NEW(ActorBody)(this);
	if (actorBody)
	{
		//const MonsterDef* def = GetDefManagerProxy()->getMonsterDef(3240, true);
		//if (def)
		//{
		//	actorBody->initMonster("itemmods/3240/body.omod", def->ModelScale, false, def->Effect.c_str(), NULL, false);
		//}
	}
	return actorBody;
}

bool ActorPlayerCorpse::interact(ClientActor* player, bool onshift /* = false */, bool isMobile /* = false */)
{
    auto player_op = dynamic_cast<ClientPlayer*>(player);
    if (player_op && m_DynamicContainer)
    {
        player_op->openContainer(m_DynamicContainer);
    }
    return true;
}

void ActorPlayerCorpse::tick()
{
    ClientActor::tick();
	World* world = getWorld();
	if (!world)
		return;
	// 判断是否为主机模式
	bool isHost = !world->isRemoteMode();
	// 应用重力和物理模拟
	if (world&& isHost)
	{
		// 应用重力 - 使用物品重力系数
		m_Motion.y -= world->getGravity(GRAVITY_ITEM);

		// 执行移动
		getLocoMotion()->doMoveStep(m_Motion);

		// 应用空气阻力
		m_Motion *= 0.98f;

		// 处理地面碰撞
		if (getLocoMotion()->m_OnGround)
		{
			// 应用地面摩擦和反弹
			m_Motion.x *= 0.7f;
			m_Motion.z *= 0.7f;
			m_Motion.y *= -0.3f; // 反弹系数

			// 计算地面停留时间
			m_OnGroundTime++;

			// 当在地面上停留足够长时间且运动很小时，停止水平运动
			if (m_OnGroundTime > 20 &&
				fabs(m_Motion.y) < 1.0f &&
				fabs(m_Motion.x) < 1.0f &&
				fabs(m_Motion.z) < 1.0f)
			{
				m_Motion.x = 0.0f;
				m_Motion.z = 0.0f;
			}
		}
		else
		{
			// 如果不在地面上，重置地面停留时间
			m_OnGroundTime = 0;
		}
	}
    // 更新尸体消失计时器
    m_TickCount++;
    
    // 如果到达淡出阶段但还未开始淡出
    if (!m_IsFading && m_TickCount >= (m_DecayTicks * 0.8f))
    {
        m_IsFading = true;
        m_FadeStartTick = m_TickCount;
        
        //// 开始淡出效果
        //SoundComponent* sound = getComponent<SoundComponent>();
        //if (sound)
        //{
        //    sound->playSound("sounds/corpse_fade.ogg");
        //}
    }
    
    // 如果正在淡出，更新淡出效果
    if (m_IsFading)
    {
        UpdateFadeEffect();
    }
    
    // 如果达到最大存在时间，自动销毁
    if (m_TickCount >= m_DecayTicks)
    {
        //// 如果还有物品未被拾取，创建掉落物
        //if (!m_IsLooted && m_CorpseBackpack && m_CorpseBackpack->getItemCount() > 0)
        //{
        //    CreateDroppedItems();
        //}
        
        // 销毁尸体
        setNeedClear();
    }
}

void ActorPlayerCorpse::ApplyCorpseVisualEffects()
{
    //ActorBody* body = getBody();
    //if (!body)
    //    return;
    //    
    //// 1. 设置死亡姿势
    //body->setCurAnim("death", 0, true);
    //
    //// 2. 添加一些视觉效果，使其看起来像尸体
    //Material* material = body->getBoundMaterial();
    //if (material)
    //{
    //    // 使尸体外观略显灰暗
    //    material->SetVectorParam("tint", Vector4(0.7f, 0.7f, 0.7f, 1.0f));
    //}
    //
    //// 3. 添加可选的尸体特效（如血迹或烟雾）
    //EffectComponent* effectComp = getComponent<EffectComponent>();
    //if (effectComp)
    //{
    //    effectComp->addEffect("effects/blood_pool.oeff", Vector3(0, 0, 0), Vector3(0, 0, 0), 1.0f);
    //}
    //
    //// 4. 禁用AI和移动
    //disableAI();
    //if (getLocoMotion())
    //{
    //    getLocoMotion()->disableMove();
    //}
}

void ActorPlayerCorpse::UpdateFadeEffect()
{
    // 计算淡出进度
    float totalFadeTicks = m_DecayTicks - m_FadeStartTick;
    float currentFadeTicks = m_TickCount - m_FadeStartTick;
    float fadeProgress = currentFadeTicks / totalFadeTicks;
    fadeProgress = std::min(fadeProgress, 1.0f);
    
    // 应用渐隐效果
    ActorBody* body = getBody();
    if (body)
    {
        float alpha = 1.0f - fadeProgress;
        //body->setAlpha(alpha);
    }
}

bool ActorPlayerCorpse::canLootCorpse(ClientPlayer* player)
{
    if (!player)
        return false;
        
    // 检查玩家是否有权拾取（可根据游戏规则自定义）
    // 例如：只有杀死玩家的人或其队友才能拾取，或者所有人都可以拾取
    if (player->getPlayerAttrib()->hasBuff(IN_SAFE_ZONE) && player->getUin() != m_PlayerUin) return false;
    
    // 检查距离
    float distance = Distance(player->getPosition().toVector3(), getPosition().toVector3());
    if (distance > 300.0f) // 3格距离
        return false;
        
    // 其他限制条件检查
    if (player->isDead() || player->isSleeping())
        return false;
        
    return true;
}

void ActorPlayerCorpse::openCorpseLoot(ClientPlayer* player)
{
    if (!player || !m_CorpseBackpack)
        return;
        
    // 记录当前拾取者
    m_CurrentLooterID = player->getObjId();
    
    // 打开尸体物品界面（使用游戏中已有的UI系统）
    MINIW::ScriptVM::game()->callFunction("OpenCorpseLootUI", "dds", 
        (double)player->getObjId(), 
        (double)getObjId(), 
        m_PlayerName.c_str());
}

bool ActorPlayerCorpse::TakeItemFromCorpse(ClientPlayer* player, int slotIndex, int count)
{
    //if (!player || !m_CorpseBackpack || slotIndex < 0 || slotIndex >= m_CorpseBackpack->getCapacity())
    //    return false;
    //
    //// 获取物品信息
    //const BackpackGrid& grid = m_CorpseBackpack->getGrid(slotIndex);
    //if (grid.count <= 0 || count <= 0 || count > grid.count)
    //    return false;
    //
    //// 尝试将物品添加到玩家背包
    //Backpack* playerBackpack = player->getBackpack();
    //if (!playerBackpack)
    //    return false;
    //
    //// 尝试添加物品到玩家背包
    //if (playerBackpack->addItem(grid.id, count, grid.quality))
    //{
    //    // 从尸体背包移除物品
    //    m_CorpseBackpack->removeItem(slotIndex, count);
    //    
    //    // 播放拾取音效
    //    SoundComponent* sound = player->getComponent<SoundComponent>();
    //    if (sound)
    //    {
    //        sound->playSound("sounds/pickup_item.ogg");
    //    }
    //    
    //    // 显示提示消息
    //    char itemName[64] = {0};
    //    ItemManager::getSingleton().getItemName(grid.id, itemName, 64);
    //    char message[128] = {0};
    //    sprintf(message, "从%s的尸体上获得 %s x%d", m_PlayerName.c_str(), itemName, count);
    //    UIManager::getSingleton().showTip(player, message);
    //    
    //    // 检查尸体是否已被完全掠夺
    //    if (m_CorpseBackpack->getItemCount() == 0)
    //    {
    //        m_IsLooted = true;
    //        
    //        // 如果已被掠夺，减少尸体消失时间
    //        int remainingTicks = m_DecayTicks - m_TickCount;
    //        if (remainingTicks > 1200) // 如果还有超过1分钟
    //        {
    //            m_DecayTicks = m_TickCount + 1200; // 设置为60秒后消失
    //            m_IsFading = true;
    //            m_FadeStartTick = m_TickCount;
    //        }
    //    }
    //    
    //    return true;
    //}
    
    return false;
}

void ActorPlayerCorpse::CreateDroppedItems()
{
    //if (!m_CorpseBackpack || m_CorpseBackpack->getItemCount() == 0)
    //    return;
    //
    //WCoord pos = getPosition();
    //
    //// 为每个物品创建掉落实体
    //for (int i = 0; i < m_CorpseBackpack->getCapacity(); i++)
    //{
    //    const BackpackGrid& grid = m_CorpseBackpack->getGrid(i);
    //    if (grid.count > 0)
    //    {
    //        // 在尸体周围随机位置生成掉落物
    //        float randX = (rand() % 200 - 100) / 100.0f;
    //        float randZ = (rand() % 200 - 100) / 100.0f;
    //        Vector3 dropPos = pos.toVector3() + Vector3(randX * 50.0f, 30.0f, randZ * 50.0f);
    //        
    //        // 生成掉落物
    //        ItemManager::getSingleton().dropItem(
    //            grid.id, 
    //            grid.count, 
    //            WCoord::FromVector3(dropPos), 
    //            grid.quality
    //        );
    //    }
    //}
    //
    //// 清空尸体背包
    //m_CorpseBackpack->clear();
    m_IsLooted = true;
}

bool ActorPlayerCorpse::leftClickInteract(ClientActor* player)
{
    ClientPlayer* clientPlayer = dynamic_cast<ClientPlayer*>(player);
    if (clientPlayer)
    {
        if (canLootCorpse(clientPlayer))
        {
            openCorpseLoot(clientPlayer);
            return true;
        }
    }
    return false;
}

int ActorPlayerCorpse::getObjType() const
{
	return OBJ_TYPE_PLAYERCORPSE;
}

bool ActorPlayerCorpse::load(const void* srcdata, int version)
{
    auto src = reinterpret_cast<const FBSave::ActorPlayerCorpse*>(srcdata);
	if (!src)
		return false;
    if(! loadActorCommon(src->basedata()))
       return false;
    /*if (!ClientActor::load(src,version))
    {
        return false;
    }*/

    // 加载玩家UID
    m_PlayerUin = src->playerUin();

	if (src->containers())
	{
		initStorageBox();

        m_DynamicContainer->load((void*)src->containers());
	}
	return true;

    //// 加载基本信息
    //m_PlayerUin = corpseData->playerUin;
    //m_PlayerName = corpseData->playerName;
    //m_PlayerLevel = corpseData->playerLevel;
    //
    //// 加载外观信息
    //m_PlayerModelIndex = corpseData->modelIndex;
    //m_HairId = corpseData->hairId;
    //m_HairColor = corpseData->hairColor;
    //m_FaceId = corpseData->faceId;
    //m_CustomSkins = corpseData->customSkins;
    //
    //// 加载尸体状态
    //m_DecayTicks = corpseData->decayTicks;
    //m_TickCount = corpseData->tickCount;
    //m_IsLooted = corpseData->isLooted;
    //
    //// 计算淡出时间
    //if (m_TickCount >= (m_DecayTicks * 0.8f))
    //{
    //    m_IsFading = true;
    //    m_FadeStartTick = (int)(m_DecayTicks * 0.8f);
    //}
    
    // 加载尸体物品背包
    // 背包内容加载需要额外的数据结构，这里略过
    
    return true;
}

flatbuffers::Offset<FBSave::SectionActor> ActorPlayerCorpse::save(SAVE_BUFFER_BUILDER& builder)
{
    auto baseData = ClientActor::saveActorCommon(builder);
	auto storage = m_DynamicContainer->SaveTo(builder);

	auto data = FBSave::CreateActorPlayerCorpse(builder, baseData, storage, m_PlayerUin);
    return saveSectionActor(builder, FBSave::SectionActorUnion_ActorPlayerCorpse,data.Union());
    //// 创建尸体数据
    //CorpseData corpseData;
    //memset(&corpseData, 0, sizeof(CorpseData));
    //
    //// 保存基本信息
    //corpseData.playerUin = m_PlayerUin;
    //strncpy(corpseData.playerName, m_PlayerName.c_str(), 63);
    //corpseData.playerLevel = m_PlayerLevel;
    //
    //// 保存外观信息
    //corpseData.modelIndex = m_PlayerModelIndex;
    //corpseData.hairId = m_HairId;
    //corpseData.hairColor = m_HairColor;
    //corpseData.faceId = m_FaceId;
    //strncpy(corpseData.customSkins, m_CustomSkins.c_str(), 127);
    //
    //// 保存尸体状态
    //corpseData.decayTicks = m_DecayTicks;
    //corpseData.tickCount = m_TickCount;
    //corpseData.isLooted = m_IsLooted;
    //
    //// 使用FlatBuffers创建SectionActor
    ////auto mobData = ClientMob::saveMob(builder);
    ////auto playerCorpseData = builder.CreateString(reinterpret_cast<const char*>(&corpseData), sizeof(CorpseData));
    //
    /////auto corpse = FBSave::CreateActorPlayerCorpse(builder, mobData, playerCorpseData);
    ////return FBSave::CreateSectionActor(builder, FBSave::SectionActorUnion_ActorPlayerCorpse, corpse.Union());
    //return flatbuffers::Offset<FBSave::SectionActor>();
}

void ActorPlayerCorpse::enterWorld(World* pworld)
{
    ClientActor::enterWorld(pworld);
    
    // 尸体进入世界后的额外处理
    ApplyCorpseVisualEffects();
    
    // 如果尸体已经处于淡出阶段，恢复淡出效果
    if (m_IsFading)
    {
        UpdateFadeEffect();
    }
	if (m_DynamicContainer)
	{
		m_DynamicContainer->enterWorld(pworld);
	}
}

void ActorPlayerCorpse::leaveWorld(bool keep_inchunk)
{
    // 在离开世界前处理任何必要的清理工作
    
    // 如果不是因为区块卸载，而是真的要销毁
    if (!keep_inchunk)
    {
        //// 如果有物品且未拾取，创建掉落物
        //if (!m_IsLooted && m_CorpseBackpack && m_CorpseBackpack->getItemCount() > 0)
        //{
        //    CreateDroppedItems();
        //}
    }
	if (m_DynamicContainer)
	{
		m_DynamicContainer->leaveWorld();
	}
    
    ClientActor::leaveWorld(keep_inchunk);
} 
