#ifndef __TORCHHANDLER_H__
#define __TORCHHANDLER_H__

class PlayerAttrib;
class BackPack;
class ClientPlayer;

/**
 * 火把处理器
 * 负责处理玩家手持火把的耐久度消耗逻辑
 */
class TorchHandler
{
public:
	TorchHandler(PlayerAttrib* playerAttrib);
	virtual ~TorchHandler();

	/**
	 * 初始化处理器
	 */
	void initialize(ClientPlayer* player);

	/**
	 * 每秒调用的火把耐久度检查
	 */
	void tick();

	/**
	 * 检测玩家是否手持火把
	 * @param torchItemId [out] 输出火把物品ID
	 * @return 是否手持火把
	 */
	bool isPlayerHoldingTorch(int& torchItemId);

	/**
	 * 减少火把耐久度
	 * @param torchItemId 火把物品ID
	 * @param amount 扣除的耐久度数量，默认为1
	 */
	void reduceTorchDurability(int torchItemId, int amount = 1);

	/**
	 * 获取火把的最大耐久度
	 * @param torchItemId 火把物品ID
	 * @return 最大耐久度，如果获取失败返回0
	 */
	int getTorchMaxDurability(int torchItemId);

	/**
	 * 根据最大耐久度计算扣除间隔（tick数）
	 * @param maxDurability 最大耐久度
	 * @return 扣除间隔的tick数
	 */
	int calculateDurabilityReductionInterval(int maxDurability);

	/**
	 * 根据最大耐久度计算每次扣除的耐久度数量
	 * @param maxDurability 最大耐久度
	 * @return 每次扣除的耐久度数量
	 */
	int calculateDurabilityReductionAmount(int maxDurability);

	/**
	 * 清空玩家手中的火把
	 * @param torchItemId 火把物品ID
	 */
	void clearPlayerTorch(int torchItemId);

	/**
	 * 判断物品是否为火把
	 * @param itemId 物品ID
	 * @return 是否为火把
	 */
	bool isTorchItem(int itemId) const;

	// Getter方法
	bool isHoldingTorch() const { return m_IsHoldingTorch; }
	int getLastHeldTorchItemId() const { return m_LastHeldTorchItemId; }

private:
	PlayerAttrib* m_PlayerAttrib;      // 玩家属性引用
	ClientPlayer* m_ClientPlayer;      // 玩家引用
	BackPack* m_Backpack;              // 背包引用

	int m_LastHeldTorchItemId;         // 上次手持的火把物品ID
	bool m_IsHoldingTorch;             // 是否正在手持火把
	bool m_IsInitialized;              // 是否已初始化
	int m_TickCounter;                 // Tick计数器，用于计算秒数
};

#endif // __TORCHHANDLER_H__

