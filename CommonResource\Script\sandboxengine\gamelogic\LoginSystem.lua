--[[
登录逻辑
FBI WARNING:__双下划线开头的函数是私有函数,外部不要调用.
]]
local LoginSystem = {}

LoginSystem.EVENT_ID = {
    Login_Success = 1,
    Login_Fail = 2,
    Login_Auth_Fail = 3,
    Login_Begin = 4,
    Login_End = 5,   
    Login_Auth_Begin = 6, 
    Login_Auth_End = 7, 
    Login_Data_Update_Begin = 8, 
    Login_Data_Update_End = 9, 
}

LoginSystem.LOGIN_TYPE = {
    Login_Question = 1,
    Login_Authinfo = 2,
    Login_Pwd = 3,
    Login_Standalone = 4,
    Login_ApiId = 5,
    Login_Reg = 6,
    Login_OpenStr = 7,
    Login_SwitchAccount = 8,
}

LoginSystem.DefaultObjKey = "login_default_obj"

--SandboxLua注册事件的key
LoginSystem.Event_Key = {
    Login_Success = "Login_Success",
    Login_Fail = "Login_Fail",
    Login_Begin = "Login_Begin",
    Login_End = "Login_End",
    Login_Data_Update_Begin = "Login_Data_Update_Begin",
    Login_Data_Update_End = "Login_Data_Update_End",
}


--是否是单机
local function isStandalone()
    if AccountManager and AccountManager.check_standalone and AccountManager:check_standalone() then
        return true
    end
    return false
end

--打印函数
function LoginSystem:print(...)
    if self.isOpenLog then
        print("LoginSystem:", ...)
    end
end

--打印函数开关
function LoginSystem:setOpenLog(bOpen)
    self.isOpenLog = bOpen
end

--销毁
function LoginSystem:destroy()
    self.listeners = {}
    self.channelApiIdConditionList = {}
end

--构造函数
function LoginSystem:constructor()
    self.listeners = {}
    self.channelApiIdConditionList = {}
    self.isOpenLog = true
    self.isLoginFail = false

    --初始化事件注册
    self:initEvent()
end

--构造闭合函数
function LoginSystem:handler(obj, method)
    return function(...)
        return method(obj, ...)
    end
end

-- --注册事件
-- function LoginSystem:regEvent(eventid, obj, handler)
--     if not self.listeners[eventid] then
--         self.listeners[eventid] = {}
--     end
--     obj = obj or self.DefaultObjKey
--     self.listeners[eventid][obj] = handler
-- end

-- --unreg
-- function LoginSystem:unRegEventByEventId(eventid)
--     for _eventid, eventList in pairs(self.listeners) do
--         if eventid == _eventid then
--             eventList = {}
--             return
--         end
--     end
-- end

-- function LoginSystem:unRegEventByObj(obj)
--     for _eventid, eventList in pairs(self.listeners) do
--         for _obj, handler in pairs(eventList) do
--             if obj == _obj then
--                 eventList[obj] = nil
--             end
--         end
--     end
-- end

-- function LoginSystem:unRegEventByEventIdObj(eventid, obj)
--     for _eventid, eventList in pairs(self.listeners) do
--         if eventid == _eventid then
--             for _obj, handler in pairs(eventList) do
--                 if obj == _obj then
--                     eventList[obj] = nil
--                 end
--             end
--             break
--         end
--     end
-- end

--派发事件
function LoginSystem:dispatchEvent(eventId, custom, loginType)
    if type(custom) ~= "table" then
        --self:print("dispatchEvent param error",custom)
        custom = {}
    end
    --self:print(code, TableToStr(custom or {}))

    for _eventid, eventList in pairs(self.listeners) do
        for _obj, handler in pairs(eventList) do
            if type(handler) == "function" then
                if type(custom) ~= "table" then
                    custom = {custom}
                end
                handler(loginType, unpack(custom))
            else
                --self:print("dispatchEvent:"..tostring(eventid)..tostring(obj).." handler nil")
            end
        end
    end
end

--初始化事件注册
function LoginSystem:initEvent()
    SandboxLua.eventDispatcher:CreateEvent(nil, self.Event_Key.Login_Success)
    SandboxLua.eventDispatcher:CreateEvent(nil, self.Event_Key.Login_Fail)
    SandboxLua.eventDispatcher:CreateEvent(nil, self.Event_Key.Login_Begin)
    SandboxLua.eventDispatcher:CreateEvent(nil, self.Event_Key.Login_End)
end

--账号密码登录
function LoginSystem:loginPwd(uin, pwd)
    if not AccountManager:requestEnterGame2New() then
        return
    end
    if not self:__isUinPwdOk(uin, pwd) then
        return
    end
    -- self:dispatchEvent(self.EVENT_ID.Login_Begin)
    self:__emitEvent(self.Event_Key.Login_Begin, MNSandbox.SandboxContext())
    --账号验证
    self:__loginPwd(uin, pwd)

    --数据更新
    AccountManager:get_account_info(uin)
    self:__data_update(true) 

    --登录成功事件
    --self:__checkSendLoginSuccess({ErrorCode.OK}, self.LOGIN_TYPE.Login_Pwd)
end

--带问题 登录
function LoginSystem:loginQuestion(uin, question)
    if not AccountManager:requestEnterGame2New() then
        return
    end
    if not self:__isUinQuestionOk(uin, question) then
        return
    end
    -- self:dispatchEvent(self.EVENT_ID.Login_Begin)
    self:__emitEvent(self.Event_Key.Login_Begin, MNSandbox.SandboxContext())
    --账号验证
    self:__loginQuestion(uin, question)
    
    --数据更新
    AccountManager:get_account_info(uin)
    local retList = {self:__data_update(true)}

    --登录成功事件
    --self:__checkSendLoginSuccess(retList, self.LOGIN_TYPE.Login_Question)    
end

--authinfo 登录
function LoginSystem:loginAuthInfo(uin, authinfo)
    if not AccountManager:requestEnterGame2New() then
        return
    end
    if not self:__isUinAuthInfoOk(uin, authinfo) then
        return
    end
    
    -- self:dispatchEvent(self.EVENT_ID.Login_Begin)
    self:__emitEvent(self.Event_Key.Login_Begin, MNSandbox.SandboxContext())
    --账号验证
    self:__loginAuthinfo(authinfo)

    --数据更新
    AccountManager:get_account_info(uin)
    local retList = {self:__data_update(true)}

    --登录成功事件
    --self:__checkSendLoginSuccess(retList, self.LOGIN_TYPE.Login_Question)    
end


--登陆接口
--兼容 client 下的登陆函数
function LoginSystem:requestEnterGame(uin, pwd)
    local current = AccountManager:load_from_file('current')
    uin = uin or (current and current.Uin) or LuaInterface:getUin() or 1
    local authinfo = current and current.authinfo
    local question = current and current.question
    local apiid = LuaInterface:getApiId()

    -- self:dispatchEvent(self.EVENT_ID.Login_Begin)
    self:__emitEvent(self.Event_Key.Login_Begin, MNSandbox.SandboxContext())
    --facebook login
    local isFbLogin, code, info = self:__checkFacebookLogin()
    if isFbLogin then
        return code, info
    end

    --渠道登录
    if self:__isChannelLogin(apiid) then
        local retList = {self:__channelLogin(apiid)}
        self:__checkSendLoginSuccess(retList, self.LOGIN_TYPE.Login_ApiId)
        return unpack(retList)
    end

    --正常登陆
    --新账号注册
    if self:__isRegLogin(uin) then
        local retList = {self:__doRegLogin(pwd)}
        self:__checkSendLoginSuccess(retList, self.LOGIN_TYPE.Login_Reg)
        return unpack(retList)
    end

    --单机
    if isStandalone() then
        local code, result = self:__LoginStandalone(uin)
        self:__checkSendLoginSuccess({code}, self.LOGIN_TYPE.Login_Standalone)
        return code, result
    elseif self:__isUnionidLogin(authinfo) then
        return self:__doUnionidLogin(uin, authinfo)
    end
    
    --登录 
    if self:__isUinQuestionOk(uin, question) then
        self:__loginQuestion(uin, question)
    elseif self:__isUinAuthInfoOk(authinfo) then
        self:__loginAuthinfo(uin, authinfo)
    else
        pwd = pwd or (current and current.Passwd) or LuaInterface:getPasswd()
        self:__loginPwd(uin, pwd)
    end

    AccountManager:get_account_info(uin)
    local code = self:__data_update(true)
    if code == ErrorCode.ACCOUNT_PASSWD_NOT_OK then
        AccountManager.pwd_wrong = true
    end

    if GetIWorldConfig():getGameData("game_env") == 2 or GetIWorldConfig():getGameData("game_env") == 12 then 
        self:__checkSendLoginSuccess({code, ignoreFail = false})
        return code
    end
    if AccountManager:getloginsafeverify() then 
        self:__checkSendLoginSuccess({code, ignoreFail = false})
        return code 
    end
    
    self:__checkSendLoginSuccess({ErrorCode.OK})
    return ErrorCode.OK
end
--兼容 client 下的登陆函数
function LoginSystem:requestEnterGameByOpenstring(accountname, openstring)
    -- self:dispatchEvent(self.EVENT_ID.Login_Begin)
    self:__emitEvent(self.Event_Key.Login_Begin, MNSandbox.SandboxContext())
    self:__startConn()
    
    local conn = AccountManager.conn
    local current = AccountManager:load_from_file('current')
    local uin = current and current.Uin or LuaInterface:getUin()
    if type(uin) == 'number' and uin <= 0 then
        uin = 1
    end
    if current and current.AccountName ~= accountname then 
        uin = 1 
    end 
    local extra = self:__getLoginExtra()
    conn:set_auth_func(function ()
        local RoleInfo = AccountManager:load_from_file('tmp1')
        conn.errorcode = nil
        conn.errorinfo = nil
        local code, authinfo = AccountManager.cluster.login.auth({
            AccountName = accountname,
            OpenString = openstring,
            ApiId = LuaInterface:getApiId(),
            CltVersion = LuaInterface:getCltVersion(),
            RoleInfo = AccountManager.account.Account.RoleInfo,
            DeviceID = self:__getDeviceID(),
            Longitude = 0,
            Latitude = 0,
            CltType = LuaInterface:getCltTye(),
            Country = self:__getCountry(),
            extra = extra,
        })
        --self:__generateLoginFailInfo(authinfo, self.LOGIN_TYPE.)
        self:print(code,authinfo)
        conn.errorcode = code
        conn.errorinfo = authinfo
        if code == ErrorCode.OK then 
            self:__setConnData(conn, authinfo, AccountManager.account)
            conn.isnewuser = authinfo.isnewuser
            if extra then 
                extra.authinfo = authinfo 
            end 
            AccountManager:save_to_file('current', {Uin = authinfo.Uin, AccountName = accountname, extra = extra})

            --self:dispatchEvent(self.EVENT_ID.Login_Success, code, authinfo)
        else
            self:__setConnData(conn, nil)
            self:__emitEvent(self.Event_Key.Login_Fail, MNSandbox.SandboxContext():SetData_Number("code", tonumber(code) or -1))
            --self:dispatchEvent(self.EVENT_ID.Login_Auth_Fail, {code, authinfo}, self.LOGIN_TYPE.Login_OpenStr)
        end 
    end )

    AccountManager:get_account_info(uin)
    local code = self:__data_update(true)
    self:__checkSendLoginSuccess({code}, self.LOGIN_TYPE.Login_OpenStr)
    return ErrorCode.OK, conn.isnewuser and 'first_login' or '', true --第三个参数是用来表示是否是sdk登录
end
--兼容 client 下的登陆函数
function LoginSystem:requestEnterGameWithAuthInfo(authinfo)
    -- self:dispatchEvent(self.EVENT_ID.Login_Begin)
    self:__emitEvent(self.Event_Key.Login_Begin, MNSandbox.SandboxContext())
    self:__startConn()

    local uin = authinfo.Uin
    if not uin or uin < 1000 then 
        return ErrorCode.FAILED, 'Uin is less thant 1000' 
    end
    local conn = AccountManager.conn
    conn:set_auth_func(function ()
        self:__setConnData(conn, nil)
        local code = conn:heartbeat()
        conn.errorcode = code
        if code == ErrorCode.OK then 
            self:__setConnData(conn, authinfo, AccountManager.account)
        else
            self:__setConnData(conn, nil)
        end 
    end )

    self:get_account_info(uin)
    local code = self:__data_update(true)
    if self:getloginsafeverify() then 
        self:__checkSendLoginSuccess({code}, self.LOGIN_TYPE.Login_Authinfo)
        return code, authinfo.isnewuser and 'first_login' or nil 
    end
    self:__checkSendLoginSuccess({ErrorCode.OK}, self.LOGIN_TYPE.Login_Authinfo)
    return ErrorCode.OK, authinfo.isnewuser and 'first_login' or nil
end
--兼容 client 下的切换账号函数
function LoginSystem:switchAccountLogin(string_of_uin, pwd, code)
    -- self:dispatchEvent(self.EVENT_ID.Login_Begin)
    self:__emitEvent(self.Event_Key.Login_Begin, MNSandbox.SandboxContext())
    AccountManager.pwd_wrong = false
    
    if not self:__findHistory() then
        return false, ErrorCode.ACCOUNT_PASSWD_NOT_OK
    end
    
    --切换帐号
    if OWorldList:getInstance().beforeSwitchAccount then		--热更现网版本没有此函数
        --CSMgr:beforeSwitchAccount();
    end
    local origin_account = AccountManager.account
    if origin_account then 
        origin_account:flush() 
    end 

    local uin = type(string_of_uin) == 'string' and string_of_uin:match('@') and string_of_uin or tonumber(string_of_uin)
    local conn = AccountManager.conn
    local origin_conninfo = {
        authed=  conn.authed,
        token = conn.token,
        other = conn.other,
        sign = conn.sign,
    }
    --断开老链接
    conn:close()
    local chatpushconn = AccountManager.chatpushconn
    chatpushconn:close()
    local origin_auth_func = conn:get_auth_func()
    conn:set_auth_func(function ()
        --重新登录 {{{
        conn.errorcode = nil
        conn.errorinfo = nil
        local code, authinfo = AccountManager.cluster.login.auth({
            Uin = uin,
            Passwd = pwd,
            ApiId = LuaInterface:getApiId(),
            CltVersion = LuaInterface:getCltVersion(),
            isswitch = true, --是否切换帐号
            DeviceID = (GetClientInfo():getDeviceID()) or 'cantget',
            Country = (type(gFunc_getCountry) == 'function' and gFunc_getCountry()) or nil,
        })
        conn.errorcode = code
        conn.errorinfo = authinfo
        if code == ErrorCode.OK then
            self:__setConnData(conn, authinfo, AccountManager.account)
        else
            self:__setConnData(conn, nil)
            self:__emitEvent(self.Event_Key.Login_Fail, MNSandbox.SandboxContext():SetData_Number("code", tonumber(code) or -1))
            --self:dispatchEvent(self.EVENT_ID.Login_Auth_Fail, {code, authinfo}, self.LOGIN_TYPE.Login_SwitchAccount)
        end 
    end )
    AccountManager:get_account_info(type(uin) == 'number' and uin or 1)
    code = self:__data_update(true)
    if code == ErrorCode.OK then 
        AccountManager:save_to_file('current', {Uin = uin, Passwd = pwd})
        AccountManager.account:OnBuddyDirty()
        AccountManager.account:OnAccountDirty()
        BuddyManager:on_switch_account()
        AccountManager.cluster.avatar.rpc_cache_clear()
        local account = AccountManager.account
        if account then 
            local Account = account.Account
            local leveldb = account.leveldb
            local RoleInfo = Account.RoleInfo
            local RoleSkinID = leveldb.RoleSkinID or RoleInfo.SkinID or 0
            if LuaInterface.setRoleInfoWithAvatarSkin and account.avatar_seat_current then 
                local seatid = account:avatar_seat_current()
                LuaInterface:setRoleInfoWithAvatarSkin(RoleInfo.Model, RoleInfo.NickName, RoleSkinID, Account.Uin, seatid and tostring(seatid) or '')
            else
                LuaInterface:setRoleInfo(RoleInfo.Model, RoleInfo.NickName, RoleSkinID, Account.Uin)
            end

            AccountManager:trigger('postSetAccount', 0, '')
        end 
        if AccountGameModeClass and AccountGameModeClass.showTimerNum then
            AccountGameModeClass.showTimerNum = 0
        end
        self:__checkSendLoginSuccess({code}, self.LOGIN_TYPE.Login_SwitchAccount)
    else
        --回滚
        if origin_account then
            print('回滚 到原来帐号 ', code)
            AccountManager.account = origin_account
            conn:set_auth_func(origin_auth_func)
            for k, v in pairs(origin_conninfo) do 
                conn[k] = v 
            end 
        end
        -- self:dispatchEvent(self.EVENT_ID.Login_Fail, {code, authinfo}, self.LOGIN_TYPE.Login_OpenStr)
        local context = MNSandbox.SandboxContext():SetData_Number("code", tonumber(code) or -1)
        self:__emitEvent(self.Event_Key.Login_Fail, context)
    end 
    -- self:dispatchEvent(self.EVENT_ID.Login_End)
    self:__emitEvent(self.Event_Key.Login_End, MNSandbox.SandboxContext():SetData_Number("code", tonumber(code) or -1))
    
    return code == ErrorCode.OK, code
end




--__私有函数 外部不要调用
--
function LoginSystem:__getLoginExtra()
    local extra = nil
    if GetFacebookLoginInfo then 
        local is_facebook_login, facebook_token, facebook_id, facebook_nickname = GetFacebookLoginInfo()
        if is_facebook_login and facebook_token and facebook_id then 
            extra = {
                platform = 'facebook',
                id = facebook_id,
                nickname = facebook_nickname,
            }
        end 
    end 
    return extra
end

--是否需要注册登录
function LoginSystem:__isRegLogin(uin)
    if type(uin) == 'number' and uin <= 1 then 
        return true
    end 
    return false
end

--是否需要注册登录
function LoginSystem:__doRegLogin(pwd)
    if ReportMgr and ReportMgr.sendStatistics then
        ReportMgr:sendStatistics()
    end
    return AccountManager:reg(pwd)
end

--是否unionid login
function LoginSystem:__isUnionidLogin(authinfo)
    if type(authinfo) == 'table' and (authinfo.appid or authinfo.openid_type) then
        if authinfo.openid_type and authinfo.openid_type == 'kuaishou' then
            return false
        end
        return true
    end
    return false
end

--unionid login
function LoginSystem:__doUnionidLogin(uin, authinfo)
    if ReportMgr and ReportMgr.setLoginType then
        ReportMgr:setLoginType(tostring(authinfo.openid_type))
    end
    local flag,msg,code = AccountManager:unionid_login(nil,false)
    if false == flag then
        --self:msgbox('微信/QQ登录授权已过期，请重新授权登录游戏')
        AccountManager:get_account_info(uin)
        self:__data_update(true)
    end

    return ErrorCode.OK
end

--是否是问题登录方式
function LoginSystem:__isQuestionOk(question)
    if type(question) == 'table' and type(question[1]) == 'string' and type(question[2]) == 'string' then
        return true
    end
    return false
end

--是否是authinfo登录
function LoginSystem:__isAuthinfoOk(authinfo)
    if type(authinfo) == 'table' and authinfo.Uin == Uin and authinfo.token and authinfo.sign then 
        return true
    end
    return false
end

--facebook login
function LoginSystem:__checkFacebookLogin(current)
    if current then 
        local extra = current.extra
        local platform = extra and extra.platform
        if platform and platform == 'facebook' then 
            local authinfo = extra.authinfo
            if authinfo then 
                return true, AccountManager:requestEnterGameWithAuthInfo(authinfo)
            end 
        end 
    end 
    if GetFacebookLoginInfo then 
        local is_facebook_login, facebook_token, facebook_id, facebook_nickname = GetFacebookLoginInfo()
        if is_facebook_login and facebook_token and facebook_id then 
            return true, AccountManager:requestEnterGameByOpenstring(facebook_id, facebook_token)
        end 
    end 
    return false
end

--单机登录
function LoginSystem:__loginStandalone(uin)
    local code, result = AccountManager:get_account_info(uin)
    return code, result
end

-- conn start
function LoginSystem:__startConn()
    local conn = AccountManager.conn
    if not conn then
        self:print("__startConn: conn is nil")
        return
    end
    conn:start()
    if not conn.other then 
        conn:heartbeat() 
    end 
end

--获取country
function LoginSystem:__getCountry()
    if type(gFunc_getCountry) == 'function' then
        return gFunc_getCountry()
    end

    return nil
end

--获取deviceid
function LoginSystem:__getDeviceID()
    if true then
        return GetClientInfo():getDeviceID()
    end
    return 'cantget'
end

--只是内部使用 设置conn的一些参数
function LoginSystem:__setConnData(conn, authinfo, account)
    conn.authed = authinfo ~= nil
    if authinfo then
        if account then
            account.islogin = true
            account:setuin(authinfo.Uin)
        end
        conn.isnewuser = authinfo.isnewuser
    else
        authinfo = {}
        conn.errorcode = nil
        conn.errorinfo = nil
    end
    conn.token = authinfo.token
    conn.sign = authinfo.sign
    conn.other = authinfo.other
end

--问题登录
function LoginSystem:__loginQuestion(uin, question)
    if not type(question) == "table" then
        self:print("__loginQuestion error", uin, question)
        return
    end
    local conn = AccountManager.conn
    conn:set_auth_func(function ()
        local code, info = AccountManager:http_rpc('passwd', 'question_verify', {Uin = uin, key = question[1], 
                                                  value = question[2], apiid = LuaInterface:getApiId(), DeviceID = self:__getDeviceID()})
        conn.errorcode = code
        info = self:__generateLoginFailInfo(info, self.LOGIN_TYPE.Login_Question)
        if code == ErrorCode.QUESTION_NOT_SET then 
            --翻一翻历史旧帐
            do 
                local h = AccountManager:load_from_file('history')
                if type(h) == 'table' and #h > 0 then 
                    local history_num = #h
                    for i = history_num, 1, -1 do
                        local history_one = h[i]
                        if type(history_one) == 'table' and history_one.Uin == uin and not history_one.question then 
                            AccountManager:save_to_file('current', history_one)
                            local context = MNSandbox.SandboxContext():SetData_Number("code", tonumber(code) or -1)
                            self:__emitEvent(self.Event_Key.Login_Fail, context)
                            return code
                        end 
                    end 
                end 
            end 
        else
            local authinfo = (type(info) == 'table' and info.authinfo)
            if code == ErrorCode.OK and authinfo then 
                conn.authed = false
                conn.token = authinfo.token
                conn.errorcode = nil
                conn.errorinfo = nil
                local code = conn:heartbeat()
                conn.errorcode = code

                if code == ErrorCode.OK then 
                    self:__setConnData(conn, authinfo, AccountManager.account)

                    self:__emitEvent(self.Event_Key.Login_Success, MNSandbox.SandboxContext():SetData_Number("code", tonumber(code) or -1))
                else
                    self:__setConnData(conn, nil)
                    conn.errorcode = code
                    conn.errorinfo = info
                    self:__emitEvent(self.Event_Key.Login_Fail, MNSandbox.SandboxContext():SetData_Number("code", tonumber(code) or -1))
                end 
            else
                conn.authed = false
                conn.token = nil
                conn.sign = nil
                conn.other = nil
                self:__emitEvent(self.Event_Key.Login_Fail, MNSandbox.SandboxContext():SetData_Number("code", tonumber(code) or -1))
            end
        end 
    end )
end

--验证登录
function LoginSystem:__loginAuthinfo(uin, authinfo)
    local conn = AccountManager.conn
    conn:set_auth_func(function ()
        conn.authed = false
        conn.token = authinfo.token
        conn.errorcode = nil
        conn.errorinfo = nil
        local code = conn:heartbeat()
        conn.errorcode = code
        if code == ErrorCode.OK then 
            self:__setConnData(conn, authinfo, AccountManager.account)
            self:__emitEvent(self.Event_Key.Login_Success, MNSandbox.SandboxContext():SetData_Number("code", tonumber(code) or -1))
        else
            self:__setConnData(conn, nil)
            local info = self:__generateLoginFailInfo(authinfo, self.LOGIN_TYPE.Login_Authinfo)
            self:__emitEvent(self.Event_Key.Login_Fail, MNSandbox.SandboxContext():SetData_Number("code", tonumber(code) or -1))
        end 
    end )   
end

--账号密码登录
function LoginSystem:__loginPwd(uin, pwd)
    local conn = AccountManager.conn
    conn:set_auth_func(function ()
        conn.errorcode = nil
        conn.errorinfo = nil
        local code, authinfo = AccountManager.cluster.login.auth({
            Uin = uin,
            Passwd = pwd,
            ApiId = LuaInterface:getApiId(),
            CltVersion = LuaInterface:getCltVersion(),
            DeviceID = self:__getDeviceID(),
            Country = self:__getCountry(),
        })
        authinfo = self:__generateLoginFailInfo(authinfo, self.LOGIN_TYPE.Login_Pwd)

        conn.errorcode = code
        conn.errorinfo = authinfo
        self:print('login.auth return code', code)
        if code == ErrorCode.OK then 
            AccountManager.pwd_wrong = nil
            self:__setConnData(conn, authinfo, AccountManager.account)
            AccountManager:save_to_file('current', {Uin = uin, Passwd = pwd})

            self:__emitEvent(self.Event_Key.Login_Success, MNSandbox.SandboxContext():SetData_Number("code", tonumber(code) or -1))
        else
            if code == ErrorCode.ACCOUNT_PASSWD_NOT_OK then 
                local code = self:__tryPwdLogin(conn, uin, pwd)
                if code == ErrorCode.OK then
                    return ErrorCode.OK
                end
            end 
            self:__setConnData(conn, nil)
            AccountManager.pwd_wrong = true
    
            self:__emitEvent(self.Event_Key.Login_Fail, MNSandbox.SandboxContext():SetData_Number("code", tonumber(code) or -1))
        end 
    end )
end

--登录错误事件传递参数添加 loginType字段
function LoginSystem:__generateLoginFailInfo(info, loginType)
    local ret = (type(info) == "table" and info) or {}
    ret.loginType = loginType
    return ret
end

--账号密码登录错误时 重新尝试登录
function LoginSystem:__tryPwdLogin(conn, uin, lastPwd)
    local try_passwd = function (tryPwd)
        conn.errorcode = nil
        conn.errorinfo = nil
        local pwd = nil
        local code, authinfo = AccountManager.cluster.login.auth({
            Uin = uin,
            pwd = tryPwd,
            ApiId = LuaInterface:getApiId(),
            CltVersion = LuaInterface:getCltVersion(),
            DeviceID = self:__getDeviceID(),
            Country = self:__getCountry(),
        })
        conn.errorcode = code
        conn.errorinfo = authinfo
        if code == ErrorCode.OK then 
            pwd = tryPwd
            self:__setConnData(conn, authinfo, AccountManager.account)
            AccountManager:save_to_file('current', {Uin = Uin, Passwd = pwd})
            AccountManager.pwd_wrong = nil
        end 
        return code
    end 

    
    local already_try = { [lastPwd] = true, }
    --尝试历史记录
    local history = AccountManager:load_from_file('history')
    if type(history) == 'table' and table.getn(history) then 
        local trylist = table.clone(history)
        for i, v in ipairs (trylist) do 
            if type(v) == 'table' then 
                if type(v.Passwd) == 'string' and not already_try[v.Passwd] then 
                    already_try[v.Passwd] = true
                    local try_code = try_passwd(v.Passwd)
                    if try_code == ErrorCode.OK then 
                        return ErrorCode.OK 
                    end 
                end 
                if type(v.OldPasswd) == 'string' and not already_try[v.OldPasswd] then 
                    already_try[v.OldPasswd] = true
                    local try_code = try_passwd(v.OldPasswd)
                    if try_code == ErrorCode.OK then 
                        return ErrorCode.OK 
                    end 
                end 
            end 
        end 
    end 

    return -1
end

--是否是渠道登录判断
function LoginSystem:__isChannelLogin(apiid)
    if self:__isApiidNeedLoginAuthinfo(apiid) or self:__isApiidNeedLoginOpenStr(apiid) then
        return true
    end
    return false
end

--渠道登录-通过验证信息方式
function LoginSystem:__isApiidNeedLoginAuthinfo(apiid)
    apiid = tonumber(apiid) or 0
    if (apiid >= 110 and apiid <= 120) or 
    (apiid == 410 or apiid == 411 or apiid == 130 or apiid == 131 or apiid == 132) then
        return true
    end
    return false
end

--渠道登录--通过openstr方式
function LoginSystem:__isApiidNeedLoginOpenStr(apiid)
    apiid = tonumber(apiid) or 0
    if (apiid == 60) or (apiid == 56) or (apiid == 47) or (apiid == 121) or (apiid == 122) 
    or (apiid == 123) or (GetClientInfo():isQQGamePcApi(apiid)) or (apiid == 124) or (apiid == 125) 
    or (apiid == 126) or (apiid == 127) or (apiid == 102) or (apiid == 128) or (apiid == 129) then 
        return true
    end
    return false
end

--渠道登录--所有标记apiid的登陆
function LoginSystem:__channelLogin(apiid)
    if self:__isApiidNeedLoginOpenStr(apiid) then
        return self:__channelLoginOpenstring()
    elseif self:__isApiidAuthinfo(apiid) then
        return self:__channelLoginAuthinfo()
    end
end

--某些渠道需要协程等待参数传递完成
function LoginSystem:__doCheckParamThread(checkitem)
    if type(checkitem) ~="table" then
        return
    end
    local canConditionDoFunc = checkitem.canConditionDoFunc
    local conditionFunc = checkitem.conditionFunc
    if type(canConditionDoFunc) == "function" and canConditionDoFunc() then 
        if type(conditionFunc) == "function" then
            conditionFunc()
        end
        for i = 1, 300 do
            if not canConditionDoFunc() then 
                break 
            end
            threadpool:wait(0.1)
        end 
    end 
end

--渠道登录的特殊检测参数的函数item生成 
function LoginSystem:__getChannelCheckItem(apiid)
    local checkItem = {}
    -- --兼容老的逻辑
    -- if apiid == 129 then
    --     checkitem.canConditionDoFunc = function ()
    --         local openstring = SdkManager:getTpLoginAccountParams()
    --         if not openstring or openstring == '' then
    --             return true
    --         end
    --         return false
    --     end
    --     checkitem.conditionFunc = function()
    --         GetClientInfo():GetFunnyCoreLoginAccountInfo()
    --         openstring = SdkManager:getTpLoginAccountParams()
    --     end
    --     return checkitem
    -- -- 102 402
    -- end
    return self.channelApiIdConditionList[apiid]
end

function LoginSystem:setChannelCheckItem(apiid, canConditionDoFunc, conditionFunc)
    local checkItem = self.channelApiIdConditionList[apiid] or {}

    checkitem.canConditionDoFunc = canConditionDoFunc
    checkitem.conditionFunc = conditionFunc
    
    self.channelApiIdConditionList[apiid] = checkItem
end

--渠道登录--openstr
function LoginSystem:__channelLoginOpenstring(apiid)
    local openstring = SdkManager:getTpLoginAccountParams()
    local accountname = SdkManager:getTpLoginAccount()
    
    self:__doCheckParamThread(self:__getChannelCheckItem(apiid))

    if not openstring or openstring == '' then 
        return 
    end 
    return AccountManager:requestEnterGameByOpenstring(accountname, openstring)
end

--渠道登录--Authinfo
function LoginSystem:__channelLoginAuthinfo()
    local s = SdkManager:getTpLoginAccountParams()
    if s  then 
        local logininfo = {}
        for k in s:gmatch('[^&]+') do
            local key, value = k:match('([^=]+)=([^=]+)')
            if key and value then 
                logininfo[key] = tonumber(value) or value
            end 
        end
        return AccountManager:requestEnterGameWithAuthInfo(logininfo)
    end
    return ErrorCode.FAILED
end

--切换账号时 需要先查下是否在账号的历史记录里
function LoginSystem:__findHistory()
    local findhistory = false
    local datadef = AccountManager:get_account_history_list() or {}
    local mininum = #datadef
    for i=1,mininum do
        if datadef[i].Uin~=nil then 
            local stringuin = tostring(datadef[i].Uin) or ''
            if string_of_uin == stringuin then
                findhistory = true
                break
            end
        end
    end

    if not findhistory then
        if pwd == '0' or pwd == 0 then
            print('setAccountPasswd can not login ')
            return false
        end
    end
    return true
end

--是否发送登陆成功的事件
function LoginSystem:__checkSendLoginSuccess(retList, loginType)
    retList = retList or {}
    local code = retList[1]
    local ignoreFail = retList.ignoreFail
    if code == ErrorCode.OK then
        self:__emitEvent(self.Event_Key.Login_Success, MNSandbox.SandboxContext())
    else
        if not ignoreFail then
            local context = MNSandbox.SandboxContext():SetData_Number("code", tonumber(code) or -1)
            self:__emitEvent(self.Event_Key.Login_Fail, context)
        end
    end
    self:__emitEvent(self.Event_Key.Login_End, MNSandbox.SandboxContext())
end

--封装data_update函数
function LoginSystem:__data_update(sync_role_info)
    self:__emitEvent(self.EVENT_ID.Login_Data_Update_Begin, MNSandbox.SandboxContext())
    local retList = {AccountManager:data_update(sync_role_info)}
    --self:dispatchEvent(self.EVENT_ID.Login_Data_Update_End)
    self:__emitEvent(self.Event_Key.Login_Data_Update_End, MNSandbox.SandboxContext())
    return unpack(retList)
end

--密码检测
function LoginSystem:__isUinPwdOk(uin, pwd)
    if not self:__isUinOk(uin) then
        return false
    end
    return true
end

--问题检测
function LoginSystem:__isUinQuestionOk(uin, question)
    if not self:__isUinOk(uin) then
        return false
    end
    return self:__isQuestionOk(question)
end

--验证信息检测
function LoginSystem:__isUinAuthInfoOk(uin, authinfo)
    if not self:__isUinOk(uin) then
        return false
    end
    return self:__isAuthinfoOk(authinfo)
end

--uin检测
function LoginSystem:__isUinOk(uin)
    local ret = tonumber(uin) or 0
    return ret > 1
end

--登录失败
function LoginSystem:__emitEvent(type, context)
    --self.Event_Key.Login_Fail
    SandboxLua.eventDispatcher:Emit(nil, type, context)
end

class.define("LoginSystem", LoginSystem)


--通用ui处理类--暂时放在这个问题建立，后面会拆分到ui文件夹内
local LoginUiHelper = {}

function LoginUiHelper:destroy()
    local LoginSystem = AccountManager:getLoginSystem()
    -- LoginSystem:unRegEventByObj(self)
end

function LoginUiHelper:constructor()
    local LoginSystem = AccountManager:getLoginSystem()

    self.handlerLoginFailList = {
        [LoginSystem.LOGIN_TYPE.Login_Pwd] = LoginSystem:handler(self, self.handlerLoginEventFail),
    }
    
    -- LoginSystem:regEvent(LoginSystem.EVENT_ID.Login_Fail, self, LoginSystem:handler(self, self.handlerLoginEventFail))
end

function LoginUiHelper:handlerLoginEventFail(loginType, code, customData)
    local newLoginType = customData.loginType
    if code == ErrorCode.PRECHECK_ACCOUNT_IS_FREEZED then
        local timeleft = 0
        if customData and customData.timeleft then
            timeleft = customData.timeleft
        end
        ShowGameTips(GetS(10589, os.date("%Y-%m-%d %H:%M", getServerNow() + timeleft)))
    elseif code == ErrorCode.PRECHECK_APIID_NOT_OK then 
        local ApiID_Svr = customData and customData.ApiID
        if ApiID_Svr and ShowTipsLoginFailByApiId then 
            ShowTipsLoginFailByApiId(ApiID_Svr)
        end 
    else
        local errStrId = t_ErrorCodeToString[code]
        if errStrId then
            ShowGameTips(GetS(errStrId), 3)
        else
            print("LoginUiHelper:login fail",code)
        end
    end

    self:__handlerLoginTypeFail(newLoginType, code, customData)
end

function LoginUiHelper:handlerLoginPwdEventFail(code, customData)
    --看切号的窗体是否打开过
    if AccountManager.pwd_wrong then
        if GetPEGuidanceHappened then
            if GetPEGuidanceHappened() then
                return
            else
                --重连很多次了
                PasswordErrorGuidance()
                return
            end
        end
    end
end

function LoginUiHelper:__handlerLoginTypeFail(loginType, code, customData)
    local f = self.handlerLoginFailList[loginType]
    if f then
        f(code, customData)
    end
end



class.define("LoginUiHelper", LoginUiHelper)