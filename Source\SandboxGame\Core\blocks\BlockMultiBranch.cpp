#include "BlockMultiBranch.h"
#include "world.h"
#include "IClientPlayer.h"
#include "ClientActor.h"
#include "ClientPlayer.h"
#include "basesection.h"
#include "container_sandboxGame.h"
#include "section.h"
#include "BlockGeom.h"
#include "special_blockid.h"
#include "BlockGeom.h"
#include "SectionMesh.h"
#include "ShareRenderMaterial.h"
IMPLEMENT_BLOCKMATERIAL(BlockMultiBranch)

BlockMultiBranch::BlockMultiBranch() : BlockBranch()
{
    // 如有需要初始化
}

void BlockMultiBranch::init(int resid)
{
    BlockBranch::init(resid);
    SetToggle(BlockToggle_HasContainer, true);
	SetToggle(BlockToggle_IsOpaqueCube, true);
}

// 从多方块树枝的任何部分找到核心方块位置
WCoord BlockMultiBranch::getCoreBlockPos(World* pworld, const WCoord& blockpos, int blockdata)
{
    if (blockdata == -1)
        blockdata = pworld->getBlockData(blockpos);
    
    // 如果已经是核心方块，直接返回其位置
    if (isCoreBlock(blockdata))
        return blockpos;
    
    // 如果是扩展方块（位于上方），则核心方块位于下方
    WCoord possibleCorePos = blockpos - WCoord(0, 1, 0);
    
    // 检查此位置是否有有效的核心方块
    int coreBlockData = pworld->getBlockData(possibleCorePos);
    if (pworld->getBlockID(possibleCorePos) == getBlockResID() && isCoreBlock(coreBlockData))
    {
        return possibleCorePos;
    }
    
    // 未找到有效的核心
    return WCoord(0, -1, 0);
}

int BlockMultiBranch::getPlaceBlockDataWithPlayer(World* pworld, IClientPlayer* player, const WCoord& blockpos, DirectionType face, float hitptx, float hitpty, float hitptz, int def_blockdata)
{
    // 从玩家获取基本数据（方向）
    int baseData = getPlaceBlockDataByPlayer(pworld, player);
    // 核心方块不添加扩展标志
    return baseData;
}

int BlockMultiBranch::getPlaceBlockDataByPlayer(World* pworld, IClientPlayer* player)
{
    // 获取玩家当前面向的方向并转换为0-3的值
    if (player)
    {
        ClientPlayer* playerTmp = player->GetPlayer();
        if (!playerTmp) return 0;
        return playerTmp->getCurPlaceDir();
    }
    return 0;
}

bool BlockMultiBranch::onBlockRepaired(World* pworld, const WCoord& blockpos, IClientPlayer* player, float amount)
{
    return onRepairedBlock(pworld, blockpos, player->GetPlayer(), amount, this) > 0;
    //if (pworld->isRemoteMode())
    //{
    //    return true;
    //}
    //ClientPlayer* playerTmp = player->GetPlayer();
    //int toolID = playerTmp->getCurToolID();
    //const ItemDef* def = GetDefManagerProxy()->getItemDef(toolID);
    //if (def && def->UseTarget == ITEM_USE_BUILDBLOCKREPAIR)
    //{
    //    int blockdata = pworld->getBlockData(blockpos);
    //    WCoord corePos = getCoreBlockPos(pworld, blockpos, blockdata);
    //    containerArchitecture* container = dynamic_cast<containerArchitecture*>(pworld->getContainerMgr()->getContainer(corePos));
    //    if (container)
    //    {
    //        if (container->checkArchitectureResEnough(2, 0, playerTmp, true) >= 0)
    //        {
    //            container->addHp(amount);
    //            return true;
    //        }
    //    }
    //}
    //return false;
}

bool BlockMultiBranch::onBlockUpGrade(World* pworld, const WCoord& blockpos, int upgradeNum, IClientPlayer* player)
{
    return onUpgradeBlock(pworld, blockpos, upgradeNum, player->GetPlayer(), this) > 0;
}

bool BlockMultiBranch::onBlockDamaged(World* pworld, const WCoord& blockpos, IClientPlayer* player, int attack_type, float damage)
{
    return onDamaged(pworld, blockpos, player->GetPlayer(), attack_type, damage);
    //if (pworld->isRemoteMode())
    //{
    //    return true;
    //}
    //int blockdata = pworld->getBlockData(blockpos);
    //WCoord corePos = getCoreBlockPos(pworld, blockpos, blockdata);
    //containerArchitecture* container = dynamic_cast<containerArchitecture*>(pworld->getContainerMgr()->getContainer(corePos));
    //if (container)
    //{
    //    container->addHp(-damage); // 负数扣血
    //    return true;
    //}
    //return false;
}

void BlockMultiBranch::onBlockDestroyedBy(World* pworld, const WCoord& blockpos, int blockdata, BLOCK_DESTROY_REASON_T destroytype, IClientActor* bywho)
{
    onDestroyBlock(pworld, blockpos, dynamic_cast<ClientPlayer*>(bywho));
}

void BlockMultiBranch::dropBlockAsItem(World* pworld, const WCoord& blockpos, int blockdata, BLOCK_MINE_TYPE droptype, float chance, int uin/* = -1 */)
{
    // 只有核心方块掉落物品
    if (isCoreBlock(blockdata))
    {
        // 只保留方向信息
        BlockBranch::dropBlockAsItem(pworld, blockpos, blockdata & 3, droptype, chance, uin);
    }
}

void BlockMultiBranch::onBlockPlacedBy(World* pworld, const WCoord& blockpos, IClientPlayer* player)
{
    int blockdata = pworld->getBlockData(blockpos);
    int blockid = pworld->getBlockID(blockpos);
    // 只有核心方块创建扩展
    if (isCoreBlock(blockdata))
    {
        // 创建一个扩展方块，位于核心正上方
        WCoord extendPos = blockpos + WCoord(0, 1, 0);

        // 扩展方块数据：
        // - 位0-1：方向（与核心相同）
        // - 位3：扩展方块标志
        int extendData = (blockdata & 3) | 8; // 保留方向状态，添加扩展标志

        // 放置扩展方块
        pworld->setBlockAll(extendPos, blockid, extendData);
    }
}

void BlockMultiBranch::onBlockAdded(World* pworld, const WCoord& blockpos)
{
}

bool BlockMultiBranch::getBlockRange(World* pworld, const WCoord& blockpos, std::vector<WCoord>& blockList, bool includeSelf)
{
    WCoord corePos = getCoreBlockPos(pworld, blockpos);
    if (corePos != WCoord(0, -1, 0))
    {
        auto blockdata = pworld->getBlockData(corePos);

        WCoord extendPos = corePos + WCoord(0, 1, 0);
        blockList.push_back(extendPos);
        if (includeSelf) blockList.push_back(corePos);
        return true;
    }
    return false;
}

void BlockMultiBranch::onBlockRemoved(World* pworld, const WCoord& blockpos, int blockid, int blockdata)
{
    // 找到核心方块
    WCoord corePos = getCoreBlockPos(pworld, blockpos, blockdata);
    if (corePos.y < 0)
        return; // 无效的核心位置
    
    // 如果移除的是核心方块，也移除扩展方块
    if (isCoreBlock(blockdata))
    {
        WCoord extendPos = blockpos + WCoord(0, 1, 0);
        if (pworld->getBlockID(extendPos) == getBlockResID())
        {
            pworld->setBlockAll(extendPos, 0, 0);
        }
    }
    // 如果移除的是扩展方块，也移除核心方块
    else
    {
        pworld->setBlockAll(corePos, 0, 0);
    }
}

bool BlockMultiBranch::canPutOntoPlayer(World* pworld, const WCoord& blockpos, IClientPlayer* player)
{
    // 检查上方位置是否可以放置扩展方块
    WCoord extendPos = blockpos + WCoord(0, 1, 0);
    
    auto* mtl = pworld->getBlockMaterial(extendPos);
    
    // 检查这个位置是否可以放置方块
    if (mtl && !mtl->canPutOntoPos(pworld->getWorldProxy(), extendPos))
        return false;
    
    // 检查建造权限
    if (!pworld->CanBuildAtPosition(extendPos, player->getUin()))
        return false;
    
    // 检查核心方块位置权限
    return pworld->CanBuildAtPosition(blockpos, player->getUin());
}

WorldContainer* BlockMultiBranch::createContainer(World* pworld, const WCoord& blockpos)
{
    int blockdata = pworld->getBlockData(blockpos);
    // 获取核心方块位置
    WCoord corePos = getCoreBlockPos(pworld, blockpos, blockdata);
    if (corePos == blockpos)
    {
		int blockId = pworld->getBlockID(blockpos);
        int bpTypeId = 0;
        int bpLevel = 0;
        initBuildData(blockId, bpTypeId, bpLevel);
        containerArchitecture* container = SANDBOX_NEW(containerArchitecture, blockpos, blockId, bpTypeId, bpLevel);
        return container;
    }
    return nullptr;
} 

int BlockMultiBranch::getBlockHP(World* pworld, const WCoord& blockpos)
{
	int blockdata = pworld->getBlockData(blockpos);
	WCoord corePos = getCoreBlockPos(pworld, blockpos, blockdata);
	containerArchitecture* container = dynamic_cast<containerArchitecture*>(pworld->getContainerMgr()->getContainer(corePos));
	if (container)
	{
		// 获取容器的HP
		return container->getHp();
	}
	else
	{
		// 如果没有容器，返回默认值
		return 0;
	}
}

WorldContainer* BlockMultiBranch::getCoreContainer(World* pworld, const WCoord& blockpos)
{
    return GetArchitecturalCoreContainer(pworld, blockpos);
}

void BlockMultiBranch::createBlockMeshPreview(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh)
{
	BlockGeomTemplate* geom = getGeom(data.m_LODLevel);
	if (!geom)
		return;

	auto psection = data.m_SharedSectionData;
	Block pblock = psection->getBlock(blockpos);
	int blockdata = pblock.getData();
    bool mirror;
	int dir = 3&blockdata;

	Rainbow::ColorRGBAf verts_light[1] = { Rainbow::ColorRGBAf::black };
	RenderBlockMaterial* mtl;
	BlockGeomMeshInfo meshinfo;
	mirror = false;
	

	mtl = getDefaultMtl();
	geom->getFaceVerts(meshinfo, 48, 1.0f, 0, dir, mirror);
	

	SectionSubMesh* psubmesh = poutmesh->getSubMesh(mtl);

	psection->getBlockVertexLight(blockpos, verts_light);
	psubmesh->addGeomBlockLight(meshinfo, &blockpos, verts_light, NULL, mtl->getUVTile());
}