#ifndef __BLOCKCOAGULATION_H__
#define __BLOCKCOAGULATION_H__

#include "BlockMaterial.h"

class BlockCoagulation : public CubeBlockMaterial //tolua_exports
{ //tolua_exports
	//typedef CubeBlockMaterial Super;
	DECLARE_BLOCKMATERIAL(BlockCoagulation)
public:
	virtual void initDefaultMtl() override;
	//tolua_begin
	BlockCoagulation();
	virtual ~BlockCoagulation();
	virtual bool canAttachedToMecha(int curblockdata, DirectionType dir, int neighbor_id, int neighbor_data) override
	{
		return false;
	}
	virtual void init(int resid) override;
	virtual void initGeomName() override;
	virtual bool hasContainer() override
	{
		return true;
	}
	//virtual const char* getGeomName()
	//{
	//	return "coagulation";
	//}
	//virtual bool hasContainer() override
	//{
	//	return true;
	//}
	//virtual int getTickInterval()
	//{
	//	return 30;
	//}
	/*virtual bool canTickImmediate()
	{
		return false;
	}*/
	//tolua_end
public:
	//tolua_begin
	virtual bool onTrigger(World* pworld, const WCoord& blockpos, DirectionType face, IClientPlayer* player, const Rainbow::Vector3f& colpoint = Rainbow::Vector3f(0, 0, 0));
	bool isWaterBlock(World* pworld, const WCoord& blockpos);
	virtual bool isOpaqueCube();
	virtual RenderBlockMaterial* getFaceMtl(const BiomeDef* biome, DirectionType dir, int blockdata, BlockColor& facecolor) override;
	virtual void onBlockPlacedBy(World* pworld, const WCoord& blockpos, IClientPlayer* player);
	virtual void onBlockAdded(World* pworld, const WCoord& blockpos);
	virtual void onBlockRemoved(World* pworld, const WCoord& blockpos, int blockid, int blockdata);
	virtual WorldContainer* createContainer(World* pworld, const WCoord& blockpos) override;
	virtual void dropBlockAsItemWithToolId(World *pworld, const WCoord &blockpos, int blockdata, BLOCK_MINE_TYPE droptype, float chance, int useToolId, int uin = -1) override;
	virtual int getBlockGeomID(int* idbuf, int* dirbuf, const SectionDataHandler* sectionData, const WCoord& blockpos, World* world) override;
	virtual void onNotify(World* pworld, const WCoord& blockpos, int blockid) override;
	virtual void blockTick(World* pworld, const WCoord& blockpos);
	virtual bool onActorCollidedWithBlock(World* pworld, const WCoord& blockpos, IClientActor* actor);
	virtual void onActorWalking(World *pworld, const WCoord &blockpos, IClientActor *actor) override;
	void isTouchLava(World* pworld, const WCoord& blockpos);
	//tolua_end
protected:
	virtual int getTextureType();

private:
	bool canNeighborBurn(WorldProxy* pworld, const WCoord& blockpos);
	bool canBlockCatchFire(WorldProxy* pworld, const WCoord& blockpos);
	bool isTouchWater(World* pworld, const WCoord& blockpos);
	void tryToCatchBlockOnFire(World *pworld, const WCoord &blockpos, int max_catchfire, int firestrength);
	int getChanceOfNeighborsEncouragingFire(World *pworld, const WCoord &blockpos);
	int getChanceToEncourageFire(World *pworld, const WCoord &blockpos, int speed);

private:
	Rainbow::SharePtr<Rainbow::Texture2D>   m_BaseTex;
	RenderBlockMaterial* m_Stable; //黑凝浆块 稳定状态贴图
	RenderBlockMaterial* m_StoreEnergy_1;//黑凝浆块 蓄能1状态贴图
	RenderBlockMaterial* m_StoreEnergy_2;//黑凝浆块 蓄能2状态贴图
	RenderBlockMaterial* m_BlowUp;//黑凝浆块 喷发状态贴图
}; //tolua_exports

#endif
