
#ifndef __BLOCK_GRAVEL_H__
#define __BLOCK_GRAVEL_H__

#include "BlockBasic.h"
//#include "ActorFallingGravel.h"
#include "BlockPressurePlate.h"
#include <deque>


class BlockMesh;
class ActorFallingGravel;

class BlockGravel : public BasicBlockMaterial //tolua_exports
{ //tolua_exports
	//typedef BasicBlockMaterial Super;
	DECLARE_BLOCKMATERIAL(BlockGravel)
public:
	//tolua_begin

	BlockGravel();

	virtual void init(int resid) override;
	virtual void dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata=0, BLOCK_MINE_TYPE droptype=BLOCK_MINE_NOTOOL, float chance=1.0f, int uin = -1);

	bool canFallBelow(World *pworld, const WCoord &blockpos);

	virtual void onActorWalking(World *pworld, const WCoord &blockpos, IClientActor *actor);
	virtual void onBlockDestroyedBy(World *pworld, const WCoord &blockpos, int blockdata, BLOCK_DESTROY_REASON_T destroytype, IClientActor *bywho);

	virtual void onBlockAdded(World *pworld, const WCoord &blockpos);
	virtual void onNotify(World *pworld, const WCoord &blockpos, int blockid);
	virtual void blockTick(World *pworld, const WCoord &blockpos);
	//virtual int getTickInterval()
	//{
	//	return 2;
	//}
	virtual void update(unsigned int dtick);

	virtual void onStartFalling(ActorFallingGravel *actor)
	{
	}
	virtual void onFinishFalling(World *pworld, const WCoord &blockpos, int blockdata)
	{
	}
	virtual void tickCS();
	//tolua_end
private:
	bool inTheFallingQueue(const WCoord &blockpos);

	void checkoutFall(World *pworld, const WCoord &blockpos);
	void tryToFall(World *pworld, const WCoord &blockpos);

private:
	std::deque<std::pair<WCoord, World*> > m_FallingBlockQueue;
	WCoord m_LastStepOnPos;
	long m_TickCount;
}; //tolua_exports

#endif