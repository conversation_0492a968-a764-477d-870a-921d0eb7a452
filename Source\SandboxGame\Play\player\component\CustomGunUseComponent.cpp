﻿#include "CustomGunUseComponent.h"
#include "ClientPlayer.h"
#include "world.h"
#include "PlayerAttrib.h"
#include "backpack.h"
#include "SandboxIdDef.h"
#include "LuaInterfaceProxy.h"
#include "defdata.h"
#include "PlayerControl.h"
#include "GameCamera.h"
#include "ClientInfoProxy.h"
#include "PCControlLua.h"
#include "TouchControlLua.h"
#include "ActorLocoMotion.h"
#include "EffectExplosion.h"
#include "SandBoxManager.h"
#include "SandboxResult.h"
#include "SandboxCoreDriver.h"
#include "ProjectileFactory.h"
#include "EffectManager.h"
#include "BulletMgr.h"
#include "WorldManager.h"
#include "Physics/IRaycast.h"
#include "OgrePhysXManager.h"
#include "AttackedComponent.h"
#include "GameNetManager.h"
#include "Components/Camera.h"
#include "InputInfo.h"
#include "ActionIdleStateGunAdvance.h"
#include "EffectParticle.h"
#include "EffectComponent.h"
#include "ItemUseComponent.h"
#include "ScriptComponent.h"
#include "UIRenderer.h"
#include "BlockMaterialMgr.h"
#include "GunGridDataComponent.h"
#include "ClientMob.h"
#include "GameMode.h"
#include "ActorBody.h"
#include "ICloudProxy.h"
#include "PlayerCheat.h"

using namespace MNSandbox;
using namespace Rainbow;
using namespace UILib;

// 中文总览：
// 该组件负责“定制枪械”的完整使用流程：
// - 生命周期：进入/离开宿主与初始化
// - 射击主循环：每帧更新、触发击发、生成射线、命中结算
// - 后坐力系统：连发叠加、时间恢复、相机旋转驱动
// - 准星与散布：首发与连发散布、准星扩散/回收
// - 辅助瞄准：小圈吸附、自瞄、自动开火、大圈减速与灵敏度控制
// - 联机校验：主机侧开火间隔/弹片/弹药校验与重放
// - 穿透与衰减：空气/液体/固体/生物/实体的穿透与伤害

bool CustomGunSetting::AimAssist = false; //吸附瞄准
bool CustomGunSetting::AutoFire = false; //自动开火
bool CustomGunSetting::AimReduce = false; //准星减速
bool CustomGunSetting::AutoAim = false; //开火自动瞄准
bool CustomGunSetting::AdsRelease = true; //瞄准按钮按下生效抬起取消 (暂PC有效)

EXPORT_SANDBOXENGINE extern  bool IsBlockCollideWithRay(BlockMaterial* blockmtl, World* pworld, const WCoord& blockpos, const Rainbow::Vector3f& origin, const Rainbow::Vector3f& dir, IntersectResult* presult);
EXPORT_SANDBOXENGINE extern  int signum(float x);

// 功能：根据当前游戏规则（队伍/驯服关系/全局开关）判断是否允许 mainPlayer 攻击目标玩家或生物
static bool CMP_CanAttack(ClientPlayer* mainPlayer, ClientMob* mob, ClientPlayer* player)
{
	if (!GetWorldManagerPtr())
		return true;

	int opt = (int)GetWorldManagerPtr()->getModAttackType();
	if (opt == -1 && GetWorldManagerPtr()->m_RuleMgr)
	{
		opt = (int)GetWorldManagerPtr()->m_RuleMgr->getRuleOptionVal(GMRULE_ATTACKPLAYER);
	}

	if (opt == 1)//禁止攻击玩家
	{
		if (player != NULL) 
			return false;
		return true;
	}
	else if (opt == 2) //只有队伍间允许攻击
	{
		if (player)
		{
			//return mainPlayer->getTeamType(player) != TEAM_TYPE_FRIENDLY;
			return false; //玩家之间没有不能攻击的
		}
		else if (mob)
		{
			if (mob->getTamedOwner() == mainPlayer)
			{
				return false;
			}
			else if (mainPlayer->getTeamType(mob) == TEAM_TYPE_FRIENDLY)
			{
				return false;
			}
			else
			{
				return !(mainPlayer->isSameTeam(mob));
			}
		}
	}

	//0 自由攻击
	return true;
}

//#define SHOW_DEBUG_LINE
//radius半径 (xCenter,yCenter)圆心 (x1, y1)矩形左下角
// 功能：检测以(xCenter,yCenter)为圆心、半径为radius的圆与屏幕矩形(x1,y1)-(x2,y2)是否相交
static bool checkOverlap(float radius, float xCenter, float yCenter, float x1, float y1, float x2, float y2) {
	// 求矩阵中心
	float xrc = 1.0 * (x2 + x1) / 2, yrc = 1.0 * (y1 + y2) / 2;
	// 矩形对角线半长向量
	float xhd = abs(x1 - xrc), yhd = abs(y1 - yrc);
	// 第1步：圆转换至以矩形中心为源点的第一象限，求出两点的距离向量
	float xdis = abs(xCenter - xrc), ydis = abs(yCenter - yrc);
	// 第2步：求圆心到矩阵的最短距离向量，负数的分量置为0
	
	float xmd = std::max(xdis - xhd, 0.0f), ymd = std::max(ydis - yhd, 0.0f);
	// 第3步：判断圆心到矩阵的最短距离是否小于圆半径
	return xmd * xmd + ymd * ymd <= radius * radius;
}

// 功能：更新屏幕AABB最小/最大点
static void clampV3f(Vector3f& screenMinPos, Vector3f& screenMaxPos, Vector3f& pos)
{
	if (pos.x > screenMaxPos.x)
	{
		screenMaxPos.x = pos.x;
	}
	if (pos.y > screenMaxPos.y)
	{
		screenMaxPos.y = pos.y;
	}

	if (pos.x < screenMinPos.x)
	{
		screenMinPos.x = pos.x;
	}
	if (pos.y < screenMinPos.y)
	{
		screenMinPos.y = pos.y;
	}

}

// 功能：将三维AABB八个角转换为屏幕坐标，得到其屏幕空间包围矩形
static void getMinMaxPos(Rainbow::Camera* camera, Vector3f& minPos, Vector3f& dim, Vector3f& screenMinPos, Vector3f& screenMaxPos)
{
	Vector3f pos1 = Vector3f(minPos.x, minPos.y, minPos.z);
	pos1 = camera->WorldToScreenPoint(pos1);
	screenMinPos = pos1;
	screenMaxPos = pos1;

	Vector3f pos2 = Vector3f(minPos.x + dim.x, minPos.y, minPos.z);
	pos2 = camera->WorldToScreenPoint(pos2);
	clampV3f(screenMinPos, screenMaxPos, pos2);

	Vector3f pos3 = Vector3f(minPos.x, minPos.y + dim.y, minPos.z);
	pos3 = camera->WorldToScreenPoint(pos3);
	clampV3f(screenMinPos, screenMaxPos, pos3);

	Vector3f pos4 = Vector3f(minPos.x + dim.x, minPos.y + dim.y, minPos.z);
	pos4 = camera->WorldToScreenPoint(pos4);
	clampV3f(screenMinPos, screenMaxPos, pos4);

	Vector3f pos5 = Vector3f(minPos.x, minPos.y, minPos.z + dim.z);
	pos5 = camera->WorldToScreenPoint(pos5);
	clampV3f(screenMinPos, screenMaxPos, pos5);

	Vector3f pos6 = Vector3f(minPos.x + dim.x, minPos.y, minPos.z + dim.z);
	pos6 = camera->WorldToScreenPoint(pos6);
	clampV3f(screenMinPos, screenMaxPos, pos6);

	Vector3f pos7 = Vector3f(minPos.x, minPos.y + dim.y, minPos.z + dim.z);
	pos7 = camera->WorldToScreenPoint(pos7);
	clampV3f(screenMinPos, screenMaxPos, pos7);

	Vector3f pos8 = Vector3f(minPos.x + dim.x, minPos.y + dim.y, minPos.z + dim.z);
	pos8 = camera->WorldToScreenPoint(pos8);
	clampV3f(screenMinPos, screenMaxPos, pos8);
}

IMPLEMENT_COMPONENTCLASS(CustomGunUseComponent)
// 构造函数：初始化枪械使用状态（弹夹、散布/准星、后坐力、灵敏度等）
CustomGunUseComponent::CustomGunUseComponent(): m_shootRecordTime(3, 0)
{
	m_Host = NULL;
	m_GunDef = NULL;
	m_Magazine = 0;
	m_IsFire = false;
	m_CurrentSpread = 0;
	m_FirstSpread = 0;
	m_isRecoverSpread = false;//是否要恢复散射
	m_LastRotateX = 0;
	m_NeedRecovery = false;
	m_fLastFireTime = 0.f;	// 上次开火时间
	m_fireCount = 0;		// 连发计数
	m_hipRecoilAngle = 0.f;			// 腰射垂直后坐力角度
	m_adsRecoilAngle = 0.f;			// 机瞄垂直后坐力角度
	m_hipHorizontalAngle = 0.f;		// 腰射水平后坐力角度
	m_adsHorizontalAngle = 0.f;		// 机瞄水平后坐力角度

	m_CurrentSightSpread = 0.f;//当前准星半径
	m_TargetSightSpread = 0;//变化的目标值
	m_lastFireTimneMs = 0;
}

// 进入宿主：绑定到玩家并注册本组件
void CustomGunUseComponent::OnEnterOwner(NS_SANDBOX::SandboxNode* owner)
{
	m_Host = dynamic_cast<ClientPlayer*>(owner);
	if (m_Host) {
		m_Host->BindCustomGunComponent(this);
	}
	Super::OnEnterOwner(owner);

	//BindOnTick();
}
// 离开宿主：复原灵敏度、取消绑定
void CustomGunUseComponent::OnLeaveOwner(NS_SANDBOX::SandboxNode* owner)
{
	//UnBindOnTick();
	RevertSensitivity();
	if (m_Host) {
		m_Host->clearGunUseUpdate();
		m_Host->BindCustomGunComponent(NULL);
		m_Host = NULL;
	}
	Super::OnLeaveOwner(owner);
}

// 初始化：切枪时重置内部状态并同步UI/脚本；若无枪数据则清除更新回调
void CustomGunUseComponent::Init(GunGridDataComponent* gunData)
{
	//切换要还原灵敏度
	if (m_scopeFactor != 1.f || m_assistFactor != 1.f)
	{
		m_scopeFactor = m_assistFactor = 1.f;
		SetSensitivity();
	}

	m_Magazine = 0;
	m_IsFire = false;
	m_CurrentSpread = 0;
	m_FirstSpread = 0;
	m_isRecoverSpread = false;//是否要恢复散射
	m_LastRotateX = 0;
	m_NeedRecovery = false;
	m_NeedRecoveryH = false;
	m_NeedRecoverySightSpread = false;
	m_fLastFireTime = 0.f;	// 上次开火时间
	m_fireCount = 0;		// 连发计数
	m_hipRecoilAngle = 0.f;			// 腰射垂直后坐力角度
	m_adsRecoilAngle = 0.f;			// 机瞄垂直后坐力角度
	m_hipHorizontalAngle = 0.f;		// 腰射水平后坐力角度
	m_adsHorizontalAngle = 0.f;		// 机瞄水平后坐力角度
	UGCCFG& ugcCfg = GetLuaInterfaceProxy().get_lua_const()->ugcCfg;
	m_resetRcRcvryInptDgrLimit = ugcCfg.get("resetRcRcvryInptDgrLimit", 1.f);
	m_decoiLFullCameraSpeed = ugcCfg.get("decoiLFullCameraSpeed", 40.f);

	m_CurrentSightSpread = 0.f;//当前准星半径
	m_TargetSightSpread = 0.f;//变化的目标值
	m_bLastFireStatus = false;
	m_fCurTime = 0.f;
	m_bNextUpdateFire = false;
	m_bIsLoad = false;
	m_GunDef = NULL;
	m_sensitivity = -1.f;
	m_isLowSensityvity = false;
	m_autoFireFlag = false;
	m_autofireTime = 0.f;//吸附圈里出现敌人的连续时间
	m_lastFireTimneMs = 0;
	m_shootTimes = 0;
	if (gunData)
	{
		setGunDef(gunData->GetGunDef());
		setIsLoad(gunData->getIsLoad());
		setMagazine(gunData->getMagazine(), true);
		setUpdate();
		MINIW::ScriptVM::game()->callFunction("SetSightingTelescopeFrame", "b", true);
	}
	else
	{
		if (m_Host) 
		{
			m_Host->clearGunUseUpdate();
		}
	}
}

//获取背包子弹数
// 从背包中获取当前子弹数量
int CustomGunUseComponent::getBulletNum()
{
	if (m_GunDef == NULL) return 0;
	int costItemId = m_GunDef->bulletId;
	int costItemNum = m_Host->getBackPack()->getItemCountInNormalPack(costItemId);
	return costItemNum;
}

// 设置枪械定义并初始化准星范围
void CustomGunUseComponent::setGunDef(const CustomGunDef* def)
{
	m_GunDef = def;
	if (def)
	{
		m_TargetSightSpread = def->sightMin;
		m_CurrentSightSpread = def->sightMin;
	}
		
}

const CustomGunDef* CustomGunUseComponent::getGunDef()
{
	return m_GunDef;
}

// 重置后坐力：按需清零连发计数并记录上次相机角度，用于后续恢复
void CustomGunUseComponent::resetRecoil(bool bResetFireCount)
{
	PlayerControl* player = g_pPlayerCtrl;
	if (player)
	{
		if (!bResetFireCount && (m_RecentRecoilRotateX != 0.f || m_RecentRecoilRotateY != 0.f)) {
			float fDiffX = abs(player->m_pCamera->m_RotatePitch - m_RecentRecoilRotateX);
			float fDiffY = abs(player->m_pCamera->m_RotateYaw - m_RecentRecoilRotateY);
			if (!(fDiffX >= m_resetRcRcvryInptDgrLimit || fDiffY >= m_resetRcRcvryInptDgrLimit))
			{
				// 因为玩家操作镜头调用到了resetRecoil（不是断了连击）, 且镜头改变的角度小于限定值
				return;
			}
		}

		m_RecentRecoilRotateX = 0.f;
		m_RecentRecoilRotateY = 0.f;
		m_LastRotateX = player->m_pCamera->m_RotatePitch;
		m_LastRotateY = player->m_pCamera->m_RotateYaw;

		if (bResetFireCount) {
			m_hipRecoilAngle = 0.f;			// 腰射垂直后坐力角度
			m_adsRecoilAngle = 0.f;			// 机瞄垂直后坐力角度
			m_hipHorizontalAngle = 0.f;		// 腰射水平后坐力角度
			m_adsHorizontalAngle = 0.f;		// 机瞄水平后坐力角度
			m_fireCount = 0;
			m_fLastFireTime = 0.f;
		}
	}
}

// 执行后坐力：在combo窗口内按速度扣减目标角度并驱动相机旋转
void CustomGunUseComponent::PerformRecoil(float fDiff, bool bImitate)
{
	if (m_GunDef == NULL) return;

	PlayerControl* player = g_pPlayerCtrl;
	if (!(player && player->m_pCamera))
		return;

	if (fDiff > 0.f)
	{
		bool isAimState = player->GetIdleStateGunAdvance()->IsAimState();
		float* pRecoilAngle = isAimState ? &m_adsRecoilAngle : &m_hipRecoilAngle;
		float* pHorizontalAngle = isAimState ? &m_adsHorizontalAngle : &m_hipHorizontalAngle;

		if (*pRecoilAngle > FLT_EPSILON || abs(*pHorizontalAngle) > FLT_EPSILON)
		{
			float timeDiffForRecoil = fDiff;
			if (timeDiffForRecoil >= m_GunDef->comboResetTime)
			{
				timeDiffForRecoil = m_GunDef->comboResetTime;
			}

			float angleDiff = 0.f;
			float angleDiffHorizontal = 0.f;
			if (*pRecoilAngle > FLT_EPSILON)
			{
				angleDiff = timeDiffForRecoil * m_decoiLFullCameraSpeed * m_GunDef->recoilCameraSpeed;
				if (angleDiff > *pRecoilAngle)
					angleDiff = *pRecoilAngle;

				(*pRecoilAngle) -= angleDiff;
			}

			if (abs(*pHorizontalAngle) > FLT_EPSILON)
			{
				angleDiffHorizontal = timeDiffForRecoil * m_decoiLFullCameraSpeed * m_GunDef->recoilCameraSpeed;
				if (angleDiffHorizontal > abs(*pHorizontalAngle))
					angleDiffHorizontal = abs(*pHorizontalAngle);

				if (*pHorizontalAngle < 0.f)
					angleDiffHorizontal = -angleDiffHorizontal;

				(*pHorizontalAngle) -= angleDiffHorizontal;
			}

			player->m_pCamera->rotate(angleDiffHorizontal / 180.f, -angleDiff / 90.f);
			if (bImitate)
			{
				player->m_pCamera->applyToEngineWithLerpOrNot(player->getWorld(), false);
			}
		}
	}
}

//恢复后坐力
// 恢复后坐力：在combo窗口之外按复位速度将相机拉回开火前角度
void CustomGunUseComponent::PerformRecoilRecovery(float fDiff, bool bImitate)
{
	if (m_GunDef == NULL) return;
	PlayerControl* player = g_pPlayerCtrl;
	if (!(player && player->m_pCamera))
		return;

	if (m_NeedRecoverySightSpread)
	{
		m_isRecoverSpread = true;//开始缩小散布大小
		m_TargetSightSpread = m_GunDef->sightMin;//firecount是0 的时候重置准星
		m_NeedRecoverySightSpread = false;
	}

	bool bRotated = false;
	if (m_NeedRecovery && m_GunDef->recoilResetSpeed > FLT_EPSILON)
	{
		if (m_LastRotateX - player->m_pCamera->m_RotatePitch > FLT_EPSILON)
		{
			float step = fDiff * m_GunDef->recoilResetSpeed;
			float maxStep = m_LastRotateX - player->m_pCamera->m_RotatePitch;
			if (step >= maxStep) {
				step = maxStep;
				m_NeedRecovery = false;
			}
			player->m_pCamera->rotate(0.f, step / 90.f);
			bRotated = true;
		}
		else
		{
			m_NeedRecovery = false;
		}
	}

	if (m_NeedRecoveryH && m_GunDef->recoilResetSpeedHorizontal > FLT_EPSILON) {
		float step = fDiff * m_GunDef->recoilResetSpeedHorizontal;
		float curCameraYaw = player->m_pCamera->m_RotateYaw;
		float lastRotateY = m_LastRotateY;
		if (abs(lastRotateY - curCameraYaw) > 180.f)
		{
			// 两个点在360度的两测
			if (lastRotateY > curCameraYaw)
				curCameraYaw += 360;
			else
				lastRotateY += 360;
		}
		if (lastRotateY - curCameraYaw > FLT_EPSILON)
		{
			float maxStep = lastRotateY - curCameraYaw;
			if (step >= maxStep) {
				step = maxStep;
				m_NeedRecoveryH = false;
			}
			player->m_pCamera->rotate(step / 180.f, 0.f);
			bRotated = true;
		}
		else if (curCameraYaw - lastRotateY > FLT_EPSILON)
		{
			float maxStep = curCameraYaw - lastRotateY;
			if (step >= maxStep) {
				step = maxStep;
				m_NeedRecoveryH = false;
			}
			player->m_pCamera->rotate(-step / 180.f, 0.f);
			bRotated = true;
		}
		else
		{
			m_NeedRecoveryH = false;
		}
	}
	if (bRotated && bImitate)
	{
		player->m_pCamera->applyToEngineWithLerpOrNot(player->getWorld(), false);
	}
}

// 综合应用后坐力与恢复：先按后坐力叠加，再超时部分执行恢复
void CustomGunUseComponent::PerformRecoilAndRecovery(float fDiff, bool bImitate)
{
	if (m_GunDef == NULL) return;

	if (m_fLastFireTime > 0.f) {
		PlayerControl* player = g_pPlayerCtrl;
		if (player && player->m_pCamera)
		{
			if (fDiff == 0.f)
			{
				float fCur = m_fCurTime;
				fDiff = fCur - m_fLastFireTime;
			}
			if (fDiff > 0.f)
			{
				PerformRecoil(fDiff, bImitate);

				if (fDiff > m_GunDef->comboResetTime)
				{
					fDiff = fDiff - m_GunDef->comboResetTime;
					PerformRecoilRecovery(fDiff, bImitate);
				}

				m_RecentRecoilRotateX = player->m_pCamera->m_RotatePitch;
				m_RecentRecoilRotateY = player->m_pCamera->m_RotateYaw;
			}
		}
	}
}

// 每帧更新：补齐应击发次数、应用后坐力/恢复、处理散布/准星渐变与辅助瞄准
void CustomGunUseComponent::OnUpdate(float dtime)
{
	if (m_GunDef == NULL || g_pPlayerCtrl == NULL) return;
	if (g_pPlayerCtrl != m_Host) return;
	m_fCurTime += dtime;
	// 是否是一直连击状态 isFire为flase表示刚reload过
	if (!(g_pPlayerCtrl->m_InputInfo->leftClick || g_pPlayerCtrl->m_InputInfo->useAction))
	{
		m_bLastFireStatus = false;
	}
	if (m_bNextUpdateFire && m_Magazine >= bulletConsume())
	{
		m_bLastFireStatus = m_IsFire && m_bLastFireStatus;
		int nNeedFireTimes = 1;
		if (m_bLastFireStatus && m_fLastFireTime > 0.f)
		{
			float curTime = m_fCurTime;
			float fDiff = curTime - m_fLastFireTime;
			float fFireRate = 60.0f / rpm();
			nNeedFireTimes = fDiff / fFireRate;
		}
		if (nNeedFireTimes > 0)
		{
			int nFireCount = FireOnceInternal(m_bLastFireStatus, nNeedFireTimes);
			if (nFireCount == nNeedFireTimes)
			{
				m_bLastFireStatus = true;
			}
		}
	}
	PerformRecoilAndRecovery(0.f, true);
	m_bNextUpdateFire = false;

	//处理散布恢复
	if (m_isRecoverSpread)
	{
		float deta = dtime * m_GunDef->spreadResetSpeed;
		m_CurrentSpread -= deta;

		if (m_CurrentSpread <= 0)
		{
			m_CurrentSpread = 0;
			m_isRecoverSpread = false;
		}
	}

	//处理准星扩散恢复
	if (m_TargetSightSpread > m_CurrentSightSpread)
	{
		float deta = dtime * (float)m_GunDef->sightDiffuseSpeed;
		m_CurrentSightSpread += deta;

		if (m_CurrentSightSpread > m_TargetSightSpread)
			m_CurrentSightSpread = m_TargetSightSpread;
	}
	else if(m_TargetSightSpread < m_CurrentSightSpread)
	{
		float deta = dtime * (float)m_GunDef->sightResetSpeed;
		m_CurrentSightSpread -= deta;

		if (m_CurrentSightSpread < m_TargetSightSpread)
			m_CurrentSightSpread = m_TargetSightSpread;
	}


	//处理辅助瞄准, 吸附 自动开火 准星减速
	if (m_GunDef->skillId.empty() && (CustomGunSetting::AimAssist || CustomGunSetting::AutoFire || CustomGunSetting::AimReduce))
	{
		CheckAimAssist(dtime);
	}
}

// 辅助瞄准：
// - 以屏幕AABB与两级圆（小圈吸附/大圈减速）相交作为触发
// - 小圈：可自瞄/自动开火；大圈：降低灵敏度
// - 做掩体遮挡检测，避免穿墙
void CustomGunUseComponent::CheckAimAssist(float dtime)
{
	//是否是瞄准状态（否则为腰射状态）
	bool isAimState = m_Host->GetIdleStateGunAdvance()->IsAimState();
	if (!m_Host)
		return;

	auto pWorld = m_Host->getWorld();
	if (!pWorld || !pWorld->getActorMgr())
		return;

	//auto pGameLuaConf = GetUGCManagerPtr()->GetGameLuaConf();
	//if (!pGameLuaConf)
	//	return nullptr;
	PlayerControl* player = g_pPlayerCtrl;
	if (!player || !player->isSightMode())
		return;

	if (!player->m_pCamera->isCameraVaild())
		return;

	float maxRange = GetGunRange();//射程
	MINIW::WorldRay worldray;
	MINIW::Ray ray;
	player->m_pCamera->getViewRayByScreenPt(&worldray, 0.5, 0.5);
	worldray.m_Range = maxRange;
	worldray.getRelativeRay(ray);
	maxRange *= maxRange;

	Vector3f origin = worldray.m_Origin.toVector3();
	Vector3f& dir = worldray.m_Dir;
	const WCoord& playerPos = player->getPosition();//玩家位置
	int clientWidth, clientHeight;
	GetClientInfoProxy()->getClientWindowSize(clientWidth, clientHeight);
	float ox = clientWidth * 0.5f;
	float oy = clientHeight * 0.5f;

	const std::vector<ClientPlayer*>& vPlayers = pWorld->getActorMgr()->ToCastMgr()->getAllPlayer();
	std::vector<ClientActor*> actors;
	pWorld->getActorMgr()->ToCastMgr()->getAllLiveActors(actors);
	size_t iPlayerSize = vPlayers.size();
	size_t iSize = vPlayers.size() + actors.size();
	ActorLiving* pActor = nullptr;
	ActorLiving* pSelector = nullptr;
	CollideAABB box;
	float ds = 0.f;
	float min_s = -1.f;
	bool needReduce = false;
	std::vector<int> checkedActors;
	float dx = 0.f;
	float dy = 0.f;

	UGCCFG& ugcCfg = GetLuaInterfaceProxy().get_lua_const()->ugcCfg;
	float raduisMax = GetSlowAimRadius();
	float raduisMin = GetAssistAimRadius();
	float autofileTime = ugcCfg.get("autoFireTime", 1.5);
#ifdef SHOW_DEBUG_LINE
	auto& display = UIRenderer::GetInstance();
	SharePtr<MaterialInstance>& mat = display.CreateInstance(UIDrawFlag::UI_DRAWFLAG_NONE, kBlendModeTranslucent);
	
	//玩家的两个圈
	display.BeginDraw(mat, display.GetNullTexture());
	Rainbow::Rectf rect(ox - raduisMax, oy - raduisMax, raduisMax*2, raduisMax*2);
	display.DrawRectWireframe(rect, ColorRGBA32(0, 255, 0, 255));
	display.EndDraw();

	display.BeginDraw(mat, display.GetNullTexture());
	Rainbow::Rectf rect1(ox - raduisMin, oy - raduisMin, raduisMin * 2, raduisMin * 2);
	display.DrawRectWireframe(rect1, ColorRGBA32(0, 255, 0, 255));
	display.EndDraw();
#endif
	for (size_t i = 0; i < iSize; i++)
	{
		if (i < iPlayerSize)
			pActor = vPlayers[i];
		else
			pActor = dynamic_cast<ActorLiving*>(actors[i - iPlayerSize]);

		if (!pActor || pActor->needClear() || player == pActor || !pActor->canBeCollidedWith() || pActor->getObjType() == OBJ_TYPE_VEHICLE)
			continue;
		if (pActor->getTeam() == 0 || pActor->getTeam() != player->getTeam())
		{
			const WCoord& pos = pActor->getPosition();
			int iDistance = (pos.x - playerPos.x) * (pos.x - playerPos.x) + (pos.y - playerPos.y) * (pos.y - playerPos.y) + (pos.z - playerPos.z) * (pos.z - playerPos.z);//不做开方
			if (iDistance > maxRange)
				continue;

			//锐角,大概小于75°
			if (DotProduct(Rainbow::Vector3f(dir.x, 0, dir.z), Rainbow::Vector3f((float)(pos.x - playerPos.x), 0, (float)(pos.z - playerPos.z))) > 0.25f)
			{
				pActor->getHitCollideBox(box);
				Vector3f screenPosMin(0.f);
				Vector3f screenPosMax(0.f);
				Vector3f _pos = box.pos.toVector3();
				Vector3f _dim = box.dim.toVector3();
				getMinMaxPos(player->m_pCamera->getEngineCamera(), _pos, _dim, screenPosMin, screenPosMax);

				//如果包围盒超出屏幕不处理
				if (screenPosMin.x < 0 || screenPosMin.y < 0 || screenPosMax.x > clientWidth || screenPosMax.y > clientHeight)
					continue;
#ifdef SHOW_DEBUG_LINE			
				display.BeginDraw(mat, display.GetNullTexture());
				Rainbow::Rectf rect(screenPosMin.x, screenPosMin.y, screenPosMax.x- screenPosMin.x, screenPosMax.y - screenPosMin.y);
				display.DrawRectWireframe(rect, ColorRGBA32(255,0,0,255));
				display.EndDraw();
#endif
				//准星减速（开火）
				if (CustomGunSetting::AimReduce && !needReduce && checkOverlap(raduisMax, ox, oy, screenPosMin.x, screenPosMin.y, screenPosMax.x, screenPosMax.y))
				{
					//辅助瞄准减速圈30
					needReduce = true;
				}

				//吸附自瞄圈20
				if ((CustomGunSetting::AutoFire || CustomGunSetting::AimAssist) && checkOverlap(raduisMin, ox, oy, screenPosMin.x, screenPosMin.y, screenPosMax.x, screenPosMax.y))
				{
					Vector3f center(box.centerX(), box.centerY(), box.centerZ());
					Vector3f centerScreen = 0.5f * (screenPosMax + screenPosMin);

					ds = (centerScreen.x - ox) * (centerScreen.x - ox) + (centerScreen.y - oy) * (centerScreen.y - oy);
					if (min_s < 0.0f || min_s > ds)
					{
						Rainbow::Vector3f v((float)(center.x - origin.x), (float)(center.y - origin.y), (float)(center.z - origin.z));
						worldray.m_Range = v.Length();
						worldray.m_Dir = v;
						worldray.m_Dir.NormalizeSafe();
						IntersectResult result;
						if (pWorld->pickGround(worldray, &result))
							continue;

						min_s = ds;
						dx = centerScreen.x - ox;
						dy = centerScreen.y - oy;
						pSelector = pActor;
					}
				}
			}
		}
	}
	//选定了目标
	if (pSelector)
	{
		//自动开火
		if (CustomGunSetting::AutoFire)
		{
			m_autofireTime += dtime;
			if (m_autofireTime > autofileTime)
				m_autoFireFlag = true;
		}
		
		//自瞄吸附
		if (CustomGunSetting::AimAssist)
		{
			float t = 0.f;
			pSelector->getHitCollideBox(box);
			int iPick = ray.intersectBox(box.minPos().toVector3(), box.maxPos().toVector3(), &t);
			if (iPick < 0)
			{
				//移动镜头，定位时间50ms
				float fact = dtime / 0.1f;
				dx *= fact;
				dy *= fact;
				float sensitivity = GetAutoAimCameraSpeed();
				player->m_pCamera->rotate(dx * sensitivity / clientWidth, dy * sensitivity / clientHeight);
			}
		}
	}
	else
	{
		m_autofireTime = 0.f;
		m_autoFireFlag = false;
	}

	if (CustomGunSetting::AimReduce)
	{
		if (needReduce)
			ReduceSensitivity();
		else
			RevertSensitivity();
	}
}

// 获取射程（cm）：基础射程*100 并叠加射程加成
float CustomGunUseComponent::GetGunRange()
{
	if (m_GunDef == NULL) return 0;
	float range = m_GunDef->range * 100;
	float rangeBonus1 = rangeBonus();
	if (rangeBonus1 > 0)
		range += range * rangeBonus1;
	return range;
	
	//这个视野是决定是否加载区块的，而且跟y没关系，如果事业内玩家跟目标y相差很大，min以后就打不到
	//这个是区块离玩家的半径，需要加上立方体的对角线长度斜着站，才能保证都打到
	//float viewRange = ClientPlayer::GetCurViewRange(nullptr) + 1.731f;
	//viewRange *= 1600.f;
	//range = std::min(viewRange, range);
	//return range;
}

// 计算单发/连发对应的后坐力角：区分腰射/ADS与垂直/水平，叠加各类加成
void CustomGunUseComponent::calcRecoilAngle()
{
	PlayerControl* player = g_pPlayerCtrl;
	if (!player)
		return;

	if (m_GunDef == NULL) return;

	float hipAcc = 0.f;
	float adsAcc = 0.f;
	for (int i = 0; i < m_GunDef->recoilAngleCumulation.size(); ++i)
	{
		const Recoil& recoil = m_GunDef->recoilAngleCumulation[i];
		if (i == m_GunDef->recoilAngleCumulation.size() - 1 || (m_fireCount >= recoil.start && m_fireCount <= recoil.finish))
		{
			adsAcc = recoil.value;
			hipAcc = recoil.value * recoil.hiptimes;
			break;
		}
	}
	bool isAimState = player->GetIdleStateGunAdvance()->IsAimState();
	if (!isAimState)
	{
		m_hipRecoilAngle = m_GunDef->hipRecoilAngle + hipAcc;
		float fLeftHipX = abs(m_GunDef->hipRecoilAngleMax - abs(m_LastRotateX - player->m_pCamera->m_RotatePitch));
		if (m_hipRecoilAngle > fLeftHipX)
			m_hipRecoilAngle = fLeftHipX;

		m_adsRecoilAngle = 0.f;
	}
	else
	{
		m_adsRecoilAngle = m_GunDef->adsRecoilAngle + adsAcc;
		float fLeftAdsX = abs(m_GunDef->adsRecoilAngleMax - abs(m_LastRotateX - player->m_pCamera->m_RotatePitch));
		if (m_adsRecoilAngle > fLeftAdsX)
			m_adsRecoilAngle = fLeftAdsX;

		m_hipRecoilAngle = 0.f;
	}


	//垂直后座力，最终垂直后座力(含腰射和瞄准)=垂直后座力当前值*MAX(0,(1+recoilbonus+recoilpitchbonus))，默认为0
	float bonus = 1 + recoilBonus() + recoilPitchBonus();
	if (bonus < 0)
		bonus = 0;

	m_hipRecoilAngle *= bonus;
	m_adsRecoilAngle *= bonus;

	for (int i = 0; i < m_GunDef->horizontalAngle.size(); ++i)
	{
		const RecoilHor& hor = m_GunDef->horizontalAngle[i];
		if (i == m_GunDef->horizontalAngle.size() - 1 || (m_fireCount >= hor.start && m_fireCount <= hor.finish))
		{
			float fRandom = GenRandomInt(10001) / 10000.f;
			float fDiff = fRandom * (hor.valueEnd - hor.value);
			m_adsHorizontalAngle = hor.value + fDiff;
			m_hipHorizontalAngle = hor.hiptimes * m_adsHorizontalAngle;
			if (!isAimState)
			{
				m_adsHorizontalAngle = 0.f;
			}
			else
			{
				m_hipHorizontalAngle = 0.f;
			}
			//水平后座力，最终水平后座力(含腰射和瞄准)=水平后座力当前值*MAX(0,(1+recoilbonus+recoilyawbonus))，默认为0
			float hBonus = 1 + recoilBonus() + recoilYawBonus();
			if (hBonus < 0)
				hBonus = 0;

			m_adsHorizontalAngle *= hBonus;
			m_hipHorizontalAngle *= hBonus;
			break;
		}
	}
}

// 投掷物发射：构建一条射线；联机发送给主机，否则本地直接抛射
bool CustomGunUseComponent::doProjectJob(uint32 combCount)
{
	if (m_GunDef == NULL) return false;

	PlayerControl* player = g_pPlayerCtrl;
	if (!player)
		return false;

	//是否是瞄准状态（否则为腰射状态）
	bool isAimState = player->GetIdleStateGunAdvance()->IsAimState();

	std::vector<MINIW::WorldRay> rays;
	rays.resize(1);
	MINIW::WorldRay& ray = rays[0];
	//player->m_pCamera->getViewRayByScreenPt(&ray, 0.5, 0.5);
	//发射投掷物按之前的逻辑
	Rainbow::Vector3f dir;
	ray.m_Dir = player->getLookDir();
	ray.m_Origin = player->getEyePosition().toWorldPos();

	if (m_Host->getWorld()->isRemoteMode())
	{
		PB_ActorShootCH ch;
		ch.set_gunid(m_GunDef->ID);
		ch.set_projectileid(projectileId());
		ch.set_isaim(isAimState);

		auto* info = ch.add_rayinfos();
		PB_Vector3f* pos = info->mutable_pos();
		pos->set_x(ray.m_Origin.x);
		pos->set_y(ray.m_Origin.y);
		pos->set_z(ray.m_Origin.z);

		PB_Vector3f* dir = info->mutable_dir();
		dir->set_x(ray.m_Dir.x);
		dir->set_y(ray.m_Dir.y);
		dir->set_z(ray.m_Dir.z);

		unsigned int useTick = Timer::getSystemTick();
		ch.set_usetick(useTick);
		return GetGameNetManagerPtr()->sendToHost(PB_ACTORSHOOT_CH, ch);
	}
	else
	{
		//投掷物射击
		return doGunFireProjectile(ray);
	}
	return false;
}

// 子弹发射（一次击发，可能含多弹片）：计算散布/准星、生成射线；联机转发或本地命中结算
bool CustomGunUseComponent::doShootJob(uint32 combCount)
{
	if (!g_pPlayerCtrl || !g_pPlayerCtrl->getWorld() || !m_GunDef)
		return false;

	PlayerControl* player = g_pPlayerCtrl;

	//该枪发射投掷物
	if (projectileId() > 0)
	{
		return doProjectJob(combCount);
	}

	if (!m_GunDef->skillId.empty())
	{
		return false;
	}

	if (!player->m_pCamera)
		return false;

	//是否是瞄准状态（否则为腰射状态）
	bool isAimState = player->GetIdleStateGunAdvance()->IsAimState();

	int bulletshrapnel = bulletShrapnel();
	if (bulletshrapnel <= 0)
		bulletshrapnel = 1;

	std::vector<MINIW::WorldRay> rays;
	rays.resize(bulletshrapnel);

	//腰射散步,瞄准
	float spreadmax = isAimState ? m_GunDef->adsSpreadMax : m_GunDef->hipSpreadMax;
	float spreadstep = isAimState ? m_GunDef->adsSpreadStep : m_GunDef->hipSpreadStep;
	float spreadtype = isAimState ? m_GunDef->adsSpreadType : m_GunDef->hipSpreadType;

	float minSightSpread = m_GunDef->sightMin;
	if (combCount == 1)
	{
		m_FirstSpread = isAimState ? m_GunDef->adsSpreadMin : m_GunDef->hipSpreadMin;
		//如果m_CurrentSpread 还在恢复，则用m_CurrentSpread
		if (m_CurrentSpread > m_FirstSpread)
			m_FirstSpread = m_CurrentSpread;
		//停止恢复散射
		m_isRecoverSpread = false;

		if (m_CurrentSightSpread > minSightSpread)
			minSightSpread = m_CurrentSightSpread;
	}
	
	m_TargetSightSpread = minSightSpread + combCount * m_GunDef->sightStep;
	if (m_TargetSightSpread > m_GunDef->sightMax)
		m_TargetSightSpread = m_GunDef->sightMax;

	float spread = m_FirstSpread + (combCount - 1) * spreadstep;//腰射首发散布角度+腰射散布每发增加角度
	if (spread > spreadmax)
		spread = spreadmax;

	//腰射散布，最终腰射散布=当前腰射散布角度*MAX(0,1+spreadbonus+spreadhipbonus))，默认为0
	float bonus = 1 + spreadBonus();
	if (isAimState)
		bonus += spreadAdsBonus();
	else
		bonus += spreadHipBonus();
	if (bonus < 0)
		bonus = 0;

	//UGCCFG& ugcCfg = GetLuaInterfaceProxy().get_lua_const()->ugcCfg;
	spread *= bonus;
	//spread = ugcCfg.get("spread", spread);
	m_CurrentSpread = spread;
	
	float range = GetGunRange();
	MINIW::WorldRay originRay;
	originRay.m_Range = range;
	player->m_pCamera->getViewRayByScreenPt(&originRay, 0.5, 0.5);
	Vector3f origin = originRay.m_Origin.toVector3();
	Vector3f endPoint = origin + originRay.m_Dir * 10000.f;
	float r = TanByAngle(spread)*10000.f;//单位射线的投射面上的圆
	Vector3f cameraPos = player->m_pCamera->getEngineCamera()->GetWorldPosition();
	player->m_pCamera->getEngineCamera()->SetWorldPosition(endPoint);
	Matrix4x4f worldToCamera = player->m_pCamera->getEngineCamera()->GetWorldToCameraMatrix();
	//Vector3f endPointCamera = worldToCamera.MultiplyPoint3(endPoint);
	Matrix4x4f cameraToWorld = worldToCamera.GetInvert_Full();

	Vector3f muzzlePos = origin;//枪口位置

	if (player->getViewMode() == CAMERA_FPS)
	{
		//第一人称
		player->m_pCamera->GetWorldPosFromWeapon(1001, muzzlePos);
	}
	else
	{
		player->getBody()->GetWorldPosFromWeapon(1001, muzzlePos);
	}
	
	for (int i = 0; i< bulletshrapnel;i++)
	{
		MINIW::WorldRay& input_ray = rays[i];
		input_ray = originRay;
		if (spread <= 0.f)
		{
			continue;
		}
		
		float random = GenRandomFloat();
		float r1 = random * r;//随机圆内某点
		//r1 = r * ugcCfg.get("randomFloat", random);
		Vector3f pointLocal(0, 0, 0);
		if (spreadtype == 0)//右上
		{
			float angle = GenRandomFloat() * 90.0f;
			pointLocal.x = r1 * Cos(angle);
			pointLocal.y = r1 * Sin(angle);
			//pointLocal.z = endPointCamera.z;
		}
		else if (spreadtype == 1)//圆
		{
			float angle = GenRandomFloat() * 360.0f;
			//angle = ugcCfg.get("angle", angle);
			pointLocal.x = r1 * Cos(angle);
			pointLocal.y = r1 * Sin(angle);
			//pointLocal.z = endPointCamera.z;
		}
		else if (spreadtype == 2)//无右下
		{
			float angle = GenRandomFloat() * 90.0f;
			pointLocal.x = r1 * Cos(angle);
			pointLocal.y = r1 * Sin(angle);
			//pointLocal.z = endPointCamera.z;
			int n = GenRandomInt(3);
			if (n == 0)//第1象限
			{
				//donothing
			}
			else if (n == 1)//第2象限
			{
				pointLocal.x *= -1;
			}
			else if (n == 2)//第3象限
			{
				pointLocal.x *= -1;
				pointLocal.y *= -1;
			}
		}
		
		Vector3f endPoint1 = cameraToWorld.MultiplyPoint3(pointLocal);
		input_ray.m_Dir = endPoint1 - origin;
		input_ray.m_Dir.NormalizeSafe();
	}
	player->m_pCamera->getEngineCamera()->SetWorldPosition(cameraPos);
	

	if(player->getWorld()->isRemoteMode())
	{
		PB_ActorShootCH ch;
		ch.set_gunid(m_GunDef->ID);
		ch.set_bulletid(m_GunDef->bulletId);
		ch.set_isaim(isAimState);
		for (size_t i = 0; i < rays.size(); i++)
		{
			const MINIW::WorldRay& ray = rays[i];
			auto* info = ch.add_rayinfos();

			PB_Vector3f* pos = info->mutable_pos();
			pos->set_x(ray.m_Origin.x);
			pos->set_y(ray.m_Origin.y);
			pos->set_z(ray.m_Origin.z);

			PB_Vector3f* dir = info->mutable_dir();
			dir->set_x(ray.m_Dir.x);
			dir->set_y(ray.m_Dir.y);
			dir->set_z(ray.m_Dir.z);

			PB_Vector3f* muzzle = info->mutable_muzzle();
			muzzle->set_x(muzzlePos.x);
			muzzle->set_y(muzzlePos.y);
			muzzle->set_z(muzzlePos.z);

			unsigned int useTick = Timer::getSystemTick();
			ch.set_usetick(useTick);

			info->set_range(ray.m_Range);
		}
		GetGameNetManagerPtr()->sendToHost(PB_ACTORSHOOT_CH, ch);
	}
	else
	{
		for (size_t i = 0; i < rays.size(); i++)
		{
			doGunFire(rays[i], muzzlePos);
		}

		auto pScriptComponent = m_Host->getScriptComponent();
		if (pScriptComponent)
		{
			pScriptComponent->OnEvent((unsigned int)CE_OnPlayerFire, true, m_Host->isPlayerMove());
		}
	}

	return true;
}

// 内部击发：按射速在一帧内补多枪；更新后坐力/恢复并扣弹，逐次调用doShootJob
int CustomGunUseComponent::FireOnceInternal(bool bLastFireStatus, int nFireTimes)
{
	if (m_GunDef == NULL) return 0;
	int nCount = 0;
	float fCur = m_fCurTime;
	float fFireRate = 60.0f / rpm();

	// OnUpdata 在 fireOnce 之后， 可能前一枪后坐力演算还没有完成，又开始击发了
	float fDiffBeforeFirstShoot = fCur - m_fLastFireTime;
	if (nFireTimes > 1)
		fDiffBeforeFirstShoot = fFireRate;
	if (bLastFireStatus || fCur - m_fLastFireTime < m_GunDef->comboResetTime)
	{
		// 连击状态， 只表现后座力， 不考虑后坐力恢复
		PerformRecoil(fDiffBeforeFirstShoot, false);
	}
	else
	{
		PerformRecoilAndRecovery(fDiffBeforeFirstShoot, false);
	}
	
	int bulletConsume1 = bulletConsume();
	while (m_Magazine >= bulletConsume1 && nFireTimes > 0)
	{
		bool success = doShootJob(m_fireCount + 1);
		if (!success)
			return nCount;

		addMagazine(-bulletConsume1);
		nFireTimes -= 1;
		++nCount;
		m_IsFire = true;
		if (nCount == 1 && !(bLastFireStatus || fCur - m_fLastFireTime < m_GunDef->comboResetTime))
		{
			// 上一帧是保持了射击按键，或者当前还在重置连击计时内，就算连击。否则重置。
			resetRecoil(true);
		}
		else
			++m_fireCount;
		
		// 获取对应连发数的后坐力配置
		calcRecoilAngle();

		if (nFireTimes > 0) 
		{
			// 当前不是最后一发，模拟完成后座力计算
			m_fLastFireTime += fFireRate;
			// 到下一枪的时间用来模拟后坐力
			PerformRecoil(fFireRate, false);
		}
	}
	m_NeedRecovery = true;
	m_NeedRecoveryH = true;
	m_NeedRecoverySightSpread = true;
	if (nFireTimes == 0)
	{
		// 最后一枪
		m_fLastFireTime = fCur;
	}
	else
	{
		// 模拟恢复一下后坐力
		float fDiff = fCur - m_fLastFireTime - m_GunDef->comboResetTime;
		if (fDiff > 0.f)
		{
			PerformRecoilRecovery(fDiff, false);
		}
	}
	return nCount;
}


// 对外触发一次击发：标记到下一帧OnUpdate中实际执行
bool CustomGunUseComponent::fireOnce()
{
	if (m_GunDef == NULL) return false;
	if (m_Magazine >= bulletConsume())
	{
		m_bNextUpdateFire = true;
		return true;
	}
	return false;
}

int CustomGunUseComponent::getMagazine()
{
	return m_Magazine;
}

// 设置弹夹并同步到背包组件/脚本UI（rectifyBag用于修正背包显示）
void CustomGunUseComponent::setMagazine(int count, bool isInit, int rectifyBag)
{
	m_Magazine = count;

	if (!isInit)
	{
		BackPackGrid* itemgrid = m_Host->getPlayerAttrib()->getEquipGrid(EQUIP_WEAPON);
		auto* comp = dynamic_cast<GunGridDataComponent*>(itemgrid->getGunDataComponent());
		if (comp)
		{
			comp->setMagazine(m_Magazine);
		}
	}
	
	if (g_pPlayerCtrl && g_pPlayerCtrl == m_Host)
	{
		int nowBulletNum = getBulletNum() + rectifyBag;
		MINIW::ScriptVM::game()->callFunction("SetGunMagazine", "ii", m_Magazine, Max(0, nowBulletNum));
	}
}

bool CustomGunUseComponent::addMagazine(int count)
{
	m_Magazine += count;

	//不够子弹了
	if (m_Magazine < 0)
	{
		m_Magazine -= count;
		return false;
	}
		
	BackPackGrid* itemgrid = m_Host->getPlayerAttrib()->getEquipGrid(EQUIP_WEAPON);
	auto* comp = dynamic_cast<GunGridDataComponent*>(itemgrid->getGunDataComponent());
	if (comp)
	{
		comp->setMagazine(m_Magazine);
	}

	if (g_pPlayerCtrl && g_pPlayerCtrl == m_Host)
		MINIW::ScriptVM::game()->callFunction("SetGunMagazine", "ii", m_Magazine, getBulletNum());

	return true;
}

void CustomGunUseComponent::doCostBulletItem(int bulletid, int num)
{
	if (m_GunDef == NULL) return;

	int costnum = num;
	m_Host->getBackPack()->removeItemInNormalPack(bulletid, costnum);
	m_Host->consumeItemOnTrigger(bulletid, costnum);
}

// 换弹完成：根据服务器或逻辑传入的目标弹夹数设置并广播事件
void CustomGunUseComponent::doReload(int count, int rectifyBag)
{
	m_reloadComplete = true;
	if (m_GunDef == NULL) return;

	if (count != 0)
	{
		if (count < 0)
		{
			setMagazine(maxAmmo(), false, rectifyBag);
		}
		else
		{
			setMagazine(count, false, rectifyBag);
		}
		m_bLastFireStatus = false;
		m_Host->Event2().Emit<>("gunReloadOnTrigger");
	}
}

//这里是来限制开火间隔的
// 射速校验（毫秒）：限制最小开火间隔，过快则拒绝（反作弊）
bool CustomGunUseComponent::canUseGun(unsigned int useTick)
{
	float fFireRate = 60000.0f / rpm();
	unsigned int now = m_lastFireTimneMs + fFireRate;
	return now <= useTick;
}

void CustomGunUseComponent::setGunUse(unsigned int useTick)
{
	m_lastFireTimneMs = useTick;
}

//host使用，发射投掷物
// 主机：投掷物发射并触发脚本事件
bool CustomGunUseComponent::doGunFireProjectile(MINIW::WorldRay& input_ray)
{
	if (!m_Host || !m_Host->getWorld() || !m_GunDef)
	{
		return false;
	}

	float baseDamage = GetRealBaseDamage();
	ProjectileFactory::throwItemByActorByPlayerRay(m_Host->getWorld(), m_Host, input_ray, 1, projectileId(), baseDamage);
	auto pScriptComponent = m_Host->getScriptComponent();
	if (pScriptComponent)
	{
		pScriptComponent->OnEvent((unsigned int)CE_OnPlayerFire, true, m_Host->isPlayerMove());
	}
	return true;
}

//host使用，发射子弹
// 主机：子弹命中结算
// - 计算空气/液体/固体衰减，穿透率达阈值则终止
// - 生物/玩家命中：构建伤害、爆头/身体倍率、命中特效、buff等
// - 方块命中：可破坏/特效/弹孔贴花
bool CustomGunUseComponent::doGunFire(MINIW::WorldRay& input_ray, Rainbow::Vector3f& muzzlePos)
{
	if (!m_Host || !m_Host->getWorld() || !m_GunDef)
	{
		return false;
	}
	BulletMgr* bulletMgr = dynamic_cast<BulletMgr*>(g_WorldMgr->getSandboxMgr("BulletMgr"));
	EffectManager* effetMgr = m_Host->getWorld()->getEffectMgr();
	UGCCFG& ugcCfg = GetLuaInterfaceProxy().get_lua_const()->ugcCfg;
	//空气衰减参数
	float decaystart = m_GunDef->decayStart * 100;//子弹衰减开始
	float decayfinish = m_GunDef->decayFinish * 100;//子弹衰减结束
	float decaymin = m_GunDef->decayMin;//空气衰减最小值

	//液体衰减
	float dacayliquid = m_GunDef->dacayLiquid;//液体衰减系数，越大可穿透距离越远
	float liquidMax = ugcCfg.get("liquidMax", 4);//最大穿透液体数量
	//枪的穿透率
	float penetration1 = penetration();

	float creaturepenetration = ugcCfg.get("creature", penetration1);
	float entitypenetration = ugcCfg.get("entity", penetration1);
	
	//中间参数
	float decayDeta = decayfinish - decaystart;
	float decaymax = 1.0f - decaymin;
	float liquidPer = 1.0f / (dacayliquid * liquidMax);

	float airDistance = 0.0f;//空气衰减距离，最大为decayfinish
	float airAtten = 0.0f;//空气衰减
	float attenNow = 0.0f;//当前累计衰减（除了空气衰减）
	int damageType = m_GunDef->damageType;
	if (damageType <= 0)
		damageType = ATTACK_RANGE;
	int damageBuffId = m_GunDef->damageBuffId;
	int damageBuffProb = m_GunDef->damageBuffProb;
	Vector3f realRangePoint = Vector3f::zero;
	Vector3f origin = input_ray.m_Origin.toVector3();
	std::vector<BlockActor> actors;
	pickBlocksAndActors(m_Host, input_ray, actors, decaystart, decayfinish, decaymin, dacayliquid, liquidMax, penetration1, ugcCfg);

	int worldid = m_Host->getWorld()->getCurMapID();
	for (auto& actor : actors)
	{
		if (actor.obj)
		{
			//空气衰减
			if (airDistance < decayfinish)
			{
				airDistance = Distance(actor.point, origin);
			}

			if (airDistance <= decaystart)
			{
				//do nothing
			}
			else if (airDistance > decaystart && airDistance < decayfinish)
			{
				airAtten = (airDistance - decaystart) * decaymax / decayDeta;
			}
			else
			{
				if (airAtten <= 0)
					airAtten = decaymax;
			}

			//在精确判定之前先判断一次
			if (attenNow + airAtten >= penetration1)
			{
				//结束
				realRangePoint = actor.point;
				break;
			}

			int objType = actor.obj->getObjType();
			//actor
			if (OBJ_TYPE_GAMEOBJECT == objType && m_Host->getWorld()->m_PhysScene)
			{
				//实体模型用物理射线判断是否射中
				Rainbow::RaycastHit hitInfo;
				//射线的原点往后一点，防止射不中
				bool ret = m_Host->getWorld()->m_PhysScene->RayCast(actor.point - 2*input_ray.m_Dir, input_ray.m_Dir, actor.t1 - actor.t0, hitInfo, 0xffffffff, true);
				if (ret)
				{
					//actor.t0 = hitInfo.distance + actor.t0;
					actor.point = hitInfo.point;
					//如果是实体则在精确检测物理盒子
					if (bulletMgr)
						bulletMgr->DecalBuletHoleToActor(actor.obj->GetActor(), actor.point, hitInfo.normal);
				}
				else//没射中
					continue;
			}

			ActorLiving* living = dynamic_cast<ActorLiving*>(actor.obj);
			ClientPlayer* player = NULL;
			if (living)
			{
				player = dynamic_cast<ClientPlayer*>(actor.obj);
				//判断是否可以攻击
				bool canattack = true;
				if (player)
				{
					canattack = CMP_CanAttack(m_Host, NULL, player);
				}
				else
				{
					ClientMob* mob = dynamic_cast<ClientMob*>(actor.obj);
					if(mob)
						canattack = CMP_CanAttack(m_Host, mob, NULL);
				}
				if (!canattack)
					continue;

				OneAttackData atkdata;
				atkdata.damage_armor = true;
				atkdata.fromplayer = m_Host;
				atkdata.atkTypeNew = (1 << damageType);
				atkdata.knockback = repelDistance();
				atkdata.ignore_resist = true;//关闭伤害保护？
				int buffId = hittedBuffId();
				if (buffId > 0)
				{
					atkdata.buffId = buffId / 1000;
					atkdata.buffLevel = buffId % 1000;
				}
				float baseDamage = GetRealBaseDamage();
				float chuantou = std::min(1.f, penetration1 - attenNow - airAtten);
				//计算伤害衰减
				baseDamage = baseDamage * chuantou;
				atkdata.atkPointsNew[damageType] = baseDamage;
				atkdata.touReduce = touReduce();
				float headPer = ugcCfg.get("headPer", 0.35f);
				if (actor.per <= headPer)
				{
					atkdata.damageFactor = headDamage();
					atkdata.isAttackHead = true;
				}
				else
				{
					atkdata.damageFactor = bodyDamage();
				}

				auto component = actor.obj->GetActor()->getAttackedComponent();
				if (component)
					component->attackedFrom(atkdata, m_Host);

				//处理damageBuffId
				if (GenRandomInt(10000) < damageBuffProb && damageBuffId > 0)
				{
					int buffId = damageBuffId / 1000;
					int buffLevel = damageBuffId % 1000;
					long long fromObjid = m_Host->getObjId();
					living->getLivingAttrib()->addBuff(buffId, buffLevel, -1, 0, fromObjid);
				}
				//命中
				m_Host->attackHitOnTrigger(living->getObjId(), living->getDefID());
				bool isDead = living->isDead();
				if (atkdata.isAttackHead)
				{
					if (m_Host->canShowShotTip())
					{
						g_pPlayerCtrl->triggerHeadshotTip(isDead);
					}
					else
					{
						auto effectComponent = m_Host->getEffectComponent();
						if (effectComponent)
						{
							ACTORBODY_EFFECT effect = isDead ? HUDFX_DEADSHOT : HUDFX_HEADSHOT;
							effectComponent->playBodyEffect(effect);
						}
					}
					if (isDead) m_Host->Event2().Emit<>("gunHeadShotOnTrigger");


					// 爆头，观察者事件接口
					//ObserverEvent_Player obevent(m_Host->getObjId());
					//obevent.SetData_Actor(living->getObjId());
					//GetObserverEventManager().OnTriggerEvent("Player.HeadShot", &obevent);
					auto pScriptComponent = m_Host->getScriptComponent();
					if (pScriptComponent)
					{
						pScriptComponent->OnEvent((unsigned int)CE_OnPlayerHeadShot, true, living->getObjId());
					}
				}
				else
				{
					if (m_Host->canShowShotTip())
					{
						g_pPlayerCtrl->triggerNormalshotTip(isDead);
					}
					else
					{
						auto effectComponent = m_Host->getEffectComponent();
						if (effectComponent)
						{
							ACTORBODY_EFFECT effect = isDead ? HUDFX_DEADSHOT : HUDFX_NORMALSHOT;
							effectComponent->playBodyEffect(effect);
						}
					}
				}
				attenNow += creaturepenetration;//生物衰减
			}
			else
			{
				if (OBJ_TYPE_GAMEOBJECT == objType)
				{
					attenNow += entitypenetration;//实体衰减
				}
				else if (OBJ_TYPE_DROPITEM == objType)
				{
					//掉落物可以穿过，donothing
				}
				else
				{
					//其他actor，全部不能穿透
					attenNow += penetration1;//最大值
				}
			}

			if (m_GunDef->hittedCameraAngle != 0)
			{
				//玩家击中玩家
				if (player)
				{
					//镜头上下动,pitch 低下头是90度 平视0 抬头是270
					float pitch = player->getFacePitch();
					//这里-180，是因为视角是反的
					float yaw = player->getFaceYaw() - 180.0f;
					if (pitch > 270.f)
						pitch -= 360.f;
					pitch -= m_GunDef->hittedCameraAngle;

					if (pitch > 88.0f)
						pitch = 88.0f;
					if (pitch < -88.0f)
						pitch = -88.0f;
					player->rotateCamera(yaw, pitch);
				}
			}
			
			//命中特效
			if (m_GunDef->particlesBulletMobHit.id > 0 && bulletMgr)
			{
				bulletMgr->PlayBulletHitEffect(worldid, m_GunDef->particlesBulletMobHit.id, m_GunDef->particlesBulletMobHit.size, actor.point, 0, 0);
			}

			//在精确判定之后再判断一次
			if (attenNow + airAtten >= penetration1)
			{
				//结束
				realRangePoint = actor.point;
				break;
			}
		}
		else
		{
			//空气衰减
			if (airDistance < decayfinish)
			{
				airDistance = Distance(actor.point, origin);
			}

			if (airDistance <= decaystart)
			{
				//do nothing
			}
			else if (airDistance > decaystart && airDistance < decayfinish)
			{
				airAtten = (airDistance - decaystart) * decaymax / decayDeta;
			}
			else
			{
				if (airAtten <= 0)
					airAtten = decaymax;
			}

			//在精确判定之前先判断一次
			if (attenNow + airAtten >= penetration1)
			{
				//结束
				realRangePoint = actor.point;
				break;
			}

			bool breakable = false;
			bool isLiquid = false;
			std::string blocktype;
			std::string DigSound;
			int mineTool = 0;
			BlockMaterial* blockmtl = g_BlockMtlMgr.getMaterial(actor.blockid);
			if (blockmtl)
			{
				isLiquid = blockmtl->isLiquid();
				const BlockDef* def = blockmtl->GetBlockDef();
				if (def)
				{
					breakable = def->Breakable;
					blocktype = def->Type;
					mineTool = def->MineTool;
					DigSound = def->DigSound;
				}
			}

			//block可破坏
			if (breakable && m_Host->getItemUseComponent()->canDestroyBlock())
			{
				//播放破坏特效
				effetMgr->playBlockDestroyEffect(0, actor.blockPos * BLOCK_SIZE + WCoord(BLOCK_SIZE / 2, BLOCK_SIZE / 2, BLOCK_SIZE / 2), DIR_NEG_X, 40);
				if (!DigSound.empty()) effetMgr->playSound(actor.blockPos * BLOCK_SIZE, DigSound.c_str(), GSOUND_DESTROY);
				m_Host->getWorld()->destroyBlock(actor.blockPos.x, actor.blockPos.y, actor.blockPos.z, 0);

				ObserverEvent_ActorBlock obevent(m_Host->getObjId(), actor.blockid, actor.blockPos.x, actor.blockPos.y, actor.blockPos.z);
				GetObserverEventManager().OnTriggerEvent("Block.DestroyBy", &obevent);
			}

			if (isLiquid)
			{
				attenNow += liquidPer;//液体衰减
			}
			else
			{
				//方块衰减
				if (blocktype == "basic")// basic类型方块衰减
				{
					float value = penetration1;
					auto iter = ugcCfg.basic.find(mineTool);
					if (iter != ugcCfg.basic.end())
					{
						value = iter->second;
					}
					attenNow += value;
				}
				else
				{
					float value = ugcCfg.get(blocktype, penetration1);
					attenNow += value;
				}


				//命中特效
				if (m_GunDef->particlesBulletBlockHit.id > 0 && bulletMgr)
				{
					float yaw = 0;
					float pitch = 0;
					if (actor.face == DIR_POS_X)
					{
						yaw = 90;
						pitch = 90;
					}
					else if (actor.face == DIR_NEG_X)
					{
						yaw = 90;
						pitch = -90;
					}
					else if (actor.face == DIR_NEG_Y)
					{
						pitch = 180;
					}
					else if (actor.face == DIR_POS_Z)
					{
						pitch = 90;
					}
					else if (actor.face == DIR_NEG_Z)
					{
						pitch = -90;
					}
					bulletMgr->PlayBulletHitEffect(worldid, m_GunDef->particlesBulletBlockHit.id, m_GunDef->particlesBulletBlockHit.size, actor.point, yaw, pitch);
				}

				if (bulletMgr)
					bulletMgr->DecalBuletHoleToBlock(m_Host, actor.blockPos, actor.point, actor.face);
			}

			//在精确判定之后再判断一次
			if (attenNow + airAtten >= penetration1)
			{
				//结束
				realRangePoint = actor.point;
				break;
			}
		}
	}

	//子弹轨迹特效
	if (bulletMgr)
		bulletMgr->PlayBulletEffect(worldid, muzzlePos, realRangePoint, m_GunDef);

	return true;
}

int CustomGunUseComponent::getGunUse(int gunId)
{
	return 0;
}


// 注册本组件的更新回调给本地玩家（客户端）
void CustomGunUseComponent::setUpdate()
{
#ifndef IWORLD_SERVER_BUILD
	if (m_Host == g_pPlayerCtrl)
	{
		auto GunFunction = [this](float dtime)
		{
			this->OnUpdate(dtime);
		};
		m_Host->setGunUseUpdate(GunFunction);
	}
#endif
}

//Host 一次包开一枪，可能包含多个弹片射线信息
// 主机收包：对一次击发进行射速/弹药/弹片数量校验并重放命中逻辑
void CustomGunUseComponent::onPlayerShoot(const PB_ActorShootCH& msg)
{
	if (!m_Host || m_GunDef == NULL)
		return;

	int gunid = msg.gunid();
	if (m_GunDef->ID != gunid)
		return;

	unsigned int useTick = msg.usetick();
	if (!canUseGun(useTick))
	{
		//开火间隔太短，作弊
		LogStringMsg("onPlayerShoot check fail0, useTick:%ld, m_lastFireTimneMs£º%ld", useTick, m_lastFireTimneMs);
		return;
	}

	if (!addMagazine(-bulletConsume()))
	{
		//不够子弹，作弊
		LogStringMsg("onPlayerShoot check fail1, Magazine:%d, bulletConsume£º%d", m_Magazine, bulletConsume());
		return;
	}

	if (msg.has_projectileid())
	{
		int projectleid = msg.projectileid();
		if(projectleid != projectileId())
			return;
		if (msg.rayinfos_size() == 0)
			return;

		const PB_GunRayInfo& info = msg.rayinfos(0);
		MINIW::WorldRay input_ray;
		input_ray.m_Origin = Rainbow::WorldPos(info.pos().x(), info.pos().y(), info.pos().z());
		input_ray.m_Dir = Vector3f(info.dir().x(), info.dir().y(), info.dir().z());
		doGunFireProjectile(input_ray);
	}
	else
	{
		//bullet
		int bulletid = msg.bulletid();
		if (bulletid != m_GunDef->bulletId)
			return;

		//这里校验，如果是触发了词条buff，多加了弹片数量，但是在主机时间已经过，会误判。
		if (bulletShrapnel() < msg.rayinfos_size())
		{
#ifdef IWORLD_SERVER_BUILD
			jsonxx::Object log;
			log << "client" << msg.rayinfos_size();
			log << "host" << bulletShrapnel();
			GetICloudProxyPtr()->InfoLog(m_Host->getUin(), 0, "cheat_gun_shrapnel", log);
#endif  // IWORLD_SERVER_BUILD
			//作弊
			LogStringMsg("onPlayerShoot check fail2, rayinfosize:%d, %d", bulletShrapnel(), msg.rayinfos_size());
			return;
		}

		for (int i = 0; i < msg.rayinfos_size(); i++)
		{
			const PB_GunRayInfo& info = msg.rayinfos(i);
			MINIW::WorldRay input_ray;
			input_ray.m_Range = info.range();
			input_ray.m_Origin = Rainbow::WorldPos(info.pos().x(), info.pos().y(), info.pos().z());
			input_ray.m_Dir = Vector3f(info.dir().x(), info.dir().y(), info.dir().z());

			Vector3f muzzlePos(info.muzzle().x(), info.muzzle().y(), info.muzzle().z());
			doGunFire(input_ray, muzzlePos);
		}

		auto pScriptComponent = m_Host->getScriptComponent();
		if (pScriptComponent)
		{
			pScriptComponent->OnEvent((unsigned int)CE_OnPlayerFire, true, m_Host->isPlayerMove());
		}
	}

	setGunUse(useTick);
}

void CustomGunUseComponent::setIsLoad(bool b)
{
	if (m_GunDef == NULL) return;
	m_bIsLoad = b;
	BackPackGrid* itemgrid = m_Host->getPlayerAttrib()->getEquipGrid(EQUIP_WEAPON);
	auto* comp = dynamic_cast<GunGridDataComponent*>(itemgrid->getGunDataComponent());
	if (comp)
		comp->setIsLoad(m_bIsLoad);
}

// 设置“开镜倍率”灵敏度因子
void CustomGunUseComponent::SetSensitivityForScope(float factor)
{
	m_scopeFactor = factor;
	SetSensitivity();
}

// 设置“辅助瞄准/减速”灵敏度因子
void CustomGunUseComponent::SetSensitivityForAssist(float factor)
{
	m_assistFactor = factor;
	SetSensitivity();
}

// 应用灵敏度：按平台读取基础灵敏度并乘以 scopeFactor*assistFactor
void CustomGunUseComponent::SetSensitivity()
{
	if (!GetClientInfoProxy() || !g_pPlayerCtrl)
		return;

	if (GetClientInfoProxy()->isPC())
	{
		if (m_sensitivity < 0 || g_pPlayerCtrl->getPCControl()->IsSetSensitivity())
		{
			m_sensitivity = g_pPlayerCtrl->getPCControl()->getSensitivity();
		}
		g_pPlayerCtrl->getPCControl()->setSensitivity2(m_sensitivity * m_scopeFactor * m_assistFactor);
	}
	else
	{
		if (m_sensitivity < 0 || g_pPlayerCtrl->getTouchControl()->IsSetSensitivity())
		{
			m_sensitivity = g_pPlayerCtrl->getTouchControl()->getSensitivity();
		}
		g_pPlayerCtrl->getTouchControl()->setSensitivity2(m_sensitivity * m_scopeFactor * m_assistFactor);
	}
}

//降低灵敏度
// 进入减速圈：降低灵敏度（读取不同平台的减速系数）
void CustomGunUseComponent::ReduceSensitivity()
{
	PlayerControl* player = g_pPlayerCtrl;
	if (!player)
		return;

	if (m_isLowSensityvity)
		return;

	UGCCFG& ugcCfg = GetLuaInterfaceProxy().get_lua_const()->ugcCfg;
	m_isLowSensityvity = true;

	if (GetClientInfoProxy()->isPC())
	{
		float slowAimLevel = ugcCfg.get("pcSlowAimLevel", 1); //系数
		if (slowAimLevel <= 0)
			slowAimLevel = 1;
		SetSensitivityForAssist(slowAimLevel);
	}
	else
	{
		float slowAimLevel = ugcCfg.get("mobileSlowAimLevel", 1); //系数
		if (slowAimLevel <= 0)
			slowAimLevel = 1;
		SetSensitivityForAssist(slowAimLevel);
	}
}

//还原灵敏度
// 离开减速圈：恢复灵敏度
void CustomGunUseComponent::RevertSensitivity()
{
	PlayerControl* player = g_pPlayerCtrl;
	if (!player)
		return;

	if (!m_isLowSensityvity)
		return;

	m_isLowSensityvity = false;
	if (GetClientInfoProxy()->isPC())
	{
		SetSensitivityForAssist(1);
	}
	else
	{
		SetSensitivityForAssist(1);
	}
}

float CustomGunUseComponent::GetSensitivity()
{
	PlayerControl* player = g_pPlayerCtrl;
	if (!player)
		return 2;

	if (GetClientInfoProxy()->isPC())
	{
		return player->getPCControl()->getSensitivity();
	}

	return player->getTouchControl()->getSensitivity();
}

//获取辅助瞄准框半径（小圈）
// 获取小圈半径：区分瞄准/腰射配置
float CustomGunUseComponent::GetAssistAimRadius()
{
	PlayerControl* player = g_pPlayerCtrl;
	if (!player)
		return 0;

	UGCCFG& ugcCfg = GetLuaInterfaceProxy().get_lua_const()->ugcCfg;
	bool isAimState = player->GetIdleStateGunAdvance()->IsAimState();
	if (isAimState)
	{
		//ads
		return ugcCfg.get("adsAssistAimRadius", 30);
	}
	//hip
	return ugcCfg.get("hipAssistAimRadius", 30);
}

//获取减速瞄准框半径（大圈）
// 获取大圈半径：区分瞄准/腰射配置
float CustomGunUseComponent::GetSlowAimRadius()
{
	PlayerControl* player = g_pPlayerCtrl;
	if (!player)
		return 0;

	UGCCFG& ugcCfg = GetLuaInterfaceProxy().get_lua_const()->ugcCfg;
	bool isAimState = player->GetIdleStateGunAdvance()->IsAimState();
	if (isAimState)
	{
		//ads
		return ugcCfg.get("adsSlowAimRadius", 100);
	}
	//hip
	return ugcCfg.get("hipSlowAimRadius", 100);
}

// 获取自瞄相机转动速度：PC与移动端分别配置
float CustomGunUseComponent::GetAutoAimCameraSpeed()
{
	UGCCFG& ugcCfg = GetLuaInterfaceProxy().get_lua_const()->ugcCfg;
	if (GetClientInfoProxy()->isPC())
	{
		ugcCfg.get("pcAutoAimCameraSpeed", 0.5);
	}

	return ugcCfg.get("mobileAutoAimCameraSpeed", 0.5);
}

// 实时基础伤害：baseDamage * max(0, 1+baseDamageBonus)
float CustomGunUseComponent::GetRealBaseDamage()
{
	float baseDamage1 = baseDamage();
	float baseDamageBonus1 = baseDamageBonus();
	baseDamageBonus1 = std::fmaxf(0, 1 + baseDamageBonus1);
	baseDamage1 *= baseDamageBonus1;
	return baseDamage1;
}

/*---------------------------------------------------这些功能不能直接从gundef获取时-----------------------------------------------------------*/
//最大弹夹
int CustomGunUseComponent::maxAmmo()
{
	if (!m_GunDef) return 0;

	int ret = m_GunDef->maxAmmo;
	return ret;
}

// 射速：ret=m_GunDef->rpm*(1+rpmBonus)，上限保护
float CustomGunUseComponent::rpm()//射速
{
	if (!m_GunDef) return 0;

	int ret = m_GunDef->rpm;
	ret *= (1 + rpmBonus());
	if (ret <= 0)
		ret = 1;

	//射速最大值，防止作弊
	if (ret > 1500)
		ret = 1500;
	return ret;
}

int CustomGunUseComponent::touReduce()//削韧值
{
	if (!m_GunDef) return 0;

	int ret = m_GunDef->touReduce;
	return ret;
}

float CustomGunUseComponent::baseDamage()
{
	if (!m_GunDef) return 0;
	float ret = m_GunDef->baseDamage;
	ret += m_Host->getPlayerAttrib()->getActorAttValueWithStatus(BUFFATTRT_FIREARM_DAMAGE, ret);
	return ret;
}

float CustomGunUseComponent::headDamage()//头部倍率
{
	if (!m_GunDef) return 0;

	float ret = m_GunDef->headDamage;
	ret += m_Host->getPlayerAttrib()->getActorAttValueWithStatus(BUFFATTRT_FIREARM_HEADDAMAGE_PERCENT, ret);
	return ret;
}

float CustomGunUseComponent::bodyDamage()//身体伤害
{
	if (!m_GunDef) return 0;

	float ret = m_GunDef->bodyDamage;
	return ret;
}

float CustomGunUseComponent::penetration()//穿透率
{
	if (!m_GunDef) return 0;

	float ret = m_GunDef->penetration;
	ret += m_Host->getPlayerAttrib()->getActorAttValueWithStatus(BUFFATTRT_PENETRANCE_PERCENT, ret);
	return ret;
}

int CustomGunUseComponent::repelDistance()//击退距离
{
	if (!m_GunDef) return 0;

	int ret = m_GunDef->repelDistance;
	ret += m_Host->getPlayerAttrib()->getActorAttValueWithStatus(BUFFATTRT_REPEL_DISTANCE, ret);
	return ret;
}

// 单发耗弹：当最大弹夹>1时叠加耗弹buff
int CustomGunUseComponent::bulletConsume()//单发消耗子弹数
{
	if (!m_GunDef) return 0;
	int ret = m_GunDef->bulletConsume;
	
	//最大弹夹数大于1时，才加buff
	if (maxAmmo() > 1)
	{
		ret += m_Host->getPlayerAttrib()->getActorAttValueWithStatus(BUFFATTRT_BULLET_EXPEND, ret);
	}
	return ret;
}

// 单发弹片数：叠加buff并做上限保护
int CustomGunUseComponent::bulletShrapnel()//单发弹片数
{
	if (!m_GunDef) return 0;

	int ret = m_GunDef->bulletShrapnel;
	ret += m_Host->getPlayerAttrib()->getActorAttValueWithStatus(BUFFATTRT_SHRAPNEL_COUNT, ret);

	//弹片数量最大值，防止作弊
	if (ret > 20)
		ret = 20;
	return ret;
}

int CustomGunUseComponent::hittedBuffId()
{
	if (!m_GunDef) return 0;

	int ret = m_GunDef->hittedBuffId;
	int ret_ = m_Host->getPlayerAttrib()->getActorAttValueWithStatus(BUFFATTRT_FIREARM_HIT_BUFF, ret);
	return ret_ <= 0 ? ret : ret_;
}

int CustomGunUseComponent::projectileId()
{
	if (!m_GunDef) return 0;

	int ret = m_GunDef->projectileId;
	int ret_ = m_Host->getPlayerAttrib()->getActorAttValueWithStatus(BUFFATTRT_FIREARM_PROJECTILE_CHANGE, ret);
	return ret_ <= 0 ? ret : ret_;
}

//1087 1089 1090 1091 1092 1093 1094 1095 1096 1097 1099 1100
/*---------------------------------------------effect的bonus值要除以100，basevalue要加1---------------------------------------------*/
float CustomGunUseComponent::rangeBonus()//射程
{
	if (!m_GunDef) return 0;

	float ret = m_GunDef->rangeBonus;
	return ret;
}

float CustomGunUseComponent::baseDamageBonus()//基础伤害加成
{
	if (!m_GunDef) return 0;

	float ret = m_GunDef->baseDamageBonus;
	//这个效果的用途是加成baseDamage的（原本的用意）
	ret += m_Host->getPlayerAttrib()->getActorAttValueWithStatus(BUFFATTRT_FIREARM_DAMAGE_PERCENT, 1);
	return ret;
}

float CustomGunUseComponent::recoilBonus()//后坐力倍率
{
	if (!m_GunDef) return 0;

	float ret = m_GunDef->recoilBonus;
	ret += m_Host->getPlayerAttrib()->getActorAttValueWithStatus(BUFFATTRT_RECOIL_PERCENT, 1);
	return ret;
}

float CustomGunUseComponent::recoilPitchBonus()//垂直后坐力
{
	if (!m_GunDef) return 0;

	float ret = m_GunDef->recoilPitchBonus;
	ret += m_Host->getPlayerAttrib()->getActorAttValueWithStatus(BUFFATTRT_PITCH_RECOIL_PERCENT, 1);
	return ret;
}

float CustomGunUseComponent::recoilYawBonus()//水平后坐力
{
	if (!m_GunDef) return 0;

	float ret = m_GunDef->recoilYawBonus;
	ret += m_Host->getPlayerAttrib()->getActorAttValueWithStatus(BUFFATTRT_YAW_RECOIL_PERCENT, 1);
	return ret;
}

float CustomGunUseComponent::spreadBonus()//散布倍率
{
	if (!m_GunDef) return 0;

	float ret = m_GunDef->spreadBonus;
	ret += m_Host->getPlayerAttrib()->getActorAttValueWithStatus(BUFFATTRT_ACCURACY_PERCENT, 1);
	return ret;
}

float CustomGunUseComponent::spreadAdsBonus()//ADS散射倍率
{
	if (!m_GunDef) return 0;

	float ret = m_GunDef->spreadAdsBonus;
	ret += m_Host->getPlayerAttrib()->getActorAttValueWithStatus(BUFFATTRT_IRONSIGHT_ACCURACY_PERCENT, 1);
	return ret;
}

float CustomGunUseComponent::spreadHipBonus()//腰射散射倍率
{
	if (!m_GunDef) return 0;

	float ret = m_GunDef->spreadHipBonus;
	ret += m_Host->getPlayerAttrib()->getActorAttValueWithStatus(BUFFATTRT_HIPFIRE_ACCURACY_PERCENT, 1);
	return ret;
}

float CustomGunUseComponent::reloadTimeBonus()//换弹时间倍率
{
	if (!m_GunDef) return 0;

	float ret = m_GunDef->reloadTimeBonus;
	ret += m_Host->getPlayerAttrib()->getActorAttValueWithStatus(BUFFATTRT_RELOADRATE_PERCENT, 1);
	return ret;
}

float CustomGunUseComponent::adsSwitchTimeBonus()//瞄准时间倍率
{
	if (!m_GunDef) return 0;

	float ret = m_GunDef->adsSwitchTimeBonus;
	ret += m_Host->getPlayerAttrib()->getActorAttValueWithStatus(BUFFATTRT_AIM_TIME_PERCENT, 1);
	return ret;
}

//腰射移动速度
float CustomGunUseComponent::hipMoveSpeedBonus()
{
	if (!m_GunDef) return 0;

	float ret = m_GunDef->hipMoveSpeedBonus;
	ret += m_Host->getPlayerAttrib()->getActorAttValueWithStatus(BUFFATTRT_HIPFIRE_MOVESPEED_PERCENT, 1);
	return ret;
}

//ADS移动速度
float CustomGunUseComponent::adsMoveSpeedBonus()
{
	if (!m_GunDef) return 0;

	float ret = m_GunDef->adsMoveSpeedBonus;
	ret += m_Host->getPlayerAttrib()->getActorAttValueWithStatus(BUFFATTRT_IRONSIGHT_MOVESPEED_PERCENT, 1);
	return ret;
}

float CustomGunUseComponent::rpmBonus()//射速倍率
{
	if (!m_GunDef) return 0;
	float ret = m_GunDef->rpmBonus;
	ret += m_Host->getPlayerAttrib()->getActorAttValueWithStatus(BUFFATTRT_FIRE_RATE_PERCENT, 1);
	return ret;
}

// 基于射线拾取命中对象（Actor与方块）并合并排序，供命中结算使用
void CustomGunUseComponent::pickBlocksAndActors(ClientPlayer* player, const MINIW::WorldRay& input_ray, std::vector<BlockActor>& actors, float decayStart, float decayFinish, float decayMin, float dacayLiquid, int liquidMax, float penetration, UGCCFG& blockPenetration)
{
	MINIW::WorldRay ray = input_ray;
	if (ray.m_Dir.x == 0)
		ray.m_Dir.x = 0.001f;
	if (ray.m_Dir.y == 0)
		ray.m_Dir.y = 0.001f;
	if (ray.m_Dir.z == 0)
		ray.m_Dir.z = 0.001f;

	ClientActor::s_AABBUseHitBox = true;
	Rainbow::Ray rRay(ray.m_Origin.toVector3(), ray.m_Dir);
	float range = ray.m_Range;
	player->getWorld()->getActorMgr()->GetActorMGT()->QueryWithRay(rRay, [this, range, &rRay, player, &actors](IClientActor* iobj, float t0, float t1) {
		//不计算自己
		ClientActor* obj = iobj ? iobj->GetActor() : nullptr;
		if (obj == player)
		{
			return true;
		}
		if (player->isMyAccountHorse(obj->getObjId())) //自己召唤的坐骑也不算
		{
			return true;
		}

		//超出射程
		if (t0 > range)
		{
			return true;
		}
		Vector3f point = rRay.GetPoint(t0);
		auto aabb = obj->GetAABB();
		float height = aabb.GetExtent().y * 2;
		float detaY = aabb.CalculateMax().y - point.y;
		BlockActor actor;
		actor.obj = obj;
		actor.t0 = t0;
		actor.t1 = t1;
		actor.per = detaY / height;
		actor.point = point;
		actors.push_back(actor);
		return true;
		});
	ClientActor::s_AABBUseHitBox = false;


	IntersectResult presultBlocks;
	float scale = 1.0f / float(BLOCK_SIZE);
	Rainbow::Vector3f origin = ray.m_Origin.toVector3() * scale;

	intersectForGun(origin, ray.m_Dir, range * scale, &presultBlocks, decayStart, decayFinish, decayMin, dacayLiquid, liquidMax, penetration, blockPenetration, player->getWorld());
	for (int i = 0; i < presultBlocks.blocks.size(); i++)
	{
		actors.push_back(std::move(presultBlocks.blocks[i]));
	}

	auto func = [](const BlockActor& a, const BlockActor& b)
	{
		return a.t0 < b.t0;
	};
	std::sort(actors.begin(), actors.end(), func);
}
//空气衰减参数
//float decaystart = 1200.0f;//衰减开始
//float decayfinish = 5000.0f;//衰减结束
//float decaymin = 0.75;//空气衰减最小值
//
//液体衰减参数
//float dacayliquid = 1.0f;//液体衰减系数，越大可穿透距离越远
//float liquidMax = 4.0f;//可穿过液体最大个数

// 在体素网格中使用DDA射线步进，收集与射线相交的方块并计算空气/液体/固体衰减
bool CustomGunUseComponent::intersectForGun(const Rainbow::Vector3f& origin, const Rainbow::Vector3f& dir, float radius, IntersectResult* presult, float decayStart, float decayFinish, float decayMin, float dacayLiquid, int liquidMax, float penetration, UGCCFG& blockPenetration, World* world)
{
	Rainbow::Vector3f origin_c2 = origin * BLOCK_FSIZE;

	//cube containing origin point
	int x = (int)floor(origin.x);
	int y = (int)floor(origin.y);
	int z = (int)floor(origin.z);

	//break out direction vector
	float dx = dir.x;
	float dy = dir.y;
	float dz = dir.z;

	//direction to increment x, y, z when stepping
	int stepX = signum(dx);
	int stepY = signum(dy);
	int stepZ = signum(dz);

	float tMaxX = intbound(origin.x, dx);
	float tMaxY = intbound(origin.y, dy);
	float tMaxZ = intbound(origin.z, dz);

	float tDeltaX = stepX / dx;
	float tDeltaY = stepY / dy;
	float tDeltaZ = stepZ / dz;

	DirectionType face = DIR_NOT_INIT;
	bool inter_ret = false;
	WCoord blockorigin = WCoord(WorldPos::GetOriginPos()) / BLOCK_SIZE;

	//中间参数
	float decayDeta = decayFinish - decayStart;
	float decaymax = 1.0f - decayMin;
	float liquidPer = 1.0f / (dacayLiquid * liquidMax);

	float airDistance = 0.0f;//空气衰减距离，最大为decayfinish
	float airAtten = 0.0f;//空气衰减
	float liquidAtten = 0.0f;//液体衰减
	float solidAtten = 0.0f;//固体衰减

	while (true)
	{
		do
		{
			if (!(y >= 0 && y < CHUNK_BLOCK_Y))
				break;

			WCoord blockpos = blockorigin + WCoord(x, y, z);
			int blockid = world->getBlockID(blockpos);
			if (blockid <= 0)
				break;

			BlockMaterial* blockmtl = g_BlockMtlMgr.getMaterial(blockid);
			if (!blockmtl)
				continue;

			if (IsBlockCollideWithRay(blockmtl, world, blockpos, origin_c2, dir, presult))
			{
				BlockActor actor;
				actor.blockPos = blockpos;
				actor.t0 = presult->collide_t;
				actor.point = presult->vecThroughTerrain;
				actor.face = presult->face;
				actor.blockid = blockid;
				presult->blocks.emplace_back(actor);
				//空气衰减
				if (airDistance < decayFinish)
				{
					airDistance = Distance(actor.point, origin_c2);
				}

				if (airDistance <= decayStart)
				{
					//do nothing
				}
				else if (airDistance > decayStart && airDistance < decayFinish)
				{
					airAtten = (airDistance - decayStart) * decaymax / decayDeta;
				}
				else
				{
					if (airAtten <= 0)
						airAtten = decaymax;
				}

				if (blockmtl->isLiquid())
				{
					//液体衰减
					liquidAtten += liquidPer;
				}
				else
				{
					std::string blocktype;
					int mineTool = 0;
					if (blockmtl->GetBlockDef())
					{
						blocktype = blockmtl->GetBlockDef()->Type;
						mineTool = blockmtl->GetBlockDef()->MineTool;
					}

					//方块衰减
					if (blocktype == "basic")// basic类型方块衰减
					{
						float value = penetration;
						auto iter = blockPenetration.basic.find(mineTool);
						if (iter != blockPenetration.basic.end())
						{
							value = iter->second;
						}
						solidAtten += value;
					}
					else
					{
						float value = blockPenetration.get(blocktype, penetration);
						solidAtten += value;
					}
				}

				if ((airAtten + liquidAtten + solidAtten) > penetration)
				{
					return true;
				}
			}
		} while (false);

		// tMaxX stores the t-value at which we cross a cube boundary along the
		// X axis, and similarly for Y and Z. Therefore, choosing the least tMax
		// chooses the closest cube boundary. Only the first case of the four
		// has been commented in detail.
		if (tMaxX < tMaxY)
		{
			if (tMaxX < tMaxZ)
			{
				if (tMaxX > radius) break;
				// Update which cube we are now in.
				x += stepX;
				// Adjust tMaxX to the next X-oriented boundary crossing.
				tMaxX += tDeltaX;
				// Record the normal vector of the cube face we entered.
				face = DIR_NEG_X;
			}
			else
			{
				if (tMaxZ > radius) break;
				z += stepZ;
				tMaxZ += tDeltaZ;
				face = DIR_NEG_Z;
			}
		}
		else
		{
			if (tMaxY < tMaxZ)
			{
				if (tMaxY > radius) break;
				y += stepY;
				tMaxY += tDeltaY;
				face = DIR_NEG_Y;
			}
			else
			{
				// Identical to the second case, repeated for simplicity in
				// the conditionals.
				if (tMaxZ > radius) break;
				z += stepZ;
				tMaxZ += tDeltaZ;
				face = DIR_NEG_Z;
			}
		}
	}

	return presult->blocks.size() > 0;
}
