
#ifndef __BLOCKFLOWERPOT_H__
#define __BLOCKFLOWERPOT_H__

#include "BlockMaterial.h"

class BlockFlowerPot : public ModelBlockMaterial //tolua_exports
{ //tolua_exports
	DECLARE_BLOCKMATERIAL(BlockFlowerPot)
public:
	//tolua_begin
	virtual void init(int resid);
	virtual void createBlockMesh(const BuildSectionMeshData& data, const WCoord &blockpos, SectionMesh *poutmesh) override;
	virtual bool onTrigger(World *pworld, const WCoord &blockpos, DirectionType face, IClientPlayer *player, const Rainbow::Vector3f &colpoint=Rainbow::Vector3f(0, 0, 0));
	virtual void dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata=0, BLOCK_MINE_TYPE droptype=BLOCK_MINE_NOTOOL, float chance=1.0f, int uin = -1) override;

	virtual int convertDataByRotate(int blockdata, int rotatetype);
	virtual int getBlockGeomID(int *idbuf, int *dirbuf, const SectionDataHandler* sectionData, const WCoord &blockpos, World* world);

	static int blockdata2Item(int blockdata, bool smallpot);
	static int item2Blockdata(int itemid, bool smallpot);
	virtual BlockMaterial::BlockType BlockTypeId() { return BlockMaterial::BlockType::BlockType_FlowerPot; }

	bool m_isSmallPot;
	//tolua_end
}; //tolua_exports

#endif