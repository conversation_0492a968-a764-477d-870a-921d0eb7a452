#ifndef __I_CLIENT_PLAYER_H__
#define __I_CLIENT_PLAYER_H__
#include "SandboxEngine.h"
#include "ClientAccountInfo.h"
#include "OgreWCoord.h"
#include "CoreCommonDef.h"
#include "TaskData.h"
class PlayerAttrib;
class IBackPack;
class World;
class ChunkIOMgr;
class WorldContainer;
class IActorLocoMotion;
class ChunkViewer;
class IClientActor;
class ClientPlayer;
class MpActorTrackerEntry;
class BackPackGrid;
namespace game
{
	namespace common
	{
		class PB_RoleData;
		class PB_SkillCDData;
	}
}

namespace Rainbow
{
	class IActorBody;
}
namespace MNSandbox
{
	class Component;
	class SandboxNode;
}
//tolua_begin
enum
{
	PLAYER_NOTIFYINFO_GETITEM = 0,
	PLAYER_NOTIFYINFO_TIPS,
	PLAYER_NOTIFYINFO_DEATH,
	PLAYER_NOTIFYINFO_HORSEEGG,  //可以产坐骑蛋
	PLAYER_NOTIFYINFO_KILLPLAYER,//击杀玩家.
	PLAYER_NOTIFYINFO_GETMULTIITEM,// 获取多个物品
	PLAYER_NOTIFYINFO_TASKGET,//任务获得
};

enum
{
	PLAYEROP_WAY_NORMAL = 0,
	PLAYEROP_WAY_FOOTBALLER,	//足球员的操作方式
	PLAYEROP_WAY_GRAVITYGUN,	//重力手套的操作方式
	PLAYEROP_WAY_BASKETBALLER,	//篮球员的操作方式
	PLAYEROP_WAY_FISHING, //钓鱼操作方式
	PLAYEROP_WAY_PUSHSNOWBALL,//推雪球操作方式
};
enum
{
	PLAYEROP_NULL = 0,
	PLAYEROP_ATTACK_BOW,
	PLAYEROP_EATFOOD,
	PLAYEROP_DRINKWATER,
	PLAYEROP_DIG,
	PLAYEROP_USE_ITEM_SKILL,
	PLAYEROP_CATCH_BALL,
	PLAYEROP_SHOOT,
	PLAYEROP_PASS_BALL,
	PLAYEROP_TACKLE,
	PLAYEROP_TACKLE_END,
	PLAYEROP_BALL_CHARGE_BEGIN,
	PLAYEROP_THROW_GRAVITYACTOR,
	PLAYEROP_CATCH_GRAVITYACTOR,
	PLAYEROP_GRAVITY_CHARGE_BEGIN,
	//篮球部分
	PLAYEROP_BASKETBALL_BLOCK_SHOT,   //盖帽
	PLAYEROP_BASKETBALL_BLOCK_SHOT_END,

	PLAYEROP_BASKETBALL_OBSTRUCT,
	PLAYEROP_BASKETBALL_OBSTRUCT_END,
	PLAYEROP_BASKETBALL_GRAB,
	PLAYEROP_BASKETBALL_GRAB_END,

	PLAYEROP_BASKETBALL_DRIBBLERUN,
	PLAYEROP_BASKETBALL_DRIBBLERUN_END,
	PLAYEROP_BASKETBALL_PASS,
	PLAYEROP_BASKETBALL_PASS_END,
	PLAYEROP_BASKETBALL_SHOOT,
	PLAYEROP_BASKETBALL_SHOOT_END,
	PLAYEROP_BASKETBALL_CHARGE_BEGIN,
	PLAYEROP_SHEILD_DEFENCE_BEGIN,

	PLAYEROP_PUSHSNOWBALL_CHARGE_BEGIN,
	PLAYEROP_PUSHSNOWBALL_SHOOT,
	PLAYEROP_PUSHSNOWBALL_SHOOT_END,
	PLAYEROP_PUSHSNOWBALL_MAKEBALL,
	PLAYEROP_PUSHSNOWBALL_MAKEMAN,
	PLAYEROP_PUSHSNOWBALL_JUMP,

	PLAYEROP_STATUS_BEGIN = 0,
	PLAYEROP_STATUS_END,
	PLAYEROP_STATUS_CANCEL
};

enum
{
	FREEZING_STATE_CLEAN = 0,
	FREEZING_STATE_CANMOVE,
	FREEZING_STATE_NOMOVE,	//禁锢状态
};

enum MotionState
{
	MOTION_STATIC = 0,
	MOTION_MOVE = 1,
	MOTION_RUN = 2,
	MOTION_JUMP = 4,
	MOTION_TWOJUMP = 8,
	MOTION_SNEAK = 16,
	MOTION_FALLGROUND = 32,
};

enum BasketballFall
{
	NOT_HAS_TARGET = -1,
	BEFORE_DESTION,
	HIT_DESTION,
	BEHIND_DESTION
};

struct SummonPetInfomation
{
	int monsterid;
	int petid;
	int stage;
	int quality;
	SummonPetInfomation() : monsterid(0), petid(0), stage(0), quality(0)
	{
	}
};

//持枪状态
enum GunHoldState
{
	NOGUN = 0,//未持枪
	GUN,//持老枪
	CUSTOMGUN//持新枪
};

//tolua_end
/* ClientPlayer 基类 */
class EXPORT_SANDBOXENGINE IClientPlayer //tolua_exports
{ //tolua_exports
public:
	IClientPlayer() = default;
	virtual ~IClientPlayer() = default;
	virtual PlayerAttrib* getPlayerAttrib() = 0;

	//tolua_begin
	virtual IClientActor* CastToActor() = 0;
	virtual MNSandbox::SandboxNode* CastToNode() = 0;
	virtual ClientPlayer* GetPlayer() = 0;
	virtual const char* getNickname() = 0;
	virtual void setRocketTeleport(bool b) = 0;
	virtual bool isRocketTeleport() = 0; 

	virtual bool isStarStationTeleporting() = 0;
	virtual void setIsStarStationTeleporting(bool isTeleporting) = 0;
	virtual void teleportRidingRocket(int targetmap) = 0;
	virtual long long iGetObjId() = 0;
	virtual std::set<int>& GetDirtyGridIndex() = 0;
	virtual WCoord& iGetPosition() = 0;
	virtual void SetPlayerPosition(const WCoord& position) = 0;
	virtual unsigned short iGetCurMapID() = 0;
	virtual bool           isMoveControlActive() const                     = 0;
	virtual void           syncPos2Client(bool sync_motion = false)        = 0;

	virtual Rainbow::IActorBody* iGetBody() = 0;
	
	virtual int getCurViewRange() = 0;
	virtual ChunkViewer* getChunkViewer() = 0;
	virtual void SetPlayerAttrViewRange(int range) = 0;
	virtual WorldContainer* getEmitterWorldContainer() = 0;
	virtual IActorLocoMotion* GetPlayerLocoMotion() = 0;
	virtual bool execCmd(const char* cmdstr) = 0;
	virtual IBackPack* getIBackPack() = 0;
    virtual void notifyGameInfo2Self(int infotype, int id, int num = 0, const char* name = NULL, const char* buff = NULL) = 0;
    virtual bool checkActionAttrState(int actionattr) = 0;
	virtual void updateTaskSysProcess(TASKSYS_TYPE type, int target1 = 0, int target2 = 0, int goalnum = 1) = 0;
	virtual bool isInSpectatorMode() = 0;
	virtual void setSpectatorUin(int uin) = 0;
	virtual PLAYER_SPECTATOR_TYPE getSpectatorType() = 0;
	virtual int getSpectatorUin() = 0;
	virtual int getToSpectatorUin() = 0;
	virtual int getCurToolID() = 0;
	virtual void renderUI() = 0;
	virtual bool isExploiting() = 0;
	virtual int getOpenContainerBaseIndex() = 0;
	virtual void GetPlayerFaceDir(float& x, float& y, float& z) = 0;
	virtual void SetMoveForward(float value) = 0;
	virtual void SetMoveRight(float value) = 0;
	virtual int getUin() const = 0;
	virtual void tryStandup() = 0;
	virtual World* GetPlayerWorld() = 0;
	virtual IClientActor* GetCurAccountHorse() = 0;
	virtual bool hasUIControl() = 0;
	virtual void stopMusicByTrigger(const char* path = NULL) = 0;
	virtual int iGetTeam() = 0;
	virtual void SetPlayerTeam(int id) = 0;
	virtual const char* getCustomjson() = 0;
	virtual void setCustomJson(std::string&) = 0;
	virtual void clearTrackerEntry() = 0;
	virtual int getSkinID() = 0;
	virtual std::vector<MpActorTrackerEntry*>& getTrackerEntrys() = 0;
	virtual int getGameResults() = 0;
	virtual void addTrackerEntry(MpActorTrackerEntry* entry) = 0;
	virtual int getGameScore() = 0;
	virtual std::string getBPTitle() = 0;
	virtual int getGameRanking() = 0;
	virtual void setTeleportPos(const WCoord& pos) = 0;
	virtual int GetAccountSkinID() = 0;

	virtual const VipInfo& getVipInfo() = 0;

	virtual bool saveToFile(long long owid = 0, ChunkIOMgr* iomgr = NULL) = 0;

	virtual bool saveUserData(long long owid) = 0;
	virtual bool saveTechTree(long long owid) = 0;

	virtual bool CanExposePosToOther() = 0;

	virtual WCoord getRealLandingPoint(int targetmap, World* pworld) = 0;

	virtual void gotoTeleportPos(World* pworld, const WCoord& blockpos, WCoord& realpos) = 0;
	virtual void gotoTransferPos(World* pworld, const WCoord& blockpos) = 0;
	virtual void gotoSpawnPoint(World* pworld, bool bRandom = true) = 0;

	virtual void addAchievement(int flags, ACHIEVEMENT_TYPE achievetype, int target_id = 0, int num = 1) = 0;
	virtual void addOWScore(float score) = 0;
	virtual void setSyncCustomModelTick(int tick) = 0;
	virtual void setSyncTransferTick(int tick) = 0;
	virtual void checkNewbieWorldProgress(int curLv, int curStep) = 0;
	virtual void applyEquips(EQUIP_SLOT_TYPE t = MAX_EQUIP_SLOTS) = 0;

	virtual bool isUnlockItem(int itemid) = 0;

	virtual bool isHost() = 0;
	virtual bool isRemote() = 0;

	virtual long long getOWID() = 0;

	virtual WorldContainer* getCurOpenedContainer() = 0;

	virtual WCoord getCurOpenedContainerPos() = 0;

	virtual void cleanupOpenedContainer() = 0;
	virtual void syncOpenFCMUIToClient(const WCoord& blockpos, bool isedited, std::string url, int version = 0, int result = 0) = 0;

	virtual int getCurOperate() = 0;
	virtual WCoord getCurOperatePos(int operatedistance = 5) = 0;

	virtual int GetPlayerCurPlaceDir() = 0;

	virtual int getPlaceDirToBlock(const WCoord& blockpos) = 0;

	virtual void shortcutItemUsed(bool ignoreDurable = false) = 0;

	virtual WORLD_ID getHookObj() = 0;

	virtual int GetTreeItemIndex() = 0;

	virtual int getOPWay() = 0;
	virtual void openEditActorModelUI(WorldContainer* container) = 0;
	virtual bool openContainer(WorldContainer* container) = 0;
	virtual void closeContainer() = 0;
	virtual void checkShowMusicClubChatBubble(float dtime) = 0;
	virtual int getCurShortcut() = 0;
	virtual int getCurShortcutItemNum() = 0;

	virtual int sleepInBed(const WCoord& blockpos, bool refreshRevivePoint = true) = 0;

	virtual int sleepInVehicleBed(const WCoord & blockpos, World * pworld) = 0;

	virtual int sleep(const WCoord & blockpos, bool refreshRevivePoint = false) = 0;
	virtual void notifyOpenWindow2Self(int blockid, int x = -1, int y = -1, int z = -1) = 0;

	virtual bool BlockBookCabinetVehicleDir(Rainbow::Vector3f& viewDir, Rainbow::Vector3f& centerDir, World* pworld, const WCoord& blockpos) = 0;

	virtual MNSandbox::Component* getPlayerAttribComponent() = 0;

	virtual int getContainersPassword(const WCoord& pos) = 0;
	virtual void setContainersPassword(const WCoord& pos, int password) = 0;
	virtual bool checkIsOpenContainer(const WCoord& pos, int index) = 0;
	virtual void refreshAvarta() = 0;
	virtual void PlayerWakeUp(bool immediately, bool updateallflag, bool setrevive) = 0;
	virtual void SetRunSandboxPlayer(bool value) = 0;
	virtual bool IsRunSandboxPlayer() = 0;
	virtual void changePlayerModel(int playerindex, int mutatemob = 0, const char* customskin = "", const char* custommodel = NULL, int itemid = 0, int blockid = 0, bool force = false) = 0;
	virtual void setSpeedUpTimes(unsigned char speedUpTimes) = 0;
	virtual int getSelectedColor() = 0;
	virtual void setSelectedColor(int color) = 0;
	virtual bool isUseHearth() = 0;
	virtual void triggerInputEvent(int vkey, const char* ktype) = 0;
	virtual int getEquipItem(EQUIP_SLOT_TYPE t) = 0;
	virtual BackPackGrid* getEquipGrid(EQUIP_SLOT_TYPE t) = 0;
	virtual BackPackGrid* getEquipGridWithType(EQUIP_SLOT_TYPE t) = 0;
	virtual int damageEquipItem(EQUIP_SLOT_TYPE t, int damage) = 0;
	virtual bool isShapeShift() = 0;
	virtual void setPianoSoundName(std::string pSoundName) = 0;
	virtual void setPianoPaticleName(std::string pPaticleName) = 0;
	virtual void setPaticlePos(WCoord pos) = 0;
	virtual void setPianoSoundPos(WCoord pos) = 0;
	virtual std::string getCurSummonPetID() = 0;
	virtual SummonPetInfomation getCurSummonPetInfo() = 0;
	virtual void summonPet(int monsterid, std::string serverid, int petid, int stage, int quality, std::string petName = "") = 0;
	virtual void changeRoleData(game::common::PB_RoleData* pRoleData) = 0;
	virtual void saveSkillCDCompToPB(game::common::PB_SkillCDData* skillCDData) = 0;
	virtual PLAYER_SPECTATOR_MODE getSpectatorMode() = 0;
	virtual bool IsOnPlatform() = 0;
	virtual IClientActor* GetCatchBall() = 0;
	virtual IClientActor* GetPlatformIClientActor() = 0;
	virtual bool GetGunZoom() = 0;
	virtual void doWaterCanoonSkill() = 0;
	virtual void setFaceYaw(float yaw, bool needSync = false) = 0;
	virtual bool isMotionChangeSyncPos() = 0;
	virtual bool GetScreenSpacePos(Rainbow::Vector3f pos, int& x, int& y) = 0;

	virtual bool IsCurToolBroken() = 0;

	virtual bool IsOffline() = 0;
	virtual bool checkInSafeZone() = 0;
	//tolua_end
	virtual void setDrinllingSingleHole(bool value) = 0;
	virtual bool getDrinllingSingleHole() = 0;
#ifdef BUILD_MINI_EDITOR_APP
	virtual void SetBindChunk(bool toggle) = 0;
	virtual void setCurToolID(int nToolId) = 0;
#endif //BUILD_MINI_EDITOR_APP

};//tolua_exports
#endif
