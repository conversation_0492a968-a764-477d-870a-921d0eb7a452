// 领地查询系统使用示例
// 这个文件展示了如何在游戏逻辑中使用新的领地查询接口

#include "TerritoryQueryInterface.h"
#include "Core/actors/helper/IClientPlayer.h"
#include "Common/OgreWCoord.h"

// 示例1：检查玩家是否在自己的领地内（最常用的功能）
void CheckPlayerTerritoryStatus(IClientPlayer* player)
{
    if (!player) return;
    
    // 简单检查玩家是否在授权领地内
    bool isInOwnTerritory = TerritoryQuery::IsPlayerInOwnTerritory(player);
    
    if (isInOwnTerritory)
    {
        // 玩家在自己的领地或被授权的领地内
        // 可以执行特殊逻辑，比如显示领地状态UI、启用特殊功能等
        //player->showMessage("您在自己的领地范围内");
    }
    else
    {
        // 玩家在野外或其他人的领地内
        // 可能需要限制某些行为
       // player->showMessage("您在野外或他人领地内");
    }
}

// 示例2：获取详细的领地信息
void ShowDetailedTerritoryInfo(IClientPlayer* player)
{
    if (!player) return;
    
    auto territoryInfo = TerritoryQuery::GetPlayerTerritoryInfo(player);
    
    if (territoryInfo.isInTerritory)
    {
        if (territoryInfo.isOwner)
        {
            //player->showMessage("欢迎回到您的领地！");
        }
        else if (territoryInfo.isAuthorized)
        {
            //player->showMessage("您被授权访问此领地");
        }
        else
        {
           // player->showMessage("您在他人的领地内，请注意行为规范");
        }
        
        // 可以显示更多领地信息
        if (territoryInfo.territory)
        {
            // 获取领地的其他信息...
        }
    }
    else
    {
       // player->showMessage("您当前在野外");
    }
}

// 示例3：建造权限检查
bool CheckBuildPermission(const WCoord& buildPos, IClientPlayer* player)
{
    if (!player) return false;
    
    unsigned int playerUin = player->getUin();
    
    // 检查玩家是否可以在指定位置建造
    bool canBuild = TerritoryQuery::CanPlayerBuildAt(buildPos, playerUin);
    
    if (!canBuild)
    {
        // 获取该位置的领地信息以提供更详细的拒绝原因
        Rainbow::Vector3f worldPos = buildPos.toVector3() * 100.0f;
        auto territoryInfo = TerritoryQuery::GetTerritoryInfo(worldPos, playerUin);
        
        if (territoryInfo.isInTerritory && !territoryInfo.isAuthorized)
        {
           // player->showMessage("无法建造：您没有在此领地的建造权限");
        }
        else
        {
           // player->showMessage("无法建造：未知原因");
        }
    }
    
    return canBuild;
}

// 示例4：实时监控玩家领地状态变化
class PlayerTerritoryMonitor
{
private:
    IClientPlayer* m_player;
    bool m_lastTerritoryStatus;
    TerritoryContainer* m_lastTerritory;
    
public:
    PlayerTerritoryMonitor(IClientPlayer* player)
        : m_player(player)
        , m_lastTerritoryStatus(false)
        , m_lastTerritory(nullptr)
    {
    }
    
    void Update()
    {
        if (!m_player) return;
        
        // 检查当前状态
        bool currentStatus = TerritoryQuery::IsPlayerInOwnTerritory(m_player);
        TerritoryContainer* currentTerritory = TerritoryQuery::GetTerritoryAt(m_player->iGetPosition().toVector3() * 100.0f);
        
        // 检查是否进入了新的领地状态
        if (currentStatus != m_lastTerritoryStatus)
        {
            if (currentStatus)
            {
                OnEnterAuthorizedTerritory(currentTerritory);
            }
            else
            {
                OnLeaveAuthorizedTerritory(m_lastTerritory);
            }
        }
        // 检查是否切换了领地
        else if (currentTerritory != m_lastTerritory)
        {
            if (currentTerritory && m_lastTerritory)
            {
                OnTerritoryChanged(m_lastTerritory, currentTerritory);
            }
        }
        
        m_lastTerritoryStatus = currentStatus;
        m_lastTerritory = currentTerritory;
    }
    
private:
    void OnEnterAuthorizedTerritory(TerritoryContainer* territory)
    {
        if (m_player && territory)
        {
            // 玩家进入了有权限的领地
           // m_player->showMessage("进入领地：您有在此区域的活动权限");
            
            // 可以触发特殊效果、音效等
            // PlayTerritoryEnterEffect();
            
            // 可以显示领地信息UI
            // ShowTerritoryInfoUI(territory);
        }
    }
    
    void OnLeaveAuthorizedTerritory(TerritoryContainer* territory)
    {
        if (m_player)
        {
            // 玩家离开了有权限的领地
            //m_player->showMessage("离开领地：进入野外区域");
            
            // 可以触发特殊效果
            // PlayTerritoryLeaveEffect();
            
            // 隐藏领地相关UI
            // HideTerritoryInfoUI();
        }
    }
    
    void OnTerritoryChanged(TerritoryContainer* oldTerritory, TerritoryContainer* newTerritory)
    {
        if (m_player)
        {
            // 玩家在不同的领地间移动
            //m_player->showMessage("切换领地");
            
            // 更新UI显示
            // UpdateTerritoryInfoUI(newTerritory);
        }
    }
};

// 示例5：性能测试和监控
void PerformanceTest()
{
    // 获取系统统计信息
    auto stats = TerritoryQuery::GetSystemStats();
    
    // 输出性能信息
    printf("领地系统性能统计:\n");
    printf("- 八叉树优化: %s\n", stats.octreeEnabled ? "启用" : "禁用");
    printf("- 总领地数量: %d\n", stats.totalTerritories);
    printf("- 八叉树节点数: %d\n", stats.octreeNodes);
    printf("- 最大深度: %d\n", stats.maxDepth);
    printf("- 平均每叶子节点领地数: %.2f\n", stats.avgTerritoriesPerLeaf);
    
    // 可以根据性能数据动态调整优化策略
    if (stats.totalTerritories > 1000 && !stats.octreeEnabled)
    {
        printf("建议启用八叉树优化以提高查询性能\n");
        TerritoryQuery::SetOptimizationEnabled(true);
    }
}

// 示例6：批量查询优化
void BatchTerritoryCheck(const std::vector<WCoord>& positions, unsigned int playerUin)
{
    std::vector<bool> results;
    results.reserve(positions.size());
    
    // 对于大量位置的批量检查，八叉树优化会显著提高性能
    for (const auto& pos : positions)
    {
        bool canBuild = TerritoryQuery::CanPlayerBuildAt(pos, playerUin);
        results.push_back(canBuild);
    }
    
    // 处理结果...
}




