#ifndef BLOCK_KEY_OF_BROKEN_SWORD_H
#define BLOCK_KEY_OF_BROKEN_SWORD_H
#include "BlockMaterial.h"

class BlockKeyOfBrokenSword : public ModelBlockMaterial //tolua_exports
{ //tolua_exports
	DECLARE_BLOCKMATERIAL(BlockKeyOfBrokenSword)
public:
	//tolua_begin
	BlockKeyOfBrokenSword();
	virtual ~BlockKeyOfBrokenSword();
	virtual float getDestroyHardness(int blockdata, IClientPlayer *player);
	virtual void init(int resid) override;
	virtual void onBlockPlacedBy(World* pworld, const WCoord& blockpos, IClientPlayer* player) override;
	virtual void onNotify(World* pworld, const WCoord& blockpos, int blockid) override;
	virtual bool onTrigger(World* pworld, const WCoord& blockpos, DirectionType face, IClientPlayer* player, const Rainbow::Vector3f& colpoint = Rainbow::Vector3f(0, 0, 0)) override;

	virtual void onBlockAdded(World* pworld, const WCoord& blockpos) override;
	virtual void onBlockRemoved(World* pworld, const WCoord& blockpos, int blockid, int blockdata) override;
	virtual void dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata = 0, BLOCK_MINE_TYPE droptype = BLOCK_MINE_NOTOOL, float chance = 1.0f, int uin = -1);
	//virtual bool hasContainer() override
	//{
	//	return true;
	//}
	virtual WorldContainer *createContainer(World *pworld, const WCoord &blockpos) override;
	//tolua_end
private:
	virtual int getBlockGeomID(int* idbuf, int* dirbuf, const SectionDataHandler* sectionData, const WCoord& blockpos, World* world) override;
	virtual int getProtoBlockGeomID(int* idbuf, int* dirbuf) override;
}; //tolua_exports
#endif // BLOCK_KEY_OF_BROKEN_SWORD_H

