
#include "BlockLilyPad.h"
#include "world.h"
#include "special_blockid.h"
#include "WorldProxy.h"
#include "BlockGeom.h"
#include "BlockLilyModel.h"
#include "SectionMesh.h"
#include "Collision.h"
#include "IClientPlayer.h"
#include "BlockMaterialMgr.h"
#include "Common/OgreShared.h"

using namespace MINIW;
IMPLEMENT_BLOCKMATERIAL(LilyPadMaterial)

static int blockID_UpItems[] = {250};  //暂时只有这一种

LilyPadMaterial::LilyPadMaterial()
{

}

LilyPadMaterial::~LilyPadMaterial()
{

}

int LilyPadMaterial::blockdata2Item(int blockdata)
{
	if(blockdata > 7)
	{
		if((blockdata>>3) <= sizeof(blockID_UpItems)/sizeof(int)) 
			return blockID_UpItems[(blockdata>>3) - 1];
	    else
			return 0;
	}
	return 0;
}

int LilyPadMaterial::item2Blockdata(int itemid, int blockdata)
{
	if(itemid == 0) return 0;

	for(int i=0; i<sizeof(blockID_UpItems)/sizeof(int); i++)
	{
		if(blockID_UpItems[i] == itemid) return (i+1)<<3 | (blockdata & 7);
	}
	return 0;
}

bool LilyPadMaterial::canPutOntoPos(WorldProxy *pworld, const WCoord &blockpos)
{
	int blockId = pworld->getBlockID(DownCoord(blockpos));
	return BlockMaterialMgr::isWater(blockId, true)/*blockId ==BLOCK_STILL_WATER*/ &&  pworld->isAirBlock(blockpos);
}


bool LilyPadMaterial::canPutOntoFace(WorldProxy *pworld, const WCoord &blockpos, int face)
{
//	if (face == DIR_POS_Y) return true;
	return true;
}

void LilyPadMaterial::onNotify(World *pworld, const WCoord &blockpos, int blockid)
{
	WCoord toppos = TopCoord(blockpos);
	blockid = pworld->getBlockID(toppos);
	if (blockid == BLOCK_AIR)
	{
		return;
	}

	blockid = pworld->getBlockID(DownCoord(blockpos));

	if (!IsWaterBlockID(blockid))
	{
		dropBlockAsItem(pworld, blockpos);
		pworld->setBlockAll(blockpos, BLOCK_AIR, 0);
	}
}

void LilyPadMaterial::createCollideData(CollisionDetect *coldetect, World *pworld, const WCoord &blockpos)
{
	int dir = pworld->getBlockData(blockpos);
	WCoord origin = blockpos*BLOCK_SIZE;
	const int BS = BLOCK_SIZE;
	const int THICK = BLOCK_SIZE / 20;
	coldetect->addObstacle(origin, origin + WCoord(BS, THICK, BS));
}

int LilyPadMaterial::getBlockGeomID(int *idbuf, int *dirbuf, const SectionDataHandler* sectionData, const WCoord &blockpos, World* world)
{
	int blockdata = sectionData->getBlock(blockpos).getData();

	//idbuf[0] = 4;

	idbuf[0] = 0;
	dirbuf[0] = (int)((blockdata&7) * 45.0f + 6);

	return 1;
}

int LilyPadMaterial::getProtoBlockGeomID(int *idbuf, int *dirbuf)
{
	idbuf[0] = 0;
	dirbuf[0] = 0;

	return 1;
}


/*
	if (dir == 0) coldetect->addObstacle(origin, origin + WCoord(THICK, BS, BS));
	else if (dir == 1) coldetect->addObstacle(origin + WCoord(BS - THICK, 0, 0), origin + WCoord(BS, BS, BS));
	else if (dir == 2) coldetect->addObstacle(origin, origin + WCoord(BS, BS, THICK));
	else if (dir == 3) coldetect->addObstacle(origin + WCoord(0, 0, BS - THICK), origin + WCoord(BS, BS, BS));
	else if (dir == 4) coldetect->addObstacle(origin, origin + WCoord(BS, THICK, THICK));
	else coldetect->addObstacle(origin + WCoord(0, BS - THICK, 0), origin + WCoord(BS, BS, BS));
}*/

void LilyPadMaterial::createBlockMesh(const BuildSectionMeshData& data, const WCoord &blockpos, SectionMesh *poutmesh)
{
	BlockGeomTemplate* geom = getGeom(data.m_LODLevel);
	if (!geom)
		return;

	auto psection = data.m_SharedSectionData;
	int blockdata = psection->getBlock(blockpos).getData();
	int blockid = psection->getBlock(blockpos).getResID();
	BlockGeomMeshInfo meshinfo;

	Rainbow::ColorRGBAf verts_light[1] = { Rainbow::ColorRGBAf::black };
	psection->getBlockVertexLight(blockpos, verts_light);
	SectionSubMesh *psubmesh = poutmesh->getSubMesh(getDefaultMtl(), false, data.m_LODLevel);

	int idbuf[32];
	int dirbuf[32];
	int ngeom = getBlockGeomID(idbuf, dirbuf, psection, blockpos, data.m_World);


	ChunkRandGen chunkrand;
	WCoord wpos = psection->getOrigin() + blockpos;
	WCoordHashCoder coder;
	chunkrand.setSeed(coder(wpos));
	float angle = (chunkrand.getFloat()) * 360;
	const float max_scale = 1.75f;
	const float min_scale = 0.8f;
	const float scale = min_scale + (chunkrand.getFloat()) * (max_scale - min_scale);
	float tx = 0;
	float tz = 0;
	float ty = 0;
	if (blockid == BLOCK_LOTUS_LEAF || blockid == BLOCK_DUCKWEED || blockid == BLOCK_WATERWEED)
	{
		ty = -0.8 * BLOCK_FSIZE;
	}

	if(blockid != 248)//漂浮的木板有碰撞，不能偏移
	{
		tx = (chunkrand.getFloat() - chunkrand.getFloat()) * 0.4f;
		tz = (chunkrand.getFloat() - chunkrand.getFloat()) * 0.4f;
		ty = ty+((chunkrand.getFloat() - chunkrand.getFloat()) * 0.2f);
	}

	for (int i = 0; i < ngeom; i++)
	{
		geom->getFaceScaleVerts(meshinfo, idbuf[i], 1.0f, scale, 0, angle > 7 ? angle : 7,0, NULL,tx,ty, tz, 0);
	}

	if (getBlockResID() == BLOCK_LOTUS_LEAF)
	{
		int colorIndex = blockdata & 3;
		BlockColor blockColor = Rainbow::ColorRGBA32::white;
		if (colorIndex != 0) {
			blockColor = m_Def->RandomColors[colorIndex - 1];
		}
		blockColor.a = 0;
		psubmesh->addGeomBlockLight(meshinfo, &blockpos, verts_light, &blockColor, getDefaultMtl()->getUVTile());
	}
	else
	{
		psubmesh->addGeomBlockLight(meshinfo, &blockpos, verts_light, NULL, getDefaultMtl()->getUVTile());

	}

	//附加上面的荷花
	int flowerid = blockdata2Item(blockdata);
	if(flowerid > 0)
	{
		LilyModelMaterial *modelMtl = dynamic_cast<LilyModelMaterial*>(g_BlockMtlMgr.getMaterial(flowerid));
		if (modelMtl)
		{
			modelMtl->createBlockMesh(data, blockpos, poutmesh);
		}
	}

}

bool LilyPadMaterial::onTrigger(World *pworld, const WCoord &blockpos, DirectionType face, IClientPlayer *player, const Rainbow::Vector3f &colpoint)
{
	if (pworld->isRemoteMode())
	{
		return true;
	}

	int olddata = pworld->getBlockData(blockpos);
	int newdata = item2Blockdata(player->getCurToolID(), olddata);

	if(((olddata>>3)>0) && newdata==0)
	{
		int itemid = blockdata2Item(olddata);
		g_BlockMtlMgr.getMaterial(itemid)->dropBlockAsItem(pworld, blockpos, 0, BLOCK_MINE_NOTOOL, 1.0f);

		pworld->setBlockData(blockpos, newdata);
	}
	else if(((olddata>>3)==0) && newdata>0)
	{
		player->shortcutItemUsed();
		pworld->setBlockData(blockpos, newdata);
	}
	return true;
}

void LilyPadMaterial::dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata, BLOCK_MINE_TYPE droptype, float chance, int uin/* = -1 */)
{
	int flowerid = blockdata2Item(blockdata);

	if (flowerid == 250)
	{
		ModelBlockMaterial *hmtl = dynamic_cast<ModelBlockMaterial *>(g_BlockMtlMgr.getMaterial(flowerid));
		if (hmtl) hmtl->dropBlockAsItem(pworld, blockpos, 0, BLOCK_MINE_NOTOOL, chance, uin);
	}

	BlockMaterial::dropBlockAsItem(pworld, blockpos, blockdata, droptype, chance, uin);
}


void LilyPadMaterial::initGeomName()
{
	m_geomName = m_Def->Texture2.c_str();
}

void LilyPadMaterial::init(int resid)
{
	BlockMaterial::init(resid);
	//防止窝刷在上面
	SetToggle(BlockToggle_IsSolid, false);
	
	auto mtl = g_BlockMtlMgr.createRenderMaterial(GetBlockDef()->Texture1.c_str(), m_Def, GETTEX_WITHDEFAULT, getDrawType());
	m_defaultMtlIndex = getRenderMtlMgr().addMtl(mtl);
	
	if (g_BlockMtlMgr.IsUseVertexAnimationEffect(resid))
	{
		AppendBlockEffect(BLOCKEFFECT_VERTEX_ANIMATION);
		if (mtl)
			mtl->SetBlockEffectFlag(BLOCKEFFECT_VERTEX_ANIMATION);
	}

	OGRE_RELEASE(mtl);

	if (m_LoadOnlyLogic) return;
}

SectionMesh* LilyPadMaterial::createBlockProtoMesh(int protodata)
{
	SectionMesh* pmesh = ENG_NEW(SectionMesh)();
	RenderBlockMaterial* mtl = getDefaultMtl();
	SectionSubMesh* psubmesh = pmesh->getSubMesh(mtl, true);

	BlockGeomMeshInfo meshinfo;

	getGeom(0)->getFaceVerts(meshinfo, 0);
	psubmesh->addTriangleList(meshinfo.vertices.data(), meshinfo.vertices.size(), meshinfo.indices.data(), meshinfo.indices.size(), nullptr);

	//pmesh->onCreate();
	return pmesh;
}


void LilyPadMaterial::initDrawType()
{
	m_blockDrawType = BLOCKDRAW_GRASS;
}

void LilyPadMaterial::initDefaultMtl()
{

}