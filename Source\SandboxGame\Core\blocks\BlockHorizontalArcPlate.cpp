
#include "BlockHorizontalArcPlate.h"
#include "BlockMaterialMgr.h"
#include "section.h"
#include "SectionMesh.h"
#include "BlockGeom.h"
#include "world.h"
#include "DefManagerProxy.h"
#include "IPlayerControl.h"
#include "BlockMeshVert.h"
#include "WorldManager.h"
#include "WeatherManager.h"
//#include "OgreMaterial.h"
#include "Common/GameStatic.h"
#include "ClientPlayer.h"
#include "OgreShared.h"
IMPLEMENT_SCENEOBJECTCLASS(HorizontalArcHalfPlateMaterial)
//IMPLEMENT_BLOCKINSTANCE(WholeTriangleMaterial)
using namespace Rainbow;

static MINIW::GameStatic<dynamic_array<dynamic_array<BlockGeomVert>>> s_mHalfWholeFace;
dynamic_array<dynamic_array<BlockGeomVert>>& HorizontalArcHalfPlateMaterial::m_mHalfWholeFace()
{
	return *s_mHalfWholeFace.EnsureInitialized();
}

static MINIW::GameStatic<dynamic_array<dynamic_array<BlockGeomVert>>> s_mHalfFace;
dynamic_array<dynamic_array<BlockGeomVert>>& HorizontalArcHalfPlateMaterial::m_mHalfFace()
{
	return *s_mHalfFace.EnsureInitialized();
}
// dynamic_array<dynamic_array<BlockGeomVert>> HorizontalArcHalfPlateMaterial::m_mHalfWholeFace;
// dynamic_array<dynamic_array<BlockGeomVert>> HorizontalArcHalfPlateMaterial::m_mHalfFace;

float HorizontalArcHalfPlateMaterial::getBlockHeight(int blockdata)
{
	if (blockdata & 8)
	{
		if (blockdata & 4)
		{
			return -0.5f;
		}
		else
		{
			return 0.5f;
		}
	}
	else if (blockdata & 4)
	{
		return -0.25f;
	}
	return 0.25f;
}

void HorizontalArcHalfPlateMaterial::createCollideData(CollisionDetect* coldetect, World* pworld, const WCoord& blockpos)
{
	auto block = pworld->getBlock(blockpos);
	float blockheight = getBlockHeight(block.getData());
	WCoord pos = blockpos * BLOCK_SIZE;

	if (blockheight == 0.5f)
	{
		coldetect->addObstacle(pos, pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE, BLOCK_SIZE));
	}
	else if (blockheight == -0.5f)
	{
		coldetect->addObstacle(pos + WCoord(0, BLOCK_HALFSIZE, 0), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
	}
	else
	{
		short step = 10;
		float movesize = 1.f / float(step);
		float heightOffset = 1.f / (float(step));
		int heightsize = BLOCK_SIZE * 0.45f;
		int xzsize = BLOCK_SIZE;
		int dir = pworld->getBlockData(blockpos) & 3;

		auto pblock = pworld->getBlock(blockpos);
		int warp = -1;
		int turnDir = -1;

		auto frontBlock = pworld->getBlock(blockpos + g_DirectionCoord[dir]);
		if (pblock.getResID() == frontBlock.getResID() && frontBlock.getData() < 8 && ((pblock.getData() & 4) == (frontBlock.getData() & 4)))
		{
			int frontDir = frontBlock.getData() & 3;
			if (frontDir != dir && frontDir != ReverseDirection(dir))
			{
				warp = 0;
				turnDir = frontDir;
			}
		}
		if (warp == -1)
		{
			auto backBlock = pworld->getBlock(blockpos + g_DirectionCoord[ReverseDirection(dir)]);
			if (pblock.getResID() == backBlock.getResID() && backBlock.getData() < 8 && ((pblock.getData() & 4) == (backBlock.getData() & 4)))
			{
				int backDir = backBlock.getData() & 3;
				if (backDir != dir && backDir != ReverseDirection(dir))
				{
					warp = 1;
					turnDir = backDir;
				}
			}
		}
		int count = 10;
		if (warp == -1)
		{
			if (blockheight > 0)
			{
				int countBox[10] = { 2,5,8,12,13,15,15,14,11,8 };
				if (dir == 0)
				{
					for (int i = 0; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(int(movesize * i * BLOCK_SIZE), 0, 0), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * heightsize+ countBox[i]), BLOCK_SIZE));
					}
				}
				else if (dir == 1)
				{
					for (int i = 0; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(0, 0, 0), pos + WCoord(BLOCK_SIZE - int(movesize * i * BLOCK_SIZE), int(heightOffset * i * heightsize + countBox[i]), BLOCK_SIZE));
					}
				}
				else if (dir == 2)
				{
					for (int i = 0; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(0, 0, int(movesize * i * BLOCK_SIZE)), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * heightsize + countBox[i]), BLOCK_SIZE));
					}
				}
				else if (dir == 3)
				{
					for (int i = 0; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(0, 0, 0), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * heightsize + countBox[i]), BLOCK_SIZE - int(movesize * i * BLOCK_SIZE)));
					}
				}
			}
			else
			{
				int countBox[10] = { -1,2,6,7,7,7,6,2,-1,-3 };
				if (dir == 0)
				{
					for (int i = 0; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(BLOCK_SIZE - int(movesize * (i+1) * xzsize), BLOCK_HALFSIZE + int(heightOffset * i * BLOCK_HALFSIZE- countBox[i]), 0), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
					}
				}
				else if (dir == 1)
				{
					for (int i = 0; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(0, BLOCK_HALFSIZE + int(heightOffset * i * BLOCK_HALFSIZE - countBox[i]), 0), pos + WCoord(int(movesize * (i + 1) * xzsize), BLOCK_SIZE, BLOCK_SIZE));
					}
				}
				else if (dir == 2)
				{
					for (int i = 0; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(0, BLOCK_HALFSIZE + int(heightOffset * i * BLOCK_HALFSIZE - countBox[i]), BLOCK_SIZE - int(movesize * (i + 1) * xzsize)), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
					}
				}
				else if (dir == 3)
				{
					for (int i = 0; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(0, BLOCK_HALFSIZE + int(heightOffset * i * BLOCK_HALFSIZE - countBox[i]), 0), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, int(movesize * (i + 1) * xzsize)));
					}
				}
			}
		}
		else if (1 == warp)
		{
			if (blockheight > 0)
			{
				int countBox[10] = { 2,5,8,12,13,15,15,14,11,8 };
				if (dir == 0 && turnDir == 2 || dir == 2 && turnDir == 0)
				{
					for (int i = 0; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(int(movesize * i * BLOCK_SIZE), 0, int(movesize * i * BLOCK_SIZE)), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * heightsize+ countBox[i]), BLOCK_SIZE));
					}
				}
				else if (dir == 0 && turnDir == 3 || dir == 3 && turnDir == 0)
				{
					for (int i = 0; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(int(movesize * i * BLOCK_SIZE), 0, 0), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * heightsize + countBox[i]), BLOCK_SIZE - int(movesize * i * BLOCK_SIZE)));
					}
				}
				else if (dir == 1 && turnDir == 2 || dir == 2 && turnDir == 1)
				{
					for (int i = 0; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(0, 0, int(movesize * i * BLOCK_SIZE)), pos + WCoord(BLOCK_SIZE - int(movesize * i * BLOCK_SIZE), int(heightOffset * i * heightsize + countBox[i]), BLOCK_SIZE));

					}
				}
				else if (dir == 1 && turnDir == 3 || dir == 3 && turnDir == 1)
				{
					for (int i = 0; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(0, 0, 0), pos + WCoord(BLOCK_SIZE - int(movesize * i * BLOCK_SIZE), int(heightOffset * i * heightsize + countBox[i]), BLOCK_SIZE - int(movesize * i * BLOCK_SIZE)));
					}
				}
			}
			else
			{
				int countBox[10] = { -1,2,6,7,7,7,6,2,-1,-3 };
				if (dir == 0 && turnDir == 2 || dir == 2 && turnDir == 0)
				{
					for (int i = 0; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(BLOCK_SIZE - int(movesize * (i + 1) * xzsize), BLOCK_HALFSIZE + int(heightOffset * i * BLOCK_HALFSIZE- countBox[i]), BLOCK_SIZE - int(movesize * (i + 1) * xzsize)), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
					}
				}
				else if (dir == 0 && turnDir == 3 || dir == 3 && turnDir == 0)
				{
					for (int i = 0; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(BLOCK_SIZE - int(movesize * (i + 1) * xzsize), BLOCK_HALFSIZE + int(heightOffset * i * BLOCK_HALFSIZE - countBox[i]), 0), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, int(movesize * (i + 1) * xzsize)));
					}
				}
				else if (dir == 1 && turnDir == 2 || dir == 2 && turnDir == 1)
				{
					for (int i = 0; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(0, BLOCK_HALFSIZE + int(heightOffset * i * BLOCK_HALFSIZE - countBox[i]), BLOCK_SIZE - int(movesize * (i + 1) * xzsize)), pos + WCoord(int(movesize * (i + 1) * xzsize), BLOCK_SIZE, BLOCK_SIZE));
					}
				}
				else if (dir == 1 && turnDir == 3 || dir == 3 && turnDir == 1)
				{
					for (int i = 0; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(0, BLOCK_HALFSIZE + int(heightOffset * i * BLOCK_HALFSIZE - countBox[i]), 0), pos + WCoord(int(movesize * (i + 1) * xzsize), BLOCK_SIZE, int(movesize * (i + 1) * xzsize)));
					}
				}
			}
		}
		else
		{
			if (blockheight > 0)
			{
				int countBox[10] = { 2,5,8,12,13,15,15,14,11,8 };
				if (dir == 0 && turnDir == 2 || dir == 2 || turnDir == 0)
				{
					for (int i = 0; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(int(movesize * i * BLOCK_SIZE), 0, 0), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * heightsize+ countBox[i]), BLOCK_SIZE));
						coldetect->addObstacle(pos + WCoord(0, 0, int(movesize * i * BLOCK_SIZE)), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * heightsize + countBox[i]), BLOCK_SIZE));
					}
				}
				else if (dir == 0 && turnDir == 3 || dir == 3 && turnDir == 0)
				{
					for (int i = 0; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(int(movesize * i * BLOCK_SIZE), 0, 0), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * heightsize + countBox[i]), BLOCK_SIZE));
						coldetect->addObstacle(pos + WCoord(0, 0, 0), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * heightsize + countBox[i]), BLOCK_SIZE - int(movesize * i * BLOCK_SIZE)));
					}
				}
				else if (dir == 1 && turnDir == 2 || dir == 2 || turnDir == 1)
				{
					for (int i = 0; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(0, 0, 0), pos + WCoord(BLOCK_SIZE - int(movesize * i * BLOCK_SIZE), int(heightOffset * i * heightsize + countBox[i]), BLOCK_SIZE));
						coldetect->addObstacle(pos + WCoord(0, 0, int(movesize * i * BLOCK_SIZE)), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * heightsize + countBox[i]), BLOCK_SIZE));
					}
				}
				else if (dir == 1 && turnDir == 3 || dir == 3 && turnDir == 1)
				{
					for (int i = 0; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(0, 0, 0), pos + WCoord(BLOCK_SIZE - int(movesize * i * BLOCK_SIZE), int(heightOffset * i * heightsize + countBox[i]), BLOCK_SIZE));
						coldetect->addObstacle(pos + WCoord(0, 0, 0), pos + WCoord(BLOCK_SIZE, int(heightOffset * i * heightsize + countBox[i]), BLOCK_SIZE - int(movesize * i * BLOCK_SIZE)));
					}
				}

			}
			else
			{
				int countBox[10] = { -1,2,6,7,7,7,6,2,-1,-3 };
				if (dir == 0 && turnDir == 2 || dir == 2 || turnDir == 0)
				{
					for (int i = 0; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(BLOCK_SIZE - int(movesize * (i + 1) * xzsize), BLOCK_HALFSIZE + int(heightOffset * i * BLOCK_HALFSIZE- countBox[i]), 0), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
						coldetect->addObstacle(pos + WCoord(0, BLOCK_HALFSIZE + int(heightOffset * i * BLOCK_HALFSIZE - countBox[i]), BLOCK_SIZE - int(movesize * (i + 1) * xzsize)), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
					}
				}
				else if (dir == 0 && turnDir == 3 || dir == 3 && turnDir == 0)
				{
					for (int i = 0; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(BLOCK_SIZE - int(movesize * (i + 1) * xzsize), BLOCK_HALFSIZE + int(heightOffset * i * BLOCK_HALFSIZE - countBox[i]), 0), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
						coldetect->addObstacle(pos + WCoord(0, BLOCK_HALFSIZE + int(heightOffset * i * BLOCK_HALFSIZE - countBox[i]), 0), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, int(movesize * (i + 1) * xzsize)));
					}
				}
				else if (dir == 1 && turnDir == 2 || dir == 2 || turnDir == 1)
				{
					for (int i = 0; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(0, BLOCK_HALFSIZE + int(heightOffset * i * BLOCK_HALFSIZE - countBox[i]), 0), pos + WCoord(int(movesize * (i + 1) * xzsize), BLOCK_SIZE, BLOCK_SIZE));
						coldetect->addObstacle(pos + WCoord(0, BLOCK_HALFSIZE + int(heightOffset * i * BLOCK_HALFSIZE - countBox[i]), BLOCK_SIZE - int(movesize * (i + 1) * xzsize)), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
					}
				}
				else if (dir == 1 && turnDir == 3 || dir == 3 && turnDir == 1)
				{
					for (int i = 0; i < step; i++)
					{
						coldetect->addObstacle(pos + WCoord(0, BLOCK_HALFSIZE + int(heightOffset * i * BLOCK_HALFSIZE - countBox[i]), 0), pos + WCoord(int(movesize * (i + 1) * xzsize), BLOCK_SIZE, BLOCK_SIZE));
						coldetect->addObstacle(pos + WCoord(0, BLOCK_HALFSIZE + int(heightOffset * i * BLOCK_HALFSIZE - countBox[i]), 0), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, int(movesize * (i + 1) * xzsize)));
					}
				}
			}
		}
	}
//for (int i = 0; i < step; i++)
//{
//	WCoord pos1 = pos + WCoord(int(movesize * i * BLOCK_SIZE), 0, 0);
//	WCoord pos2 = pos + WCoord(BLOCK_SIZE, int(heightOffset * i * heightsize + countBox[i]), BLOCK_SIZE);
//	drawLine(Vector3f(pos1.x, pos1.y, pos1.z), Vector3f(pos2.x, pos2.y, pos2.z), this);
//	pos1 = pos + WCoord(0, 0, int(movesize * i * BLOCK_SIZE));
//	pos2 = pos + WCoord(BLOCK_SIZE, int(heightOffset * i * heightsize + countBox[i]), BLOCK_SIZE);
//	drawLine(Vector3f(pos1.x, pos1.y, pos1.z), Vector3f(pos2.x, pos2.y, pos2.z), this);
//}
//for (int i = 0; i < step; i++)
//{
//	WCoord pos1 = pos + WCoord(200, 0, 0) + WCoord(int(movesize * i * BLOCK_SIZE), 0, 0);
//	WCoord pos2 = pos + WCoord(200, 0, 0) + WCoord(BLOCK_SIZE, int(heightOffset * i * heightsize + countBox[i]), BLOCK_SIZE);
//	drawLine(Vector3f(pos1.x, pos1.y, pos1.z), Vector3f(pos2.x, pos2.y, pos2.z), this);
//	 pos1 = pos + WCoord(200, 0, 0);
//	 pos2 = pos + WCoord(200, 0, 0) + WCoord(BLOCK_SIZE, int(heightOffset * i * heightsize + countBox[i]), BLOCK_SIZE - int(movesize * i * BLOCK_SIZE));
//	drawLine(Vector3f(pos1.x, pos1.y, pos1.z), Vector3f(pos2.x, pos2.y, pos2.z), this);
//}
//for (int i = 0; i < step; i++)
//{
//	WCoord pos1 = pos + WCoord(400, 0, 0);
//	WCoord pos2 = pos + WCoord(400, 0, 0) + WCoord(BLOCK_SIZE - int(movesize * i * BLOCK_SIZE), int(heightOffset * i * heightsize + countBox[i]), BLOCK_SIZE);
//	drawLine(Vector3f(pos1.x, pos1.y, pos1.z), Vector3f(pos2.x, pos2.y, pos2.z), this);
//	 pos1 = pos + WCoord(400, 0, 0) + WCoord(0, 0, int(movesize * i * BLOCK_SIZE));
//	 pos2 = pos + WCoord(400, 0, 0) + WCoord(BLOCK_SIZE, int(heightOffset * i * heightsize + countBox[i]), BLOCK_SIZE);
//	drawLine(Vector3f(pos1.x, pos1.y, pos1.z), Vector3f(pos2.x, pos2.y, pos2.z), this);
//}
//for (int i = 0; i < step; i++)
//{
//	WCoord pos1 = pos + WCoord(600, 0, 0) ;
//	WCoord pos2 = pos + WCoord(600, 0, 0) + WCoord(BLOCK_SIZE - int(movesize * i * BLOCK_SIZE), int(heightOffset * i * heightsize + countBox[i]), BLOCK_SIZE);
//	drawLine(Vector3f(pos1.x, pos1.y, pos1.z), Vector3f(pos2.x, pos2.y, pos2.z), this);
//	 pos1 = pos + WCoord(600, 0, 0);
//	 pos2 = pos + WCoord(600, 0, 0) + WCoord(BLOCK_SIZE, int(heightOffset * i * heightsize + countBox[i]), BLOCK_SIZE - int(movesize * i * BLOCK_SIZE));
//	drawLine(Vector3f(pos1.x, pos1.y, pos1.z), Vector3f(pos2.x, pos2.y, pos2.z), this);
//}
//for (int i = 0; i < step; i++)
//{
//	WCoord pos1 = pos + WCoord(BLOCK_SIZE - int(movesize * (i+1) * xzsize), BLOCK_HALFSIZE + int(heightOffset * i * BLOCK_HALFSIZE - countBox[i]), BLOCK_SIZE - int(movesize * (i + 1) * xzsize));
//	WCoord pos2 = pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE);
//	drawLine(Vector3f(pos1.x, pos1.y, pos1.z), Vector3f(pos2.x, pos2.y, pos2.z), this);
//}
//for (int i = 0; i < step; i++)
//{
//	WCoord pos1 = pos + WCoord(200, 0, 0) + WCoord(BLOCK_SIZE - int(movesize * (i + 1) * xzsize), BLOCK_HALFSIZE + int(heightOffset * i * BLOCK_HALFSIZE - countBox[i]), 0);
//	WCoord pos2 = pos + WCoord(200, 0, 0) + WCoord(BLOCK_SIZE, BLOCK_SIZE, int(movesize * (i + 1) * xzsize));
//	drawLine(Vector3f(pos1.x, pos1.y, pos1.z), Vector3f(pos2.x, pos2.y, pos2.z), this);
//}
//for (int i = 0; i < step; i++)
//{
//	WCoord pos1 = pos + WCoord(400, 0, 0) + WCoord(0, BLOCK_HALFSIZE + int(heightOffset * i * BLOCK_HALFSIZE - countBox[i]), BLOCK_SIZE - int(movesize * (i + 1) * xzsize));
//	WCoord pos2 = pos + WCoord(400, 0, 0) + WCoord(int(movesize * (i + 1) * xzsize), BLOCK_SIZE, BLOCK_SIZE);
//	drawLine(Vector3f(pos1.x, pos1.y, pos1.z), Vector3f(pos2.x, pos2.y, pos2.z), this);
//}
//for (int i = 0; i < step; i++)
//{
//	WCoord pos1 = pos + WCoord(600, 0, 0) + WCoord(0, BLOCK_HALFSIZE + int(heightOffset * i * BLOCK_HALFSIZE - countBox[i]), 0);
//	WCoord pos2 = pos + WCoord(600, 0, 0) + WCoord(int(movesize * (i + 1) * xzsize), BLOCK_SIZE, int(movesize * (i + 1) * xzsize));
//	drawLine(Vector3f(pos1.x, pos1.y, pos1.z), Vector3f(pos2.x, pos2.y, pos2.z), this);
//}

//for (int i = 0; i < step; i++)
//{
//	WCoord pos1 = pos + WCoord(int(movesize * i * BLOCK_SIZE), 0, int(movesize * i * BLOCK_SIZE));
//	WCoord pos2 = pos + WCoord(BLOCK_SIZE, int(heightOffset * i * heightsize + countBox[i]), BLOCK_SIZE);
//	drawLine(Vector3f(pos1.x, pos1.y, pos1.z), Vector3f(pos2.x, pos2.y, pos2.z), this);
//}
//for (int i = 0; i < step; i++)
//{
//	WCoord pos1 = pos + WCoord(200, 0, 0) + WCoord(int(movesize * i * BLOCK_SIZE), 0, 0);
//	WCoord pos2 = pos + WCoord(200, 0, 0) + WCoord(BLOCK_SIZE, int(heightOffset * i * heightsize + countBox[i]), BLOCK_SIZE - int(movesize * i * BLOCK_SIZE));
//	drawLine(Vector3f(pos1.x, pos1.y, pos1.z), Vector3f(pos2.x, pos2.y, pos2.z), this);
//}
//for (int i = 0; i < step; i++)
//{
//	WCoord pos1 = pos + WCoord(400, 0, 0) + WCoord(0, 0, int(movesize * i * BLOCK_SIZE));
//	WCoord pos2 = pos + WCoord(400, 0, 0) + WCoord(BLOCK_SIZE - int(movesize * i * BLOCK_SIZE), int(heightOffset * i * heightsize + countBox[i]), BLOCK_SIZE);
//	drawLine(Vector3f(pos1.x, pos1.y, pos1.z), Vector3f(pos2.x, pos2.y, pos2.z), this);
//}
//for (int i = 0; i < step; i++)
//{
//	WCoord pos1 = pos + WCoord(600, 0, 0);
//	WCoord pos2 = pos + WCoord(600, 0, 0) + WCoord(BLOCK_SIZE - int(movesize * i * BLOCK_SIZE), int(heightOffset * i * heightsize + countBox[i]), BLOCK_SIZE - int(movesize * i * BLOCK_SIZE));
//	drawLine(Vector3f(pos1.x, pos1.y, pos1.z), Vector3f(pos2.x, pos2.y, pos2.z), this);
//}
//for (int i = 0; i < step; i++)
//{
//	WCoord pos1 = pos + WCoord(BLOCK_SIZE - int(movesize * (i+1) * xzsize), BLOCK_HALFSIZE + int(heightOffset * i * BLOCK_HALFSIZE - countBox[i]), 0);
//	WCoord pos2 = pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE);
//	drawLine(Vector3f(pos1.x, pos1.y, pos1.z), Vector3f(pos2.x, pos2.y, pos2.z), this);
//}
//for (int i = 0; i < step; i++)
//{
//	WCoord pos1 = pos + WCoord(200, 0, 0) + WCoord(0, BLOCK_HALFSIZE + int(heightOffset * i * BLOCK_HALFSIZE - countBox[i]), 0);
//	WCoord pos2 = pos + WCoord(200, 0, 0) + WCoord(int(movesize * (i + 1) * xzsize), BLOCK_SIZE, BLOCK_SIZE);
//	drawLine(Vector3f(pos1.x, pos1.y, pos1.z), Vector3f(pos2.x, pos2.y, pos2.z), this);
//}
//for (int i = 0; i < step; i++)
//{
//	WCoord pos1 = pos + WCoord(400, 0, 0) + WCoord(0, BLOCK_HALFSIZE + int(heightOffset * i * BLOCK_HALFSIZE - countBox[i]), BLOCK_SIZE - int(movesize * (i + 1) * xzsize));
//	WCoord pos2 = pos + WCoord(400, 0, 0) + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE);
//	drawLine(Vector3f(pos1.x, pos1.y, pos1.z), Vector3f(pos2.x, pos2.y, pos2.z), this);
//}
//for (int i = 0; i < step; i++)
//{
//	WCoord pos1 = pos + WCoord(600, 0, 0) + WCoord(0, BLOCK_HALFSIZE + int(heightOffset * i * BLOCK_HALFSIZE - countBox[i]), 0);
//	WCoord pos2 = pos + WCoord(600, 0, 0) + WCoord(BLOCK_SIZE, BLOCK_SIZE, int(movesize * (i + 1) * xzsize));
//	drawLine(Vector3f(pos1.x, pos1.y, pos1.z), Vector3f(pos2.x, pos2.y, pos2.z), this);
//}

//for (int i = 0; i < step; i++)
//{
//	WCoord pos1 = pos + WCoord(int(movesize * i * BLOCK_SIZE), 0, 0);
//	WCoord pos2 = pos + WCoord(BLOCK_SIZE, int(heightOffset * i * heightsize + countBox[i]), BLOCK_SIZE);
//	drawLine(Vector3f(pos1.x, pos1.y, pos1.z), Vector3f(pos2.x, pos2.y, pos2.z), this);
//}
//for (int i = 0; i < step; i++)
//{
//	WCoord pos1 = pos + WCoord(200,0,0);
//	WCoord pos2 = pos + WCoord(200, 0, 0) + WCoord(BLOCK_SIZE - int(movesize * i * BLOCK_SIZE), int(heightOffset * i * heightsize + countBox[i]), BLOCK_SIZE);
//	drawLine(Vector3f(pos1.x, pos1.y, pos1.z), Vector3f(pos2.x, pos2.y, pos2.z), this);
//}
//for (int i = 0; i < step; i++)
//{
//	WCoord pos1 = pos + WCoord(400, 0, 0) + WCoord(0, 0, int(movesize * i * BLOCK_SIZE));
//	WCoord pos2 = pos + WCoord(400, 0, 0) + WCoord(BLOCK_SIZE, int(heightOffset * i * heightsize + countBox[i]), BLOCK_SIZE);
//	drawLine(Vector3f(pos1.x, pos1.y, pos1.z), Vector3f(pos2.x, pos2.y, pos2.z), this);
//}
//for (int i = 0; i < step; i++)
//{
//	WCoord pos1 = pos + WCoord(600, 0, 0);
//	WCoord pos2 = pos + WCoord(600, 0, 0) + WCoord(BLOCK_SIZE, int(heightOffset * i * heightsize + countBox[i]), BLOCK_SIZE - int(movesize * i * BLOCK_SIZE));
//	drawLine(Vector3f(pos1.x, pos1.y, pos1.z), Vector3f(pos2.x, pos2.y, pos2.z), this);
//}
}

bool HorizontalArcHalfPlateMaterial::coverNeighbor(int curblockdata, SolidBlockMaterial* neighbor, int neighbor_data, DirectionType dir)
{
	int updown = (curblockdata & 4) >> 2;
	if ((updown + 4) == dir)
	{
		if (dir == DIR_NEG_Y)
		{
			if (neighbor && neighbor->GetAttrRenderType() == BLOCKRENDER_FLUID && neighbor_data) return false;
		}
		return true;
	}
	return false;
}

void HorizontalArcHalfPlateMaterial::dropBlockAsItem(World* pworld, const WCoord& blockpos, int blockdata, BLOCK_MINE_TYPE droptype, float chance, int uin/* = -1 */)
{
	CubeBlockMaterial::dropBlockAsItem(pworld, blockpos, blockdata, droptype, chance, uin);
	if (blockdata & 8)
	{
		CubeBlockMaterial::dropBlockAsItem(pworld, blockpos, blockdata, droptype, chance, uin);
	}
}

int HorizontalArcHalfPlateMaterial::getPhisicMeshVerts(BaseSection* psection, const WCoord& blockpos, dynamic_array<Rainbow::Vector3f>& verts, dynamic_array<UInt16>& idxs)
{
#ifdef IWORLD_SERVER_BUILD
	if (!m_mPhyModel.size())
	{
		initVertData();
	}
#endif	
	int blockdata = psection->getBlock(blockpos).getData();
	if (blockdata < 8)
	{
		auto pblock = psection->getBlock(blockpos);
		int warp = -1;
		int turnDir = -1;
		DirectionType curDir = DirectionType(blockdata & 3);
		auto frontBlock = psection->getNeighborBlock(blockpos, g_DirectionCoord[curDir]);
		if (pblock.getResID() == frontBlock.getResID() && frontBlock.getData() < 8 && ((pblock.getData() & 4) == (frontBlock.getData() & 4)))
		{
			int frontDir = frontBlock.getData() & 3;
			if (frontDir != curDir && frontDir != ReverseDirection(curDir))
			{
				warp = 0;
				turnDir = frontDir;
			}
		}
		if (warp == -1)
		{
			auto backBlock = psection->getNeighborBlock(blockpos, g_DirectionCoord[ReverseDirection(curDir)]);
			if (pblock.getResID() == backBlock.getResID() && backBlock.getData() < 8 && ((pblock.getData() & 4) == (backBlock.getData() & 4)))
			{
				int backDir = backBlock.getData() & 3;
				if (backDir != curDir && backDir != ReverseDirection(curDir))
				{
					warp = 1;
					turnDir = backDir;
				}
			}
		}
		if (warp == -1)
		{
			if (m_mPhyModel.find(blockdata) != m_mPhyModel.end())
			{
				ArcPhyModel* pTag = &m_mPhyModel[blockdata];
				verts = pTag->verts;
				idxs = pTag->idxs;
				return  pTag->ArcCount;
			}
		}
		else
		{
			int downUp = (blockdata & 4) ? 1 : 0;
			if (m_mPhyModel.find(10000 + downUp * 1000 + warp * 100 + curDir * 10 + turnDir) != m_mPhyModel.end())
			{
				ArcPhyModel* pTag = &m_mPhyModel[10000 + downUp * 1000 + warp * 100 + curDir * 10 + turnDir];
				verts = pTag->verts;
				idxs = pTag->idxs;
				return  pTag->ArcCount;
			}
		}
	}
	else
	{
		ArcPhyModel* pTag = &m_mPhyModel[20000 + (blockdata & 4)];
		verts = pTag->verts;
		idxs = pTag->idxs;
		return  pTag->ArcCount;
	}
	return 0;
}

BLOCK_RENDERTYPE_T HorizontalArcHalfPlateMaterial::GetAttrRenderType() const
{
	return BLOCKRENDER_MODEL;
}

void HorizontalArcHalfPlateMaterial::onBlockPlacedBy(World* pworld, const WCoord& blockpos, IClientPlayer* player)
{
	ClientPlayer* playerTmp = player->GetPlayer();
	if (!playerTmp) return;
	int dir = playerTmp->getCurPlaceDir();
	float x, y, z;
	playerTmp->getFaceDir(x, y, z);
	int data = dir;
	if (y > 0)
	{
		data += 4;
	}
	pworld->setBlockData(blockpos, data);
}

void HorizontalArcHalfPlateMaterial::createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh)
{
	auto psection = data.m_SharedSectionData;
#ifndef IWORLD_SERVER_BUILD
	FaceVertexLight faceVertexLight;
	Block pblock = psection->getBlock(blockpos);

	int curblockdata = pblock.getData();
	int curDir = curblockdata & 3;

	float blockheight = getBlockHeight(curblockdata);
	DirectionType specialdir = DIR_NOT_INIT;

	if (blockheight > 0 && blockheight < 1.0f) specialdir = DIR_POS_Y;
	else if (blockheight<0 && blockheight>-1.0f) specialdir = DIR_NEG_Y;

	const BiomeDef* biome = GetDefManagerProxy()->getBiomeDef(1);
	std::vector<int> wholeFace;
	std::vector<int> halfWholeFace;
	std::vector<int> halfFace;
	std::vector<int> triangleFace;
	std::vector<int> slantFace;
	dynamic_array<int> turnslantFace;
	if (curblockdata & 8)
	{
		CubeBlockMaterial::createBlockMesh(data, blockpos, poutmesh);
		return;
	}
	else
	{
		int warp = -1;
		int turnDir = -1;
		auto frontBlock = psection->getNeighborBlock(blockpos, g_DirectionCoord[curDir]);
		if (pblock.getResID() == frontBlock.getResID() && frontBlock.getData() < 8 && ((pblock.getData() & 4) == (frontBlock.getData() & 4)))
		{
			int frontDir = frontBlock.getData() & 3;
			if (frontDir != curDir && frontDir != ReverseDirection(curDir))
			{
				warp = 0;
				turnDir = frontDir;
			}
		}

		if (warp == -1)
		{
			auto backBlock = psection->getNeighborBlock(blockpos, g_DirectionCoord[ReverseDirection(curDir)]);
			if (pblock.getResID() == backBlock.getResID() && backBlock.getData() < 8 && ((pblock.getData() & 4) == (backBlock.getData() & 4)))
			{
				int backDir = backBlock.getData() & 3;
				if (backDir != curDir && backDir != ReverseDirection(curDir))
				{
					warp = 1;
					turnDir = backDir;
				}
			}
		}

		if (specialdir == DIR_POS_Y)
		{
			wholeFace.push_back(4);
			if (warp == -1)
			{
				halfFace.push_back(ReverseDirection(curDir));
			}
		}
		else
		{
			wholeFace.push_back(5);
			if (warp == -1)
			{
				halfFace.push_back(ReverseDirection(curDir) + 4);
			}
		}

		if (curDir == DIR_NEG_X)
		{
			if (specialdir == DIR_POS_Y)
			{
				if (warp == -1)
				{
					triangleFace.push_back(6);
					triangleFace.push_back(7);
					slantFace.push_back(0);
				}
				else
				{
					if (1 == warp)
					{
						triangleFace.push_back(ReverseDirection(turnDir) + 4);//4+3
						triangleFace.push_back(ReverseDirection(curDir) + (1 - (turnDir % 2)) * 4);
					}
					else
					{
						triangleFace.push_back(turnDir + 4);//4+3       6  7
						triangleFace.push_back(((turnDir - 1) % 2) * 4);    //4  0
						halfFace.push_back(ReverseDirection(curDir));
						halfFace.push_back(ReverseDirection(turnDir));
					}
					turnslantFace.push_back(warp * 100 + curDir * 10 + turnDir);
					turnslantFace.push_back(warp * 100 + turnDir * 10 + curDir);
				}
			}
			else
			{
				if (warp == -1)
				{
					triangleFace.push_back(14);//3*4+2
					triangleFace.push_back(15);//3*4+3
					slantFace.push_back(4);
				}
				else
				{
					if (1 == warp)
					{
						triangleFace.push_back(ReverseDirection(turnDir) + 12);//3*4+3
						triangleFace.push_back(ReverseDirection(curDir) + (1 - (turnDir % 2)) * 4 + 8);
					}
					else
					{
						triangleFace.push_back(turnDir + 12);//3*4+3 14  15
						triangleFace.push_back(curDir + (1 - (turnDir % 2)) * 4 + 8);  // 12   8
						halfFace.push_back(ReverseDirection(curDir) + 4);
						halfFace.push_back(ReverseDirection(turnDir) + 4);
					}
					turnslantFace.push_back(1000 + warp * 100 + curDir * 10 + turnDir);
					turnslantFace.push_back(1000 + warp * 100 + turnDir * 10 + curDir);
				}
			}
		}
		else if (curDir == DIR_POS_X)
		{
			if (specialdir == DIR_POS_Y)
			{
				if (warp == -1)
				{
					triangleFace.push_back(2);
					triangleFace.push_back(3);
					slantFace.push_back(1);
				}
				else
				{
					if (1 == warp)
					{
						triangleFace.push_back(ReverseDirection(turnDir));
						triangleFace.push_back(ReverseDirection(curDir) + (1 - (turnDir % 2)) * 4);
					}
					else
					{
						triangleFace.push_back(turnDir); //2 3
						triangleFace.push_back(curDir + ((turnDir + 1) % 2) * 4); //5 1
						halfFace.push_back(ReverseDirection(curDir));
						halfFace.push_back(ReverseDirection(turnDir));
					}
					turnslantFace.push_back(warp * 100 + curDir * 10 + turnDir);
					turnslantFace.push_back(warp * 100 + turnDir * 10 + curDir);
				}
			}
			else
			{
				if (warp == -1)
				{
					triangleFace.push_back(10);//2*4+2
					triangleFace.push_back(11);//2*4+3
					slantFace.push_back(5);
				}
				else
				{
					if (1 == warp)
					{
						triangleFace.push_back(ReverseDirection(turnDir) + 8);//2*4+3
						triangleFace.push_back(ReverseDirection(curDir) + (1 - (turnDir % 2)) * 4 + 8);
					}
					else
					{
						triangleFace.push_back(turnDir + 8);//2*4+3  10 11
						triangleFace.push_back(curDir + (1 - (turnDir % 2)) * 4 + 8); //13  9
						halfFace.push_back(ReverseDirection(curDir) + 4);
						halfFace.push_back(ReverseDirection(turnDir) + 4);
					}
					turnslantFace.push_back(1000 + warp * 100 + curDir * 10 + turnDir);
					turnslantFace.push_back(1000 + warp * 100 + turnDir * 10 + curDir);
				}
			}
		}
		else if (curDir == DIR_NEG_Z)
		{
			if (specialdir == DIR_POS_Y)
			{
				if (warp == -1)
				{
					triangleFace.push_back(4);
					triangleFace.push_back(5);
					slantFace.push_back(2);
				}
				else
				{
					if (1 == warp)
					{
						triangleFace.push_back(ReverseDirection(turnDir) + 4);
						triangleFace.push_back(ReverseDirection(curDir) + (1 - (turnDir % 2)) * 4);
					}
					else
					{
						triangleFace.push_back(turnDir + 4);  //4 5
						triangleFace.push_back(curDir + ((turnDir + 1) % 2) * 4);  //6 2
						halfFace.push_back(ReverseDirection(curDir));
						halfFace.push_back(ReverseDirection(turnDir));
					}
					turnslantFace.push_back(warp * 100 + curDir * 10 + turnDir);
					turnslantFace.push_back(warp * 100 + turnDir * 10 + curDir);
				}
			}
			else
			{
				if (warp == -1)
				{
					triangleFace.push_back(12);//3*4+0
					triangleFace.push_back(13);//3*4+1
					slantFace.push_back(6);
				}
				else
				{
					if (1 == warp)
					{
						triangleFace.push_back(ReverseDirection(turnDir) + 12);//3*4+1
						triangleFace.push_back(ReverseDirection(curDir) + (1 - (turnDir % 2)) * 4 + 8);
					}
					else
					{
						triangleFace.push_back(turnDir + 12);//3*4+1
						halfFace.push_back(ReverseDirection(curDir) + 4);
						halfFace.push_back(ReverseDirection(turnDir) + 4);
					}
					turnslantFace.push_back(1000 + warp * 100 + curDir * 10 + turnDir);
					turnslantFace.push_back(1000 + warp * 100 + turnDir * 10 + curDir);
				}
			}
		}
		else if (curDir == DIR_POS_Z)
		{
			if (specialdir == DIR_POS_Y)
			{
				if (warp == -1)
				{
					triangleFace.push_back(0);
					triangleFace.push_back(1);
					slantFace.push_back(3);
				}
				else
				{
					if (1 == warp)
					{
						triangleFace.push_back(ReverseDirection(turnDir));
						triangleFace.push_back(ReverseDirection(curDir) + (1 - (turnDir % 2)) * 4);
					}
					else
					{
						triangleFace.push_back(turnDir); // 1 0
						triangleFace.push_back(curDir + ((turnDir + 1) % 2) * 4);  // 3 7
						halfFace.push_back(ReverseDirection(curDir));
						halfFace.push_back(ReverseDirection(turnDir));
					}
					turnslantFace.push_back(warp * 100 + curDir * 10 + turnDir);
					turnslantFace.push_back(warp * 100 + turnDir * 10 + curDir);
				}
			}
			else
			{
				if (warp == -1)
				{
					triangleFace.push_back(8);//2*4+0
					triangleFace.push_back(9);//2*4+1
					slantFace.push_back(7);
				}
				else
				{
					if (1 == warp)
					{
						triangleFace.push_back(ReverseDirection(turnDir) + 8);//2*4+1
						triangleFace.push_back(ReverseDirection(curDir) + (1 - (turnDir % 2)) * 4 + 8);
					}
					else
					{
						triangleFace.push_back(turnDir + 8);//2*4+1
						halfFace.push_back(ReverseDirection(curDir) + 4);
						halfFace.push_back(ReverseDirection(turnDir) + 4);
					}
					turnslantFace.push_back(1000 + warp * 100 + curDir * 10 + turnDir);
					turnslantFace.push_back(1000 + warp * 100 + turnDir * 10 + curDir);
				}
			}
		}
	}
	BlockColor facecolor(255, 255, 255, 0);
	for (auto& d : wholeFace)
	{
		DirectionType dir = (DirectionType)d;
		// 		if (m_DisableCoverFaceOpt || dir == specialdir || !psection->getNeighborCover(blockpos, this, curblockdata, dir))
		{
			bool flipQuad = psection->getCubeFaceVertexLight(blockpos, dir, faceVertexLight);

			dynamic_array<UInt16>* indices = /*flipQuad ? &m_dNegIndices : */m_dPosIndices;

			RenderBlockMaterial* pmtl = getFaceMtl(biome, dir, pblock.getData(), facecolor);
			if (pmtl == NULL)
				continue;
			SectionSubMesh* psubmesh = poutmesh->getSubMesh(pmtl);
			const float* uvtile = nullptr;
			if (psubmesh && !psubmesh->IsIgnoreTileUV())
				uvtile = pmtl->getUVTile();
			BlockGeomMeshInfo mesh;

			mesh.vertices = m_mArcWholeFace()[d];
			mesh.indices = *indices;


			//unsigned short dir_color = TriangleNormal2LightColor(g_DirectionCoord[dir].toVector3());
			unsigned int avelt[4];
			getAvelt(data, blockpos, dir, avelt);
			for (int n = 0; n < mesh.vertices.size(); n++)
			{
				auto& vert = mesh.vertices[n];
				InitBlockVertLight(vert, avelt[n], uvtile);
			}
			if (psubmesh) //psubmesh->addGeomFace(mesh, &blockpos);
				psubmesh->addGeomFaceLight(mesh, &blockpos, faceVertexLight, &facecolor, pmtl->getUVTile());
		}
	}
	for (auto& d : halfWholeFace)
	{
		DirectionType dir = (DirectionType)(d + 4);
		// 		if (m_DisableCoverFaceOpt || dir == specialdir || !psection->getNeighborCover(blockpos, this, curblockdata, dir))
		{
			bool flipQuad = psection->getCubeFaceVertexLight(blockpos, dir, faceVertexLight);

			dynamic_array<UInt16>* indices = m_dPosIndices;

			RenderBlockMaterial* pmtl = getFaceMtl(biome, dir, pblock.getData(), facecolor);
			if (pmtl == NULL)
				continue;
			SectionSubMesh* psubmesh = poutmesh->getSubMesh(pmtl);
			const float* uvtile = nullptr;
			if (psubmesh && !psubmesh->IsIgnoreTileUV())
				uvtile = pmtl->getUVTile();
			BlockGeomMeshInfo mesh;

			mesh.vertices = m_mHalfWholeFace()[d];
			mesh.indices = *indices;
			unsigned int avelt[4];
			getAvelt(data, blockpos, dir, avelt);
			for (int n = 0; n < mesh.vertices.size(); n++)
			{
				auto& vert = mesh.vertices[n];
				InitBlockVertLight(vert, avelt[n], uvtile);
			}
			if (psubmesh) //psubmesh->addGeomFace(mesh, &blockpos);// 
				psubmesh->addGeomFaceLight(mesh, &blockpos, faceVertexLight, &facecolor, pmtl->getUVTile());
		}
	}
	for (auto& d : halfFace)
	{
		DirectionType dir = (DirectionType)(d % 4);
		// 		if (m_DisableCoverFaceOpt || dir == specialdir || !psection->getNeighborCover(blockpos, this, curblockdata, dir))
		{
			bool flipQuad = psection->getCubeFaceVertexLight(blockpos, dir, faceVertexLight);
			dynamic_array<UInt16>* indices = m_dPosIndices;

			RenderBlockMaterial* pmtl = getFaceMtl(biome, dir, pblock.getData(), facecolor);
			if (pmtl == NULL)
				continue;
			SectionSubMesh* psubmesh = poutmesh->getSubMesh(pmtl);
			const float* uvtile = nullptr;
			if (psubmesh && !psubmesh->IsIgnoreTileUV())
				uvtile = pmtl->getUVTile();
			BlockGeomMeshInfo mesh;

			mesh.vertices = m_mHalfFace()[d];
			mesh.indices = *indices;
			unsigned int avelt[4];
			getAvelt(data, blockpos, dir, avelt);
			for (int n = 0; n < mesh.vertices.size(); n++)
			{
				auto& vert = mesh.vertices[n];
				InitBlockVertLight(vert, avelt[n], uvtile);
			}
			if (psubmesh) //psubmesh->addGeomFace(mesh, &blockpos); 
				psubmesh->addGeomFaceLight(mesh, &blockpos, faceVertexLight, &facecolor, pmtl->getUVTile());
		}
	}
	for (auto& d : triangleFace)
	{
		DirectionType dir = (DirectionType)(d % 4);
		bool flipQuad = psection->getCubeFaceVertexLight(blockpos, dir, faceVertexLight);
		dynamic_array<UInt16>* indices = m_PosDiamondIndices;

		RenderBlockMaterial* pmtl = getFaceMtl(biome, dir, pblock.getData(), facecolor);
		if (pmtl == NULL)
			continue;
		SectionSubMesh* psubmesh = poutmesh->getSubMesh(pmtl);
		const float* uvtile = nullptr;
		if (psubmesh && !psubmesh->IsIgnoreTileUV())
			uvtile = pmtl->getUVTile();
		BlockGeomMeshInfo mesh;
		mesh.vertices = m_mDiamondFace[d];
		
		mesh.indices = *indices;
		for (int n = 0; n < mesh.vertices.size(); n++)
		{
			auto& vert = mesh.vertices[n];
			int aveltMe = 0;
			int aveltNeight[3] = { 0 };
			int avelt = 0;
			if (vert.pos.y > 0 && specialdir == DIR_POS_Y || vert.pos.y == 0 && specialdir == DIR_NEG_Y)
			{
				auto selfPos = blockpos + g_DirectionCoord[specialdir] + g_DirectionCoord[dir];
				bool isXdir = dir < 2;
				int xdir = isXdir ? dir : vert.pos.x == 0 ? 0 : 1;
				int zdir = !isXdir ? dir : vert.pos.z == 0 ? 2 : 3;
				aveltMe = psection->getLight2(selfPos, true);
				aveltNeight[0] = psection->getLight2(selfPos + g_DirectionCoord[xdir], true);
				aveltNeight[1] = psection->getLight2(selfPos + g_DirectionCoord[zdir], true);
				aveltNeight[2] = psection->getLight2(selfPos + g_DirectionCoord[xdir] + g_DirectionCoord[zdir], true);
				avelt = ((aveltMe + aveltNeight[0] + aveltNeight[1] + aveltNeight[2]) >> 2) & 0xff00ff;
			}
			else
			{
				auto selfPos = blockpos + g_DirectionCoord[dir];
				int sideDir = specialdir == DIR_NEG_Y ? DIR_POS_Y : DIR_NEG_Y;
				aveltMe = psection->getLight2(selfPos, true);
				aveltNeight[0] = psection->getLight2(selfPos + g_DirectionCoord[sideDir], true);
				avelt = ((aveltMe + aveltNeight[0]) >> 1) & 0xff00ff;
			}
			InitBlockVertLight(vert, avelt, uvtile);
		}
		if (psubmesh) psubmesh->addGeomFace(mesh, &blockpos);// 
			//psubmesh->addGeomFaceLight(mesh, &blockpos, faceVertexLight, &facecolor, pmtl->getUVTile());
	}
	for (auto& d : turnslantFace)
	{
		DirectionType dir = (DirectionType)((d / 10) % 10);
		bool flipQuad = psection->getFaceVertexLight(NeighborCoord(blockpos, specialdir), dir, faceVertexLight);
		FaceVertexLight faceUpDownVertexLight;
		psection->getFaceVertexLight(blockpos, specialdir, faceUpDownVertexLight);
		/*for (int i = 0; i < 4; i++)
		{
			if (specialdir == DIR_NEG_Y)
			{
				FaceVertexLight faceUpVertexLight;
				psection->getFaceVertexLight(blockpos, DIR_POS_Y, faceUpVertexLight);
				faceVertexLight.m_Light[i] = (3 * faceVertexLight.m_Light[i] + (faceUpDownVertexLight.m_Light[i]+ 2*faceUpVertexLight.m_Light[i])) / 6;
				faceVertexLight.m_AmbientOcclusion[i] = (3 * faceVertexLight.m_AmbientOcclusion[i] + faceUpDownVertexLight.m_AmbientOcclusion[i]) / 4;
			}
			else
			{
				faceVertexLight.m_Light[i] = (faceVertexLight.m_Light[i] + (2 * faceUpDownVertexLight.m_Light[i])) / 3;
				faceVertexLight.m_AmbientOcclusion[i] = (faceVertexLight.m_AmbientOcclusion[i] + 2 * faceUpDownVertexLight.m_AmbientOcclusion[i]) / 3;
			}
		}*/
		dynamic_array<UInt16>* indices = m_TurnArcIndices;
		RenderBlockMaterial* pmtl = getFaceMtl(biome, dir, pblock.getData(), facecolor);
		if (pmtl == NULL)
			continue;
		SectionSubMesh* psubmesh = poutmesh->getSubMesh(pmtl);
		const float* uvtile = nullptr;
		if (psubmesh && !psubmesh->IsIgnoreTileUV())
			uvtile = pmtl->getUVTile();
		BlockGeomMeshInfo mesh;

		mesh.vertices = m_mTurnArcFace[d];
		mesh.indices = *indices;
		//unsigned short dir_color1 = TriangleNormal2LightColor(g_DirectionCoord[dir].toVector3());
		//unsigned short dir_color2 = TriangleNormal2LightColor(g_DirectionCoord[specialdir].toVector3());
		BlockColor vertcolor = Rainbow::ColorRGBA32::white;// r g b a
		//vertcolor.v = 0xffffffff;
		vertcolor.a = 0;// (dir_color1 + 2 * dir_color2) / 3;
		//if (specialdir == DIR_NEG_Y)
		//{
		//	unsigned short dir_color3 = TriangleNormal2LightColor(g_DirectionCoord[DIR_POS_Y].toVector3());
		//	vertcolor.a = (3 * dir_color1 + dir_color2+2* dir_color3) / 6;
		//}
		for (int n = 0; n < m_mTurnArcFace[d].size(); n++)
		{
			auto& vert = mesh.vertices[n];
			int aveltMe = 0;
			int aveltNeight[3] = { 0 };
			int avelt = 0;
			if (vert.pos.y > 0 && specialdir == DIR_POS_Y || vert.pos.y == 0 && specialdir == DIR_NEG_Y)
			{
				auto selfPos = blockpos + g_DirectionCoord[specialdir];
				bool isXdir = dir < 2;
				int xdir = isXdir ? dir : vert.pos.x == 0 ? 0 : 1;
				int zdir = !isXdir ? dir : vert.pos.z == 0 ? 2 : 3;
				aveltMe = psection->getLight2(selfPos, true);
				aveltNeight[0] = psection->getLight2(selfPos + g_DirectionCoord[xdir], true);
				aveltNeight[1] = psection->getLight2(selfPos + g_DirectionCoord[zdir], true);
				aveltNeight[2] = psection->getLight2(selfPos + g_DirectionCoord[xdir] + g_DirectionCoord[zdir], true);
				avelt = ((aveltMe + aveltNeight[0] + aveltNeight[1] + aveltNeight[2]) >> 2) & 0xff00ff;
			}
			else
			{
				auto selfPos = blockpos + g_DirectionCoord[dir];
				int xdir = vert.pos.x == 0 ? 0 : 1;
				int zdir = vert.pos.z == 0 ? 2 : 3;
				int sideDir = dir > 1 ? xdir : zdir;
				aveltMe = psection->getLight2(selfPos, true);
				aveltNeight[0] = psection->getLight2(selfPos + g_DirectionCoord[sideDir], true);
				avelt = ((aveltMe + aveltNeight[0]) >> 1) & 0xff00ff;
			}
			//int lt1 = (((avelt >> 4) & 0xf) * vertcolor.a) >> 5;
			//int lt2 = (((avelt >> 20) & 0xf) * vertcolor.a) >> 5;
			//vert.pos.w = (lt1 << 8) | lt2;
			//vert.color = vertcolor;
			vert.color = vertcolor;
			InitBlockVertLight(vert, avelt, uvtile);
		}
		if (psubmesh) psubmesh->addGeomFace(mesh, &blockpos);// addGeomFaceLight(mesh, &blockpos, faceVertexLight, &facecolor, pmtl->getUVTile());
	}
	for (auto& d : slantFace)
	{
		DirectionType dir = (DirectionType)curDir;
		bool flipQuad = psection->getFaceVertexLight(blockpos, dir, faceVertexLight);
		FaceVertexLight faceUpDownVertexLight;
		psection->getFaceVertexLight(blockpos, specialdir, faceUpDownVertexLight);
		for (int i = 0; i < 4; i++)
		{
			if (specialdir == DIR_NEG_Y)
			{
				faceVertexLight.m_Light[i] = (3 * faceVertexLight.m_Light[i] + (faceUpDownVertexLight.m_Light[i])) / 4;
				faceVertexLight.m_AmbientOcclusion[i] = (3 * faceVertexLight.m_AmbientOcclusion[i] + faceUpDownVertexLight.m_AmbientOcclusion[i]) / 4;
			}
			else
			{
				faceVertexLight.m_Light[i] = (faceVertexLight.m_Light[i] + (2 * faceUpDownVertexLight.m_Light[i])) / 3;
				faceVertexLight.m_AmbientOcclusion[i] = (faceVertexLight.m_AmbientOcclusion[i] + 2 * faceUpDownVertexLight.m_AmbientOcclusion[i]) / 3;
			}
		}

		dynamic_array<UInt16>* indices = m_PosBigArcIndices;

		RenderBlockMaterial* pmtl = getFaceMtl(biome, dir, pblock.getData(), facecolor);
		if (pmtl == NULL)
			continue;
		SectionSubMesh* psubmesh = poutmesh->getSubMesh(pmtl);
		const float* uvtile = nullptr;
		if (psubmesh && !psubmesh->IsIgnoreTileUV())
			uvtile = pmtl->getUVTile();
		BlockGeomMeshInfo mesh;

		mesh.vertices = m_mBigArcFace[d];
		mesh.indices = *indices;
		// unsigned short dir_color1 = TriangleNormal2LightColor(g_DirectionCoord[dir].toVector3());
		// unsigned short dir_color2 = TriangleNormal2LightColor(g_DirectionCoord[specialdir].toVector3());
		BlockColor vertcolor = Rainbow::ColorRGBA32::white;
		//vertcolor.v = 0xffffffff;
		vertcolor.a = 0;//( dir_color1 + 2*dir_color2) / 3;
		// if (specialdir == DIR_NEG_Y)
		// {
		// 	vertcolor.a = (3 * dir_color1 + dir_color2) / 4;
		// }
		for (int n = 0; n < m_mBigArcFace[d].size(); n++)
		{
			auto& vert = mesh.vertices[n];
			int aveltMe = 0;
			int aveltNeight[3] = { 0 };
			int avelt = 0;
			if (vert.pos.y > 0 && specialdir == DIR_POS_Y || vert.pos.y == 0 && specialdir == DIR_NEG_Y)
			{
				auto selfPos = blockpos + g_DirectionCoord[specialdir];
				aveltMe = psection->getLight2(selfPos, true);
				bool isXdir = dir < 2;
				int xdir = isXdir ? dir : vert.pos.x == 0 ? 0 : 1;
				int zdir = !isXdir ? dir : vert.pos.z == 0 ? 2 : 3;
				aveltNeight[0] = psection->getLight2(selfPos + g_DirectionCoord[xdir], true);
				aveltNeight[1] = psection->getLight2(selfPos + g_DirectionCoord[zdir], true);
				aveltNeight[2] = psection->getLight2(selfPos + g_DirectionCoord[xdir] + g_DirectionCoord[zdir], true);
				avelt = ((aveltMe + aveltNeight[0] + aveltNeight[1] + aveltNeight[2]) >> 2) & 0xff00ff;
				avelt = ((aveltMe + aveltNeight[0]) >> 1) & 0xff00ff;
			}
			else
			{
				auto selfPos = blockpos + g_DirectionCoord[dir];
				aveltMe = psection->getLight2(selfPos, true);
				int xdir = vert.pos.x == 0 ? 0 : 1;
				int zdir = vert.pos.z == 0 ? 2 : 3;
				int sideDir = dir > 1 ? xdir : zdir;
				aveltNeight[0] = psection->getLight2(selfPos + g_DirectionCoord[sideDir], true);
				avelt = ((aveltMe + aveltNeight[0]) >> 1) & 0xff00ff;
			}
			//int lt1 = (((avelt >> 4) & 0xf) * vertcolor.a) >> 5;
			//int lt2 = (((avelt >> 20) & 0xf) * vertcolor.a) >> 5;
			//vert.pos.w = (lt1 << 8) | lt2;
			//vert.color = vertcolor;
			vert.color = vertcolor;
			InitBlockVertLight(vert, avelt, uvtile);
		}
		if (psubmesh) psubmesh->addGeomFace(mesh, &blockpos);// addGeomFaceLight(mesh, &blockpos, faceVertexLight, &facecolor, pmtl->getUVTile());
	}
#endif
}

void HorizontalArcHalfPlateMaterial::initHalfFaceVertData()
{
	if (m_mHalfFace().size())
	{
		return;
	}
	for (int i = 0; i < 2; i++)
	{
		for (int j = 0; j < 4; j++)
		{
			int d = i * 4 + j;
			dynamic_array<UInt16>* indices = m_dPosIndices;
			DirectionType dir = (DirectionType)(d % 4);
			dynamic_array<BlockGeomVert> vertices;
			Rainbow::Vector3f normalVec = g_DirectionCoord[j].toVector3();
			// unsigned short dir_color = TriangleNormal2LightColor(g_DirectionCoord[dir].toVector3());
			////unsigned short dir_color = Normal2LightColor(normalVec);
			BlockVector vertcolor;
			vertcolor.v = 0xffffffff;
			vertcolor.w = 0;
			Normalize(normalVec);
			BlockVector normal_dir = PackVertNormal(normalVec);
			BlockGeomVert vert[4];
			if (0 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0x7f7f);
				vert[1].pos = Rainbow::Vector4f(0, 0, 100, 0x7f7f);
				vert[2].pos = Rainbow::Vector4f(0, 50, 100, 0x7f7f);
				vert[3].pos = Rainbow::Vector4f(0, 50, 0, 0x7f7f);
				//纹理不分上下部分
				vert[0].uv = { 1, 1 };
				vert[1].uv = { 0, 1 };
				vert[2].uv = { 0, 0 };
				vert[3].uv = { 1, 0 };
			}
			else if (1 == d)
			{
				vert[0].pos = Rainbow::Vector4f(100, 0, 0, 0x7f7f);
				vert[1].pos = Rainbow::Vector4f(100, 50, 0, 0x7f7f);
				vert[2].pos = Rainbow::Vector4f(100, 50, 100, 0x7f7f);
				vert[3].pos = Rainbow::Vector4f(100, 0, 100, 0x7f7f);

				vert[0].uv = { 0, 1 };
				vert[1].uv = { 0, 0 };
				vert[2].uv = { 1, 0 };
				vert[3].uv = { 1, 1 };
			}
			else if (2 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0x7f7f);
				vert[1].pos = Rainbow::Vector4f(0, 50, 0, 0x7f7f);
				vert[2].pos = Rainbow::Vector4f(100, 50, 0, 0x7f7f);
				vert[3].pos = Rainbow::Vector4f(100, 0, 0, 0x7f7f);

				vert[0].uv = { 0, 1 };
				vert[1].uv = { 0, 0 };
				vert[2].uv = { 1, 0 };
				vert[3].uv = { 1, 1 };
			}
			else if (3 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 0, 100, 0x7f7f);
				vert[1].pos = Rainbow::Vector4f(100, 0, 100, 0x7f7f);
				vert[2].pos = Rainbow::Vector4f(100, 50, 100, 0x7f7f);
				vert[3].pos = Rainbow::Vector4f(0, 50, 100, 0x7f7f);

				vert[0].uv = { 1, 1 };
				vert[1].uv = { 0, 1 };
				vert[2].uv = { 0, 0 };
				vert[3].uv = { 1, 0 };
			}
			else if (4 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 50, 0, 0x7f7f);
				vert[1].pos = Rainbow::Vector4f(0, 50, 100, 0x7f7f);
				vert[2].pos = Rainbow::Vector4f(0, 100, 100, 0x7f7f);
				vert[3].pos = Rainbow::Vector4f(0, 100, 0, 0x7f7f);
				//纹理不分上下部分
				vert[0].uv = { 1, 1 };
				vert[1].uv = { 0, 1 };
				vert[2].uv = { 0, 0 };
				vert[3].uv = { 1, 0 };
			}
			else if (5 == d)
			{
				vert[0].pos = Rainbow::Vector4f(100, 50, 0, 0x7f7f);
				vert[1].pos = Rainbow::Vector4f(100, 100, 0, 0x7f7f);
				vert[2].pos = Rainbow::Vector4f(100, 100, 100, 0x7f7f);
				vert[3].pos = Rainbow::Vector4f(100, 50, 100, 0x7f7f);

				vert[0].uv = { 0, 1 };
				vert[1].uv = { 0, 0 };
				vert[2].uv = { 1, 0 };
				vert[3].uv = { 1, 1 };
			}
			else if (6 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 50, 0, 0x7f7f);
				vert[1].pos = Rainbow::Vector4f(0, 100, 0, 0x7f7f);
				vert[2].pos = Rainbow::Vector4f(100, 100, 0, 0x7f7f);
				vert[3].pos = Rainbow::Vector4f(100, 50, 0, 0x7f7f);

				vert[0].uv = { 0, 1 };
				vert[1].uv = { 0, 0 };
				vert[2].uv = { 1, 0 };
				vert[3].uv = { 1, 1 };
			}
			else if (7 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 50, 100, 0x7f7f);
				vert[1].pos = Rainbow::Vector4f(100, 50, 100, 0x7f7f);
				vert[2].pos = Rainbow::Vector4f(100, 100, 100, 0x7f7f);
				vert[3].pos = Rainbow::Vector4f(0, 100, 100, 0x7f7f);

				vert[0].uv = { 1, 1 };
				vert[1].uv = { 0, 1 };
				vert[2].uv = { 0, 0 };
				vert[3].uv = { 1, 0 };
			}

			for (int oo = 0; oo < 4; oo++)
			{
				vert[oo].uv = { short(vert[oo].uv.x * BLOCKUV_SCALE), short(i == 1 ? short(vert[oo].uv.y * 0.5f * BLOCKUV_SCALE) : short((1.f - (1.f - vert[oo].uv.y) * 0.5f) * BLOCKUV_SCALE)) };
				vert[oo].normal = normal_dir;
				vert[oo].color.SetUInt32(vertcolor.v);
				vert[oo].pos.w = 0;
				vertices.push_back(vert[oo]);
			}
			m_mHalfFace().push_back(vertices);
		}
	}
}

void HorizontalArcHalfPlateMaterial::initVertData()
{
	initHalfWholeFaceVertData();
	initHalfFaceVertData();
	ArcPlateMaterial::initVertData();
// 	initWholeFaceVertData();
// 	initTriangleFaceVertData();
// 	initSlantFaceVertData();
// 	initPhyModelData();
}

void HorizontalArcHalfPlateMaterial::initHalfWholeFaceVertData()
{
	if (m_mHalfWholeFace().size() != 0)
	{
		return;
	}
	for (int d = 4; d < 6; d++)
	{
		DirectionType dir = (DirectionType)d;
		dynamic_array<UInt16>* indices = m_dPosIndices;

		dynamic_array<BlockGeomVert> vertices;
		Rainbow::Vector3f normalVec = g_DirectionCoord[d].toVector3();
		// unsigned short dir_color = TriangleNormal2LightColor(g_DirectionCoord[dir].toVector3());
		////unsigned short dir_color = Normal2LightColor(normalVec);
		BlockVector vertcolor;
		vertcolor.v = 0xffffffff;
		vertcolor.w = 0;
		Normalize(normalVec);
		BlockVector normal_dir = PackVertNormal(normalVec);
		BlockGeomVert vert[4];
		/*if (0 == d)
		{
			vert[0].pos = Rainbow::Vector4f(50, 0, 0, 0x7f7f);
			vert[1].pos = Rainbow::Vector4f(50, 0, 100, 0x7f7f);
			vert[2].pos = Rainbow::Vector4f(50, 100, 100, 0x7f7f);
			vert[3].pos = Rainbow::Vector4f(50, 100, 0, 0x7f7f);

			vert[0].uv = { 1, 1 };
			vert[1].uv = { 0, 1 };
			vert[2].uv = { 0, 0 };
			vert[3].uv = { 1, 0 };
		}
		else if (1 == d)
		{
			vert[0].pos = Rainbow::Vector4f(50, 0, 0, 0x7f7f);
			vert[1].pos = Rainbow::Vector4f(50, 100, 0, 0x7f7f);
			vert[2].pos = Rainbow::Vector4f(50, 100, 100, 0x7f7f);
			vert[3].pos = Rainbow::Vector4f(50, 0, 100, 0x7f7f);

			vert[0].uv = { 0, 1 };
			vert[1].uv = { 0, 0 };
			vert[2].uv = { 1, 0 };
			vert[3].uv = { 1, 1 };
		}
		else if (2 == d)
		{
			vert[0].pos = Rainbow::Vector4f(0, 0, 50, 0x7f7f);
			vert[1].pos = Rainbow::Vector4f(0, 100, 50, 0x7f7f);
			vert[2].pos = Rainbow::Vector4f(100, 100, 50, 0x7f7f);
			vert[3].pos = Rainbow::Vector4f(100, 0, 50, 0x7f7f);

			vert[0].uv = { 0, 1 };
			vert[1].uv = { 0, 0 };
			vert[2].uv = { 1, 0 };
			vert[3].uv = { 1, 1 };
		}
		else if (3 == d)
		{
			vert[0].pos = Rainbow::Vector4f(0, 0, 50, 0x7f7f);
			vert[1].pos = Rainbow::Vector4f(100, 0, 50, 0x7f7f);
			vert[2].pos = Rainbow::Vector4f(100, 100, 50, 0x7f7f);
			vert[3].pos = Rainbow::Vector4f(0, 100, 50, 0x7f7f);

			vert[0].uv = { 1, 1 };
			vert[1].uv = { 0, 1 };
			vert[2].uv = { 0, 0 };
			vert[3].uv = { 1, 0 };
		}
		else */if (4 == d)
		{
			vert[0].pos = Rainbow::Vector4f(0, 50, 0, 0x7f7f);
			vert[1].pos = Rainbow::Vector4f(100, 50, 0, 0x7f7f);
			vert[2].pos = Rainbow::Vector4f(100, 50, 100, 0x7f7f);
			vert[3].pos = Rainbow::Vector4f(0, 50, 100, 0x7f7f);

			vert[0].uv = { 1, 0 };
			vert[1].uv = { 0, 0 };
			vert[2].uv = { 0, 1 };
			vert[3].uv = { 1, 1 };
		}
		else if (5 == d)
		{
			vert[0].pos = Rainbow::Vector4f(0, 50, 0, 0x7f7f);
			vert[1].pos = Rainbow::Vector4f(0, 50, 100, 0x7f7f);
			vert[2].pos = Rainbow::Vector4f(100, 50, 100, 0x7f7f);
			vert[3].pos = Rainbow::Vector4f(100, 50, 0, 0x7f7f);

			vert[0].uv = { 0, 0 };
			vert[1].uv = { 0, 1 };
			vert[2].uv = { 1, 1 };
			vert[3].uv = { 1, 0 };
		}

		for (int oo = 0; oo < 4; oo++)
		{
			vert[oo].uv = {short( vert[oo].uv.x * BLOCKUV_SCALE),short( vert[oo].uv.y * BLOCKUV_SCALE) };
			vert[oo].normal = normal_dir;
			vert[oo].color.SetUInt32(vertcolor.v);
 			vert[oo].pos.w = 0;
			vertices.push_back(vert[oo]);
		}
		m_mHalfWholeFace().push_back(vertices);
	}
}

void HorizontalArcHalfPlateMaterial::initDiamondFaceVertData()
{
	for (int i = 0; i < 4; i++)
	{
		for (int j = 0; j < 4; j++)
		{
			DirectionType dir = (DirectionType)j;
			dynamic_array<UInt16>* indices = m_PosDiamondIndices;
			int d = i * 4 + j;
			// unsigned short dir_color = TriangleNormal2LightColor(g_DirectionCoord[dir].toVector3());
			dynamic_array<BlockGeomVert> vertices;
			Rainbow::Vector3f normalVec = g_DirectionCoord[j].toVector3();
			////unsigned short dir_color = Normal2LightColor(normalVec);
			Normalize(normalVec);
			BlockVector normal_dir = PackVertNormal(normalVec);
			BlockVector vertcolor;
			vertcolor.v = 0xffffffff;
			vertcolor.w = 0;

			Rainbow::Vector2f uv[5];
			BlockGeomVert vert[5];
			if (0 == d)//斜线朝向3 down
			{
				vert[0].pos = Rainbow::Vector4f(0, 50, 0, 0);
				vert[1].pos = Rainbow::Vector4f(0, 0, 0, 0);
				vert[2].pos = Rainbow::Vector4f(0, 0, 100, 0);
				vert[3].pos = Rainbow::Vector4f(0, 29.263f, 66.667f, 0);
				vert[4].pos = Rainbow::Vector4f(0, 45.107f, 33.333f, 0);

				uv[0] = { 1, 0.5f };
				uv[1] = { 1, 1 };
				uv[2] = { 0, 1 };
				uv[3] = { 0.33333f, 0.70737f };
				uv[4] = { 0.66667f, 0.54893f };
			}
			else if (1 == d)
			{
				vert[0].pos = Rainbow::Vector4f(100, 0, 100, 0);
				vert[1].pos = Rainbow::Vector4f(100, 0, 0, 0);
				vert[2].pos = Rainbow::Vector4f(100, 50, 0, 0);
				vert[3].pos = Rainbow::Vector4f(100, 45.107f, 33.333f, 0);
				vert[4].pos = Rainbow::Vector4f(100, 29.263f, 66.667f, 0);

				uv[0] = { 1, 1 };
				uv[1] = { 0, 1 };
				uv[2] = { 0, 0.5f };
				uv[3] = { 0.33333f, 0.54893f };
				uv[4] = { 0.66667f, 0.70737f };
			}
			else if (2 == d)
			{
				vert[0].pos = Rainbow::Vector4f(100, 0, 0, 0);
				vert[1].pos = Rainbow::Vector4f(0, 0, 0, 0);
				vert[2].pos = Rainbow::Vector4f(0, 50, 0, 0);
				vert[3].pos = Rainbow::Vector4f(33.333f, 45.107f, 0, 0);
				vert[4].pos = Rainbow::Vector4f(66.667f, 29.263f, 0, 0);

				uv[0] = { 1, 1 };
				uv[1] = { 0, 1 };
				uv[2] = { 0, 0.5f };
				uv[3] = { 0.33333f, 0.54893f };
				uv[4] = { 0.66667f, 0.70737f };
			}
			else if (3 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 50, 100, 0);
				vert[1].pos = Rainbow::Vector4f(0, 0, 100, 0);
				vert[2].pos = Rainbow::Vector4f(100, 0, 100, 0);
				vert[3].pos = Rainbow::Vector4f(66.667f, 29.263f, 100, 0);
				vert[4].pos = Rainbow::Vector4f(33.333f, 45.107f, 100, 0);

				uv[0] = { 1, 0.5f };
				uv[1] = { 1, 1 };
				uv[2] = { 0, 1 };
				uv[3] = { 0.33333f, 0.70737f };
				uv[4] = { 0.66667f, 0.54893f };
			}
			else if (4 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
				vert[1].pos = Rainbow::Vector4f(0, 0, 100, 0);
				vert[2].pos = Rainbow::Vector4f(0, 50, 100, 0);
				vert[3].pos = Rainbow::Vector4f(0, 45.107f, 66.667f, 0);
				vert[4].pos = Rainbow::Vector4f(0, 29.263f, 33.333f, 0);

				uv[0] = { 1, 1 };
				uv[1] = { 0, 1 };
				uv[2] = { 0, 0.5f };
				uv[3] = { 0.33333f, 0.54893f };
				uv[4] = { 0.66667f, 0.70737f };
			}
			else if (5 == d)
			{
				vert[0].pos = Rainbow::Vector4f(100, 50, 100, 0);
				vert[1].pos = Rainbow::Vector4f(100, 0, 100, 0);
				vert[2].pos = Rainbow::Vector4f(100, 0, 0, 0);
				vert[3].pos = Rainbow::Vector4f(100, 29.263f, 33.333f, 0);
				vert[4].pos = Rainbow::Vector4f(100, 45.107f, 66.667f, 0);

				uv[0] = { 1, 0.5f };
				uv[1] = { 1, 1 };
				uv[2] = { 0, 1 };
				uv[3] = { 0.33333f, 0.70737f };
				uv[4] = { 0.66667f, 0.54893f };
			}
			else if (6 == d)
			{
				vert[0].pos = Rainbow::Vector4f(100, 50, 0, 0);
				vert[1].pos = Rainbow::Vector4f(100, 0, 0, 0);
				vert[2].pos = Rainbow::Vector4f(0, 0, 0, 0);
				vert[3].pos = Rainbow::Vector4f(33.333f, 29.263f, 0, 0);
				vert[4].pos = Rainbow::Vector4f(66.667f, 45.107f, 0, 0);

				uv[0] = { 1, 0.5f };
				uv[1] = { 1, 1 };
				uv[2] = { 0, 1 };
				uv[3] = { 0.33333f, 0.70737f };
				uv[4] = { 0.66667f, 0.54893f };
			}
			else if (7 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 0, 100, 0);
				vert[1].pos = Rainbow::Vector4f(100, 0, 100, 0);
				vert[2].pos = Rainbow::Vector4f(100, 50, 100, 0);
				vert[3].pos = Rainbow::Vector4f(66.667f, 45.107f, 100, 0);
				vert[4].pos = Rainbow::Vector4f(33.333f, 29.263f, 100, 0);

				uv[0] = { 1, 1 };
				uv[1] = { 0, 1 };
				uv[2] = { 0, 0.5f };
				uv[3] = { 0.33333f, 0.54893f };
				uv[4] = { 0.66667f, 0.70737f };
			}
			else if (8 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 100, 100, 0);
				vert[1].pos = Rainbow::Vector4f(0, 100, 0, 0);
				vert[2].pos = Rainbow::Vector4f(0, 50, 0, 0);
				vert[3].pos = Rainbow::Vector4f(0, 54.893f, 33.333f, 0);
				vert[4].pos = Rainbow::Vector4f(0, 70.737f, 66.667f, 0);

				uv[0] = { 0, 0 };
				uv[1] = { 1, 0 };
				uv[2] = { 1, 0.5f };
				uv[3] = { 0.66667f, 0.45107f };
				uv[4] = { 0.33333f, 0.29263f };
			}
			else if (9 == d)
			{
				vert[0].pos = Rainbow::Vector4f(100, 50, 0, 0);
				vert[1].pos = Rainbow::Vector4f(100, 100, 0, 0);
				vert[2].pos = Rainbow::Vector4f(100, 100, 100, 0);
				vert[3].pos = Rainbow::Vector4f(100, 70.737f, 66.667f, 0);
				vert[4].pos = Rainbow::Vector4f(100, 54.893f, 33.333f, 0);

				uv[0] = { 0, 0.5f };
				uv[1] = { 0, 0 };
				uv[2] = { 1, 0 };
				uv[3] = { 0.66667f, 0.29263f };
				uv[4] = { 0.33333f, 0.45107f };
			}
			else if (10 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 50, 0, 0);
				vert[1].pos = Rainbow::Vector4f(0, 100, 0, 0);
				vert[2].pos = Rainbow::Vector4f(100, 100, 0, 0);
				vert[3].pos = Rainbow::Vector4f(66.667f, 70.737f, 0, 0);
				vert[4].pos = Rainbow::Vector4f(33.333f, 54.893f, 0, 0);

				uv[0] = { 0, 0.5f };
				uv[1] = { 0, 0 };
				uv[2] = { 1, 0 };
				uv[3] = { 0.66667f, 0.29263f };
				uv[4] = { 0.33333f, 0.45107f };
			}
			else if (11 == d)
			{
				vert[0].pos = Rainbow::Vector4f(100, 100, 100, 0);
				vert[1].pos = Rainbow::Vector4f(0, 100, 100, 0);
				vert[2].pos = Rainbow::Vector4f(0, 50, 100, 0);
				vert[3].pos = Rainbow::Vector4f(33.333f, 54.893f, 100, 0);
				vert[4].pos = Rainbow::Vector4f(66.667f, 70.737f, 100, 0);

				uv[0] = { 0, 0 };
				uv[1] = { 1, 0 };
				uv[2] = { 1, 0.5f };
				uv[3] = { 0.66667f, 0.45107f };
				uv[4] = { 0.33333f, 0.29263f };
			}
			else if (12 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 50, 100, 0);
				vert[1].pos = Rainbow::Vector4f(0, 100, 100, 0);
				vert[2].pos = Rainbow::Vector4f(0, 100, 0, 0);
				vert[3].pos = Rainbow::Vector4f(0, 70.737f, 33.333f, 0);
				vert[4].pos = Rainbow::Vector4f(0, 54.893f, 66.667f, 0);

				uv[0] = { 0, 0.5f };
				uv[1] = { 0, 0 };
				uv[2] = { 1, 0 };
				uv[3] = { 0.66667f, 0.29263f };
				uv[4] = { 0.33333f, 0.45107f };
			}
			else if (13 == d)
			{
				vert[0].pos = Rainbow::Vector4f(100, 100, 0, 0);
				vert[1].pos = Rainbow::Vector4f(100, 100, 100, 0);
				vert[2].pos = Rainbow::Vector4f(100, 50, 100, 0);
				vert[3].pos = Rainbow::Vector4f(100, 54.893f, 66.667f, 0);
				vert[4].pos = Rainbow::Vector4f(100, 70.737f, 33.333f, 0);

				uv[0] = { 0, 0 };
				uv[1] = { 1, 0 };
				uv[2] = { 1, 0.5f };
				uv[3] = { 0.66667f, 0.45107f };
				uv[4] = { 0.33333f, 0.29263f };
			}
			else if (14 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 100, 0, 0);
				vert[1].pos = Rainbow::Vector4f(100, 100, 0, 0);
				vert[2].pos = Rainbow::Vector4f(100, 50, 0, 0);
				vert[3].pos = Rainbow::Vector4f(66.667f, 54.893f, 0, 0);
				vert[4].pos = Rainbow::Vector4f(33.333f, 70.737f, 0, 0);

				uv[0] = { 0, 0 };
				uv[1] = { 1, 0 };
				uv[2] = { 1, 0.5f };
				uv[3] = { 0.66667f, 0.45107f };
				uv[4] = { 0.33333f, 0.29263f };
			}
			else if (15 == d)
			{
				vert[0].pos = Rainbow::Vector4f(100, 50, 100, 0);
				vert[1].pos = Rainbow::Vector4f(100, 100, 100, 0);
				vert[2].pos = Rainbow::Vector4f(0, 100, 100, 0);
				vert[3].pos = Rainbow::Vector4f(33.333f, 70.737f, 100, 0);
				vert[4].pos = Rainbow::Vector4f(66.667f, 54.893f, 100, 0);

				uv[0] = { 0, 0.5f };
				uv[1] = { 0, 0 };
				uv[2] = { 1, 0 };
				uv[3] = { 0.66667f, 0.29263f };
				uv[4] = { 0.33333f, 0.45107f };
			}
			for (int oo = 0; oo < 5; oo++)
			{
				vert[oo].uv = { short(uv[oo].x * BLOCKUV_SCALE), short(uv[oo].y * BLOCKUV_SCALE) };
				vert[oo].normal = normal_dir;
				vert[oo].color.SetUInt32(vertcolor.v);
				vert[oo].pos.w = 0;
				vertices.push_back(vert[oo]);
			}
			m_mDiamondFace.push_back(vertices);
		}
	}
}

void HorizontalArcHalfPlateMaterial::initBigArcFaceVertData()
{
	for (int i = 0; i < 2; i++)
	{
		for (int j = 0; j < 4; j++)
		{
			DirectionType dir = (DirectionType)j;
			dynamic_array<UInt16>* indices = m_PosBigArcIndices;
			int d = i * 4 + j;
			dynamic_array<BlockGeomVert> vertices;
			Rainbow::Vector3f normalVec = (g_DirectionCoord[j] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
			//unsigned short dir_color = Normal2LightColor(normalVec);
			BlockVector vertcolor;
			vertcolor.v = 0xffffffff;
			vertcolor.w = 0;
			Normalize(normalVec);
			BlockVector normal_dir = PackVertNormal(normalVec);
			BlockGeomVert vert[8];
			Rainbow::Vector2f uv[8];
			if (0 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
				vert[1].pos = Rainbow::Vector4f(0, 0, 100, 0);
				vert[2].pos = Rainbow::Vector4f(33.333f, 29.263f, 100, 0);
				vert[3].pos = Rainbow::Vector4f(33.333f, 29.263f, 0, 0);
				vert[4].pos = Rainbow::Vector4f(66.667f, 45.107f, 0, 0);
				vert[5].pos = Rainbow::Vector4f(66.667f, 45.107f, 100, 0);
				vert[6].pos = Rainbow::Vector4f(100, 50, 100, 0);
				vert[7].pos = Rainbow::Vector4f(100, 50, 0, 0);

				uv[0] = { 1, 1 };
				uv[1] = { 0, 1 };
				uv[2] = { 0, 0.6667f };
				uv[3] = { 1, 0.6667f };
				uv[4] = { 1, 0.3333f };
				uv[5] = { 0, 0.3333f };
				uv[6] = { 0, 0 };
				uv[7] = { 1, 0 };
			}
			else if (1 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 50, 0, 0);
				vert[1].pos = Rainbow::Vector4f(0, 50, 100, 0);
				vert[2].pos = Rainbow::Vector4f(33.333f, 45.107f, 100, 0);
				vert[3].pos = Rainbow::Vector4f(33.333f, 45.107f, 0, 0);
				vert[4].pos = Rainbow::Vector4f(66.667f, 29.263f, 0, 0);
				vert[5].pos = Rainbow::Vector4f(66.667f, 29.263f, 100, 0);
				vert[6].pos = Rainbow::Vector4f(100, 0, 100, 0);
				vert[7].pos = Rainbow::Vector4f(100, 0, 0, 0);

				uv[0] = { 0, 0 };
				uv[1] = { 1, 0 };
				uv[2] = { 1, 0.3333f };
				uv[3] = { 0, 0.3333f };
				uv[4] = { 0, 0.6667f };
				uv[5] = { 1, 0.6667f };
				uv[6] = { 1, 1 };
				uv[7] = { 0, 1 };
			}
			else if (2 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 50, 100, 0);
				vert[1].pos = Rainbow::Vector4f(100, 50, 100, 0);
				vert[2].pos = Rainbow::Vector4f(100, 45.107f, 66.667f, 0);
				vert[3].pos = Rainbow::Vector4f(0, 45.107f, 66.667f, 0);
				vert[4].pos = Rainbow::Vector4f(0, 29.263f, 33.333f, 0);
				vert[5].pos = Rainbow::Vector4f(100, 29.263f, 33.333f, 0);
				vert[6].pos = Rainbow::Vector4f(100, 0, 0, 0);
				vert[7].pos = Rainbow::Vector4f(0, 0, 0, 0);

				uv[0] = { 0, 0 };
				uv[1] = { 1, 0 };
				uv[2] = { 1, 0.3333f };
				uv[3] = { 0, 0.3333f };
				uv[4] = { 0, 0.6667f };
				uv[5] = { 1, 0.6667f };
				uv[6] = { 1, 1 };
				uv[7] = { 0, 1 };
			}
			else if (3 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 0, 100, 0);
				vert[1].pos = Rainbow::Vector4f(100, 0, 100, 0);
				vert[2].pos = Rainbow::Vector4f(100, 29.263f, 66.667f, 0);
				vert[3].pos = Rainbow::Vector4f(0, 29.263f, 66.667f, 0);
				vert[4].pos = Rainbow::Vector4f(0, 45.107f, 33.333f, 0);
				vert[5].pos = Rainbow::Vector4f(100, 45.107f, 33.333f, 0);
				vert[6].pos = Rainbow::Vector4f(100, 50, 0, 0);
				vert[7].pos = Rainbow::Vector4f(0, 50, 0, 0);

				uv[0] = { 1, 1 };
				uv[1] = { 0, 1 };
				uv[2] = { 0, 0.6667f };
				uv[3] = { 1, 0.6667f };
				uv[4] = { 1, 0.3333f };
				uv[5] = { 0, 0.3333f };
				uv[6] = { 0, 0 };
				uv[7] = { 1, 0 };
			}
			else if (4 == d)
			{
				vert[0].pos = Rainbow::Vector4f(100, 50, 0, 0);
				vert[1].pos = Rainbow::Vector4f(100, 50, 100, 0);
				vert[2].pos = Rainbow::Vector4f(66.667f, 54.893f, 100, 0);
				vert[3].pos = Rainbow::Vector4f(66.667f, 54.893f, 0, 0);
				vert[4].pos = Rainbow::Vector4f(33.333f, 70.737f, 0, 0);
				vert[5].pos = Rainbow::Vector4f(33.333f, 70.737f, 100, 0);
				vert[6].pos = Rainbow::Vector4f(0, 100, 100, 0);
				vert[7].pos = Rainbow::Vector4f(0, 100, 0, 0);

				uv[0] = { 1, 1 };
				uv[1] = { 0, 1 };
				uv[2] = { 0, 0.6667f };
				uv[3] = { 1, 0.6667f };
				uv[4] = { 1, 0.3333f };
				uv[5] = { 0, 0.3333f };
				uv[6] = { 0, 0 };
				uv[7] = { 1, 0 };
			}
			else if (5 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 50, 100, 0);
				vert[1].pos = Rainbow::Vector4f(0, 50, 0, 0);
				vert[2].pos = Rainbow::Vector4f(33.333f, 54.893f, 0, 0);
				vert[3].pos = Rainbow::Vector4f(33.333f, 54.893f, 100, 0);
				vert[4].pos = Rainbow::Vector4f(66.667f, 70.737f, 100, 0);
				vert[5].pos = Rainbow::Vector4f(66.667f, 70.737f, 0, 0);
				vert[6].pos = Rainbow::Vector4f(100, 100, 0, 0);
				vert[7].pos = Rainbow::Vector4f(100, 100, 100, 0);

				uv[0] = { 1, 1 };
				uv[1] = { 0, 1 };
				uv[2] = { 0, 0.6667f };
				uv[3] = { 1, 0.6667f };
				uv[4] = { 1, 0.3333f };
				uv[5] = { 0, 0.3333f };
				uv[6] = { 0, 0 };
				uv[7] = { 1, 0 };
			}
			else if (6 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 100, 0, 0);
				vert[1].pos = Rainbow::Vector4f(100, 100, 0, 0);
				vert[2].pos = Rainbow::Vector4f(100, 70.737f, 33.333f, 0);
				vert[3].pos = Rainbow::Vector4f(0, 70.737f, 33.333f, 0);
				vert[4].pos = Rainbow::Vector4f(0, 54.893f, 66.667f, 0);
				vert[5].pos = Rainbow::Vector4f(100, 54.893f, 66.667f, 0);
				vert[6].pos = Rainbow::Vector4f(100, 50, 100, 0);
				vert[7].pos = Rainbow::Vector4f(0, 50, 100, 0);


				uv[0] = { 0, 0 };
				uv[1] = { 1, 0 };
				uv[2] = { 1, 0.3333f };
				uv[3] = { 0, 0.3333f };
				uv[4] = { 0, 0.6667f };
				uv[5] = { 1, 0.6667f };
				uv[6] = { 1, 1 };
				uv[7] = { 0, 1 };
			}
			else if (7 == d)
			{
				vert[0].pos = Rainbow::Vector4f(0, 50, 0, 0);
				vert[1].pos = Rainbow::Vector4f(100, 50, 0, 0);
				vert[2].pos = Rainbow::Vector4f(100, 54.893f, 33.333f, 0);
				vert[3].pos = Rainbow::Vector4f(0, 54.893f, 33.333f, 0);
				vert[4].pos = Rainbow::Vector4f(0, 70.737f, 66.667f, 0);
				vert[5].pos = Rainbow::Vector4f(100, 70.737f, 66.667f, 0);
				vert[6].pos = Rainbow::Vector4f(100, 100, 100, 0);
				vert[7].pos = Rainbow::Vector4f(0, 100, 100, 0);

				uv[0] = { 1, 1 };
				uv[1] = { 0, 1 };
				uv[2] = { 0, 0.6667f };
				uv[3] = { 1, 0.6667f };
				uv[4] = { 1, 0.3333f };
				uv[5] = { 0, 0.3333f };
				uv[6] = { 0, 0 };
				uv[7] = { 1, 0 };
			}
			for (int oo = 0; oo < 8; oo++)
			{
				vert[oo].uv = { short(uv[oo].x * BLOCKUV_SCALE), short(uv[oo].y * BLOCKUV_SCALE) };
				vert[oo].normal = normal_dir;
				vert[oo].color.SetUInt32(vertcolor.v);
				// 				vert[oo].color = 0xffffffff;
				vertices.push_back(vert[oo]);
			}
			m_mBigArcFace.push_back(vertices);
		}
	}
}

void HorizontalArcHalfPlateMaterial::initTurnArcFaceVertData()
{
	for (int i = 0; i < 2; i++)//updown
	{
		for (int j = 0; j < 2; j++) //0是凹1是凸
		{
			for (int k = 0; k < 4; k++)//4个主方向
			{
				if (0 == k)
				{
					if (0 == i)
					{
						if (1 == j)
						{
							for (int o = 2; o <= 3; o++)
							{
								dynamic_array<BlockGeomVert> vertices;
								BlockGeomVert vert[7];
								Rainbow::Vector2f uv[7];
								if (o == 2)//0102
								{
									vert[0].pos = Rainbow::Vector4f(100, 50, 100, 0);
									vert[1].pos = Rainbow::Vector4f(66.667f, 45.107f, 100, 0);
									vert[2].pos = Rainbow::Vector4f(66.667f, 45.107f, 66.667f, 0);
									vert[3].pos = Rainbow::Vector4f(33.333f, 29.263f, 100, 0);
									vert[4].pos = Rainbow::Vector4f(33.333f, 29.263f, 33.333f, 0);
									vert[5].pos = Rainbow::Vector4f(0, 0, 100, 0);
									vert[6].pos = Rainbow::Vector4f(0, 0, 0, 0);
									uv[0] = { 0, 0 };
									uv[1] = { 0, 0.3333f };
									uv[2] = { 0.33333f, 0.3333f };
									uv[3] = { 0, 0.6667f };
									uv[4] = { 0.66667f, 0.6667f };
									uv[5] = { 0, 1 };
									uv[6] = { 1, 1 };
								}
								else//0103
								{
									vert[0].pos = Rainbow::Vector4f(100, 50, 0, 0);
									vert[1].pos = Rainbow::Vector4f(66.667f, 45.107f, 33.333f, 0);
									vert[2].pos = Rainbow::Vector4f(66.667f, 45.107f, 0, 0);
									vert[3].pos = Rainbow::Vector4f(33.333f, 29.263f, 66.667f, 0);
									vert[4].pos = Rainbow::Vector4f(33.333f, 29.263f, 0, 0);
									vert[5].pos = Rainbow::Vector4f(0, 0, 100, 0);
									vert[6].pos = Rainbow::Vector4f(0, 0, 0, 0);
									uv[0] = { 1, 0 };
									uv[1] = { 0.66667f, 0.3333f };
									uv[2] = { 1, 0.3333f };
									uv[3] = { 0.33333f, 0.6667f };
									uv[4] = { 1, 0.6667f };
									uv[5] = { 0, 1 };
									uv[6] = { 1, 1 };
								}

								Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
								//unsigned short dir_color = Normal2LightColor(normalVec);
								Normalize(normalVec);
								BlockVector normal_dir = PackVertNormal(normalVec);
								BlockVector vertcolor;
								vertcolor.v = 0xffffffff;
								vertcolor.w = 0;

								for (int oo = 0; oo < 7; oo++)
								{
									vert[oo].uv = { short(uv[oo].x * BLOCKUV_SCALE), short(uv[oo].y * BLOCKUV_SCALE) };
									vert[oo].normal = normal_dir;
									vert[oo].color.SetUInt32(vertcolor.v);
									vertices.push_back(vert[oo]);
								}

								m_mTurnArcFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							}
						}
						else
						{
							for (int o = 2; o <= 3; o++)
							{
								dynamic_array<BlockGeomVert> vertices;
								BlockGeomVert vert[7];
								Rainbow::Vector2f uv[7];
								if (o == 2)//0002
								{
									vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
									vert[1].pos = Rainbow::Vector4f(33.333f, 29.263f, 0, 0);
									vert[2].pos = Rainbow::Vector4f(33.333f, 29.263f, 33.333f, 0);
									vert[3].pos = Rainbow::Vector4f(66.667f, 45.107f, 0, 0);
									vert[4].pos = Rainbow::Vector4f(66.667f, 45.107f, 66.667f, 0);
									vert[5].pos = Rainbow::Vector4f(100, 50, 0, 0);
									vert[6].pos = Rainbow::Vector4f(100, 50, 100, 0);
									uv[0] = { 1, 1 };
									uv[1] = { 1, 0.6667f };
									uv[2] = { 0.66667f, 0.6667f };
									uv[3] = { 1, 0.3333f };
									uv[4] = { 0.33333f, 0.3333f };
									uv[5] = { 1, 0 };
									uv[6] = { 0, 0 };
								}
								else//0003
								{
									vert[0].pos = Rainbow::Vector4f(0, 0, 100, 0);
									vert[1].pos = Rainbow::Vector4f(33.333f, 29.263f, 66.667f, 0);
									vert[2].pos = Rainbow::Vector4f(33.333f, 29.263f, 100, 0);
									vert[3].pos = Rainbow::Vector4f(66.667f, 45.107f, 33.333f, 0);
									vert[4].pos = Rainbow::Vector4f(66.667f, 45.107f, 100, 0);
									vert[5].pos = Rainbow::Vector4f(100, 50, 0, 0);
									vert[6].pos = Rainbow::Vector4f(100, 50, 100, 0);
									uv[0] = { 0, 1 };
									uv[1] = { 0.33333f, 0.6667f };
									uv[2] = { 0, 0.6667f };
									uv[3] = { 0.66667f, 0.3333f };
									uv[4] = { 0, 0.3333f };
									uv[5] = { 1, 0 };
									uv[6] = { 0, 0 };
								}

								Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
								//unsigned short dir_color = Normal2LightColor(normalVec);
								Normalize(normalVec);
								BlockVector normal_dir = PackVertNormal(normalVec);
								BlockVector vertcolor;
								vertcolor.v = 0xffffffff;
								vertcolor.w = 0;
								for (int oo = 0; oo < 7; oo++)
								{
									vert[oo].uv = { short(uv[oo].x * BLOCKUV_SCALE), short(uv[oo].y * BLOCKUV_SCALE) };
									vert[oo].normal = normal_dir;
									vert[oo].color.SetUInt32(vertcolor.v);
									vertices.push_back(vert[oo]);
								}
								m_mTurnArcFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							}
						}
					}
					else
					{
						if (1 == j)
						{
							for (int o = 2; o <= 3; o++)
							{
								dynamic_array<BlockGeomVert> vertices;
								BlockGeomVert vert[7];
								Rainbow::Vector2f uv[7];
								if (o == 2)//1102
								{
									vert[0].pos = Rainbow::Vector4f(100, 50, 100, 0);
									vert[1].pos = Rainbow::Vector4f(66.667f, 54.893f, 66.667f, 0);
									vert[2].pos = Rainbow::Vector4f(66.667f, 54.893f, 100, 0);
									vert[3].pos = Rainbow::Vector4f(33.333f, 70.737f, 33.333f, 0);
									vert[4].pos = Rainbow::Vector4f(33.333f, 70.737f, 100, 0);
									vert[5].pos = Rainbow::Vector4f(0, 100, 0, 0);
									vert[6].pos = Rainbow::Vector4f(0, 100, 100, 0);
									uv[0] = { 0, 1 };
									uv[1] = { 0.33333f, 0.6667f };
									uv[2] = { 0, 0.6667f };
									uv[3] = { 0.66667f, 0.3333f };
									uv[4] = { 0, 0.3333f };
									uv[5] = { 1, 0 };
									uv[6] = { 0, 0 };
								}
								else//1103
								{
									vert[0].pos = Rainbow::Vector4f(100, 50, 0, 0);
									vert[1].pos = Rainbow::Vector4f(66.667f, 54.893f, 0, 0);
									vert[2].pos = Rainbow::Vector4f(66.667f, 54.893f, 33.333f, 0);
									vert[3].pos = Rainbow::Vector4f(33.333f, 70.737f, 0, 0);
									vert[4].pos = Rainbow::Vector4f(33.333f, 70.737f, 66.667f, 0);
									vert[5].pos = Rainbow::Vector4f(0, 100, 0, 0);
									vert[6].pos = Rainbow::Vector4f(0, 100, 100, 0);
									uv[0] = { 1, 1 };
									uv[1] = { 1, 0.6667f };
									uv[2] = { 0.66667f, 0.6667f };
									uv[3] = { 1, 0.3333f };
									uv[4] = { 0.33333f, 0.3333f };
									uv[5] = { 1, 0 };
									uv[6] = { 0, 0 };
								}

								Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
								//unsigned short dir_color = Normal2LightColor(normalVec);
								Normalize(normalVec);
								BlockVector normal_dir = PackVertNormal(normalVec);
								BlockVector vertcolor;
								vertcolor.v = 0xffffffff;
								vertcolor.w = 0;
								for (int oo = 0; oo < 7; oo++)
								{
									vert[oo].uv = { short(uv[oo].x * BLOCKUV_SCALE), short(uv[oo].y * BLOCKUV_SCALE) };
									vert[oo].normal = normal_dir;
									vert[oo].color.SetUInt32(vertcolor.v);
									vertices.push_back(vert[oo]);
								}
								m_mTurnArcFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							}
						}
						else
						{
							for (int o = 2; o <= 3; o++)
							{
								dynamic_array<BlockGeomVert> vertices;
								BlockGeomVert vert[7];
								Rainbow::Vector2f uv[7];
								if (o == 2)//1002
								{
									vert[0].pos = Rainbow::Vector4f(0, 100, 0, 0);
									vert[1].pos = Rainbow::Vector4f(33.333f, 70.737f, 33.333f, 0);
									vert[2].pos = Rainbow::Vector4f(33.333f, 70.737f, 0, 0);
									vert[3].pos = Rainbow::Vector4f(66.667f, 54.893f, 66.667f, 0);
									vert[4].pos = Rainbow::Vector4f(66.667f, 54.893f, 0, 0);
									vert[5].pos = Rainbow::Vector4f(100, 50, 100, 0);
									vert[6].pos = Rainbow::Vector4f(100, 50, 0, 0);
									uv[0] = { 0, 1 };
									uv[1] = { 0.66667f, 0.3333f };
									uv[2] = { 1, 0.3333f };
									uv[3] = { 0.33333f, 0.6667f };
									uv[4] = { 1, 0.6667f };
									uv[5] = { 0, 1 };
									uv[6] = { 1, 1 };
								}
								else//1003
								{
									vert[0].pos = Rainbow::Vector4f(0, 100, 100, 0);
									vert[1].pos = Rainbow::Vector4f(33.333f, 70.737f, 100, 0);
									vert[2].pos = Rainbow::Vector4f(33.333f, 70.737f, 66.667f, 0);
									vert[3].pos = Rainbow::Vector4f(66.667f, 54.893f, 100, 0);
									vert[4].pos = Rainbow::Vector4f(66.667f, 54.893f, 33.333f, 0);
									vert[5].pos = Rainbow::Vector4f(100, 50, 100, 0);
									vert[6].pos = Rainbow::Vector4f(100, 50, 0, 0);
									uv[0] = { 0, 0 };
									uv[1] = { 0, 0.3333f };
									uv[2] = { 0.33333f, 0.3333f };
									uv[3] = { 0, 0.6667f };
									uv[4] = { 0.66667f, 0.6667f };
									uv[5] = { 0, 1 };
									uv[6] = { 1, 1 };
								}

								Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
								//unsigned short dir_color = Normal2LightColor(normalVec);
								Normalize(normalVec);
								BlockVector normal_dir = PackVertNormal(normalVec);
								BlockVector vertcolor;
								vertcolor.v = 0xffffffff;
								vertcolor.w = 0;
								for (int oo = 0; oo < 7; oo++)
								{
									vert[oo].uv = { short(uv[oo].x * BLOCKUV_SCALE), short(uv[oo].y * BLOCKUV_SCALE) };
									vert[oo].normal = normal_dir;
									vert[oo].color.SetUInt32(vertcolor.v);
									vertices.push_back(vert[oo]);
								}
								m_mTurnArcFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							}
						}
					}
				}
				else if (1 == k)
				{
					if (0 == i)
					{
						if (1 == j)
						{
							for (int o = 2; o <= 3; o++)
							{
								dynamic_array<BlockGeomVert> vertices;
								BlockGeomVert vert[7];
								Rainbow::Vector2f uv[7];
								if (o == 2)//0112
								{
									vert[0].pos = Rainbow::Vector4f(0, 50, 100, 0);
									vert[1].pos = Rainbow::Vector4f(33.333f, 45.107f, 66.667f, 0);
									vert[2].pos = Rainbow::Vector4f(33.333f, 45.107f, 100, 0);
									vert[3].pos = Rainbow::Vector4f(66.667f, 29.263f, 33.333f, 0);
									vert[4].pos = Rainbow::Vector4f(66.667f, 29.263f, 100, 0);
									vert[5].pos = Rainbow::Vector4f(100, 0, 0, 0);
									vert[6].pos = Rainbow::Vector4f(100, 0, 100, 0);
									uv[0] = { 1, 0 };
									uv[1] = { 0.66667f, 0.3333f };
									uv[2] = { 1, 0.3333f };
									uv[3] = { 0.33333f, 0.6667f };
									uv[4] = { 1, 0.6667f };
									uv[5] = { 0, 1 };
									uv[6] = { 1, 1 };
								}
								else//0113
								{
									vert[0].pos = Rainbow::Vector4f(0, 50, 0, 0);
									vert[1].pos = Rainbow::Vector4f(33.333f, 45.107f, 0, 0);
									vert[2].pos = Rainbow::Vector4f(33.333f, 45.107f, 33.333f, 0);
									vert[3].pos = Rainbow::Vector4f(66.667f, 29.263f, 0, 0);
									vert[4].pos = Rainbow::Vector4f(66.667f, 29.263f, 66.667f, 0);
									vert[5].pos = Rainbow::Vector4f(100, 0, 0, 0);
									vert[6].pos = Rainbow::Vector4f(100, 0, 100, 0);
									uv[0] = { 0, 0 };
									uv[1] = { 0, 0.3333f };
									uv[2] = { 0.33333f, 0.3333f };
									uv[3] = { 0, 0.6667f };
									uv[4] = { 0.66667f, 0.6667f };
									uv[5] = { 0, 1 };
									uv[6] = { 1, 1 };
								}

								Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
								//unsigned short dir_color = Normal2LightColor(normalVec);
								Normalize(normalVec);
								BlockVector normal_dir = PackVertNormal(normalVec);
								BlockVector vertcolor;
								vertcolor.v = 0xffffffff;
								vertcolor.w = 0;
								for (int oo = 0; oo < 7; oo++)
								{
									vert[oo].uv = { short(uv[oo].x * BLOCKUV_SCALE), short(uv[oo].y * BLOCKUV_SCALE) };
									vert[oo].normal = normal_dir;
									vert[oo].color.SetUInt32(vertcolor.v);
									vertices.push_back(vert[oo]);
								}
								m_mTurnArcFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							}
						}
						else
						{
							for (int o = 2; o <= 3; o++)
							{
								dynamic_array<BlockGeomVert> vertices;
								BlockGeomVert vert[7];
								Rainbow::Vector2f uv[7];
								if (o == 2)//0012
								{
									vert[0].pos = Rainbow::Vector4f(100, 0, 0, 0);
									vert[1].pos = Rainbow::Vector4f(66.667f, 29.263f, 33.333f, 0);
									vert[2].pos = Rainbow::Vector4f(66.667f, 29.263f, 0, 0);
									vert[3].pos = Rainbow::Vector4f(33.333f, 45.107f, 66.667f, 0);
									vert[4].pos = Rainbow::Vector4f(33.333f, 45.107f, 0, 0);
									vert[5].pos = Rainbow::Vector4f(0, 50, 100, 0);
									vert[6].pos = Rainbow::Vector4f(0, 50, 0, 0);
									uv[0] = { 0, 1 };
									uv[1] = { 0.33333f, 0.6667f };
									uv[2] = { 0, 0.6667f };
									uv[3] = { 0.66667f, 0.3333f };
									uv[4] = { 0, 0.3333f };
									uv[5] = { 1, 0 };
									uv[6] = { 0, 0 };
								}
								else//0013
								{
									vert[0].pos = Rainbow::Vector4f(100, 0, 100, 0);
									vert[1].pos = Rainbow::Vector4f(66.667f, 29.263f, 100, 0);
									vert[2].pos = Rainbow::Vector4f(66.667f, 29.263f, 66.667f, 0);
									vert[3].pos = Rainbow::Vector4f(33.333f, 45.107f, 100, 0);
									vert[4].pos = Rainbow::Vector4f(33.333f, 45.107f, 33.333f, 0);
									vert[5].pos = Rainbow::Vector4f(0, 50, 100, 0);
									vert[6].pos = Rainbow::Vector4f(0, 50, 0, 0);
									uv[0] = { 1, 1 };
									uv[1] = { 1, 0.6667f };
									uv[2] = { 0.66667f, 0.6667f };
									uv[3] = { 1, 0.3333f };
									uv[4] = { 0.33333f, 0.3333f };
									uv[5] = { 1, 0 };
									uv[6] = { 0, 0 };
								}
								Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
								//unsigned short dir_color = Normal2LightColor(normalVec);
								Normalize(normalVec);
								BlockVector normal_dir = PackVertNormal(normalVec);
								BlockVector vertcolor;
								vertcolor.v = 0xffffffff;
								vertcolor.w = 0;
								for (int oo = 0; oo < 7; oo++)
								{
									vert[oo].uv = { short(uv[oo].x * BLOCKUV_SCALE), short(uv[oo].y * BLOCKUV_SCALE) };
									vert[oo].normal = normal_dir;
									vert[oo].color.SetUInt32(vertcolor.v);
									vertices.push_back(vert[oo]);
								}
								m_mTurnArcFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							}
						}
					}
					else
					{
						if (1 == j)
						{
							for (int o = 2; o <= 3; o++)
							{
								dynamic_array<BlockGeomVert> vertices;
								BlockGeomVert vert[7];
								Rainbow::Vector2f uv[7];
								if (o == 2)//1112
								{
									vert[0].pos = Rainbow::Vector4f(0, 50, 100, 0);
									vert[1].pos = Rainbow::Vector4f(33.333f, 54.893f, 100, 0);
									vert[2].pos = Rainbow::Vector4f(33.333f, 54.893f, 66.667f, 0);
									vert[3].pos = Rainbow::Vector4f(66.667f, 70.737f, 100, 0);
									vert[4].pos = Rainbow::Vector4f(66.667f, 70.737f, 33.333f, 0);
									vert[5].pos = Rainbow::Vector4f(100, 100, 100, 0);
									vert[6].pos = Rainbow::Vector4f(100, 100, 0, 0);

									uv[0] = { 1, 1 };
									uv[1] = { 1, 0.6667f };
									uv[2] = { 0.66667f, 0.6667f };
									uv[3] = { 1, 0.3333f };
									uv[4] = { 0.33333f, 0.3333f };
									uv[5] = { 1, 0 };
									uv[6] = { 0, 0 };
								}
								else//1113
								{
									vert[0].pos = Rainbow::Vector4f(0, 50, 0, 0);
									vert[1].pos = Rainbow::Vector4f(33.333f, 54.893f, 33.333f, 0);
									vert[2].pos = Rainbow::Vector4f(33.333f, 54.893f, 0, 0);
									vert[3].pos = Rainbow::Vector4f(66.667f, 70.737f, 66.667f, 0);
									vert[4].pos = Rainbow::Vector4f(66.667f, 70.737f, 0, 0);
									vert[5].pos = Rainbow::Vector4f(100, 100, 100, 0);
									vert[6].pos = Rainbow::Vector4f(100, 100, 0, 0);

									uv[0] = { 0, 1 };
									uv[1] = { 0.33333f, 0.6667f };
									uv[2] = { 0, 0.6667f };
									uv[3] = { 0.66667f, 0.3333f };
									uv[4] = { 0, 0.3333f };
									uv[5] = { 1, 0 };
									uv[6] = { 0, 0 };
								}

								Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
								//unsigned short dir_color = Normal2LightColor(normalVec);
								BlockVector vertcolor;
								vertcolor.v = 0xffffffff;
								vertcolor.w = 0;
								Normalize(normalVec);
								BlockVector normal_dir = PackVertNormal(normalVec);
								for (int oo = 0; oo < 7; oo++)
								{
									vert[oo].uv = { short(uv[oo].x * BLOCKUV_SCALE), short(uv[oo].y * BLOCKUV_SCALE) };
									vert[oo].normal = normal_dir;
									vert[oo].color.SetUInt32(vertcolor.v);
									vertices.push_back(vert[oo]);
								}
								m_mTurnArcFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							}
						}
						else
						{
							for (int o = 2; o <= 3; o++)
							{
								dynamic_array<BlockGeomVert> vertices;
								BlockGeomVert vert[7];
								Rainbow::Vector2f uv[7];
								if (o == 2)//1012
								{
									vert[0].pos = Rainbow::Vector4f(100, 100, 0, 0);
									vert[1].pos = Rainbow::Vector4f(66.667f, 70.737f, 0, 0);
									vert[2].pos = Rainbow::Vector4f(66.667f, 70.737f, 33.333f, 0);
									vert[3].pos = Rainbow::Vector4f(33.333f, 54.893f, 0, 0);
									vert[4].pos = Rainbow::Vector4f(33.333f, 54.893f, 66.667f, 0);
									vert[5].pos = Rainbow::Vector4f(0, 50, 0, 0);
									vert[6].pos = Rainbow::Vector4f(0, 50, 100, 0);

									uv[0] = { 0, 0 };
									uv[1] = { 0, 0.3333f };
									uv[2] = { 0.33333f, 0.3333f };
									uv[3] = { 0, 0.6667f };
									uv[4] = { 0.66667f, 0.6667f };
									uv[5] = { 0, 1 };
									uv[6] = { 1, 1 };
								}
								else//1013
								{
									vert[0].pos = Rainbow::Vector4f(100, 100, 100, 0);
									vert[1].pos = Rainbow::Vector4f(66.667f, 70.737f, 66.667f, 0);
									vert[2].pos = Rainbow::Vector4f(66.667f, 70.737f, 100, 0);
									vert[3].pos = Rainbow::Vector4f(33.333f, 54.893f, 33.333f, 0);
									vert[4].pos = Rainbow::Vector4f(33.333f, 54.893f, 100, 0);
									vert[5].pos = Rainbow::Vector4f(0, 50, 0, 0);
									vert[6].pos = Rainbow::Vector4f(0, 50, 100, 0);

									uv[0] = { 1, 0 };
									uv[1] = { 0.66667f, 0.3333f };
									uv[2] = { 1, 0.3333f };
									uv[3] = { 0.33333f, 0.6667f };
									uv[4] = { 1, 0.6667f };
									uv[5] = { 0, 1 };
									uv[6] = { 1, 1 };
								}

								Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
								//unsigned short dir_color = Normal2LightColor(normalVec);
								BlockVector vertcolor;
								vertcolor.v = 0xffffffff;
								vertcolor.w = 0;
								Normalize(normalVec);
								BlockVector normal_dir = PackVertNormal(normalVec);
								for (int oo = 0; oo < 7; oo++)
								{
									vert[oo].uv = { short(uv[oo].x * BLOCKUV_SCALE), short(uv[oo].y * BLOCKUV_SCALE) };
									vert[oo].normal = normal_dir;
									vert[oo].color.SetUInt32(vertcolor.v);
									vertices.push_back(vert[oo]);
								}
								m_mTurnArcFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							}
						}
					}
				}
				else if (2 == k)
				{
					if (0 == i)
					{
						if (1 == j)
						{
							for (int o = 0; o <= 1; o++)
							{
								dynamic_array<BlockGeomVert> vertices;
								BlockGeomVert vert[7];
								Rainbow::Vector2f uv[7];
								if (o == 0)//0120
								{
									vert[0].pos = Rainbow::Vector4f(100, 50, 100, 0);
									vert[1].pos = Rainbow::Vector4f(66.667f, 45.107f, 66.667f, 0);
									vert[2].pos = Rainbow::Vector4f(100, 45.107f, 66.667f, 0);
									vert[3].pos = Rainbow::Vector4f(33.333f, 29.263f, 33.333f, 0);
									vert[4].pos = Rainbow::Vector4f(100, 29.263f, 33.333f, 0);
									vert[5].pos = Rainbow::Vector4f(0, 0, 0, 0);
									vert[6].pos = Rainbow::Vector4f(100, 0, 0, 0);

									uv[0] = { 1, 0 };
									uv[1] = { 0.66667f, 0.3333f };
									uv[2] = { 1, 0.3333f };
									uv[3] = { 0.33333f, 0.6667f };
									uv[4] = { 1, 0.6667f };
									uv[5] = { 0, 1 };
									uv[6] = { 1, 1 };
								}
								else//0121
								{
									vert[0].pos = Rainbow::Vector4f(0, 50, 100, 0);
									vert[1].pos = Rainbow::Vector4f(0, 45.107f, 66.667f, 0);
									vert[2].pos = Rainbow::Vector4f(33.333f, 45.107f, 66.667f, 0);
									vert[3].pos = Rainbow::Vector4f(0, 29.263f, 33.333f, 0);
									vert[4].pos = Rainbow::Vector4f(66.667f, 29.263f, 33.333f, 0);
									vert[5].pos = Rainbow::Vector4f(0, 0, 0, 0);
									vert[6].pos = Rainbow::Vector4f(100, 0, 0, 0);

									uv[0] = { 0, 0 };
									uv[1] = { 0, 0.3333f };
									uv[2] = { 0.33333f, 0.3333f };
									uv[3] = { 0, 0.6667f };
									uv[4] = { 0.66667f, 0.6667f };
									uv[5] = { 0, 1 };
									uv[6] = { 1, 1 };
								}

								Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
								//unsigned short dir_color = Normal2LightColor(normalVec);
								BlockVector vertcolor;
								vertcolor.v = 0xffffffff;
								vertcolor.w = 0;
								Normalize(normalVec);
								BlockVector normal_dir = PackVertNormal(normalVec);
								for (int oo = 0; oo < 7; oo++)
								{
									vert[oo].uv = { short(uv[oo].x * BLOCKUV_SCALE), short(uv[oo].y * BLOCKUV_SCALE) };
									vert[oo].normal = normal_dir;
									vert[oo].color.SetUInt32(vertcolor.v);
									vertices.push_back(vert[oo]);
								}
								m_mTurnArcFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							}
						}
						else
						{
							for (int o = 0; o <= 1; o++)
							{
								dynamic_array<BlockGeomVert> vertices;
								BlockGeomVert vert[7];
								Rainbow::Vector2f uv[7];
								if (o == 0)//0020
								{
									vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
									vert[1].pos = Rainbow::Vector4f(33.333f, 29.263f, 33.333f, 0);
									vert[2].pos = Rainbow::Vector4f(0, 29.263f, 33.333f, 0);
									vert[3].pos = Rainbow::Vector4f(66.667f, 45.107f, 66.667f, 0);
									vert[4].pos = Rainbow::Vector4f(0, 45.107f, 66.667f, 0);
									vert[5].pos = Rainbow::Vector4f(100, 50, 100, 0);
									vert[6].pos = Rainbow::Vector4f(0, 50, 100, 0);

									uv[0] = { 0, 1 };
									uv[1] = { 0.33333f, 0.6667f };
									uv[2] = { 0, 0.6667f };
									uv[3] = { 0.66667f, 0.3333f };
									uv[4] = { 0, 0.3333f };
									uv[5] = { 1, 0 };
									uv[6] = { 0, 0 };
								}
								else//0021
								{
									vert[0].pos = Rainbow::Vector4f(100, 0, 0, 0);
									vert[1].pos = Rainbow::Vector4f(100, 29.263f, 33.333f, 0);
									vert[2].pos = Rainbow::Vector4f(66.667f, 29.263f, 33.333f, 0);
									vert[3].pos = Rainbow::Vector4f(100, 45.107f, 66.667f, 0);
									vert[4].pos = Rainbow::Vector4f(33.333f, 45.107f, 66.667f, 0);
									vert[5].pos = Rainbow::Vector4f(100, 50, 100, 0);
									vert[6].pos = Rainbow::Vector4f(0, 50, 100, 0);

									uv[0] = { 1, 1 };
									uv[1] = { 1, 0.6667f };
									uv[2] = { 0.66667f, 0.6667f };
									uv[3] = { 1, 0.3333f };
									uv[4] = { 0.33333f, 0.3333f };
									uv[5] = { 1, 0 };
									uv[6] = { 0, 0 };
								}

								Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
								//unsigned short dir_color = Normal2LightColor(normalVec);
								BlockVector vertcolor;
								vertcolor.v = 0xffffffff;
								vertcolor.w = 0;
								Normalize(normalVec);
								BlockVector normal_dir = PackVertNormal(normalVec);
								for (int oo = 0; oo < 7; oo++)
								{
									vert[oo].uv = { short(uv[oo].x * BLOCKUV_SCALE), short(uv[oo].y * BLOCKUV_SCALE) };
									vert[oo].normal = normal_dir;
									vert[oo].color.SetUInt32(vertcolor.v);
									vertices.push_back(vert[oo]);
								}
								m_mTurnArcFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							}
						}
					}
					else
					{
						if (1 == j)
						{
							for (int o = 0; o <= 1; o++)
							{
								dynamic_array<BlockGeomVert> vertices;
								BlockGeomVert vert[7];
								Rainbow::Vector2f uv[7];
								if (o == 0)//1120
								{
									vert[0].pos = Rainbow::Vector4f(100, 50, 100, 0);
									vert[1].pos = Rainbow::Vector4f(100, 54.893f, 66.667f, 0);
									vert[2].pos = Rainbow::Vector4f(66.667f, 54.893f, 66.667f, 0);
									vert[3].pos = Rainbow::Vector4f(100, 70.737f, 33.333f, 0);
									vert[4].pos = Rainbow::Vector4f(33.333f, 70.737f, 33.333f, 0);
									vert[5].pos = Rainbow::Vector4f(100, 100, 0, 0);
									vert[6].pos = Rainbow::Vector4f(0, 100, 0, 0);

									uv[0] = { 1, 1 };
									uv[1] = { 1, 0.6667f };
									uv[2] = { 0.66667f, 0.6667f };
									uv[3] = { 1, 0.3333f };
									uv[4] = { 0.33333f, 0.3333f };
									uv[5] = { 1, 0 };
									uv[6] = { 0, 0 };
								}
								else//1121
								{
									vert[0].pos = Rainbow::Vector4f(0, 50, 100, 0);
									vert[1].pos = Rainbow::Vector4f(33.333f, 54.893f, 66.667f, 0);
									vert[2].pos = Rainbow::Vector4f(0, 54.893f, 66.667f, 0);
									vert[3].pos = Rainbow::Vector4f(66.667f, 70.737f, 33.333f, 0);
									vert[4].pos = Rainbow::Vector4f(0, 70.737f, 33.333f, 0);
									vert[5].pos = Rainbow::Vector4f(100, 100, 0, 0);
									vert[6].pos = Rainbow::Vector4f(0, 100, 0, 0);

									uv[0] = { 0, 1 };
									uv[1] = { 0.33333f, 0.6667f };
									uv[2] = { 0, 0.6667f };
									uv[3] = { 0.66667f, 0.3333f };
									uv[4] = { 0, 0.3333f };
									uv[5] = { 1, 0 };
									uv[6] = { 0, 0 };
								}

								Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
								//unsigned short dir_color = Normal2LightColor(normalVec);
								BlockVector vertcolor;
								vertcolor.v = 0xffffffff;
								vertcolor.w = 0;
								Normalize(normalVec);
								BlockVector normal_dir = PackVertNormal(normalVec);
								for (int oo = 0; oo < 7; oo++)
								{
									vert[oo].uv = { short(uv[oo].x * BLOCKUV_SCALE), short(uv[oo].y * BLOCKUV_SCALE) };
									vert[oo].normal = normal_dir;
									vert[oo].color.SetUInt32(vertcolor.v);
									vertices.push_back(vert[oo]);
								}
								m_mTurnArcFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							}
						}
						else
						{
							for (int o = 0; o <= 1; o++)
							{
								dynamic_array<BlockGeomVert> vertices;
								BlockGeomVert vert[7];
								Rainbow::Vector2f uv[7];
								if (o == 0)//1020
								{
									vert[0].pos = Rainbow::Vector4f(0, 100, 0, 0);
									vert[1].pos = Rainbow::Vector4f(0, 70.737f, 33.333f, 0);
									vert[2].pos = Rainbow::Vector4f(33.333f, 70.737f, 33.333f, 0);
									vert[3].pos = Rainbow::Vector4f(0, 54.893f, 66.667f, 0);
									vert[4].pos = Rainbow::Vector4f(66.667f, 54.893f, 66.667f, 0);
									vert[5].pos = Rainbow::Vector4f(0, 50, 100, 0);
									vert[6].pos = Rainbow::Vector4f(100, 50, 100, 0);

									uv[0] = { 0, 0 };
									uv[1] = { 0, 0.3333f };
									uv[2] = { 0.33333f, 0.3333f };
									uv[3] = { 0, 0.6667f };
									uv[4] = { 0.66667f, 0.6667f };
									uv[5] = { 0, 1 };
									uv[6] = { 1, 1 };
								}

								else//1021
								{
									vert[0].pos = Rainbow::Vector4f(100, 100, 0, 0);
									vert[1].pos = Rainbow::Vector4f(66.667f, 70.737f, 33.333f, 0);
									vert[2].pos = Rainbow::Vector4f(100, 70.737f, 33.333f, 0);
									vert[3].pos = Rainbow::Vector4f(33.333f, 54.893f, 66.667f, 0);
									vert[4].pos = Rainbow::Vector4f(100, 54.893f, 66.667f, 0);
									vert[5].pos = Rainbow::Vector4f(0, 50, 100, 0);
									vert[6].pos = Rainbow::Vector4f(100, 50, 100, 0);

									uv[0] = { 1, 0 };
									uv[1] = { 0.66667f, 0.3333f };
									uv[2] = { 1, 0.3333f };
									uv[3] = { 0.33333f, 0.6667f };
									uv[4] = { 1, 0.6667f };
									uv[5] = { 0, 1 };
									uv[6] = { 1, 1 };
								}

								Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
								//unsigned short dir_color = Normal2LightColor(normalVec);
								BlockVector vertcolor;
								vertcolor.v = 0xffffffff;
								vertcolor.w = 0;
								Normalize(normalVec);
								BlockVector normal_dir = PackVertNormal(normalVec);
								for (int oo = 0; oo < 7; oo++)
								{
									vert[oo].uv = { short(uv[oo].x * BLOCKUV_SCALE), short(uv[oo].y * BLOCKUV_SCALE) };
									vert[oo].normal = normal_dir;
									vert[oo].color.SetUInt32(vertcolor.v);
									vertices.push_back(vert[oo]);
								}
								m_mTurnArcFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							}
						}
					}
				}
				else if (3 == k)
				{
					if (0 == i)
					{
						if (1 == j)
						{
							for (int o = 0; o <= 1; o++)
							{
								dynamic_array<BlockGeomVert> vertices;
								BlockGeomVert vert[7];
								Rainbow::Vector2f uv[7];
								if (o == 0)
								{
									vert[0].pos = Rainbow::Vector4f(100, 50, 0, 0);
									vert[1].pos = Rainbow::Vector4f(100, 45.107f, 33.333f, 0);
									vert[2].pos = Rainbow::Vector4f(66.667f, 45.107f, 33.333f, 0);
									vert[3].pos = Rainbow::Vector4f(100, 29.263f, 66.667f, 0);
									vert[4].pos = Rainbow::Vector4f(33.333f, 29.263f, 66.667f, 0);
									vert[5].pos = Rainbow::Vector4f(100, 0, 100, 0);
									vert[6].pos = Rainbow::Vector4f(0, 0, 100, 0);

									uv[0] = { 0, 0 };
									uv[1] = { 0, 0.3333f };
									uv[2] = { 0.33333f, 0.3333f };
									uv[3] = { 0, 0.6667f };
									uv[4] = { 0.66667f, 0.6667f };
									uv[5] = { 0, 1 };
									uv[6] = { 1, 1 };
								}
								else
								{
									vert[0].pos = Rainbow::Vector4f(0, 50, 0, 0);
									vert[1].pos = Rainbow::Vector4f(33.333f, 45.107f, 33.333f, 0);
									vert[2].pos = Rainbow::Vector4f(0, 45.107f, 33.333f, 0);
									vert[3].pos = Rainbow::Vector4f(66.667f, 29.263f, 66.667f, 0);
									vert[4].pos = Rainbow::Vector4f(0, 29.263f, 66.667f, 0);
									vert[5].pos = Rainbow::Vector4f(100, 0, 100, 0);
									vert[6].pos = Rainbow::Vector4f(0, 0, 100, 0);

									uv[0] = { 1, 0 };
									uv[1] = { 0.66667f, 0.3333f };
									uv[2] = { 1, 0.3333f };
									uv[3] = { 0.33333f, 0.6667f };
									uv[4] = { 1, 0.6667f };
									uv[5] = { 0, 1 };
									uv[6] = { 1, 1 };

								}

								Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
								//unsigned short dir_color = Normal2LightColor(normalVec);
								BlockVector vertcolor;
								vertcolor.v = 0xffffffff;
								vertcolor.w = 0;
								Normalize(normalVec);
								BlockVector normal_dir = PackVertNormal(normalVec);
								for (int oo = 0; oo < 7; oo++)
								{
									vert[oo].uv = { short(uv[oo].x * BLOCKUV_SCALE), short(uv[oo].y * BLOCKUV_SCALE) };
									vert[oo].normal = normal_dir;
									vert[oo].color.SetUInt32(vertcolor.v);
									vertices.push_back(vert[oo]);
								}
								m_mTurnArcFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							}
						}
						else
						{
							for (int o = 0; o <= 1; o++)
							{
								dynamic_array<BlockGeomVert> vertices;
								BlockGeomVert vert[7];
								Rainbow::Vector2f uv[7];
								if (o == 0)//0030
								{
									vert[0].pos = Rainbow::Vector4f(0, 0, 100, 0);
									vert[1].pos = Rainbow::Vector4f(0, 29.263f, 66.667f, 0);
									vert[2].pos = Rainbow::Vector4f(33.333f, 29.263f, 66.667f, 0);
									vert[3].pos = Rainbow::Vector4f(0, 45.107f, 33.333f, 0);
									vert[4].pos = Rainbow::Vector4f(66.667f, 45.107f, 33.333f, 0);
									vert[5].pos = Rainbow::Vector4f(0, 50, 0, 0);
									vert[6].pos = Rainbow::Vector4f(100, 50, 0, 0);

									uv[0] = { 1, 1 };
									uv[1] = { 1, 0.6667f };
									uv[2] = { 0.66667f, 0.6667f };
									uv[3] = { 1, 0.3333f };
									uv[4] = { 0.33333f, 0.3333f };
									uv[5] = { 1, 0 };
									uv[6] = { 0, 0 };
								}
								else//0031
								{
									vert[0].pos = Rainbow::Vector4f(100, 0, 100, 0);
									vert[1].pos = Rainbow::Vector4f(66.667f, 29.263f, 66.667f, 0);
									vert[2].pos = Rainbow::Vector4f(100, 29.263f, 66.667f, 0);
									vert[3].pos = Rainbow::Vector4f(33.333f, 45.107f, 33.333f, 0);
									vert[4].pos = Rainbow::Vector4f(100, 45.107f, 33.333f, 0);
									vert[5].pos = Rainbow::Vector4f(0, 50, 0, 0);
									vert[6].pos = Rainbow::Vector4f(100, 50, 0, 0);

									uv[0] = { 0, 1 };
									uv[1] = { 0.33333f, 0.6667f };
									uv[2] = { 0, 0.6667f };
									uv[3] = { 0.66667f, 0.3333f };
									uv[4] = { 0, 0.3333f };
									uv[5] = { 1, 0 };
									uv[6] = { 0, 0 };
								}

								Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
								//unsigned short dir_color = Normal2LightColor(normalVec);
								BlockVector vertcolor;
								vertcolor.v = 0xffffffff;
								vertcolor.w = 0;
								Normalize(normalVec);
								BlockVector normal_dir = PackVertNormal(normalVec);
								for (int oo = 0; oo < 7; oo++)
								{
									vert[oo].uv = { short(uv[oo].x * BLOCKUV_SCALE), short(uv[oo].y * BLOCKUV_SCALE) };
									vert[oo].normal = normal_dir;
									vert[oo].color.SetUInt32(vertcolor.v);
									vertices.push_back(vert[oo]);
								}
								m_mTurnArcFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							}
						}
					}
					else
					{
						if (1 == j)
						{
							for (int o = 0; o <= 1; o++)
							{
								dynamic_array<BlockGeomVert> vertices;
								BlockGeomVert vert[7];
								Rainbow::Vector2f uv[7];
								if (o == 0)//1130
								{
									vert[0].pos = Rainbow::Vector4f(100, 50, 0, 0);
									vert[1].pos = Rainbow::Vector4f(66.667f, 54.893f, 33.333f, 0);
									vert[2].pos = Rainbow::Vector4f(100, 54.893f, 33.333f, 0);
									vert[3].pos = Rainbow::Vector4f(33.333f, 70.737f, 66.667f, 0);
									vert[4].pos = Rainbow::Vector4f(100, 70.737f, 66.667f, 0);
									vert[5].pos = Rainbow::Vector4f(0, 100, 100, 0);
									vert[6].pos = Rainbow::Vector4f(100, 100, 100, 0);

									uv[0] = { 0, 1 };
									uv[1] = { 0.33333f, 0.6667f };
									uv[2] = { 0, 0.6667f };
									uv[3] = { 0.66667f, 0.3333f };
									uv[4] = { 0, 0.3333f };
									uv[5] = { 1, 0 };
									uv[6] = { 0, 0 };
								}
								else//1131
								{
									vert[0].pos = Rainbow::Vector4f(0, 50, 0, 0);
									vert[1].pos = Rainbow::Vector4f(0, 54.893f, 33.333f, 0);
									vert[2].pos = Rainbow::Vector4f(33.333f, 54.893f, 33.333f, 0);
									vert[3].pos = Rainbow::Vector4f(0, 70.737f, 66.667f, 0);
									vert[4].pos = Rainbow::Vector4f(66.667f, 70.737f, 66.667f, 0);
									vert[5].pos = Rainbow::Vector4f(0, 100, 100, 0);
									vert[6].pos = Rainbow::Vector4f(100, 100, 100, 0);

									uv[0] = { 1, 1 };
									uv[1] = { 1, 0.6667f };
									uv[2] = { 0.66667f, 0.6667f };
									uv[3] = { 1, 0.3333f };
									uv[4] = { 0.33333f, 0.3333f };
									uv[5] = { 1, 0 };
									uv[6] = { 0, 0 };
								}

								Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
								//unsigned short dir_color = Normal2LightColor(normalVec);
								BlockVector vertcolor;
								vertcolor.v = 0xffffffff;
								vertcolor.w = 0;
								Normalize(normalVec);
								BlockVector normal_dir = PackVertNormal(normalVec);
								for (int oo = 0; oo < 7; oo++)
								{
									vert[oo].uv = { short(uv[oo].x * BLOCKUV_SCALE), short(uv[oo].y * BLOCKUV_SCALE) };
									vert[oo].normal = normal_dir;
									vert[oo].color.SetUInt32(vertcolor.v);
									vertices.push_back(vert[oo]);
								}
								m_mTurnArcFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							}
						}
						else
						{
							for (int o = 0; o <= 1; o++)
							{
								dynamic_array<BlockGeomVert> vertices;
								BlockGeomVert vert[7];
								Rainbow::Vector2f uv[7];
								if (o == 0)//1030
								{
									vert[0].pos = Rainbow::Vector4f(0, 100, 100, 0);
									vert[1].pos = Rainbow::Vector4f(33.333f, 70.737f, 66.667f, 0);
									vert[2].pos = Rainbow::Vector4f(0, 70.737f, 66.667f, 0);
									vert[3].pos = Rainbow::Vector4f(66.667f, 54.893f, 33.333f, 0);
									vert[4].pos = Rainbow::Vector4f(0, 54.893f, 33.333f, 0);
									vert[5].pos = Rainbow::Vector4f(100, 50, 0, 0);
									vert[6].pos = Rainbow::Vector4f(0, 50, 0, 0);

									uv[0] = { 1, 0 };
									uv[1] = { 0.66667f, 0.3333f };
									uv[2] = { 1, 0.3333f };
									uv[3] = { 0.33333f, 0.6667f };
									uv[4] = { 1, 0.6667f };
									uv[5] = { 0, 1 };
									uv[6] = { 1, 1 };
								}
								else//1031
								{
									vert[0].pos = Rainbow::Vector4f(100, 100, 100, 0);
									vert[1].pos = Rainbow::Vector4f(100, 70.737f, 66.667f, 0);
									vert[2].pos = Rainbow::Vector4f(66.667f, 70.737f, 66.667f, 0);
									vert[3].pos = Rainbow::Vector4f(100, 54.893f, 33.333f, 0);
									vert[4].pos = Rainbow::Vector4f(33.333f, 54.893f, 33.333f, 0);
									vert[5].pos = Rainbow::Vector4f(100, 50, 0, 0);
									vert[6].pos = Rainbow::Vector4f(0, 50, 0, 0);

									uv[0] = { 0, 0 };
									uv[1] = { 0, 0.3333f };
									uv[2] = { 0.33333f, 0.3333f };
									uv[3] = { 0, 0.6667f };
									uv[4] = { 0.66667f, 0.6667f };
									uv[5] = { 0, 1 };
									uv[6] = { 1, 1 };
								}

								Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
								//unsigned short dir_color = Normal2LightColor(normalVec);
								BlockVector vertcolor;
								vertcolor.v = 0xffffffff;
								vertcolor.w = 0;
								Normalize(normalVec);
								BlockVector normal_dir = PackVertNormal(normalVec);
								for (int oo = 0; oo < 7; oo++)
								{
									vert[oo].uv = { short(uv[oo].x * BLOCKUV_SCALE), short(uv[oo].y * BLOCKUV_SCALE) };
									vert[oo].normal = normal_dir;
									vert[oo].color.SetUInt32(vertcolor.v);
									vertices.push_back(vert[oo]);
								}
								m_mTurnArcFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							}
						}
					}
				}
			}
		}
	}
}


void HorizontalArcHalfPlateMaterial::initPhyModelData()
{
	if (m_mArcWholeFace().size() == 0 || m_mDiamondFace.size() == 0 || m_mBigArcFace.size() == 0
		|| m_mHalfWholeFace().size() == 0 || m_mHalfFace().size() == 0)
	{
		return;
	}
	int triangleFaceId[4] = { 6,2,4,0 };
	for (int i = 0; i < 2; i++)
	{
		for (int j = 0; j < 4; j++)
		{
			ArcPhyModel model;
			dynamic_array<dynamic_array<BlockGeomVert>*> faceList;
			//4 vert
			faceList.push_back(0 == i ? &m_mArcWholeFace()[4] : &m_mArcWholeFace()[5]);
			faceList.push_back(&m_mHalfFace()[ReverseDirection(j) + (i * 4)]);
			faceList.push_back(&m_mDiamondFace[8 * i + triangleFaceId[j]]);
			faceList.push_back(&m_mBigArcFace[4 * i + j]);
			faceList.push_back(&m_mDiamondFace[8 * i + triangleFaceId[j] + 1]);

			int ArcCount = 0;
			for (auto face : faceList)
			{
				dynamic_array<short> indexlist;
				for (auto& vertdata : (*face))
				{
					Rainbow::Vector3f pos(vertdata.pos.x, vertdata.pos.y, vertdata.pos.z);
					short index = -1;
					for (int o = 0; o < model.verts.size(); o++)
					{
						if (model.verts[o] == pos)
						{
							index = o;
							break;
						}
					}
					if (index == -1)
					{
						index = static_cast<short>( model.verts.size());
						model.verts.push_back(pos);
					}
					indexlist.push_back(index);
				}
				dynamic_array<UInt16>* posIndeices = m_dPosIndices;
				if (indexlist.size() == 5)
				{
					posIndeices = m_PosDiamondIndices;
				}
				else if (indexlist.size() == 8)
				{
					posIndeices = m_PosBigArcIndices;
				}
				ArcCount += indexlist.size() - 2;
				for (auto& idxex : (*posIndeices))
				{
					model.idxs.push_back(indexlist[idxex]);
				}
			}
			model.ArcCount = ArcCount;
			m_mPhyModel.insert(make_pair(i * 4 + j, model));
		}
	}

	int speicalFaceId[4] = { 4,0,4,0 };
	for (int i = 0; i < 2; i++)//0 down 1 up
	{
		for (int j = 0; j < 2; j++)//0 凹 1 凸
		{
			for (int k = 0; k < 4; k++)
			{
				int dirlist[2] = { RotateDirPos90(k), RotateDir90(k) };
				for (int q = 0; q < 2; q++)
				{
					auto iter = m_mPhyModel.find(10000 + i * 1000 + j * 100 + k * 10 + dirlist[q]);
					if (iter == m_mPhyModel.end())
					{
						ArcPhyModel model;
						dynamic_array<dynamic_array<BlockGeomVert>*> faceList;
						faceList.push_back(0 == i ? &m_mArcWholeFace()[4] : &m_mArcWholeFace()[5]);
						if (1 == j)
						{
							faceList.push_back(&m_mDiamondFace[8 * i + speicalFaceId[k] + ReverseDirection(dirlist[q])]);
						}
						else
						{
							faceList.push_back(&m_mDiamondFace[8 * i + speicalFaceId[k] + dirlist[q]]);
							faceList.push_back(&m_mHalfFace()[ReverseDirection(k) + i * 4]);
							faceList.push_back(&m_mHalfFace()[ReverseDirection(dirlist[q]) + i * 4]);
						}
						faceList.push_back(&m_mTurnArcFace[i * 1000 + j * 100 + k * 10 + dirlist[q]]);
						faceList.push_back(&m_mTurnArcFace[i * 1000 + j * 100 + dirlist[q] * 10 + k]);

						int ArcCount = 0;
						for (auto face : faceList)
						{
							dynamic_array<short> indexlist;
							for (auto& vertdata : (*face))
							{
								Rainbow::Vector3f pos(vertdata.pos.x, vertdata.pos.y, vertdata.pos.z);
								short index = -1;
								for (int o = 0; o < model.verts.size(); o++)
								{
									if (model.verts[o] == pos)
									{
										index = o;
										break;
									}
								}
								if (index == -1)
								{
									index = static_cast<short>(model.verts.size());
									model.verts.push_back(pos);
								}
								indexlist.push_back(index);
							}
							dynamic_array<UInt16>* posIndeices = m_dPosIndices;
							if (indexlist.size() == 5)
							{
								posIndeices = m_PosDiamondIndices;
							}
							else if (indexlist.size() == 7)
							{
								posIndeices = m_TurnArcIndices;
							}
							ArcCount += indexlist.size() - 2;
							for (auto& idxex : (*posIndeices))
							{
								model.idxs.push_back(indexlist[idxex]);
							}
						}
						model.ArcCount = ArcCount;
						m_mPhyModel.insert(make_pair(10000 + i * 1000 + j * 100 + k * 10 + dirlist[q], model));
					}
				}
			}
		}
	}

	for (int i = 0; i < 2; i++)
	{
		auto iter = m_mPhyModel.find(20000 + i);
		if (iter == m_mPhyModel.end())
		{
			ArcPhyModel model;
			dynamic_array<dynamic_array<BlockGeomVert>*> faceList;
			faceList.push_back(0 == i ? &m_mArcWholeFace()[4] : &m_mArcWholeFace()[5]);
			faceList.push_back(0 == i ? &m_mHalfWholeFace()[1] : &m_mHalfWholeFace()[0]); 
			for (int ii = 0 + i * 4; ii < i * 4 + 4; ii++)
			{
				faceList.push_back(&m_mHalfFace()[ii]);
			}

			int ArcCount = 0;
			for (auto face : faceList)
			{
				dynamic_array<short> indexlist;
				for (auto& vertdata : (*face))
				{
					Rainbow::Vector3f pos(vertdata.pos.x, vertdata.pos.y, vertdata.pos.z);
					short index = -1;
					for (int o = 0; o < model.verts.size(); o++)
					{
						if (model.verts[o] == pos)
						{
							index = o;
							break;
						}
					}
					if (index == -1)
					{
						index = static_cast<short>(model.verts.size());
						model.verts.push_back(pos);
					}
					indexlist.push_back(index);
				}
				dynamic_array<UInt16>* posIndeices = m_dPosIndices;
				if (indexlist.size() == 5)
				{
					posIndeices = m_PosDiamondIndices;
				}
				else if (indexlist.size() == 8)
				{
					posIndeices = m_PosBigArcIndices;
				}
				ArcCount += indexlist.size() - 2;
				for (auto& idxex : (*posIndeices))
				{
					model.idxs.push_back(indexlist[idxex]);
				}
			}
			model.ArcCount = ArcCount;
			m_mPhyModel.insert(make_pair(20000 + i, model));
		}
	}
}

 SectionMesh* HorizontalArcHalfPlateMaterial::createBlockProtoMesh(int protodata)
 {
	 SectionMesh* pmesh = ENG_NEW(SectionMesh)();
	 int blockdata = protodata;

	 const BiomeDef* biome = GetDefManagerProxy()->getBiomeDef(0);

	 char list[5] = { 3,4,4,5,2 };
	 BlockColor facecolor(255, 255, 255, 0);
	 for (int i = 0; i < 5; i++)
	 {
		 DirectionType dir = (DirectionType)(list[i] % 4);
		 RenderBlockMaterial* pmtl = getFaceMtl(biome, dir, blockdata, facecolor);
		 SectionSubMesh* psubmesh = pmesh->getSubMesh(pmtl, true);
		 dynamic_array<BlockGeomVert> vertices;

		 BlockGeomMeshInfo meshinfo;
		 if (i == 0)
		 {
			 meshinfo.vertices = m_mHalfFace()[list[i]];
			 meshinfo.indices = *m_dPosIndices;
		 }
		 else if (i == 1)
		 {
			 meshinfo.vertices = m_mArcWholeFace()[list[i]];
			 meshinfo.indices = *m_dPosIndices;
		 }
		 else if (i < 4)
		 {
			 meshinfo.vertices = m_mDiamondFace[list[i]];
			 meshinfo.indices = *m_PosDiamondIndices;
		 }
		 else
		 {
			 meshinfo.vertices = m_mBigArcFace[list[i]];
			 meshinfo.indices = *m_PosBigArcIndices;
		 }
		 if (psubmesh)
			 psubmesh->addGeomFaceLight(meshinfo, NULL, s_DefaultFaceVertexLights, &facecolor);
	 }
	 return pmesh;
 }

 typedef HorizontalArcHalfPlateMaterial::BlockInstance HorizontalArcHalfPlateMaterialInstance;
 IMPLEMENT_SCENEOBJECTCLASS(HorizontalArcHalfPlateMaterialInstance)
	 MNSandbox::ReflexClassParam<HorizontalArcHalfPlateMaterial::BlockInstance, int> HorizontalArcHalfPlateMaterialInstance::R_Dir(0, "Dir", "Block", &HorizontalArcHalfPlateMaterial::BlockInstance::GetBlockData, &HorizontalArcHalfPlateMaterial::BlockInstance::SetBlockData);




 IMPLEMENT_SCENEOBJECTCLASS(HorizontalArcPlateMaterial)
 float HorizontalArcPlateMaterial::getBlockHeight(int blockdata)
 {
	 if (blockdata & 8)
	 {
		 return 1;
	 }
	 else if (blockdata & 4)
	 {
		 return -0.5f;
	 }
	 return 0.5f;
 }

 void HorizontalArcPlateMaterial::initPhyModelData()
 {
	 if (m_mArcWholeFace().size() == 0 || m_mDiamondFace.size() == 0 || m_mBigArcFace.size() == 0
		 || m_mHalfWholeFace().size() == 0 || m_mHalfFace().size() == 0)
	 {
		 return;
	 }
	 int triangleFaceId[4] = { 6,2,4,0 };
	 for (int i = 0; i < 2; i++)
	 {
		 for (int j = 0; j < 4; j++)
		 {
			 ArcPhyModel model;
			 dynamic_array<dynamic_array<BlockGeomVert>*> faceList;
			 //4 vert
			 faceList.push_back(0 == i ? &m_mArcWholeFace()[4] : &m_mArcWholeFace()[5]);
			 faceList.push_back(&m_mArcWholeFace()[ReverseDirection(j)]);
			 faceList.push_back(&m_mDiamondFace[8 * i + triangleFaceId[j]]);
			 faceList.push_back(&m_mBigArcFace[4 * i + j]);
			 faceList.push_back(&m_mDiamondFace[8 * i + triangleFaceId[j] + 1]);
			 faceList.push_back(&m_mHalfFace()[j + i * 4]);

			 int ArcCount = 0;
			 for (auto face : faceList)
			 {
				 std::vector<short> indexlist;
				 for (auto& vertdata : (*face))
				 {
					 Rainbow::Vector3f pos(vertdata.pos.x, vertdata.pos.y, vertdata.pos.z);
					 short index = -1;
					 for (int o = 0; o < model.verts.size(); o++)
					 {
						 if (model.verts[o] == pos)
						 {
							 index = o;
							 break;
						 }
					 }
					 if (index == -1)
					 {
						 index = static_cast<short>(model.verts.size());
						 model.verts.push_back(pos);
					 }
					 indexlist.push_back(index);
				 }
				 dynamic_array<UInt16>* posIndeices = m_dPosIndices;
				 if (indexlist.size() == 6)
				 {
					 posIndeices = m_PosBigDiamondIndices;
				 }
				 else if (indexlist.size() == 8)
				 {
					 posIndeices = m_PosBigArcIndices;
				 }
				 ArcCount += indexlist.size() - 2;
				 for (auto& idxex : (*posIndeices))
				 {
					 model.idxs.push_back(indexlist[idxex]);
				 }
			 }
			 model.ArcCount = ArcCount;
			 m_mPhyModel.insert(make_pair(i * 4 + j, model));
		 }
	 }

	 int speicalFaceId[4] = { 4,0,4,0 };
	 for (int i = 0; i < 2; i++)//0 down 1 up
	 {
		 for (int j = 0; j < 2; j++)//0 凹 1 凸
		 {
			 for (int k = 0; k < 4; k++)
			 {
				 int dirlist[2] = { RotateDirPos90(k), RotateDir90(k) };
				 for (int q = 0; q < 2; q++)
				 {
					 auto iter = m_mPhyModel.find(10000 + i * 1000 + j * 100 + k * 10 + dirlist[q]);
					 if (iter == m_mPhyModel.end())
					 {
						 ArcPhyModel model;
						 dynamic_array<dynamic_array<BlockGeomVert>*> faceList;
						 faceList.push_back(0 == i ? &m_mArcWholeFace()[4] : &m_mArcWholeFace()[5]);
						 if (1 == j)
						 {
							 faceList.push_back(&m_mDiamondFace[8 * i + speicalFaceId[k] + ReverseDirection(dirlist[q])]);
						 }
						 else
						 {
							 faceList.push_back(&m_mDiamondFace[8 * i + speicalFaceId[k] + dirlist[q]]);
						 }
						 faceList.push_back(&m_mTurnArcFace[i * 1000 + j * 100 + k * 10 + dirlist[q]]);
						 faceList.push_back(&m_mTurnArcFace[i * 1000 + j * 100 + dirlist[q] * 10 + k]);
						 if (j == 0)
						 {
							 faceList.push_back(&m_mArcWholeFace()[ReverseDirection(k)]);
							 faceList.push_back(&m_mArcWholeFace()[ReverseDirection(dirlist[q])]);
						 }
						 else if (j == 1)
						 {
							 faceList.push_back(&m_mHalfFace()[dirlist[q] + i * 4]);
							 faceList.push_back(&m_mHalfFace()[k + i * 4]);
						 }
						 int ArcCount = 0;
						 for (auto face : faceList)
						 {
							 std::vector<short> indexlist;
							 for (auto& vertdata : (*face))
							 {
								 Rainbow::Vector3f pos(vertdata.pos.x, vertdata.pos.y, vertdata.pos.z);
								 short index = -1;
								 for (int o = 0; o < model.verts.size(); o++)
								 {
									 if (model.verts[o] == pos)
									 {
										 index = o;
										 break;
									 }
								 }
								 if (index == -1)
								 {
									 index = static_cast<short>(model.verts.size());
									 model.verts.push_back(pos);
								 }
								 indexlist.push_back(index);
							 }
							 dynamic_array<UInt16>* posIndeices = m_dPosIndices;
							 if (indexlist.size() == 6)
							 {
								 posIndeices = m_PosBigDiamondIndices;
							 }
							 else if (indexlist.size() == 7)
							 {
								 posIndeices = m_TurnArcIndices;
							 }
							 ArcCount += indexlist.size() - 2;
							 for (auto& idxex : (*posIndeices))
							 {
								 model.idxs.push_back(indexlist[idxex]);
							 }
						 }
						 model.ArcCount = ArcCount;
						 m_mPhyModel.insert(make_pair(10000 + i * 1000 + j * 100 + k * 10 + dirlist[q], model));
					 }
				 }
			 }
		 }
	 }
 }

 void HorizontalArcPlateMaterial::initBigArcFaceVertData()
 {
	 for (int i = 0; i < 2; i++)
	 {
		 for (int j = 0; j < 4; j++)
		 {
			 DirectionType dir = (DirectionType)j;
			 dynamic_array<UInt16>* indices = m_PosBigArcIndices;
			 int d = i * 4 + j;
			 dynamic_array<BlockGeomVert> vertices;
			 Rainbow::Vector3f normalVec = (g_DirectionCoord[j] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
			 normalVec.y /= 2.f;
			 //unsigned short dir_color = Normal2LightColor(normalVec);
			 BlockVector vertcolor;
			 vertcolor.v = 0xffffffff;
			 vertcolor.w = 0;
			 Normalize(normalVec);
			 BlockVector normal_dir = PackVertNormal(normalVec);
			 BlockGeomVert vert[8];
			 Rainbow::Vector2f uv[8];
			 if (0 == d)
			 {
				 vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
				 vert[1].pos = Rainbow::Vector4f(0, 0, 100, 0);
				 vert[2].pos = Rainbow::Vector4f(33.333f, 29.263f, 100, 0);
				 vert[3].pos = Rainbow::Vector4f(33.333f, 29.263f, 0, 0);
				 vert[4].pos = Rainbow::Vector4f(66.667f, 45.107f, 0, 0);
				 vert[5].pos = Rainbow::Vector4f(66.667f, 45.107f, 100, 0);
				 vert[6].pos = Rainbow::Vector4f(100, 50, 100, 0);
				 vert[7].pos = Rainbow::Vector4f(100, 50, 0, 0);

				 uv[0] = { 1, 1 };
				 uv[1] = { 0, 1 };
				 uv[2] = { 0, 0.6667f };
				 uv[3] = { 1, 0.6667f };
				 uv[4] = { 1, 0.3333f };
				 uv[5] = { 0, 0.3333f };
				 uv[6] = { 0, 0 };
				 uv[7] = { 1, 0 };
			 }
			 else if (1 == d)
			 {
				 vert[0].pos = Rainbow::Vector4f(0, 50, 0, 0);
				 vert[1].pos = Rainbow::Vector4f(0, 50, 100, 0);
				 vert[2].pos = Rainbow::Vector4f(33.333f, 45.107f, 100, 0);
				 vert[3].pos = Rainbow::Vector4f(33.333f, 45.107f, 0, 0);
				 vert[4].pos = Rainbow::Vector4f(66.667f, 29.263f, 0, 0);
				 vert[5].pos = Rainbow::Vector4f(66.667f, 29.263f, 100, 0);
				 vert[6].pos = Rainbow::Vector4f(100, 0, 100, 0);
				 vert[7].pos = Rainbow::Vector4f(100, 0, 0, 0);

				 uv[0] = { 0, 0 };
				 uv[1] = { 1, 0 };
				 uv[2] = { 1, 0.3333f };
				 uv[3] = { 0, 0.3333f };
				 uv[4] = { 0, 0.6667f };
				 uv[5] = { 1, 0.6667f };
				 uv[6] = { 1, 1 };
				 uv[7] = { 0, 1 };
			 }
			 else if (2 == d)
			 {
				 vert[0].pos = Rainbow::Vector4f(0, 50, 100, 0);
				 vert[1].pos = Rainbow::Vector4f(100, 50, 100, 0);
				 vert[2].pos = Rainbow::Vector4f(100, 45.107f, 66.667f, 0);
				 vert[3].pos = Rainbow::Vector4f(0, 45.107f, 66.667f, 0);
				 vert[4].pos = Rainbow::Vector4f(0, 29.263f, 33.333f, 0);
				 vert[5].pos = Rainbow::Vector4f(100, 29.263f, 33.333f, 0);
				 vert[6].pos = Rainbow::Vector4f(100, 0, 0, 0);
				 vert[7].pos = Rainbow::Vector4f(0, 0, 0, 0);

				 uv[0] = { 0, 0 };
				 uv[1] = { 1, 0 };
				 uv[2] = { 1, 0.3333f };
				 uv[3] = { 0, 0.3333f };
				 uv[4] = { 0, 0.6667f };
				 uv[5] = { 1, 0.6667f };
				 uv[6] = { 1, 1 };
				 uv[7] = { 0, 1 };
			 }
			 else if (3 == d)
			 {
				 vert[0].pos = Rainbow::Vector4f(0, 0, 100, 0);
				 vert[1].pos = Rainbow::Vector4f(100, 0, 100, 0);
				 vert[2].pos = Rainbow::Vector4f(100, 29.263f, 66.667f, 0);
				 vert[3].pos = Rainbow::Vector4f(0, 29.263f, 66.667f, 0);
				 vert[4].pos = Rainbow::Vector4f(0, 45.107f, 33.333f, 0);
				 vert[5].pos = Rainbow::Vector4f(100, 45.107f, 33.333f, 0);
				 vert[6].pos = Rainbow::Vector4f(100, 50, 0, 0);
				 vert[7].pos = Rainbow::Vector4f(0, 50, 0, 0);

				 uv[0] = { 1, 1 };
				 uv[1] = { 0, 1 };
				 uv[2] = { 0, 0.6667f };
				 uv[3] = { 1, 0.6667f };
				 uv[4] = { 1, 0.3333f };
				 uv[5] = { 0, 0.3333f };
				 uv[6] = { 0, 0 };
				 uv[7] = { 1, 0 };
			 }
			 else if (4 == d)
			 {
				 vert[0].pos = Rainbow::Vector4f(100, 50, 0, 0);
				 vert[1].pos = Rainbow::Vector4f(100, 50, 100, 0);
				 vert[2].pos = Rainbow::Vector4f(66.667f, 54.893f, 100, 0);
				 vert[3].pos = Rainbow::Vector4f(66.667f, 54.893f, 0, 0);
				 vert[4].pos = Rainbow::Vector4f(33.333f, 70.737f, 0, 0);
				 vert[5].pos = Rainbow::Vector4f(33.333f, 70.737f, 100, 0);
				 vert[6].pos = Rainbow::Vector4f(0, 100, 100, 0);
				 vert[7].pos = Rainbow::Vector4f(0, 100, 0, 0);

				 uv[0] = { 1, 1 };
				 uv[1] = { 0, 1 };
				 uv[2] = { 0, 0.6667f };
				 uv[3] = { 1, 0.6667f };
				 uv[4] = { 1, 0.3333f };
				 uv[5] = { 0, 0.3333f };
				 uv[6] = { 0, 0 };
				 uv[7] = { 1, 0 };
			 }
			 else if (5 == d)
			 {
				 vert[0].pos = Rainbow::Vector4f(0, 50, 100, 0);
				 vert[1].pos = Rainbow::Vector4f(0, 50, 0, 0);
				 vert[2].pos = Rainbow::Vector4f(33.333f, 54.893f, 0, 0);
				 vert[3].pos = Rainbow::Vector4f(33.333f, 54.893f, 100, 0);
				 vert[4].pos = Rainbow::Vector4f(66.667f, 70.737f, 100, 0);
				 vert[5].pos = Rainbow::Vector4f(66.667f, 70.737f, 0, 0);
				 vert[6].pos = Rainbow::Vector4f(100, 100, 0, 0);
				 vert[7].pos = Rainbow::Vector4f(100, 100, 100, 0);

				 uv[0] = { 1, 1 };
				 uv[1] = { 0, 1 };
				 uv[2] = { 0, 0.6667f };
				 uv[3] = { 1, 0.6667f };
				 uv[4] = { 1, 0.3333f };
				 uv[5] = { 0, 0.3333f };
				 uv[6] = { 0, 0 };
				 uv[7] = { 1, 0 };
			 }
			 else if (6 == d)
			 {
				 vert[0].pos = Rainbow::Vector4f(0, 100, 0, 0);
				 vert[1].pos = Rainbow::Vector4f(100, 100, 0, 0);
				 vert[2].pos = Rainbow::Vector4f(100, 70.737f, 33.333f, 0);
				 vert[3].pos = Rainbow::Vector4f(0, 70.737f, 33.333f, 0);
				 vert[4].pos = Rainbow::Vector4f(0, 54.893f, 66.667f, 0);
				 vert[5].pos = Rainbow::Vector4f(100, 54.893f, 66.667f, 0);
				 vert[6].pos = Rainbow::Vector4f(100, 50, 100, 0);
				 vert[7].pos = Rainbow::Vector4f(0, 50, 100, 0);


				 uv[0] = { 0, 0 };
				 uv[1] = { 1, 0 };
				 uv[2] = { 1, 0.3333f };
				 uv[3] = { 0, 0.3333f };
				 uv[4] = { 0, 0.6667f };
				 uv[5] = { 1, 0.6667f };
				 uv[6] = { 1, 1 };
				 uv[7] = { 0, 1 };
			 }
			 else if (7 == d)
			 {
				 vert[0].pos = Rainbow::Vector4f(0, 50, 0, 0);
				 vert[1].pos = Rainbow::Vector4f(100, 50, 0, 0);
				 vert[2].pos = Rainbow::Vector4f(100, 54.893f, 33.333f, 0);
				 vert[3].pos = Rainbow::Vector4f(0, 54.893f, 33.333f, 0);
				 vert[4].pos = Rainbow::Vector4f(0, 70.737f, 66.667f, 0);
				 vert[5].pos = Rainbow::Vector4f(100, 70.737f, 66.667f, 0);
				 vert[6].pos = Rainbow::Vector4f(100, 100, 100, 0);
				 vert[7].pos = Rainbow::Vector4f(0, 100, 100, 0);

				 uv[0] = { 1, 1 };
				 uv[1] = { 0, 1 };
				 uv[2] = { 0, 0.6667f };
				 uv[3] = { 1, 0.6667f };
				 uv[4] = { 1, 0.3333f };
				 uv[5] = { 0, 0.3333f };
				 uv[6] = { 0, 0 };
				 uv[7] = { 1, 0 };
			 }
			 float yOffset = 50;
			 if (d > 3) yOffset *= -1;
			 for (int oo = 0; oo < 8; oo++)
			 {
				 vert[oo].uv = { short(uv[oo].x * BLOCKUV_SCALE), short(uv[oo].y * BLOCKUV_SCALE) };
				 vert[oo].normal = normal_dir;
				 vert[oo].color.SetUInt32(vertcolor.v);
				 vert[oo].pos.y += yOffset;
				 // 				vert[oo].color = 0xffffffff;
				 vertices.push_back(vert[oo]);
			 }
			 m_mBigArcFace.push_back(vertices);
		 }
	 }
 }

 void HorizontalArcPlateMaterial::initDiamondFaceVertData()
 {
	 for (int i = 0; i < 4; i++)
	 {
		 for (int j = 0; j < 4; j++)
		 {
			 DirectionType dir = (DirectionType)j;
			 dynamic_array<UInt16>* indices = m_PosDiamondIndices;
			 int d = i * 4 + j;
			 //unsigned short dir_color = TriangleNormal2LightColor(g_DirectionCoord[dir].toVector3());
			 dynamic_array<BlockGeomVert> vertices;
			 Rainbow::Vector3f normalVec = g_DirectionCoord[dir].toVector3();
			 //unsigned short dir_color = Normal2LightColor(normalVec);
			 Normalize(normalVec);
			 BlockVector normal_dir = PackVertNormal(normalVec);
			 BlockVector vertcolor;
			 vertcolor.v = 0xffffffff;
			 vertcolor.w = 0;

			 Rainbow::Vector2f uv[6];
			 BlockGeomVert vert[6];
			 if (0 == d)//斜线朝向3 down
			 {
				 vert[0].pos = Rainbow::Vector4f(0, 100, 0, 0);
				 vert[1].pos = Rainbow::Vector4f(0, 0, 0, 0);
				 vert[2].pos = Rainbow::Vector4f(0, 0, 100, 0);
				 vert[3].pos = Rainbow::Vector4f(0, 50, 100, 0);
				 vert[4].pos = Rainbow::Vector4f(0, 79.263f, 66.667f, 0);
				 vert[5].pos = Rainbow::Vector4f(0, 95.107f, 33.333f, 0);
				 uv[0] = { 1, 0 };
				 uv[1] = { 1, 1 };
				 uv[2] = { 0, 1 };
				 uv[3] = { 0, 0.5f };
				 uv[4] = { 0.33333f, 0.20737f };
				 uv[5] = { 0.66667f, 0.04893f };
			 }
			 else if (1 == d)
			 {
				 vert[0].pos = Rainbow::Vector4f(100, 0, 100, 0);
				 vert[1].pos = Rainbow::Vector4f(100, 0, 0, 0);
				 vert[2].pos = Rainbow::Vector4f(100, 100, 0, 0);
				 vert[3].pos = Rainbow::Vector4f(100, 95.107f, 33.333f, 0);
				 vert[4].pos = Rainbow::Vector4f(100, 79.263f, 66.667f, 0);
				 vert[5].pos = Rainbow::Vector4f(100, 50, 100, 0);

				 uv[0] = { 1, 1 };
				 uv[1] = { 0, 1 };
				 uv[2] = { 0, 0 };
				 uv[3] = { 0.33333f, 0.04893f };
				 uv[4] = { 0.66667f, 0.20737f };
				 uv[5] = { 1, 0.5f };
			 }
			 else if (2 == d)
			 {
				 vert[0].pos = Rainbow::Vector4f(100, 0, 0, 0);
				 vert[1].pos = Rainbow::Vector4f(0, 0, 0, 0);
				 vert[2].pos = Rainbow::Vector4f(0, 100, 0, 0);
				 vert[3].pos = Rainbow::Vector4f(33.333f, 95.107f, 0, 0);
				 vert[4].pos = Rainbow::Vector4f(66.667f, 79.263f, 0, 0);
				 vert[5].pos = Rainbow::Vector4f(100, 50, 0, 0);

				 uv[0] = { 1, 1 };
				 uv[1] = { 0, 1 };
				 uv[2] = { 0, 0 };
				 uv[3] = { 0.33333f, 0.04893f };
				 uv[4] = { 0.66667f, 0.20737f };
				 uv[5] = { 1, 0.5f };
			 }
			 else if (3 == d)
			 {
				 vert[0].pos = Rainbow::Vector4f(0, 100, 100, 0);
				 vert[1].pos = Rainbow::Vector4f(0, 0, 100, 0);
				 vert[2].pos = Rainbow::Vector4f(100, 0, 100, 0);
				 vert[3].pos = Rainbow::Vector4f(100, 50, 100, 0);
				 vert[4].pos = Rainbow::Vector4f(66.667f, 79.263f, 100, 0);
				 vert[5].pos = Rainbow::Vector4f(33.333f, 95.107f, 100, 0);

				 uv[0] = { 1, 0 };
				 uv[1] = { 1, 1 };
				 uv[2] = { 0, 1 };
				 uv[3] = { 0, 0.5f };
				 uv[4] = { 0.33333f, 0.20737f };
				 uv[5] = { 0.66667f, 0.04893f };
			 }
			 else if (4 == d)
			 {
				 vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
				 vert[1].pos = Rainbow::Vector4f(0, 0, 100, 0);
				 vert[2].pos = Rainbow::Vector4f(0, 100, 100, 0);
				 vert[3].pos = Rainbow::Vector4f(0, 95.107f, 66.667f, 0);
				 vert[4].pos = Rainbow::Vector4f(0, 79.263f, 33.333f, 0);
				 vert[5].pos = Rainbow::Vector4f(0, 50, 0, 0);

				 uv[0] = { 1, 1 };
				 uv[1] = { 0, 1 };
				 uv[2] = { 0, 0 };
				 uv[3] = { 0.33333f, 0.04893f };
				 uv[4] = { 0.66667f, 0.20737f };
				 uv[5] = { 1, 0.5f };
			 }
			 else if (5 == d)
			 {
				 vert[0].pos = Rainbow::Vector4f(100, 100, 100, 0);
				 vert[1].pos = Rainbow::Vector4f(100, 0, 100, 0);
				 vert[2].pos = Rainbow::Vector4f(100, 0, 0, 0);
				 vert[3].pos = Rainbow::Vector4f(100, 50, 0, 0);
				 vert[4].pos = Rainbow::Vector4f(100, 79.263f, 33.333f, 0);
				 vert[5].pos = Rainbow::Vector4f(100, 95.107f, 66.667f, 0);

				 uv[0] = { 1, 0 };
				 uv[1] = { 1, 1 };
				 uv[2] = { 0, 1 };
				 uv[3] = { 0, 0.5f };
				 uv[4] = { 0.33333f, 0.20737f };
				 uv[5] = { 0.66667f, 0.04893f };
			 }
			 else if (6 == d)
			 {
				 vert[0].pos = Rainbow::Vector4f(100, 100, 0, 0);
				 vert[1].pos = Rainbow::Vector4f(100, 0, 0, 0);
				 vert[2].pos = Rainbow::Vector4f(0, 0, 0, 0);
				 vert[3].pos = Rainbow::Vector4f(0, 50, 0, 0);
				 vert[4].pos = Rainbow::Vector4f(33.333f, 79.263f, 0, 0);
				 vert[5].pos = Rainbow::Vector4f(66.667f, 95.107f, 0, 0);

				 uv[0] = { 1, 0 };
				 uv[1] = { 1, 1 };
				 uv[2] = { 0, 1 };
				 uv[3] = { 0, 0.5f };
				 uv[4] = { 0.33333f, 0.20737f };
				 uv[5] = { 0.66667f, 0.04893f };
			 }
			 else if (7 == d)
			 {
				 vert[0].pos = Rainbow::Vector4f(0, 0, 100, 0);
				 vert[1].pos = Rainbow::Vector4f(100, 0, 100, 0);
				 vert[2].pos = Rainbow::Vector4f(100, 100, 100, 0);
				 vert[3].pos = Rainbow::Vector4f(66.667f, 95.107f, 100, 0);
				 vert[4].pos = Rainbow::Vector4f(33.333f, 79.263f, 100, 0);
				 vert[5].pos = Rainbow::Vector4f(0, 50, 100, 0);
				 uv[0] = { 1, 1 };
				 uv[1] = { 0, 1 };
				 uv[2] = { 0, 0 };
				 uv[3] = { 0.33333f, 0.04893f };
				 uv[4] = { 0.66667f, 0.20737f };
				 uv[5] = { 1, 0.5f };
			 }
			 else if (8 == d)
			 {
				 vert[0].pos = Rainbow::Vector4f(0, 100, 100, 0);
				 vert[1].pos = Rainbow::Vector4f(0, 100, 0, 0);
				 vert[2].pos = Rainbow::Vector4f(0, 0, 0, 0);
				 vert[3].pos = Rainbow::Vector4f(0, 4.893f, 33.333f, 0);
				 vert[4].pos = Rainbow::Vector4f(0, 20.737f, 66.667f, 0);
				 vert[5].pos = Rainbow::Vector4f(0, 50, 100, 0);

				 uv[0] = { 0, 0 };
				 uv[1] = { 1, 0 };
				 uv[2] = { 1, 1 };
				 uv[3] = { 0.66667f, 0.95107f };
				 uv[4] = { 0.33333f, 0.79263f };
				 uv[5] = { 0, 0.5f };
			 }
			 else if (9 == d)
			 {
				 vert[0].pos = Rainbow::Vector4f(100, 0, 0, 0);
				 vert[1].pos = Rainbow::Vector4f(100, 100, 0, 0);
				 vert[2].pos = Rainbow::Vector4f(100, 100, 100, 0);
				 vert[3].pos = Rainbow::Vector4f(100, 50, 100, 0);
				 vert[4].pos = Rainbow::Vector4f(100, 20.737f, 66.667f, 0);
				 vert[5].pos = Rainbow::Vector4f(100, 4.893f, 33.333f, 0);

				 uv[0] = { 0, 1 };
				 uv[1] = { 0, 0 };
				 uv[2] = { 1, 0 };
				 uv[3] = { 1, 0.5f };
				 uv[4] = { 0.66667f, 0.79263f };
				 uv[5] = { 0.33333f, 0.95107f };
			 }
			 else if (10 == d)
			 {
				 vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
				 vert[1].pos = Rainbow::Vector4f(0, 100, 0, 0);
				 vert[2].pos = Rainbow::Vector4f(100, 100, 0, 0);
				 vert[3].pos = Rainbow::Vector4f(100, 50, 0, 0);
				 vert[4].pos = Rainbow::Vector4f(66.667f, 20.737f, 0, 0);
				 vert[5].pos = Rainbow::Vector4f(33.333f, 4.893f, 0, 0);

				 uv[0] = { 0, 1 };
				 uv[1] = { 0, 0 };
				 uv[2] = { 1, 0 };
				 uv[3] = { 1, 0.5f };
				 uv[4] = { 0.66667f, 0.79263f };
				 uv[5] = { 0.33333f, 0.95107f };
			 }
			 else if (11 == d)
			 {
				 vert[0].pos = Rainbow::Vector4f(100, 100, 100, 0);
				 vert[1].pos = Rainbow::Vector4f(0, 100, 100, 0);
				 vert[2].pos = Rainbow::Vector4f(0, 0, 100, 0);
				 vert[3].pos = Rainbow::Vector4f(33.333f, 4.893f, 100, 0);
				 vert[4].pos = Rainbow::Vector4f(66.667f, 20.737f, 100, 0);
				 vert[5].pos = Rainbow::Vector4f(100, 50, 100, 0);

				 uv[0] = { 0, 0 };
				 uv[1] = { 1, 0 };
				 uv[2] = { 1, 1 };
				 uv[3] = { 0.66667f, 0.95107f };
				 uv[4] = { 0.33333f, 0.79263f };
				 uv[5] = { 0, 0.5f };
			 }
			 else if (12 == d)
			 {
				 vert[0].pos = Rainbow::Vector4f(0, 0, 100, 0);
				 vert[1].pos = Rainbow::Vector4f(0, 100, 100, 0);
				 vert[2].pos = Rainbow::Vector4f(0, 100, 0, 0);
				 vert[3].pos = Rainbow::Vector4f(0, 50, 0, 0);
				 vert[4].pos = Rainbow::Vector4f(0, 20.737f, 33.333f, 0);
				 vert[5].pos = Rainbow::Vector4f(0, 4.893f, 66.667f, 0);

				 uv[0] = { 0, 1 };
				 uv[1] = { 0, 0 };
				 uv[2] = { 1, 0 };
				 uv[3] = { 1, 0.5f };
				 uv[4] = { 0.66667f, 0.79263f };
				 uv[5] = { 0.33333f, 0.95107f };
			 }
			 else if (13 == d)
			 {
				 vert[0].pos = Rainbow::Vector4f(100, 100, 0, 0);
				 vert[1].pos = Rainbow::Vector4f(100, 100, 100, 0);
				 vert[2].pos = Rainbow::Vector4f(100, 0, 100, 0);
				 vert[3].pos = Rainbow::Vector4f(100, 4.893f, 66.667f, 0);
				 vert[4].pos = Rainbow::Vector4f(100, 20.737f, 33.333f, 0);
				 vert[5].pos = Rainbow::Vector4f(100, 50, 0, 0);

				 uv[0] = { 0, 0 };
				 uv[1] = { 1, 0 };
				 uv[2] = { 1, 1 };
				 uv[3] = { 0.66667f, 0.95107f };
				 uv[4] = { 0.33333f, 0.79263f };
				 uv[5] = { 0, 0.5f };
			 }
			 else if (14 == d)
			 {
				 vert[0].pos = Rainbow::Vector4f(0, 100, 0, 0);
				 vert[1].pos = Rainbow::Vector4f(100, 100, 0, 0);
				 vert[2].pos = Rainbow::Vector4f(100, 0, 0, 0);
				 vert[3].pos = Rainbow::Vector4f(66.667f, 4.893f, 0, 0);
				 vert[4].pos = Rainbow::Vector4f(33.333f, 20.737f, 0, 0);
				 vert[5].pos = Rainbow::Vector4f(0, 50, 0, 0);

				 uv[0] = { 0, 0 };
				 uv[1] = { 1, 0 };
				 uv[2] = { 1, 1 };
				 uv[3] = { 0.66667f, 0.95107f };
				 uv[4] = { 0.33333f, 0.79263f };
				 uv[5] = { 0, 0.5f };
			 }
			 else if (15 == d)
			 {
				 vert[0].pos = Rainbow::Vector4f(100, 0, 100, 0);
				 vert[1].pos = Rainbow::Vector4f(100, 100, 100, 0);
				 vert[2].pos = Rainbow::Vector4f(0, 100, 100, 0);
				 vert[3].pos = Rainbow::Vector4f(0, 50, 100, 0);
				 vert[4].pos = Rainbow::Vector4f(33.333f, 20.737f, 100, 0);
				 vert[5].pos = Rainbow::Vector4f(66.667f, 4.893f, 100, 0);

				 uv[0] = { 0, 1 };
				 uv[1] = { 0, 0 };
				 uv[2] = { 1, 0 };
				 uv[3] = { 1, 0.5f };
				 uv[4] = { 0.66667f, 0.79263f };
				 uv[5] = { 0.33333f, 0.95107f };
			 }

			 for (int oo = 0; oo < 6; oo++)
			 {
				 vert[oo].uv = { short(uv[oo].x * BLOCKUV_SCALE), short(uv[oo].y * BLOCKUV_SCALE) };
				 vert[oo].normal = normal_dir;
				 vert[oo].color.SetUInt32(vertcolor.v);
				/* int lt1 = (((255 >> 4) & 0xf) * vertcolor.w) >> 5;
				 int lt2 = (((255 >> 20) & 0xf) * vertcolor.w) >> 5;
				 vert[oo].pos.w = (lt1 << 8) | lt2;*/
				 vertices.push_back(vert[oo]);
			 }
			 m_mDiamondFace.push_back(vertices);
		 }
	 }
 }

 void HorizontalArcPlateMaterial::initTurnArcFaceVertData()
 {
	 for (int i = 0; i < 2; i++)//updown
	 {
		 for (int j = 0; j < 2; j++) //0是凹1是凸
		 {
			 for (int k = 0; k < 4; k++)//4个主方向
			 {
				 if (0 == k)
				 {
					 if (0 == i)
					 {
						 if (1 == j)
						 {
							 for (int o = 2; o <= 3; o++)
							 {
								 dynamic_array<BlockGeomVert> vertices;
								 BlockGeomVert vert[7];
								 Rainbow::Vector2f uv[7];
								 if (o == 2)//0102
								 {
									 vert[0].pos = Rainbow::Vector4f(100, 50, 100, 0);
									 vert[1].pos = Rainbow::Vector4f(66.667f, 45.107f, 100, 0);
									 vert[2].pos = Rainbow::Vector4f(66.667f, 45.107f, 66.667f, 0);
									 vert[3].pos = Rainbow::Vector4f(33.333f, 29.263f, 100, 0);
									 vert[4].pos = Rainbow::Vector4f(33.333f, 29.263f, 33.333f, 0);
									 vert[5].pos = Rainbow::Vector4f(0, 0, 100, 0);
									 vert[6].pos = Rainbow::Vector4f(0, 0, 0, 0);
									 uv[0] = { 0, 0 };
									 uv[1] = { 0, 0.3333f };
									 uv[2] = { 0.33333f, 0.3333f };
									 uv[3] = { 0, 0.6667f };
									 uv[4] = { 0.66667f, 0.6667f };
									 uv[5] = { 0, 1 };
									 uv[6] = { 1, 1 };
								 }
								 else//0103
								 {
									 vert[0].pos = Rainbow::Vector4f(100, 50, 0, 0);
									 vert[1].pos = Rainbow::Vector4f(66.667f, 45.107f, 33.333f, 0);
									 vert[2].pos = Rainbow::Vector4f(66.667f, 45.107f, 0, 0);
									 vert[3].pos = Rainbow::Vector4f(33.333f, 29.263f, 66.667f, 0);
									 vert[4].pos = Rainbow::Vector4f(33.333f, 29.263f, 0, 0);
									 vert[5].pos = Rainbow::Vector4f(0, 0, 100, 0);
									 vert[6].pos = Rainbow::Vector4f(0, 0, 0, 0);
									 uv[0] = { 1, 0 };
									 uv[1] = { 0.66667f, 0.3333f };
									 uv[2] = { 1, 0.3333f };
									 uv[3] = { 0.33333f, 0.6667f };
									 uv[4] = { 1, 0.6667f };
									 uv[5] = { 0, 1 };
									 uv[6] = { 1, 1 };
								 }

								 Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
								 //unsigned short dir_color = Normal2LightColor(normalVec);
								 Normalize(normalVec);
								 BlockVector normal_dir = PackVertNormal(normalVec);
								 BlockVector vertcolor;
								 vertcolor.v = 0xffffffff;
								 vertcolor.w = 0;

								 for (int oo = 0; oo < 7; oo++)
								 {
									 vert[oo].uv = { short(uv[oo].x * BLOCKUV_SCALE), short(uv[oo].y * BLOCKUV_SCALE) };
									 vert[oo].normal = normal_dir;
									 vert[oo].color.SetUInt32(vertcolor.v);
									 vert[oo].pos.y += 50;
									 vertices.push_back(vert[oo]);
								 }

								 m_mTurnArcFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							 }
						 }
						 else
						 {
							 for (int o = 2; o <= 3; o++)
							 {
								 dynamic_array<BlockGeomVert> vertices;
								 BlockGeomVert vert[7];
								 Rainbow::Vector2f uv[7];
								 if (o == 2)//0002
								 {
									 vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
									 vert[1].pos = Rainbow::Vector4f(33.333f, 29.263f, 0, 0);
									 vert[2].pos = Rainbow::Vector4f(33.333f, 29.263f, 33.333f, 0);
									 vert[3].pos = Rainbow::Vector4f(66.667f, 45.107f, 0, 0);
									 vert[4].pos = Rainbow::Vector4f(66.667f, 45.107f, 66.667f, 0);
									 vert[5].pos = Rainbow::Vector4f(100, 50, 0, 0);
									 vert[6].pos = Rainbow::Vector4f(100, 50, 100, 0);
									 uv[0] = { 1, 1 };
									 uv[1] = { 1, 0.6667f };
									 uv[2] = { 0.66667f, 0.6667f };
									 uv[3] = { 1, 0.3333f };
									 uv[4] = { 0.33333f, 0.3333f };
									 uv[5] = { 1, 0 };
									 uv[6] = { 0, 0 };
								 }
								 else//0003
								 {
									 vert[0].pos = Rainbow::Vector4f(0, 0, 100, 0);
									 vert[1].pos = Rainbow::Vector4f(33.333f, 29.263f, 66.667f, 0);
									 vert[2].pos = Rainbow::Vector4f(33.333f, 29.263f, 100, 0);
									 vert[3].pos = Rainbow::Vector4f(66.667f, 45.107f, 33.333f, 0);
									 vert[4].pos = Rainbow::Vector4f(66.667f, 45.107f, 100, 0);
									 vert[5].pos = Rainbow::Vector4f(100, 50, 0, 0);
									 vert[6].pos = Rainbow::Vector4f(100, 50, 100, 0);
									 uv[0] = { 0, 1 };
									 uv[1] = { 0.33333f, 0.6667f };
									 uv[2] = { 0, 0.6667f };
									 uv[3] = { 0.66667f, 0.3333f };
									 uv[4] = { 0, 0.3333f };
									 uv[5] = { 1, 0 };
									 uv[6] = { 0, 0 };
								 }

								 Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
								 //unsigned short dir_color = Normal2LightColor(normalVec);
								 Normalize(normalVec);
								 BlockVector normal_dir = PackVertNormal(normalVec);
								 BlockVector vertcolor;
								 vertcolor.v = 0xffffffff;
								 vertcolor.w = 0;
								 for (int oo = 0; oo < 7; oo++)
								 {
									 vert[oo].uv = { short(uv[oo].x * BLOCKUV_SCALE), short(uv[oo].y * BLOCKUV_SCALE) };
									 vert[oo].normal = normal_dir;
									 vert[oo].color.SetUInt32(vertcolor.v);
									 vert[oo].pos.y += 50;
									 vertices.push_back(vert[oo]);
								 }
								 m_mTurnArcFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							 }
						 }
					 }
					 else
					 {
						 if (1 == j)
						 {
							 for (int o = 2; o <= 3; o++)
							 {
								 dynamic_array<BlockGeomVert> vertices;
								 BlockGeomVert vert[7];
								 Rainbow::Vector2f uv[7];
								 if (o == 2)//1102
								 {
									 vert[0].pos = Rainbow::Vector4f(100, 50, 100, 0);
									 vert[1].pos = Rainbow::Vector4f(66.667f, 54.893f, 66.667f, 0);
									 vert[2].pos = Rainbow::Vector4f(66.667f, 54.893f, 100, 0);
									 vert[3].pos = Rainbow::Vector4f(33.333f, 70.737f, 33.333f, 0);
									 vert[4].pos = Rainbow::Vector4f(33.333f, 70.737f, 100, 0);
									 vert[5].pos = Rainbow::Vector4f(0, 100, 0, 0);
									 vert[6].pos = Rainbow::Vector4f(0, 100, 100, 0);
									 uv[0] = { 0, 1 };
									 uv[1] = { 0.33333f, 0.6667f };
									 uv[2] = { 0, 0.6667f };
									 uv[3] = { 0.66667f, 0.3333f };
									 uv[4] = { 0, 0.3333f };
									 uv[5] = { 1, 0 };
									 uv[6] = { 0, 0 };
								 }
								 else//1103
								 {
									 vert[0].pos = Rainbow::Vector4f(100, 50, 0, 0);
									 vert[1].pos = Rainbow::Vector4f(66.667f, 54.893f, 0, 0);
									 vert[2].pos = Rainbow::Vector4f(66.667f, 54.893f, 33.333f, 0);
									 vert[3].pos = Rainbow::Vector4f(33.333f, 70.737f, 0, 0);
									 vert[4].pos = Rainbow::Vector4f(33.333f, 70.737f, 66.667f, 0);
									 vert[5].pos = Rainbow::Vector4f(0, 100, 0, 0);
									 vert[6].pos = Rainbow::Vector4f(0, 100, 100, 0);
									 uv[0] = { 1, 1 };
									 uv[1] = { 1, 0.6667f };
									 uv[2] = { 0.66667f, 0.6667f };
									 uv[3] = { 1, 0.3333f };
									 uv[4] = { 0.33333f, 0.3333f };
									 uv[5] = { 1, 0 };
									 uv[6] = { 0, 0 };
								 }

								 Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
								 //unsigned short dir_color = Normal2LightColor(normalVec);
								 Normalize(normalVec);
								 BlockVector normal_dir = PackVertNormal(normalVec);
								 BlockVector vertcolor;
								 vertcolor.v = 0xffffffff;
								 vertcolor.w = 0;
								 for (int oo = 0; oo < 7; oo++)
								 {
									 vert[oo].uv = { short(uv[oo].x * BLOCKUV_SCALE), short(uv[oo].y * BLOCKUV_SCALE) };
									 vert[oo].normal = normal_dir;
									 vert[oo].color.SetUInt32(vertcolor.v);
									 vert[oo].pos.y -= 50;
									 vertices.push_back(vert[oo]);
								 }
								 m_mTurnArcFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							 }
						 }
						 else
						 {
							 for (int o = 2; o <= 3; o++)
							 {
								 dynamic_array<BlockGeomVert> vertices;
								 BlockGeomVert vert[7];
								 Rainbow::Vector2f uv[7];
								 if (o == 2)//1002
								 {
									 vert[0].pos = Rainbow::Vector4f(0, 100, 0, 0);
									 vert[1].pos = Rainbow::Vector4f(33.333f, 70.737f, 33.333f, 0);
									 vert[2].pos = Rainbow::Vector4f(33.333f, 70.737f, 0, 0);
									 vert[3].pos = Rainbow::Vector4f(66.667f, 54.893f, 66.667f, 0);
									 vert[4].pos = Rainbow::Vector4f(66.667f, 54.893f, 0, 0);
									 vert[5].pos = Rainbow::Vector4f(100, 50, 100, 0);
									 vert[6].pos = Rainbow::Vector4f(100, 50, 0, 0);
									 uv[0] = { 0, 1 };
									 uv[1] = { 0.66667f, 0.3333f };
									 uv[2] = { 1, 0.3333f };
									 uv[3] = { 0.33333f, 0.6667f };
									 uv[4] = { 1, 0.6667f };
									 uv[5] = { 0, 1 };
									 uv[6] = { 1, 1 };
								 }
								 else//1003
								 {
									 vert[0].pos = Rainbow::Vector4f(0, 100, 100, 0);
									 vert[1].pos = Rainbow::Vector4f(33.333f, 70.737f, 100, 0);
									 vert[2].pos = Rainbow::Vector4f(33.333f, 70.737f, 66.667f, 0);
									 vert[3].pos = Rainbow::Vector4f(66.667f, 54.893f, 100, 0);
									 vert[4].pos = Rainbow::Vector4f(66.667f, 54.893f, 33.333f, 0);
									 vert[5].pos = Rainbow::Vector4f(100, 50, 100, 0);
									 vert[6].pos = Rainbow::Vector4f(100, 50, 0, 0);
									 uv[0] = { 0, 0 };
									 uv[1] = { 0, 0.3333f };
									 uv[2] = { 0.33333f, 0.3333f };
									 uv[3] = { 0, 0.6667f };
									 uv[4] = { 0.66667f, 0.6667f };
									 uv[5] = { 0, 1 };
									 uv[6] = { 1, 1 };
								 }

								 Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
								 //unsigned short dir_color = Normal2LightColor(normalVec);
								 Normalize(normalVec);
								 BlockVector normal_dir = PackVertNormal(normalVec);
								 BlockVector vertcolor;
								 vertcolor.v = 0xffffffff;
								 vertcolor.w = 0;
								 for (int oo = 0; oo < 7; oo++)
								 {
									 vert[oo].uv = { short(uv[oo].x * BLOCKUV_SCALE), short(uv[oo].y * BLOCKUV_SCALE) };
									 vert[oo].normal = normal_dir;
									 vert[oo].color.SetUInt32(vertcolor.v);
									 vert[oo].pos.y -= 50;
									 vertices.push_back(vert[oo]);
								 }
								 m_mTurnArcFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							 }
						 }
					 }
				 }
				 else if (1 == k)
				 {
					 if (0 == i)
					 {
						 if (1 == j)
						 {
							 for (int o = 2; o <= 3; o++)
							 {
								 dynamic_array<BlockGeomVert> vertices;
								 BlockGeomVert vert[7];
								 Rainbow::Vector2f uv[7];
								 if (o == 2)//0112
								 {
									 vert[0].pos = Rainbow::Vector4f(0, 50, 100, 0);
									 vert[1].pos = Rainbow::Vector4f(33.333f, 45.107f, 66.667f, 0);
									 vert[2].pos = Rainbow::Vector4f(33.333f, 45.107f, 100, 0);
									 vert[3].pos = Rainbow::Vector4f(66.667f, 29.263f, 33.333f, 0);
									 vert[4].pos = Rainbow::Vector4f(66.667f, 29.263f, 100, 0);
									 vert[5].pos = Rainbow::Vector4f(100, 0, 0, 0);
									 vert[6].pos = Rainbow::Vector4f(100, 0, 100, 0);
									 uv[0] = { 1, 0 };
									 uv[1] = { 0.66667f, 0.3333f };
									 uv[2] = { 1, 0.3333f };
									 uv[3] = { 0.33333f, 0.6667f };
									 uv[4] = { 1, 0.6667f };
									 uv[5] = { 0, 1 };
									 uv[6] = { 1, 1 };
								 }
								 else//0113
								 {
									 vert[0].pos = Rainbow::Vector4f(0, 50, 0, 0);
									 vert[1].pos = Rainbow::Vector4f(33.333f, 45.107f, 0, 0);
									 vert[2].pos = Rainbow::Vector4f(33.333f, 45.107f, 33.333f, 0);
									 vert[3].pos = Rainbow::Vector4f(66.667f, 29.263f, 0, 0);
									 vert[4].pos = Rainbow::Vector4f(66.667f, 29.263f, 66.667f, 0);
									 vert[5].pos = Rainbow::Vector4f(100, 0, 0, 0);
									 vert[6].pos = Rainbow::Vector4f(100, 0, 100, 0);
									 uv[0] = { 0, 0 };
									 uv[1] = { 0, 0.3333f };
									 uv[2] = { 0.33333f, 0.3333f };
									 uv[3] = { 0, 0.6667f };
									 uv[4] = { 0.66667f, 0.6667f };
									 uv[5] = { 0, 1 };
									 uv[6] = { 1, 1 };
								 }

								 Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
								 //unsigned short dir_color = Normal2LightColor(normalVec);
								 Normalize(normalVec);
								 BlockVector normal_dir = PackVertNormal(normalVec);
								 BlockVector vertcolor;
								 vertcolor.v = 0xffffffff;
								 vertcolor.w = 0;
								 for (int oo = 0; oo < 7; oo++)
								 {
									 vert[oo].uv = { short(uv[oo].x * BLOCKUV_SCALE), short(uv[oo].y * BLOCKUV_SCALE) };
									 vert[oo].normal = normal_dir;
									 vert[oo].color.SetUInt32(vertcolor.v);
									 vert[oo].pos.y += 50;
									 vertices.push_back(vert[oo]);
								 }
								 m_mTurnArcFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							 }
						 }
						 else
						 {
							 for (int o = 2; o <= 3; o++)
							 {
								 dynamic_array<BlockGeomVert> vertices;
								 BlockGeomVert vert[7];
								 Rainbow::Vector2f uv[7];
								 if (o == 2)//0012
								 {
									 vert[0].pos = Rainbow::Vector4f(100, 0, 0, 0);
									 vert[1].pos = Rainbow::Vector4f(66.667f, 29.263f, 33.333f, 0);
									 vert[2].pos = Rainbow::Vector4f(66.667f, 29.263f, 0, 0);
									 vert[3].pos = Rainbow::Vector4f(33.333f, 45.107f, 66.667f, 0);
									 vert[4].pos = Rainbow::Vector4f(33.333f, 45.107f, 0, 0);
									 vert[5].pos = Rainbow::Vector4f(0, 50, 100, 0);
									 vert[6].pos = Rainbow::Vector4f(0, 50, 0, 0);
									 uv[0] = { 0, 1 };
									 uv[1] = { 0.33333f, 0.6667f };
									 uv[2] = { 0, 0.6667f };
									 uv[3] = { 0.66667f, 0.3333f };
									 uv[4] = { 0, 0.3333f };
									 uv[5] = { 1, 0 };
									 uv[6] = { 0, 0 };
								 }
								 else//0013
								 {
									 vert[0].pos = Rainbow::Vector4f(100, 0, 100, 0);
									 vert[1].pos = Rainbow::Vector4f(66.667f, 29.263f, 100, 0);
									 vert[2].pos = Rainbow::Vector4f(66.667f, 29.263f, 66.667f, 0);
									 vert[3].pos = Rainbow::Vector4f(33.333f, 45.107f, 100, 0);
									 vert[4].pos = Rainbow::Vector4f(33.333f, 45.107f, 33.333f, 0);
									 vert[5].pos = Rainbow::Vector4f(0, 50, 100, 0);
									 vert[6].pos = Rainbow::Vector4f(0, 50, 0, 0);
									 uv[0] = { 1, 1 };
									 uv[1] = { 1, 0.6667f };
									 uv[2] = { 0.66667f, 0.6667f };
									 uv[3] = { 1, 0.3333f };
									 uv[4] = { 0.33333f, 0.3333f };
									 uv[5] = { 1, 0 };
									 uv[6] = { 0, 0 };
								 }
								 Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
								 //unsigned short dir_color = Normal2LightColor(normalVec);
								 Normalize(normalVec);
								 BlockVector normal_dir = PackVertNormal(normalVec);
								 BlockVector vertcolor;
								 vertcolor.v = 0xffffffff;
								 vertcolor.w = 0;
								 for (int oo = 0; oo < 7; oo++)
								 {
									 vert[oo].uv = { short(uv[oo].x * BLOCKUV_SCALE), short(uv[oo].y * BLOCKUV_SCALE) };
									 vert[oo].normal = normal_dir;
									 vert[oo].color.SetUInt32(vertcolor.v);
									 vert[oo].pos.y += 50;
									 vertices.push_back(vert[oo]);
								 }
								 m_mTurnArcFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							 }
						 }
					 }
					 else
					 {
						 if (1 == j)
						 {
							 for (int o = 2; o <= 3; o++)
							 {
								 dynamic_array<BlockGeomVert> vertices;
								 BlockGeomVert vert[7];
								 Rainbow::Vector2f uv[7];
								 if (o == 2)//1112
								 {
									 vert[0].pos = Rainbow::Vector4f(0, 50, 100, 0);
									 vert[1].pos = Rainbow::Vector4f(33.333f, 54.893f, 100, 0);
									 vert[2].pos = Rainbow::Vector4f(33.333f, 54.893f, 66.667f, 0);
									 vert[3].pos = Rainbow::Vector4f(66.667f, 70.737f, 100, 0);
									 vert[4].pos = Rainbow::Vector4f(66.667f, 70.737f, 33.333f, 0);
									 vert[5].pos = Rainbow::Vector4f(100, 100, 100, 0);
									 vert[6].pos = Rainbow::Vector4f(100, 100, 0, 0);

									 uv[0] = { 1, 1 };
									 uv[1] = { 1, 0.6667f };
									 uv[2] = { 0.66667f, 0.6667f };
									 uv[3] = { 1, 0.3333f };
									 uv[4] = { 0.33333f, 0.3333f };
									 uv[5] = { 1, 0 };
									 uv[6] = { 0, 0 };
								 }
								 else//1113
								 {
									 vert[0].pos = Rainbow::Vector4f(0, 50, 0, 0);
									 vert[1].pos = Rainbow::Vector4f(33.333f, 54.893f, 33.333f, 0);
									 vert[2].pos = Rainbow::Vector4f(33.333f, 54.893f, 0, 0);
									 vert[3].pos = Rainbow::Vector4f(66.667f, 70.737f, 66.667f, 0);
									 vert[4].pos = Rainbow::Vector4f(66.667f, 70.737f, 0, 0);
									 vert[5].pos = Rainbow::Vector4f(100, 100, 100, 0);
									 vert[6].pos = Rainbow::Vector4f(100, 100, 0, 0);

									 uv[0] = { 0, 1 };
									 uv[1] = { 0.33333f, 0.6667f };
									 uv[2] = { 0, 0.6667f };
									 uv[3] = { 0.66667f, 0.3333f };
									 uv[4] = { 0, 0.3333f };
									 uv[5] = { 1, 0 };
									 uv[6] = { 0, 0 };
								 }

								 Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
								 //unsigned short dir_color = Normal2LightColor(normalVec);
								 BlockVector vertcolor;
								 vertcolor.v = 0xffffffff;
								 vertcolor.w = 0;
								 Normalize(normalVec);
								 BlockVector normal_dir = PackVertNormal(normalVec);
								 for (int oo = 0; oo < 7; oo++)
								 {
									 vert[oo].uv = { short(uv[oo].x * BLOCKUV_SCALE), short(uv[oo].y * BLOCKUV_SCALE) };
									 vert[oo].normal = normal_dir;
									 vert[oo].color.SetUInt32(vertcolor.v);
									 vert[oo].pos.y -= 50;
									 vertices.push_back(vert[oo]);
								 }
								 m_mTurnArcFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							 }
						 }
						 else
						 {
							 for (int o = 2; o <= 3; o++)
							 {
								 dynamic_array<BlockGeomVert> vertices;
								 BlockGeomVert vert[7];
								 Rainbow::Vector2f uv[7];
								 if (o == 2)//1012
								 {
									 vert[0].pos = Rainbow::Vector4f(100, 100, 0, 0);
									 vert[1].pos = Rainbow::Vector4f(66.667f, 70.737f, 0, 0);
									 vert[2].pos = Rainbow::Vector4f(66.667f, 70.737f, 33.333f, 0);
									 vert[3].pos = Rainbow::Vector4f(33.333f, 54.893f, 0, 0);
									 vert[4].pos = Rainbow::Vector4f(33.333f, 54.893f, 66.667f, 0);
									 vert[5].pos = Rainbow::Vector4f(0, 50, 0, 0);
									 vert[6].pos = Rainbow::Vector4f(0, 50, 100, 0);

									 uv[0] = { 0, 0 };
									 uv[1] = { 0, 0.3333f };
									 uv[2] = { 0.33333f, 0.3333f };
									 uv[3] = { 0, 0.6667f };
									 uv[4] = { 0.66667f, 0.6667f };
									 uv[5] = { 0, 1 };
									 uv[6] = { 1, 1 };
								 }
								 else//1013
								 {
									 vert[0].pos = Rainbow::Vector4f(100, 100, 100, 0);
									 vert[1].pos = Rainbow::Vector4f(66.667f, 70.737f, 66.667f, 0);
									 vert[2].pos = Rainbow::Vector4f(66.667f, 70.737f, 100, 0);
									 vert[3].pos = Rainbow::Vector4f(33.333f, 54.893f, 33.333f, 0);
									 vert[4].pos = Rainbow::Vector4f(33.333f, 54.893f, 100, 0);
									 vert[5].pos = Rainbow::Vector4f(0, 50, 0, 0);
									 vert[6].pos = Rainbow::Vector4f(0, 50, 100, 0);

									 uv[0] = { 1, 0 };
									 uv[1] = { 0.66667f, 0.3333f };
									 uv[2] = { 1, 0.3333f };
									 uv[3] = { 0.33333f, 0.6667f };
									 uv[4] = { 1, 0.6667f };
									 uv[5] = { 0, 1 };
									 uv[6] = { 1, 1 };
								 }

								 Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
								 //unsigned short dir_color = Normal2LightColor(normalVec);
								 BlockVector vertcolor;
								 vertcolor.v = 0xffffffff;
								 vertcolor.w = 0;
								 Normalize(normalVec);
								 BlockVector normal_dir = PackVertNormal(normalVec);
								 for (int oo = 0; oo < 7; oo++)
								 {
									 vert[oo].uv = { short(uv[oo].x * BLOCKUV_SCALE), short(uv[oo].y * BLOCKUV_SCALE) };
									 vert[oo].normal = normal_dir;
									 vert[oo].color.SetUInt32(vertcolor.v);
									 vert[oo].pos.y -= 50;
									 vertices.push_back(vert[oo]);
								 }
								 m_mTurnArcFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							 }
						 }
					 }
				 }
				 else if (2 == k)
				 {
					 if (0 == i)
					 {
						 if (1 == j)
						 {
							 for (int o = 0; o <= 1; o++)
							 {
								 dynamic_array<BlockGeomVert> vertices;
								 BlockGeomVert vert[7];
								 Rainbow::Vector2f uv[7];
								 if (o == 0)//0120
								 {
									 vert[0].pos = Rainbow::Vector4f(100, 50, 100, 0);
									 vert[1].pos = Rainbow::Vector4f(66.667f, 45.107f, 66.667f, 0);
									 vert[2].pos = Rainbow::Vector4f(100, 45.107f, 66.667f, 0);
									 vert[3].pos = Rainbow::Vector4f(33.333f, 29.263f, 33.333f, 0);
									 vert[4].pos = Rainbow::Vector4f(100, 29.263f, 33.333f, 0);
									 vert[5].pos = Rainbow::Vector4f(0, 0, 0, 0);
									 vert[6].pos = Rainbow::Vector4f(100, 0, 0, 0);

									 uv[0] = { 1, 0 };
									 uv[1] = { 0.66667f, 0.3333f };
									 uv[2] = { 1, 0.3333f };
									 uv[3] = { 0.33333f, 0.6667f };
									 uv[4] = { 1, 0.6667f };
									 uv[5] = { 0, 1 };
									 uv[6] = { 1, 1 };
								 }
								 else//0121
								 {
									 vert[0].pos = Rainbow::Vector4f(0, 50, 100, 0);
									 vert[1].pos = Rainbow::Vector4f(0, 45.107f, 66.667f, 0);
									 vert[2].pos = Rainbow::Vector4f(33.333f, 45.107f, 66.667f, 0);
									 vert[3].pos = Rainbow::Vector4f(0, 29.263f, 33.333f, 0);
									 vert[4].pos = Rainbow::Vector4f(66.667f, 29.263f, 33.333f, 0);
									 vert[5].pos = Rainbow::Vector4f(0, 0, 0, 0);
									 vert[6].pos = Rainbow::Vector4f(100, 0, 0, 0);

									 uv[0] = { 0, 0 };
									 uv[1] = { 0, 0.3333f };
									 uv[2] = { 0.33333f, 0.3333f };
									 uv[3] = { 0, 0.6667f };
									 uv[4] = { 0.66667f, 0.6667f };
									 uv[5] = { 0, 1 };
									 uv[6] = { 1, 1 };
								 }

								 Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
								 //unsigned short dir_color = Normal2LightColor(normalVec);
								 BlockVector vertcolor;
								 vertcolor.v = 0xffffffff;
								 vertcolor.w = 0;
								 Normalize(normalVec);
								 BlockVector normal_dir = PackVertNormal(normalVec);
								 for (int oo = 0; oo < 7; oo++)
								 {
									 vert[oo].uv = { short(uv[oo].x * BLOCKUV_SCALE), short(uv[oo].y * BLOCKUV_SCALE) };
									 vert[oo].normal = normal_dir;
									 vert[oo].color.SetUInt32(vertcolor.v);
									 vert[oo].pos.y += 50;
									 vertices.push_back(vert[oo]);
								 }
								 m_mTurnArcFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							 }
						 }
						 else
						 {
							 for (int o = 0; o <= 1; o++)
							 {
								 dynamic_array<BlockGeomVert> vertices;
								 BlockGeomVert vert[7];
								 Rainbow::Vector2f uv[7];
								 if (o == 0)//0020
								 {
									 vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
									 vert[1].pos = Rainbow::Vector4f(33.333f, 29.263f, 33.333f, 0);
									 vert[2].pos = Rainbow::Vector4f(0, 29.263f, 33.333f, 0);
									 vert[3].pos = Rainbow::Vector4f(66.667f, 45.107f, 66.667f, 0);
									 vert[4].pos = Rainbow::Vector4f(0, 45.107f, 66.667f, 0);
									 vert[5].pos = Rainbow::Vector4f(100, 50, 100, 0);
									 vert[6].pos = Rainbow::Vector4f(0, 50, 100, 0);

									 uv[0] = { 0, 1 };
									 uv[1] = { 0.33333f, 0.6667f };
									 uv[2] = { 0, 0.6667f };
									 uv[3] = { 0.66667f, 0.3333f };
									 uv[4] = { 0, 0.3333f };
									 uv[5] = { 1, 0 };
									 uv[6] = { 0, 0 };
								 }
								 else//0021
								 {
									 vert[0].pos = Rainbow::Vector4f(100, 0, 0, 0);
									 vert[1].pos = Rainbow::Vector4f(100, 29.263f, 33.333f, 0);
									 vert[2].pos = Rainbow::Vector4f(66.667f, 29.263f, 33.333f, 0);
									 vert[3].pos = Rainbow::Vector4f(100, 45.107f, 66.667f, 0);
									 vert[4].pos = Rainbow::Vector4f(33.333f, 45.107f, 66.667f, 0);
									 vert[5].pos = Rainbow::Vector4f(100, 50, 100, 0);
									 vert[6].pos = Rainbow::Vector4f(0, 50, 100, 0);

									 uv[0] = { 1, 1 };
									 uv[1] = { 1, 0.6667f };
									 uv[2] = { 0.66667f, 0.6667f };
									 uv[3] = { 1, 0.3333f };
									 uv[4] = { 0.33333f, 0.3333f };
									 uv[5] = { 1, 0 };
									 uv[6] = { 0, 0 };
								 }

								 Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
								 //unsigned short dir_color = Normal2LightColor(normalVec);
								 BlockVector vertcolor;
								 vertcolor.v = 0xffffffff;
								 vertcolor.w = 0;
								 Normalize(normalVec);
								 BlockVector normal_dir = PackVertNormal(normalVec);
								 for (int oo = 0; oo < 7; oo++)
								 {
									 vert[oo].uv = { short(uv[oo].x * BLOCKUV_SCALE), short(uv[oo].y * BLOCKUV_SCALE) };
									 vert[oo].normal = normal_dir;
									 vert[oo].color.SetUInt32(vertcolor.v);
									 vert[oo].pos.y += 50;
									 vertices.push_back(vert[oo]);
								 }
								 m_mTurnArcFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							 }
						 }
					 }
					 else
					 {
						 if (1 == j)
						 {
							 for (int o = 0; o <= 1; o++)
							 {
								 dynamic_array<BlockGeomVert> vertices;
								 BlockGeomVert vert[7];
								 Rainbow::Vector2f uv[7];
								 if (o == 0)//1120
								 {
									 vert[0].pos = Rainbow::Vector4f(100, 50, 100, 0);
									 vert[1].pos = Rainbow::Vector4f(100, 54.893f, 66.667f, 0);
									 vert[2].pos = Rainbow::Vector4f(66.667f, 54.893f, 66.667f, 0);
									 vert[3].pos = Rainbow::Vector4f(100, 70.737f, 33.333f, 0);
									 vert[4].pos = Rainbow::Vector4f(33.333f, 70.737f, 33.333f, 0);
									 vert[5].pos = Rainbow::Vector4f(100, 100, 0, 0);
									 vert[6].pos = Rainbow::Vector4f(0, 100, 0, 0);

									 uv[0] = { 1, 1 };
									 uv[1] = { 1, 0.6667f };
									 uv[2] = { 0.66667f, 0.6667f };
									 uv[3] = { 1, 0.3333f };
									 uv[4] = { 0.33333f, 0.3333f };
									 uv[5] = { 1, 0 };
									 uv[6] = { 0, 0 };
								 }
								 else//1121
								 {
									 vert[0].pos = Rainbow::Vector4f(0, 50, 100, 0);
									 vert[1].pos = Rainbow::Vector4f(33.333f, 54.893f, 66.667f, 0);
									 vert[2].pos = Rainbow::Vector4f(0, 54.893f, 66.667f, 0);
									 vert[3].pos = Rainbow::Vector4f(66.667f, 70.737f, 33.333f, 0);
									 vert[4].pos = Rainbow::Vector4f(0, 70.737f, 33.333f, 0);
									 vert[5].pos = Rainbow::Vector4f(100, 100, 0, 0);
									 vert[6].pos = Rainbow::Vector4f(0, 100, 0, 0);

									 uv[0] = { 0, 1 };
									 uv[1] = { 0.33333f, 0.6667f };
									 uv[2] = { 0, 0.6667f };
									 uv[3] = { 0.66667f, 0.3333f };
									 uv[4] = { 0, 0.3333f };
									 uv[5] = { 1, 0 };
									 uv[6] = { 0, 0 };
								 }

								 Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
								 //unsigned short dir_color = Normal2LightColor(normalVec);
								 BlockVector vertcolor;
								 vertcolor.v = 0xffffffff;
								 vertcolor.w = 0;
								 Normalize(normalVec);
								 BlockVector normal_dir = PackVertNormal(normalVec);
								 for (int oo = 0; oo < 7; oo++)
								 {
									 vert[oo].uv = { short(uv[oo].x * BLOCKUV_SCALE), short(uv[oo].y * BLOCKUV_SCALE) };
									 vert[oo].normal = normal_dir;
									 vert[oo].color.SetUInt32(vertcolor.v);
									 vert[oo].pos.y -= 50;
									 vertices.push_back(vert[oo]);
								 }
								 m_mTurnArcFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							 }
						 }
						 else
						 {
							 for (int o = 0; o <= 1; o++)
							 {
								 dynamic_array<BlockGeomVert> vertices;
								 BlockGeomVert vert[7];
								 Rainbow::Vector2f uv[7];
								 if (o == 0)//1020
								 {
									 vert[0].pos = Rainbow::Vector4f(0, 100, 0, 0);
									 vert[1].pos = Rainbow::Vector4f(0, 70.737f, 33.333f, 0);
									 vert[2].pos = Rainbow::Vector4f(33.333f, 70.737f, 33.333f, 0);
									 vert[3].pos = Rainbow::Vector4f(0, 54.893f, 66.667f, 0);
									 vert[4].pos = Rainbow::Vector4f(66.667f, 54.893f, 66.667f, 0);
									 vert[5].pos = Rainbow::Vector4f(0, 50, 100, 0);
									 vert[6].pos = Rainbow::Vector4f(100, 50, 100, 0);

									 uv[0] = { 0, 0 };
									 uv[1] = { 0, 0.3333f };
									 uv[2] = { 0.33333f, 0.3333f };
									 uv[3] = { 0, 0.6667f };
									 uv[4] = { 0.66667f, 0.6667f };
									 uv[5] = { 0, 1 };
									 uv[6] = { 1, 1 };
								 }

								 else//1021
								 {
									 vert[0].pos = Rainbow::Vector4f(100, 100, 0, 0);
									 vert[1].pos = Rainbow::Vector4f(66.667f, 70.737f, 33.333f, 0);
									 vert[2].pos = Rainbow::Vector4f(100, 70.737f, 33.333f, 0);
									 vert[3].pos = Rainbow::Vector4f(33.333f, 54.893f, 66.667f, 0);
									 vert[4].pos = Rainbow::Vector4f(100, 54.893f, 66.667f, 0);
									 vert[5].pos = Rainbow::Vector4f(0, 50, 100, 0);
									 vert[6].pos = Rainbow::Vector4f(100, 50, 100, 0);

									 uv[0] = { 1, 0 };
									 uv[1] = { 0.66667f, 0.3333f };
									 uv[2] = { 1, 0.3333f };
									 uv[3] = { 0.33333f, 0.6667f };
									 uv[4] = { 1, 0.6667f };
									 uv[5] = { 0, 1 };
									 uv[6] = { 1, 1 };
								 }

								 Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
								 //unsigned short dir_color = Normal2LightColor(normalVec);
								 BlockVector vertcolor;
								 vertcolor.v = 0xffffffff;
								 vertcolor.w = 0;
								 Normalize(normalVec);
								 BlockVector normal_dir = PackVertNormal(normalVec);
								 for (int oo = 0; oo < 7; oo++)
								 {
									 vert[oo].uv = { short(uv[oo].x * BLOCKUV_SCALE), short(uv[oo].y * BLOCKUV_SCALE) };
									 vert[oo].normal = normal_dir;
									 vert[oo].color.SetUInt32(vertcolor.v);
									 vert[oo].pos.y -= 50;
									 vertices.push_back(vert[oo]);
								 }
								 m_mTurnArcFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							 }
						 }
					 }
				 }
				 else if (3 == k)
				 {
					 if (0 == i)
					 {
						 if (1 == j)
						 {
							 for (int o = 0; o <= 1; o++)
							 {
								 dynamic_array<BlockGeomVert> vertices;
								 BlockGeomVert vert[7];
								 Rainbow::Vector2f uv[7];
								 if (o == 0)
								 {
									 vert[0].pos = Rainbow::Vector4f(100, 50, 0, 0);
									 vert[1].pos = Rainbow::Vector4f(100, 45.107f, 33.333f, 0);
									 vert[2].pos = Rainbow::Vector4f(66.667f, 45.107f, 33.333f, 0);
									 vert[3].pos = Rainbow::Vector4f(100, 29.263f, 66.667f, 0);
									 vert[4].pos = Rainbow::Vector4f(33.333f, 29.263f, 66.667f, 0);
									 vert[5].pos = Rainbow::Vector4f(100, 0, 100, 0);
									 vert[6].pos = Rainbow::Vector4f(0, 0, 100, 0);

									 uv[0] = { 0, 0 };
									 uv[1] = { 0, 0.3333f };
									 uv[2] = { 0.33333f, 0.3333f };
									 uv[3] = { 0, 0.6667f };
									 uv[4] = { 0.66667f, 0.6667f };
									 uv[5] = { 0, 1 };
									 uv[6] = { 1, 1 };
								 }
								 else
								 {
									 vert[0].pos = Rainbow::Vector4f(0, 50, 0, 0);
									 vert[1].pos = Rainbow::Vector4f(33.333f, 45.107f, 33.333f, 0);
									 vert[2].pos = Rainbow::Vector4f(0, 45.107f, 33.333f, 0);
									 vert[3].pos = Rainbow::Vector4f(66.667f, 29.263f, 66.667f, 0);
									 vert[4].pos = Rainbow::Vector4f(0, 29.263f, 66.667f, 0);
									 vert[5].pos = Rainbow::Vector4f(100, 0, 100, 0);
									 vert[6].pos = Rainbow::Vector4f(0, 0, 100, 0);

									 uv[0] = { 1, 0 };
									 uv[1] = { 0.66667f, 0.3333f };
									 uv[2] = { 1, 0.3333f };
									 uv[3] = { 0.33333f, 0.6667f };
									 uv[4] = { 1, 0.6667f };
									 uv[5] = { 0, 1 };
									 uv[6] = { 1, 1 };

								 }

								 Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
								 //unsigned short dir_color = Normal2LightColor(normalVec);
								 BlockVector vertcolor;
								 vertcolor.v = 0xffffffff;
								 vertcolor.w = 0;
								 Normalize(normalVec);
								 BlockVector normal_dir = PackVertNormal(normalVec);
								 for (int oo = 0; oo < 7; oo++)
								 {
									 vert[oo].uv = { short(uv[oo].x * BLOCKUV_SCALE), short(uv[oo].y * BLOCKUV_SCALE) };
									 vert[oo].normal = normal_dir;
									 vert[oo].color.SetUInt32(vertcolor.v);
									 vert[oo].pos.y += 50;
									 vertices.push_back(vert[oo]);
								 }
								 m_mTurnArcFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							 }
						 }
						 else
						 {
							 for (int o = 0; o <= 1; o++)
							 {
								 dynamic_array<BlockGeomVert> vertices;
								 BlockGeomVert vert[7];
								 Rainbow::Vector2f uv[7];
								 if (o == 0)//0030
								 {
									 vert[0].pos = Rainbow::Vector4f(0, 0, 100, 0);
									 vert[1].pos = Rainbow::Vector4f(0, 29.263f, 66.667f, 0);
									 vert[2].pos = Rainbow::Vector4f(33.333f, 29.263f, 66.667f, 0);
									 vert[3].pos = Rainbow::Vector4f(0, 45.107f, 33.333f, 0);
									 vert[4].pos = Rainbow::Vector4f(66.667f, 45.107f, 33.333f, 0);
									 vert[5].pos = Rainbow::Vector4f(0, 50, 0, 0);
									 vert[6].pos = Rainbow::Vector4f(100, 50, 0, 0);

									 uv[0] = { 1, 1 };
									 uv[1] = { 1, 0.6667f };
									 uv[2] = { 0.66667f, 0.6667f };
									 uv[3] = { 1, 0.3333f };
									 uv[4] = { 0.33333f, 0.3333f };
									 uv[5] = { 1, 0 };
									 uv[6] = { 0, 0 };
								 }
								 else//0031
								 {
									 vert[0].pos = Rainbow::Vector4f(100, 0, 100, 0);
									 vert[1].pos = Rainbow::Vector4f(66.667f, 29.263f, 66.667f, 0);
									 vert[2].pos = Rainbow::Vector4f(100, 29.263f, 66.667f, 0);
									 vert[3].pos = Rainbow::Vector4f(33.333f, 45.107f, 33.333f, 0);
									 vert[4].pos = Rainbow::Vector4f(100, 45.107f, 33.333f, 0);
									 vert[5].pos = Rainbow::Vector4f(0, 50, 0, 0);
									 vert[6].pos = Rainbow::Vector4f(100, 50, 0, 0);

									 uv[0] = { 0, 1 };
									 uv[1] = { 0.33333f, 0.6667f };
									 uv[2] = { 0, 0.6667f };
									 uv[3] = { 0.66667f, 0.3333f };
									 uv[4] = { 0, 0.3333f };
									 uv[5] = { 1, 0 };
									 uv[6] = { 0, 0 };
								 }

								 Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
								 //unsigned short dir_color = Normal2LightColor(normalVec);
								 BlockVector vertcolor;
								 vertcolor.v = 0xffffffff;
								 vertcolor.w = 0;
								 Normalize(normalVec);
								 BlockVector normal_dir = PackVertNormal(normalVec);
								 for (int oo = 0; oo < 7; oo++)
								 {
									 vert[oo].uv = { short(uv[oo].x * BLOCKUV_SCALE), short(uv[oo].y * BLOCKUV_SCALE) };
									 vert[oo].normal = normal_dir;
									 vert[oo].color.SetUInt32(vertcolor.v);
									 vert[oo].pos.y += 50;
									 vertices.push_back(vert[oo]);
								 }
								 m_mTurnArcFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							 }
						 }
					 }
					 else
					 {
						 if (1 == j)
						 {
							 for (int o = 0; o <= 1; o++)
							 {
								 dynamic_array<BlockGeomVert> vertices;
								 BlockGeomVert vert[7];
								 Rainbow::Vector2f uv[7];
								 if (o == 0)//1130
								 {
									 vert[0].pos = Rainbow::Vector4f(100, 50, 0, 0);
									 vert[1].pos = Rainbow::Vector4f(66.667f, 54.893f, 33.333f, 0);
									 vert[2].pos = Rainbow::Vector4f(100, 54.893f, 33.333f, 0);
									 vert[3].pos = Rainbow::Vector4f(33.333f, 70.737f, 66.667f, 0);
									 vert[4].pos = Rainbow::Vector4f(100, 70.737f, 66.667f, 0);
									 vert[5].pos = Rainbow::Vector4f(0, 100, 100, 0);
									 vert[6].pos = Rainbow::Vector4f(100, 100, 100, 0);

									 uv[0] = { 0, 1 };
									 uv[1] = { 0.33333f, 0.6667f };
									 uv[2] = { 0, 0.6667f };
									 uv[3] = { 0.66667f, 0.3333f };
									 uv[4] = { 0, 0.3333f };
									 uv[5] = { 1, 0 };
									 uv[6] = { 0, 0 };
								 }
								 else//1131
								 {
									 vert[0].pos = Rainbow::Vector4f(0, 50, 0, 0);
									 vert[1].pos = Rainbow::Vector4f(0, 54.893f, 33.333f, 0);
									 vert[2].pos = Rainbow::Vector4f(33.333f, 54.893f, 33.333f, 0);
									 vert[3].pos = Rainbow::Vector4f(0, 70.737f, 66.667f, 0);
									 vert[4].pos = Rainbow::Vector4f(66.667f, 70.737f, 66.667f, 0);
									 vert[5].pos = Rainbow::Vector4f(0, 100, 100, 0);
									 vert[6].pos = Rainbow::Vector4f(100, 100, 100, 0);

									 uv[0] = { 1, 1 };
									 uv[1] = { 1, 0.6667f };
									 uv[2] = { 0.66667f, 0.6667f };
									 uv[3] = { 1, 0.3333f };
									 uv[4] = { 0.33333f, 0.3333f };
									 uv[5] = { 1, 0 };
									 uv[6] = { 0, 0 };
								 }

								 Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
								 //unsigned short dir_color = Normal2LightColor(normalVec);
								 BlockVector vertcolor;
								 vertcolor.v = 0xffffffff;
								 vertcolor.w = 0;
								 Normalize(normalVec);
								 BlockVector normal_dir = PackVertNormal(normalVec);
								 for (int oo = 0; oo < 7; oo++)
								 {
									 vert[oo].uv = { short(uv[oo].x * BLOCKUV_SCALE), short(uv[oo].y * BLOCKUV_SCALE) };
									 vert[oo].normal = normal_dir;
									 vert[oo].color.SetUInt32(vertcolor.v);
									 vert[oo].pos.y -= 50;
									 vertices.push_back(vert[oo]);
								 }
								 m_mTurnArcFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							 }
						 }
						 else
						 {
							 for (int o = 0; o <= 1; o++)
							 {
								 dynamic_array<BlockGeomVert> vertices;
								 BlockGeomVert vert[7];
								 Rainbow::Vector2f uv[7];
								 if (o == 0)//1030
								 {
									 vert[0].pos = Rainbow::Vector4f(0, 100, 100, 0);
									 vert[1].pos = Rainbow::Vector4f(33.333f, 70.737f, 66.667f, 0);
									 vert[2].pos = Rainbow::Vector4f(0, 70.737f, 66.667f, 0);
									 vert[3].pos = Rainbow::Vector4f(66.667f, 54.893f, 33.333f, 0);
									 vert[4].pos = Rainbow::Vector4f(0, 54.893f, 33.333f, 0);
									 vert[5].pos = Rainbow::Vector4f(100, 50, 0, 0);
									 vert[6].pos = Rainbow::Vector4f(0, 50, 0, 0);

									 uv[0] = { 1, 0 };
									 uv[1] = { 0.66667f, 0.3333f };
									 uv[2] = { 1, 0.3333f };
									 uv[3] = { 0.33333f, 0.6667f };
									 uv[4] = { 1, 0.6667f };
									 uv[5] = { 0, 1 };
									 uv[6] = { 1, 1 };
								 }
								 else//1031
								 {
									 vert[0].pos = Rainbow::Vector4f(100, 100, 100, 0);
									 vert[1].pos = Rainbow::Vector4f(100, 70.737f, 66.667f, 0);
									 vert[2].pos = Rainbow::Vector4f(66.667f, 70.737f, 66.667f, 0);
									 vert[3].pos = Rainbow::Vector4f(100, 54.893f, 33.333f, 0);
									 vert[4].pos = Rainbow::Vector4f(33.333f, 54.893f, 33.333f, 0);
									 vert[5].pos = Rainbow::Vector4f(100, 50, 0, 0);
									 vert[6].pos = Rainbow::Vector4f(0, 50, 0, 0);

									 uv[0] = { 0, 0 };
									 uv[1] = { 0, 0.3333f };
									 uv[2] = { 0.33333f, 0.3333f };
									 uv[3] = { 0, 0.6667f };
									 uv[4] = { 0.66667f, 0.6667f };
									 uv[5] = { 0, 1 };
									 uv[6] = { 1, 1 };
								 }

								 Rainbow::Vector3f normalVec = (g_DirectionCoord[k] + g_DirectionCoord[i ? 4 : 5] * 2).toVector3();
								 //unsigned short dir_color = Normal2LightColor(normalVec);
								 BlockVector vertcolor;
								 vertcolor.v = 0xffffffff;
								 vertcolor.w = 0;
								 Normalize(normalVec);
								 BlockVector normal_dir = PackVertNormal(normalVec);
								 for (int oo = 0; oo < 7; oo++)
								 {
									 vert[oo].uv = { short(uv[oo].x * BLOCKUV_SCALE), short(uv[oo].y * BLOCKUV_SCALE) };
									 vert[oo].normal = normal_dir;
									 vert[oo].color.SetUInt32(vertcolor.v);
									 vert[oo].pos.y -= 50;
									 vertices.push_back(vert[oo]);
								 }
								 m_mTurnArcFace.insert(make_pair(/*Rainbow::Vector4f(i, j, k, o)*/i * 1000 + j * 100 + k * 10 + o, vertices));
							 }
						 }
					 }
				 }
			 }
		 }
	 }
 }


 void HorizontalArcPlateMaterial::dropBlockAsItem(World* pworld, const WCoord& blockpos, int blockdata, BLOCK_MINE_TYPE droptype, float chance, int uin/* = -1 */)
 {
	 auto block = pworld->getBlock(blockpos);
	 for (int i = 0; i < 3; i++) 
	 {
		 CubeBlockMaterial::dropBlockAsItem(pworld, blockpos, blockdata, droptype, chance, uin);
	 }
	 if (blockdata == 15)
	 {
		 CubeBlockMaterial::dropBlockAsItem(pworld, blockpos, blockdata, droptype, chance, uin);
	 }
 }

 int HorizontalArcPlateMaterial::getPhisicMeshVerts(BaseSection* psection, const WCoord& blockpos, dynamic_array<Rainbow::Vector3f>& verts, dynamic_array<UInt16>& idxs)
 {
#ifdef IWORLD_SERVER_BUILD
	 if (!m_mPhyModel.size())
	 {
		 initVertData();
	 }
#endif
	 int blockdata = psection->getBlock(blockpos).getData();
	 if (blockdata < 8)
	 {
		 auto pblock = psection->getBlock(blockpos);
		 int warp = -1;
		 int turnDir = -1;
		 DirectionType curDir = DirectionType(blockdata & 3);
		 auto frontBlock = psection->getNeighborBlock(blockpos, g_DirectionCoord[curDir]);
		 if (pblock.getResID() == frontBlock.getResID() && frontBlock.getData() < 8 && ((pblock.getData() & 4) == (frontBlock.getData() & 4)))
		 {
			 int frontDir = frontBlock.getData() & 3;
			 if (frontDir != curDir && frontDir != ReverseDirection(curDir))
			 {
				 warp = 0;
				 turnDir = frontDir;
			 }
		 }
		 if (warp == -1)
		 {
			 auto backBlock = psection->getNeighborBlock(blockpos, g_DirectionCoord[ReverseDirection(curDir)]);
			 if (pblock.getResID() == backBlock.getResID() && backBlock.getData() < 8 && ((pblock.getData() & 4) == (backBlock.getData() & 4)))
			 {
				 int backDir = backBlock.getData() & 3;
				 if (backDir != curDir && backDir != ReverseDirection(curDir))
				 {
					 warp = 1;
					 turnDir = backDir;
				 }
			 }
		 }
		 if (warp == -1)
		 {
			 if (m_mPhyModel.find(blockdata) != m_mPhyModel.end())
			 {
				 ArcPhyModel* pTag = &m_mPhyModel[blockdata];
				 verts = pTag->verts;
				 idxs = pTag->idxs;
				 return  pTag->ArcCount;
			 }
		 }
		 else
		 {
			 int downUp = (blockdata & 8) ? 1 : 0;
			 if (m_mPhyModel.find(10000 + downUp * 1000 + warp * 100 + curDir * 10 + turnDir) != m_mPhyModel.end())
			 {
				 ArcPhyModel* pTag = &m_mPhyModel[10000 + downUp * 1000 + warp * 100 + curDir * 10 + turnDir];
				 verts = pTag->verts;
				 idxs = pTag->idxs;
				 return  pTag->ArcCount;
			 }
		 }
	 }
	 return 0;
 }

 SectionMesh* HorizontalArcPlateMaterial::createBlockProtoMesh(int protodata)
 {
	 SectionMesh* pmesh = ENG_NEW(SectionMesh)();
	 int blockdata = protodata;

	 const BiomeDef* biome = GetDefManagerProxy()->getBiomeDef(0);

	 char list[6] = { 2,4,3,4,5,2 };
	 BlockColor facecolor(255, 255, 255, 0);
	 for (int i = 0; i < 6; i++)
	 {
		 DirectionType dir = (DirectionType)(list[i] % 4);
		 RenderBlockMaterial* pmtl = getFaceMtl(biome, dir, blockdata, facecolor);
		 SectionSubMesh* psubmesh = pmesh->getSubMesh(pmtl, true);
		 dynamic_array<BlockGeomVert> vertices;

		 BlockGeomMeshInfo meshinfo;
		 if (i == 0)
		 {
			 meshinfo.vertices = m_mHalfFace()[list[i]];
			 meshinfo.indices = *m_dPosIndices;
		 }
		 else if (i <= 2)
		 {
			 meshinfo.vertices = m_mArcWholeFace()[list[i]];
			 meshinfo.indices = *m_dPosIndices;
		 }
		 else if (i <= 4)
		 {
			 meshinfo.vertices = m_mDiamondFace[list[i]];
			 meshinfo.indices = *m_PosBigDiamondIndices;
		 }
		 else
		 {
			 meshinfo.vertices = m_mBigArcFace[list[i]];
			 meshinfo.indices = *m_PosBigArcIndices;
		 }
		 if (psubmesh)
			 psubmesh->addGeomFaceLight(meshinfo, NULL, s_DefaultFaceVertexLights, &facecolor);
	 }
	 return pmesh;
 }

 void HorizontalArcPlateMaterial::createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh)
 {
	 auto psection = data.m_SharedSectionData;
#ifndef IWORLD_SERVER_BUILD
	 FaceVertexLight faceVertexLight;
	 Block pblock = psection->getBlock(blockpos);

	 int curblockdata = pblock.getData();
	 int curDir = curblockdata & 3;

	 float blockheight = getBlockHeight(curblockdata);
	 DirectionType specialdir = DIR_NOT_INIT;

	 if (blockheight > 0 && blockheight < 1.0f) specialdir = DIR_POS_Y;
	 else if (blockheight<0 && blockheight>-1.0f) specialdir = DIR_NEG_Y;

	 const BiomeDef* biome = GetDefManagerProxy()->getBiomeDef(1);

	 std::vector<int> wholeFace;
	 std::vector<int> halfWholeFace;
	 std::vector<int> halfFace;
	 std::vector<int> triangleFace;
	 std::vector<int> slantFace;
	 dynamic_array<int> turnslantFace;
	 if (curblockdata & 8)
	 {
		 CubeBlockMaterial::createBlockMesh(data, blockpos, poutmesh);
		 return;
	 }
	 else
	 {
		 int warp = -1;
		 int turnDir = -1;
		 auto frontBlock = psection->getNeighborBlock(blockpos, g_DirectionCoord[curDir]);
		 if (pblock.getResID() == frontBlock.getResID() && frontBlock.getData() < 8 && ((pblock.getData() & 4) == (frontBlock.getData() & 4)))
		 {
			 int frontDir = frontBlock.getData() & 3;
			 if (frontDir != curDir && frontDir != ReverseDirection(curDir))
			 {
				 warp = 0;
				 turnDir = frontDir;
			 }
		 }

		 if (warp == -1)
		 {
			 auto backBlock = psection->getNeighborBlock(blockpos, g_DirectionCoord[ReverseDirection(curDir)]);
			 if (pblock.getResID() == backBlock.getResID() && backBlock.getData() < 8 && ((pblock.getData() & 4) == (backBlock.getData() & 4)))
			 {
				 int backDir = backBlock.getData() & 3;
				 if (backDir != curDir && backDir != ReverseDirection(curDir))
				 {
					 warp = 1;
					 turnDir = backDir;
				 }
			 }
		 }
		 int add = 0;
		 if (specialdir == DIR_POS_Y)
		 {
			 wholeFace.push_back(4);
		 }
		 else
		 {
			 wholeFace.push_back(5);
			 add = 4;
		 }
		 if (warp == -1)
		 {
			 wholeFace.push_back(ReverseDirection(curDir));
		 }

		 if (curDir == DIR_NEG_X)
		 {
			 if (specialdir == DIR_POS_Y)
			 {
				 if (warp == -1)
				 {
					 triangleFace.push_back(6);
					 triangleFace.push_back(7);
					 slantFace.push_back(0);
				 }
				 else
				 {
					 if (1 == warp)
					 {
						 triangleFace.push_back(ReverseDirection(turnDir) + 4);//4+3   7  6
						 triangleFace.push_back(ReverseDirection(curDir) + (1 - (turnDir % 2)) * 4);
					 }
					 else
					 {
						 triangleFace.push_back(turnDir + 4);//4+3
						 triangleFace.push_back(((turnDir - 1) % 2) * 4);    //4  0
					 }
					 turnslantFace.push_back(warp * 100 + curDir * 10 + turnDir);
					 turnslantFace.push_back(warp * 100 + turnDir * 10 + curDir);
				 }
			 }
			 else
			 {
				 if (warp == -1)
				 {
					 triangleFace.push_back(14);//3*4+2
					 triangleFace.push_back(15);//3*4+3
					 slantFace.push_back(4);
				 }
				 else
				 {
					 if (1 == warp)
					 {
						 triangleFace.push_back(ReverseDirection(turnDir) + 12);//3*4+3
						 triangleFace.push_back(ReverseDirection(curDir) + (1 - (turnDir % 2)) * 4 + 8);
					 }
					 else
					 {
						 triangleFace.push_back(turnDir + 12);//3*4+3
						 triangleFace.push_back(curDir + (1 - (turnDir % 2)) * 4 + 8);  // 12   8
					 }
					 turnslantFace.push_back(1000 + warp * 100 + curDir * 10 + turnDir);
					 turnslantFace.push_back(1000 + warp * 100 + turnDir * 10 + curDir);
				 }
			 }
		 }
		 else if (curDir == DIR_POS_X)
		 {
			 if (specialdir == DIR_POS_Y)
			 {
				 if (warp == -1)
				 {
					 triangleFace.push_back(2);
					 triangleFace.push_back(3);
					 slantFace.push_back(1);
				 }
				 else
				 {
					 if (1 == warp)
					 {
						 triangleFace.push_back(ReverseDirection(turnDir)); //==3   2
						 triangleFace.push_back(ReverseDirection(curDir) + (1 - (turnDir % 2)) * 4);
					 }
					 else
					 {
						 triangleFace.push_back(turnDir);
						 triangleFace.push_back(curDir + ((turnDir + 1) % 2) * 4); //5 1
					 }
					 turnslantFace.push_back(warp * 100 + curDir * 10 + turnDir);
					 turnslantFace.push_back(warp * 100 + turnDir * 10 + curDir);
				 }
			 }
			 else
			 {
				 if (warp == -1)
				 {
					 triangleFace.push_back(10);//2*4+2
					 triangleFace.push_back(11);//2*4+3
					 slantFace.push_back(5);
				 }
				 else
				 {
					 if (1 == warp)
					 {
						 triangleFace.push_back(ReverseDirection(turnDir) + 8);//2*4+3
						 triangleFace.push_back(ReverseDirection(curDir) + (1 - (turnDir % 2)) * 4 + 8);
					 }
					 else
					 {
						 triangleFace.push_back(turnDir + 8);//2*4+3
						 triangleFace.push_back(curDir + (1 - (turnDir % 2)) * 4 + 8); //13  9
					 }
					 turnslantFace.push_back(1000 + warp * 100 + curDir * 10 + turnDir);
					 turnslantFace.push_back(1000 + warp * 100 + turnDir * 10 + curDir);
				 }
			 }
		 }
		 else if (curDir == DIR_NEG_Z)
		 {
			 if (specialdir == DIR_POS_Y)
			 {
				 if (warp == -1)
				 {
					 triangleFace.push_back(4);
					 triangleFace.push_back(5);
					 slantFace.push_back(2);
				 }
				 else
				 {
					 if (1 == warp)
					 {
						 triangleFace.push_back(ReverseDirection(turnDir) + 4);//==4
						 triangleFace.push_back(ReverseDirection(curDir) + (1 - (turnDir % 2)) * 4);
					 }
					 else
					 {
						 triangleFace.push_back(turnDir + 4);
						 triangleFace.push_back(curDir + ((turnDir + 1) % 2) * 4);  //6 2
					 }
					 turnslantFace.push_back(warp * 100 + curDir * 10 + turnDir);
					 turnslantFace.push_back(warp * 100 + turnDir * 10 + curDir);
				 }
			 }
			 else
			 {
				 if (warp == -1)
				 {
					 triangleFace.push_back(12);//3*4+0
					 triangleFace.push_back(13);//3*4+1
					 slantFace.push_back(6);
				 }
				 else
				 {
					 if (1 == warp)
					 {
						 triangleFace.push_back(ReverseDirection(turnDir) + 12);//3*4+1
						 triangleFace.push_back(ReverseDirection(curDir) + (1 - (turnDir % 2)) * 4 + 8);
					 }
					 else
					 {
						 triangleFace.push_back(turnDir + 12);//3*4+1
					 }
					 turnslantFace.push_back(1000 + warp * 100 + curDir * 10 + turnDir);
					 turnslantFace.push_back(1000 + warp * 100 + turnDir * 10 + curDir);
				 }
			 }
		 }
		 else if (curDir == DIR_POS_Z)
		 {
			 if (specialdir == DIR_POS_Y)
			 {
				 if (warp == -1)
				 {
					 triangleFace.push_back(0);
					 triangleFace.push_back(1);
					 slantFace.push_back(3);
				 }
				 else
				 {
					 if (1 == warp)
					 {
						 triangleFace.push_back(ReverseDirection(turnDir)); //ReverseDirection(turnDir)=1
						 triangleFace.push_back(ReverseDirection(curDir) + (1 - (turnDir % 2)) * 4);
					 }
					 else
					 {
						 triangleFace.push_back(turnDir);
						 triangleFace.push_back(curDir + ((turnDir + 1) % 2) * 4);  // 3 7
					 }
					 turnslantFace.push_back(warp * 100 + curDir * 10 + turnDir);
					 turnslantFace.push_back(warp * 100 + turnDir * 10 + curDir);
				 }
			 }
			 else
			 {
				 if (warp == -1)
				 {
					 triangleFace.push_back(8);//2*4+0
					 triangleFace.push_back(9);//2*4+1
					 slantFace.push_back(7);
				 }
				 else
				 {
					 if (1 == warp)
					 {
						 triangleFace.push_back(ReverseDirection(turnDir) + 8);//2*4+1
						 triangleFace.push_back(ReverseDirection(curDir) + (1 - (turnDir % 2)) * 4 + 8);
					 }
					 else
					 {
						 triangleFace.push_back(turnDir + 8);//2*4+1
					 }
					 turnslantFace.push_back(1000 + warp * 100 + curDir * 10 + turnDir);
					 turnslantFace.push_back(1000 + warp * 100 + turnDir * 10 + curDir);
				 }
			 }
		 }
		 if (warp == 0)
		 {
			 wholeFace.push_back(ReverseDirection(curDir));
			 wholeFace.push_back(ReverseDirection(turnDir));
// 			 halfFace.push_back(turnDir + add);
		 }
		 else if (warp == 1)
		 {
			 halfFace.push_back(turnDir + add);
			 halfFace.push_back(curDir + add);
		 }
		 else
		 {
			 halfFace.push_back(curDir + add);
// 			 for (int ii = 0; ii < 4; ii++)
// 			 {
// 				 if (ReverseDirection(curDir) != ii)
// 				 {
// 					 halfFace.push_back(ii + add);
// 				 }
// 			 }
		 }
	 }
	 BlockColor facecolor(255, 255, 255, 0);


	 for (auto& d : wholeFace)
	 {
		 DirectionType dir = (DirectionType)d;
		 // 		if (m_DisableCoverFaceOpt || dir == specialdir || !psection->getNeighborCover(blockpos, this, curblockdata, dir))
		 {
			 bool flipQuad = psection->getCubeFaceVertexLight(blockpos, dir, faceVertexLight);
			 dynamic_array<UInt16>* indices = /*flipQuad ? &m_dNegIndices : */m_dPosIndices;

			 RenderBlockMaterial* pmtl = getFaceMtl(biome, dir, pblock.getData(), facecolor);
			 if (pmtl == NULL)
				 continue;
			 SectionSubMesh* psubmesh = poutmesh->getSubMesh(pmtl);
			 const float* uvtile = nullptr;
			 if (psubmesh && !psubmesh->IsIgnoreTileUV())
				 uvtile = pmtl->getUVTile();
			 BlockGeomMeshInfo mesh;

			 mesh.vertices = m_mArcWholeFace()[d];
			 mesh.indices = *indices;
			 unsigned int avelt[4];
			 getAvelt(data, blockpos, dir, avelt);
			 for (int n = 0; n < mesh.vertices.size(); n++)
			 {
				 auto& vert = mesh.vertices[n];
				 InitBlockVertLight(vert, avelt[n], uvtile);
			 }
			 if (psubmesh) psubmesh->addGeomFaceLight(mesh, &blockpos, faceVertexLight, &facecolor, pmtl->getUVTile());
		 }
	 }
	 for (auto& d : halfWholeFace)
	 {
		 DirectionType dir = (DirectionType)d;
		 // 		if (m_DisableCoverFaceOpt || dir == specialdir || !psection->getNeighborCover(blockpos, this, curblockdata, dir))
		 {
			 bool flipQuad = psection->getCubeFaceVertexLight(blockpos, dir, faceVertexLight);

			 dynamic_array<UInt16>* indices = m_dPosIndices;

			 RenderBlockMaterial* pmtl = getFaceMtl(biome, dir, pblock.getData(), facecolor);
			 if (pmtl == NULL)
				 continue;
			 SectionSubMesh* psubmesh = poutmesh->getSubMesh(pmtl);
			 const float* uvtile = nullptr;
			 if (psubmesh && !psubmesh->IsIgnoreTileUV())
				 uvtile = pmtl->getUVTile();
			 BlockGeomMeshInfo mesh;

			 mesh.vertices = m_mHalfWholeFace()[d];
			 mesh.indices = *indices;
			 unsigned int avelt[4];
			 getAvelt(data, blockpos, dir, avelt);
			 for (int n = 0; n < mesh.vertices.size(); n++)
			 {
				 auto& vert = mesh.vertices[n];
				 InitBlockVertLight(vert, avelt[n], uvtile);
			 }
			 if (psubmesh) psubmesh->addGeomFaceLight(mesh, &blockpos, faceVertexLight, &facecolor, pmtl->getUVTile());
		 }
	 }
	 for (auto& d : halfFace)
	 {
		 DirectionType dir = (DirectionType)(d % 4);
		 // 		if (m_DisableCoverFaceOpt || dir == specialdir || !psection->getNeighborCover(blockpos, this, curblockdata, dir))
		 {
			 bool flipQuad = psection->getCubeFaceVertexLight(blockpos, dir, faceVertexLight);
			 dynamic_array<UInt16>* indices = m_dPosIndices;

			 RenderBlockMaterial* pmtl = getFaceMtl(biome, dir, pblock.getData(), facecolor);
			 if (pmtl == NULL)
				 continue;
			 SectionSubMesh* psubmesh = poutmesh->getSubMesh(pmtl);
			 const float* uvtile = nullptr;
			 if (psubmesh && !psubmesh->IsIgnoreTileUV())
				 uvtile = pmtl->getUVTile();
			 BlockGeomMeshInfo mesh;

			 mesh.vertices = m_mHalfFace()[d];
			 mesh.indices = *indices;
			 unsigned int avelt[4];
			 getAvelt(data, blockpos, dir, avelt);
			 for (int n = 0; n < mesh.vertices.size(); n++)
			 {
				 auto& vert = mesh.vertices[n];
				 InitBlockVertLight(vert, avelt[n], uvtile);
			 }
			 if (psubmesh) psubmesh->addGeomFaceLight(mesh, &blockpos, faceVertexLight, &facecolor, pmtl->getUVTile());
		 }
	 }
	 for (auto& d : triangleFace)
	 {
		 DirectionType dir = (DirectionType)(d % 4);
		 bool flipQuad = psection->getCubeFaceVertexLight(blockpos, dir, faceVertexLight);

		 dynamic_array<UInt16>* indices = m_PosBigDiamondIndices;
		 RenderBlockMaterial* pmtl = getFaceMtl(biome, dir, pblock.getData(), facecolor);
		 if (pmtl == NULL)
			 continue;
		 SectionSubMesh* psubmesh = poutmesh->getSubMesh(pmtl);
		 const float* uvtile = nullptr;
		 if (psubmesh && !psubmesh->IsIgnoreTileUV())
			 uvtile = pmtl->getUVTile();
		 BlockGeomMeshInfo mesh;
		 mesh.vertices = m_mDiamondFace[d];
		 mesh.indices = *indices;
		 unsigned int avelt[4];
		 getAvelt(data, blockpos, dir, avelt);
		 for (int n = 0; n < mesh.vertices.size(); n++)
		 {
			 auto& vert = mesh.vertices[n];
			 int aveltMe = 0;
			 int aveltNeight[3] = { 0 };
			 int avelt = 0;
			 if (vert.pos.y > 0 && specialdir == DIR_POS_Y || vert.pos.y == 0 && specialdir == DIR_NEG_Y)
			 {
				 auto selfPos = blockpos + g_DirectionCoord[specialdir] + g_DirectionCoord[dir];
				 bool isXdir = dir < 2;
				 int xdir = isXdir ? dir : vert.pos.x == 0 ? 0 : 1;
				 int zdir = !isXdir ? dir : vert.pos.z == 0 ? 2 : 3;
				 aveltMe = psection->getLight2(selfPos, true);
				 aveltNeight[0] = psection->getLight2(selfPos + g_DirectionCoord[xdir], true);
				 aveltNeight[1] = psection->getLight2(selfPos + g_DirectionCoord[zdir], true);
				 aveltNeight[2] = psection->getLight2(selfPos + g_DirectionCoord[xdir] + g_DirectionCoord[zdir], true);
				 avelt = ((aveltMe + aveltNeight[0] + aveltNeight[1] + aveltNeight[2]) >> 2) & 0xff00ff;
			 }
			 else
			 {
				 auto selfPos = blockpos + g_DirectionCoord[dir];
				 int sideDir = specialdir == DIR_NEG_Y ? DIR_POS_Y : DIR_NEG_Y;
				 aveltMe = psection->getLight2(selfPos, true);
				 aveltNeight[0] = psection->getLight2(selfPos + g_DirectionCoord[sideDir], true);
				 avelt = ((aveltMe + aveltNeight[0]) >> 1) & 0xff00ff;
			 }
			 InitBlockVertLight(vert, avelt, uvtile);
		 }
		 if (psubmesh) psubmesh->addGeomFace(mesh, &blockpos);
			 //psubmesh->addGeomFaceLight(mesh, &blockpos, faceVertexLight, &facecolor, pmtl->getUVTile());
	 }
	 for (auto& d : turnslantFace)
	 {
		 DirectionType dir = (DirectionType)((d / 10) % 10);
		 FaceVertexLight faceUpDownVertexLight;
		 //bool flipQuad = psection->getFaceVertexLight(NeighborCoord(blockpos, specialdir), dir, faceVertexLight);
		 //psection->getFaceVertexLight(blockpos, specialdir, faceUpDownVertexLight);
		 /*for (int i = 0; i < 4; i++)
		 {
			 if (specialdir == DIR_NEG_Y)
			 {
				 faceVertexLight.m_Light[i] = (3 * faceVertexLight.m_Light[i] + (faceUpDownVertexLight.m_Light[i])) / 4;
				 faceVertexLight.m_AmbientOcclusion[i] = (3 * faceVertexLight.m_AmbientOcclusion[i] + faceUpDownVertexLight.m_AmbientOcclusion[i]) / 4;
			 }
			 else
			 {
				 faceVertexLight.m_Light[i] = (faceVertexLight.m_Light[i] + (2 * faceUpDownVertexLight.m_Light[i])) / 3;
				 faceVertexLight.m_AmbientOcclusion[i] = (faceVertexLight.m_AmbientOcclusion[i] + 2 * faceUpDownVertexLight.m_AmbientOcclusion[i]) / 3;
			 }
		 }*/
		 dynamic_array<UInt16>* indices = m_TurnArcIndices;
		 RenderBlockMaterial* pmtl = getFaceMtl(biome, dir, pblock.getData(), facecolor);
		 if (pmtl == NULL)
			 continue;
		 SectionSubMesh* psubmesh = poutmesh->getSubMesh(pmtl);
		 const float* uvtile = nullptr;
		 if (psubmesh && !psubmesh->IsIgnoreTileUV())
			 uvtile = pmtl->getUVTile();
		 BlockGeomMeshInfo mesh;
		 bool isSpical = false;
		 World* pworld = data.m_World;
		 auto dirBlock = pworld->getBlock(NeighborCoord(blockpos + psection->getOrigin(), specialdir));
		 /*if (IsHalfTriangleBlock(dirBlock.getResID()))
		 {
			 auto dirBlock_mtl = g_BlockMtlMgr.getMaterial(dirBlock.getResID());
			 auto mtl = dirBlock_mtl->GetBlockDef();
			 auto pblock_mtl = g_BlockMtlMgr.getMaterial(pblock.getResID() - 1);
			 auto thismtlLittle = pblock_mtl->GetBlockDef();
			 if (mtl->Type == thismtlLittle->Type)
			 {
				 isSpical = true;
			 }
		 }*/
		 mesh.vertices = m_mTurnArcFace[d];
		 mesh.indices = *indices;
		//  unsigned short dir_color1 = TriangleNormal2LightColor(g_DirectionCoord[dir].toVector3());
		//  unsigned short dir_color2 = TriangleNormal2LightColor(g_DirectionCoord[specialdir].toVector3());
		 BlockColor vertcolor = Rainbow::ColorRGBA32::white;
		 //vertcolor.v = 0xffffffff;
		 vertcolor.a = 0;//(dir_color1 + 2 * dir_color2) / 3;
		//  if (specialdir == DIR_NEG_Y)
		//  {
		// 	 unsigned short dir_color3 = TriangleNormal2LightColor(g_DirectionCoord[DIR_POS_Y].toVector3());
		// 	 vertcolor.a = (3 * dir_color1 + dir_color2 + 2 * dir_color3) / 6;
		//  }
		 for (int n = 0; n < m_mTurnArcFace[d].size(); n++)
		 {
			 auto& vert = mesh.vertices[n];
			 int aveltMe = 0;
			 int aveltNeight[3] = { 0 };
			 int avelt = 0;
			 if (vert.pos.y > 0 && specialdir == DIR_POS_Y || vert.pos.y == 0 && specialdir == DIR_NEG_Y)
			 {
				 bool isXdir = dir < 2;
				 int xdir = isXdir ? dir : vert.pos.x == 0 ? 0 : 1;
				 int zdir = !isXdir ? dir : vert.pos.z == 0 ? 2 : 3;
				 auto selfPos = blockpos + g_DirectionCoord[specialdir];
				 aveltMe = psection->getLight2(selfPos, true);
				 aveltNeight[0] = psection->getLight2(selfPos + g_DirectionCoord[xdir], true);
				 aveltNeight[1] = psection->getLight2(selfPos + g_DirectionCoord[zdir], true);
				 aveltNeight[2] = psection->getLight2(selfPos + g_DirectionCoord[xdir] + g_DirectionCoord[zdir], true);
				 avelt = ((aveltMe + aveltNeight[0] + aveltNeight[1] + aveltNeight[2]) >> 2) & 0xff00ff;
			 }
			 else
			 {
				 auto selfPos = blockpos + g_DirectionCoord[dir];
				 aveltMe = psection->getLight2(selfPos, true);
				 int xdir = vert.pos.x == 0 ? 0 : 1;
				 int zdir = vert.pos.z == 0 ? 2 : 3;
				 int sideDir = dir > 1 ? xdir : zdir;

				 aveltMe = isSpical ? psection->getLight2(selfPos + g_DirectionCoord[specialdir], true) : psection->getLight2(selfPos, true);
				 aveltNeight[0] = psection->getLight2(selfPos + g_DirectionCoord[sideDir], true);
				 avelt = ((aveltMe + aveltNeight[0]) >> 1) & 0xff00ff;
			 }
			 //int lt1 = (((avelt >> 4) & 0xf) * vertcolor.a) >> 5;
			 //int lt2 = (((avelt >> 20) & 0xf) * vertcolor.a) >> 5;
			 //vert.pos.w = (lt1 << 8) | lt2;
			 //vert.color = vertcolor;
			 vert.color = vertcolor;
			 InitBlockVertLight(vert, avelt, uvtile);
		 }
		 if (psubmesh) psubmesh->addGeomFace(mesh, &blockpos); //addGeomFaceLight(mesh, &blockpos, faceVertexLight, &facecolor, pmtl->getUVTile());
	 }
	 for (auto& d : slantFace)
	 {
		 DirectionType dir = (DirectionType)curDir;
		 /*bool flipQuad = psection->getFaceVertexLight(blockpos, dir, faceVertexLight);
		 FaceVertexLight faceUpDownVertexLight;
		 psection->getFaceVertexLight(blockpos, specialdir, faceUpDownVertexLight);	*/

		/* for (int i = 0; i < 4; i++)
		 {
			 if (specialdir == DIR_NEG_Y)
			 {
				 faceVertexLight.m_Light[i] = (3 * faceVertexLight.m_Light[i] + (faceUpDownVertexLight.m_Light[i])) / 4;
				 faceVertexLight.m_AmbientOcclusion[i] = (3 * faceVertexLight.m_AmbientOcclusion[i] + faceUpDownVertexLight.m_AmbientOcclusion[i]) / 4;
			 }
			 else
			 {
				 faceVertexLight.m_Light[i] = (faceVertexLight.m_Light[i] + (2 * faceUpDownVertexLight.m_Light[i])) / 3;
				 faceVertexLight.m_AmbientOcclusion[i] = (faceVertexLight.m_AmbientOcclusion[i] + 2 * faceUpDownVertexLight.m_AmbientOcclusion[i]) / 3;
			 }
		 }*/

		 dynamic_array<UInt16>* indices = m_PosBigArcIndices;
		 RenderBlockMaterial* pmtl = getFaceMtl(biome, dir, pblock.getData(), facecolor);
		 if (pmtl == NULL)
			 continue;
		 SectionSubMesh* psubmesh = poutmesh->getSubMesh(pmtl);
		 const float* uvtile = nullptr;
		 if (psubmesh && !psubmesh->IsIgnoreTileUV())
			 uvtile = pmtl->getUVTile();
		 BlockGeomMeshInfo mesh;
		 bool isSpical = false;
		 World* pworld = data.m_World;
		 auto dirBlock = pworld->getBlock(NeighborCoord(blockpos + psection->getOrigin(), specialdir));
		/* if (IsHalfTriangleBlock(dirBlock.getResID()))
		 {
			 auto dirBlock_mtl = g_BlockMtlMgr.getMaterial(dirBlock.getResID());
			 auto mtl = dirBlock_mtl->GetBlockDef();
			 auto pblock_mtl = g_BlockMtlMgr.getMaterial(pblock.getResID() - 1);
			 auto thismtlLittle = pblock_mtl->GetBlockDef();
			 if (mtl->Type == thismtlLittle->Type)
			 {
				 isSpical = true;
			 }
		 }*/
		 mesh.vertices = m_mBigArcFace[d];
		 mesh.indices = *indices;
		//  unsigned short dir_color1 = TriangleNormal2LightColor(g_DirectionCoord[dir].toVector3());
		//  unsigned short dir_color2 = TriangleNormal2LightColor(g_DirectionCoord[specialdir].toVector3());
		 BlockColor vertcolor = Rainbow::ColorRGBA32::white;
		 //vertcolor.v = 0xffffffff;
		 vertcolor.a = 0;//(dir_color1 + 2 * dir_color2) / 3;
		//  if (specialdir == DIR_NEG_Y)
		//  {
		// 	 vertcolor.a = (3 * dir_color1 + dir_color2) / 4;
		//  }
		 for (int n = 0; n < m_mBigArcFace[d].size(); n++)
		 {
			 auto& vert = mesh.vertices[n];
			 int aveltMe = 0;
			 int aveltNeight[3] = { 0 };
			 int avelt = 0;
			 if (vert.pos.y > 0 && specialdir == DIR_POS_Y || vert.pos.y == 0 && specialdir == DIR_NEG_Y)
			 {
				 bool isXdir = dir < 2;
				 int xdir = isXdir ? dir : vert.pos.x == 0 ? 0 : 1;
				 int zdir = !isXdir ? dir : vert.pos.z == 0 ? 2 : 3;
				 auto selfPos = blockpos + g_DirectionCoord[specialdir];
				 aveltMe = psection->getLight2(selfPos, true);
				 aveltNeight[0] = psection->getLight2(selfPos + g_DirectionCoord[xdir], true);
				 aveltNeight[1] = psection->getLight2(selfPos + g_DirectionCoord[zdir], true);
				 aveltNeight[2] = psection->getLight2(selfPos + g_DirectionCoord[xdir] + g_DirectionCoord[zdir], true);
				 avelt = ((aveltMe + aveltNeight[0] + aveltNeight[1] + aveltNeight[2]) >> 2) & 0xff00ff;
			 }
			 else
			 {
				 auto selfPos = blockpos + g_DirectionCoord[dir];
				 aveltMe = psection->getLight2(selfPos, true);
				 int xdir = vert.pos.x == 0 ? 0 : 1;
				 int zdir = vert.pos.z == 0 ? 2 : 3;
				 int sideDir = dir > 1 ? xdir : zdir;

				 aveltMe = isSpical ? psection->getLight2(selfPos + g_DirectionCoord[specialdir], true) : psection->getLight2(selfPos, true);
				 aveltNeight[0] = psection->getLight2(selfPos + g_DirectionCoord[sideDir], true);
				 avelt = ((aveltMe + aveltNeight[0]) >> 1) & 0xff00ff;
			 }
			 //int lt1 = (((avelt >> 4) & 0xf) * vertcolor.a) >> 5;
			 //int lt2 = (((avelt >> 20) & 0xf) * vertcolor.a) >> 5;
			 //vert.pos.w = (lt1 << 8) | lt2;
			 //vert.color = vertcolor;
			 vert.color = vertcolor;
			 InitBlockVertLight(vert, avelt, uvtile);
		 }

		 if (psubmesh) psubmesh->addGeomFace(mesh, &blockpos); //addGeomFaceLight(mesh, &blockpos, faceVertexLight, &facecolor, pmtl->getUVTile());
	 }
#endif
 }

 void HorizontalArcPlateMaterial::createCollideData(CollisionDetect* coldetect, World* pworld, const WCoord& blockpos)
 {
	 auto block = pworld->getBlock(blockpos);
	 float blockheight = getBlockHeight(block.getData());
	 WCoord pos = blockpos * BLOCK_SIZE;
	 if (blockheight == 1)
	 {
		 coldetect->addObstacle(pos, pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
	 }
	 else
	 {
		 short step = 10;
		 float movesize = 1.f / float(step);
		 float heightOffset = 1.f / (float(step) - 1.f);
		 int heightsize = BLOCK_SIZE * 0.45f;
		 int xzsize = BLOCK_SIZE;
		 int dir = pworld->getBlockData(blockpos) & 3;

		 auto pblock = pworld->getBlock(blockpos);
		 int warp = -1;
		 int turnDir = -1;

		 auto frontBlock = pworld->getBlock(blockpos + g_DirectionCoord[dir]);
		 if (pblock.getResID() == frontBlock.getResID() && frontBlock.getData() < 8 && ((pblock.getData() & 4) == (frontBlock.getData() & 4)))
		 {
			 int frontDir = frontBlock.getData() & 3;
			 if (frontDir != dir && frontDir != ReverseDirection(dir))
			 {
				 warp = 0;
				 turnDir = frontDir;
			 }
		 }
		 if (warp == -1)
		 {
			 auto backBlock = pworld->getBlock(blockpos + g_DirectionCoord[ReverseDirection(dir)]);
			 if (pblock.getResID() == backBlock.getResID() && backBlock.getData() < 8 && ((pblock.getData() & 4) == (backBlock.getData() & 4)))
			 {
				 int backDir = backBlock.getData() & 3;
				 if (backDir != dir && backDir != ReverseDirection(dir))
				 {
					 warp = 1;
					 turnDir = backDir;
				 }
			 }
		 }
		 int count = 10;
		 if (warp == -1)
		 {
			 if (blockheight > 0)
			 {
				 int countBox[10] = { 0,5,8,12,13,15,15,14,11,8 };
				 if (dir == 0)
				 {
					 for (int i = 0; i < step; i++)
					 {
						 coldetect->addObstacle(pos + WCoord(int(movesize * i * BLOCK_SIZE), BLOCK_HALFSIZE, 0), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE + int(heightOffset * i * heightsize + countBox[i]), BLOCK_SIZE));
					 }
				 }
				 else if (dir == 1)
				 {
					 for (int i = 0; i < step; i++)
					 {
						 coldetect->addObstacle(pos + WCoord(0, BLOCK_HALFSIZE, 0), pos + WCoord(BLOCK_SIZE - int(movesize * i * BLOCK_SIZE), BLOCK_HALFSIZE + int(heightOffset * i * heightsize + countBox[i]), BLOCK_SIZE));
					 }
				 }
				 else if (dir == 2)
				 {
					 for (int i = 0; i < step; i++)
					 {
						 coldetect->addObstacle(pos + WCoord(0, BLOCK_HALFSIZE, int(movesize * i * BLOCK_SIZE)), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE + int(heightOffset * i * heightsize + countBox[i]), BLOCK_SIZE));
					 }
				 }
				 else if (dir == 3)
				 {
					 for (int i = 0; i < step; i++)
					 {
						 coldetect->addObstacle(pos + WCoord(0, BLOCK_HALFSIZE, 0), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE + int(heightOffset * i * heightsize + countBox[i]), BLOCK_SIZE - int(movesize * i * BLOCK_SIZE)));
					 }
				 }
				 coldetect->addObstacle(pos, pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE, BLOCK_SIZE));
			 }
			 else
			 {
				 int countBox[10] = { -1,2,6,7,7,7,6,2,-1,-3 };
				 if (dir == 0)
				 {
					 for (int i = 0; i < step; i++)
					 {
						 coldetect->addObstacle(pos + WCoord(BLOCK_SIZE - int(movesize * (i + 1) * xzsize), int(heightOffset * i * BLOCK_HALFSIZE - countBox[i]), 0), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE, BLOCK_SIZE));
					 }
				 }
				 else if (dir == 1)
				 {
					 for (int i = 0; i < step; i++)
					 {
						 coldetect->addObstacle(pos + WCoord(0, int(heightOffset * i * BLOCK_HALFSIZE - countBox[i]), 0), pos + WCoord(int(movesize * ( i + 1 ) * xzsize), BLOCK_HALFSIZE, BLOCK_SIZE));
					 }
				 }
				 else if (dir == 2)
				 {
					 for (int i = 0; i < step; i++)
					 {
						 coldetect->addObstacle(pos + WCoord(0, int(heightOffset * i * BLOCK_HALFSIZE - countBox[i]), BLOCK_SIZE - int(movesize * (i + 1) * xzsize)), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE, BLOCK_SIZE));
					 }
				 }
				 else if (dir == 3)
				 {
					 for (int i = 0; i < step; i++)
					 {
						 coldetect->addObstacle(pos + WCoord(0, int(heightOffset * i * BLOCK_HALFSIZE - countBox[i]), 0), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE, int(movesize * (i + 1) * xzsize)));
					 }
				 }
				 coldetect->addObstacle(pos + WCoord(0, BLOCK_HALFSIZE, 0), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
			 }
		 }
		 else if (1 == warp)
		 {
			 if (blockheight > 0)
			 {
				 int countBox[10] = { 0,5,8,12,13,15,15,14,11,8 };
				 if (dir == 0 && turnDir == 2 || dir == 2 && turnDir == 0)
				 {
					 for (int i = 0; i < step; i++)
					 {
						 coldetect->addObstacle(pos + WCoord(int(movesize * i * BLOCK_SIZE), BLOCK_HALFSIZE, int(movesize * i * BLOCK_SIZE)), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE + int(heightOffset * i * heightsize+ countBox[i]), BLOCK_SIZE));
					 }
				 }
				 else if (dir == 0 && turnDir == 3 || dir == 3 && turnDir == 0)
				 {
					 for (int i = 0; i < step; i++)
					 {
						 coldetect->addObstacle(pos + WCoord(int(movesize * i * BLOCK_SIZE), BLOCK_HALFSIZE, 0), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE + int(heightOffset * i * heightsize + countBox[i]), BLOCK_SIZE - int(movesize * i * BLOCK_SIZE)));
					 }
				 }
				 else if (dir == 1 && turnDir == 2 || dir == 2 && turnDir == 1)
				 {
					 for (int i = 0; i < step; i++)
					 {
						 coldetect->addObstacle(pos + WCoord(0, BLOCK_HALFSIZE, int(movesize * i * BLOCK_SIZE)), pos + WCoord(BLOCK_SIZE - int(movesize * i * BLOCK_SIZE), BLOCK_HALFSIZE + int(heightOffset * i * heightsize + countBox[i]), BLOCK_SIZE));
					 }
				 }
				 else if (dir == 1 && turnDir == 3 || dir == 3 && turnDir == 1)
				 {
					 for (int i = 0; i < step; i++)
					 {
						 coldetect->addObstacle(pos + WCoord(0, BLOCK_HALFSIZE, 0), pos + WCoord(BLOCK_SIZE - int(movesize * i * BLOCK_SIZE), BLOCK_HALFSIZE + int(heightOffset * i * heightsize + countBox[i]), BLOCK_SIZE - int(movesize * i * BLOCK_SIZE)));
					 }
				 }
				 coldetect->addObstacle(pos, pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE, BLOCK_SIZE));
			 }
			 else
			 {
				 int countBox[10] = { -1,2,6,7,7,7,6,2,-1,-3 };
				 if (dir == 0 && turnDir == 2 || dir == 2 && turnDir == 0)
				 {
					 for (int i = 0; i < step; i++)
					 {
						 coldetect->addObstacle(pos + WCoord(BLOCK_SIZE - int(movesize * (i+1) * xzsize),  int(heightOffset * i * BLOCK_HALFSIZE- countBox[i]), BLOCK_SIZE - int(movesize * (i + 1) * xzsize)), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE, BLOCK_SIZE));
					 }
				 }
				 else if (dir == 0 && turnDir == 3 || dir == 3 && turnDir == 0)
				 {
					 for (int i = 0; i < step; i++)
					 {
						 coldetect->addObstacle(pos + WCoord(BLOCK_SIZE - int(movesize * (i + 1) * xzsize), int(heightOffset * i * BLOCK_HALFSIZE - countBox[i]), 0), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE, int(movesize * (i + 1) * xzsize)));
					 }
				 }
				 else if (dir == 1 && turnDir == 2 || dir == 2 && turnDir == 1)
				 {
					 for (int i = 0; i < step; i++)
					 {
						 coldetect->addObstacle(pos + WCoord(0, int(heightOffset * i * BLOCK_HALFSIZE - countBox[i]), BLOCK_SIZE - int(movesize * (i + 1) * xzsize)), pos + WCoord(int(movesize * (i + 1) * xzsize), BLOCK_HALFSIZE, BLOCK_SIZE));
					 }
				 }
				 else if (dir == 1 && turnDir == 3 || dir == 3 && turnDir == 1)
				 {
					 for (int i = 0; i < step; i++)
					 {
						 coldetect->addObstacle(pos + WCoord(0, int(heightOffset * i * BLOCK_HALFSIZE - countBox[i]), 0), pos + WCoord(int(movesize * (i + 1) * xzsize), BLOCK_HALFSIZE, int(movesize * (i + 1) * xzsize)));
					 }
				 }
				 coldetect->addObstacle(pos + WCoord(0, BLOCK_HALFSIZE, 0), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
			 }
		 }
		 else
		 {
			 if (blockheight > 0)
			 {
				 int countBox[10] = { 0,5,8,12,13,15,15,14,11,8 };
				 if (dir == 0 && turnDir == 2 || dir == 2 || turnDir == 0)
				 {
					 for (int i = 0; i < step; i++)
					 {
						 coldetect->addObstacle(pos + WCoord(int(movesize * i * BLOCK_SIZE), BLOCK_HALFSIZE, 0), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE + int(heightOffset * i * heightsize+ countBox[i]), BLOCK_SIZE));
						 coldetect->addObstacle(pos + WCoord(0, BLOCK_HALFSIZE, int(movesize * i * BLOCK_SIZE)), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE + int(heightOffset * i * heightsize + countBox[i]), BLOCK_SIZE));
					 }
				 }
				 else if (dir == 0 && turnDir == 3 || dir == 3 && turnDir == 0)
				 {
					 for (int i = 0; i < step; i++)
					 {
						 coldetect->addObstacle(pos + WCoord(int(movesize * i * BLOCK_SIZE), BLOCK_HALFSIZE, 0), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE + int(heightOffset * i * heightsize + countBox[i]), BLOCK_SIZE));
						 coldetect->addObstacle(pos + WCoord(0, BLOCK_HALFSIZE, 0), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE + int(heightOffset * i * heightsize + countBox[i]), BLOCK_SIZE - int(movesize * i * BLOCK_SIZE)));
					 }
				 }
				 else if (dir == 1 && turnDir == 2 || dir == 2 || turnDir == 1)
				 {
					 for (int i = 0; i < step; i++)
					 {
						 coldetect->addObstacle(pos + WCoord(0, BLOCK_HALFSIZE, 0), pos + WCoord(BLOCK_SIZE - int(movesize * i * BLOCK_SIZE), BLOCK_HALFSIZE + int(heightOffset * i * heightsize + countBox[i]), BLOCK_SIZE));
						 coldetect->addObstacle(pos + WCoord(0, BLOCK_HALFSIZE, int(movesize * i * BLOCK_SIZE)), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE + int(heightOffset * i * heightsize + countBox[i]), BLOCK_SIZE));
					 }
				 }
				 else if (dir == 1 && turnDir == 3 || dir == 3 && turnDir == 1)
				 {
					 for (int i = 0; i < step; i++)
					 {
						 coldetect->addObstacle(pos + WCoord(0, BLOCK_HALFSIZE, 0), pos + WCoord(BLOCK_SIZE - int(movesize * i * BLOCK_SIZE), BLOCK_HALFSIZE + int(heightOffset * i * heightsize + countBox[i]), BLOCK_SIZE));
						 coldetect->addObstacle(pos + WCoord(0, BLOCK_HALFSIZE, 0), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE + int(heightOffset * i * heightsize + countBox[i]), BLOCK_SIZE - int(movesize * i * BLOCK_SIZE)));
					 }
				 }
				 coldetect->addObstacle(pos, pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE, BLOCK_SIZE));
			 }
			 else
			 {
				 int countBox[10] = { -1,2,6,7,7,7,6,2,-1,-3 };
				 if (dir == 0 && turnDir == 2 || dir == 2 || turnDir == 0)
				 {
					 for (int i = 0; i < step; i++)
					 {
						 coldetect->addObstacle(pos + WCoord(BLOCK_SIZE - int(movesize * (i + 1) * xzsize), int(heightOffset * i * BLOCK_HALFSIZE- countBox[i]), 0), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE, BLOCK_SIZE));
						 coldetect->addObstacle(pos + WCoord(0, int(heightOffset * i * BLOCK_HALFSIZE - countBox[i]), BLOCK_SIZE - int(movesize * (i + 1) * xzsize)), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE, BLOCK_SIZE));
					 }
				 }
				 else if (dir == 0 && turnDir == 3 || dir == 3 && turnDir == 0)
				 {
					 for (int i = 0; i < step; i++)
					 {
						 coldetect->addObstacle(pos + WCoord(BLOCK_SIZE - int(movesize * (i + 1) * xzsize), int(heightOffset * i * BLOCK_HALFSIZE - countBox[i]), 0), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE, BLOCK_SIZE));
						 coldetect->addObstacle(pos + WCoord(0, int(heightOffset * i * BLOCK_HALFSIZE - countBox[i]), 0), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE, int(movesize * (i + 1) * xzsize)));
					 }
				 }
				 else if (dir == 1 && turnDir == 2 || dir == 2 || turnDir == 1)
				 {
					 for (int i = 0; i < step; i++)
					 {
						 coldetect->addObstacle(pos + WCoord(0, int(heightOffset * i * BLOCK_HALFSIZE - countBox[i]), 0), pos + WCoord(int(movesize * (i + 1) * xzsize), BLOCK_HALFSIZE, BLOCK_SIZE));
						 coldetect->addObstacle(pos + WCoord(0, int(heightOffset * i * BLOCK_HALFSIZE - countBox[i]), BLOCK_SIZE - int(movesize * (i + 1) * xzsize)), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE, BLOCK_SIZE));
					 }
				 }
				 else if (dir == 1 && turnDir == 3 || dir == 3 && turnDir == 1)
				 {
					 for (int i = 0; i < step; i++)
					 {
						 coldetect->addObstacle(pos + WCoord(0, int(heightOffset * i * BLOCK_HALFSIZE - countBox[i]), 0), pos + WCoord(int(movesize * (i + 1) * xzsize), BLOCK_HALFSIZE, BLOCK_SIZE));
						 coldetect->addObstacle(pos + WCoord(0, int(heightOffset * i * BLOCK_HALFSIZE - countBox[i]), 0), pos + WCoord(BLOCK_SIZE, BLOCK_HALFSIZE, int(movesize * (i + 1) * xzsize)));
					 }
				 }
				 coldetect->addObstacle(pos + WCoord(0, BLOCK_HALFSIZE, 0), pos + WCoord(BLOCK_SIZE, BLOCK_SIZE, BLOCK_SIZE));
			 }
		 }
	 }
 }

 bool HorizontalArcPlateMaterial::coverNeighbor(int curblockdata, SolidBlockMaterial* neighbor, int neighbor_data, DirectionType dir)
 {
	 if (curblockdata & 8)
	 {
		 if (dir == DIR_NEG_Y)
		 {
			 if (neighbor && neighbor->GetAttrRenderType() == BLOCKRENDER_FLUID && neighbor_data) return false;
		 }
		 return true;
	 }
	 else
	 {
		 if (dir < DIR_NEG_Y)
		 {
			 int curDir = curblockdata & 3;
			 if (curDir == ReverseDirection(dir))
			 {
				 return true;
			 }
		 }
		 else
		 {
			 int updown = (curblockdata & 4) >> 2;
			 if ((updown + 4) == dir)
			 {
				 if (dir == DIR_NEG_Y)
				 {
					 if (neighbor && neighbor->GetAttrRenderType() == BLOCKRENDER_FLUID && neighbor_data) return false;
				 }
				 return true;
			 }
		 }
	 }
	 return false;
 }

 typedef HorizontalArcPlateMaterial::BlockInstance HorizontalArcPlateMaterialInstance;
 IMPLEMENT_SCENEOBJECTCLASS(HorizontalArcPlateMaterialInstance)
	 MNSandbox::ReflexClassParam<HorizontalArcPlateMaterial::BlockInstance, int> HorizontalArcPlateMaterialInstance::R_Dir(0, "Dir", "Block", &HorizontalArcPlateMaterial::BlockInstance::GetBlockData, &HorizontalArcPlateMaterial::BlockInstance::SetBlockData);