
#ifndef __BLOCKSLAB_H__
#define __BLOCKSLAB_H__

#include "BlockMaterial.h"

class SlabMaterial : public CubeBlockMaterial //tolua_exports
{ //tolua_exports
	//typedef CubeBlockMaterial Super;
	DECLARE_SCENEOBJECTCLASS(SlabMaterial)
	//DECLARE_BLOCKINSTANCE(SlabMaterial)
public:
	SlabMaterial();
	virtual ~SlabMaterial();
	//tolua_begin
	virtual void init(int resid) override;
	virtual int getPlaceBlockData(World *pworld, const WCoord &blockpos, DirectionType face, float hitptx, float hitpty, float hitptz, int def_blockdata);
	//virtual bool isOpaqueCube()
	//{
	//	return false;
	//}
	virtual bool hasSolidTopSurface(int blockdata);
	virtual bool canAttachedToMecha(int curblockdata, DirectionType dir, int neighbor_id, int neighbor_data) override;
	virtual void dropBlockAsItem(World *pworld, const WCoord &blockpos, int blockdata=0, BLOCK_MINE_TYPE droptype=BLOCK_MINE_NOTOOL, float chance=1.0f, int uin = -1) override;
	virtual char* getPhisicMeshBit(BaseSection* psection, const WCoord& blockpos) override;
	virtual SectionMesh* createBlockProtoMesh(int protodata = 0);
	virtual void createBlockMeshAngle(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh);
	virtual void createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh);
	virtual bool coverNeighbor(int curblockdata, SolidBlockMaterial* neighbor, int neighbor_data, DirectionType dir) override;
	virtual BlockMaterial::BlockType BlockTypeId() { return BlockMaterial::BlockType::BlockType_Slab; }
	RenderBlockMaterial* snowTopMtl, * snowSideMtl;
	//const char *getGeomName() {
	//	if (GetBlockDef() && !GetBlockDef()->Texture2.empty())
	//	{
	//		return GetBlockDef()->Texture2.c_str();
	//	}

	//	return "cube";
	//}
	//tolua_end
private:
	virtual float getBlockHeight(int blockdata);
	virtual void initGeomName() override;

public:
	DECLARE_BLOCKMATERIAL_INSTANCE_BEGIN(SlabMaterial)
		DECLARE_BLOCKMATERIAL_INSTANCE_R_PARAM(R_Dir, int)
	DECLARE_BLOCKMATERIAL_INSTANCE_END(SlabMaterial)

}; //tolua_exports

#endif