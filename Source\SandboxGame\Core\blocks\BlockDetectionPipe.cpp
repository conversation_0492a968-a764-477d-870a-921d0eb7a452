
#include "BlockDetectionPipe.h"
#include "container_detectionpipe.h"
#include "IClientPlayer.h"
#include "world.h"
#include "special_blockid.h"
#include "SectionMesh.h"
#include "BlockGeom.h"
#include "ShareRenderMaterial.h"
#include "BlockMaterialMgr.h"
#include "chunk.h"

IMPLEMENT_BLOCKMATERIAL(BlockDetectionPipe)

BlockDetectionPipe::BlockDetectionPipe()
{
	m_OnMtl = NULL;
}

BlockDetectionPipe::~BlockDetectionPipe()
{
	OGRE_RELEASE(m_OnMtl);
}

void BlockDetectionPipe::init(int resid)
{
	ModelBlockMaterial::init(resid);

	char texname[64];
	sprintf(texname, "%s_green", m_Def->Texture2.c_str());
	m_OnMtl = g_BlockMtlMgr.createRenderMaterial(texname, m_Def);
}


void BlockDetectionPipe::initGeomName()
{
	m_geomName = "detection_pipe";
}

int BlockDetectionPipe::getPlaceBlockData(World *pworld, const WCoord &blockpos, DirectionType face, float hitptx, float hitpty, float hitptz, int def_blockdata)
{
	return face & 7;
}

void BlockDetectionPipe::onBlockAdded(World *pworld, const WCoord &blockpos)
{
	ModelBlockMaterial::onBlockAdded(pworld, blockpos);
}


void BlockDetectionPipe::onBlockRemoved(World* pworld, const WCoord& blockpos, int blockid, int blockdata)
{
	ModelBlockMaterial::onBlockAdded(pworld, blockpos);

	if (pworld->getContainerMgr())
	{
		//如果已经发射了召回。
		ContainerDetectionPipeline* container = dynamic_cast<ContainerDetectionPipeline*>(pworld->getContainerMgr()->getContainer(blockpos));
		if (container)
		{
			container->withDrawBlockLaser();
			WorldContainerMgr* containerMgr = dynamic_cast<WorldContainerMgr*>(pworld->getContainerMgr());
			if (containerMgr) {
				containerMgr->removeContainerByChunk(container);
			}
			if (pworld->getChunk(blockpos)) pworld->getChunk(blockpos)->removeContainer(container, true);
		}
	}
}



void BlockDetectionPipe::createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh)
{
	BlockGeomTemplate* geom = getGeom(data.m_LODLevel);
	if (!geom)
		return;
	auto psection = data.m_SharedSectionData;
	Block pblock = psection->getBlock(blockpos);
	int blockdata = pblock.getData();
	int placedir = blockdata & 7;

	int base_geom = 0;
	int rotate = DIR_NEG_Z;
	int mirror = 0;
   	if (placedir < 4)
	{
		base_geom = 1;
		rotate = placedir;
	}
	else if (placedir == 4)
	{
		base_geom = 0;
		rotate = DIR_NEG_X;
	}
	else if (placedir == 5)
	{
		mirror = 2;
		base_geom = 0;
		rotate = DIR_NEG_X;
	}	

	Rainbow::ColorRGBAf verts_light[1] = { Rainbow::ColorRGBAf::black };
	psection->getBlockVertexLight(blockpos, verts_light);

	bool isChange = (blockdata & 8) == 8;
	RenderBlockMaterial* mtl = isChange ? m_OnMtl : getDefaultMtl();
	SectionSubMesh* psubmesh = poutmesh->getSubMesh(mtl);

	BlockGeomMeshInfo meshinfo;
	//base
	geom->getFaceVerts(meshinfo, base_geom, 1.0f, 0, rotate, mirror);
	psubmesh->addGeomBlockLight(meshinfo, &blockpos, verts_light, NULL, mtl->getUVTile());
}

void BlockDetectionPipe::onNotify(World* pworld, const WCoord& blockpos, int blockid)
{
	ContainerDetectionPipeline* container = dynamic_cast<ContainerDetectionPipeline*>(pworld->getContainerMgr()->getContainer(blockpos));
	if (container)
	{
		container->checkDirConnect();
		if (container->isTransfering())
		{
			container->checkTransferDir();
		}
	}
}

bool BlockDetectionPipe::onTrigger(World* pworld, const WCoord& blockpos, DirectionType face, IClientPlayer* player, const Rainbow::Vector3f& colpoint)
{
	if (pworld->isRemoteMode())
	{
		return true;
	}
	int toolId = player->getCurToolID();
	if (toolId == BLOCK_COLLECTING_PIPE || toolId == BLOCK_TRANSPORT_PIPE || toolId == BLOCK_DETECTION_PIPE)
	{
		return false;
	}
	if (pworld->getContainerMgr() == NULL)
	{
		return true;
	}
	ContainerDetectionPipeline* container = dynamic_cast<ContainerDetectionPipeline*>(pworld->getContainerMgr()->getContainer(blockpos));
	if (container)
	{
		player->openContainer(container);
	}
	return true;
}

void BlockDetectionPipe::onBlockPlacedBy(World* pworld, const WCoord& blockpos, IClientPlayer* player)
{
	/*int face = pworld->getBlockData(blockpos);

	if (face == DIR_NEG_Y)
	{
		int placedir = player->getPlaceDirToBlock(blockpos);
		if (placedir == DIR_NEG_X || placedir == DIR_POS_X || placedir == DIR_NEG_Z || placedir == DIR_POS_Z)
		{
			face = placedir;
		}
		pworld->setBlockData(blockpos, face, 3);
	}*/

	int placedir = player->getPlaceDirToBlock(blockpos);
	pworld->setBlockData(blockpos, ReverseDirection(placedir), 3);

	createContainer(pworld, blockpos, ReverseDirection(placedir), 0);
}

ContainerElectricElement* BlockDetectionPipe::createContainer(World* pworld, const WCoord& blockpos, int dir, int power)
{
	ContainerDetectionPipeline* container = SANDBOX_NEW(ContainerDetectionPipeline, blockpos, dir, power);
	pworld->getContainerMgr()->spawnContainer(container);

	int reverseDir = ReverseDirection(dir);
	for (int i = 0; i < 6; i++)
	{
		if (i != dir && i != reverseDir)
		{
			container->setOutPutDirOpen((DirectionType)i, true);	
		}
	}
	//container->emitBlockLaser();
	container->m_Dir = dir;
	container->checkDirConnect();
	return container;
}

bool BlockDetectionPipe::beginTransfer(World* pworld, const WCoord& blockpos, long long objID, int dir)
{
	if (objID == 0 && dir == 0)
	{
		updateState(pworld, blockpos, false);
		return false;
	}
	ContainerDetectionPipeline* container = dynamic_cast<ContainerDetectionPipeline*>(pworld->getContainerMgr()->getContainer(blockpos));
	if (container)
	{
		container->beginTransfer(objID);
		return true;
	}
	return false;
}

bool BlockDetectionPipe::canConnectToDir(World* pworld, const WCoord& blockpos, int dir, const BackPackGrid& grid)
{
	ContainerDetectionPipeline* container = dynamic_cast<ContainerDetectionPipeline*>(pworld->getContainerMgr()->getContainer(blockpos));
	if (container)
	{
		int blockData = pworld->getBlockData(blockpos) & 7;
		int reverseDir = ReverseDirection(blockData);
		if (reverseDir == dir && !container->isTransfering())
		{
			return true;
		}
	}
	return false;
}

void BlockDetectionPipe::endTransfer(World* pworld, const WCoord& blockpos)
{
	updateState(pworld, blockpos, true);
}

void BlockDetectionPipe::updateState(World* pworld, const WCoord& blockpos, bool isShow)
{	
	int blockdata = pworld->getBlockData(blockpos) & 7;
	if (isShow)
	{
		if (pworld->getContainerMgr())
		{
			
			ContainerElectricElement* container = dynamic_cast<ContainerElectricElement*>(pworld->getContainerMgr()->getContainer(blockpos));
			if (!container && Chunk::ms_SetBlockCreateContainer)
			{
				createContainer(pworld, blockpos, blockdata, 15);
			}

			if (container)
			{
				container->setPower(15);
				//这里是更新
				//container->emitBlockLaser();
				pworld->setBlockData(blockpos, blockdata | 8, 2);				
				pworld->notifyBlockSides(blockpos, m_BlockResID);
			}
		}
	}
	else
	{
		if (pworld->getContainerMgr())
		{
			//如果已经发射了召回。
			ContainerElectricElement* container = dynamic_cast<ContainerElectricElement*>(pworld->getContainerMgr()->getContainer(blockpos));
			if (container)
			{
				container->setPower(0);
				//container->withDrawBlockLaser(2);
				pworld->setBlockData(blockpos, blockdata & 7, 2);				
				pworld->notifyBlockSides(blockpos, m_BlockResID);
			}
		}
	}
}

int BlockDetectionPipe::getIndirectlyPowered(World* pworld, const WCoord& blockpos, int dir)
{
	return 0;
}

int BlockDetectionPipe::outputWeakEnergy(World* pworld, const WCoord& blockpos, DirectionType dir)
{
	ContainerElectricElement* container = dynamic_cast<ContainerElectricElement*>(pworld->getContainerMgr()->getContainer(blockpos));
	if (container)
	{
		return container->getPower(dir);
	}
	return 0;
}

void BlockDetectionPipe::dropBlockAsItem(World* pworld, const WCoord& blockpos, int blockdata, BLOCK_MINE_TYPE droptype, float chance, int uin/* = -1 */)
{
	ModelBlockMaterial::dropBlockAsItem(pworld, blockpos, blockdata, droptype, chance, uin);
	if (!pworld || !pworld->getContainerMgr())
		return;

	ContainerDetectionPipeline* container = dynamic_cast<ContainerDetectionPipeline*>(pworld->getContainerMgr()->getContainer(blockpos));
	if (container)
	{
		container->dropItems();
	}
}


unsigned int BlockDetectionPipe::electricEmitBlockLaserType(World* pworld, const WCoord& blockpos, int dir)
{
	return 0;
}