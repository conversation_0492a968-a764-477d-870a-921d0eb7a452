#pragma once

#include "ClientMob.h"
#include "backpack.h"
#include "CarryComponent.h"
#include "ActorBody.h"

class EXPORT_SANDBOXGAME DynamicContainer;

class EXPORT_SANDBOXGAME ActorMobCorpse;
class ActorMobCorpse : public ClientActor
{
    DECLARE_SCENEOBJECTCLASS(ActorMobCorpse)
public:
    ActorMobCorpse();
    virtual ~ActorMobCorpse();

    // 从Mob创建尸体
    static ActorMobCorpse* CreateFromMob(ClientMob* mob);
    void init();
    void initStorageBox();
    ActorBody* newActorBody();
    bool interact(ClientActor* player, bool onshift = false, bool isMobile = false);
    
    // 重写基类方法
    virtual bool load(const void* srcdata, int version) override;
    virtual flatbuffers::Offset<FBSave::SectionActor> save(SAVE_BUFFER_BUILDER& builder) override;
    virtual void tick() override;
    virtual void enterWorld(World* pworld) override;
    virtual void leaveWorld(bool keep_inchunk) override;
    virtual bool leftClickInteract(ClientActor* player) override;
    virtual int getObjType() const override;

    // 交互相关方法
    bool canLootCorpse(ClientPlayer* player);
    void openCorpseLoot(ClientPlayer* player);
    
    // 尸体属性设置 - 基于tick的计时系统
    void SetDecayTicks(int ticks) { m_DecayTicks = ticks; }
    int GetRemainingTicks() const { return m_DecayTicks - m_TickCount; }
    bool IsLooted() const { return m_IsLooted; }
    
    // 尸体外观相关
    void ApplyCorpseVisualEffects();
    void UpdateFadeEffect();
    
    // 获取尸体物品
    BackPack* GetCorpseBackpack() { return m_CorpseBackpack; }
    
    // Mob信息
    int GetMobId() const { return m_MobId; }
    int GetMobLevel() const { return m_MobLevel; }
    const core::string& GetMobName() const { return m_MobName; }
    
    // 尸体的掉落物品处理
    bool TakeItemFromCorpse(ClientPlayer* player, int slotIndex, int count);

    virtual bool supportSaveToPB() override
    {
        return false;
    }
    virtual bool canBeCollidedWith() override
    {
        return true;
    }

private:
    // 创建事件监听
    void createEvent();
    
    // 创建尸体掉落物
    void CreateDroppedItems();
    
    // 尸体保存/加载结构
    struct MobCorpseData {
        int mobId;
        char mobName[64];
        int mobLevel;
        int modelIndex;
        char modelPath[128];
        int decayTicks;
        int tickCount;
        bool isLooted;
        float bodyScale;
    };
    
    // 关联的Mob信息
    int m_MobId;
    core::string m_MobName;
    int m_MobLevel;
    
    // 外观信息
    int m_MobModelIndex;
    core::string m_ModelPath;
    float m_BodyScale;
    
    // 尸体背包
    BackPack* m_CorpseBackpack;
    
    // 尸体状态 - 基于tick的计时系统
    int m_DecayTicks;       // 尸体存在的总tick数
    int m_TickCount;        // 已经过去的tick数
    int m_FadeStartTick;    // 开始消失的tick数
    bool m_IsLooted;        // 是否已被掠夺
    bool m_IsFading;        // 是否正在消失
    
    // 事件监听器
    void* m_LootListener;
    void* m_ItemTakenListener;
    
    // 拾取物品的玩家ID
    long long m_CurrentLooterID;

    DynamicContainer* m_DynamicContainer;

private:
    Vector3f m_Motion;    // 尸体的运动向量
    int m_OnGroundTime;   // 在地面上停留的时间
};
