
#ifndef __BLOCK_MOSS_H__
#define __BLOCK_MOSS_H__

//#include "BlockMaterial.h"
#include "BlockFlatPiece.h"

//苔藓 1阶段
class MossBlockMaterial : public FlatPieceMaterial //tolua_exports
{ //tolua_exports
	DECLARE_BLOCKMATERIAL(MossBlockMaterial)
    //DECLARE_BLOCKINSTANCE(MossBlockMaterial)
public:
	//tolua_begin
    MossBlockMaterial();
    virtual ~MossBlockMaterial();
    virtual void initDefaultMtl() override;
    virtual void init(int resid) override;
    virtual float getBlockSlipperiness(int blockdata = 0)
    {
        return 1.2f;
    }
	virtual void onNotify(World *pworld, const WCoord &blockpos, int blockid) override;
    virtual void createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh) override;
	virtual int getPlaceBlockData(World *pworld, const WCoord &blockpos, DirectionType face, float hitptx, float hitpty, float hitptz, int def_blockdata) override;
	virtual bool canPutOntoFace(WorldProxy *pworld, const WCoord &blockpos, int face) override;
    virtual BlockTexElement *getDestroyTexture(Block pblock, BlockTexDesc &desc) override;
    virtual int convertDataByRotate(int blockdata, int rotatetype) override;
    std::map<int, RenderBlockMaterial*>MossPic;
    virtual void dropBlockAsItem(World* pworld, const WCoord& blockpos, int blockdata = 0, BLOCK_MINE_TYPE droptype = BLOCK_MINE_NOTOOL, float chance = 1.0f, int uin = -1) override;
    bool canPut(World* pworld, const WCoord& blockpos,int dir);
    //tolua_end
protected:
    virtual bool isDoubleSide() override
    {
        return true;
    }
    virtual bool isFarHide() override
    {
        return true;
    }
private:
	bool canPlacedOn(int blockid);
    bool canStay(World* pworld, const WCoord& blockpos);
}; //tolua_exports
//大苔藓 2-3阶段
class MossHugeBlockMaterial : public MossBlockMaterial //tolua_exports
{ //tolua_exports
    DECLARE_BLOCKMATERIAL(MossHugeBlockMaterial)
    //DECLARE_BLOCKINSTANCE(MossBlockMaterial)
public:
    //tolua_begin
    virtual void createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh) override;
    virtual void dropBlockAsItem(World* pworld, const WCoord& blockpos, int blockdata = 0, BLOCK_MINE_TYPE droptype = BLOCK_MINE_NOTOOL, float chance = 1.0f, int uin = -1) override;
    virtual float getBlockSlipperiness(int blockdata = 0)
    {
        int level = ((blockdata & 8) ? 2 : 1);
        if(level==1)
            return 1.4f;
        return 1.6f;
    }
    virtual int convertDataByRotate(int blockdata, int rotatetype) override;
    //tolua_end


}; //tolua_exports

#endif