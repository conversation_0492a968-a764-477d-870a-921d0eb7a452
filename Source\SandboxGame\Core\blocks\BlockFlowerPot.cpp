
#include "BlockFlowerPot.h"
#include "section.h"
#include "IClientPlayer.h"
#include "world.h"
#include "BlockModelHerb.h"
#include "BlockFlowerModel.h"
#include "BlockColorFlower.h"
#include "BlockMaterialMgr.h"

static int s_SmallPotItems[] = { 224, 225, 303, 304, 305, 307, 308, 309, 310, 311, 312, 1186 };
static int s_BigPotItems[] = { 243, 300, 301, 302, 306, 313 };

IMPLEMENT_BLOCKMATERIAL(BlockFlowerPot)


int BlockFlowerPot::blockdata2Item(int blockdata, bool smallpot)
{
	if (blockdata > 0)
	{
		if (smallpot)
		{
			if (blockdata <= sizeof(s_SmallPotItems) / sizeof(int)) return s_SmallPotItems[blockdata - 1];
		}
		else
		{
			if (blockdata <= sizeof(s_BigPotItems) / sizeof(int)) return s_BigPotItems[blockdata - 1];
		}
		//assert(0);
	}
	return 0;
}

int BlockFlowerPot::item2Blockdata(int itemid, bool smallpot)
{
	if (itemid == 0) return 0;
	if (smallpot)
	{
		for (int i = 0; i < sizeof(s_SmallPotItems) / sizeof(int); i++)
		{
			if (s_SmallPotItems[i] == itemid) return i + 1;
		}
	}
	else
	{
		for (int i = 0; i < sizeof(s_BigPotItems) / sizeof(int); i++)
		{
			if (s_BigPotItems[i] == itemid) return i + 1;
		}
	}
	return 0;
}

void BlockFlowerPot::init(int resid)
{
	ModelBlockMaterial::init(resid);

	m_isSmallPot = getBlockResID() == 928 || getBlockResID() == 946 || getBlockResID() == 947;
	m_nSpecialLogicType[0] |= BlockSpceialLogicTeam0::BlockFaceToPlane;
}

void BlockFlowerPot::createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh)
{
	auto psection = data.m_SharedSectionData;
	ModelBlockMaterial::createBlockMesh(data, blockpos, poutmesh);

	int blockdata = psection->getBlock(blockpos).getData();
	int flowerid = blockdata2Item(blockdata, m_isSmallPot);

	if (flowerid > 0)
	{
		const SectionDataHandler* neighborSectionData = NULL;
		HerbMaterial* hmtl = dynamic_cast<HerbMaterial*>(g_BlockMtlMgr.getMaterial(flowerid));
		if (hmtl)
		{
			if (dynamic_cast<BlockFlowerModel*>(hmtl))	//一个模型，但是要区分高度，应该没用到了，但是这个类还在，就留着
			{
				float oy = m_isSmallPot ? 0.4f : 0.9f;
				hmtl->setRenderInPot(1, oy);
				if (m_isSmallPot)
				{
					hmtl->createBlockMesh(data, blockpos, poutmesh);
				}
				else
				{
					if (psection)
					{
						neighborSectionData = psection->getNeighborSectionData(blockpos, DIR_POS_Y);
					}
					hmtl->setRenderInPot(2, oy);

					if (neighborSectionData)
					{
						WCoord wcoord = TopCoord(blockpos);
						if (wcoord.y >= SECTION_BLOCK_DIM)
						{
							wcoord.y -= SECTION_BLOCK_DIM;
						}
						hmtl->createBlockMesh(data, wcoord, poutmesh);
					}
				}
			}
			else if (dynamic_cast<ColorFlowerMaterial*>(hmtl) || flowerid == 1186)	//一个模型的花
			{
				float oy = m_isSmallPot ? 0.4f : 0.9f;
				if (flowerid == 1186)//彼岸花较矮，增加特殊判断
					oy = 0.5f;
				hmtl->setRenderInPot(1, oy);
				hmtl->createBlockMesh(data, blockpos, poutmesh);
			}
			else	// 上下两部分的花
			{
				float oy = m_isSmallPot ? 0.4f : 0.9f;
				hmtl->setRenderInPot(1, oy);

				hmtl->createBlockMesh(data, blockpos, poutmesh);
				if (!m_isSmallPot)
				{
					if (psection)
					{
						neighborSectionData = psection->getNeighborSectionData(blockpos, DIR_POS_Y);
					}
					hmtl->setRenderInPot(2, oy);

					if (neighborSectionData)
					{
						WCoord wcoord = TopCoord(blockpos);
						if (wcoord.y >= SECTION_BLOCK_DIM)
						{
							wcoord.y -= SECTION_BLOCK_DIM;
						}
						hmtl->createBlockMesh(data, wcoord, poutmesh);
					}
				}
			}
			hmtl->setRenderInPot(0);
		}
		else
		{
			BlockModelHerb* modelMtl = dynamic_cast<BlockModelHerb*>(g_BlockMtlMgr.getMaterial(flowerid));
			if (modelMtl)
			{
				float oy = m_isSmallPot ? 0.4f : 0.7f;
				modelMtl->generateFlowerPotMesh(psection, blockpos, poutmesh, 0, Rainbow::Vector3f(0.0f, oy, 0.0f));
				if (!m_isSmallPot)
				{
					if (psection)
					{
						neighborSectionData = psection->getNeighborSectionData(blockpos, DIR_POS_Y);
					}
					if (neighborSectionData)
					{
						WCoord wcoord = TopCoord(blockpos);
						if (wcoord.y >= SECTION_BLOCK_DIM)
						{
							wcoord.y -= SECTION_BLOCK_DIM;
						}
						modelMtl->generateFlowerPotMesh(neighborSectionData, wcoord, poutmesh, 1, Rainbow::Vector3f(0.0f, oy, 0.0f));
					}
				}
			}
		}

	}
}

bool BlockFlowerPot::onTrigger(World* pworld, const WCoord& blockpos, DirectionType face, IClientPlayer* player, const Rainbow::Vector3f& colpoint)
{
	if (pworld->isRemoteMode())
	{
		return true;
	}

	int olddata = pworld->getBlockData(blockpos);
	int newdata = item2Blockdata(player->getCurToolID(), m_isSmallPot);

	if (olddata > 0 && newdata == 0)
	{
		int itemid = blockdata2Item(olddata, m_isSmallPot);
		g_BlockMtlMgr.getMaterial(itemid)->dropBlockAsItem(pworld, blockpos, 0, BLOCK_MINE_NOTOOL, 1.0f);

		pworld->setBlockData(blockpos, newdata);
	}
	else if (olddata == 0 && newdata > 0)
	{
		player->shortcutItemUsed();
		pworld->setBlockData(blockpos, newdata);
	}
	return true;
}

void BlockFlowerPot::dropBlockAsItem(World* pworld, const WCoord& blockpos, int blockdata, BLOCK_MINE_TYPE droptype, float chance, int uin/* = -1 */)
{
	int flowerid = blockdata2Item(blockdata, m_isSmallPot);

	//sun flower
	if (flowerid == 300)
	{
		ModelBlockMaterial* hmtl = dynamic_cast<ModelBlockMaterial*>(g_BlockMtlMgr.getMaterial(flowerid));
		if (hmtl) hmtl->dropBlockAsItem(pworld, blockpos, 0, BLOCK_MINE_NOTOOL, chance, uin);
	}
	else if (flowerid > 0)
	{
		HerbMaterial* hmtl = dynamic_cast<HerbMaterial*>(g_BlockMtlMgr.getMaterial(flowerid));
		if (hmtl) hmtl->dropBlockAsItem(pworld, blockpos, 0, BLOCK_MINE_NOTOOL, chance, uin);
	}

	ModelBlockMaterial::dropBlockAsItem(pworld, blockpos, blockdata, droptype, chance, uin);
}


int BlockFlowerPot::convertDataByRotate(int blockdata, int rotatetype)
{
	return blockdata;
}

int BlockFlowerPot::getBlockGeomID(int* idbuf, int* dirbuf, const SectionDataHandler* sectionData, const WCoord& blockpos, World* world)
{
	idbuf[0] = 0;
	dirbuf[0] = DIR_NEG_Z;
	return 1;
}
