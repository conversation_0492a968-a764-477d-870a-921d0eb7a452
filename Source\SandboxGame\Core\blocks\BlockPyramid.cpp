
#include "BlockPyramid.h"
#include "BlockMaterialMgr.h"
#include "section.h"
#include "SectionMesh.h"
#include "BlockGeom.h"
#include "world.h"
#include "DefManagerProxy.h"
#include "IPlayerControl.h"
#include "BlockMeshVert.h"
#include "WorldManager.h"
#include "WeatherManager.h"
//#include "OgreMaterial.h"
#include "Gizmo/DebugUtility.h"
#include "Common/GameStatic.h"
#include "ClientPlayer.h"
IMPLEMENT_SCENEOBJECTCLASS(PyramidMaterial)
//IMPLEMENT_BLOCKINSTANCE(PyramidMaterial)
using namespace Rainbow;


static MINIW::GameStatic<dynamic_array<dynamic_array<BlockGeomVert>>> s_mWholeFace;
dynamic_array<dynamic_array<BlockGeomVert>>& PyramidMaterial::m_mWholeFace()
{
	return *s_mWholeFace.EnsureInitialized();
}

dynamic_array<UInt16>* PyramidMaterial::m_dPosIndices/* = { 0, 1, 2, 0, 2, 3 }*/;
dynamic_array<UInt16>* PyramidMaterial::m_PosTrIndices/* = { 0, 1, 2 }*/;
Rainbow::Vector3f* PyramidMaterial::ms_LightVec;
void initAllPyramidIndex(void* data)
{
	PyramidMaterial::m_dPosIndices = new dynamic_array<UInt16>();
	*PyramidMaterial::m_dPosIndices = { 0, 1, 2, 0, 2, 3 };
	PyramidMaterial::m_PosTrIndices = new dynamic_array<UInt16>();
	*PyramidMaterial::m_PosTrIndices = { 0, 1, 2 };
	PyramidMaterial::ms_LightVec = new Rainbow::Vector3f(0.f, 0.8944f, 0.4472f);
}
void uninitAllPyramidIndex(void* data)
{
	PyramidMaterial::m_dPosIndices->clear_dealloc();
	delete(PyramidMaterial::m_dPosIndices);
	PyramidMaterial::m_PosTrIndices->clear_dealloc();
	delete(PyramidMaterial::m_PosTrIndices);
	delete(PyramidMaterial::ms_LightVec);
}
MINIW::GameRuntimeInitializeAndCleanup initPyramideIdx(initAllPyramidIndex, uninitAllPyramidIndex);

// dynamic_array<dynamic_array<BlockGeomVert>> PyramidMaterial::m_mWholeFace;


PyramidMaterial::PyramidMaterial() 
{

}

PyramidMaterial::~PyramidMaterial()
{
}

void PyramidMaterial::init(int resid)
{
	CubeBlockMaterial::init(resid);
	SetToggle(BlockToggle_IsOpaqueCube, false);
	SetAttrRenderType(BLOCKRENDER_CUBE_MODEL);

	if (m_LoadOnlyLogic) return;

	char texname[256];
	RenderBlockMaterial* topmtl = nullptr , * sidemtl = nullptr, * bottommtl = nullptr;

	sprintf(texname, "%s_top", GetBlockDef()->Texture1.c_str());
	topmtl = g_BlockMtlMgr.createRenderMaterial(texname, GetBlockDef());
	if (topmtl == NULL)
	{
		topmtl = g_BlockMtlMgr.createRenderMaterial(GetBlockDef()->Texture1.c_str(), GetBlockDef(), GETTEX_WITHDEFAULT);

		sidemtl = topmtl;
		sidemtl->AddRef();
		bottommtl = topmtl;
		bottommtl->AddRef();
	}
	else
	{
		if (!GetBlockDef()->Texture2.empty()) sidemtl = g_BlockMtlMgr.createRenderMaterial(GetBlockDef()->Texture2.c_str(), GetBlockDef(), GETTEX_WITHDEFAULT);
		else
		{
			sprintf(texname, "%s_side", GetBlockDef()->Texture1.c_str());
			sidemtl = g_BlockMtlMgr.createRenderMaterial(texname, GetBlockDef(), GETTEX_WITHDEFAULT);
		}

		sprintf(texname, "%s_bottom", GetBlockDef()->Texture1.c_str());
		bottommtl = g_BlockMtlMgr.createRenderMaterial(texname, GetBlockDef());
		if (bottommtl == NULL)
		{
			bottommtl = topmtl;
			bottommtl->AddRef();
		}
	}

	
	

	setFaceMtl(DIR_POS_X, sidemtl);
	setFaceMtl(DIR_NEG_Z, sidemtl);
	setFaceMtl(DIR_NEG_X, sidemtl);
	setFaceMtl(DIR_POS_Z, sidemtl);

	setFaceMtl(DIR_POS_Y, topmtl);
	setFaceMtl(DIR_NEG_Y, bottommtl);


	ENG_RELEASE(sidemtl);
	ENG_RELEASE(topmtl);
	ENG_RELEASE(bottommtl);

	m_nSpecialLogicType[0] |= RotateMechaStopNoChangePos;
	initVertData();
}

bool PyramidMaterial::hasSolidTopSurface(int blockdata)
{
	return true;
}

float PyramidMaterial::getBlockHeight(int blockdata)
{
	if (blockdata & 8)
	{
		return 1.f;
	}
	else if (blockdata & 4)
	{
		return -0.5f;
	}
	return 0.5f;
}

void PyramidMaterial::drawBox(WCoord min, WCoord max)
{
	// 	Vector3f down[4] = { Vector3f(min.x, min.y, min.z), Vector3f(max.x, min.y, min.z), Vector3f(max.x, min.y, max.z), Vector3f(min.x, min.y, max.z) };
	// 	Vector3f up[4] = { Vector3f(min.x, max.y, min.z), Vector3f(max.x, max.y, min.z), Vector3f(max.x, max.y, max.z), Vector3f(min.x, max.y, max.z) };
	// 
	// 	for (int i = 0; i < 4; i++)
	// 	{
	// 		DebugDrawLine(down[i], down[(i + 1) % 4], ColorRGBAf(1, 0, 0, 1));
	// 		DebugDrawLine(up[i], up[(i + 1) % 4], ColorRGBAf(1, 0, 0, 1));
	// 		DebugDrawLine(down[i], up[i], ColorRGBAf(1, 0, 0, 1));
	// 	}
}

unsigned char PyramidMaterial::TriangleNormal2LightColor(const Rainbow::Vector3f& normal, bool useDirColor/* = false*/, short Dir/* = 0*/)
{
	Rainbow::Vector3f tmp;
	tmp.x = Rainbow::Abs(normal.x);
	tmp.y = normal.y;
	tmp.z = Rainbow::Abs(normal.z);

	//Vector3 v = s_LightVec;
	//Normalize(v);
	if (!ms_LightVec)
	{
		MINIW::GameRuntimeInitializeAndCleanup initPyramideIdx(initAllPyramidIndex, uninitAllPyramidIndex);
	}
	float t = 0;

	t = DotProduct(tmp, *ms_LightVec);
	t = t * 0.2f + 0.8f;

	return (unsigned char)(t * 255);
}

bool PyramidMaterial::canSprayPaint(World* pworld, const WCoord& blockpos)
{
	return (pworld->getBlockData(blockpos) & 8) > 0;
}

int PyramidMaterial::convertDataByRotate(int blockdata, int rotatetype)
{
	int dir = commonConvertDataByRotate(blockdata & 3, rotatetype);
	return ((blockdata >> 2) << 2) | dir;
}

void PyramidMaterial::createCollideData(CollisionDetect* coldetect, World* pworld, const WCoord& blockpos)
{
	float blockheight = getBlockHeight(pworld->getBlockData(blockpos));
	WCoord pos = blockpos * BLOCK_SIZE;

	short step = 10;
	int size = BLOCK_SIZE * 0.9f;
	int dir = pworld->getBlockData(blockpos) & 3;

	auto pblock = pworld->getBlock(blockpos);

	short phyType = 0;
	if (blockheight > 0 && blockheight < 1.0f) phyType = 0;
	else if (blockheight<0 && blockheight>-1.0f) phyType = 1;
	if (phyType == 0)
	{
		for (int i = 1; i < step + 1; i++)
		{
			coldetect->addObstacle(pos + WCoord((i - 1) * 5, 10 * (i - 1), (i - 1) * 5), pos + WCoord((BLOCK_SIZE - (i - 1) * 10), 10 * i, (BLOCK_SIZE - (i - 1) * 10)));
		}
	}
	else
	{
		for (int i = 1; i < step + 1; i++)
		{
			coldetect->addObstacle(pos + WCoord((i - 1) * 5, BLOCK_SIZE-10 * (i - 1), (i - 1) * 5), pos + WCoord((BLOCK_SIZE - (i - 1) * 10), BLOCK_SIZE-10 * i, (BLOCK_SIZE - (i - 1) * 10)));
		}
	}

		
	
}

bool PyramidMaterial::coverNeighbor(int curblockdata, SolidBlockMaterial* neighbor, int neighbor_data, DirectionType dir)
{
	if (curblockdata & 8)
	{
		if (dir == DIR_NEG_Y)
		{
			if (neighbor && neighbor->GetAttrRenderType() == BLOCKRENDER_FLUID && neighbor_data) return false;
		}
		return true;
	}
	else
	{
		int curDir = curblockdata & 3;
		int updown = curblockdata & 4;
		if (ReverseDirection(curDir) == dir || (updown + 4) == dir)
		{
			int updown = (curblockdata & 4) >> 2;
			if ((updown + 4) == dir)
			{
				if (dir == DIR_NEG_Y)
				{
					if (neighbor && neighbor->GetAttrRenderType() == BLOCKRENDER_FLUID && neighbor_data) return false;
				}
				return true;
			}
		}
	}
	return false;
}

bool PyramidMaterial::canAttachedToMecha(int curblockdata, DirectionType dir, int neighbor_id, int neighbor_data)
{
	/*	if(dir == DIR_NEG_Y)
		{
			return curblockdata != 1;
		}
		else if(dir == DIR_POS_Y)
		{
			return curblockdata != 0;
		}
		else */return true;
}

void PyramidMaterial::dropBlockAsItem(World* pworld, const WCoord& blockpos, int blockdata, BLOCK_MINE_TYPE droptype, float chance, int uin)
{
	CubeBlockMaterial::dropBlockAsItem(pworld, blockpos, blockdata, droptype, chance, uin);

	if (blockdata & 8)
	{
		CubeBlockMaterial::dropBlockAsItem(pworld, blockpos, blockdata, droptype, chance, uin);
	}
}

int PyramidMaterial::getPhisicMeshVerts(BaseSection* psection, const WCoord& blockpos, dynamic_array<Rainbow::Vector3f>& verts, dynamic_array<UInt16>& idxs)
{
#ifdef IWORLD_SERVER_BUILD
	if (!m_mPhyModel.size())
	{
		initVertData();
	}
#endif	
	int blockdata = psection->getBlock(blockpos).getData();
	float blockheight = getBlockHeight(blockdata);
	short phyType = 0;
	if (blockheight > 0 && blockheight < 1.0f) phyType = 0;
	else if (blockheight<0 && blockheight>-1.0f) phyType = 1; 
	if (m_mPhyModel.find(phyType) != m_mPhyModel.end())
	{
		PyramidPhyModel* pTag = &m_mPhyModel[phyType];
		verts = pTag->verts;
		idxs = pTag->idxs;
		return  pTag->triangleCount;
	}
	return 0;
}

BLOCK_RENDERTYPE_T PyramidMaterial::GetAttrRenderType() const
{
	return BLOCKRENDER_MODEL;
}

void PyramidMaterial::onBlockPlacedBy(World* pworld, const WCoord& blockpos, IClientPlayer* player)
{
	ClientPlayer* playerTmp = player->GetPlayer();
	if (!playerTmp) return;
	int dir = playerTmp->getCurPlaceDir();
	float x, y, z;
	playerTmp->getFaceDir(x, y, z);
	int data = dir;
	if (y > 0)
	{
		data += 4;
	}
	pworld->setBlockData(blockpos, data);
}



void PyramidMaterial::createBlockMesh(const BuildSectionMeshData& data, const WCoord& blockpos, SectionMesh* poutmesh)
{
	auto psection = data.m_SharedSectionData;
#ifndef IWORLD_SERVER_BUILD
	FaceVertexLight faceVertexLight;
	//float block_light[16] = { 0 };
	Block pblock = psection->getBlock(blockpos);
	int curblockdata = pblock.getData();
	DirectionType curDir = DirectionType(curblockdata & 3);

	float blockheight = getBlockHeight(curblockdata);
	DirectionType specialdir = DIR_NOT_INIT;
	if (blockheight > 0 && blockheight < 1.0f) specialdir = DIR_POS_Y;
	else if (blockheight<0 && blockheight>-1.0f) specialdir = DIR_NEG_Y;

	const BiomeDef* biome = GetDefManagerProxy()->getBiomeDef(1);

	std::vector<int> wholeFace;
	std::vector<int> triangleFace;
	if (curblockdata & 8)
	{
		CubeBlockMaterial::createBlockMesh(data, blockpos, poutmesh);
		return;
	}
	else
	{

		if (specialdir == DIR_POS_Y)
		{
			wholeFace.push_back(0);
			triangleFace.push_back(0);
			triangleFace.push_back(1);
			triangleFace.push_back(2);
			triangleFace.push_back(3);
		}
		else
		{
			wholeFace.push_back(1);
			triangleFace.push_back(4);
			triangleFace.push_back(5);
			triangleFace.push_back(6);
			triangleFace.push_back(7);
		}
	}
	BlockColor facecolor(255, 255, 255, 0);
	for (auto& d : wholeFace)
	{
		DirectionType dir = (DirectionType)(d+4);
		// 		if (m_DisableCoverFaceOpt || dir == specialdir || !psection->getNeighborCover(blockpos, this, curblockdata, dir))
		{
			bool flipQuad = psection->getFaceVertexLight(blockpos, dir, faceVertexLight);

			dynamic_array<UInt16>* indices = /*flipQuad ? &m_dNegIndices : */m_dPosIndices;

			RenderBlockMaterial* pmtl = getFaceMtl(biome, dir, pblock.getData(), facecolor);
			if (pmtl == NULL)
				continue;
			SectionSubMesh* psubmesh = poutmesh->getSubMesh(pmtl);
			const float* uvtile = nullptr;
			if (psubmesh && !psubmesh->IsIgnoreTileUV())
				uvtile = pmtl->getUVTile();


			BlockGeomMeshInfo mesh;

			// 			mesh.vertices = vertices;
			mesh.vertices = m_mWholeFace()[d];
			mesh.indices = *indices;
			//unsigned short dir_color = TriangleNormal2LightColor(g_DirectionCoord[dir].toVector3());
			if (psubmesh)
				psubmesh->addGeomFaceLight(mesh, &blockpos, faceVertexLight, &facecolor, pmtl->getUVTile());
		}
	}
	for (auto& d : triangleFace)
	{
		DirectionType dir = (DirectionType)(d % 4);
		dynamic_array<UInt16>* indices = /*flipQuad ? &m_NegTrIndices : */m_PosTrIndices;
		//bool flipQuad = psection->getFaceVertexLight(blockpos, dir, faceVertexLight);
		//FaceVertexLight faceUpDownVertexLight;
		//psection->getFaceVertexLight(blockpos, specialdir, faceUpDownVertexLight);
		//for (int i = 0; i < 4; i++)
		//{
		//	if (specialdir == DIR_NEG_Y)
		//	{
		//		faceVertexLight.m_Light[i] = (3 * faceVertexLight.m_Light[i] + (faceUpDownVertexLight.m_Light[i])) / 4;
		//		faceVertexLight.m_AmbientOcclusion[i] = (3 * faceVertexLight.m_AmbientOcclusion[i] + faceUpDownVertexLight.m_AmbientOcclusion[i]) / 4;
		//	}
		//	else
		//	{
		//		faceVertexLight.m_Light[i] = (faceVertexLight.m_Light[i] + (2 * faceUpDownVertexLight.m_Light[i])) / 3;
		//		faceVertexLight.m_AmbientOcclusion[i] = (faceVertexLight.m_AmbientOcclusion[i] + 2 * faceUpDownVertexLight.m_AmbientOcclusion[i]) / 3;
		//	}
		//}
		RenderBlockMaterial* pmtl = getFaceMtl(biome, dir, pblock.getData(), facecolor);
		if (pmtl == NULL)
			continue;
		SectionSubMesh* psubmesh = poutmesh->getSubMesh(pmtl);
		const float* uvtile = nullptr;
		if (psubmesh && !psubmesh->IsIgnoreTileUV())
			uvtile = pmtl->getUVTile();

		BlockGeomMeshInfo mesh;
		// 		mesh.vertices = vertices;
		// unsigned short dir_color1 = TriangleNormal2LightColor(g_DirectionCoord[dir].toVector3());
		// unsigned short dir_color2 = TriangleNormal2LightColor(g_DirectionCoord[specialdir].toVector3());
		BlockColor vertcolor = Rainbow::ColorRGBA32::white;
		//vertcolor.v = 0xffffffff;
		vertcolor.a = 0;//(dir_color1 + 2 * dir_color2) / 3;
		// if (specialdir == DIR_NEG_Y)
		// {
		// 	vertcolor.a = (3 * dir_color1 + dir_color2) / 4;
		// }

		mesh.vertices = m_mTriangleFace[d];
		
		mesh.indices = *indices;
		for (int n = 0; n < m_mTriangleFace[d].size(); n++)
		{
			auto& vert = mesh.vertices[n];
			int aveltMe = 0;
			int aveltNeight[3] = { 0 };
			int avelt = 0;
			if (vert.pos.y > 0 && specialdir == DIR_POS_Y || vert.pos.y == 0 && specialdir == DIR_NEG_Y)
			{
				auto selfPos = blockpos + g_DirectionCoord[specialdir];
				bool isXdir = dir < 2;
				int xdir = isXdir ? dir : vert.pos.x == 0 ? 0 : 1;
				int zdir = !isXdir ? dir : vert.pos.z == 0 ? 2 : 3;
				aveltMe = psection->getLight2(selfPos, true);
				aveltNeight[0] = psection->getLight2(selfPos + g_DirectionCoord[xdir], true);
				aveltNeight[1] = psection->getLight2(selfPos + g_DirectionCoord[zdir], true);
				aveltNeight[2] = psection->getLight2(selfPos + g_DirectionCoord[xdir] + g_DirectionCoord[zdir], true);
				avelt = ((aveltMe + aveltNeight[0] + aveltNeight[1] + aveltNeight[2]) >> 2) & 0xff00ff;
			}
			else
			{
				auto selfPos = blockpos + g_DirectionCoord[dir];
				int xdir = vert.pos.x == 0 ? 0 : 1;
				int zdir = vert.pos.z == 0 ? 2 : 3;
				int sideDir = dir > 1 ? xdir : zdir;
				aveltMe = psection->getLight2(selfPos, true);
				aveltNeight[0] = psection->getLight2(selfPos + g_DirectionCoord[sideDir], true);
				avelt = ((aveltMe + aveltNeight[0]) >> 1) & 0xff00ff;
			}
			vert.color = vertcolor;
			InitBlockVertLight(vert, avelt, uvtile);
			//int lt1 = (((avelt >> 4) & 0xf) * vertcolor.a) >> 5;
			//int lt2 = (((avelt >> 20) & 0xf) * vertcolor.a) >> 5;
			//vert.pos.w = (lt1 << 8) | lt2;
			//vert.color = vertcolor;
		}
		if (psubmesh) psubmesh->addGeomFace(mesh, &blockpos);
	}

#endif
}

void PyramidMaterial::initVertData()
{
	initWholeFaceVertData();
	initTriangleFaceVertData();
	initPhyModelData();
}

void PyramidMaterial::initWholeFaceVertData()
{
	if (m_mWholeFace().size() != 0)
	{
		return;
	}
	for (int d = 0; d < 2; d++)
	{
		DirectionType dir = (DirectionType)d;
		dynamic_array<UInt16>* indices = m_dPosIndices;

		dynamic_array<BlockGeomVert> vertices;
		Rainbow::Vector3f normalVec = g_DirectionCoord[d?5:4].toVector3();
		Normalize(normalVec);
		BlockVector normal_dir = PackVertNormal(normalVec);
		BlockGeomVert vert[4];
		//unsigned short dir_color = Normal2LightColor(g_DirectionCoord[dir].toVector3());
		// unsigned short dir_color = TriangleNormal2LightColor(g_DirectionCoord[d ? 5 : 4].toVector3());
		BlockVector vertcolor;
		vertcolor.v = 0xffffffff;
		vertcolor.w = 0;
		if (0 == d)
		{
			vert[0].pos = Rainbow::Vector4f(0, 0, 0, 0);
			vert[1].pos = Rainbow::Vector4f(100, 0, 0, 0);
			vert[2].pos = Rainbow::Vector4f(100, 0, 100, 0);
			vert[3].pos = Rainbow::Vector4f(0, 0, 100, 0);

			vert[0].uv = { 1, 0 };
			vert[1].uv = { 0, 0 };
			vert[2].uv = { 0, 1 };
			vert[3].uv = { 1, 1 };
		}
		else if (1 == d)
		{
			vert[0].pos = Rainbow::Vector4f(0, 100, 0, 0);
			vert[1].pos = Rainbow::Vector4f(0, 100, 100, 0);
			vert[2].pos = Rainbow::Vector4f(100, 100, 100, 0);
			vert[3].pos = Rainbow::Vector4f(100, 100, 0, 0);

			vert[0].uv = { 0, 0 };
			vert[1].uv = { 0, 1 };
			vert[2].uv = { 1, 1 };
			vert[3].uv = { 1, 0 };
		}

		for (int oo = 0; oo < 4; oo++)
		{
			vert[oo].uv = { (short)(vert[oo].uv.x * BLOCKUV_SCALE), (short)(vert[oo].uv.y * BLOCKUV_SCALE) };
			vert[oo].normal = normal_dir;
			vert[oo].color.SetUInt32(vertcolor.v);
			// 			vert[oo].color = 0xffffffff;
			// int lt1 = (((255 >> 4) & 0xf) * vertcolor.w) >> 5;
			// int lt2 = (((255 >> 20) & 0xf) * vertcolor.w) >> 5;
			vert[oo].pos.w = 0;//(lt1 << 8) | lt2;
			vertices.push_back(vert[oo]);
		}
		m_mWholeFace().push_back(vertices);
	}
}

void PyramidMaterial::initTriangleFaceVertData()
{
	for (int i = 0; i < 2; i++)
	{
		for (int j = 0; j < 4; j++)
		{
			DirectionType dir = (DirectionType)j;
			dynamic_array<UInt16>* indices = m_PosTrIndices;
			int d = i * 4 + j;
			//unsigned short dir_color = TriangleNormal2LightColor(g_DirectionCoord[dir].toVector3(),true, dir);
			dynamic_array<BlockGeomVert> vertices;
			Rainbow::Vector3f normalVec = g_DirectionCoord[dir].toVector3();
			//unsigned short dir_color = Normal2LightColor(normalVec);
			Normalize(normalVec);
			BlockVector normal_dir = PackVertNormal(normalVec);
			BlockVector vertcolor;
			vertcolor.v = 0xffffffff;
			vertcolor.w = 0;

			Rainbow::Vector2f uv[3];
			BlockGeomVert vert[3];
			if (0 == d)//б�߳���3 down
			{
				vert[0].pos = Rainbow::Vector4f(50, 100, 50, 0);
				vert[1].pos = Rainbow::Vector4f(0, 0, 0, 0);
				vert[2].pos = Rainbow::Vector4f(0, 0, 100, 0);

				uv[0] = { 0.5, 0 };
				uv[1] = { 1, 1 };
				uv[2] = { 0, 1 };
			}
			else if (1 == d)
			{
				vert[0].pos = Rainbow::Vector4f(50, 100, 50, 0);
				vert[1].pos = Rainbow::Vector4f(100, 0, 100, 0);
				vert[2].pos = Rainbow::Vector4f(100, 0, 0, 0);

				uv[0] = { 0.5, 0 };
				uv[1] = { 1, 1 };
				uv[2] = { 0, 1 };
			}
			else if (2 == d)
			{
				vert[0].pos = Rainbow::Vector4f(50, 100, 50, 0);
				vert[1].pos = Rainbow::Vector4f(100, 0, 0, 0);
				vert[2].pos = Rainbow::Vector4f(0, 0, 0, 0);

				uv[0] = { 0.5, 0 };
				uv[1] = { 1, 1 };
				uv[2] = { 0, 1 };
			}
			else if (3 == d)
			{
				vert[0].pos = Rainbow::Vector4f(50, 100, 50, 0);
				vert[1].pos =  Rainbow::Vector4f(0, 0, 100, 0);
				vert[2].pos = Rainbow::Vector4f(100, 0, 100, 0);

				uv[0] = { 0.5, 0 };
				uv[1] = { 1, 1 };
				uv[2] = { 0, 1 };
			}
			else if (4 == d)
			{
				vert[0].pos = Rainbow::Vector4f(50, 0, 50, 0);
				vert[1].pos = Rainbow::Vector4f(0, 100, 100, 0);
				vert[2].pos = Rainbow::Vector4f(0, 100, 0, 0);

				uv[0] = { 0.5, 1 };
				uv[1] = { 0, 0 };
				uv[2] = { 1, 0 };
			}
			else if (5 == d)
			{
				vert[0].pos = Rainbow::Vector4f(50, 0, 50, 0);
				vert[1].pos = Rainbow::Vector4f(100, 100, 0, 0);
				vert[2].pos = Rainbow::Vector4f(100, 100, 100, 0);

				uv[0] = { 0.5, 1 };
				uv[1] = { 0, 0 };
				uv[2] = { 1, 0 };
			}
			else if (6 == d)
			{
				vert[0].pos = Rainbow::Vector4f(50, 0, 50, 0);
				vert[1].pos = Rainbow::Vector4f(0, 100, 0, 0);
				vert[2].pos = Rainbow::Vector4f(100, 100, 0, 0);

				uv[0] = { 0.5, 1 };
				uv[1] = { 0, 0 };
				uv[2] = { 1, 0 };
			}
			else if (7 == d)
			{
				vert[0].pos = Rainbow::Vector4f(50, 0, 50, 0);
				vert[1].pos = Rainbow::Vector4f(100, 100, 100, 0);
				vert[2].pos = Rainbow::Vector4f(0, 100, 100, 0);

				uv[0] = { 0.5, 1 };
				uv[1] = { 0, 0 };
				uv[2] = { 1, 0 };
			}
			for (int oo = 0; oo < 3; oo++)
			{
				vert[oo].uv = { short(uv[oo].x * BLOCKUV_SCALE), short(uv[oo].y * BLOCKUV_SCALE) };
				vert[oo].normal = normal_dir;
				vert[oo].color.SetUInt32(vertcolor.v);
				//int lt1 = (((255 >> 4) & 0xf) * vertcolor.w) >> 5;
				//int lt2 = (((255 >> 20) & 0xf) * vertcolor.w) >> 5;
				//vert[oo].pos.w = (lt1 << 8) | lt2;
				vertices.push_back(vert[oo]);
			}
			m_mTriangleFace.push_back(vertices);
		}
	}
}




void PyramidMaterial::initPhyModelData()
{
	if (m_mWholeFace().size() == 0 || m_mTriangleFace.size() == 0 )
	{
		return;
	}
	for (int i = 0; i < 2; i++)
	{
		PyramidPhyModel model;
		dynamic_array<dynamic_array<BlockGeomVert>*> faceList;
		faceList.push_back(0 == i ? &m_mWholeFace()[0] : &m_mWholeFace()[1]);
		faceList.push_back(&m_mTriangleFace[i * 4 + 0]);
		faceList.push_back(&m_mTriangleFace[i * 4 + 1]);
		faceList.push_back(&m_mTriangleFace[i * 4 + 2]);
		faceList.push_back(&m_mTriangleFace[i * 4 + 3]);
		int triangleCount = 0;
		for (auto face : faceList)
		{
			dynamic_array<short> indexlist;
			for (auto& vertdata : (*face))
			{
				Rainbow::Vector3f pos(vertdata.pos.x, vertdata.pos.y, vertdata.pos.z);
				short index = -1;
				for (int o = 0; o < model.verts.size(); o++)
				{
					if (model.verts[o] == pos)
					{
						index = o;
						break;
					}
				}
				if (index == -1)
				{
					index = (short)model.verts.size();
					model.verts.push_back(pos);
				}
				indexlist.push_back(index);
			}
			dynamic_array<UInt16>* posIndeices = m_dPosIndices;
			if (indexlist.size() == 3)
			{
				posIndeices = m_PosTrIndices;
				triangleCount++;
			}
			else if (indexlist.size() > 3)
			{
				triangleCount += indexlist.size() - 2;
			}
			for (auto& idxex : (*posIndeices))
			{
				model.idxs.push_back(indexlist[idxex]);
			}
		}
		model.triangleCount = triangleCount;
		m_mPhyModel.insert(make_pair(i, model));
	}
	


}

SectionMesh* PyramidMaterial::createBlockProtoMesh(int protodata)
{
	SectionMesh* pmesh = ENG_NEW(SectionMesh)();
	int blockdata = protodata;

	const BiomeDef* biome = GetDefManagerProxy()->getBiomeDef(0);
	//  	dynamic_array<UInt16>* indices = &m_dPosIndices;

	char list[4] = { 0,1,2,3 };
	BlockColor facecolor(255, 255, 255, 0);
	for (int i = 0; i < 4; i++)
	{
		DirectionType dir = (DirectionType)(list[i] % 4);
		RenderBlockMaterial* pmtl = getFaceMtl(biome, dir, blockdata, facecolor);
		SectionSubMesh* psubmesh = pmesh->getSubMesh(pmtl, true);

		BlockGeomMeshInfo meshinfo;
		//if (i < 1)
		//{
		//	meshinfo.vertices = m_mWholeFace()[0];
		//	meshinfo.indices = *m_dPosIndices;
		//}
		//else
		//{
			meshinfo.vertices = m_mTriangleFace[i];
			meshinfo.indices = *m_PosTrIndices;
		//}
		if (psubmesh)
			psubmesh->addGeomFaceLight(meshinfo, NULL, s_DefaultFaceVertexLights, &facecolor);
	}
	return pmesh;
}

typedef PyramidMaterial::BlockInstance PyramidMaterialInstance;
IMPLEMENT_SCENEOBJECTCLASS(PyramidMaterialInstance)
MNSandbox::ReflexClassParam<PyramidMaterial::BlockInstance, int> PyramidMaterialInstance::R_Dir(0, "Dir", "Block", &PyramidMaterial::BlockInstance::GetBlockData, &PyramidMaterial::BlockInstance::SetBlockData);