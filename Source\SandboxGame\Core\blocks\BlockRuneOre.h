
#ifndef __BLOCK_RUNE_ORE_H__
#define __BLOCK_RUNE_ORE_H__

#include "BlockBasic.h"

//符文矿石
class BlockRuneOre : public BasicBlockMaterial //tolua_exports
{ //tolua_exports
	DECLARE_BLOCKMATERIAL(BlockRuneOre)
public:
	//tolua_begin
	virtual void dropBlockAsItemWithToolId(World *pworld, const WCoord &blockpos, int blockdata, BLOCK_MINE_TYPE droptype, float chance, int useToolId, int uin = -1) override;
	//tolua_end
}; //tolua_exports

#endif//__BLOCK_RUNE_ORE_H__
